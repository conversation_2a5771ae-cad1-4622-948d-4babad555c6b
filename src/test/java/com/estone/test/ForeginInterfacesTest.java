package com.estone.test;

import java.sql.Timestamp;
import java.util.*;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.estone.apv.action.WhApvPackController;
import com.estone.common.util.JedisUtils;
import com.estone.common.util.SpringUtils;
import com.estone.common.util.StringRedisUtils;
import com.estone.sku.action.MergeSkuTaskController;
import com.estone.system.rabbitmq.service.impl.AmqMessageServiceImpl;
import com.estone.warehouse.bean.FrozenStock;
import com.estone.warehouse.bean.FrozenStockQueryCondition;
import com.estone.warehouse.service.FrozenStockService;
import com.whq.tool.json.ResponseJson;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import com.estone.WmsApplication;
import com.estone.combineSku.bean.WhCombineSkuTask;
import com.estone.combineSku.bean.WhCombineSkuTaskQueryCondition;
import com.estone.combineSku.enums.WhCombineSkuTaskStatus;
import com.estone.combineSku.service.WhCombineSkuTaskService;
import com.estone.common.util.PdfUtils;
import com.estone.foreign.bean.WhLendDTO;
import com.estone.loanedout.bean.WhLend;
import com.estone.loanedout.bean.WhLendItem;
import com.estone.loanedout.service.WhLendService;
import com.estone.picking.enums.PickingTaskType;
import com.estone.picking.utils.PickingTaskUtils;
import com.estone.sku.bean.MergeSku;
import com.estone.sku.service.MergeSkuService;
import com.estone.transfer.service.WhFbaAllocationHandleService;

/**
 * <AUTHOR>
 * @date 2024-01-12 15:02
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = WmsApplication.class)
@WebAppConfiguration
public class ForeginInterfacesTest {

    @Autowired
    private MergeSkuService mergeSkuService;

    @Resource
    private WhFbaAllocationHandleService whFbaAllocationHandleService;

    @Resource
    private WhLendService whLendService;

    @Resource
    private WhCombineSkuTaskService whCombineSkuTaskService;

    @Test
    public void testCompleteMergeSku(){
        MergeSku mergeSku = new MergeSku();
        mergeSku.setProductManageId(8764);
        mergeSkuService.pushToProductMS(Arrays.asList(mergeSku));
    }

    @Test
    public void testParsePdf() throws Exception {
        String apvNo = "YSTN24051011000184";
        String source = whFbaAllocationHandleService.getJitPdfUrl(apvNo, 1, null, null);
        PdfUtils.parseAndSplitToSave(source, apvNo, null);
    }


    @Test
    public void testCrossFloor(){
        PickingTaskUtils.checkCrossFloor(658276, PickingTaskType.TRANSFER_MULTIPLEMULTIPLE.intCode());
    }

    @Test
    public void testCreateWhLend() throws Exception {
        String sku = "6EE302104-1";
        List<String> skus = Arrays.asList(sku);
        WhLendItem item = new WhLendItem();
        item.setSku(sku);
        item.setQuantity(2);
        WhLendDTO whLendDTO = new WhLendDTO();
        whLendDTO.setItems(Arrays.asList(item));
        whLendDTO.setSource("PMS");
        whLendDTO.setIsNeedReturn(1);
        whLendDTO.setPlanReturnTime(new Timestamp(System.currentTimeMillis()));
        whLendDTO.setAcceptBy("1");
        whLendDTO.setAcceptByName("1");
        whLendDTO.setLendReason("sssss");
        WhLend whLend = whLendService.allotStockAndCreate(skus, whLendDTO);
    }


    @Test
    public void testCombineSkuAllot() throws Exception {
        List<Integer> ids = Arrays.asList(89);
        WhCombineSkuTaskQueryCondition whCombineSkuTaskQueryCondition = new WhCombineSkuTaskQueryCondition();
        if (CollectionUtils.isNotEmpty(ids)){
            // 校验输入库位是否存在
            whCombineSkuTaskQueryCondition.setIdList(ids);
        }
        List<WhCombineSkuTask> whCombineSkuTasks = whCombineSkuTaskService.queryWhCombineSkuTasks(whCombineSkuTaskQueryCondition, null);
        if (CollectionUtils.isEmpty(whCombineSkuTasks)) return;

        if (whCombineSkuTasks.stream().anyMatch(w -> w.getStatus() > WhCombineSkuTaskStatus.WAITING_ALLOT.intCode()))
            return ;
        whCombineSkuTaskService.allotSkuStock(whCombineSkuTasks);
    }
    @Test
    public void testCombineSkuPickTask() throws Exception {
        List<Integer> ids = Arrays.asList(89);
        WhCombineSkuTaskQueryCondition whCombineSkuTaskQueryCondition = new WhCombineSkuTaskQueryCondition();

        whCombineSkuTaskQueryCondition.setIdList(ids);
        whCombineSkuTaskQueryCondition.setStatusList(Arrays.asList(WhCombineSkuTaskStatus.ALLOT.intCode(), WhCombineSkuTaskStatus.PART_ALLOT.intCode()));
        List<WhCombineSkuTask> whCombineSkuTasks = whCombineSkuTaskService.queryWhCombineSkuTasks(whCombineSkuTaskQueryCondition, null);
        if (CollectionUtils.isEmpty(whCombineSkuTasks)) {
            return;
        }
        whCombineSkuTaskService.doCreatePickingTask(whCombineSkuTasks,false);
    }

    @Test
    public void testMerge2OnePdfPage(){
        List<String> pdfs = Arrays.asList("D:\\test\\ss1.pdf","D:\\test\\ss2.pdf");
        try{
            PdfUtils.merge2OnePdfPage(pdfs, "D:\\test", "test.pdf");
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Test
    public void testDecorateMessage(){
        String message = AmqMessageServiceImpl.decorateMessageBody(null);
        System.out.println(message);
        message = AmqMessageServiceImpl.decorateMessageBody(null);
        System.out.println(message);
    }


    @Test
    public void testUpdateWhCombineSkuTask(){
        FrozenStockQueryCondition frozenQuery = new FrozenStockQueryCondition();
        frozenQuery.setStockIdList(Arrays.asList(1693377));
        FrozenStockService frozenStockService = SpringUtils.getBean(FrozenStockService.class);
        List<FrozenStock> frozenStocks = frozenStockService.queryFrozenStocks(frozenQuery, null);
        System.out.println(JSON.toJSONString(frozenStocks));
    }

    @Test
    public void testMergeSku(){
        JedisUtils.init();
        StringRedisUtils.init();
        MergeSkuTaskController bean = SpringUtils.getBean(MergeSkuTaskController.class);
        bean.createTaskNo(Arrays.asList(99));
    }

    @Test
    public void testPrint(){
        StringRedisUtils.init();
        WhApvPackController bean = SpringUtils.getBean(WhApvPackController.class);
//        ResponseJson responseJson = bean.omsMgPrint("YSTN24121321134865714");
        List<String> as = Arrays.asList("YSTN24121316134853487","YSTN24121400134876979","YSTN24121401134879462","YSTN24121401134880303","YSTN24121402134882182");
        List<ResponseJson> responses = new ArrayList<>();
        for(String s:as) {
            ResponseJson responseJson = bean.omsMgPrint(s);
            responses.add(responseJson);
        }
        for (ResponseJson r:responses){
            System.out.println(JSON.toJSON(r));
        }
    }
}
