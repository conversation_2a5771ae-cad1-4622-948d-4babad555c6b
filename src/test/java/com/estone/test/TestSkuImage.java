package com.estone.test;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;

import com.alibaba.fastjson.JSON;
import com.estone.sku.bean.WhSkuImage;
import com.estone.sku.bean.WhSkuImageQueryCondition;
import com.estone.sku.service.WhSkuImageService;
import com.whq.tool.component.Pager;

/**
 * @version 1.0
 * @author: zcx
 * @Description 去除重复图片sql
 * @date 2019/11/14
 */
@Slf4j
public class TestSkuImage extends BaseJunit {
    @Resource
    private WhSkuImageService whSkuImageService;

    @Test
    public void assembleSql() {
        WhSkuImageQueryCondition query = new WhSkuImageQueryCondition();
        int totalCount = 261010;
        int pageSize = 30000;
        int totalPage = (totalCount + pageSize - 1) / pageSize;
        for (int pageNo = 1; pageNo <= totalPage; pageNo++) {
            Pager pager = new Pager(pageNo, pageSize);
            List<WhSkuImage> whSkuImages = whSkuImageService.queryWhSkuImages(query, pager);
            System.out.println(System.currentTimeMillis());
            StringBuffer buf = new StringBuffer();
            StringBuffer skuBuf = new StringBuffer();

            for (WhSkuImage skuImage : whSkuImages) {
                getRepetitionImage(skuImage.getImageCommonReal(), skuImage, "imageCommonReal");
                getRepetitionImage(skuImage.getImageCommonUnreal(), skuImage, "imageCommonUnreal");
                getRepetitionImage(skuImage.getImageSmtReal(), skuImage, "imageSmtReal");
                getRepetitionImage(skuImage.getImageSmtUnreal(), skuImage, "imageSmtUnreal");
                if (StringUtils.isNotBlank(skuImage.getImageCommonReal())
                        || StringUtils.isNotBlank(skuImage.getImageCommonUnreal())
                        || StringUtils.isNotBlank(skuImage.getImageSmtReal())
                        || StringUtils.isNotBlank(skuImage.getImageSmtUnreal())) {
                    String sql = "UPDATE wh_sku_image SET ";
                    if (StringUtils.isNotBlank(skuImage.getImageCommonReal())) {
                        sql += " image_common_real = '" + skuImage.getImageCommonReal() + "' ,";
                    }
                    if (StringUtils.isNotBlank(skuImage.getImageCommonUnreal())) {
                        sql += " image_common_unreal = '" + skuImage.getImageCommonUnreal() + "' ,";
                    }
                    if (StringUtils.isNotBlank(skuImage.getImageSmtReal())) {
                        sql += " image_smt_real = '" + skuImage.getImageSmtReal() + "' ,";
                    }
                    if (StringUtils.isNotBlank(skuImage.getImageSmtUnreal())) {
                        sql += " image_smt_unreal = '" + skuImage.getImageSmtUnreal() + "' ,";
                    }
                    sql = sql.substring(0, sql.length() - 1);

                    sql += " WHERE sku = '" + skuImage.getSku() + "' ;";
                    sql += System.getProperty("line.separator");
                    buf.append(sql);
                    skuBuf.append(skuImage.getSku()).append(",");
                }
            }
            System.out.println("修改的sku:" + skuBuf.toString());
            write(pageNo, buf.toString());
        }
    }

    private void write(int pageNo, String string) {
        File file = new File("E:/data-" + pageNo + ".sql");

        try (FileOutputStream fop = new FileOutputStream(file)) {
            if (!file.exists()) {
                file.createNewFile();
            }
            byte[] contentInBytes = string.getBytes();
            fop.write(contentInBytes);
            fop.flush();
        }
        catch (FileNotFoundException e) {
            log.error(e.getMessage(), e);
        }
        catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    private void getRepetitionImage(String urls, WhSkuImage bean, String type) {
        if (StringUtils.isNotBlank(urls)) {
            List<String> imageCommonUnreals = JSON.parseArray(urls, String.class);
            Collection<Object> value = filterDuplicateImages(imageCommonUnreals);
            if (imageCommonUnreals.size() > value.size()) {
                String jsonString = JSON.toJSONString(value);
                setValue(bean, type, jsonString);
            }
            else {
                setValue(bean, type, null);
            }
            imageCommonUnreals.removeAll(value);

            for (String imageCommonUnreal : imageCommonUnreals) {
                String imageName = getImageName(imageCommonUnreal);
                for (Object url : value) {
                    String newImageName = getImageName((String) url);
                    if (imageName.equals(newImageName)) {

                        Date date = null;
                        Date newDate = null;
                        try {
                            date = getDate(getDateStr(imageCommonUnreal));
                            newDate = getDate(getDateStr((String) url));
                            if (date.compareTo(newDate) > 0) {
                                System.out.println("新图片时间小于原来时间: sku:" + bean.getSku() + " 最新图片:" + url + "---对比图片:"
                                        + imageCommonUnreal);
                            }
                        }
                        catch (ParseException e) {
                        }
                    }
                }
            }
        }
    }

    private void setValue(WhSkuImage bean, String type, String saveValue) {
        switch (type) {
            case "imageCommonReal":
                bean.setImageCommonReal(saveValue);
                break;
            case "imageCommonUnreal":
                bean.setImageCommonUnreal(saveValue);
                break;
            case "imageSmtReal":
                bean.setImageSmtReal(saveValue);
                break;
            case "imageSmtUnreal":
                bean.setImageSmtUnreal(saveValue);
                break;
        }
    }

    private Collection<Object> filterDuplicateImages(List value) {
        HashMap<String, Object> tempMap = new HashMap<>();
        for (Object url : ((List) value)) {
            if (url instanceof String) {
                int nameStart = ((String) url).lastIndexOf("/");
                String imageName = ((String) url).substring(nameStart + 1);
                String saveValue = (String) tempMap.get(imageName);
                if (StringUtils.isBlank(saveValue)) {
                    tempMap.put(imageName, url);
                }
                else {
                    int dateStart = StringUtils.lastOrdinalIndexOf((String) url, "/", 3) + 1;
                    String date = ((String) url).substring(dateStart, nameStart);
                    String lastDate = getDateStr(saveValue);
                    if (date.compareTo(lastDate) > 0) {// 按时间顺序保留最新的图片
                        tempMap.put(imageName, url);
                    }
                }
            }
            else {
                tempMap.put(url.toString(), url);
            }
        }
        return tempMap.values();
    }

    private String getImageName(String url) {
        return url.substring(url.lastIndexOf("/") + 1);
    }

    private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM/dd-HH-mm-ss-SSS");

    private Date getDate(String dateStr) throws ParseException {
        return dateFormat.parse(dateStr);
    }

    private String getDateStr(String saveValue) {
        return saveValue.substring(StringUtils.lastOrdinalIndexOf(saveValue, "/", 3) + 1, saveValue.lastIndexOf("/"));
    }
}
