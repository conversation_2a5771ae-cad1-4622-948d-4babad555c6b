package com.estone.test;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.CaseInsensitiveMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.sql.*;
import java.util.List;
import java.util.Map;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @Date 2022/4/30
 * @Version: 0.0.1
 */

@Slf4j
@RestController
@RequestMapping("/test")
public class TestSqlInfoController {


    public static void main(String[] args) {
        String dbName = "wms-151";
        String dbUrl = "******************************/"+dbName;
        String dbUserName= "root";
        String dbPassWord = "123456";
        Map<String, String> testTableColumnMap = new CaseInsensitiveMap();
        Map<String, List<String>> testTableListMap = getTables(dbUrl, dbUserName, dbPassWord,testTableColumnMap,dbName);
        dbName = "wms";
          dbUrl = "******************************/"+dbName;
          dbUserName= "readonly";
          dbPassWord = "!QAZxsw2";
        Map<String, List<String>> propTableListMap = getTables(dbUrl, dbUserName, dbPassWord,null,dbName);

        printlnSqlInfo(testTableColumnMap, testTableListMap, propTableListMap);

    }

    /**
     * 打印sql信息
     * @param testTableColumnMap
     * @param testTableListMap
     * @param propTableListMap
     */
    private static void printlnSqlInfo(Map<String, String> testTableColumnMap, Map<String, List<String>> testTableListMap, Map<String, List<String>> propTableListMap) {
        testTableListMap.forEach((testName,testColumnNameList) ->{
            List<String> propColumnList = propTableListMap.get(testName);
            List<String> propNotColumnList = Lists.newArrayList();
            if (CollectionUtils.isEmpty(propColumnList)){
                // 表不存在
                System.out.println("表不存在: " + testName);
            }else{
                Map<String, String> propColumnMap = new CaseInsensitiveMap();
                // 和线上字段不一致
                propColumnList.forEach(propColumn ->{
                    propColumnMap.put(propColumn,propColumn);
                });
                testColumnNameList.stream().forEach(testColumnName ->{
                    if (StringUtils.isEmpty(propColumnMap.get(testColumnName))){
                        propNotColumnList.add(testColumnName);
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(propNotColumnList)){
                System.out.println("线上表:" + testName + " 没有这些字段: " + propNotColumnList.toString());
                propNotColumnList.forEach(propColumn ->{
                    System.out.println(testTableColumnMap.get(testName + propColumn));
                });
            }
        });
    }

    private static  Map<String, List<String>> getTables(String dbUrl, String dbUserName, String dbPassWord, Map<String, String> tableColumnMap, String dbName) {
        Map<String, List<String>> tableListMap = Maps.newHashMap();

        Connection conn=null;
        Statement stmt=null;
        ResultSet rs=null;
        try{
            //注册驱动
            DriverManager.registerDriver(new com.mysql.jdbc.Driver());
            //获取连接
            conn=DriverManager.getConnection(dbUrl,dbUserName,dbPassWord);
            //获取数据库操作对象
            stmt=conn.createStatement();
            //执行SQL语句
            String sql="show tables";
            rs = stmt.executeQuery(sql);//执行给定SQL语句，该语句返回单个ResultSet对象
            //处理查询结果集

            List<String> tableNamesList = Lists.newArrayList();
            while(rs.next()){
                String tableName= rs.getString("Tables_in_" + dbName);
                tableNamesList.add(tableName);

            }

            for (String tableName : tableNamesList) {
                List<String> columnList = Lists.newArrayList();
                ResultSet allColumn = stmt.executeQuery("select  *  from information_schema.COLUMNS " +
                        "where TABLE_SCHEMA = " + "\'" +dbName +"\'" + "and  table_name = " + "\'" + tableName + "\'");
                while (allColumn.next()) {
                    String columnName = allColumn.getString("COLUMN_NAME");
                    if (tableColumnMap != null){
                        String COLUMN_TYPE = allColumn.getString("COLUMN_TYPE");
                        String COLUMN_DEFAULT = StringUtils.isEmpty(allColumn.getString("COLUMN_DEFAULT")) ? "NULL" : allColumn.getString("COLUMN_DEFAULT");
                        String COLUMN_COMMENT = allColumn.getString("COLUMN_COMMENT");
                        String alterTableSql = "ALTER TABLE "+ dbName + "." +tableName +" ADD COLUMN " + columnName + " " + COLUMN_TYPE +" DEFAULT " + COLUMN_DEFAULT + " COMMENT " +  "\'"+COLUMN_COMMENT+"\'" +";";
                        tableColumnMap.put(tableName + columnName ,alterTableSql);
//                        System.out.println("alterTableSql = " + alterTableSql);
                    }

                    columnList.add(columnName);
                }
                tableListMap.put(tableName, columnList);
            }

        }catch (SQLException e){
            log.error(e.getMessage(), e);
        }finally {
            //释放资源
            if(rs!=null){
                try {
                    rs.close();
                } catch (SQLException e) {
                    log.error(e.getMessage(), e);
                }
            }
            if(stmt!=null){
                try {
                    stmt.close();
                } catch (SQLException e) {
                    log.error(e.getMessage(), e);
                }
            }
            if(conn!=null){
                try {
                    conn.close();
                } catch (SQLException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return tableListMap;
    }

}
