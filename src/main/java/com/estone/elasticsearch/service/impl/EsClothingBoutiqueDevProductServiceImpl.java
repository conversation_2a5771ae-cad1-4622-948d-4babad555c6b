package com.estone.elasticsearch.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.common.util.model.ApiResult;
import com.estone.elasticsearch.model.ClothingBoutiqueDevProductEs;
import com.estone.elasticsearch.model.EsIndexNameConstant;
import com.estone.elasticsearch.model.EsQueryParam;
import com.estone.elasticsearch.model.PageInfo;
import com.estone.elasticsearch.service.EsClothingBoutiqueDevProductService;
import com.estone.exquisite.bean.ClothingBoutiqueDevProductQueryCondition;
import com.whq.tool.component.Pager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 精品sku es操作类
 */
@Service
@Slf4j
public class EsClothingBoutiqueDevProductServiceImpl implements EsClothingBoutiqueDevProductService {

    @Override
    public List<ClothingBoutiqueDevProductEs> page(ClothingBoutiqueDevProductQueryCondition queryCondition, Pager pager) {

        EsQueryParam<ClothingBoutiqueDevProductQueryCondition> esQueryParamRequest = new EsQueryParam(queryCondition, EsIndexNameConstant.CLOTHING_BOUTIQUE_DEV_PRODUCT);
        ApiResult apiResult = EsApvServiceImpl.page(esQueryParamRequest, pager);
        if (apiResult.getResult() == null)
            return new ArrayList<>();
        PageInfo<List<ClothingBoutiqueDevProductEs>> pageInfo = JSONObject.parseObject(JSONObject.toJSONString(apiResult.getResult()), new TypeReference<>() {
        });
        pager.setTotalCount((int)pageInfo.getTotal());
        return  pageInfo.getContents();
    }
}
