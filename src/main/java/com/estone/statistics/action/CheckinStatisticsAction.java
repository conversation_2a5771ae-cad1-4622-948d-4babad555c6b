package com.estone.statistics.action;

import com.estone.common.SelectJson;
import com.estone.common.util.POIUtils;
import com.estone.statistics.bean.CheckinStatistics;
import com.estone.statistics.bean.CheckinStatisticsQueryCondition;
import com.estone.statistics.domain.CheckinStatisticsDo;
import com.estone.statistics.enums.CheckinStatisticsEnum;
import com.estone.statistics.service.CheckinStatisticsService;
import com.estone.statistics.util.StatisticsUtils;
import com.estone.system.user.service.SaleUserService;
import com.whq.tool.action.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.*;

/**
 * @ClassName: CheckinStatisticsAction
 * @Description: 入库作业统计
 * <AUTHOR>
 * @Date 2019年1月3日
 */
@Controller
@RequestMapping(value = "statistics/checkin")
@Slf4j
public class CheckinStatisticsAction extends BaseController {

    @Resource
    private SaleUserService saleUserService;

    @Resource
    private CheckinStatisticsService checkinStatisticsService;

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") CheckinStatisticsDo domain) {
        initFormData(domain);
        return "statistics/checkin_statistics_list";
    }

    private void initFormData(@ModelAttribute("domain") CheckinStatisticsDo domain) {
        CheckinStatisticsQueryCondition query = domain.getQuery();
        if (query == null) {
            query = new CheckinStatisticsQueryCondition();
            domain.setQuery(query);
        }
        query.setReadOnly(true);
        domain.setSaleUserList(saleUserService.queryAllSaleUsers());// 用户
        domain.setLinkTypeSelectJson(SelectJson.getList(CheckinStatisticsEnum.values()));// 环节
    }

    private void queryCheckinStatistics(@ModelAttribute("domain") CheckinStatisticsDo domain) {
        domain.setCheckinStatisticses(
                checkinStatisticsService.queryCheckinStatistics(domain.getQuery(), domain.getPage()));
        domain.setCheckinStatistics(domain.getQuery().getTotalCheckinStatistics());
    }

    @RequestMapping(value = "search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") CheckinStatisticsDo domain) {
        initFormData(domain);
        queryCheckinStatistics(domain);
        return "statistics/checkin_statistics_list";
    }

    @RequestMapping(value = "download", method = {RequestMethod.POST})
    @ResponseBody
    public void download(@ModelAttribute("domain") CheckinStatisticsDo domain, HttpServletResponse response) {
        CheckinStatisticsQueryCondition query = domain.getQuery();
        if (Objects.nonNull(query)) {
            query.setReadOnly(true);
        }
        List<CheckinStatistics> list = checkinStatisticsService.queryCheckinStatistics(query, null);

        Map<String, List<String>> headersMap = StatisticsUtils.getCheckinHeadersMap(query.getLinkTypeStr());

        List<String> downloadTitles = new ArrayList<String>();
        Collection<List<String>> collections = headersMap.values();
        for (List<String> titles : collections) {
            for (String title : titles) {
                downloadTitles.add(title);
            }
        }

        String[] headers = downloadTitles.toArray(new String[downloadTitles.size()]);

        OutputStream os = null;

        try {
            String fileName = "入库作业报表" + System.currentTimeMillis() + ".xls";
            response.setContentType("application/ms-excel");
            response.setHeader("Content-Disposition",
                    "attachment; filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            os = response.getOutputStream();
            final List<List<String>> recordsData = new ArrayList<List<String>>();
            POIUtils.createExcel(headers, list, statistics -> {

                recordsData.clear();

                List<String> downloadList = new ArrayList<String>(headers.length);

                downloadList.add(POIUtils.transferObj2Str(statistics.getUserName() + "-" + statistics.getName()));

                if (null != headersMap.get(CheckinStatisticsEnum.DELIVERY.getCode())) {
                    downloadList.add(POIUtils.transferObj2Str(statistics.getDeliveryOrderCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getDeliveryUnitCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getDeliveryWeight()));
                }

                if (null != headersMap.get(CheckinStatisticsEnum.CHECKIN.getCode())) {
                    downloadList.add(POIUtils.transferObj2Str(statistics.getCheckinOrderCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getCheckinUnitCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getCheckinPoOrderCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getCheckinProcessSkuCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getCheckinProcessPcsCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getCheckinNormalSkuCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getCheckinNormalPcsCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getCheckinProcessTagSkuCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getCheckinProcessTagPcsCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getCheckinProcessLightTagSkuCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getCheckinProcessLightTagPcsCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getCheckinProcessWeightTagSkuCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getCheckinProcessWeightTagPcsCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getCheckinOrdinaryTagSkuCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getCheckinOrdinaryTagPcsCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getCheckinProcessSuperTagSkuCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getCheckinProcessSuperTagPcsCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getCheckinAllocationSkuCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getCheckinAllocationPcsCount()));
                }

                if (null != headersMap.get(CheckinStatisticsEnum.QC.getCode())) {
                    downloadList.add(POIUtils.transferObj2Str(statistics.getQcSkuCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getQcPcsCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getAllCheckQcSkuCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getAllCheckQcPcsCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getCheckQcAllocationSkuCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getCheckQcAllocationPcsCount()));
                }

                if (null != headersMap.get(CheckinStatisticsEnum.PICKUP.getCode())) {
                    downloadList.add(POIUtils.transferObj2Str(statistics.getPickupSkuCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getPickupPcsCount()));
                }

                if (null != headersMap.get(CheckinStatisticsEnum.PUTAWAY.getCode())) {
                    downloadList.add(POIUtils.transferObj2Str(statistics.getPutawaySkuCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getPutawayPcsCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getAllocationPutawaySkuCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getAllocationPutawayPcsCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getDirectPutawaySkuCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getDirectPutawayPcsCount()));
                }

                if (null != headersMap.get(CheckinStatisticsEnum.RETURN.getCode())) {
                    downloadList.add(POIUtils.transferObj2Str(statistics.getReturnSkuCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getReturnPcsCount()));
                }

                if (null != headersMap.get(CheckinStatisticsEnum.ABROADRETURN.getCode())) {
                    downloadList.add(POIUtils.transferObj2Str(statistics.getAbroadReturnSkuCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getAbroadReturnPcsCount()));
                }

                if (null != headersMap.get(CheckinStatisticsEnum.RETURNED_BATCH.getCode())) {
                    downloadList.add(POIUtils.transferObj2Str(statistics.getReturnedBatchCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getPackageCount()));
                }

                if (null != headersMap.get(CheckinStatisticsEnum.RETURNED_SEEDING.getCode())) {
                    downloadList.add(POIUtils.transferObj2Str(statistics.getReturnedSeedingSkuCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getReturnedSeedingPcsCount()));
                }
                if (null != headersMap.get(CheckinStatisticsEnum.EXCEPTION.getCode())) {
                    downloadList.add(POIUtils.transferObj2Str(statistics.getHandleUnConfirmCount()));
                    downloadList.add(POIUtils.transferObj2Str(statistics.getFinishCount()));
                }

                recordsData.add(downloadList);

                return recordsData;

            }, true, os);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(os);
        }
    }

}
