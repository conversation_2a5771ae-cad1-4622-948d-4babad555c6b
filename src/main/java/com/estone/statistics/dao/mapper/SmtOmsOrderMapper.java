package com.estone.statistics.dao.mapper;

import com.estone.statistics.bean.SmtOmsOrder;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class SmtOmsOrderMapper implements RowMapper<SmtOmsOrder> {

    public SmtOmsOrder mapRow(ResultSet rs, int rowNum) throws SQLException {
        SmtOmsOrder entity = new SmtOmsOrder();
        entity.setId(rs.getObject(SmtOmsOrderDBField.ID) == null ? null : rs.getInt(SmtOmsOrderDBField.ID));
        entity.setOrderType(rs.getString(SmtOmsOrderDBField.ORDER_TYPE));
        entity.setAccountNumber(rs.getString(SmtOmsOrderDBField.ACCOUNT_NUMBER));
        entity.setPlatformOrder(rs.getString(SmtOmsOrderDBField.PLATFORM_ORDER));
        entity.setSku(rs.getString(SmtOmsOrderDBField.SKU));
        entity.setQuantity(rs.getObject(SmtOmsOrderDBField.QUANTITY) == null ? null : rs.getInt(SmtOmsOrderDBField.QUANTITY));
        entity.setPayTime(rs.getTimestamp(SmtOmsOrderDBField.PAY_TIME));
        entity.setDeliverTime(rs.getTimestamp(SmtOmsOrderDBField.DELIVER_TIME));
        entity.setCreateTime(rs.getString(SmtOmsOrderDBField.CREATE_TIME));
        entity.setStatusName(rs.getString(SmtOmsOrderDBField.STATUS_NAME));
        return entity;
    }
}