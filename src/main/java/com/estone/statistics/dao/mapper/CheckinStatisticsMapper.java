package com.estone.statistics.dao.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.estone.statistics.bean.CheckinStatistics;
import com.estone.statistics.enums.CheckinStatisticsEnum;

public class CheckinStatisticsMapper implements RowMapper<CheckinStatistics> {

    private CheckinStatisticsEnum linkTypeEnum;

    private boolean isAll;

    public CheckinStatisticsMapper() {

    }

    public CheckinStatisticsMapper(CheckinStatisticsEnum linkTypeEnum, boolean isAll) {
        this.linkTypeEnum = linkTypeEnum;
        this.isAll = isAll;
    }

    @Override
    public CheckinStatistics mapRow(ResultSet rs, int rowNum) throws SQLException {
        CheckinStatistics entity = new CheckinStatistics();

        entity.setUserId(rs.getInt("userId"));
        entity.setUserName(rs.getString("userName"));
        entity.setName(rs.getString("name"));

        switch (linkTypeEnum) {
            case DELIVERY: {
                entity.setDeliveryOrderCount(rs.getLong("deliveryOrderCount"));
                entity.setDeliveryUnitCount(rs.getLong("deliveryUnitCount"));
                entity.setDeliveryWeight(rs.getLong("deliveryWeight"));
                if (isAll) {
                    entity.setDeliveryDate(rs.getString("deliveryDate"));
                }
            }
                break;
            case CHECKIN: {
                entity.setCheckinPoOrderCount(rs.getLong("checkinPoOrderCount"));
                if (isAll) {
                    entity.setCheckinDate(rs.getString("checkinDate"));
                }
            }
                break;
            case QC: {
                entity.setQcSkuCount(rs.getLong("qcSkuCount"));
                entity.setQcPcsCount(rs.getLong("qcPcsCount"));
                entity.setAllCheckQcSkuCount(rs.getLong("allCheckQcSkuCount"));
                entity.setAllCheckQcPcsCount(rs.getLong("allCheckQcPcsCount"));
                if (isAll) {
                    entity.setQcDate(rs.getString("qcDate"));
                }
            }
                break;
            case PICKUP: {
                entity.setPickupSkuCount(rs.getLong("pickupSkuCount"));
                entity.setPickupPcsCount(rs.getLong("pickupPcsCount"));
                if (isAll) {
                    entity.setPickupDate(rs.getString("pickupDate"));
                }
            }
                break;
            case PUTAWAY: {
                entity.setPutawaySkuCount(rs.getLong("putawaySkuCount"));
                entity.setPutawayPcsCount(rs.getLong("putawayPcsCount"));
                entity.setDirectPutawaySkuCount(rs.getLong("directPutawaySkuCount"));
                entity.setDirectPutawayPcsCount(rs.getLong("directPutawayPcsCount"));
                if (isAll) {
                    entity.setPutawayDate(rs.getString("putawayDate"));
                }
            }
                break;
            case RETURN: {
                entity.setReturnSkuCount(rs.getLong("returnSkuCount"));
                entity.setReturnPcsCount(rs.getLong("returnPcsCount"));
                if (isAll) {
                    entity.setReturnDate(rs.getString("returnDate"));
                }
            }
                break;
            case ABROADRETURN: {
                entity.setAbroadReturnSkuCount(rs.getLong("abroadReturnSkuCount"));
                entity.setAbroadReturnPcsCount(rs.getLong("abroadReturnPcsCount"));
                if (isAll) {
                    entity.setAbroadReturnDate(rs.getString("abroadReturnDate"));
                }
                break;
            }
            case RETURNED_BATCH: {
                entity.setReturnedBatchCount(rs.getLong("returnedBatchCount"));
                entity.setPackageCount(rs.getLong("packageCount"));
                if (isAll) {
                    entity.setReturnedBatchDate(rs.getString("returnedBatchDate"));
                }
                break;
            }
            case RETURNED_SEEDING: {
                entity.setReturnedSeedingSkuCount(rs.getLong("returnedSeedingSkuCount"));
                entity.setReturnedSeedingPcsCount(rs.getLong("returnedSeedingPcsCount"));
                if (isAll) {
                    entity.setReturnedSeedingDate(rs.getString("returnedSeedingDate"));
                }
                break;
            }
            case EXCEPTION: {
                entity.setHandleUnConfirmCount(rs.getLong("handleUnConfirmCount"));
                entity.setFinishCount(rs.getLong("finishCount"));
                if (isAll) {
                    entity.setHandleDate(rs.getString("handleDate"));
                }
                break;
            }
        }

        return entity;
    }

}
