package com.estone.statistics.dao.impl;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.common.util.SqlerTemplate;
import com.estone.statistics.bean.AfterSaleDayStock;
import com.estone.statistics.bean.AfterSaleDayStockQueryCondition;
import com.estone.statistics.dao.AfterSaleDayStockDao;
import com.estone.statistics.dao.mapper.AfterSaleDayStockDBField;
import com.estone.statistics.dao.mapper.AfterSaleDayStockMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;

@Repository("afterSaleDayStockDao")
public class AfterSaleDayStockDaoImpl implements AfterSaleDayStockDao {

    private void setQueryCondition(SqlerRequest request, AfterSaleDayStockQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加只读权限
        request.setReadOnly(true);
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(AfterSaleDayStockDBField.ID, DataType.INT, query.getId());
        request.addDataParam(AfterSaleDayStockDBField.SKU, DataType.STRING, query.getSku());
        request.addDataParam(AfterSaleDayStockDBField.VENDOR_NAME, DataType.STRING, query.getVendorName());
        request.addDataParam(AfterSaleDayStockDBField.VENDOR_CODE, DataType.STRING, query.getVendorCode());
        request.addDataParam("start_time", DataType.STRING, query.getStartTime());
        request.addDataParam("end_time", DataType.STRING, query.getEndTime());
        if (CollectionUtils.isNotEmpty(query.getSkuList())) {
            request.addDataParam("sku_list", DataType.STRING, query.getSkuList());
        }
        if (CollectionUtils.isNotEmpty(query.getVendorCodeList())) {
            request.addDataParam("vendor_code_list", DataType.STRING, query.getVendorCodeList());
        }
        if (CollectionUtils.isNotEmpty(query.getVendorNameList())) {
            request.addDataParam("vendor_name_list", DataType.STRING, query.getVendorNameList());
        }
        if (query.getQueryLast() != null && query.getQueryLast()) {
            request.addSqlDataParam("QUERY_LAST", " AND id IN (SELECT MAX(id) FROM after_sale_day_stock GROUP BY sku,vendor_code)");
        }
    }

    @Override
    public int queryAfterSaleDayStockCount(AfterSaleDayStockQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryAfterSaleDayStockCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<AfterSaleDayStock> queryAfterSaleDayStockList(AfterSaleDayStockQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryAfterSaleDayStockList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new AfterSaleDayStockMapper());
    }

    @Override
    public int queryAfterSaleDayStockPageCount(AfterSaleDayStockQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryAfterSaleDayStockPageCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<AfterSaleDayStock> queryAfterSaleDayStockPageList(AfterSaleDayStockQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryAfterSaleDayStockPageList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new AfterSaleDayStockMapper(true));
    }

    @Override
    public AfterSaleDayStock queryAfterSaleDayStock(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryAfterSaleDayStockByPrimaryKey");
        request.addDataParam(AfterSaleDayStockDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new AfterSaleDayStockMapper());
    }

    @Override
    public AfterSaleDayStock queryAfterSaleDayStock(AfterSaleDayStockQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryAfterSaleDayStock");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new AfterSaleDayStockMapper());
    }

    @Override
    public void createAfterSaleDayStock(AfterSaleDayStock entity) {
        SqlerRequest request = new SqlerRequest("createAfterSaleDayStock");
        request.addDataParam(AfterSaleDayStockDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(AfterSaleDayStockDBField.SKU_NAME, DataType.STRING, entity.getSkuName());
        request.addDataParam(AfterSaleDayStockDBField.VENDOR_NAME, DataType.STRING, entity.getVendorName());
        request.addDataParam(AfterSaleDayStockDBField.VENDOR_CODE, DataType.STRING, entity.getVendorCode());
        request.addDataParam(AfterSaleDayStockDBField.CHECK_IN_QTY, DataType.INT, entity.getCheckInQty());
        request.addDataParam(AfterSaleDayStockDBField.DECR_QTY, DataType.INT, entity.getDecrQty());
        request.addDataParam(AfterSaleDayStockDBField.DELIVER_QTY, DataType.INT, entity.getDeliverQty());
        request.addDataParam(AfterSaleDayStockDBField.ALLOT_OUT_QTY, DataType.INT, entity.getAllotOutQty());
        request.addDataParam(AfterSaleDayStockDBField.COMBINE_OUT_QTY, DataType.INT, entity.getCombineOutQty());
        request.addDataParam(AfterSaleDayStockDBField.SCRAP_QTY, DataType.INT, entity.getScrapQty());
        request.addDataParam(AfterSaleDayStockDBField.BAD_PRODUCT_QTY, DataType.INT, entity.getBadProductQty());
        request.addDataParam(AfterSaleDayStockDBField.LEND_OUT_QTY, DataType.INT, entity.getLendOutQty());
        request.addDataParam(AfterSaleDayStockDBField.RFO_QTY, DataType.INT, entity.getRfoQty());
        request.addDataParam(AfterSaleDayStockDBField.END_QTY, DataType.INT, entity.getEndQty());
        request.addDataParam(AfterSaleDayStockDBField.START_QTY, DataType.INT, entity.getStartQty());
        request.addDataParam(AfterSaleDayStockDBField.CREATION_DATE, DataType.TIMESTAMP, entity.getCreationDate());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateAfterSaleDayStock(AfterSaleDayStock entity) {
        SqlerRequest request = new SqlerRequest("updateAfterSaleDayStockByPrimaryKey");
        request.addDataParam(AfterSaleDayStockDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(AfterSaleDayStockDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(AfterSaleDayStockDBField.SKU_NAME, DataType.STRING, entity.getSkuName());
        request.addDataParam(AfterSaleDayStockDBField.VENDOR_NAME, DataType.STRING, entity.getVendorName());
        request.addDataParam(AfterSaleDayStockDBField.VENDOR_CODE, DataType.STRING, entity.getVendorCode());
        request.addDataParam(AfterSaleDayStockDBField.CHECK_IN_QTY, DataType.INT, entity.getCheckInQty());
        request.addDataParam(AfterSaleDayStockDBField.DECR_QTY, DataType.INT, entity.getDecrQty());
        request.addDataParam(AfterSaleDayStockDBField.DELIVER_QTY, DataType.INT, entity.getDeliverQty());
        request.addDataParam(AfterSaleDayStockDBField.ALLOT_OUT_QTY, DataType.INT, entity.getAllotOutQty());
        request.addDataParam(AfterSaleDayStockDBField.COMBINE_OUT_QTY, DataType.INT, entity.getCombineOutQty());
        request.addDataParam(AfterSaleDayStockDBField.SCRAP_QTY, DataType.INT, entity.getScrapQty());
        request.addDataParam(AfterSaleDayStockDBField.BAD_PRODUCT_QTY, DataType.INT, entity.getBadProductQty());
        request.addDataParam(AfterSaleDayStockDBField.LEND_OUT_QTY, DataType.INT, entity.getLendOutQty());
        request.addDataParam(AfterSaleDayStockDBField.RFO_QTY, DataType.INT, entity.getRfoQty());
        request.addDataParam(AfterSaleDayStockDBField.END_QTY, DataType.INT, entity.getEndQty());
        request.addDataParam(AfterSaleDayStockDBField.START_QTY, DataType.INT, entity.getStartQty());
        
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateAfterSaleDayStock(List<AfterSaleDayStock> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createAfterSaleDayStock");
            for (AfterSaleDayStock entity : entityList) {
                request.addBatchDataParam(AfterSaleDayStockDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(AfterSaleDayStockDBField.SKU_NAME, DataType.STRING, entity.getSkuName());
                request.addBatchDataParam(AfterSaleDayStockDBField.VENDOR_NAME, DataType.STRING, entity.getVendorName());
                request.addBatchDataParam(AfterSaleDayStockDBField.VENDOR_CODE, DataType.STRING, entity.getVendorCode());
                request.addBatchDataParam(AfterSaleDayStockDBField.CHECK_IN_QTY, DataType.INT, entity.getCheckInQty());
                request.addBatchDataParam(AfterSaleDayStockDBField.DECR_QTY, DataType.INT, entity.getDecrQty());
                request.addBatchDataParam(AfterSaleDayStockDBField.DELIVER_QTY, DataType.INT, entity.getDeliverQty());
                request.addBatchDataParam(AfterSaleDayStockDBField.ALLOT_OUT_QTY, DataType.INT, entity.getAllotOutQty());
                request.addBatchDataParam(AfterSaleDayStockDBField.COMBINE_OUT_QTY, DataType.INT, entity.getCombineOutQty());
                request.addBatchDataParam(AfterSaleDayStockDBField.SCRAP_QTY, DataType.INT, entity.getScrapQty());
                request.addBatchDataParam(AfterSaleDayStockDBField.BAD_PRODUCT_QTY, DataType.INT, entity.getBadProductQty());
                request.addBatchDataParam(AfterSaleDayStockDBField.LEND_OUT_QTY, DataType.INT, entity.getLendOutQty());
                request.addBatchDataParam(AfterSaleDayStockDBField.RFO_QTY, DataType.INT, entity.getRfoQty());
                request.addBatchDataParam(AfterSaleDayStockDBField.END_QTY, DataType.INT, entity.getEndQty());
                request.addBatchDataParam(AfterSaleDayStockDBField.START_QTY, DataType.INT, entity.getStartQty());
                request.addBatchDataParam(AfterSaleDayStockDBField.CREATION_DATE, DataType.TIMESTAMP, entity.getCreationDate());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateAfterSaleDayStock(List<AfterSaleDayStock> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateAfterSaleDayStockByPrimaryKey");
            for (AfterSaleDayStock entity : entityList) {
                request.addBatchDataParam(AfterSaleDayStockDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(AfterSaleDayStockDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(AfterSaleDayStockDBField.SKU_NAME, DataType.STRING, entity.getSkuName());
                request.addBatchDataParam(AfterSaleDayStockDBField.VENDOR_NAME, DataType.STRING, entity.getVendorName());
                request.addBatchDataParam(AfterSaleDayStockDBField.VENDOR_CODE, DataType.STRING, entity.getVendorCode());
                request.addBatchDataParam(AfterSaleDayStockDBField.CHECK_IN_QTY, DataType.INT, entity.getCheckInQty());
                request.addBatchDataParam(AfterSaleDayStockDBField.DECR_QTY, DataType.INT, entity.getDecrQty());
                request.addBatchDataParam(AfterSaleDayStockDBField.DELIVER_QTY, DataType.INT, entity.getDeliverQty());
                request.addBatchDataParam(AfterSaleDayStockDBField.ALLOT_OUT_QTY, DataType.INT, entity.getAllotOutQty());
                request.addBatchDataParam(AfterSaleDayStockDBField.COMBINE_OUT_QTY, DataType.INT, entity.getCombineOutQty());
                request.addBatchDataParam(AfterSaleDayStockDBField.SCRAP_QTY, DataType.INT, entity.getScrapQty());
                request.addBatchDataParam(AfterSaleDayStockDBField.BAD_PRODUCT_QTY, DataType.INT, entity.getBadProductQty());
                request.addBatchDataParam(AfterSaleDayStockDBField.LEND_OUT_QTY, DataType.INT, entity.getLendOutQty());
                request.addBatchDataParam(AfterSaleDayStockDBField.RFO_QTY, DataType.INT, entity.getRfoQty());
                request.addBatchDataParam(AfterSaleDayStockDBField.END_QTY, DataType.INT, entity.getEndQty());
                request.addBatchDataParam(AfterSaleDayStockDBField.START_QTY, DataType.INT, entity.getStartQty());
                
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteAfterSaleDayStock(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteAfterSaleDayStockByPrimaryKey");
        request.addDataParam(AfterSaleDayStockDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}