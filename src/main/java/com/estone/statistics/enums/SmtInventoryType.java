package com.estone.statistics.enums;

/**
 * @Description: SMT库存类型
 * @Author: Yimeil
 * @Date: 2025/2/28 16:00
 * @Version: 1.0.0
 */
public enum SmtInventoryType {
    GOOD_TYPE("良品", "1"),

    BAD_TYPE("残次品", "101");

    private String code;

    private String name;

    private SmtInventoryType(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Integer intCode() {
        return Integer.valueOf(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(String code) {
        SmtInventoryType[] values = values();
        for (SmtInventoryType type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }
}
