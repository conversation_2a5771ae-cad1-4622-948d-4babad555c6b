package com.estone.statistics.bean;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022-06-28 15:08
 */
public class PurchaseLibaryDiscountParamVo implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;
    //优先级
    private Integer level;
    //可售天数≥
    private Integer saleDayGe;
    //可售天数≤
    private Integer saleDayLe;
    //库龄≥
    private Integer libraryAgeGe;
    //库龄≤
    private Integer libraryAgeLe;
    //建议折扣
    private Double suggestDiscount;
    //留底可售天数
    private Integer copySaleDay;

    private String creater;

    private String createDate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getSaleDayGe() {
        return saleDayGe;
    }

    public void setSaleDayGe(Integer saleDayGe) {
        this.saleDayGe = saleDayGe;
    }

    public Integer getSaleDayLe() {
        return saleDayLe;
    }

    public void setSaleDayLe(Integer saleDayLe) {
        this.saleDayLe = saleDayLe;
    }

    public Integer getLibraryAgeGe() {
        return libraryAgeGe;
    }

    public void setLibraryAgeGe(Integer libraryAgeGe) {
        this.libraryAgeGe = libraryAgeGe;
    }

    public Integer getLibraryAgeLe() {
        return libraryAgeLe;
    }

    public void setLibraryAgeLe(Integer libraryAgeLe) {
        this.libraryAgeLe = libraryAgeLe;
    }

    public Double getSuggestDiscount() {
        return suggestDiscount;
    }

    public void setSuggestDiscount(Double suggestDiscount) {
        this.suggestDiscount = suggestDiscount;
    }

    public Integer getCopySaleDay() {
        return copySaleDay;
    }

    public void setCopySaleDay(Integer copySaleDay) {
        this.copySaleDay = copySaleDay;
    }

    public String getCreater() {
        return creater;
    }

    public void setCreater(String creater) {
        this.creater = creater;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    @Override
    public String toString() {
        return "PurchaseLibaryDiscountParamVo{" +
                "id=" + id +
                ", level=" + level +
                ", saleDayGe=" + saleDayGe +
                ", saleDayLe=" + saleDayLe +
                ", libraryAgeGe=" + libraryAgeGe +
                ", libraryAgeLe=" + libraryAgeLe +
                ", suggestDiscount=" + suggestDiscount +
                ", copySaleDay=" + copySaleDay +
                ", creater='" + creater + '\'' +
                ", createDate='" + createDate + '\'' +
                '}';
    }

    //确认范围
    public Boolean checkDiscount(Integer sysSale,Long sysLibraryAge){
        if(sysSale == 0 || sysLibraryAge == 0){
            return false;
        }
        return this.saleDayGe <= sysSale &&  this.saleDayLe >= sysSale && this.libraryAgeGe <= sysLibraryAge && this.libraryAgeLe >= sysLibraryAge;
    }
    //确认折扣系数
    public BigDecimal conversionDiscount(){
        if(this.suggestDiscount == null){
            return new BigDecimal(90);
        }
        if(this.suggestDiscount == 0){
            return new BigDecimal(90);
        }
        return new BigDecimal(this.suggestDiscount).multiply(new BigDecimal(10));
    }
}
