package com.estone.statistics.bean;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
@Data
public class CheckoutStatisticsQueryCondition extends CheckoutStatistics {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    /**
     * 仓库编号
     */
    private Integer warehouseId;

    /**
     * 用户
     */
    private String userIds;
    private List<Integer> userIdList;

    /**
     * 环节
     */
    private String linkTypeStr;

    /**
     * 操作时间
     */
    private String startHandleTime;
    private String endHandleTime;

    /**
     * 合单时间
     */
    private String startMergeTime;
    private String endMergeTime;

    /**
     * 拣货时间
     */
    private String startPickTime;
    private String endPickTime;

    /**
     * 播种时间
     */
    private String startSoftTime;
    private String endSoftTime;

    /**
     * 包装时间
     */
    private String startPackingTime;
    private String endPackingTime;

    /**
     * 扫描时间
     */
    private String startScanTime;
    private String endScanTime;

    /**
     * 装车时间
     */
    private String startLoadingTime;
    private String endLoadingTime;

    /**
     * FBA贴标时间
     */
    @Getter
    @Setter
    private String startTagTime;
    @Getter
    @Setter
    private String endTagTime;

    /**
     * apv状态
     */
    private String apvStatusStr;

    /**
     * 导出类型
     */
    private String exportType;

    /**
     * 出库看板数据展示
     */
    private String dateFormat;

    /**
     * 是否实时查询
     */
    private Boolean isCurrentTime = false;

    /**
     * 是否查询直发数据
     */
    private Boolean isDirect = false;

    /**
     * 汇总
     */
    private CheckoutStatistics totalCheckoutStatistics = new CheckoutStatistics();

    private Boolean readOnly = false;

    //仓库类型
    private Integer type;

    public void setReadOnly(Boolean readOnly) {
        this.readOnly = readOnly;
    }

    public Boolean getReadOnly() {
        return this.readOnly;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getUserIds() {
        return userIds;
    }

    public void setUserIds(String userIds) {
        this.userIds = userIds;
    }

    public List<Integer> getUserIdList() {
        return userIdList;
    }

    public void setUserIdList(List<Integer> userIdList) {
        this.userIdList = userIdList;
    }

    public String getLinkTypeStr() {
        return linkTypeStr;
    }

    public void setLinkTypeStr(String linkTypeStr) {
        this.linkTypeStr = linkTypeStr;
    }

    public String getStartHandleTime() {
        return startHandleTime;
    }

    public void setStartHandleTime(String startHandleTime) {
        this.startHandleTime = startHandleTime;
    }

    public String getEndHandleTime() {
        return endHandleTime;
    }

    public void setEndHandleTime(String endHandleTime) {
        this.endHandleTime = endHandleTime;
    }

    public String getStartMergeTime() {
        return startMergeTime;
    }

    public void setStartMergeTime(String startMergeTime) {
        this.startMergeTime = startMergeTime;
    }

    public String getEndMergeTime() {
        return endMergeTime;
    }

    public void setEndMergeTime(String endMergeTime) {
        this.endMergeTime = endMergeTime;
    }

    public String getStartPickTime() {
        return startPickTime;
    }

    public void setStartPickTime(String startPickTime) {
        this.startPickTime = startPickTime;
    }

    public String getEndPickTime() {
        return endPickTime;
    }

    public void setEndPickTime(String endPickTime) {
        this.endPickTime = endPickTime;
    }

    public String getStartSoftTime() {
        return startSoftTime;
    }

    public void setStartSoftTime(String startSoftTime) {
        this.startSoftTime = startSoftTime;
    }

    public String getEndSoftTime() {
        return endSoftTime;
    }

    public void setEndSoftTime(String endSoftTime) {
        this.endSoftTime = endSoftTime;
    }

    public String getStartPackingTime() {
        return startPackingTime;
    }

    public void setStartPackingTime(String startPackingTime) {
        this.startPackingTime = startPackingTime;
    }

    public String getEndPackingTime() {
        return endPackingTime;
    }

    public void setEndPackingTime(String endPackingTime) {
        this.endPackingTime = endPackingTime;
    }

    public String getStartScanTime() {
        return startScanTime;
    }

    public void setStartScanTime(String startScanTime) {
        this.startScanTime = startScanTime;
    }

    public String getEndScanTime() {
        return endScanTime;
    }

    public void setEndScanTime(String endScanTime) {
        this.endScanTime = endScanTime;
    }

    public String getStartLoadingTime() {
        return startLoadingTime;
    }

    public void setStartLoadingTime(String startLoadingTime) {
        this.startLoadingTime = startLoadingTime;
    }

    public String getEndLoadingTime() {
        return endLoadingTime;
    }

    public void setEndLoadingTime(String endLoadingTime) {
        this.endLoadingTime = endLoadingTime;
    }

    public String getApvStatusStr() {
        return apvStatusStr;
    }

    public void setApvStatusStr(String apvStatusStr) {
        this.apvStatusStr = apvStatusStr;
    }

    public String getExportType() {
        return exportType;
    }

    public void setExportType(String exportType) {
        this.exportType = exportType;
    }

    public String getDateFormat() {
        return dateFormat;
    }

    public void setDateFormat(String dateFormat) {
        this.dateFormat = dateFormat;
    }

    public Boolean getIsCurrentTime() {
        return isCurrentTime;
    }

    public void setIsCurrentTime(Boolean isCurrentTime) {
        this.isCurrentTime = isCurrentTime;
    }

    public Boolean getIsDirect() {
        return isDirect;
    }

    public void setIsDirect(Boolean isDirect) {
        this.isDirect = isDirect;
    }

    public CheckoutStatistics getTotalCheckoutStatistics() {
        return totalCheckoutStatistics;
    }

    public void setTotalCheckoutStatistics(CheckoutStatistics totalCheckoutStatistics) {
        this.totalCheckoutStatistics = totalCheckoutStatistics;
    }

}
