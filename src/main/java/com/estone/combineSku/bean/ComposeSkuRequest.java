package com.estone.combineSku.bean;

import lombok.Data;

import java.util.List;

/**
 * @Auther yucm
 * @Date 2024/4/9
 */
@Data
public class ComposeSkuRequest {

    /**
     * 修改时间范围-开始
     */
    private String updateAtBegin;

    /**
     * 修改时间范围-结束
     */
    private String updateAtEnd;

    /**
     * 组合SPU
     */
    private List<String> composeSkus;

    /**
     * 是否查询所有
     */
    private Boolean getAll;

    /**
     * 上一页的最大ID
     */
    private Integer previousMaxId;
}
