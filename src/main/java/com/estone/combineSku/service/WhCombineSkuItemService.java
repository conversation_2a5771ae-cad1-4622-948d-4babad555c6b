package com.estone.combineSku.service;

import com.estone.combineSku.bean.WhCombineSkuItem;
import com.estone.combineSku.bean.WhCombineSkuItemQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;
import java.util.Set;

public interface WhCombineSkuItemService {
    List<WhCombineSkuItem> queryAllWhCombineSkuItems();

    List<WhCombineSkuItem> queryWhCombineSkuItems(WhCombineSkuItemQueryCondition query, Pager pager);

    WhCombineSkuItem getWhCombineSkuItem(Integer id);

    WhCombineSkuItem getWhCombineSkuItemDetail(Integer id);

    WhCombineSkuItem queryWhCombineSkuItem(WhCombineSkuItemQueryCondition query);

    void createWhCombineSkuItem(WhCombineSkuItem whCombineSkuItem);

    void batchCreateWhCombineSkuItem(List<WhCombineSkuItem> entityList);

    void deleteWhCombineSkuItem(Integer id);

    void updateWhCombineSkuItem(WhCombineSkuItem whCombineSkuItem);

    void batchUpdateWhCombineSkuItem(List<WhCombineSkuItem> entityList);

    void deleteWhCombineSkuItemByIds(Set<Integer> deleteSkuItem);
}