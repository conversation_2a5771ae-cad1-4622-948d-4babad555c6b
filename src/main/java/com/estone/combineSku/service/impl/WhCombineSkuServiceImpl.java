package com.estone.combineSku.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.estone.combineSku.bean.*;
import com.estone.combineSku.dao.WhCombineSkuDao;
import com.estone.combineSku.enums.WhCombineSkuStatus;
import com.estone.combineSku.service.WhCombineSkuItemService;
import com.estone.combineSku.service.WhCombineSkuService;
import com.estone.common.util.*;
import com.estone.common.util.model.ApiResult;
import com.estone.exquisite.service.BaseService;
import com.estone.sku.bean.WhSku;
import com.estone.sku.bean.WhSkuQueryCondition;
import com.estone.sku.dao.mapper.WhSkuDBField;
import com.estone.sku.dao.mapper.WhSkuStockMapper;
import com.estone.sku.enums.ProcessType;
import com.estone.sku.service.WhSkuService;
import com.estone.system.param.bean.SystemParam;
import com.estone.warehouse.bean.WhStock;
import com.estone.warehouse.bean.WhStockQueryCondition;
import com.estone.warehouse.dao.impl.WhStockDaoImpl;
import com.estone.warehouse.enums.LocationTagEnum;
import com.estone.warehouse.service.WhStockService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

@Service("whCombineSkuService")
@Slf4j
public class WhCombineSkuServiceImpl extends BaseService<WhCombineSkuQueryCondition,WhCombineSku> implements WhCombineSkuService {
    @Resource
    private WhCombineSkuDao whCombineSkuDao;

    @Resource
    private WhCombineSkuItemService whCombineSkuItemService;

    @Resource
    private WhSkuService whSkuService;

    @Resource
    private WhStockService whStockService;

    // 分页大小
    private static final Integer PAGE_SIZE = 1000;
    @Autowired
    private WhStockDaoImpl whStockDao;

    @Override
    public WhCombineSku getWhCombineSku(Integer id) {
        WhCombineSku whCombineSku = whCombineSkuDao.queryWhCombineSku(id);
        return whCombineSku;
    }

    @Override
    public WhCombineSku getWhCombineSkuDetail(Integer id) {
        WhCombineSku whCombineSku = whCombineSkuDao.queryWhCombineSku(id);
        // 关联查询
        return whCombineSku;
    }

    @Override
    public WhCombineSku queryWhCombineSku(WhCombineSkuQueryCondition query) {
        Assert.notNull(query, "query is null!");
        WhCombineSku whCombineSku = whCombineSkuDao.queryWhCombineSku(query);
        return whCombineSku;
    }

    @Override
    public List<WhCombineSku> queryAllWhCombineSkus() {
        return whCombineSkuDao.queryWhCombineSkuList();
    }

    @Override
    public List<WhCombineSku> list(WhCombineSkuQueryCondition search, Pager pager) {
        return queryWhCombineSkus(search,pager);
    }

    @Override
    public List<WhCombineSku> queryWhCombineSkus(WhCombineSkuQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = whCombineSkuDao.queryWhCombineSkuCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<>();
            }
        }
        List<WhCombineSku> whCombineSkus = whCombineSkuDao.queryWhCombineSkuList(query, pager);
        // 组装库存信息
        if (query.isStockFlag() && CollectionUtils.isNotEmpty(whCombineSkus)) {
            WhStockQueryCondition whStockQueryCondition = new WhStockQueryCondition();
            whStockQueryCondition.setSkus(whCombineSkus.stream().map(w -> w.getSpu()).collect(Collectors.toList()));
            whStockQueryCondition.setLocationTag(LocationTagEnum.COMBINE_SKU.getCode());
            whStockQueryCondition.setLocationNumber(query.getLocationNumber());
            List<WhStock> whStocks = whStockDao.queryPageStocks(whStockQueryCondition, null);
            if (CollectionUtils.isNotEmpty(whStocks)){
                Map<String, List<WhStock>> skuMap = whStocks.stream().collect(Collectors.groupingBy(w -> w.getSku()));
                for (WhCombineSku whCombineSku:whCombineSkus) {
                    whCombineSku.setWhStockList(skuMap.get(whCombineSku.getSpu()));
                }
            }
        }
        return whCombineSkus;
    }

    @Override
    public void createWhCombineSku(WhCombineSku whCombineSku) {
        try {
            whCombineSkuDao.createWhCombineSku(whCombineSku);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateWhCombineSku(List<WhCombineSku> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whCombineSkuDao.batchCreateWhCombineSku(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteWhCombineSku(Integer id) {
        try {
            whCombineSkuDao.deleteWhCombineSku(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateWhCombineSku(WhCombineSku whCombineSku) {
        try {
            whCombineSkuDao.updateWhCombineSku(whCombineSku);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateWhCombineSku(List<WhCombineSku> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whCombineSkuDao.batchUpdateWhCombineSku(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    // 同步sku
    @Override
    public Integer syncCombineSku(ComposeSkuRequest composeSkuRequest) {

        Assert.notNull(composeSkuRequest, "query is null!");

        try {
            // 调用产品http接口查询sku信息
            SystemParam systemParam = CacheUtils.SystemParamGet("product_system.SYNC_COMBINE_SKU_URL");

            if (null == systemParam) {
                log.error("systemParam product_system.SYNC_COMBINE_SKU_URL is null");
                throw new RuntimeException("systemParam product_system.SYNC_COMBINE_SKU_URL is null");
            }

            String url = systemParam.getParamValue();
            if (StringUtils.isBlank(url)) {
                log.error("systemParam paramValue is null");
                throw new RuntimeException("systemParam paramValue is null");
            }
            log.info(String.format("同步组合sku，参数，updateAtBegin：【%s】,composeSkus：【%s】,previousMaxId：【%s】,getAll：【%s】",
                    composeSkuRequest.getUpdateAtBegin(),StringUtils.join(composeSkuRequest.getComposeSkus(),","),
                    composeSkuRequest.getPreviousMaxId(),composeSkuRequest.getGetAll()));
            ApiResult<List<Object>> apiResult = HttpExtendUtils.post(url,HttpUtils.ACCESS_TOKEN,
                    JSONObject.toJSON(composeSkuRequest), ApiResult.class, 120 * 1000, 120 * 1000);
            if (null != apiResult && apiResult.isSuccess()) {
                List<WmsComposeSkuResponse> result = BeanConvertUtils.convertList(apiResult.getResult(), WmsComposeSkuResponse.class);
                //List<WmsComposeSkuResponse> result = apiResult.getResult();
                if (CollectionUtils.isNotEmpty(result)) {
                    Map<String, StringBuilder> logInfoMap = ((WhCombineSkuServiceImpl) AopContext.currentProxy()).doAssembleData(result);
                    // 记录日志
                    if (MapUtils.isNotEmpty(logInfoMap)) {
                        WhCombineSkuQueryCondition queryCondition = new WhCombineSkuQueryCondition();
                        queryCondition.setSpu(StringUtils.join(logInfoMap.keySet(), ","));
                        Map<String, Integer> spuIdMap = whCombineSkuDao.queryIdBySpus(queryCondition);
                        for (Map.Entry<String, Integer> entrie : spuIdMap.entrySet()) {
                            SystemLogUtils.COMBINE_SKU.log(entrie.getValue(), logInfoMap.get(entrie.getKey()).toString());
                        }
                    }
                    log.info(String.format("同步组合sku成功，size：【%s】", result.size()));
                    // 继续获取下一页
                    if (result.size() >= PAGE_SIZE){
                        return result.stream().mapToInt(r -> r.getId()).max().orElse(0);
                    }
                }
            }else{
                throw new RuntimeException(apiResult.getErrorMsg());
            }
        }
        catch (Exception e) {
            log.error("查询产品系统组合sku信息异常！", e);
            throw new RuntimeException(e);
        }
        return null;
    }


    // 同步组合suku  需要事务,必须实现类重写
    @Override
    public Map<String,StringBuilder> doAssembleData(List<WmsComposeSkuResponse> result) {
        List<String> composeSkus = result.stream().map(r -> r.getComposeSku()).collect(Collectors.toList());
        WhCombineSkuQueryCondition queryCondition = new WhCombineSkuQueryCondition();
        queryCondition.setSpu(StringUtils.join(composeSkus,","));
        List<WhCombineSku> whCombineSkus = queryWhCombineSkus(queryCondition, null);
        // 新增
        List<WhCombineSku> insertWhCombineSku = new ArrayList<>();
        List<WhCombineSkuItem> insertWhCombineSkuItem = new ArrayList<>();
        // 更新
        List<WhCombineSku> updateWhCombineSku = new ArrayList<>();
        List<WhCombineSkuItem> updateWhCombineSkuItem = new ArrayList<>();
        // 删除
        Set<Integer> deleteSkuItem = new HashSet<>();
        Map<String, WhCombineSku> whCombineSkuMap = whCombineSkus.stream().collect(Collectors.toMap(w -> w.getSpu(), w -> w, (w1, w2) -> w2));
        Map<String,StringBuilder> logInfo = new HashMap<>();
        for (WmsComposeSkuResponse response : result) {
            WhCombineSku existWhCombineSku = whCombineSkuMap.get(response.getComposeSku());
            // 新增
            if (existWhCombineSku == null) {
                WhCombineSku whCombineSku = builderWhCombineSku(response,null);
                insertWhCombineSkuItem.addAll(builderWhCombineSkuItem(response,whCombineSku));
                calcWeight(whCombineSku);
                insertWhCombineSku.add(whCombineSku);
                logInfo.put(whCombineSku.getSpu(),new StringBuilder(String.format("创建组装sku[%s]；",whCombineSku.getSpu())));
                continue;
            }
            // 更新
            List<WhCombineSkuItem> itemList = existWhCombineSku.getItemList();
            Map<String, WhCombineSkuItem> itemMap = itemList.stream().collect(Collectors.toMap(w -> w.getSku(), w -> w, (w1, w2) -> w2));
            Set<Integer> delIdSet = itemList.stream().map(i -> i.getId()).collect(Collectors.toSet());
            // 校验明细是否有变动
            StringBuilder changeItemInfo = new StringBuilder();
            for (Map.Entry<String, Integer> entrie : response.getSkuNumberMap().entrySet()) {
                WhCombineSkuItem whCombineSkuItem = itemMap.get(entrie.getKey());
                if (whCombineSkuItem != null){
                    if (!whCombineSkuItem.getQuantity().equals(entrie.getValue())) {
                        changeItemInfo.append(String.format("sku[%s]数量变更，原:[%s],新:[%s]；",entrie.getKey(),whCombineSkuItem.getQuantity(),entrie.getValue()));
                        whCombineSkuItem.setQuantity(entrie.getValue());
                        updateWhCombineSkuItem.add(whCombineSkuItem);
                    }
                    delIdSet.remove(whCombineSkuItem.getId());
                } else {
                    insertWhCombineSkuItem.add(WhCombineSkuItem.builder().spu(existWhCombineSku.getSpu()).sku(entrie.getKey()).quantity(entrie.getValue()).build());
                    changeItemInfo.append(String.format("新增sku[%s]；",insertWhCombineSkuItem.stream().map(i -> i.getSku()).collect(Collectors.joining(","))));
                }
            }
            if (CollectionUtils.isNotEmpty(delIdSet)){
                deleteSkuItem.addAll(delIdSet);
                changeItemInfo.append(String.format("删除sku[%s]；",itemList.stream().filter(i -> deleteSkuItem.contains(i.getId())).map(i -> i.getSku())
                        .collect(Collectors.joining(","))));
            }
            // 明细无变动，校验列表是否有变动
            StringBuilder changeInfo = compareWhCombineSku(response, existWhCombineSku);
            changeInfo.append(changeItemInfo);
            // 明细有变动，列表一起更新
            if (StringUtils.isNotBlank(changeItemInfo)) {
                calcWeight(existWhCombineSku);
            }
            if (StringUtils.isNotBlank(changeInfo)) {
                updateWhCombineSku.add(builderWhCombineSku(response, existWhCombineSku));
                logInfo.put(existWhCombineSku.getSpu(), changeInfo);
            }
        }
       // 删除
        whCombineSkuItemService.deleteWhCombineSkuItemByIds(deleteSkuItem);
        // 保存数据
        whCombineSkuItemService.batchCreateWhCombineSkuItem(insertWhCombineSkuItem);
        whCombineSkuDao.batchCreateWhCombineSku(insertWhCombineSku);
        // 更新
        whCombineSkuDao.batchUpdateWhCombineSku(updateWhCombineSku);
        whCombineSkuItemService.batchUpdateWhCombineSkuItem(updateWhCombineSkuItem);
        return logInfo;
    }

    @Override
    public List<WhSku> convertWhSkuListByLocation(String location) {
        List<WhSku> result = new ArrayList<>();
        if (StringUtils.isBlank(location)){
            return result;
        }
        WhStockQueryCondition whStockQueryCondition = new WhStockQueryCondition();
        whStockQueryCondition.setLocationTag(LocationTagEnum.COMBINE_SKU.getCode());
        whStockQueryCondition.setLocationNumber(location);
        List<WhStock> whStocks = whStockService.queryWhStocks(whStockQueryCondition, null);
        if (CollectionUtils.isEmpty(whStocks)) {
            return result;
        }
        WhCombineSkuQueryCondition whCombineSkuQueryCondition = new WhCombineSkuQueryCondition();
        List<String> skuList = whStocks.stream().map(WhStock::getSku).collect(Collectors.toList());
        whCombineSkuQueryCondition.setSpus(skuList);
        whCombineSkuQueryCondition.setStockFlag(true);
        whCombineSkuQueryCondition.setLocationNumber(location);
        List<WhCombineSku> whCombineSkus = this.queryWhCombineSkus(whCombineSkuQueryCondition, null);
        for(WhCombineSku combineSku : whCombineSkus) {
            if (CollectionUtils.isEmpty(combineSku.getWhStockList())){
                continue;
            }
            result.addAll(combineSku.batchCovertWhSku());
        }
        return result;
    }

    @Override
    public Map<String, String> queryLocationNumberBySpu(String spu) {
        SqlerRequest request = new SqlerRequest("queryLocationNumberBySpu");
        request.addDataParam("spu", DataType.STRING, spu);
        request.setReadOnly(true);
        return SqlerTemplate.queryForObject(request, new RowMapper<Map<String, String>>() {
            @Override
            public Map<String, String> mapRow(ResultSet resultSet, int i) throws SQLException {
                Map<String, String> map = new HashMap<>();
                map.put("locationNumber", resultSet.getString("locationNumber"));
                return map;
            }
        });
    }

    @Override
    public List<WhSku> queryWhCombineSkuStocks(String spu) {
        SqlerRequest request = new SqlerRequest("queryWhCombineSkuStocks");
        request.addDataParam("spu", DataType.STRING, spu);
        request.setReadOnly(true);
        return SqlerTemplate.query(request, new RowMapper<WhSku>() {
            @Override
            public WhSku mapRow(ResultSet rs, int i) throws SQLException {
                WhSku entity = new WhSku();
                entity.setId(rs.getObject(WhSkuDBField.ID) == null ? null : rs.getInt(WhSkuDBField.ID));
                entity.setSku(rs.getString(WhSkuDBField.SKU));
                entity.setName(rs.getString(WhSkuDBField.NAME));
                entity.setImageUrl(rs.getString(WhSkuDBField.IMAGE_URL));
                entity.setWarehouseId(
                        rs.getObject(WhSkuDBField.WAREHOUSE_ID) == null ? null : rs.getInt(WhSkuDBField.WAREHOUSE_ID));
                // 库存
                WhStock stock = new WhStock();
                stock.setId(
                        rs.getObject("wr.id") == null ? 0 : rs.getInt("wr.id"));
                stock.setSurplusQuantity(
                        rs.getObject("wr.surplus_quantity") == null ? 0 : rs.getInt("wr.surplus_quantity"));
                stock.setPickNotQuantity(
                        rs.getObject("wr.pick_not_quantity") == null ? 0 : rs.getInt("wr.pick_not_quantity"));
                stock.setPickQuantity(rs.getObject("wr.pick_quantity") == null ? 0 : rs.getInt("wr.pick_quantity"));
                stock.setCancelQuantity(rs.getObject("wr.cancel_quantity") == null ? 0 : rs.getInt("wr.cancel_quantity"));
                stock.setQcQuantity(rs.getObject("wr.qc_quantity") == null ? 0 : rs.getInt("wr.qc_quantity"));
                stock.setWaitingUpQuantity(
                        rs.getObject("wr.waiting_up_quantity") == null ? 0 : rs.getInt("wr.waiting_up_quantity"));
                stock.setUpQuantity(rs.getObject("wr.up_quantity") == null ? 0 : rs.getInt("wr.up_quantity"));
                stock.setAllotQuantity(rs.getObject("wr.allot_quantity") == null ? 0 : rs.getInt("wr.allot_quantity"));
                stock.setPickReturnQuantity(
                        rs.getObject("wr.pick_return_quantity") == null ? 0 : rs.getInt("wr.pick_return_quantity"));
                // 待QC+待上架+上架中+可用库存+已分配+已拣库存+取消库存+拣货缺货库存+已拣返架
                stock.setQuantity(stock.getQcQuantity() + stock.getWaitingUpQuantity() + stock.getUpQuantity()
                        + stock.getSurplusQuantity() + stock.getAllotQuantity() + stock.getCancelQuantity()
                        + stock.getPickNotQuantity() + stock.getPickQuantity() + stock.getPickReturnQuantity());
                // 可用库存+已分配
                stock.setLocationQuantity(stock.getSurplusQuantity() + stock.getAllotQuantity());

                entity.setWhStock(stock);
                return entity;
            }
        });
    }

    // 构建WhCombineSku 对象
    public WhCombineSku builderWhCombineSku(WmsComposeSkuResponse response,WhCombineSku whCombineSku) {
        if (whCombineSku == null) {
            whCombineSku = new WhCombineSku();
            whCombineSku.setSpu(response.getComposeSku());
        }
        whCombineSku.setStatus(response.getComposeStatus());
        whCombineSku.setName(response.getName());
        whCombineSku.setImageUrl(response.getPic());
        whCombineSku.setProductCreatedBy(response.getUser());
        return whCombineSku;
    }
    // 构建WhCombineSkuItem
    public List<WhCombineSkuItem> builderWhCombineSkuItem(WmsComposeSkuResponse response,WhCombineSku whCombineSku) {
        whCombineSku.setItemList(response.getSkuNumberMap().entrySet().stream().map(entrie ->
                WhCombineSkuItem.builder().spu(whCombineSku.getSpu()).sku(entrie.getKey()).quantity(entrie.getValue()).build()).collect(Collectors.toList()));
        return whCombineSku.getItemList();
    }
    // 对比属性
    public StringBuilder compareWhCombineSku(WmsComposeSkuResponse response,WhCombineSku whCombineSku) {
       StringBuilder changeInfo = new StringBuilder();
       if (!StringUtils.equalsIgnoreCase(whCombineSku.getName(),response.getName()))
           changeInfo.append(String.format("组合标题变更，原:[%s]，新:[%s]；",whCombineSku.getName(),response.getName()));
       if ((whCombineSku.getStatus() == null &&  response.getComposeStatus() != null)
               || (whCombineSku.getStatus() != null &&  response.getComposeStatus() == null)
                || (whCombineSku.getStatus() != null && response.getComposeStatus() != null &&  !whCombineSku.getStatus().equals(response.getComposeStatus())))
            changeInfo.append(String.format("状态变更，原:[%s]，新:[%s]；", (whCombineSku.getStatus() == null ? null : WhCombineSkuStatus.getByCodeName(whCombineSku.getStatus()))
                    ,(response.getComposeStatus() == null ? null : WhCombineSkuStatus.getByCodeName(response.getComposeStatus()))));
       if (!StringUtils.equalsIgnoreCase(whCombineSku.getImageUrl(),response.getPic()))
            changeInfo.append(String.format("图片变更，原:[%s]，新:[%s]；",whCombineSku.getImageUrl(),response.getPic()));
       if (!StringUtils.equalsIgnoreCase(whCombineSku.getName(),response.getName()))
            changeInfo.append(String.format(";创建人变更，原:[%s]，新:[%s]；",whCombineSku.getProductCreatedBy(),response.getUser()));
       return changeInfo;
    }

    // 计算重量
    private void calcWeight(WhCombineSku whCombineSku) {
        // 获取所有sku
        Set<String> skuSet = whCombineSku.getItemList().stream().map(i -> i.getSku()).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(skuSet)) return;
        WhSkuQueryCondition queryCondition = new WhSkuQueryCondition();
        queryCondition.setSkus(new ArrayList<>(skuSet));
        List<WhSku> whSkus = whSkuService.queryWhSkus(queryCondition, null);
        Map<String, Double> skuWeightMap = whSkus.stream().filter(w -> w.getCustomsWeight() != null).collect(Collectors.toMap(w -> w.getSku(), w -> w.getCustomsWeight()));
        Map<String, Double> skuNetWeightMap = whSkus.stream().filter(w -> w.getNetWeight() != null).collect(Collectors.toMap(w -> w.getSku(), w -> w.getNetWeight()));
        Double weight = 0d,netWeight = 0d;
        for (WhCombineSkuItem i: whCombineSku.getItemList()){
            Integer quantity = i.getQuantity();
            if (quantity == null || quantity == 0) continue; ;
            weight +=((skuWeightMap.get(i.getSku()) == null ? 0 : skuWeightMap.get(i.getSku())) * quantity);
            netWeight += ((skuNetWeightMap.get(i.getSku()) == null ? 0 : skuNetWeightMap.get(i.getSku())) * quantity);
        }
        whCombineSku.setCustomsWeight(weight);
        whCombineSku.setNetWeight(netWeight);
    }


}