package com.estone.allocation.dao;

import java.util.List;

import com.estone.allocation.bean.WhApvAllocationDemand;
import com.estone.allocation.bean.WhApvAllocationDemandItem;
import com.estone.allocation.bean.WhApvAllocationDemandQueryCondition;
import com.estone.allocation.bean.WhApvAllocationDemandSku;
import com.whq.tool.component.Pager;

public interface WhApvAllocationDemandDao {

    // 根据主键查询调拨需求
    WhApvAllocationDemand queryWhApvAllocationDemandByPrimaryKey(Integer primaryKey);

    List<WhApvAllocationDemand> queryWhApvAllocationDemands(WhApvAllocationDemandQueryCondition query);

    WhApvAllocationDemand queryWhApvAllocationDemand(WhApvAllocationDemandQueryCondition query);

    // 调拨需求数量
    int queryWhApvAllocationDemandCount(WhApvAllocationDemandQueryCondition query);

    // 分页查询调拨需求
    List<WhApvAllocationDemand> queryWhApvAllocationDemandList(WhApvAllocationDemandQueryCondition query, Pager pager);

    // 查询调拨单item apv_item
    List<WhApvAllocationDemandItem> queryWhApvAllocationDemandItems(WhApvAllocationDemandQueryCondition query);

    List<WhApvAllocationDemandItem> queryWhApvAllocationDemandItemList(WhApvAllocationDemandQueryCondition query);

    List<WhApvAllocationDemandSku> queryWhApvAllocationDemandSkus(WhApvAllocationDemandQueryCondition query);

    List<WhApvAllocationDemandSku> queryWhApvAllocationDemandSkuList(WhApvAllocationDemandQueryCondition query);

    WhApvAllocationDemandSku queryWhApvAllocationDemandSku(WhApvAllocationDemandQueryCondition query);

    // 创建订单调拨需求
    void createWhApvAllocationDemand(WhApvAllocationDemand entity);

    // 创建订单调拨需求item
    void batchCreateWhApvAllocationDemandItem(List<WhApvAllocationDemandItem> entityList);

    // 创建订单调拨需求sku
    void batchCreateWhApvAllocationDemandSku(List<WhApvAllocationDemandSku> entityList);

    void updateWhApvAllocationDemand(WhApvAllocationDemand entity);

    void batchUpdateWhApvAllocationDemand(List<WhApvAllocationDemand> entityList);

    List<WhApvAllocationDemand> queryWhApvAllocationDemandAndSku(WhApvAllocationDemandQueryCondition query);

    // PDA订单调拨拣货完成 修改调拨需求sku数据
    void updateWhApvAllocationDemandSku(WhApvAllocationDemandSku entity);

    // PDA订单调拨拣货完成 修改调拨需求sku的已拣数量
    void updateWhApvAllocationDemandSkuToPick(WhApvAllocationDemandSku entity);

    // PDA订单调拨拣货完成 修改调拨需求item数据
    int updateWhApvAllocationDemandItem(WhApvAllocationDemandItem entity);

    /**
     * 批量更新调拨APV数据
     * @param demandItems
     */
    void batchUpdateWhApvAllocationDemandItemsData(List<WhApvAllocationDemandItem> demandItems);

    // PDA订单调拨拣货完成 修改调拨需求sku的已播数量
    public void updateWhApvAllocationDemandSkuToSow(WhApvAllocationDemandSku entity);

}