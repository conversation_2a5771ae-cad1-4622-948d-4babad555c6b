package com.estone.allocation.dao.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.estone.allocation.bean.WhApvAllocationBoard;

public class WhApvAllocationBoardMapper implements RowMapper<WhApvAllocationBoard> {

    /**
     * This method corresponds to the database table wh_allocation_board
     *
     * @mbggenerated Fri Mar 08 14:00:28 CST 2019
     */
    public WhApvAllocationBoard mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhApvAllocationBoard entity = new WhApvAllocationBoard();
        entity.setBoardId(rs.getObject(WhApvAllocationBoardDBField.BOARD_ID) == null ? null
                : rs.getInt(WhApvAllocationBoardDBField.BOARD_ID));
        entity.setBoardNo(rs.getString(WhApvAllocationBoardDBField.BOARD_NO));
        entity.setWarehouseId(rs.getObject(WhApvAllocationBoardDBField.WAREHOUSE_ID) == null ? null
                : rs.getInt(WhApvAllocationBoardDBField.WAREHOUSE_ID));
        entity.setBoardType(rs.getInt(WhApvAllocationBoardDBField.BOARD_TYPE));
        entity.setBoardStatus(rs.getInt(WhApvAllocationBoardDBField.BOARD_STATUS));
        entity.setBindUser(rs.getObject(WhApvAllocationBoardDBField.BIND_USER) == null ? null
                : rs.getInt(WhApvAllocationBoardDBField.BIND_USER));
        entity.setBindId(rs.getString(WhApvAllocationBoardDBField.BIND_ID));
        entity.setBindTime(rs.getTimestamp(WhApvAllocationBoardDBField.BIND_TIME));
        entity.setPrintStatus(rs.getInt(WhApvAllocationBoardDBField.PRINT_STATUS));
        entity.setCreateBy(rs.getObject(WhApvAllocationBoardDBField.CREATE_BY) == null ? null
                : rs.getInt(WhApvAllocationBoardDBField.CREATE_BY));
        entity.setCreateTime(rs.getTimestamp(WhApvAllocationBoardDBField.CREATE_TIME));
        entity.setRemark(rs.getString(WhApvAllocationBoardDBField.REMARK));
        return entity;
    }
}