package com.estone.allocation.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.allocation.bean.AcrossInventoryRecord;
import com.estone.allocation.bean.AcrossInventoryRecordQueryCondition;
import com.estone.allocation.dao.AcrossInventoryRecordDao;
import com.estone.allocation.dao.mapper.AcrossInventoryRecordDBField;
import com.estone.allocation.dao.mapper.AcrossInventoryRecordMapper;
import com.estone.common.util.SqlerTemplate;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;

@Repository("acrossInventoryRecordDao")
public class AcrossInventoryRecordDaoImpl implements AcrossInventoryRecordDao {

    private void setQueryCondition(SqlerRequest request, AcrossInventoryRecordQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);

        request.addDataParam(AcrossInventoryRecordDBField.ID, DataType.INT, query.getId());
        request.addDataParam(AcrossInventoryRecordDBField.APV_NO, DataType.STRING, query.getApvNo());
        request.addDataParam(AcrossInventoryRecordDBField.MATCH_REQUEST_STATUS, DataType.INT,
                query.getMatchRequestStatus());
        request.addDataParam(AcrossInventoryRecordDBField.MOVE_REQUEST_STATUS, DataType.INT,
                query.getMoveRequestStatus());
        request.addDataParam(AcrossInventoryRecordDBField.CANCLE_REQUEST_STATUS, DataType.INT,
                query.getCancleRequestStatus());
        request.addDataParam(AcrossInventoryRecordDBField.CREATE_TIME, DataType.TIMESTAMP, query.getCreateTime());
        request.addDataParam(AcrossInventoryRecordDBField.UPDATE_TIME, DataType.TIMESTAMP, query.getUpdateTime());
        request.addDataParam(AcrossInventoryRecordDBField.SKU_OPTION, DataType.STRING, query.getSkuOption());
    }

    /**
     * This method corresponds to the database table across_inventory_record
     *
     * @mbggenerated Thu Apr 11 16:29:38 CST 2019
     */
    public int queryAcrossInventoryRecordCount(AcrossInventoryRecordQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryAcrossInventoryRecordCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    /**
     * This method corresponds to the database table across_inventory_record
     *
     * @mbggenerated Thu Apr 11 16:29:38 CST 2019
     */
    public List<AcrossInventoryRecord> queryAcrossInventoryRecordList() {
        SqlerRequest request = new SqlerRequest("queryAcrossInventoryRecordList");
        return SqlerTemplate.query(request, new AcrossInventoryRecordMapper());
    }

    /**
     * This method corresponds to the database table across_inventory_record
     *
     * @mbggenerated Thu Apr 11 16:29:38 CST 2019
     */
    public List<AcrossInventoryRecord> queryAcrossInventoryRecordList(AcrossInventoryRecordQueryCondition query,
            Pager pager) {
        SqlerRequest request = new SqlerRequest("queryAcrossInventoryRecordList");
        setQueryCondition(request, query);
        if (pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new AcrossInventoryRecordMapper());
    }

    /**
     * This method corresponds to the database table across_inventory_record
     *
     * @mbggenerated Thu Apr 11 16:29:38 CST 2019
     */
    public AcrossInventoryRecord queryAcrossInventoryRecord(Integer primaryKey) {
        Assert.notNull(primaryKey);
        SqlerRequest request = new SqlerRequest("queryAcrossInventoryRecordByPrimaryKey");
        request.addDataParam(AcrossInventoryRecordDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new AcrossInventoryRecordMapper());
    }

    /**
     * This method corresponds to the database table across_inventory_record
     *
     * @mbggenerated Thu Apr 11 16:29:38 CST 2019
     */
    public AcrossInventoryRecord queryAcrossInventoryRecord(AcrossInventoryRecordQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryAcrossInventoryRecord");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new AcrossInventoryRecordMapper());
    }

    /**
     * This method corresponds to the database table across_inventory_record
     *
     * @mbggenerated Thu Apr 11 16:29:38 CST 2019
     */
    public int createAcrossInventoryRecord(AcrossInventoryRecord entity) {
        SqlerRequest request = new SqlerRequest("createAcrossInventoryRecord");
        request.addDataParam(AcrossInventoryRecordDBField.APV_NO, DataType.STRING, entity.getApvNo());
        request.addDataParam(AcrossInventoryRecordDBField.MATCH_REQUEST_STATUS, DataType.INT,
                entity.getMatchRequestStatus());
        request.addDataParam(AcrossInventoryRecordDBField.MOVE_REQUEST_STATUS, DataType.INT,
                entity.getMoveRequestStatus());
        request.addDataParam(AcrossInventoryRecordDBField.CANCLE_REQUEST_STATUS, DataType.INT,
                entity.getCancleRequestStatus());
        request.addDataParam(AcrossInventoryRecordDBField.CREATE_TIME, DataType.TIMESTAMP, entity.getCreateTime());
        request.addDataParam(AcrossInventoryRecordDBField.UPDATE_TIME, DataType.TIMESTAMP, entity.getUpdateTime());
        request.addDataParam(AcrossInventoryRecordDBField.SKU_OPTION, DataType.STRING, entity.getSkuOption());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
        return autoIncrementId;
    }

    /**
     * This method corresponds to the database table across_inventory_record
     *
     * @mbggenerated Thu Apr 11 16:29:38 CST 2019
     */
    public int updateAcrossInventoryRecord(AcrossInventoryRecord entity) {
        SqlerRequest request = new SqlerRequest("updateAcrossInventoryRecordByPrimaryKey");
        request.addDataParam(AcrossInventoryRecordDBField.ID, DataType.INT, entity.getId());

        request.addDataParam(AcrossInventoryRecordDBField.APV_NO, DataType.STRING, entity.getApvNo());
        request.addDataParam(AcrossInventoryRecordDBField.MATCH_REQUEST_STATUS, DataType.INT,
                entity.getMatchRequestStatus());
        request.addDataParam(AcrossInventoryRecordDBField.MOVE_REQUEST_STATUS, DataType.INT,
                entity.getMoveRequestStatus());
        request.addDataParam(AcrossInventoryRecordDBField.CANCLE_REQUEST_STATUS, DataType.INT,
                entity.getCancleRequestStatus());
        request.addDataParam(AcrossInventoryRecordDBField.CREATE_TIME, DataType.TIMESTAMP, entity.getCreateTime());
        request.addDataParam(AcrossInventoryRecordDBField.UPDATE_TIME, DataType.TIMESTAMP, entity.getUpdateTime());
        request.addDataParam(AcrossInventoryRecordDBField.SKU_OPTION, DataType.STRING, entity.getSkuOption());
        return SqlerTemplate.execute(request);
    }

    /**
     * This method corresponds to the database table across_inventory_record
     *
     * @mbggenerated Thu Apr 11 16:29:38 CST 2019
     */
    public void batchCreateAcrossInventoryRecord(List<AcrossInventoryRecord> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createAcrossInventoryRecord");
            for (AcrossInventoryRecord entity : entityList) {
                request.addBatchDataParam(AcrossInventoryRecordDBField.APV_NO, DataType.STRING, entity.getApvNo());
                request.addBatchDataParam(AcrossInventoryRecordDBField.MATCH_REQUEST_STATUS, DataType.INT,
                        entity.getMatchRequestStatus());
                request.addBatchDataParam(AcrossInventoryRecordDBField.MOVE_REQUEST_STATUS, DataType.INT,
                        entity.getMoveRequestStatus());
                request.addBatchDataParam(AcrossInventoryRecordDBField.CANCLE_REQUEST_STATUS, DataType.INT,
                        entity.getCancleRequestStatus());
                request.addBatchDataParam(AcrossInventoryRecordDBField.CREATE_TIME, DataType.TIMESTAMP,
                        entity.getCreateTime());
                request.addBatchDataParam(AcrossInventoryRecordDBField.UPDATE_TIME, DataType.TIMESTAMP,
                        entity.getUpdateTime());
                request.addBatchDataParam(AcrossInventoryRecordDBField.SKU_OPTION, DataType.STRING,
                        entity.getSkuOption());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    /**
     * This method corresponds to the database table across_inventory_record
     *
     * @mbggenerated Thu Apr 11 16:29:38 CST 2019
     */
    public void batchUpdateAcrossInventoryRecord(List<AcrossInventoryRecord> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateAcrossInventoryRecordByPrimaryKey");
            for (AcrossInventoryRecord entity : entityList) {
                request.addBatchDataParam(AcrossInventoryRecordDBField.ID, DataType.INT, entity.getId());

                request.addBatchDataParam(AcrossInventoryRecordDBField.APV_NO, DataType.STRING, entity.getApvNo());
                request.addBatchDataParam(AcrossInventoryRecordDBField.MATCH_REQUEST_STATUS, DataType.INT,
                        entity.getMatchRequestStatus());
                request.addBatchDataParam(AcrossInventoryRecordDBField.MOVE_REQUEST_STATUS, DataType.INT,
                        entity.getMoveRequestStatus());
                request.addBatchDataParam(AcrossInventoryRecordDBField.CANCLE_REQUEST_STATUS, DataType.INT,
                        entity.getCancleRequestStatus());
                request.addBatchDataParam(AcrossInventoryRecordDBField.CREATE_TIME, DataType.TIMESTAMP,
                        entity.getCreateTime());
                request.addBatchDataParam(AcrossInventoryRecordDBField.UPDATE_TIME, DataType.TIMESTAMP,
                        entity.getUpdateTime());
                request.addBatchDataParam(AcrossInventoryRecordDBField.SKU_OPTION, DataType.STRING,
                        entity.getSkuOption());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    /**
     * This method corresponds to the database table across_inventory_record
     *
     * @mbggenerated Thu Apr 11 16:29:38 CST 2019
     */
    public void deleteAcrossInventoryRecord(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteAcrossInventoryRecordByPrimaryKey");
        request.addDataParam(AcrossInventoryRecordDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}