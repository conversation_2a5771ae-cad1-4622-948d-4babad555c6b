package com.estone.allocation.service;

import java.util.List;

import com.estone.allocation.bean.WhApvAllocation;
import com.estone.allocation.bean.WhApvAllocationDemand;
import com.estone.allocation.bean.WhApvAllocationOrderItem;
import com.estone.allocation.bean.WhApvAllocationOrderSku;
import com.estone.allocation.bean.WhApvAllocationQueryCondition;
import com.whq.tool.component.Pager;

public interface WhApvAllocationOrderService {

    // 根据条件查询订单调拨单
    WhApvAllocation queryApvAllocationOrder(WhApvAllocationQueryCondition query);

    // 分页查询订单调拨单
    List<WhApvAllocation> queryApvAllocationOrderList(WhApvAllocationQueryCondition query, Pager pager);

    // 分页查询调订单拨单sku详情
    WhApvAllocation queryApvAllocationOrderSkuList(WhApvAllocationQueryCondition query, Pager pager);

    // 生成订单调拨单
    void generateApvAllocationOrder(Integer batchLimit, List<WhApvAllocationDemand> entityList);
    
    // 生成订单调拨单, 库位数量切割多个拣货任务
    void generateApvAllocationOrder(Integer batchLimit, Integer locationNum, List<WhApvAllocationDemand> entityList);

    // 查询订单调拨item表
    WhApvAllocationOrderItem queryApvAllocationOrderItem(WhApvAllocationOrderItem whApvAllocationOrderItem);

    // 查询订单调拨sku表
    WhApvAllocationOrderSku queryApvAllocationOrderSku(WhApvAllocationOrderSku whApvAllocationOrderSku);

    // 不分页查询订单调拨单SKU信息列表（PDA拣货需要获取拣货列表）
    List<WhApvAllocationOrderSku> queryApvAllocationOrderSkus(WhApvAllocationOrderSku whApvAllocationOrderSku);

    // 修改sku的拣货数量（PDA订单调拨拣货下一步调用）
    int updateApvAllocationOrderSku(WhApvAllocationOrderSku whApvAllocationOrderSku);

    // 修改sku的已播数量（PDA订单调拨播种调用）
    int updateApvAllocationOrderSkuToSow(WhApvAllocationOrderSku whApvAllocationOrderSku);

    // 修改sku的调拨数量（PDA订单调拨播种调用）
    int updateApvAllocationOrderSkuToAllcoation(WhApvAllocationOrderSku whApvAllocationOrderSku);

    // 修改表中需播数量（PDA订单调拨拣货完成调用）
    int updateApvAllocationOrderSkuBySkuAndAllocationNo(WhApvAllocationOrderSku whApvAllocationOrderSku);

    // PDA拣货完成专用 查询拣货数量大于播种数量的记录
    List<WhApvAllocationOrderSku> queryApvAllocationOrderSkuForPickAndSow(
            WhApvAllocationOrderSku whApvAllocationOrderSku);

    // PDA播种专用
    WhApvAllocationOrderSku queryApvAllocationOrderSkuBySkuAndAllocationNo(
            WhApvAllocationOrderSku whApvAllocationOrderSku);

    List<WhApvAllocationOrderItem> queryWhApvAllocationOrderItems(List<Integer> allocationIdList);
}
