package com.estone.warehouse.service;

import com.estone.warehouse.bean.WhStockMove;
import com.estone.warehouse.bean.WhStockMoveItem;
import com.estone.warehouse.bean.WhStockMoveQueryCondition;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

import java.util.List;

public interface WhStockMoveService {
    List<WhStockMove> queryAllWhStockMoves();

    List<WhStockMove> queryWhStockMoves(WhStockMoveQueryCondition query, Pager pager);

    WhStockMove getWhStockMove(Integer id);

    WhStockMove getWhStockMoveDetail(Integer id);

    WhStockMove queryWhStockMove(WhStockMoveQueryCondition query);

    void createWhStockMove(WhStockMove whStockMove);

    void batchCreateWhStockMove(List<WhStockMove> entityList);

    void deleteWhStockMove(Integer id);

    void updateWhStockMove(WhStockMove whStockMove);

    void batchUpdateWhStockMove(List<WhStockMove> entityList);

    List<WhStockMove> queryWhStockMoveAndItems(WhStockMoveQueryCondition query, Pager pager);

    ResponseJson createStockMoveAndItems(List<String> skuList,List<WhStockMoveItem> whStockMoveItems,String remark);

    boolean updateStockByMoveStock(List<String> skuList,List<WhStockMoveItem> itemList);

    boolean updateTransferStockByMoveStock(List<String> skuList,List<WhStockMoveItem> itemList);

    /**
     * 自动调整库位
     *
     * @param skuList
     */
    void syncZeroStockLocation(List<String> skuList);
}