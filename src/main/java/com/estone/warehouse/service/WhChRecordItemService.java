package com.estone.warehouse.service;

import com.estone.warehouse.bean.WhChRecordItem;
import com.estone.warehouse.bean.WhChRecordItemQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhChRecordItemService {
    List<WhChRecordItem> queryAllWhChRecordItems();

    List<WhChRecordItem> queryWhChRecordItems(WhChRecordItemQueryCondition query, Pager pager);

    WhChRecordItem getWhChRecordItem(Integer id);

    WhChRecordItem getWhChRecordItemDetail(Integer id);

    WhChRecordItem queryWhChRecordItem(WhChRecordItemQueryCondition query);

    void createWhChRecordItem(WhChRecordItem whChRecordItem);

    void batchCreateWhChRecordItem(List<WhChRecordItem> entityList);

    void deleteWhChRecordItem(Integer id);

    void updateWhChRecordItem(WhChRecordItem whChRecordItem);

    void batchUpdateWhChRecordItem(List<WhChRecordItem> entityList);
}