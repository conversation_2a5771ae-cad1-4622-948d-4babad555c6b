package com.estone.warehouse.service;

import com.estone.android.domain.AndroidProductDo;
import com.estone.sku.bean.WhUniqueSku;
import com.estone.warehouse.bean.WhAbroadReturn;
import com.estone.warehouse.bean.WhAbroadReturnItem;
import com.estone.warehouse.bean.WhAbroadReturnItemQueryCondition;
import com.estone.warehouse.bean.WhBatchReturn;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

import java.util.List;

public interface WhAbroadReturnItemService {
    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    List<WhAbroadReturnItem> queryAllWhAbroadReturnItems();

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    List<WhAbroadReturnItem> queryWhAbroadReturnItems(WhAbroadReturnItemQueryCondition query, Pager pager);

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    WhAbroadReturnItem getWhAbroadReturnItem(Integer id);

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    WhAbroadReturnItem getWhAbroadReturnItemDetail(Integer id);

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    WhAbroadReturnItem queryWhAbroadReturnItem(WhAbroadReturnItemQueryCondition query);

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    void createWhAbroadReturnItem(WhAbroadReturnItem whAbroadReturnItem);

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    void batchCreateWhAbroadReturnItem(List<WhAbroadReturnItem> entityList);

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    void deleteWhAbroadReturnItem(Integer id);

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    int updateWhAbroadReturnItem(WhAbroadReturnItem whAbroadReturnItem);

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    void batchUpdateWhAbroadReturnItem(List<WhAbroadReturnItem> entityList);

    void deleteWhAbroadReturnItemByReturnId(Integer id);

    int queryWhAbroadReturnItemCount(WhAbroadReturnItemQueryCondition query);

    ResponseJson updateWhAbroadReturnItemAndComplete(List<String> skuList, WhAbroadReturnItem whAbroadReturnItem,
            AndroidProductDo domain);

    ResponseJson updateNormalWhAbroadReturnItemAndComplete(List<String> asList, WhAbroadReturnItem abroadReturnItem,
                                                           AndroidProductDo domain, WhAbroadReturn whAbroadReturn, WhBatchReturn whBatchReturn, WhUniqueSku uniqueSku);

    void doBindExpBatchNoAndUniqSku(Integer relationId, String relationNo, String uniqSku, WhAbroadReturnItem whAbroadReturnItem);

    void updateTemuReturnQuantity(List<String> apvNoList, String sku);
}