package com.estone.multiplelocation.dao.mapper;

/**
 * <AUTHOR>
 * @date 2023-02-24 17:26
 */
public interface ExpRuleSettingDBField {

    String EXP_LE = "exp_le";

    String LESS_AND_EQUALS_EXP_LE = "less_and_equals_exp_le";

    String GREATER_AND_EQUALS_EXP_LE = "greater_and_equals_exp_le";

    String LESS_AND_EQUALS_EXP_GE = "less_and_equals_exp_ge";

    String GREATER_AND_EQUALS_EXP_GE = "greater_and_equals_exp_ge";

    String EXP_GE = "exp_ge";

    String ID = "id";

    String SAME_LOCATION_EXP_SPAN = "same_location_exp_span";

    String DAY_TYPE = "day_type";
}
