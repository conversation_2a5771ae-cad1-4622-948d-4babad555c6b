package com.estone.multiplelocation.bean;

import com.estone.common.util.CommonUtils;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-02-28 10:02
 */
@Data
public class LocationMatchRecordQueryCondition extends AllocateLocationMatchRecord{

    /**
     * 多个sku用英文逗号分割的字符串
     */
    private String skuStrs;

    /**
     * 用英文逗号分割的多个分配库位
     */
    private String locationStrs;

    /**
     * 用英文逗号分割的多个关联单据
     */
    private String relateReceiptNoStrs;

    /**
     * 查找的Id集合
     */
    private List<Integer> recordsIds;

    /**
     * 用于标识是否进行下载操作
     */
    private Boolean isDownload = false;

    private String handleResultCodeList;

    /**
     * 起始分配时间
     */
    private String fromAllocationTime;

    /**
     * 截止分配时间
     */
    private String toAllocationTime;

    public List<String> getSkuList(){
        return getSplitStrList(this.getSkuStrs());
    }

    public List<String> getLocationList(){
        return getSplitStrList(this.getLocationStrs());
    }

    public List<String> getRelateReceiptNoList(){
        return getSplitStrList(this.getRelateReceiptNoStrs());
    }

    private List<String> getSplitStrList(String text){
        if (StringUtils.isBlank(text)){
            return new ArrayList<>();
        }
        return CommonUtils.splitList(text,",");
    }
}
