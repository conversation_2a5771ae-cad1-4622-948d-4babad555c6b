package com.estone.apv.action;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.apv.bean.*;
import com.estone.apv.common.ApvOrderType;
import com.estone.apv.common.ApvStatus;
import com.estone.apv.domain.WhApvGoodsDo;
import com.estone.apv.enums.ApvTaxTypeEnum;
import com.estone.apv.service.ApvOversizeService;
import com.estone.apv.service.PackExceptionUuidItemService;
import com.estone.apv.service.WhApvOutStockChainService;
import com.estone.apv.service.WhApvService;
import com.estone.apv.util.ApvPackUtils;
import com.estone.apv.util.ApvTransferUtils;
import com.estone.common.util.*;
import com.estone.common.util.model.ApiResult;
import com.estone.sku.bean.WhSku;
import com.estone.sku.bean.WhSkuWithPmsInfo;
import com.estone.system.param.bean.SystemParam;

/**
 * 拆分订单包装
 * <AUTHOR>
 * @date 2025/5/19
 */
@RestController
@RequestMapping(value = "apv/split/pack")
public class WhApvSplitPackController {

    @Resource
    private WhApvService whApvService;

    @Resource
    private ApvOversizeService apvOversizeService;

    @Resource
    private PackExceptionUuidItemService packExceptionUuidItemService;

    @Resource
    private WhApvOutStockChainService whApvOutStockChainService;

    @GetMapping("scanOrder")
    public ApiResult<?> scanOrder(@RequestParam String input) {
        if (StringUtils.isBlank(input)) {
            return ApiResult.newError("参数缺失");
        }
        WhApv whApv = null;
        WhApvQueryCondition query = new WhApvQueryCondition();
        if (input.startsWith("YST")) {
            query.setApvNo(input);
        } else {
            query.setRightLikeTrackingNumberOrServiceProviderNo(input);
        }
        whApv = whApvService.queryWhApv(query);
        if (whApv == null) {
            query.setApvNo(null);
            query.setRightLikeTrackingNumberOrServiceProviderNo(null);
            query.setPaymentStatus(input);
            whApv = whApvService.queryWhApv(query);
        }
        if (whApv == null) {
            return ApiResult.newError("未找到原订单：" + input);
        }
        WhApvQueryCondition newQuery = new WhApvQueryCondition();
        newQuery.setSplitApvNo(whApv.getApvNo());
        List<WhApv> newApvs = whApvService.queryWhApvAndItemList(newQuery, null);
        List<Integer> statusList = List.of(ApvStatus.SINGLETON_TOUCHING.intCode(), ApvStatus.MULTI_TOUCHING.intCode(), ApvStatus.CHECK_PRINT.intCode());
        boolean noneMatch = newApvs.stream().noneMatch(c -> statusList.contains(c.getStatus()));
        if (CollectionUtils.isEmpty(newApvs) || noneMatch) {
            // 无拆分包裹 去除拆分标签
            ApvOversizeQueryCondition queryCondition = new ApvOversizeQueryCondition();
            queryCondition.setApvNo(whApv.getApvNo());
            ApvOversize apvOversize = apvOversizeService.queryApvOversize(queryCondition);
            if (apvOversize != null) {
                ApvOversize update = new ApvOversize();
                update.setId(apvOversize.getId());
                apvOversize.remLabel("SPLIT");
                update.setLabel(apvOversize.getLabel());
                apvOversizeService.updateApvOversize(update);
            }
            return ApiResult.newError("无拆分包裹信息，请返架处理");
        }

        // 组合SKU
        newApvs.forEach(ApvPackUtils::buildZhSku);
        SystemParam systemParam = CacheUtils.SystemParamGet("apv_logistics.LOGISTICS_COMPANY");
        List<String> logisticsList = CommonUtils.splitList(systemParam.getParamValue(), ",");
        for (WhApv newApv : newApvs) {
            newApv.setWaybillSize(logisticsList.contains(newApv.getLogisticsCompany())?"100*150":"100*100");
            newApv.setOriginApvNo(whApv.getApvNo());
        }
        newApvs.sort(Comparator.comparing(WhApv::getStatus).reversed());
        return ApiResult.newSuccess(newApvs);
    }

    @PostMapping("scanSku")
    public ApiResult<?> scanSku(@RequestBody SplitPackDto dto) {
        String splitApvNo = dto.getSplitApvNo();
        String apvNo = dto.getApvNo();
        String spu = dto.getSpu();
        String uuid = dto.getUuid();
        String beforeSku = dto.getBeforeSku();
        if (StringUtils.isBlank(splitApvNo) || StringUtils.isBlank(uuid)) {
            return ApiResult.newError("参数缺失");
        }
        String sku = CompatibleSkuUtils.getSku(uuid);

        String existStr = StringRedisUtils.get(RedisConstant.MM_APV_PACK_KEY + apvNo);
        if (StringUtils.isNotBlank(apvNo) && StringUtils.isNotBlank(existStr)) {
            WhApvGoodsDo apvDo = JSONObject.parseObject(existStr, new TypeReference<WhApvGoodsDo>() {
            });
            if (apvDo == null || apvDo.getWhApv() == null || apvDo.getWhApv().getWhApvItems() == null
                    || apvDo.getWhApv().getWhApvItems().size() == 0) {
                return ApiResult.newError("单据缓存信息缺失：" + apvNo);
            }

            if (StringUtils.contains(ApvTaxTypeEnum.getNameByCode(apvDo.getWhApv().getBuyerCheckout()), ApvTaxTypeEnum.GPSR.getName())) {
                String message = packExceptionUuidItemService.checkGpsrTag(apvDo.getWhApv().getApvNo(), sku);
                if (StringUtils.isNotBlank(message)) {
                    return ApiResult.newError("sku："+ sku + message);
                }
            }

            WhApvItem whApvItem = apvDo.getWhApv().getWhApvItems().stream()
                    .filter(item -> item.getSku().equals(sku)).findFirst().orElse(null);

            if (ApvOrderType.getJitIntCode().contains(apvDo.getWhApv().getShipStatus())) {

                if (StringUtils.isBlank(spu))
                    spu = null;
                String finalSuitSku = spu;
                // 合并相同sku数据
                List<WhApvItem> apvItems = BeanConvertUtils.convertList(apvDo.getWhApv().getWhApvItems(), WhApvItem.class);

                if (apvDo.getWhApv().getWhApvItems().stream().anyMatch(i -> StringUtils.isNotBlank(i.getSpu()))) {
                    apvItems = apvDo.getWhApv().getWhApvItems();
                }
                if (StringUtils.isNotBlank(beforeSku) && !StringUtils.equalsIgnoreCase(sku, beforeSku)) {
                    whApvItem = apvItems.stream().filter(item -> item.getSku().equals(sku)).findFirst().orElse(null);
                    if (apvItems.stream().filter(item -> item.getSku().equals(sku)).count() > 1)
                        whApvItem = apvItems.stream()
                                .filter(item -> item.getSku().equals(sku)
                                        && StringUtils.equalsIgnoreCase(finalSuitSku, item.getSpu()))
                                .findFirst().orElse(null);

                } else if (StringUtils.isNotBlank(beforeSku) && StringUtils.equalsIgnoreCase(sku, beforeSku)) {
                    whApvItem = apvDo.getWhApvItem();
                }
                else if (apvItems.stream()
                        .filter(item -> item.getSku().equals(sku) && StringUtils.isBlank(item.getSpu())).count() > 1) {
                    whApvItem = apvDo.getWhApvItem();
                }
                else {
                    whApvItem = apvItems.stream()
                            .filter(item -> item.getSku().equals(sku)
                                    && StringUtils.equalsIgnoreCase(finalSuitSku, item.getSpu()))
                            .findFirst().orElse(null);
                }
            }
            apvDo.setWhApvItem(whApvItem);
            if (StringUtils.isBlank(spu) && (whApvItem == null || StringUtils.isBlank(whApvItem.getSpu()))) {
                apvDo.setLocalSuitSkuMap(null);
            }
            apvDo.setQuery(null);
            // 缓存当前包装数量
            WhApvItem skuItem = apvDo.getSkuMap().get(sku);
            skuItem.setPackQty(Optional.ofNullable(skuItem.getPackQty()).orElse(0) + 1);
            StringRedisUtils.set(RedisConstant.MM_APV_PACK_KEY + apvNo, JSONObject.toJSONString(apvDo),  3600L);
            return ApiResult.newSuccess(apvDo);
        }

        // 根据sku随机获取新单据
        WhApvQueryCondition newQuery = new WhApvQueryCondition();
        newQuery.setSplitApvNo(splitApvNo);
        newQuery.setStatusList(List.of(ApvStatus.SINGLETON_TOUCHING.intCode(), ApvStatus.MULTI_TOUCHING.intCode(), ApvStatus.CHECK_PRINT.intCode()));
        List<WhApv> newApvs = whApvService.queryWhApvAndItemList(newQuery, null);
        if (CollectionUtils.isEmpty(newApvs)) {
            return ApiResult.newError("单据不是待发货状态，请返架处理：" + splitApvNo);
        }

        WhApvGoodsDo whApvDo = new WhApvGoodsDo();
        WhApv whApv = null;
        WhApvItem whApvItem = null;
        for (WhApv apv : newApvs) {
            if (CollectionUtils.isNotEmpty(apv.getWhApvItems())) {
                List<WhApvItem> collect = apv.getWhApvItems().stream().filter(c -> StringUtils.equalsIgnoreCase(sku, c.getSku())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    whApv = apv;
                    whApvItem = collect.get(0);
                }
            }
        }
        if (whApv == null) {
            return ApiResult.newError("未找到可包装该sku的单据");
        }

        // 获取包材信息
        ApvPackUtils.getPmsInfos(whApvDo, whApv);
        if (CollectionUtils.isNotEmpty(whApvDo.getWhSkuWithPmsInfos())) {
            Map<String, WhSkuWithPmsInfo> map = whApvDo.getWhSkuWithPmsInfos().stream().collect(Collectors.toMap(WhSkuWithPmsInfo::getSku, c -> c, (c1, c2) -> c1));
            for (WhApvItem apvItem : whApv.getWhApvItems()) {
                WhSku whSku = apvItem.getWhSku();
                if (whSku != null) {
                    whSku.setWhSkuWithPmsInfo(map.get(apvItem.getSku()));
                }
            }
        }
        if (whApvDo.getSkuTagMap() != null && !whApvDo.getSkuTagMap().isEmpty()) {
            for (WhApvItem apvItem : whApv.getWhApvItems()) {
                WhSku whSku = apvItem.getWhSku();
                if (whSku != null) {
                    whSku.setSpecialTag(whApvDo.getSkuTagMap().get(apvItem.getSku()));
                }
            }
        }

        {
            List<WhApvItem> skuItemList = whApv.getWhApvItems();
            if (ApvOrderType.getJitIntCode().contains(whApv.getShipStatus())){
                ApvPackUtils.buildZhSku(whApv);
                List<WhApvItem> jitItemList = whApv.getWhApvItems().stream()
                        .filter(f -> StringUtils.equalsIgnoreCase(sku, f.getSku()))
                        .collect(Collectors.toList());
                if (jitItemList.stream().noneMatch(i -> StringUtils.isNotBlank(i.getSpu()))) {
                    whApvItem = skuItemList.stream().filter(f -> StringUtils.equalsIgnoreCase(sku, f.getSku()))
                            .collect(Collectors.toList()).get(0);
                    if (StringUtils.isNotBlank(whApvItem.getBuyerCheckout())){
                        String skuBarcode = JSON.parseObject(whApvItem.getBuyerCheckout(), new TypeReference<List<ApvTransferUtils.SkuInfo>>() {
                        }).stream().map(ApvTransferUtils.SkuInfo::getSkuBarcode).collect(Collectors.joining(","));
                        whApvItem.setSkuBarcode(skuBarcode);
                    }
                }
                else {
                    if (jitItemList.stream().anyMatch(i -> StringUtils.isNotBlank(i.getSpu()))) {
                        jitItemList.removeIf(i -> StringUtils.isBlank(i.getSpu()));
                    }
                    whApvItem = jitItemList.get(0);
                    Map<String, List<WhApvItem>> suitMap = whApv.getWhApvItems().stream()
                            .collect(Collectors.groupingBy(i -> Optional.ofNullable(i.getSpu()).orElse("普通")));
                    whApvDo.setLocalSuitSkuMap(suitMap);
                }
            }

            // 根据list的whApvItems里的SKU分组合并saleQty
            Map<String, WhApvItem> skuMap = skuItemList.stream()
                    .collect(Collectors.toMap(WhApvItem::getSku, a -> a, (o1, o2) -> {
                        o1.setSaleQuantity(Optional.ofNullable(o1.getSaleQuantity()).orElse(0)
                                + Optional.ofNullable(o2.getSaleQuantity()).orElse(0));
                        return o1;
                    }));
            // 缓存当前包装数量
            WhApvItem skuItem = skuMap.get(sku);
            skuItem.setPackQty(Optional.ofNullable(skuItem.getPackQty()).orElse(0) + 1);
            whApvDo.setSkuMap(skuMap);
        }
        whApvDo.setWhApv(whApv);
        whApvDo.setWhApvItem(whApvItem);
        whApvDo.setQuery(null);
        // 获取欧代标签SKU
        ApvPackUtils.getSpecialSkuList(whApvDo, whApv);
        StringRedisUtils.set(RedisConstant.MM_APV_PACK_KEY + apvNo, JSONObject.toJSONString(whApvDo),  3600L);
        return ApiResult.newSuccess(whApvDo);
    }

    @PostMapping("pass")
    public ApiResult<?> pass(@RequestBody SplitPackDto dto) {

        return ApiResult.newSuccess();
    }

}
