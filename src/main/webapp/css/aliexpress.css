.ipt-custom-weight {
    line-height: normal;
    width: 100px;
    margin: 5px 5px;
}
.width-100p {
    width: 100%;
}
.width-80 {
    width: 80px;
}
.width-100 {
    width: 100px;
}
.pd0 {
    padding: 0;
}
.pd5 {
    padding: 5px;
}
.pdr10 {
    padding-right: 10px;
}
.pdl0 {
    padding-left: 0;
}
.padding-horizon-2 {
    padding-left: 2px;
    padding-right: 2px;
}

.padding-horizon-5 {
    padding-left: 5px;
    padding-right: 5px;
}

.padding-horizon-10 {
    padding-left: 10px;
    padding-right: 10px;
}

.img-top-bar {
    width: 100%;
    height: 30px;
}

.pct40 {
    width: 40%;
}
.nm {
    margin: 0!important;
}
.ml0 {
    margin-left: 0!important;
}
.ml10 {
    margin-left: 10px!important;
}
.mr10 {
    margin-right: 10px!important;
}
.mb10 {
    margin-bottom: 10px;
}

.mt30 {
    margin-top: 30px;
}

.category-list {
     margin-top: 0;
}

.category-view, .caterow, .category-name-group{
    height: 100%;
}

ul.list-group-tabs {
    height: 90%!important;
}

.sku-images {
    width: 100px;
    height: 100px;
}

.cursor-pointer {
    cursor: pointer;
}

.ico-transfer {
    position: absolute;
    top: 10px;
    left: 10px;
}

.ico-remove {
    position: absolute;
    top: 10px;
    right: 10px;
}

.img-unchecked {
    background-color: #eee;
}

.img-checked {
    background-color: #7bb97b;
}

.img-container {
    padding-left: 2px;
    padding-right: 2px;
    padding-top: 2px;
    padding-bottom: 2px;
    border: 1px solid #cac5c5;
}

.a-img {
    padding: 0;
}

.sku-img-div {
    padding: 5px;
}

.upload-img-div {
    cursor: pointer;
    border: 1px solid #e0e0e0;
    border-radius: 0;
    background-color: #f9f9f9;
    height: 180px;
    line-height: 180px;
    text-align: center;
    color: #ccc;
    font-size: 16px;
    font-weight: bold;
}

.upload-img-div:hover {
    background-color: #fff;
    color: #ddd;
}

.productTr {
    position: relative;
    background-color: #fff;
    border: 1px solid #cfd9db;
    -moz-box-shadow: 0 0 5px #ccc;
    -webkit-box-shadow: 0 0 5px #ccc;
    -ms-box-shadow: 0 0 5px #ccc;
    box-shadow: 0 0 5px #ccc;
}

.noshadow {
    -webkit-box-shadow: none!important;
    box-shadow: none!important;
}

.table-sku-attr {
    width: 100%;
    background-color: transparent;
    border-collapse: collapse;
    border-spacing: 0;
}

.table-sku-attr > thead > tr > th {
    text-align: center;
    font-size: 14px;
    padding: 5px;
    font-weight: normal;
    background-color: #eee;
    color: #5e5e5e;
    border: 0 !important;
}
.table-sku-attr > tbody > tr {
    border:1px solid #eee;
}
.table-sku-attr > tbody > tr > td {
    text-align: center;
    font-size: 14px;
    padding: 5px;
    font-weight: normal;
}
.img-preview {
    width: 50px;
    height: 50px;
    margin-left: 20px;
}
.has-error .form-control {
    background-color: #FFF;
}
.has-error .input-group-addon {
    color: #000;
    border-color: #e5e5e5;
    background: #e5e5e5;
}