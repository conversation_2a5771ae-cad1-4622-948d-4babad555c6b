@charset "UTF-8"; 

.margin_configurator {
	border: dashed 1px #222;
	position: relative;
	height: 80px;
	padding: 40px 70px
}

.margin_configurator .input_left {
	position: absolute;
	top: 40%;
	left: 5px
}

.margin_configurator .input_top {
	position: absolute;
	top: 5px;
	left: 50%;
	margin-left: -23px
}

.margin_configurator .input_right {
	position: absolute;
	top: 40%;
	right: 5px
}

.margin_configurator .input_bottom {
	position: absolute;
	bottom: 5px;
	left: 50%;
	margin-left: -23px
}

.margin_configurator .inner_box {
	width: 100%;
	height: 100%;
	border: solid 1px #000
}

.margin_configurator input[type="text"] {
	width: 24px;
	text-align: center
}

.margin_configurator input[type="checkbox"] {
	vertical-align: middle
}

html {
	margin: 0;
	padding: 0;
	border: 0;
	min-width: 1300px
}

body, h1, h2, h3, h4, h5, h6, div, dl, dt, dd, ul, ol, li, pre, code,
	form, fieldset, legend, input, button, textarea, p, blockquote, th, td,
	hr, article, aside, details, figcaption, figure, footer, header, hgroup,
	menu, nav, section {
	margin: 0;
	padding: 0
}

h1, h2, h3, h4, h5, h6 {
	font-size: 100%;
	font-weight: 500;
	-webkit-margin-before: 0;
	-webkit-margin-after: 0;
	-webkit-margin-start: 0;
	-webkit-margin-end: 0;
	line-height: 1.3
}

fieldset, img {
	border: none 0
}

article, aside, details, figcaption, figure, footer, header, hgroup,
	menu, nav, section {
	display: block
}

audio, canvas, video {
	display: inline-block
}

button, input, select, textarea {
	font-size: 100%;
	border-radius: 0;
	vertical-align: middle
}

button, input[type="button"], input[type="reset"], input[type="submit"]
	{
	cursor: pointer;
	-webkit-appearance: button;
	outline: none
}

img, object, video, embed {
	max-width: 100%;
	vertical-align: middle
}

button::-moz-focus-inner, input[type="button"]::-moz-focus-inner, input[type="reset"]::-moz-focus-inner,
	input[type="submit"]::-moz-focus-inner {
	border: none;
	padding: 0
}

.bp-reset-element, body, h1, h2, h3, h4, h5, h6, article, aside, details,
	figcaption, figure, footer, header, hgroup, menu, nav, section, summary,
	blockquote, q, th, td, caption, table, div, span, object, iframe, p,
	pre, a, abbr, acronym, address, code, del, dfn, em, img, dl, dt, dd, ol,
	ul, li, fieldset, form, label, legend, caption, tbody, tfoot, thead, tr
	{
	margin: 0;
	padding: 0;
	border: 0;
	font-weight: inherit;
	font-style: inherit;
	font-size: 100%;
	font-family: "\5FAE\8F6F\96C5\9ED1", "微软雅黑", "Microsoft YaHei",
		Helvetica, Tahoma, sans-serif;
	vertical-align: baseline
}

article, aside, details, figcaption, figure, footer, header, hgroup,
	menu, nav, section, summary {
	display: block
}

blockquote, q {
	quotes: "" ""
}

blockquote:before, blockquote:after, q:before, q:after {
	content: ""
}

th, td, caption {
	float: none !important;
	text-align: left;
	font-weight: normal;
	vertical-align: middle
}

table {
	border-collapse: separate;
	border-spacing: 0;
	vertical-align: middle
}

a img {
	border: none
}

input[type="radio"], input[type="checkbox"] {
	margin: 0px 5px 0px 0
}

img {
	max-width: 100%;
	vertical-align: middle
}

ul, ol {
	list-style: none
}

@font-face {
	font-family: "icomoon";
	src: url(http://s0.kuaizhan.com/css/fonts/icomoon.eot);
	src: url(http://s0.kuaizhan.com/css/fonts/icomoon.eot?#iefix)
		format("embedded-opentype"),
		url("data:application/x-font-woff;charset=utf-8;base64,d09GRgABAAAAACdQAA0AAAAARDQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABGRlRNAAAnNAAAABoA AAAcZZ4z8kdERUYAACcUAAAAHwAAACAAoAAGT1MvMgAAAaAAAABHAAAAVi+71DhjbWFwAAACsAAA AGwAAAF6mXiGTmdhc3AAACcMAAAACAAAAAj//wADZ2x5ZgAABAQAACCXAAA4AEzZANxoZWFkAAAB MAAAADAAAAA2AdUX42hoZWEAAAFgAAAAIAAAACQEfwPTaG10eAAAAegAAADFAAABzOqLDN1sb2Nh AAADHAAAAOgAAADoGOsnBm1heHAAAAGAAAAAHgAAACAA7gDnbmFtZQAAJJwAAADfAAABhk/c8+5w b3N0AAAlfAAAAY0AAASIzYzsbHjaY2BkYGAA4vRzdXnx/DZfGbiZGEDggkr2JBj9f/7/+0znGB8D uRwMYGkAT8cM7HjaY2BkYGB8+P8+gx7zpP/z/79jOscAFEEBxQDFzAhUeNpjYGRgYChmeMJgyQAC TEDMyAAScwDzGQApvQHWAAB42mNgZBJhnMDAysDB6MOYxsDA4A6lvzJIMrQwMDAxsDIzwIEAgskQ kOaawuDwgOEFJ+OD/w8Y9BjvMCg2MDAwguQAnkAL7gB42mNkYJBkAIFVDAxMDHAsgsRGx/FArADj M09ieMWkBubLQcVAbB4s+uB6mBQYmLGZzfiFQQOJbwamHUDs//OR9HMBsQGauaJArMEkD3aDIJJc FhSDzHgEpAWAWA9JXp3pDUMulJ0ApcWY9jFUA2lNIBZi2sOQAhUPgdnPrMAwHyiuzxQDtk8BRziB zHOA4gIk8/FixncMO+D+esIgCaQdmSLgbuBmkgD61RJoNwMDJ9M6hmVA9WeYVBhkGW8zaAAAeKwd RAAAAHjaY2BgYGaAYBkGRgYQKAHyGMF8FoYIIC3EIAAUYXrA+kD2gd+DuAd5L5hesL/g/P+fgeEB wwP2B/IPAh4kvGB8wQYSU2BWYFJgVGCQ/y+RK5ElkQk1Ew0wsjHAJRiZgAQTugKGYQ8AldsY1gAA ABIAEgASADYAbAGiAgoCNAJyAq4CzAMiA0oDyAQABFoEigSkBNQE5gUiBWIFlgXeBhgGWgaEBp4G zAcSB1wHpAfMB+gIQAh+CKQIvAkUCYQJzAn0Cg4KLgpmCuoLAgsyC2oLrAvSC/gMQAxSDGQMjAyk DOgN4A5aDqYOxg8EDzoPgg/MEAAQHhDEEOYRMhGMEcoR9BJQErYS/BOEFBoUVBTEFOYVIBVQFYwV uhXIFdwV6hX8Fg4WRBZ6FqAWxBbWFugXABcuF3IXmBfuGF4YfhjGGP4ZUhmUGdYaABpEGl4aohuK HAB42uV7eZgcxZVnvMzKjDqzsiorK7uru6vr6KrsbqkP1ZVqWt1dQsJIFjpAWEISSGWQwAgEDawB CYyKwwiQhHtGIB9gu+TZMfaaQ2htCwwzLmObGY7BMtLAx7A2xeLxxc5Y5jBez3zZ+yKqqlVqCfz5 22//2uyKIyNeZkRGvHjv915EE4G0E0J+BUUiEkpINpANmNlAsv2l7zz4IBTtajsoRCBkuiYQqBGF BAkBatCkQfWsZeQtw5JMywB1eHhy8oK7Qt++oDw8bNdikzGoDQ+XL/h26K4LJieHh2NYQiRsCV8G FeLH9+gkQrrwbfGErAdC4Ww8U8gHcmmJ5q24HwzLiE+ADkfsUqizMwQVjO23drXBqm9ks9mt9rOv 7IJKZ8iu8tpiqPPo1v8QbumwrF17/+o/sJkREpr+PfwOXuTf5cEWNWKQDtJNksQkc8gQyRKLjJIJ soicTZaRlWQ1WUs2kE3kEnIZuYJcTa4jN5CbyE5yO9lFdpPPkX3k8+QB8lXyN+Qh8i3yGPnv5DB5 inyf/JD8A3mB/IQcJa+S18kb5C1CtCQOjp7MG1k9mWrJ05a8hmEU9DlwmlT/iDpMkxZ7b7aemo20 Wf5hqXEa2tnlp6NveQ9vv0n3jeuvjx4aXbAAbmtkjjTS+6PRaFdXdFZ8urKurp9Eo/Ynu9jdaDR6 Q1eDdF3zga6WzKFmfvTkktEWmvtanlrHqxZ0dcGj1993CDu2rp4cqSdQiSJd/VdP/p29rItdmMz8 FmApCxiNNghYKfaC/XhLREV+i3G+/nO8RnB54cyfGsRGYHmpZZStlllmoVgsxWLFWAzjMo8rqlpS 1SOqaq/i91hX4b9SsVgpFqFiV9ky/qgQO+nCb4GZ9R5BXk51QSAkJ9K5QjZj5Kl+Il+B0pkXnnnm YLtRa6RQLsWK9vH40NCZQ86/HWtkvoHvdMyMEcXxSZE+QoKhcKaQSyfkRirlc+k5YkLugro0GANs Lp6PV/EdQ2f+jsdwruZbVR32aZoPij6tXJ3G3sI1rKrxO84rKywaXlWrlLBd14fOT4KQdogHIJnP 5hsBb+Nay72kWwKxcTTZBTHWXuPGrpVxeMtQtiv1ez6a9bpqiYu6ZrselJwE0gkqh4xwxirkpLwB b0/+YVLXWWRXKzClquc/cT6LnmYPe9kcAP6dlqcYPxGthT+kWbzSzDMpgQs4n2QhEA+I+HXY2xL/ 2bVmrtRaBKUii2sxuwQViPHvKfO4/p2MgnGKjR9ox6CEySbsawX7qpI2/GYU6KGo0A3jjnyOM0sm HIIz79iI3BBQqW71J29cNX/+qvlQ23jmwOVrP66HnJ2rz71i4MyvzWfl+L2xxrezrx3ClXM2WU4u whFMcu4bO5GerDvY1/UwxmFcxdioJU+5VOVhVMDxyGrxRDqvN1ktJEN1WItoFQx19iqzmE/vc4rL pbh+w+NipYxXpYgL7Xz7C0oQCT/l04JQURm5Wo/xOc6AUPJp+IZY0UWL1HVydHmMvQMH+T2/r/6U z99YJ0xHVnHe2XwncZ7jelyK1z8IpXA8n61/lojcWaivxkwZCHJmyS6yN5WQRTHdW63u8oV8vhBU izG+JMplniC77WDlvjqPTr8hALxJeskCQlK5QTGfMwfBLOTZwOkhQwFc84oDR4qx7jjkqUwHISHT tJk2C1YhY4QNWYCOVDoiyrkU4ABCfwG0zkSnds0a7RxtzTWX9OWEVCYl5PoGAoGBRd9ehMmqXqur o79bcfdPeETRGwx6MT5nnhgxw5GOTKJzrpxIyHM7OzyOtkSizeHx+v2MyO9nY/RbcqaQgVuJkwRQ RiUZZ3QJ2YyetxopnXUPW8tr+/6xJcCtfYN9cEEfj+9vyeOYiCRG+uBVuLax9ojGVg6OtpEMZGNw +F8fe+wfH3sMrrUPw1L7f9jTOPQoFaLT78Dz8BWcM0RIZlrBBUDlqDABRnhcGAKrMCjAznt2e9vi Ht899/jcCYMlJ+7g/hOlRsLtu2e3zxM3fLv5HZPJROWyROLvpwbAweOTq1DmQKWe4lzGpt+AKq4b A2UEARRgKFGBcU5WV0Bn6wFLgoxlrIJF5cwQ4KQCKSLP+LTiuVfveHjH1RMb79g4NDDhgQR4JgaG YGc8xpgqFpdvOG/Njh1rzrvBfn/jxjuGBoQva9qXhYEh7JuXlAQZe9CLHBSmCqTzuXFIIxslE8g+ CkSxcRn5BVstWKMiNirILr9n6SJZEqky14x0xt1UlORFSz1+lxxdvA/w2rc4Wp0IeKkeppo36FSd gsfjDHo1GtapNzAR754AmOhm+mp6msuLH5A4tq8IycSgg3XAKqRRQPgBhVF2XMAvh5JXDyj+nrb+ sB5IesMbOpThQODcjQeGPbH81oo3GdDD/W09fiWge8Nbh9vahg9sPJcaOaVjA1uW8oxMbkrjJBk4 FcEyKSQ1ZE1TxxlJPgusDwftMsesLO6xUYfaNXbFOns7O3tziGQrnVBDZDuDe+0ilGsob8HBKDpL OBc4I6ftTzdfCx/RHxbyTCd8SC/YdWrrFY4Magwb0BlsIODqU0gI11+UpLFVKU4NPYsr7pRYpHFT aAgqJqcEMqkW1VpLKCFrq0dQcmgRqGFr1epJYZqgztnGarW6eGzqB/bVBAJxjkjwM5sfHgCUmVC1 D/KvGLarkXQ6whQ1xGzUVHYJxxHK6YhdjqSRrBLjupo0MIKbkLgWFzUxkEUUDJWSXULVOFU8woUs f0OxyMgZ3+Ezb2I/2KrnzCaHuiGcmYBxYDJ6Rv0dmQoNKzVFedhvYhLqLPK5hFoI70z/wwomw6Ep NvmsgskgkThQhFT5u/s/+u2SIaeHIJ1j5Vkqw+8msbGnFWWP0otJszF72cNKzLNHwaLh0PseqIYw 16s0Ciabbe96mEqexpPve0hdJzXmnM10P8lhb5gKSuZnBryuk5LNJIGdYp0TUWDiba4wCpmwDjUU MnaJa8YKsnCRhe/l0ulcGgEFkGqqo9iRAmRwpq+YLivXs8VYmlHZz/K56kilOnB0mn1ieHIeIZaO yjGfTMT1bMbKp5NWQ33JTHHlUVvl2VDpIVRbOiqxnFkoOZ2q6oQ21YnX5P5j+/3+ZJffK93+vdsl r78r6fd//+eAy+HnENu6f//Wgf6OaNzyPU89YeOi22+/yAh76PM+Kx7t6Oc6o9kfV8NKZo35wbQK QC0wDWpacDA+pKIhrNu7Y1PQOxmtdSITD8U7dCxU7ampGBZehYV1DM758SCzLlImAhYTcPFm8zwI iEFLuCoq+HewNLUK82qlVKrU5+qNBnaPkkEyjk9nChOAojDOlIGebUiGHBaMQZzGjZCMOgLBVGOW pFn38A1fmzgk+nz2E6mOUomtJ46MahDvsP8Ua1NVtY1H0JKvBn1f8wU7UmgMhDpx2cYqpT82qloj Nm6907+GH8BbOI9ca/U0LAIrwHp4QpJpaIQkGWQLsKWeCYPHp/l82n4q2a9JsiyBKdGBhGG/ZiRQ l5pGwqcpiqacC+sdTqfD/jrGsNG+kYEJuKstjpqC+yNiMA3lFoxNwNJoHK2wuo1eTyG2/dLt9o5y GcFwmcewss/+aR8iZWYQYHyS/EB7IxAX42DErayBwgpF6EHVLsYOqtzkwudj2FYBTY9fwJ4/YzNy 7NFqn7QGbVY6OxTgUbtitlxv/wivFa0lsMfeC9fZjyYCCRSfGKsJ+8iuxC77SEtJAyfWdew8sp7p GD05g0g/OtfQQk3hwJiwkTt9Bn5g7xyibo88dGoMt/qCQfvlYoA6nTSAsb8lKiLOdckx6o6dEhc1 BSq+IM6Bm8aok8X+lmhGlhe5ThnGNRMydDmZMDGkG3JDxsVgIKCgTKpgFQKbPBckgxgVhFjM6cz2 hPQF559/4/kdkl371P1iuv/jeUtxhkLpdSOdPTA3u78rGXBCSlq2ddmyfF/cC9+9/hOBoXkfi3eL 6Q5B8c1tpzR6/vCaHYhpvjr9G/gj6hadkAkwxxFDKTAENFxADD4IMjWFv71Eu/uLq9N3L/dquW3z NmS2vfTd9su+dEn78vNSd39pteYYV73RwkvfzV51WYRjWlQ3sG4G06KGbP7FUK4WZ8I66+yxJWc0 ZNq/I09fhSuFcSj2I0kN5DaDZi1TzGpoFpbLxSqLysdXDa96FBcHEIyqzG5CGeaefhNx9KNo75NU gpqFnGmFs3WjHweRMniGqztjcSnNyuFz3fozAM/o3dH+/gX9jUyUFeJt/zXhT8oe+ZNhlo9KtBSe MzonXKISJ8b+DhMfvA4PIQ4cICsZTmZDZoULTDGigkQFqgBK4jQvQE0qO+rZMFOtZr0ei3mZaRlh XoAPNujhxS2+LpcnKqryUklwzHH45aUyiF0ep8O3xdPtLLol+zcugV4nI1iV/4vkYwSSIM6ReOro 8jol76WeqGshplu8UacHejf6BLenUxakxUg86BAciyW/o8Pjino2uUX3Wa7oAbdfvkIW3H66Vc5L vF4aEkFaLClSK517o1d0c32N80ZQRaD9IiBGdDFPAZWYEZm0/usTFzL8ZRO48IkL4drNc1K7L2nx zTDsE2Q2PLd4TDQzAnFHA8Km4oFsII4sEisutaoMTHG4AD24qoABSOb+eP+KBRdWgCzggKOMRTGO HzCGI6jLmK7mNi1peH2EBoJptdflRh2ce2jnzkM7/3rPxRfvufir7cFg8wcHd7Iae/piVvVi0HsZ swwv8z7Bq9n369Pvwb/Bi9hegNkkhFkgzCDJoBmSTFi5gphAlYjcR3FhZTM4+YwJFYBNP7xL3IPh aHeblgDD7FaCkuRodzk7x9Q2X1iivjZXZD78Eyfa88P5Ub8RVdrsI/kVAY+sGF3zzg1pvkgHk5e9 xAWvwGHsg3bieylyHyIB5C3LgAsO3XLLoVu+tXJgxYqBlTyGw7ewsnXNe4z5+EVJD/wv2IKz6cYv QomAyNpK5pNWPq4ziG2YVMLsu2lrpDfxv+3MPyd6RyzotjN7lyy5fGUstmzrVlhy9ivd3Tg2genf w3H4CZ+LOfjyxgwIjR7iWKCUwZBGqYOh0I12FK4JP9qxcMmP9+z58Z6/+fyVV37+ys2aLOfyVA5S ms9TGpRpPifLWJjPy/49jBAiVzLKvUFehoQyUlB+h/QUn5ZbfI1ozUiQDOAfYGmlWomxdPokfyTS pOokAqMAUuP+P06zlSyD++DTDImnkvm614fLl9G65TsGXPLAHVf4Ir4r3JLkbmb+dNIdz/D3PQv3 kT8xWZmKczkVrz9v/4FRgZs9iaSM+oTfhvk346SP2YWF5sJJDgqIqFBfzHJXNZkfZzL7hbE1Y2Nr wDMpOUP+sj/klCY5Tq5ifOj61auvX31esfg6oxlbood1/DEIxkkQRdvPrGY0u2NEnX5z+h0hCIew HwpHgAUyxr1nW8iV5Gayh9xHHiDfIk8x/2vdfjDQJLSMCZhR2XU1begnAcEo/EXkYfL/9O0Fv+73 6194ZkA/e/ExAa9jIIohn/OYy+t1HXP67lbCihLm9YuOsvqjAqt3HWX1R10+u+f/8gVwe0hRQv5z RiP94cUpER7Ax1nkeMDp9TpZlPxzBHZthkJf3PPRr/gQAuYDUBGXHcG14UH8OI+chXP9CS5vZuRp T0veaF0OJ5YFGgLMRPAzFzWqwW7UejSOIKPBoOGmFft33Aa366a42hlSGbRn2VCnXXtBByqE2gvt IYEiABxwQNQxcB93NF7HYyhqPu5VbY3+Ds1TlivW487QwX2mQxR73e5eUXSY/vF0enwZd2PWf3yZ DU6/AT/Bb04hTiaptJXGTiNSS2K3caEaUYFJ9bCVYT4ga9BhFhhak41wAXbD/PiYsZg6zwjG5wf0 qKGEYtFhPTQcULsDStjncBlBZWFM0ub44pLed2SsZ54gtIch0ZXavDYRagsLIR10Xe/uE6Sgnjqa ngthNd0n6E07GTFWdQbTkzGwdCuFqldP5amZz1Zj3IeAIVat1h6MfPepVC1W63n6ichXuD3SeJ7J 5rodcIqmNDm0T864c9AazDPYbLC9i9XP7t377N5KZXKyMnmwWOS++1hjywiqe1mt/bNJVo02SyxW rkeVIhtTk+urMuliXrSmD6mO5Gk3mGhAmgyl1z1K+Tg8be/yqKrnwGKPCvBHAVT3kq+5VbXDfhA2 Q9nvtt9xK+2K7AKXrEC74gbF7bePv8fHadv0W4i1l87IKNLTWP6SHufLf5YLy5ht/Lk6zI4OE9rs B8/tvf0m+2ZRkkS4A+MeQ7VvVg1DhTtU491UJJKK3L1taLn5mUWSaP9CkCQBOkXJaf9C1XUVOjHm frQG/vGT9hn769SdaWy4C+II9rEGJVEcZVKqZa/l5L3qc6BCXXbRBQ/ZJeqCqsu+nE8Dmwm2cV3j pLFQZ8xFh4eRtOqiU1PUVd9cwjEqNfZU/NwD1F33ANXnpN4fyvyLTRUSD8Rhkr+TRT8ql5fzNVri +vEkh16bai/hnrblUCQte/uU+E5wG9QRytZj+/cf228f2To0+qQBtf3s1v7DPfOWfd5AWidBKQ8/ gh3Yx24csQnE3JvI1WQn88acLK/NWfez66W/kN7I170Dzb3C2d6B6yVBZTyhCpLjRBZasoYk/pLl MXKcyMKJbNThsA848IJNDkc3stUBzlabVGN7gJGwaL7qQHioItF8FvHcTR9Ze/gYq2URMu/bgba2 AOgBw8D5vomkhAzcfEJ21C3/ANpb9XATHLaPXnnlLv6Dm+1vwlr7CDjtP9YD4+M+xHYvw6vIMYgY EYHOZbYsoIXKdgFaednMGOFu5vzXkxriej3b6iCGQyO+Qe1YsKMjCCuQVe4A8DlGsMdr19svf2e7 ffwdfsFnRnxMG1yvdXRooGqDaZCkEYcPwP4dXLE90x2NRpl7Hr8nSADx+Pc5L0dIAns2yHx2KZqk JooV09JMSzKolaKSaTDOMwuDwExe2UK0bJgSc59RzTTgaR2M++HL/c4V4ITgORmPXRuB2HNapn1R 4OeLgm4poLmkq+SxdUrSfiMU6N/g8qrbv75yTW8/eLogBtGlocMHlh9bvj8+NHf07tEVSM+euQzU tpz74x8f+3J70n5kLI6zsHn6DWEDvMJlsYlf0ZBQTXMIRw9LhqCxE0lZLerQjLBh30/37fup/cKN 37zxxm9ecbUzEHYVVUMtusIB59UuAxax6n0Psdobl2GBShc+6AkEPA8upKrhutrZ6ktkp2TamE/K pA2ZH4hziZ9MZalhMd90rFQrlkpAMJomlUoMLTEsK5WK0wQjRNGVSn0Ozp9+G74Dz6A96EMZP4Dj nycjdSnHpVraNJITDS8zlzFDMAgG1hpaFNi9pRXSYtLkmoZrm7vtD4IR+GRkk3yTfO2n3Qq4wOPa 5/Jgqpxt/8arQhto3p95NUzVl+f6Ry8YHV06dxR2RYJ+fzASHRzs8rnGGaAad/n2BT32B8yOA5cn aH/wErtafOw1tCEXMl5OJxpecMNq+LwLGTYXfghTCw2stEnD9Xs2O6w+bdGm27yQM+FnQs/m5FXJ pCDTXqckaNo2raAJodA8HyxP/uh0hcnGE/CUkExu69ncg2qk1yELSLItFGJJpju5PLl5++mLexpP 4Rp3T/8Sba8XOU+lcXWeYn0xx4iVLjBfD/dV6MzxkM/xg0N5uPj5e+99/t6vHZicPDC5f2l2SOzu 7nC4wu1uKgwKUfOCri4jci+jgdQkI8ot6IdQFIxOwy0JLjoX2sS+WP/jcbTKKfkq8QtXwU1c3gTI eWQNWU82ouRu+IQ0Gh4VEVch47FNu8KEkOUCY+YvY3RBwkzPwY7mGUFuSJS5byxsIPbilVS2WsRK BV6yMyxcGHa7/bLooPQiUaReURCjSVWIeEWJooSUBEGUZepWPL5AOw1QEEDyjohuURRdmBWdPbL7 16/ya29yfhJG54NXU+90AyBKl1dsEMai1BegitMj46MidbC9RLdDAHAI/k5wRZyCQxCxjIpY9mSy N9mXZLpsiOjCfXAZ12Um95yvQKtp26k4YDYYcZyEXaxUUmdqCc3yVtNFHhNyBQsHRspauTHBTCYo 010m3xspdAkhGa3DtJD8MChjP9eKeOy3xgDwh8MhLQ2A9jF85KFTi85BzPN7jnn8DPO85w/hsvBi PJZsb0+23XrZ3KWp67tw2F7GsXPAy4JDftlwCG7tZTkoen7NKtDqEXh5WAIXlmuCGzkmSjYIZ8BV KEnaUM8QLYvqRA/RZDxt5gtGyIjjlyNnyMgJ3ezohYzFh96+NuYpddg/u7QNzPXg/lTo4jc/feeP l+tw6eNb/H646hNGcn2/eHXsM/d8LBpY7dnok4qRcUVY3LmEy6/6nt+/4OzMQbk1htLgLLIUZ4dz p8kds+xIAksZezKWi6OkNNlRpTByJk0W2AY0u0MjEylbvefMBIfbFE9ElsOBDjPWFw70tHdHVM0R 9uvbYlCOfS7c5qUul9o5L6I/heLeoyoRv5Bv920tlyuVarmMghaKbm97rCcciPcuOTvVFgieEVa8 5VqtXArLblPt1MaDXiorkZzgj3jt8hG1aNcwIi37PT/ge0dN6ZXnB60whAUyzHZhK8NrxhbOnbtw bA0MLJzLTnqx/PDwmpP36Zh8MVr9bOnGhlwzNfgJDnOcQakGynuEA8Z6XPNnF2T9ffrSNUv1BuwL c8xY/73b3tXV3tbT398zq02Nneg8xWJhpwkbY61nG639t4d37Hh4x/FiscTPsTWx5bM7WPkrxSqW lf/Cb0IsbFpRpqgM+TTf9C38lr7Gd53mm+x3+3va6t81u11PfX93xofHdlZmtd1oD9lAa2my0cxv Y7WWhthclwhzmVYY+manhPi2UKVa5SxQxLpq46waaZ4PLFYqNeSv+nGzMueXE3QEIVy8yCoYAaub JMNYN8UsG9CtgKmbgOx5EKYqFbuCmXofhuFgnQZt04CRNwDxw8FhbKbESfieVhBlZIV08nMF7DgH 99mjkE83XGuGleWHSqjJtybh74Wzhs/oCcU7esHl7B0Z6c1s27agJ5v+F2bXwNzhxQOR3g6USCOr RrZty/Z0D/0rYpWZs3KAtm8XkyjA3omt1Q8aNd142XyjMXaaDc2TvxeG4m6PK1PMuoLB/LJl+WJ5 Mj6U/l5x1eQkdDtd8SExmjKjbLulVBqKdw3sKJUmJ7l/vH7Gykm8zDubYjCUorhgXnJgQMuwoIov WVVkvnLuL48VJ6emapOTZWbCQbFcnppq+trZu45gzkdChOTxFSmmCbhpnrIMdvSu+Ra2e2gfnJqa bBzhRPafmiqXT/FxAtfBApnmzoIad4fOoslDHq3wfN1HWpuuU9Rp/g1pjjK/PwyhckZYbZgCeXzl ysfL0a98JQrtkUolcvPB1aubc0y4z8HDeFwrhFHVyUPATz4mGey00OzHWdbpcy5FcW1ftQqm+guF 5g+edEmKfbEiubZX97eUz4wNPInv9nOpTXr4waOcyR194RBNz7oXW1qHa0MKnTePKiE6MXGRptBo lCoaHRhY3+hGEWvm7ZlHkax4qJj2a7R7TTfV/M65t8yFxSd6xcbkCVKAW+FeNj89LcjQbMJHuNVR Ed3MQLvzTmanucXKr1xihWWdjrt2YYRVfHxdM/vrDs49zBvfzn0YGRw9M6kbzcPTVj5JDal5khrx mylBHbwaDSyaQtOmzJw0nBMYX7GDLhV2tBdZ7Iv2MzREo5TCOE/t44jrgdGXiyVOwg/5YvoaUiBB iOITLD2IhOT/A53J/nchIrwEL/BzgMTKZ02KH2Cm8nE25ob1zm2PPnIbONaCw/7PtWtufeSRB297 5JHX1s55bS2mzF5eSDR4DJ5v2NwhXAFD+J4WzErj+SQ/aGpk2ZaI1Yr9mB+iG23U3W/yKwU9N3jk B6sXi+J6Ee5WvfZzzD922NvhXOf0/qqtHf/yv5Q9N2wWHOsc4qenCauGskf1IEEnXy+bp/+E9vvj +D3M/4dY/M+3fvdHNtt9XPZsW88b3HlKg8zvpSEvP9Jy+mDk9OfHmAGSbwRjUEBc0jgeirAPDUZ+ uC0H/2RfqkejOnyJxfal8KVflfn1uKS0derZUGfY4wEowyNY+yKnzOlR+4uQOzJ4E/5dD1LQ7exr 78Srvc/pDjjEoMvFMIZn+rfwQcMW72KnzZo7eoJc9xNI41JekZCrZW6IwyUv3Cv+NYYDB64TJjHY z/n61g/E3fV4Q+SsYIcbm39V/KsX78Vgv8KpJg+kBGd8YE1BEpyJgTX5uzqCZ0U8epTU9xTfgvfQ fm76AxRgTYeDhXRCwQXNJAoTJXmGqahuMYTAJA28L+z9h90YLoRrDlwH1x649HKq6s4xUHUVxpy6 Sq9whiHHSXYLdvXAdUh2NpapdOJ+t6q67x+XVd11OWXzNUQ6BXlmD78+azMWWwCD2cK7D8Em+0Az 3LiQX6+PnHMGjFmWdba1wqqvIRfKiA/gTeS3OcyXXrD4oYd8MkQVQFuayQGOQPVszhpHfX/iTNvR kJ7MGJG+kc2+0ErM+er3vSNbfKEVyazxHb5d9VhyRWitb6QvguIFM+HkCh1veyNGgt3G+L4XG9+v kzH4Z7jzdOcTvg5D9k9nwp3ZC/CP9/1FshTl26W4cvs+RLKHTyvurxM/KzpFRRQnJ0VMnOJnJ7GE niih4mdfx1DPXjNZr2IUs4vYGHpI1/T/hD9B+RQ/6OfQyn6MSaZc2mR+dHYWDSGM1TiXy+65Z2VW vTGrXmy5Z/Uwc663Xm82nmf3rc83661Z9VZLPb+fZdHe4pG7qQs+Mrbd8vdlF2BMnf/ZWrvRTTFx s/wml1yVXRhRZ+lEKZw2G0Nr97qAYQRgr2oEm3VOcG+JyW63zKItMXYv8/jJMist49uhp0GAT9i/ jrHT9Zh3/Xgn79FORrzutK/YcuK5QfsQN7RXMFcs37v3wwtwH2r7FciJNJwJW1nUfuKH+ULwb8YZ wnjsJG9IpltgxjFzh7wxR+vVROr0+56SZMWQHXTBx2Jirl12+amLuiWH5PMoEOoKtycyKFUVwSF4 jGupJkuyBiJ1A6hLPDrMuwbt/LZU3H43JIgO0eF74LC0fUyJxP0datgrSACy20EFQQSPizoEkUpd IxDIBRDHSIixBUodoiQwePZ/AKvoZKAAeNp1jkFqAkEQRd/oaAgJwVXIsiGbbGaYnoCCB5gDZOFe pBkG1IZWISdx5RGyzDFygBwhx/A71iYLG4p+9ftX/QYeOZJxORl3TIwH4jfjIa98GufSv4xHPPBj PJb+J2eW30t56qcuPBC/GA9pqIxz6SfjEc98G4+l/9KxIrJRRbbQreImRsEHgZYDa5YktaE9rJeC pvft+zvJEXDUlEpzzFX/9101z5SCmaqWz/OuNXG7b2Jqg6vLys2d5Yr8tJgVdeVluvW3hVITO71f M3yfzyKkXacdvqxuzp4BBTo53QB42n3TVW+UYRBA4T1boMUp7u76vbrf4pUtUtwdakCRAiH8fwic ayaZnMsnczGdbuf/8/PP0unSZYhlLGcFw4ywklWsZg1rWcd6NjDKRjaxmS1sZRvb2cFOdrGbPexl H/s5wEEOcZgjHOUYxznBSU5xmjOc5RwNgUgiU6j0aOlzngtc5BKXucJVxhhngkkGTHGN69zgJtPc 4jZ3uMs97vOAhzziMU94yjOe84KXvOI1b3jLO2aYZY55FnjPBz6yyCc+84WvLPGN7/wY/rW0OGia xgYbbbLZFtuzre3bMTtuJ+ykHdipfw26QTfoBt2gG3RDtfpBP+gH/aAf9IN+0I26UTfqRt2oG3Wj btSNulE36kbdqBt1o3dH/aSf9JN+0k/6ST/pJ/2kn/STftJP+kk/6Sf9rJ/1s37Wz/pZP+tn/ayf 9bN+1s/6WT/rF92iW3SLbtEtukW36Bbdolt0i27RLbpFt+pW3apbdatu1a26VbfqVt06Y2ftnJ23 C3/b+k+t/9Q21fZs/zd3nxHSAAAAAAAAAf//AAJ42mNgZGBg4ANiCQYQYGJgZGBmKAKSLGAeAwAJ SgCnAHjaY2BgYGQAgpOd+YYg+hxXUgKMBgA65wUoAAA=")
		format("woff"),
		url(http://s0.kuaizhan.com/res/skin/css/fonts/icomoon.eot)
		format("embedded-opentype"),
		url(http://s0.kuaizhan.com/res/skin/css/fonts/icomoon.ttf)
		format("truetype"),
		url(http://s0.kuaizhan.com/res/skin/css/fonts/icomoon.svg)
		format("svg")
}

body {
	line-height: 1.5;
	font-family: "\5FAE\8F6F\96C5\9ED1", "微软雅黑", "Microsoft YaHei",
		Helvetica, Tahoma, sans-serif;
	color: #333;
	font-size: 12px
}

body .font-ico {
	font-family: 'icomoon';
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	-webkit-font-smoothing: antialiased
}

body .feedback, body .error, body .alert, body .notice, body .success,
	body .info {
	padding: 0.8em;
	margin-bottom: 1em;
	border: 1px solid #ddd
}

body .error, body .alert {
	background: #fbe3e4;
	color: #8a1f11;
	border-color: #f27979
}

body .error a, body .alert a {
	color: #8a1f11
}

body .notice {
	background: #fff6bf;
	color: #514721;
	border-color: #f9c86a
}

body .notice a {
	color: #514721
}

body .success {
	background: #e6efc2;
	color: #264409;
	border-color: #c6d880
}

body .success a {
	color: #264409
}

body .info {
	background: #d5edf8;
	color: #205791;
	border-color: #92cae4
}

body .info a {
	color: #205791
}

body .hide {
	display: none
}

body .highlight {
	background: #ff0
}

body .added {
	background: #060;
	color: #fff
}

body .removed {
	background: #900;
	color: #fff
}

body .border {
	padding-right: 4px;
	margin-right: 5px;
	border-right: 1px solid #b8b8b8
}

body .colborder {
	padding-right: 19px;
	margin-right: 20px;
	border-right: 1px solid #ddd
}

body hr {
	background: #ddd;
	color: #ddd;
	clear: both;
	float: none;
	width: 100%;
	height: 1px;
	margin: 0 0 1.45em;
	border: none
}

body hr.space {
	background: #ddd;
	color: #ddd;
	clear: both;
	float: none;
	width: 100%;
	height: 1px;
	margin: 0 0 1.45em;
	border: none;
	background: white;
	color: white;
	visibility: hidden
}

body .txt-quiet {
	color: #666
}

body .txt-loud {
	color: #111
}

body .txt-small {
	font-size: 10px
}

body .txt-normal {
	font-size: 12px
}

body .txt-large {
	font-size: 16px
}

body .txt-strong {
	font-weight: bold
}

body .txt-left {
	text-align: left
}

body .txt-center {
	text-align: center
}

body .txt-right {
	text-align: right
}

body a.link {
	color: #06c;
	text-decoration: none
}

body a.link:hover {
	color: #09f;
	text-decoration: underline
}

body a.button {
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline;
	margin: 0 10px 0 0;
	border-width: 1px;
	border-style: solid;
	font-size: 12px;
	font-weight: bold;
	text-decoration: none;
	cursor: pointer;
	outline: none;
	-webkit-border-radius: 0px;
	-moz-border-radius: 0px;
	-ms-border-radius: 0px;
	-o-border-radius: 0px;
	border-radius: 0px;
	padding: 0 15px;
	height: 28px;
	width: auto;
	line-height: 28px
}

body a.button img {
	margin: 0 3px -3px 0 !important;
	padding: 0;
	border: none;
	width: 16px;
	height: 16px;
	float: none
}

body a.button .font-ico {
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline;
	margin: 9px 5px 0 0;
	vertical-align: top
}

body button {
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline;
	margin: 0 10px 0 0;
	border-width: 1px;
	border-style: solid;
	font-size: 12px;
	font-weight: bold;
	text-decoration: none;
	cursor: pointer;
	outline: none;
	-webkit-border-radius: 0px;
	-moz-border-radius: 0px;
	-ms-border-radius: 0px;
	-o-border-radius: 0px;
	border-radius: 0px;
	height: 30px;
	line-height: 30px;
	width: auto;
	overflow: visible;
	padding: 0 15px
}

body button img {
	margin: 0 3px -3px 0 !important;
	padding: 0;
	border: none;
	width: 16px;
	height: 16px;
	float: none
}

body button[type] {
	line-height: 30px
}

body a.button, body button {
	background-color: #fafafa;
	background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(0%, #fafafa),
		color-stop(100%, #ededed));
	background-image: -webkit-linear-gradient(#fafafa, #ededed);
	background-image: -moz-linear-gradient(#fafafa, #ededed);
	background-image: -o-linear-gradient(#fafafa, #ededed);
	background-image: linear-gradient(#fafafa, #ededed);
	border-color: #d4d4d4 #c4c4c4 #c4c4c4 #d4d4d4;
	color: #333
}

body a.button:hover, body button:hover {
	background-color: #fcfcfc;
	background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(0%, #fcfcfc),
		color-stop(100%, #efefef));
	background-image: -webkit-linear-gradient(#fcfcfc, #efefef);
	background-image: -moz-linear-gradient(#fcfcfc, #efefef);
	background-image: -o-linear-gradient(#fcfcfc, #efefef);
	background-image: linear-gradient(#fcfcfc, #efefef);
	border-color: #94d4fc #84c4ec #84c4ec #94d4fc;
	color: #0085d9
}

body a.button:active, body button:active {
	background-color: #6299c5;
	background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(0%, #699ec7),
		color-stop(100%, #5d91bb));
	background-image: -webkit-linear-gradient(#699ec7, #5d91bb);
	background-image: -moz-linear-gradient(#699ec7, #5d91bb);
	background-image: -o-linear-gradient(#699ec7, #5d91bb);
	background-image: linear-gradient(#699ec7, #5d91bb);
	border-color: #72a9d5 #6299c5 #6299c5 #72a9d5;
	color: #fff
}

body button.confirm, body a.button.confirm {
	background-color: #57b4f0;
	background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(0%, #5fb7f0),
		color-stop(100%, #52abe4));
	background-image: -webkit-linear-gradient(#5fb7f0, #52abe4);
	background-image: -moz-linear-gradient(#5fb7f0, #52abe4);
	background-image: -o-linear-gradient(#5fb7f0, #52abe4);
	background-image: linear-gradient(#5fb7f0, #52abe4);
	border-color: #38a9f1 #2899e1 #2899e1 #38a9f1;
	color: #fff
}

body button.confirm:hover, body a.button.confirm:hover {
	background-color: #71bff2;
	background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(0%, #78c2f2),
		color-stop(100%, #6bb5e5));
	background-image: -webkit-linear-gradient(#78c2f2, #6bb5e5);
	background-image: -moz-linear-gradient(#78c2f2, #6bb5e5);
	background-image: -o-linear-gradient(#78c2f2, #6bb5e5);
	background-image: linear-gradient(#78c2f2, #6bb5e5);
	border-color: #38a9f1 #2899e1 #2899e1 #38a9f1;
	color: #fff
}

body button.confirm:active, body a.button.confirm:active {
	background-color: #71bff2;
	background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(0%, #78c2f2),
		color-stop(100%, #6bb5e5));
	background-image: -webkit-linear-gradient(#78c2f2, #6bb5e5);
	background-image: -moz-linear-gradient(#78c2f2, #6bb5e5);
	background-image: -o-linear-gradient(#78c2f2, #6bb5e5);
	background-image: linear-gradient(#78c2f2, #6bb5e5);
	border-color: #38a9f1 #2899e1 #2899e1 #38a9f1;
	color: #fff
}

body button.disabled, body a.button.disabled {
	filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=65);
	opacity: 0.65;
	cursor: not-allowed
}

body a.button-inset {
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline;
	margin: 0 10px 0 0;
	border-width: 1px;
	border-style: solid;
	font-size: 12px;
	font-weight: bold;
	text-decoration: none;
	cursor: pointer;
	outline: none;
	-webkit-border-radius: 0px;
	-moz-border-radius: 0px;
	-ms-border-radius: 0px;
	-o-border-radius: 0px;
	border-radius: 0px;
	padding: 0 15px;
	height: 26px;
	width: auto;
	line-height: 26px;
	border: 2px solid white;
	color: white;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	-ms-border-radius: 3px;
	-o-border-radius: 3px;
	border-radius: 3px;
	box-sizing: content-box
}

body a.button-inset img {
	margin: 0 3px -3px 0 !important;
	padding: 0;
	border: none;
	width: 16px;
	height: 16px;
	float: none
}

body a.button-inset.cancel {
	border-color: transparent;
	background: none
}

body a.button-inset.cancel:hover {
	color: white
}

body a.button-inset img {
	margin: 0 5px 0 0 !important
}

body a.button-large, body a.button.large {
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline;
	margin: 0 10px 0 0;
	border-width: 1px;
	border-style: solid;
	font-size: 12px;
	font-weight: bold;
	text-decoration: none;
	cursor: pointer;
	outline: none;
	-webkit-border-radius: 0px;
	-moz-border-radius: 0px;
	-ms-border-radius: 0px;
	-o-border-radius: 0px;
	border-radius: 0px;
	padding: 0 20px;
	height: 38px;
	width: auto;
	line-height: 38px;
	font-size: 14px;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	-ms-border-radius: 3px;
	-o-border-radius: 3px;
	border-radius: 3px
}

body a.button-large img, body a.button.large img {
	margin: 0 3px -3px 0 !important;
	padding: 0;
	border: none;
	width: 16px;
	height: 16px;
	float: none
}

body button.button-large, body button.button.large {
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline;
	margin: 0 10px 0 0;
	border-width: 1px;
	border-style: solid;
	font-size: 12px;
	font-weight: bold;
	text-decoration: none;
	cursor: pointer;
	outline: none;
	-webkit-border-radius: 0px;
	-moz-border-radius: 0px;
	-ms-border-radius: 0px;
	-o-border-radius: 0px;
	border-radius: 0px;
	height: 40px;
	line-height: 40px;
	width: auto;
	overflow: visible;
	padding: 0 20px;
	font-size: 14px;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	-ms-border-radius: 3px;
	-o-border-radius: 3px;
	border-radius: 3px
}

body button.button-large img, body button.button.large img {
	margin: 0 3px -3px 0 !important;
	padding: 0;
	border: none;
	width: 16px;
	height: 16px;
	float: none
}

body button.button-large[type], body button.button.large[type] {
	line-height: 40px
}

body a.button.huge {
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline;
	margin: 0 10px 0 0;
	border-width: 1px;
	border-style: solid;
	font-size: 12px;
	font-weight: bold;
	text-decoration: none;
	cursor: pointer;
	outline: none;
	-webkit-border-radius: 0px;
	-moz-border-radius: 0px;
	-ms-border-radius: 0px;
	-o-border-radius: 0px;
	border-radius: 0px;
	padding: 0 20px;
	height: 38px;
	width: auto;
	line-height: 38px;
	font-size: 14px;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	-ms-border-radius: 3px;
	-o-border-radius: 3px;
	border-radius: 3px
}

body a.button.huge img {
	margin: 0 3px -3px 0 !important;
	padding: 0;
	border: none;
	width: 16px;
	height: 16px;
	float: none
}

body button.button.huge {
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline;
	margin: 0 10px 0 0;
	border-width: 1px;
	border-style: solid;
	font-size: 12px;
	font-weight: bold;
	text-decoration: none;
	cursor: pointer;
	outline: none;
	-webkit-border-radius: 0px;
	-moz-border-radius: 0px;
	-ms-border-radius: 0px;
	-o-border-radius: 0px;
	border-radius: 0px;
	height: 40px;
	line-height: 40px;
	width: auto;
	overflow: visible;
	padding: 0 20px;
	font-size: 14px;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	-ms-border-radius: 3px;
	-o-border-radius: 3px;
	border-radius: 3px
}

body button.button.huge img {
	margin: 0 3px -3px 0 !important;
	padding: 0;
	border: none;
	width: 16px;
	height: 16px;
	float: none
}

body button.button.huge[type] {
	line-height: 40px
}

body button.strech, body a.button.strech {
	display: block;
	width: auto
}

body button.strech.huge, body a.button.strech.huge {
	margin-left: 20px;
	margin-right: 20px
}

body input.text, body input.title, body input[type=email], body input[type=text],
	body input[type=password] {
	background-color: white
}

body input.title {
	font-size: 1.5em
}

body .select {
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline;
	position: relative;
	cursor: pointer;
	background: #fff url(../images/select.png) right 50% no-repeat
}

body input.text, body input.title, body input[type=email], body input[type=text],
	body input[type=password] {
	width: 232px;
	font-size: 1em;
	line-height: normal;
	height: 24px;
	padding: 2px 8px
}

body .select .selected {
	width: 220px;
	font-size: 1em;
	line-height: 24px;
	height: 24px;
	padding: 2px 20px 2px 8px;
	white-space: nowrap;
	overflow: hidden;
	-ms-text-overflow: ellipsis;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis
}

body .select .options {
	display: none;
	position: absolute;
	padding-left: 0;
	left: -1px;
	top: 28px;
	width: 100%;
	background-color: #fff;
	z-index: 10
}

body .select .options li {
	list-style: none;
	padding: 0 8px;
	line-height: 28px;
	white-space: nowrap;
	overflow: hidden;
	-ms-text-overflow: ellipsis;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis
}

body .select .options li:hover {
	background-color: #ededed
}

body .select .options li.disabled {
	cursor: not-allowed;
	opacity: 0.65
}

body .select:hover .options {
	display: block
}

body textarea {
	padding: 8px;
	font-size: 1em;
	line-height: normal;
	width: 232px;
	height: 90px;
	resize: none
}

body input.text, body input.title, body input[type=email], body input[type=text],
	body input[type=password], body textarea, body .select {
	background-color: #fff;
	border: 1px solid #a8acad;
	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	-ms-border-radius: 0;
	-o-border-radius: 0;
	border-radius: 0
}

body input.text .options, body input.title .options, body input[type=email] .options,
	body input[type=text] .options, body input[type=password] .options,
	body textarea .options, body .select .options {
	border: 1px solid #a8acad;
	border-top: none
}

body input.text:focus, body input.title:focus, body input[type=email]:focus,
	body input[type=text]:focus, body input[type=password]:focus, body textarea:focus,
	body .select:focus {
	border-color: #48adee
}

body input.text:focus .options, body input.title:focus .options, body input[type=email]:focus .options,
	body input[type=text]:focus .options, body input[type=password]:focus .options,
	body textarea:focus .options, body .select:focus .options {
	border-color: #48adee
}

body input.text:hover, body input.title:hover, body input[type=email]:hover,
	body input[type=text]:hover, body input[type=password]:hover, body textarea:hover,
	body .select:hover {
	border-color: #48adee
}

body input.text:hover .options, body input.title:hover .options, body input[type=email]:hover .options,
	body input[type=text]:hover .options, body input[type=password]:hover .options,
	body textarea:hover .options, body .select:hover .options {
	border-color: #48adee
}

body select {
	background-color: #fff;
	border-width: 1px;
	border-style: solid
}

body input.text, body input.title, body input[type=email], body input[type=text],
	body input[type=password], body textarea {
	outline: none;
	-webkit-box-shadow: inset 1px 1px 3px 0px #ddd;
	-moz-box-shadow: inset 1px 1px 3px 0px #ddd;
	box-shadow: inset 1px 1px 3px 0px #ddd
}

body input.text:focus, body input.title:focus, body input[type=email]:focus,
	body input[type=text]:focus, body input[type=password]:focus, body textarea:focus
	{
	-webkit-box-shadow: inset 1px 1px 3px 0px #ddd;
	-moz-box-shadow: inset 1px 1px 3px 0px #ddd;
	box-shadow: inset 1px 1px 3px 0px #ddd
}

body input.text:hover, body input.title:hover, body input[type=email]:hover,
	body input[type=text]:hover, body input[type=password]:hover, body textarea:hover
	{
	-webkit-box-shadow: inset 1px 1px 3px 0px #ddd;
	-moz-box-shadow: inset 1px 1px 3px 0px #ddd;
	box-shadow: inset 1px 1px 3px 0px #ddd
}

body .form label, body form label {
	font-weight: bold
}

body .form label.checkbox, body .form label.radio, body form label.checkbox,
	body form label.radio {
	font-weight: normal
}

body .form fieldset, body form fieldset {
	padding: 1.4em;
	margin: 0 0 1.5em 0
}

body .form legend, body form legend {
	font-weight: bold;
	font-size: 1.2em
}

body .form p, body .form .form-row, body form p, body form .form-row {
	padding: 0.5em 0;
	margin: 0 0 1.5em;
	*zoom: 1
}

body .form p:after, body .form .form-row:after, body form p:after, body form .form-row:after
	{
	content: ".";
	display: block;
	visibility: hidden;
	clear: both;
	height: 0;
	font-size: 0
}

body .form .form-subrow, body form .form-subrow {
	padding: 0.5em 0;
	margin: 0 0 1em;
	*zoom: 1
}

body .form .form-subrow:after, body form .form-subrow:after {
	content: ".";
	display: block;
	visibility: hidden;
	clear: both;
	height: 0;
	font-size: 0
}

body .form p.first, body .form .form-row.first, body .form .form-subrow.first,
	body form p.first, body form .form-row.first, body form .form-subrow.first
	{
	padding-top: 0
}

body .form p.last, body .form .form-row.last, body .form .form-subrow.last,
	body form p.last, body form .form-row.last, body form .form-subrow.last
	{
	margin-bottom: 0
}

body .form .form-cell, body form .form-cell {
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline
}

body .form .tip, body form .tip {
	display: inline-block;
	vertical-align: top;
	padding: 0 0 0 25px;
	margin: 0 0 0 1em
}

body .form .tip.next-line, body form .tip.next-line {
	margin-left: 0
}

body .form .tip-error, body form .tip-error {
	display: none;
	background: url(../images/tip-error.png) no-repeat 0 8px
}

body .form .tip-right, body form .tip-right {
	background: url(../images/tip-right.png) no-repeat 0 8px
}

body .form .tip-info, body form .tip-info {
	background: url(../images/tip-light.png) no-repeat 0 7px
}

body .form .select.vd-error, body .form input.vd-error, body .form textarea.vd-error,
	body form .select.vd-error, body form input.vd-error, body form textarea.vd-error
	{
	border-color: #f27979
}

body .form input.vd-right, body .form textarea.vd-right, body form input.vd-right,
	body form textarea.vd-right {
	border-color: #c6d880
}

body .form p, body .form .form-row, body .form .form-subrow, body form p,
	body form .form-row, body form .form-subrow {
	line-height: 30px;
	min-height: 30px
}

body .form p.error, body .form p.alert, body .form .form-row.error, body .form .form-row.alert,
	body .form .form-subrow.error, body .form .form-subrow.alert, body form p.error,
	body form p.alert, body form .form-row.error, body form .form-row.alert,
	body form .form-subrow.error, body form .form-subrow.alert {
	border-width: 0;
	background-color: transparent;
	color: inherit
}

body .form p.error .tip-error, body .form p.alert .tip-error, body .form .form-row.error .tip-error,
	body .form .form-row.alert .tip-error, body .form .form-subrow.error .tip-error,
	body .form .form-subrow.alert .tip-error, body form p.error .tip-error,
	body form p.alert .tip-error, body form .form-row.error .tip-error,
	body form .form-row.alert .tip-error, body form .form-subrow.error .tip-error,
	body form .form-subrow.alert .tip-error {
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline
}

body .form p.error .tip-info, body .form p.alert .tip-info, body .form .form-row.error .tip-info,
	body .form .form-row.alert .tip-info, body .form .form-subrow.error .tip-info,
	body .form .form-subrow.alert .tip-info, body form p.error .tip-info,
	body form p.alert .tip-info, body form .form-row.error .tip-info, body form .form-row.alert .tip-info,
	body form .form-subrow.error .tip-info, body form .form-subrow.alert .tip-info
	{
	display: none
}

body .form .form-cell, body form .form-cell {
	width: 250px
}

body .form label, body form label {
	display: inline;
	float: left;
	margin-right: 15px;
	width: 85px;
	text-align: left
}

body .form label.checkbox, body .form label.radio, body form label.checkbox,
	body form label.radio {
	width: auto;
	float: none;
	margin: 0 1em 0 0
}

body .form .tip, body form .tip {
	height: 30px;
	line-height: 30px
}

body .form.form-large .select.vd-error, body .form.form-large input.vd-error,
	body .form.form-large textarea.vd-error, body form.form-large .select.vd-error,
	body form.form-large input.vd-error, body form.form-large textarea.vd-error
	{
	border-color: #f27979
}

body .form.form-large input.vd-right, body .form.form-large textarea.vd-right,
	body form.form-large input.vd-right, body form.form-large textarea.vd-right
	{
	border-color: #c6d880
}

body .form.form-large p, body .form.form-large .form-row, body .form.form-large .form-subrow,
	body form.form-large p, body form.form-large .form-row, body form.form-large .form-subrow
	{
	line-height: 35px;
	min-height: 35px
}

body .form.form-large p.error, body .form.form-large p.alert, body .form.form-large .form-row.error,
	body .form.form-large .form-row.alert, body .form.form-large .form-subrow.error,
	body .form.form-large .form-subrow.alert, body form.form-large p.error,
	body form.form-large p.alert, body form.form-large .form-row.error,
	body form.form-large .form-row.alert, body form.form-large .form-subrow.error,
	body form.form-large .form-subrow.alert {
	border-width: 0;
	background-color: transparent;
	color: inherit
}

body .form.form-large p.error .tip-error, body .form.form-large p.alert .tip-error,
	body .form.form-large .form-row.error .tip-error, body .form.form-large .form-row.alert .tip-error,
	body .form.form-large .form-subrow.error .tip-error, body .form.form-large .form-subrow.alert .tip-error,
	body form.form-large p.error .tip-error, body form.form-large p.alert .tip-error,
	body form.form-large .form-row.error .tip-error, body form.form-large .form-row.alert .tip-error,
	body form.form-large .form-subrow.error .tip-error, body form.form-large .form-subrow.alert .tip-error
	{
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline
}

body .form.form-large p.error .tip-info, body .form.form-large p.alert .tip-info,
	body .form.form-large .form-row.error .tip-info, body .form.form-large .form-row.alert .tip-info,
	body .form.form-large .form-subrow.error .tip-info, body .form.form-large .form-subrow.alert .tip-info,
	body form.form-large p.error .tip-info, body form.form-large p.alert .tip-info,
	body form.form-large .form-row.error .tip-info, body form.form-large .form-row.alert .tip-info,
	body form.form-large .form-subrow.error .tip-info, body form.form-large .form-subrow.alert .tip-info
	{
	display: none
}

body .form.form-large .form-cell, body form.form-large .form-cell {
	width: 250px
}

body .form.form-large label, body form.form-large label {
	display: inline;
	float: left;
	margin-right: 15px;
	width: 85px;
	text-align: left
}

body .form.form-large label.checkbox, body .form.form-large label.radio,
	body form.form-large label.checkbox, body form.form-large label.radio {
	width: auto;
	float: none;
	margin: 0 1em 0 0
}

body .form.form-large .tip, body form.form-large .tip {
	height: 35px;
	line-height: 35px
}

body .form.single-col label, body form.single-col label {
	display: block;
	float: none;
	text-align: left;
	width: auto
}

body .form.single-col label.checkbox, body .form.single-col label.radio,
	body form.single-col label.checkbox, body form.single-col label.radio {
	display: inline-block
}

body .form.single-col .form-cell, body form.single-col .form-cell {
	width: auto
}

body form.inline {
	line-height: 3
}

body form.inline p, body form.inline .form-row {
	margin-bottom: 0
}

body form.inline a.button, body form.inline a.confirm, body form.inline button,
	body form.inline input[type='reset'], body form.inline input[type='submit']
	{
	margin: 0
}

body .fn-right {
	display: inline;
	float: right
}

body .ui-slider {
	width: 250px;
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline
}

body .ui-slider .progress {
	position: relative;
	border: none;
	background: #baddf3;
	width: 193px;
	margin: 0 3.5px;
	height: 5px;
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline
}

body .ui-slider .value {
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline;
	width: 50px;
	text-align: right
}

body .ui-slider em, body .ui-slider i {
	position: absolute;
	top: 0;
	border-radius: 2.5px
}

body .ui-slider em {
	left: 0;
	height: 5px;
	border: none;
	background: #1a8fda;
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline
}

body .ui-slider i {
	background: #fff;
	box-shadow: 1px 1px 2px 0px #ccc;
	left: 50%;
	width: 5px;
	height: 15px;
	border: 1px solid #0a3551;
	margin-top: -5.5px;
	cursor: pointer
}

body .ui-tab {
	border: 1px solid #b8b8b8;
	border-radius: 5px
}

body .ui-tab .tab-nav {
	margin: 0;
	padding: 0;
	border-top: 1px solid white;
	border-bottom: 1px solid #c7c7c7;
	background-color: #e4e5e6;
	border-radius: 5px 5px 0 0
}

body .ui-tab .tab-nav li {
	list-style: none;
	vertical-align: top;
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline
}

body .ui-tab .tab-nav a {
	color: #666;
	font-size: 1.2em;
	height: 40px;
	line-height: 40px;
	padding: 0 20px;
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline;
	text-decoration: none
}

body .ui-tab .tab-nav li.cur {
	color: #1a8fda;
	background-color: white;
	border-width: 0 1px;
	border-style: solid;
	border-color: #c7c7c7;
	padding-top: 1px;
	padding-bottom: 1px;
	margin-top: -1px;
	margin-bottom: -1px
}

body .ui-tab .tab-nav li.cur a {
	color: #111
}

body .ui-tab .tab-nav li:first-child {
	border-radius: 5px 0 0 0;
	border-left: none
}

body .ui-tab .tab-panels .tab-content {
	padding: 20px;
	display: none
}

body .ui-tab .tab-panels fieldset {
	padding: 0;
	margin: 0
}

body .ui-tab .tab-panels .tab-content.cur {
	display: block
}

body .ui-tab.theme-simple {
	border: none;
	border-radius: 0
}

body .ui-tab.theme-simple .tab-nav {
	background: none
}

body .ui-tab.theme-simple .tab-nav li.cur {
	border-width: 0 0 3px 0;
	border-color: #1a8fda;
	margin-bottom: -2px;
	padding-bottom: 0;
	padding-top: 0;
	margin-top: -1px
}

body .ui-type-select ul.type-ul {
	text-align: center;
	margin-top: -1.2em;
	overflow: hidden;
	*zoom: 1
}

body .ui-type-select ul.type-ul li.type-li {
	width: 100%;
	float: left;
	margin-top: 1.2em;
	cursor: pointer;
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline
}

body .ui-type-select ul.type-ul li.type-li .option {
	padding: 7px;
	position: relative;
	border: 2px solid transparent;
	border-radius: 3px
}

body .ui-type-select ul.type-ul li.type-li .option:hover {
	border-color: #a3d2f0
}

body .ui-type-select ul.type-ul li.type-li.first {
	margin-top: 0
}

body .ui-type-select ul.type-ul li.type-li.cur {
	cursor: default
}

body .ui-type-select ul.type-ul li.type-li.cur .option {
	border: 2px solid #1a8fda
}

body .ui-type-select ul.type-ul li.type-li.cur .option:hover {
	border-color: #6ab6e6
}

body .ui-type-select ul.type-ul li.type-li.cur .ico {
	position: absolute;
	right: -1px;
	bottom: -1px;
	width: 23px;
	height: 23px;
	background: url(../images/ui-bg.png) no-repeat -5px -5px transparent
}

body .ui-type-select ul.two-col li.type-li {
	width: 50%
}

body .ui-type-select ul.three-col li.type-li {
	width: 33%
}

body .popover {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 100;
	display: none;
	padding: 5px
}

body .popover .arrow {
	position: absolute;
	width: 0;
	height: 0
}

body .popover.top {
	margin-top: -5px
}

body .popover.top .ico-arrow {
	position: absolute;
	right: 50%;
	bottom: -12px;
	margin-right: -9px;
	width: 18px;
	height: 18px
}

body .popover.top .ico-arrow-bd {
	position: absolute;
	left: 0;
	top: 0;
	border-style: solid;
	border-width: 9px;
	border-color: #f9c86a transparent transparent transparent
}

body .popover.top .ico-arrow-bg {
	position: absolute;
	left: 2px;
	top: -1px;
	border-style: solid;
	border-width: 7px;
	border-color: #fff8d3 transparent transparent transparent
}

body .popover.top .font-ico {
	font-size: 20px;
	color: #ed0000;
	vertical-align: -3px;
	margin-right: 8px
}

body .popover.right {
	margin-left: 5px
}

body .popover.bottom {
	margin-top: 5px
}

body .popover.left {
	margin-left: -5px
}

body .popover-inner {
	overflow: hidden;
	background: #fff8d3;
	border: 2px solid #f9c86a;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	-ms-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	-webkit-box-shadow: 0 0 5px 3px #ddd;
	-moz-box-shadow: 0 0 5px 3px #ddd;
	box-shadow: 0 0 5px 3px #ddd
}

body .popover-title {
	padding: 9px 15px;
	line-height: 1;
	background-color: #f5f5f5;
	border-bottom: 1px solid #eee;
	-webkit-border-radius: 3px 3px 0 0;
	-moz-border-radius: 3px 3px 0 0;
	-ms-border-radius: 3px 3px 0 0;
	-o-border-radius: 3px 3px 0 0;
	border-radius: 3px 3px 0 0
}

body .popover-content {
	line-height: 22px;
	font-size: 12px;
	padding: 8px 13px;
	-webkit-border-radius: 0 0 3px 3px;
	-moz-border-radius: 0 0 3px 3px;
	-ms-border-radius: 0 0 3px 3px;
	-o-border-radius: 0 0 3px 3px;
	border-radius: 0 0 3px 3px;
	-webkit-background-clip: padding;
	-moz-background-clip: padding;
	background-clip: padding-box
}

body .popover-content p, body .popover-content ul, body .popover-content ol
	{
	margin-bottom: 0
}

body .modal-backdrop {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 999;
	background-color: #000000
}

body .modal-backdrop.fade {
	filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);
	opacity: 0
}

body .modal-backdrop, body .modal-backdrop.fade.in {
	filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=50);
	opacity: 0.5
}

body .ui-pop {
	display: none;
	position: absolute;
	left: 50%;
	top: 50%;
	z-index: 1000;
	background: #fff;
	padding: 30px 90px;
	border-radius: 3px;
	box-shadow: 0 0 10px 1px rgba(0, 0, 0, 0.5);
	margin: -50px 0 0 -200px
}

body .ui-pop .close {
	position: absolute;
	right: -15px;
	top: -15px;
	width: 31px;
	height: 31px;
	background: url(../images/preview-close.png) no-repeat;
	cursor: pointer
}

body .ui-pop .close:hover {
	background: url(../images/preview-close1.png) no-repeat
}

body .ui-pop .hd, body .ui-pop .btn-con {
	text-align: center
}

body .ui-pop .hd {
	line-height: 32px;
	font-size: 16px
}

body .ui-pop .btn-con {
	font-size: 14px;
	text-align: center;
	padding-top: 2em
}

body .ui-pop .btn-con .confirm, body .ui-pop .btn-con .button, body .ui-pop .btn-con button
	{
	width: 100px;
	height: 34px;
	line-height: 32px;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	-ms-border-radius: 3px;
	-o-border-radius: 3px;
	border-radius: 3px
}

body .ui-pop-lite {
	display: none;
	position: absolute;
	left: 50%;
	top: 50%;
	z-index: 1000;
	background: #fff;
	padding: 20px 30px;
	border: 1px solid #838383;
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	-ms-border-radius: 4px;
	-o-border-radius: 4px;
	border-radius: 4px;
	-webkit-box-shadow: 0px 1px 6px 1px #b2b2b2;
	-moz-box-shadow: 0px 1px 6px 1px #b2b2b2;
	box-shadow: 0px 1px 6px 1px #b2b2b2;
	font-size: 12px;
	line-height: 22px
}

body .ui-pop-lite .close {
	position: absolute;
	right: -15px;
	top: -15px;
	width: 31px;
	height: 31px;
	background: url(../images/preview-close.png) no-repeat;
	cursor: pointer
}

body .ui-pop-lite .close:hover {
	background: url(../images/preview-close1.png) no-repeat
}

body .ui-pop-lite .hd, body .ui-pop-lite .btn-con {
	text-align: center
}

body .ui-pop-lite p {
	font-size: 14px;
	padding: 0
}

body .ui-pop-lite h1 {
	font-size: 14px;
	margin-bottom: 0.5em
}

body .ui-pop-lite .btn-con {
	padding-top: 10px
}

body .ui-pop-lite .btn-con .confirm, body .ui-pop-lite .btn-con .button,
	body .ui-pop-lite .btn-con button {
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	-ms-border-radius: 3px;
	-o-border-radius: 3px;
	border-radius: 3px;
	width: 72px;
	line-height: 24px;
	font-size: 12px
}

body .ui-pop-info {
	z-index: 1001;
	margin: 0
}

body .ui-pop-confirm {
	z-index: 1001
}

body .ui-pop-prompt {
	z-index: 1001
}

body .ui-pop-prompt p {
	margin-bottom: 1em
}

body .ui-pop-preview {
	display: none;
	position: absolute;
	left: 50%;
	top: 50%;
	z-index: 1000;
	background: #fff;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	-ms-border-radius: 3px;
	-o-border-radius: 3px;
	border-radius: 3px
}

body .ui-pop-preview .close {
	position: absolute;
	right: -15px;
	top: -15px;
	width: 31px;
	height: 31px;
	background: url(../images/preview-close.png) no-repeat;
	cursor: pointer
}

body .ui-pop-preview .close:hover {
	background: url(../images/preview-close1.png) no-repeat
}

body .ui-pop-preview .hd, body .ui-pop-preview .btn-con {
	text-align: center
}

body .ui-pop-preview .close, body .ui-pop-preview .close:hover {
	background: url(../../plugin/images/icon_Sprite.png) no-repeat;
	width: 16px;
	height: 16px;
	position: absolute;
	right: 10px;
	top: 10px;
	display: block;
	text-indent: -999em
}

body .ui-pop-preview>.inner {
	position: relative;
	overflow: hidden
}

body .ui-pop-preview h2 {
	text-align: center;
	line-height: 40px;
	height: 40px;
	font-size: 16px
}

body .ui-pop-preview .preview-content {
	-webkit-border-radius: 3px 0 0 3px;
	-moz-border-radius: 3px 0 0 3px;
	-ms-border-radius: 3px 0 0 3px;
	-o-border-radius: 3px 0 0 3px;
	border-radius: 3px 0 0 3px;
	padding: 20px 0 20px 20px;
	background-color: #f6f6f6
}

body .ui-pop-preview .preview-content .phone-wrap {
	width: 320px;
	padding: 29px 19px;
	margin: auto;
	margin-top: 10px;
	background: url(../images/preview-bg.png) no-repeat 0 0 transparent
}

body .ui-pop-preview .preview-content .phone-window {
	background-color: white;
	width: 320px;
	height: 480px
}

body .ui-pop-preview .preview-content iframe {
	width: 100%;
	height: 100%
}

body .ui-pop-preview .preview-content iframe::-webkit-scrollbar {
	display: none
}

body .ui-pop-preview .preview-content iframe::-webkit-scrollbar-track {
	display: none
}

body .ui-pop-preview .preview-content iframe::-webkit-scrollbar-thumb {
	display: none
}

body .ui-pop-preview aside {
	background-color: white;
	-webkit-border-radius: 0 3px 3px 0;
	-moz-border-radius: 0 3px 3px 0;
	-ms-border-radius: 0 3px 3px 0;
	-o-border-radius: 0 3px 3px 0;
	border-radius: 0 3px 3px 0;
	padding: 20px 20px 20px 0
}

body .ui-pop-preview aside .nav-aside ul {
	height: 150px;
	overflow: auto;
	padding-bottom: 10px;
	border-bottom: 1px solid #e8e8e8
}

body .ui-pop-preview aside .nav-aside ul::-webkit-scrollbar {
	width: 4px;
	background: transparent !important
}

body .ui-pop-preview aside .nav-aside ul::-webkit-scrollbar-track {
	-webkit-box-shadow: none;
	width: 4px;
	background: transparent !important
}

body .ui-pop-preview aside .nav-aside ul::-webkit-scrollbar-thumb {
	border-radius: 2px;
	background: rgba(0, 0, 0, 0.2);
	-webkit-box-shadow: none
}

body .ui-pop-preview aside .nav-aside li {
	margin-bottom: 10px
}

body .ui-pop-preview aside section {
	margin-bottom: 20px
}

body .ui-pop-preview aside section.last {
	margin-bottom: 0
}

body .ui-pop-preview aside .price {
	float: right;
	color: #de3e1b
}

body .ui-pop-preview.open {
	display: block
}

body .ui-inset-success {
	visibility: hidden;
	position: absolute;
	left: 50%;
	z-index: 1099;
	color: #514721;
	background-color: #fff6bf;
	border: 1px solid #f9c86a;
	border-radius: 3px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2)
}

body .ui-inset-success .msg {
	padding: 10px
}

body .ui-inset-success.center {
	top: 50%
}

body .ui-inset-success.top {
	top: 50%
}

body .ui-loading {
	position: fixed;
	left: 50%;
	top: 50%;
	margin: -30px 0 0 -30px;
	z-index: 1000
}

body .ui-loading .icon-loading {
	width: 32px;
	height: 32px;
	margin: auto;
	display: block;
	background: url(../images/loading-h.gif) no-repeat 0 0 transparent
}

body .ui-loading h1 {
	color: white;
	font-size: 16px;
	text-align: center;
	line-height: 40px
}

body .ui-toast {
	position: absolute;
	top: 48px;
	left: 50%;
	width: 800px;
	margin-left: -400px;
	background-color: #fff6bf;
	border: 1px solid #f9c86a;
	z-index: 998;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	-ms-border-radius: 3px;
	-o-border-radius: 3px;
	border-radius: 3px;
	-webkit-box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
	-moz-box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
	box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2)
}

body .ui-toast .inner {
	color: #000;
	font-size: 14px;
	position: relative;
	height: 32px;
	line-height: 30px;
	text-align: center
}

body .ui-toast .close {
	position: absolute;
	top: 50%;
	margin-top: -12px;
	line-height: 24px;
	width: 24px;
	height: 24px;
	right: 12px;
	font-size: 12px;
	color: #9d8100;
	text-decoration: none
}

body .ui-toast .close:hover {
	color: white;
	background-color: #9f8300;
	border-radius: 12px
}

body .ui-box {
	min-height: 100px;
	background: #fff;
	padding: 0;
	border: 1px solid #b8b8b8;
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	-ms-border-radius: 4px;
	-o-border-radius: 4px;
	border-radius: 4px
}

body .ui-box .inner {
	padding: 10px
}

body .ui-inset {
	padding: 1em;
	background-color: #f3f7fa;
	border: 1px solid #d8e5ef;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	-ms-border-radius: 3px;
	-o-border-radius: 3px;
	border-radius: 3px
}

body .ui-color-pick li {
	display: inline-block;
	border: 1px solid #4b4b4b;
	text-align: center;
	color: #fff;
	vertical-align: top;
	cursor: pointer
}

body .ui-color-pick li.cur {
	text-shadow: 0 -1px 2px rgba(0, 0, 0, 0.45)
}

body .ui-color-pick .font-ico {
	display: none
}

body .ui-color-pick .cur .font-ico {
	display: inline
}

body .ui-color-pick li {
	width: 28px;
	height: 28px;
	line-height: 28px;
	border-radius: 3px;
	font-size: 13px;
	margin: 7.5px
}

body .ui-color-pick li:hover {
	border: 2px solid #1a91db;
	margin: 6.5px
}

body .ui-color-pick li.cur {
	border: 2px solid #f9c86a;
	margin: 6.5px;
	color: #fff6bf
}

body .ui-color-pick .current-color {
	line-height: 30px;
	margin-bottom: 1em
}

body .ui-color-pick .color-cur {
	display: inline-block;
	width: 28px;
	height: 28px;
	line-height: 29px;
	border: 2px solid #c5c5c5;
	border-radius: 3px;
	text-align: center;
	font-size: 13px;
	color: #fff;
	margin-right: 15px;
	vertical-align: top
}

body .ui-color-pick input.color-val {
	width: 6em;
	font-family: Arial
}

body .ui-color-pick .more-color {
	float: right
}

body .ui-color-pick-mini li {
	display: inline-block;
	border: 1px solid #4b4b4b;
	text-align: center;
	color: #fff;
	vertical-align: top;
	cursor: pointer
}

body .ui-color-pick-mini li.cur {
	text-shadow: 0 -1px 2px rgba(0, 0, 0, 0.45)
}

body .ui-color-pick-mini .font-ico {
	display: none
}

body .ui-color-pick-mini .cur .font-ico {
	display: inline
}

body .ui-color-pick-mini li {
	width: 20px;
	height: 20px;
	line-height: 20px;
	border-radius: 3px;
	font-size: 13px;
	margin: 5.5px
}

body .ui-color-pick-mini li:hover {
	border: 2px solid #1a91db;
	margin: 4.5px
}

body .ui-color-pick-mini li.cur {
	border: 2px solid #f9c86a;
	margin: 4.5px;
	color: #fff6bf
}

body .ui-color-panel {
	position: absolute;
	border: 1px solid #bbb;
	padding: 5px;
	background-color: white;
	border-radius: 3px
}

body .ui-color-panel ul {
	width: 208px;
	background-color: white;
	overflow: hidden;
	list-style: none
}

body .ui-color-panel li {
	float: left
}

body .ui-color-panel li a {
	display: block;
	border: 1px solid #a6a6a6;
	margin: 5px;
	padding: 0
}

body .ui-color-panel li a:hover {
	margin: 4px;
	padding: 0;
	border: 2px solid #FFC016
}

body .ui-color-panel li em {
	height: 14px;
	width: 14px;
	border: none;
	display: block
}

body .ui-icon-panel {
	position: absolute;
	width: 300px;
	padding: 10px 20px;
	text-align: center;
	background: rgba(90, 90, 90, 0.95);
	z-index: 1000;
	border-radius: 3px
}

body .ui-icon-panel ul {
	overflow: hidden;
	*zoom: 1
}

body .ui-icon-panel li {
	list-style: none;
	float: left;
	margin: 8px
}

body .ui-icon-panel li a {
	position: relative;
	display: inline-block;
	vertical-align: top;
	width: 34px;
	height: 34px;
	line-height: 34px;
	font-size: 22px;
	color: #fff;
	text-align: center;
	border: 3px solid transparent;
	cursor: pointer;
	text-decoration: none
}

body .ui-icon-panel li a:hover {
	border: 3px solid #f9c86a
}

body .ui-icon-panel li a .ico-selected {
	display: none;
	position: absolute;
	right: 0;
	bottom: 0;
	width: 23px;
	height: 23px;
	background: url(../images/type-selected.png) no-repeat 3px 3px
}

body .ui-icon-panel li.cur a {
	border: 3px solid #f9c86a
}

body .ui-icon-panel li.cur a .ico-selected {
	display: block
}

body .ui-iconselect {
	width: 270px
}

body .ui-iconselect ul {
	overflow: hidden;
	*zoom: 1
}

body .ui-iconselect li {
	list-style: none;
	float: left;
	margin: 0 5px
}

body .ui-iconselect li a {
	position: relative;
	display: inline-block;
	vertical-align: top;
	width: 24px;
	height: 24px;
	line-height: 24px;
	font-size: 16px;
	color: #666;
	text-align: center;
	border: 3px solid transparent;
	cursor: pointer;
	text-decoration: none
}

body .ui-iconselect li a:hover {
	border: 3px solid #f9c86a
}

body .ui-iconselect li a .ico-selected {
	display: none;
	position: absolute;
	right: 0;
	bottom: 0;
	width: 23px;
	height: 23px;
	background: url(../images/type-selected.png) no-repeat 3px 3px
}

body .ui-iconselect li.cur a {
	border: 3px solid #f9c86a
}

body .ui-iconselect li.cur a .ico-selected {
	display: block
}

body .ui-iconselect ul {
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline
}

body .ui-iconselect .add {
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline;
	text-decoration: none;
	color: #666;
	width: 30px;
	height: 30px;
	text-align: center;
	text-decoration: none;
	text-align: center;
	font-size: 12px;
	line-height: 15px;
	font-style: normal
}

body .ui-iconselect .add .ico {
	margin-right: 0;
	font-size: 12px;
	line-height: 15px
}

body .ui-richtext {
	width: 100%;
	min-width: 318px;
	border: 1px solid #b8b8b8
}

body .ui-richtext textarea {
	visibility: hidden;
	display: none
}

body .ui-richtext .head {
	width: 100%;
	height: 35px;
	background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(0%, #fafafa),
		color-stop(100%, #ededed));
	background-image: -webkit-linear-gradient(#fafafa, #ededed);
	background-image: -moz-linear-gradient(#fafafa, #ededed);
	background-image: -o-linear-gradient(#fafafa, #ededed);
	background-image: linear-gradient(#fafafa, #ededed);
	border-bottom: 1px solid #b8b8b8
}

body .ui-richtext .head .ico {
	cursor: pointer;
	position: absolute;
	display: block;
	height: 30px;
	width: 30px;
	background: url(../images/editor.png) no-repeat transparent;
	background-position-y: 0;
	padding: 1px
}

body .ui-richtext .head .ico:hover {
	background-color: white;
	background-position-y: -60px
}

body .ui-richtext .head .ctr-size .ico {
	background-position-x: -5px
}

body .ui-richtext .head .ctr-bold .ico {
	background-position-x: -44px
}

body .ui-richtext .head .ctr-italic .ico {
	background-position-x: -81px
}

body .ui-richtext .head .ctr-line .ico {
	background-position-x: -118px
}

body .ui-richtext .head .ctr-color .ico {
	background-position-x: -158px
}

body .ui-richtext .head .ctr-link .ico {
	background-position-x: -198px
}

body .ui-richtext .head .ctr-bgcolor .ico {
	background-position-x: -238px
}

body .ui-richtext .head .ctr-list .ico {
	background-position-x: -276px
}

body .ui-richtext .head .ctr-align .ico {
	background-position-x: -316px
}

body .ui-richtext .head .ctr-para .ico {
	background-position-x: -276px
}

body .ui-richtext .head li {
	height: 32px;
	width: 32px;
	list-style: none;
	float: left;
	position: relative;
	overflow: visible;
	border: 1px solid transparent
}

body .ui-richtext .head li:hover {
	border: 1px solid #48adee
}

body .ui-richtext .head li:hover .ico {
	background-color: #fff;
	border-radius: 3px 3px 0 0;
	z-index: 9;
	padding-bottom: 1px
}

body .ui-richtext .head .ctr-pic .ico {
	background: none;
	text-decoration: none;
	color: white;
	text-shadow: 0 1px 3px #114565;
	font-size: 20px;
	line-height: 25px
}

body .ui-richtext .head li.ctr-pic:hover .ico {
	color: black;
	text-shadow: none
}

body .ui-richtext .head .dropdown {
	display: none;
	color: #fff;
	position: absolute;
	z-index: 1;
	top: 32px;
	left: -1px;
	border: 1px solid #48adee;
	-webkit-border-radius: 0 0 3px 3px;
	-moz-border-radius: 0 0 3px 3px;
	-ms-border-radius: 0 0 3px 3px;
	-o-border-radius: 0 0 3px 3px;
	border-radius: 0 0 3px 3px
}

body .ui-richtext .head .dropdown a {
	display: block;
	background: white;
	text-decoration: none;
	width: 100px;
	padding: 5px 10px;
	text-align: left;
	cursor: pointer;
	color: #333
}

body .ui-richtext .head .dropdown a:hover {
	color: #fff;
	background-color: #1e96e2;
	text-decoration: none
}

body .ui-richtext .head .dropdown a:hover em {
	color: #fff
}

body .ui-richtext .head li:hover .dropdown {
	display: block;
	margin-top: -1px
}

body .ui-richtext .head .ctr-size:hover .ico {
	background-position: -5px -60px
}

body .ui-richtext .head .ctr-size .dropdown em {
	float: right;
	font-style: normal;
	color: #999;
	font-size: 11px
}

body .ui-richtext .head .ctr-size .small {
	font-size: 14px;
	line-height: 18px
}

body .ui-richtext .head .ctr-size .middle {
	font-size: 18px;
	line-height: 22px
}

body .ui-richtext .head .ctr-size .big {
	font-size: 24px;
	line-height: 28px
}

body .ui-richtext .head .ctr-size .biger {
	font-size: 30px;
	line-height: 34px
}

body .ui-richtext .ctr-align .dropdown a {
	width: 38px;
	height: 34px;
	padding: 0;
	background: url(../images/editor.png) no-repeat 0 0 #fff
}

body .ui-richtext .ctr-align .dropdown a.left {
	background-position: -8px -176px
}

body .ui-richtext .ctr-align .dropdown a.middle {
	background-position: -47px -176px
}

body .ui-richtext .ctr-align .dropdown a.right {
	background-position: -86px -176px
}

body .ui-richtext .ctr-align .dropdown a.left:hover {
	background-position: -125px -176px
}

body .ui-richtext .ctr-align .dropdown a.middle:hover {
	background-position: -164px -176px
}

body .ui-richtext .ctr-align .dropdown a.right:hover {
	background-position: -203px -176px
}

body .ui-richtext .content {
	background-color: white
}

body .ui-richtext .content .editable {
	min-height: 150px;
	outline: none;
	word-wrap: normal;
	word-break: break-all
}

body .ui-richtext .content p {
	padding: 0;
	text-indent: 2em;
	line-height: 1.6
}

body .ui-richtext.theme-simple .editable {
	height: 100px
}

body .ui-pic-select {
	width: 400px
}

body .ui-pic-select .nocontent {
	height: 200px;
	line-height: 200px;
	font-size: 24px;
	text-align: center;
	color: #aaa
}

body .ui-pic-select .tip {
	font-size: 12px;
	color: #666
}

body .ui-pic-select .option img {
	border: 1px solid #999999
}

body .ui-pic-select .option p {
	white-space: nowrap;
	overflow: hidden;
	-ms-text-overflow: ellipsis;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis
}

body .ui-pic-select .ui-type-select ul {
	padding: 0
}

body .ui-pic-select .gallery-wrap {
	min-height: 266px
}

body .ui-pic-select .gallery-wrap .btn-con {
	display: none;
	text-align: center
}

body .ui-pic-select .upload-wrap {
	text-align: center
}

body .ui-pic-select.mode-multiple .gallery-wrap .btn-con {
	display: block
}

body .ui-linkselect {
	display: block;
	width: 320px
}

body .ui-linkselect .col-type, body .ui-linkselect .col-link {
	margin: 0 15px 0 0;
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline
}

body .ui-linkselect .col-type {
	width: 110px
}

body .ui-linkselect .col-type .select {
	width: 108px
}

body .ui-linkselect .col-type .selected {
	width: 92px
}

body .ui-linkselect .col-link {
	width: 190px
}

body .ui-linkselect .col-link .select {
	width: 188px
}

body .ui-linkselect .col-link .selected {
	width: 172px
}

body .ui-linkselect .col-link input[type='text'] {
	width: 172px
}

body .ui-linkselect .col-link .ui-placeholder {
	background-color: #fff;
	border: 1px solid #a8acad;
	width: 188px;
	height: 28px;
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline
}

body .ui-linkselect .col-link .ui-typeahead {
	width: 190px
}

body .ui-linkselect .col-link .ui-typeahead .options {
	width: 188px
}

body .ui-linkselect .col-link {
	margin: 0
}

body .ui-grouplink .col-name, body .ui-grouplink .col-op, body .ui-quicklink-list .col-name,
	body .ui-quicklink-list .col-op {
	margin: 0 10px 0 0;
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline
}

body .ui-grouplink .col-name, body .ui-quicklink-list .col-name {
	width: 80px;
	white-space: nowrap;
	overflow: hidden;
	-ms-text-overflow: ellipsis;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis
}

body .ui-grouplink .col-name input[type='text'], body .ui-quicklink-list .col-name input[type='text']
	{
	width: 62px
}

body .ui-grouplink .col-type, body .ui-grouplink .col-link, body .ui-quicklink-list .col-type,
	body .ui-quicklink-list .col-link {
	margin: 0 10px 0 0;
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline
}

body .ui-grouplink .col-type, body .ui-quicklink-list .col-type {
	width: 90px
}

body .ui-grouplink .col-type .select, body .ui-quicklink-list .col-type .select
	{
	width: 88px
}

body .ui-grouplink .col-type .selected, body .ui-quicklink-list .col-type .selected
	{
	width: 72px
}

body .ui-grouplink .col-link, body .ui-quicklink-list .col-link {
	width: 160px
}

body .ui-grouplink .col-link .select, body .ui-quicklink-list .col-link .select
	{
	width: 158px
}

body .ui-grouplink .col-link .selected, body .ui-quicklink-list .col-link .selected
	{
	width: 142px
}

body .ui-grouplink .col-link input[type='text'], body .ui-quicklink-list .col-link input[type='text']
	{
	width: 142px
}

body .ui-grouplink .col-link .ui-placeholder, body .ui-quicklink-list .col-link .ui-placeholder
	{
	background-color: #fff;
	border: 1px solid #a8acad;
	width: 158px;
	height: 28px;
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline
}

body .ui-grouplink .col-link .ui-typeahead, body .ui-quicklink-list .col-link .ui-typeahead
	{
	width: 160px
}

body .ui-grouplink .col-link .ui-typeahead .options, body .ui-quicklink-list .col-link .ui-typeahead .options
	{
	width: 158px
}

body .ui-grouplink .col-op, body .ui-quicklink-list .col-op {
	width: 50px;
	margin: 0
}

body .ui-grouplink .head label, body .ui-quicklink-list .head label {
	text-align: left
}

body .ui-grouplink .content, body .ui-quicklink-list .content {
	overflow: visible
}

body .ui-grouplink .link-item, body .ui-quicklink-list .link-item {
	position: relative
}

body .ui-grouplink .link-item .sort-handle, body .ui-quicklink-list .link-item .sort-handle
	{
	background: url(../images/ui-bg.png) no-repeat -12px -30px transparent;
	position: absolute;
	cursor: move;
	height: 30px;
	width: 7px;
	left: 0;
	top: 0.5em;
	text-decoration: none !important
}

body .ui-grouplink .link-item:hover .sort-handle, body .ui-quicklink-list .link-item:hover .sort-handle
	{
	display: block
}

body .ui-grouplink .sort-placeholder, body .ui-quicklink-list .sort-placeholder
	{
	height: 28px;
	color: #514721;
	background-color: #fffadf;
	border: 1px dashed #f9c86a;
	margin: 10px 0
}

body .ui-grouplink.st-sortable .head, body .ui-quicklink-list.st-sortable .head
	{
	padding-left: 20px;
	width: 410px
}

body .ui-grouplink.st-sortable .link-item, body .ui-quicklink-list.st-sortable .link-item
	{
	width: 410px;
	padding-left: 20px
}

body .ui-quicklink-list .col-op {
	width: 160px
}

body .ui-quicklink-list .col-op .delete {
	width: 40px;
	display: inline-block
}

body .ui-typeahead {
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline;
	position: relative;
	width: 250px
}

body .ui-typeahead .options {
	display: none;
	position: absolute;
	padding-left: 0;
	left: 0;
	top: 28px;
	width: 248px;
	background-color: #fff;
	z-index: 10;
	border: 1px solid #a8acad;
	border-top: none
}

body .ui-typeahead .options li {
	list-style: none;
	padding: 0 8px;
	line-height: 28px;
	white-space: nowrap;
	overflow: hidden;
	-ms-text-overflow: ellipsis;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis
}

body .ui-typeahead .options li:hover {
	background-color: #ededed
}

body .ui-typeahead .options .nocontent {
	text-align: center;
	padding: 0 8px;
	font-size: 12px;
	line-height: 28px;
	white-space: nowrap;
	overflow: hidden;
	-ms-text-overflow: ellipsis;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis;
	background-color: #fff6bf;
	color: #514721
}

body .ui-typeahead:hover .options {
	display: block;
	border-color: #48adee
}

body .ui-typeahead.state-focus .options {
	display: block
}

body .ui-typeahead.state-blur .options {
	display: none
}

body .ui-picedit .content {
	overflow: visible
}

body .ui-picedit.st-add-full .add {
	display: none
}

body .ui-picedit .sort-placeholder {
	height: 28px;
	color: #514721;
	background-color: #fffadf;
	border: 1px dashed #f9c86a;
	margin: 10px 0
}

body .ui-picedit .picedit-item {
	*zoom: 1;
	position: relative;
	background-color: transparent
}

body .ui-picedit .picedit-item:after {
	content: ".";
	display: block;
	visibility: hidden;
	clear: both;
	height: 0;
	font-size: 0
}

body .ui-picedit .picedit-item .sort-handle {
	background: url(../images/icon-move.png) no-repeat center center
		transparent;
	position: absolute;
	cursor: move;
	height: 100%;
	width: 30px;
	left: 0;
	top: 0;
	text-decoration: none !important
}

body .ui-picedit .picedit-item label {
	width: 60px;
	margin: 0;
	text-align: left
}

body .ui-picedit .picedit-item input.desc-text {
	width: 192px;
	display: none
}

body .ui-picedit .picedit-item .link-edit {
	display: none
}

body .ui-picedit .picedit-item.st-use-desc input.desc-text {
	display: inline-block
}

body .ui-picedit .picedit-item.st-use-link .link-edit {
	display: inline-block
}

body .ui-picedit .picedit-item .col-type, body .ui-picedit .picedit-item .col-link
	{
	margin: 0 10px 0 0;
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline
}

body .ui-picedit .picedit-item .col-type {
	width: 60px
}

body .ui-picedit .picedit-item .col-type .select {
	width: 58px
}

body .ui-picedit .picedit-item .col-type .selected {
	width: 42px
}

body .ui-picedit .picedit-item .col-link {
	width: 140px
}

body .ui-picedit .picedit-item .col-link .select {
	width: 138px
}

body .ui-picedit .picedit-item .col-link .selected {
	width: 122px
}

body .ui-picedit .picedit-item .col-link input[type='text'] {
	width: 122px
}

body .ui-picedit .picedit-item .col-link .ui-placeholder {
	background-color: #fff;
	border: 1px solid #a8acad;
	width: 138px;
	height: 28px;
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline
}

body .ui-picedit .picedit-item .col-link .ui-typeahead {
	width: 140px
}

body .ui-picedit .picedit-item .col-link .ui-typeahead .options {
	width: 138px
}

body .ui-picedit .picedit-item .col-link {
	margin: 0
}

body .ui-picedit .picedit-item .pic {
	float: left;
	width: 118px;
	height: 88px;
	overflow: hidden;
	position: relative;
	border: 1px solid #999
}

body .ui-picedit .picedit-item .delete {
	display: none;
	position: absolute;
	bottom: 0;
	right: 0;
	width: 30px;
	font-size: 12px;
	color: #fff;
	background-color: black;
	opacity: .6;
	z-index: 9;
	cursor: pointer;
	text-align: center;
	text-decoration: none
}

body .ui-picedit .picedit-item .delete:hover {
	opacity: .8
}

body .ui-picedit .picedit-item img {
	width: 118px;
	vertical-align: middle
}

body .ui-picedit .picedit-item .oper {
	float: left;
	vertical-align: top;
	margin-left: 15px;
	width: 270px
}

body .ui-picedit .picedit-item .oper-row {
	*zoom: 1;
	margin-bottom: 12px
}

body .ui-picedit .picedit-item .oper-row:after {
	content: ".";
	display: block;
	visibility: hidden;
	clear: both;
	height: 0;
	font-size: 0
}

body .ui-picedit .picedit-item:hover {
	background-color: #fff7cb
}

body .ui-picedit .picedit-item:hover .delete {
	display: block
}

body .ui-picedit.st-sortable .picedit-item {
	padding-left: 30px;
	width: 405px
}

body .ui-switchrow .form-cell {
	display: none
}

body .ui-switchrow.st-checked .form-cell {
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: middle;
	*vertical-align: auto;
	zoom: 1;
	*display: inline
}

body .ui-imgcut {
	padding: 10px;
	background: rgba(90, 90, 90, 0.95);
	z-index: 1000;
	border-radius: 3px;
	box-shadow: 0 1px 6px 1px rgba(0, 0, 0, 0.5)
}

body .ui-imgcut .image {
	max-height: 500px;
	max-width: 500px
}

body .imgareaselect-border1 {
	background: url(../images/border-anim-v.gif) repeat-y left top
}

body .imgareaselect-border2 {
	background: url(../images/border-anim-h.gif) repeat-x left top
}

body .imgareaselect-border3 {
	background: url(../images/border-anim-v.gif) repeat-y right top
}

body .imgareaselect-border4 {
	background: url(../images/border-anim-h.gif) repeat-x left bottom
}

body .imgareaselect-border1, body .imgareaselect-border2, body .imgareaselect-border3,
	body .imgareaselect-border4 {
	filter: alpha(opacity = 50);
	opacity: 0.5
}

body .imgareaselect-handle {
	background-color: #fff;
	border: solid 1px #000;
	filter: alpha(opacity = 50);
	opacity: 0.5
}

body .imgareaselect-outer {
	background-color: #000;
	filter: alpha(opacity = 50);
	opacity: 0.5
}

body .ui-table th, body .ui-table td {
	height: 30px;
	line-height: 30px;
	padding: 5px 10px
}

body .ui-table th {
	background-color: #ededed;
	font-size: 14px
}

body .ui-table td {
	font-size: 12px;
	border-bottom: 1px solid #e8e8e8
}

body .ui-table.cross-row tbody tr:nth-child(2n) {
	background-color: #e8f3fb
}

body .ui-table tr:hover th, body .ui-table tr:hover td {
	text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8)
}

body .ui-table tr:hover td {
	background-color: #d1e8f7
}

body.typography {
	line-height: 1.5;
	font-family: "\5FAE\8F6F\96C5\9ED1", "微软雅黑", "Microsoft YaHei",
		Helvetica, Tahoma, sans-serif;
	color: #333;
	font-size: 12px
}

body.typography h1, body.typography h2, body.typography h3, body.typography h4,
	body.typography h5, body.typography h6 {
	font-weight: normal;
	color: #222
}

body.typography h1 img, body.typography h2 img, body.typography h3 img,
	body.typography h4 img, body.typography h5 img, body.typography h6 img
	{
	margin: 0
}

body.typography h1 {
	font-size: 3em;
	line-height: 1;
	margin-bottom: 0.50em
}

body.typography h2 {
	font-size: 2em;
	margin-bottom: 0.75em
}

body.typography h3 {
	font-size: 1.5em;
	line-height: 1;
	margin-bottom: 1.00em
}

body.typography h4 {
	font-size: 1.2em;
	line-height: 1.25;
	margin-bottom: 1.25em
}

body.typography h5 {
	font-size: 1em;
	font-weight: bold;
	margin-bottom: 1.50em
}

body.typography h6 {
	font-size: 1em;
	font-weight: bold
}

body.typography p {
	margin: 0 0 1.5em
}

body.typography p .left {
	display: inline;
	float: left;
	margin: 1.5em 1.5em 1.5em 0;
	padding: 0
}

body.typography p .right {
	display: inline;
	float: right;
	margin: 1.5em 0 1.5em 1.5em;
	padding: 0
}

body.typography a {
	text-decoration: underline;
	color: #06c
}

body.typography a:visited {
	color: #004c99
}

body.typography a:focus {
	color: #09f
}

body.typography a:hover {
	color: #09f
}

body.typography a:active {
	color: #bf00ff
}

body.typography blockquote {
	margin: 1.5em;
	color: #666;
	font-style: italic
}

body.typography strong, body.typography dfn {
	font-weight: bold
}

body.typography em, body.typography dfn {
	font-style: italic
}

body.typography sup, body.typography sub {
	line-height: 0
}

body.typography abbr, body.typography acronym {
	border-bottom: 1px dotted #666666
}

body.typography address {
	margin: 0 0 1.5em;
	font-style: italic
}

body.typography del {
	color: #666
}

body.typography pre {
	margin: 1.5em 0;
	white-space: pre
}

body.typography pre, body.typography code, body.typography tt {
	font: 1em "andale mono", "lucida console", monospace;
	line-height: 1.5
}

body.typography li ul, body.typography li ol {
	margin: 0
}

body.typography ul, body.typography ol {
	margin: 0;
	padding: 0
}

body.typography ul {
	list-style-type: none
}

body.typography ol {
	list-style-type: none
}

body.typography dl {
	margin: 0 0 1.5em 0
}

body.typography dl dt {
	font-weight: bold
}

body.typography dd {
	margin-left: 1.5em
}

body.typography table {
	margin-bottom: 1.4em;
	width: 100%
}

body.typography th {
	font-weight: bold
}

body.typography thead th {
	background: #fff
}

body.typography th, body.typography td, body.typography caption {
	padding: 4px 10px 4px 5px
}

body.typography table.striped tr:nth-child(even) td, body.typography table tr.even td
	{
	background: #e5ecf9
}

body.typography tfoot {
	font-style: italic
}

body.typography caption {
	background: #eee
}

body.typography .quiet {
	color: #666
}

body.typography .loud {
	color: #111
}

.column, .span-1, .span-2, .span-3, .span-4, .span-5, .span-6, .span-7,
	.span-8, .span-9, .span-10, .span-11, .span-12, .span-13, .span-14,
	.span-15, .span-16, .span-17, .span-18, .span-19, .span-20, .span-21,
	.span-22, .span-23, .span-24, .span-25, .span-26, .span-27, .span-28,
	.span-29, .span-30, .span-31, .span-32 {
	display: inline;
	float: left;
	margin-right: 10px
}

* html .column, * html .span-1, * html .span-2, * html .span-3, * html .span-4,
	* html .span-5, * html .span-6, * html .span-7, * html .span-8, * html .span-9,
	* html .span-10, * html .span-11, * html .span-12, * html .span-13, * html .span-14,
	* html .span-15, * html .span-16, * html .span-17, * html .span-18, * html .span-19,
	* html .span-20, * html .span-21, * html .span-22, * html .span-23, * html .span-24,
	* html .span-25, * html .span-26, * html .span-27, * html .span-28, * html .span-29,
	* html .span-30, * html .span-31, * html .span-32 {
	overflow-x: hidden
}

.last {
	margin-right: 0
}

.span-1 {
	width: 20px
}

.span-2 {
	width: 50px
}

.span-3 {
	width: 80px
}

.span-4 {
	width: 110px
}

.span-5 {
	width: 140px
}

.span-6 {
	width: 170px
}

.span-7 {
	width: 200px
}

.span-8 {
	width: 230px
}

.span-9 {
	width: 260px
}

.span-10 {
	width: 290px
}

.span-11 {
	width: 320px
}

.span-12 {
	width: 350px
}

.span-13 {
	width: 380px
}

.span-14 {
	width: 410px
}

.span-15 {
	width: 440px
}

.span-16 {
	width: 470px
}

.span-17 {
	width: 500px
}

.span-18 {
	width: 530px
}

.span-19 {
	width: 560px
}

.span-20 {
	width: 590px
}

.span-21 {
	width: 620px
}

.span-22 {
	width: 650px
}

.span-23 {
	width: 680px
}

.span-24 {
	width: 710px
}

.span-25 {
	width: 740px
}

.span-26 {
	width: 770px
}

.span-27 {
	width: 800px
}

.span-28 {
	width: 830px
}

.span-29 {
	width: 860px
}

.span-30 {
	width: 890px
}

.span-31 {
	width: 920px
}

.span-32 {
	width: 950px;
	margin: 0
}

input.span-1, textarea.span-1, select.span-1 {
	width: 20px
}

input.span-2, textarea.span-2, select.span-2 {
	width: 50px
}

input.span-3, textarea.span-3, select.span-3 {
	width: 80px
}

input.span-4, textarea.span-4, select.span-4 {
	width: 110px
}

input.span-5, textarea.span-5, select.span-5 {
	width: 140px
}

input.span-6, textarea.span-6, select.span-6 {
	width: 170px
}

input.span-7, textarea.span-7, select.span-7 {
	width: 200px
}

input.span-8, textarea.span-8, select.span-8 {
	width: 230px
}

input.span-9, textarea.span-9, select.span-9 {
	width: 260px
}

input.span-10, textarea.span-10, select.span-10 {
	width: 290px
}

input.span-11, textarea.span-11, select.span-11 {
	width: 320px
}

input.span-12, textarea.span-12, select.span-12 {
	width: 350px
}

input.span-13, textarea.span-13, select.span-13 {
	width: 380px
}

input.span-14, textarea.span-14, select.span-14 {
	width: 410px
}

input.span-15, textarea.span-15, select.span-15 {
	width: 440px
}

input.span-16, textarea.span-16, select.span-16 {
	width: 470px
}

input.span-17, textarea.span-17, select.span-17 {
	width: 500px
}

input.span-18, textarea.span-18, select.span-18 {
	width: 530px
}

input.span-19, textarea.span-19, select.span-19 {
	width: 560px
}

input.span-20, textarea.span-20, select.span-20 {
	width: 590px
}

input.span-21, textarea.span-21, select.span-21 {
	width: 620px
}

input.span-22, textarea.span-22, select.span-22 {
	width: 650px
}

input.span-23, textarea.span-23, select.span-23 {
	width: 680px
}

input.span-24, textarea.span-24, select.span-24 {
	width: 710px
}

input.span-25, textarea.span-25, select.span-25 {
	width: 740px
}

input.span-26, textarea.span-26, select.span-26 {
	width: 770px
}

input.span-27, textarea.span-27, select.span-27 {
	width: 800px
}

input.span-28, textarea.span-28, select.span-28 {
	width: 830px
}

input.span-29, textarea.span-29, select.span-29 {
	width: 860px
}

input.span-30, textarea.span-30, select.span-30 {
	width: 890px
}

input.span-31, textarea.span-31, select.span-31 {
	width: 920px
}

input.span-32, textarea.span-32, select.span-32 {
	width: 950px
}

.append-1 {
	padding-right: 30px
}

.append-2 {
	padding-right: 60px
}

.append-3 {
	padding-right: 90px
}

.append-4 {
	padding-right: 120px
}

.append-5 {
	padding-right: 150px
}

.append-6 {
	padding-right: 180px
}

.append-7 {
	padding-right: 210px
}

.append-8 {
	padding-right: 240px
}

.append-9 {
	padding-right: 270px
}

.append-10 {
	padding-right: 300px
}

.append-11 {
	padding-right: 330px
}

.append-12 {
	padding-right: 360px
}

.append-13 {
	padding-right: 390px
}

.append-14 {
	padding-right: 420px
}

.append-15 {
	padding-right: 450px
}

.append-16 {
	padding-right: 480px
}

.append-17 {
	padding-right: 510px
}

.append-18 {
	padding-right: 540px
}

.append-19 {
	padding-right: 570px
}

.append-20 {
	padding-right: 600px
}

.append-21 {
	padding-right: 630px
}

.append-22 {
	padding-right: 660px
}

.append-23 {
	padding-right: 690px
}

.append-24 {
	padding-right: 720px
}

.append-25 {
	padding-right: 750px
}

.append-26 {
	padding-right: 780px
}

.append-27 {
	padding-right: 810px
}

.append-28 {
	padding-right: 840px
}

.append-29 {
	padding-right: 870px
}

.append-30 {
	padding-right: 900px
}

.append-31 {
	padding-right: 930px
}

.prepend-1 {
	padding-left: 30px
}

.prepend-2 {
	padding-left: 60px
}

.prepend-3 {
	padding-left: 90px
}

.prepend-4 {
	padding-left: 120px
}

.prepend-5 {
	padding-left: 150px
}

.prepend-6 {
	padding-left: 180px
}

.prepend-7 {
	padding-left: 210px
}

.prepend-8 {
	padding-left: 240px
}

.prepend-9 {
	padding-left: 270px
}

.prepend-10 {
	padding-left: 300px
}

.prepend-11 {
	padding-left: 330px
}

.prepend-12 {
	padding-left: 360px
}

.prepend-13 {
	padding-left: 390px
}

.prepend-14 {
	padding-left: 420px
}

.prepend-15 {
	padding-left: 450px
}

.prepend-16 {
	padding-left: 480px
}

.prepend-17 {
	padding-left: 510px
}

.prepend-18 {
	padding-left: 540px
}

.prepend-19 {
	padding-left: 570px
}

.prepend-20 {
	padding-left: 600px
}

.prepend-21 {
	padding-left: 630px
}

.prepend-22 {
	padding-left: 660px
}

.prepend-23 {
	padding-left: 690px
}

.prepend-24 {
	padding-left: 720px
}

.prepend-25 {
	padding-left: 750px
}

.prepend-26 {
	padding-left: 780px
}

.prepend-27 {
	padding-left: 810px
}

.prepend-28 {
	padding-left: 840px
}

.prepend-29 {
	padding-left: 870px
}

.prepend-30 {
	padding-left: 900px
}

.prepend-31 {
	padding-left: 930px
}

.pull-1, .pull-2, .pull-3, .pull-4, .pull-5, .pull-6, .pull-7, .pull-8,
	.pull-9, .pull-10, .pull-11, .pull-12, .pull-13, .pull-14, .pull-15,
	.pull-16, .pull-17, .pull-18, .pull-19, .pull-20, .pull-21, .pull-22,
	.pull-23, .pull-24, .pull-25, .pull-26, .pull-27, .pull-28, .pull-29,
	.pull-30, .pull-31, .pull-32 {
	display: inline;
	float: left;
	position: relative
}

.pull-1 {
	margin-left: -30px
}

.pull-2 {
	margin-left: -60px
}

.pull-3 {
	margin-left: -90px
}

.pull-4 {
	margin-left: -120px
}

.pull-5 {
	margin-left: -150px
}

.pull-6 {
	margin-left: -180px
}

.pull-7 {
	margin-left: -210px
}

.pull-8 {
	margin-left: -240px
}

.pull-9 {
	margin-left: -270px
}

.pull-10 {
	margin-left: -300px
}

.pull-11 {
	margin-left: -330px
}

.pull-12 {
	margin-left: -360px
}

.pull-13 {
	margin-left: -390px
}

.pull-14 {
	margin-left: -420px
}

.pull-15 {
	margin-left: -450px
}

.pull-16 {
	margin-left: -480px
}

.pull-17 {
	margin-left: -510px
}

.pull-18 {
	margin-left: -540px
}

.pull-19 {
	margin-left: -570px
}

.pull-20 {
	margin-left: -600px
}

.pull-21 {
	margin-left: -630px
}

.pull-22 {
	margin-left: -660px
}

.pull-23 {
	margin-left: -690px
}

.pull-24 {
	margin-left: -720px
}

.pull-25 {
	margin-left: -750px
}

.pull-26 {
	margin-left: -780px
}

.pull-27 {
	margin-left: -810px
}

.pull-28 {
	margin-left: -840px
}

.pull-29 {
	margin-left: -870px
}

.pull-30 {
	margin-left: -900px
}

.pull-31 {
	margin-left: -930px
}

.pull-32 {
	margin-left: -960px
}

.push-1, .push-2, .push-3, .push-4, .push-5, .push-6, .push-7, .push-8,
	.push-9, .push-10, .push-11, .push-12, .push-13, .push-14, .push-15,
	.push-16, .push-17, .push-18, .push-19, .push-20, .push-21, .push-22,
	.push-23, .push-24, .push-25, .push-26, .push-27, .push-28, .push-29,
	.push-30, .push-31, .push-32 {
	display: inline;
	float: left;
	position: relative
}

.push-1 {
	margin: 0 -30px 1.5em 30px
}

.push-2 {
	margin: 0 -60px 1.5em 60px
}

.push-3 {
	margin: 0 -90px 1.5em 90px
}

.push-4 {
	margin: 0 -120px 1.5em 120px
}

.push-5 {
	margin: 0 -150px 1.5em 150px
}

.push-6 {
	margin: 0 -180px 1.5em 180px
}

.push-7 {
	margin: 0 -210px 1.5em 210px
}

.push-8 {
	margin: 0 -240px 1.5em 240px
}

.push-9 {
	margin: 0 -270px 1.5em 270px
}

.push-10 {
	margin: 0 -300px 1.5em 300px
}

.push-11 {
	margin: 0 -330px 1.5em 330px
}

.push-12 {
	margin: 0 -360px 1.5em 360px
}

.push-13 {
	margin: 0 -390px 1.5em 390px
}

.push-14 {
	margin: 0 -420px 1.5em 420px
}

.push-15 {
	margin: 0 -450px 1.5em 450px
}

.push-16 {
	margin: 0 -480px 1.5em 480px
}

.push-17 {
	margin: 0 -510px 1.5em 510px
}

.push-18 {
	margin: 0 -540px 1.5em 540px
}

.push-19 {
	margin: 0 -570px 1.5em 570px
}

.push-20 {
	margin: 0 -600px 1.5em 600px
}

.push-21 {
	margin: 0 -630px 1.5em 630px
}

.push-22 {
	margin: 0 -660px 1.5em 660px
}

.push-23 {
	margin: 0 -690px 1.5em 690px
}

.push-24 {
	margin: 0 -720px 1.5em 720px
}

.push-25 {
	margin: 0 -750px 1.5em 750px
}

.push-26 {
	margin: 0 -780px 1.5em 780px
}

.push-27 {
	margin: 0 -810px 1.5em 810px
}

.push-28 {
	margin: 0 -840px 1.5em 840px
}

.push-29 {
	margin: 0 -870px 1.5em 870px
}

.push-30 {
	margin: 0 -900px 1.5em 900px
}

.push-31 {
	margin: 0 -930px 1.5em 930px
}

.push-32 {
	margin: 0 -960px 1.5em 960px
}

.prepend-top {
	margin-top: 1.5em
}

.append-bottom {
	margin-bottom: 1.5em
}

.clearfix {
	*zoom: 1
}

.clearfix:after {
	content: ".";
	display: block;
	visibility: hidden;
	clear: both;
	height: 0;
	font-size: 0
}

body hr {
	clear: both
}

body.one-col #container {
	width: 950px;
	margin: 0 auto;
	overflow: hidden;
	*zoom: 1;
	overflow: hidden;
	*zoom: 1
}

body.one-col #header, body.one-col #footer {
	display: inline;
	float: left;
	margin-right: 10px;
	width: 950px
}

* html body.one-col #header, * html body.one-col #footer {
	overflow-x: hidden
}

body.one-col #sidebar {
	display: inline;
	float: left;
	margin-right: 10px;
	width: 290px
}

* html body.one-col #sidebar {
	overflow-x: hidden
}

body.one-col #content {
	display: inline;
	float: left;
	margin-right: 0;
	width: 650px
}

* html body.one-col #content {
	overflow-x: hidden
}

body.two-col #container {
	width: 950px;
	margin: 0 auto;
	overflow: hidden;
	*zoom: 1;
	overflow: hidden;
	*zoom: 1
}

body.two-col #header, body.two-col #footer {
	display: inline;
	float: left;
	margin-right: 10px;
	width: 950px
}

* html body.two-col #header, * html body.two-col #footer {
	overflow-x: hidden
}

body.two-col #sidebar {
	display: inline;
	float: left;
	margin-right: 10px;
	width: 290px
}

* html body.two-col #sidebar {
	overflow-x: hidden
}

body.two-col #content {
	display: inline;
	float: left;
	margin-right: 0;
	width: 650px
}

* html body.two-col #content {
	overflow-x: hidden
}