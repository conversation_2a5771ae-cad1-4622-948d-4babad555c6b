<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <#include "/common/include.html">
    <style type="text/css">
        #task-list td {
            vertical-align: middle;
        }
        #task-list thead>tr>th {
            vertical-align: middle;
        }
        #fixedDiv thead>tr>th {
            vertical-align: middle;
        }
        .input-group-addon {
            border-color: #e5e5e5;
            background: #e5e5e5;
        }
        .col-md-2>.form-control{
            width: 180px;
        }
        #creader{
            margin-left: 100px;
        }
    </style>
</head>
<body>
<@header method="header" active="16010000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li>
                    <a href="#">优选仓</a>
                </li>
                <li class="active">优选仓销退入库单</li>
            </ul>
        </div>
    </div>

    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">

        <div class="row">
            <div class="col-md-12 col-new-wms-8">
                <#assign query=domain.query>
                <form action="${CONTEXT_PATH}pacStockInOrder/search"
                      class="form-horizontal form-bordered form-row-stripped"
                      method="post" modelAttribute="domain" name="pacStockInOrderForm" id="domain">
                    <!-- 分页信息 -->
                    <input id="page-no" type="hidden" name="page.pageNo" value="1">
                    <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
                    <input id="order_type" type="hidden" name="query.orderType" value="501">
                    <div class="form-body">
                        <div class="form-group">
                            <label class="control-label col-md-1" style="width: 30px">SKU</label>
                            <div class="col-md-2" style="width: 220px">
                                <input class="form-control" type="text" name="query.sku" placeholder="多个查询请以英文逗号分开" value="${query.sku }">
                            </div>
                            <label class="control-label col-md-1" style="width:70px">入库单号</label>
                            <div class="col-md-2" style="width: 220px">
                                <input class="form-control" type="text" name="query.orderCode" placeholder="多个查询请以英文逗号分开" value="${query.orderCode }">
                            </div>

                            <label class="control-label col-md-1" style="width: 90px">入库单号状态</label>
                            <div class="col-md-2">
                                <input class="form-control" type="text" name="query.status" value="${query.status}">
                            </div>

                            <label class="control-label col-md-2" style="width:65px" id="creader">创建时间</label>
                            <div class="col-md-3 input-group type-label">
                                <input class="form-control Wdate" type="text" name="query.startTime" placeholder="" readonly="readonly" value="${query.startTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                <label class="input-group-addon" style="width:25px ">到</label>
                                <input class="form-control Wdate" type="text" name="query.endTime" placeholder="" readonly="readonly" value="${query.endTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                            </div>
                        </div>
                        <div class="col-md-offset-10" style="text-align: right">
                            <@header method="auth" authCode="PRIORITY_WAREHOUSE_RETURN_INVENTORY_DOWNLOAD">
                            <button type="button" class="btn btn-default" onclick="downloadRecord()">
                                <i class="icon-download"></i> 导出
                            </button>
                            </@header>
                            <button type="button" class="btn default" onclick="formReset(this)">
                                <i class="icon-refresh"></i> 重置
                            </button>
                            <button type="submit" class="btn blue">
                                <i class="icon-search"></i> 查询
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <br/>
        </div>

        <div class="row">
            <div id="fixedDiv" class="col-md-12">
                <table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
                    <colgroup>
                        <col width="4%" />
                        <col width="10%" />
                        <col width="10%" />
                        <col width="8%" />
                        <col width="8%" />
                        <col width="10%" />
                        <col width="12%" />
                        <col width="8%" />
                        <col width="10%" />
                        <col width="10%" />
                        <col width="10%" />
                    </colgroup>
                    <thead>
                        <tr>
                            <th><label class="checkbox-inline"><input type="checkbox" name="checkAll"> 全选</label></th>
                            <th>入库单号</th>
                            <th>原发货单号</th>
                            <th>SKU</th>
                            <th>入库单数量</th>
                            <th>上架数量</th>
                            <th>SKU标题</th>
                            <th>入库单状态</th>
                            <th>创建时间</th>
                            <th>同步时间</th>
                            <th>上架时间</th>
                        </tr>
                    </thead>
                </table>
            </div>
            <div class="col-md-12" id="task-list-warp">
                <table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                    <colgroup>
                        <col width="4%" />
                        <col width="10%" />
                        <col width="10%" />
                        <col width="8%" />
                        <col width="8%" />
                        <col width="10%" />
                        <col width="12%" />
                        <col width="8%" />
                        <col width="10%" />
                        <col width="10%" />
                        <col width="10%" />
                    </colgroup>
                    <thead>
                        <tr>
                            <th><label class="checkbox-inline"><input type="checkbox" name="checkAll"> 全选</label></th>
                            <th>入库单号</th>
                            <th>原发货单号</th>
                            <th>SKU</th>
                            <th>入库单数量</th>
                            <th>上架数量</th>
                            <th>SKU标题</th>
                            <th>入库单状态</th>
                            <th>创建时间</th>
                            <th>同步时间</th>
                            <th>上架时间</th>
                        </tr>
                    </thead>
                    <tbody>
                    <#list domain.pacStockInOrders as order>
                        <#if (order.orderItemList)!>
                            <#list order.orderItemList as orderItem>
                            <tr>
                                <#if orderItem_index==0>
                                <td rowspan="${order.orderItemList ? size}">
                                    <label class="checkbox-inline">
                                        <input class="${order.id}" name="ids" type="checkbox" value="${order.id}">
                                    </label>
                                </td>
                                <td rowspan="${order.orderItemList ? size}">${order.orderCode}</td>
                                <td rowspan="${order.orderItemList ? size}">${order.originalApvNo}</td>
                            </#if>


                            <td rowspan="1">${orderItem.itemCode}</td>
                            <td rowspan="1">${orderItem.itemQuantity}</td>
                            <td rowspan="1">${orderItem.itemUpQuantity}</td>
                            <td rowspan="1">${orderItem.itemName}</td>

                            <#if orderItem_index==0>
                            <td rowspan="${order.orderItemList ? size}"> ${util('enumName', 'com.estone.pac.enums.PacStockInOrderStatus', order.status)}</td>

                            <td rowspan="${order.orderItemList ? size}">${order.orderCreateTime}</td>
                            <td rowspan="${order.orderItemList ? size}">${order.createTime}</td>
                            <td rowspan="${order.orderItemList ? size}">${order.upDate}</td>
                            </#if>
                            </tr>
                             </#list>
                        <#else>
                        <tr>
                            <td>
                                <label class="checkbox-inline">
                                    <input class="${order.id}" name="ids" type="checkbox" value="${order.id}">
                                </label>
                            </td>
                            <td>${order.orderCode}</td>
                            <td>${order.originalApvNo}</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>

                            <td> ${util('enumName', 'com.estone.pac.enums.PacStockInOrderStatus', order.status)}</td>

                            <td>${order.orderCreateTime}</td>
                            <td>${order.createTime}</td>
                            <td>${order.upDate}</td>
                        </tr>
                    </#if>
                </#list>
                </table>
            </div>
        </div>

        <div id="fixed-bottom" >
            <div id="pager"><ul class="pages"><li class="select"><select><option>10</option><option>30</option><option>50</option><option>100</option><option>300</option><option>500</option><option>1000</option></select></li><li class="pgEmpty">首页</li><li class="pgEmpty">« 上一页</li><li class="pgCurrent">1</li><li class="page-number">2</li><li class="page-number">3</li><li class="page-number">4</li><li class="page-number">5</li><li class="page-number">6</li><li class="page-number">7</li><li class="page-number">下一页 »</li><li class="page-number">末页</li><li class="count"><span>页1/16 </span><input size="3"></li><li class="go page-number">Go</li><li>共 155 条记录</li></ul></div>
        </div>

    </div>

    <#include "/common/footer.html">
    <img id="enlarge" style='position:absolute;width:800px;height:800px;top:10%;left:15%;display:none;'/>
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/access.js?v=${.now?datetime}"></script>
<script type="text/javascript">

    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";

    $("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });
    var heights = $("body").height();
    if(heights > 910) {
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }

    // 全选
    var checkAll = $("input[name='checkAll']");
    // 子选项
    var itemIds = $("input[name='ids']");
    checkAll.change(
        function() {
            itemIds.prop("checked", $(this).prop("checked"));
            itemIds.each(function() {
                var f = $(this).is(":checked");
                var checkClass = $(this).prop("class");
                $("." + checkClass).each(function() {
                    $(this).prop("checked", f);
                })
            })
        }
    );



    // 获取选中的记录
    function getCheckedIds() {
        var checkedIds = $("input[name='ids']:checked");
        return checkedIds;
    }

    // 状态
    var resultJson = ${domain.statusJson};
    $("input[name='query.status']").select2({
        data : resultJson,
        placeholder : "状态",
        allowClear : true
        //multiple: true
    });


    // 导出
    function downloadRecord() {
        var checkedDatas = getCheckedIds();
        if(checkedDatas.length == 0) {
            var param = $("#domain").serialize();

            window.open(CONTEXT_PATH + "pacStockInOrder/download?" + param);
        } else {
            var ids = checkedDatas.serialize();
            window.open(CONTEXT_PATH + "pacStockInOrder/download?" + ids);
        }
    }
</script>
</body>
</html>