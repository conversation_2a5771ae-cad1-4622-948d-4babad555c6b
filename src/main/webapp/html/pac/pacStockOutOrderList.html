<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
<@header method="header" active="16030000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li>
						<a href="#">优选仓</a>
					</li>
					<li class="active">优选仓退仓单</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
			<#assign query=domain.query>
			<!-- END PAGE HEADER-->
			<div class="row">
				<div class="col-md-12">
					<form action="${CONTEXT_PATH}pacStockOutOrder/search"
						class="form-horizontal form-bordered form-row-stripped" enctype="multipart/form-data"
						method="post" modelAttribute="domain" name="pacStockOutOrder" id="domain">
						<!-- 分页信息 -->
						<input id="page-no" type="hidden" name="page.pageNo" value="1">
						<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
						<div class="form-body">
							<div class="form-group">
								<label class="control-label col-md-1" style="width: 160px">SKU</label>
								<div class="col-md-3" style="width: 220px">
									<input class="form-control" type="text" name="query.skuSplit" placeholder="多个查询请以英文逗号分开" value="${query.skuSplit }">
								</div>

								<label class="control-label col-md-1" style="width: 210px">退仓单单号</label>
								<div class="col-md-3" style="width: 220px">
									<input class="form-control" type="text" name="query.orderCodeSplit" placeholder="多个查询请以英文逗号分开" value="${query.orderCodeSplit }">
								</div>

								<label class="control-label col-md-1" style="width: 210px">接收时间</label>
								<div class="col-md-3">
									<div class="input-group">
										<input class="form-control Wdate" type="text" placeholder="" readonly="readonly" id="fromCreateTime" name="query.fromCreateTime" value="${query.fromCreateTime}" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
										<span class="input-group-addon">到</span>
										<input class="form-control Wdate" type="text" placeholder="" readonly="readonly" id=toCreateTime"  name="query.toCreateTime" value="${query.toCreateTime}" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
								</div>



							</div>
							<div class="form-group">
								<label class="control-label col-md-1" style="width: 160px">商品ID</label>
								<div class="col-md-3" style="width: 220px">
									<input class="form-control" name="query.itemIdSplit" type="text" value="${query.itemIdSplit}" placeholder="多个查询请以英文逗号分开">
								</div>
								<label class="control-label col-md-1" style="width: 210px">退仓单状态</label>
								<div class="col-md-3" style="width: 220px">
									<input class="form-control" type="text" name="query.status" value="${query.status}" placeholder="">
								</div>
								<label class="control-label col-md-1" style="width: 210px">完结时间</label>
								<div class="col-md-3">
									<div class="input-group">
										<input class="form-control Wdate" type="text" placeholder="" readonly="readonly" id="fromUpDate" name="query.fromUpDate" value="${domain.query.fromUpDate}" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
										<span class="input-group-addon">到</span>
										<input class="form-control Wdate" type="text" placeholder="" readonly="readonly" id=toUpDate"  name="query.toUpDate" value="${domain.query.toUpDate}" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
								</div>

							</div>
						</div>
						<div>
							<br />
                            <@header method="auth" authCode="PRIORITY_WAREHOUSE_RETURN_ORDER_DOWNLOAD">
							<div class="pull-left" style="margin-left: 10px;">
								<button type="button" class="btn btn-default" style="width:80px;" onclick="download()">
									<i class="icon-download"></i> 导出
								</button>
							</div>
                            </@header>
							<div class="col-md-offset-10" style="text-align: right">
								<button type="button" class="btn btn-default" onclick="formReset(this)">
									<i class="icon-refresh"></i> 重置
								</button>
								<button type="submit" class="btn blue">
									<i class="icon-search"></i> 查询
								</button>
							</div>
						</div>
					</form>
				</div>
				<br/>
			</div>

			<div class="row">
				<div id="fixedDiv" class="col-md-12">
					<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
						<colgroup>
							<col width="5%" />
							<col width="13%" />
							<col width="10%" />
							<col width="8%" />
							<col width="8%" />
							<col width="8%" />
							<col width="8%" />
							<col width="12%" />
							<col width="8%" />
							<col width="12%" />
							<col width="10%" />
						</colgroup>
						<thead>
						<tr>
							<th><label class="checkbox-inline"><input type="checkbox" name="checkAll"> 全选</label></th>
							<th>退仓单物流单号</th>
							<th>货主ID</th>
							<th>ITEM数量</th>
							<th>SKU种类</th>
							<th>总退仓数量</th>
							<th>实际退仓数量</th>
							<th>接收时间</th>
							<th>退仓单状态</th>
							<th>完结时间</th>
							<th>操作</th>
						</tr>
						</thead>
					</table>
				</div>
				<div class="col-md-12" id="task-list-warp">
					<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
						<colgroup>
							<col width="5%" />
							<col width="13%" />
							<col width="10%" />
							<col width="8%" />
							<col width="8%" />
							<col width="8%" />
							<col width="8%" />
							<col width="12%" />
							<col width="8%" />
							<col width="12%" />
							<col width="10%" />
						</colgroup>
						<thead>
						<tr>
							<th><label class="checkbox-inline"><input type="checkbox" name="checkAll"> 全选</label></th>
							<th>退仓单物流单号</th>
							<th>货主ID</th>
							<th>ITEM数量</th>
							<th>SKU种类</th>
							<th>总退仓数量</th>
							<th>实际退仓数量</th>
							<th>接收时间</th>
							<th>退仓单状态</th>
							<th>完结时间</th>
							<th>操作</th>
						</tr>
						</thead>
						<tbody>
							<#list domain.pacStockOutOrders as outOrder>
								<tr>
									<td>
										<label class="checkbox-inline"><input type="checkbox" value="${outOrder.id}" name="ids" /> ${outOrder.id}</label>
									</td>
									<td>${outOrder.orderCode}</td>
									<td>${outOrder.ownerUserId}</td>
									<td>${outOrder.totalOrderItemCount}</td>
									<td>${outOrder.skuQuantity}</td>
									<td>${outOrder.outQuantitySum}</td>
									<td>${outOrder.actualOutQuantity}</td>
									<td>${outOrder.createTime}</td>
									<td> ${util('enumName', 'com.estone.pac.enums.PacStockOutOrderStatus', outOrder.status)}</td>
									<td>${outOrder.upDate}</td>
									<td>
										<a class="btn btn-xs btn-default" href="${CONTEXT_PATH}pacStockOutOrder/outOrderItems?id=${outOrder.id }">
											详情
										</a>
									</td>
								</tr>
							</#list>
						</tbody>
					</table>
				</div>
			</div>
			<div id="fixed-bottom">
				<div id="pager"></div>
			</div>
		</div>
		<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
	<script type="text/javascript">
		// 分页
		var total = "${domain.page.totalCount}";
		var pageNo = "${domain.page.pageNo}";
		var pageSize = "${domain.page.pageSize}";
		$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

        var heights = $("body").height();
        if(heights>910){
            $("#fixed-bottom").addClass("fixed-bottom-height")
        }

        // 返架员
    	$.getJSON(CONTEXT_PATH + "system/saleusers/userByRole?roleId=", function(json){
    		if (json) {
    			$("input[name='query.returnUser']").select2({
    				data : json,
    				placeholder : "返架员",
    				allowClear : true
    			});
    		} else {
    			$("input[name='query.returnUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
    		}
    	});

		function getCheckedRerutnIds() {
			var checkedRerutnIds = $("input[name='ids']:checked");
			return checkedRerutnIds;
		}
		// 导出
		function download() {
			var checkedDatas = getCheckedRerutnIds();
			if(checkedDatas.length == 0) {
				var param = $("#domain").serialize();

				window.open(CONTEXT_PATH + "pacStockOutOrder/download?" + param);
			} else {
				var ids = checkedDatas.serialize();
				window.open(CONTEXT_PATH + "pacStockOutOrder/download?" + ids);
			}
		}


		function reloadPage(){
			  location.reload()
		}

		// 状态
		var resultJson = ${domain.statusJson};
		$("input[name='query.status']").select2({
			data : resultJson,
			placeholder : "状态",
			allowClear : true
			//multiple: true
		});

		// 全选
		var checkAll = $("input[name='checkAll']");
		// 子选项
		var itemIds = $("input[name='ids']");
		checkAll.change(
				function() {
					itemIds.prop("checked", $(this).prop("checked"));
					itemIds.each(function() {
						var f = $(this).is(":checked");
						var checkClass = $(this).prop("class");
						$("." + checkClass).each(function() {
							$(this).prop("checked", f);
						})
					})
				}
		);
	</script>
</body>
</html>