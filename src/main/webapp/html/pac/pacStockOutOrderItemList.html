<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <#include "/common/include.html">
    <style type="text/css">
        .modal-body input {
            width: 240px;
        }

        #task-list td {
            vertical-align:middle;
        }
        #order_code{
            font-size: 16px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
<@header method="header" active="16030000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li><a href="#">优选仓</a></li>
                <li class="active"><a href="${CONTEXT_PATH}pacStockOutOrder/create">优选仓退仓单</a></li>
                <li class="active" >优选仓退仓单详情</li>
            </ul>
        </div>
    </div>
    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">
        <#assign query=domain.query>
        <!-- END PAGE HEADER-->
        <div class="row">
            <br>
            <div  id="order_code">
                <table>
                    <tr>
                        <td>退仓单物流单号 :</td>
                        <td style="width:200px;">${ domain.pacStockOutOrder.orderCode}</td>
                    </tr>
                </table>
            </div>
            <form action="${CONTEXT_PATH}pacStockOutOrder/searchItem"
                  class="form-horizontal form-bordered form-row-stripped" enctype="multipart/form-data"
                  method="post" modelAttribute="domain" name="pacStockOutOrder" id="domain">
                <input type="hidden" name="query.id" value="${ domain.pacStockOutOrder.id}">
                <input type="hidden" name="query.orderCode" value="${ domain.pacStockOutOrder.orderCode}">
                <div class="form-body">
                    <div class="form-group">
                        <label class="control-label col-md-1" style="width: 120px">SKU</label>
                        <div class="col-md-3" style="width: 220px">
                            <input class="form-control" type="text" name="query.skuSplit" placeholder="多个查询请以英文逗号分开" value="${query.skuSplit }">
                        </div>
                        <label class="control-label col-md-1" style="width: 210px">退仓单状态</label>
                        <div class="col-md-3" style="width: 220px">
                            <input class="form-control" type="text" name="query.status" value="${query.status}">
                        </div>
                        <label class="control-label col-md-1" style="width: 210px">商品ID</label>
                        <div class="col-md-3" style="width: 220px">
                            <input class="form-control" name="query.itemIdSplit" type="text" value="${query.itemIdSplit}" placeholder="多个查询请以英文逗号分开">
                        </div>

                        <div class="col-md-offset-10" style="text-align: right">
                            <button type="button" class="btn btn-default" onclick="formReset(this)">
                                <i class="icon-refresh"></i> 重置
                            </button>
                            <button type="submit" class="btn blue">
                                <i class="icon-search"></i> 查询
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div>
                    <table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                        <colgroup>
                            <col width="12%" />
                            <col width="13%" />
                            <col width="12%" />
                            <col width="13%" />
                            <col width="12%" />
                            <col width="13%" />
                            <col width="12%" />
                        </colgroup>
                        <thead>
                        <tr>
                            <th>sku</th>
                            <th>商品ID</th>
                            <th>退仓数量</th>
                            <th>实退数量</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>退仓时间</th>
                        </tr>
                        </thead>
                        <tbody>
                        <#list domain.pacStockOutOrder.orderItemList as orderItem>
                            <tr>
                                <td>${orderItem.itemCode}</td>
                                <td>${orderItem.itemId}</td>
                                <td>${orderItem.itemQuantity}</td>
                                <td>${orderItem.itemUpQuantity}</td>
                                    <!--状态-->
                                <td> ${util('enumName', 'com.estone.pac.enums.PacStockOutOrderStatus', orderItem.status)}</td>
                                <td>${orderItem.createTime}</td>
                                <td>${orderItem.updateTime}</td>
                            </tr>
                        </#list>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript">
    // 状态
    var resultJson = ${domain.statusJson};
    $("input[name='query.status']").select2({
        data : resultJson,
        placeholder : "状态",
        allowClear : true
        //multiple: true
    });

</script>
</body>
</html>