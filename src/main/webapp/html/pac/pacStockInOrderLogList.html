<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		.top_bar{
			position:fixed;top:0px;
		}
		#task-list td {
			vertical-align:middle;
		}
	</style>
</head>
<body>
<@header method="header" active="16020000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">优选仓管理</a></li>
				<li class="active">优选仓库存日志</li>
			</ul>
		</div>
	</div>

	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">

		<#assign query=domain.query>
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="col-md-12">
				<form action="${CONTEXT_PATH}pacStockLog/search"
					  class="form-horizontal form-bordered form-row-stripped"
					  method="post" modelAttribute="domain" name="pacStockLogFrom" id="domain">
					<!-- 分页信息 -->
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
					<div class="form-body">
						<div class="form-group">
                            <label class="control-label col-md-1">货主ID</label>
                            <div class="col-md-2">
                                <input class="form-control" name="query.ownerUserId" type="text" value="${query.ownerUserId}" id="货主ID">
                            </div>

							<label class="control-label col-md-1">SKU</label>
							<div class="col-md-2">
								<input class="form-control" name="query.sku" type="text" value="${query.sku}" id="sku">
							</div>
							<label class="control-label col-md-1">库存类型</label>
							<div class="col-md-2">
								<input class="form-control" name="query.type" type="text" value="${query.type}">
							</div>


							<label class="control-label col-md-1">创建人</label>
							<div class="col-md-2" >
								<input class="form-control" type="text" name="query.createBy" value="${query.createBy }">
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1">改变值</label>
							<div class="col-md-2">
								<select name="query.change" class="form-control" value="${query.change}">
									<option value="" <#if (query.change)??>selected</#if>>所有</option>
									<option value="true" <#if query.change == true>selected</#if>>调增</option>
									<option value="false" <#if (query.change)?? && query.change == false>selected</#if>>调减</option>
								</select>
							</div>
							<label class="control-label col-md-1">是否连续</label>
							<div class="col-md-2">
								<select name="query.continuity" class="form-control" value="${query.continuity}">
									<option value="" <#if (query.continuity)??>selected</#if>>所有</option>
									<option value="true" <#if query.continuity == true>selected</#if>>连续</option>
									<option value="false" <#if (query.continuity)?? && query.continuity == false>selected</#if>>不连续</option>
								</select>
							</div>

							<label class="control-label col-md-1" >创建时间</label>
							<div class="col-md-3 input-group type-label">
								<input class="form-control Wdate" type="text" name="query.startTime" placeholder="" readonly="readonly" value="${query.startTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								<label class="input-group-addon" style="width:25px ">到</label>
								<input class="form-control Wdate" type="text" name="query.endTime" placeholder="" readonly="readonly" value="${query.endTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
							</div>
						</div>
					</div>
					<div>
						<br />
						<div class="col-md-offset-10" style="text-align: right">
							<button type="button" class="btn btn-default" onclick="formReset(this)">
								<i class="icon-refresh"></i> 重置
							</button>
							<button type="submit" class="btn blue" onclick="return checkParam()">
								<i class="icon-search"></i> 查询
							</button>
                            <@header method="auth" authCode="PRIORITY_WAREHOUSE_INVENTORY_LOG_DOWNLOAD">
							<button type="button" class="btn btn-default" onclick="downloadRecord()">
								<i class="icon-download"></i> 导出
							</button>
                            </@header>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>

		<div class="row">
			<div id="fixedDiv" class="col-md-12">
				<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
					<colgroup>
						<col width="6%" />
						<col width="12%" />
						<col width="12%" />
						<col width="14%" />
						<col width="12%" />
						<col width="12%" />
						<col width="10%" />
						<col width="12%" />
						<col width="26%" />

					</colgroup>
					<thead>
					<tr>
						<th><label class="checkbox-inline"><input type="checkbox" name="checkAll"> 全选</label></th>
						<th>SKU</th>
						<th>货主ID</th>
						<th>原有库存</th>
						<th>修改库存</th>
						<th>修改后库存</th>
						<th>操作人</th>
						<th>操作时间</th>
						<th>备注</th>
					</tr>
					</thead>
				</table>
			</div>
			<div class="col-md-12" id="task-list-warp">
				<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
					<colgroup>
                        <col width="6%" />
                        <col width="12%" />
                        <col width="12%" />
                        <col width="14%" />
                        <col width="12%" />
                        <col width="12%" />
                        <col width="10%" />
                        <col width="12%" />
                        <col width="26%" />

					</colgroup>
					<thead>
					<tr>
						<th><label class="checkbox-inline"><input type="checkbox" name="checkAll"> 全选</label></th>
						<th>SKU</th>
                        <th>货主ID</th>
						<th>原有库存</th>
						<th>修改库存</th>
						<th>修改后库存</th>
						<th>操作人</th>
						<th>操作时间</th>
						<th>备注</th>
					</tr>
					</thead>
					<tbody>
					<#list domain.pacStockLogs as stockLog>
					<tr<#if (stockLog.continuity)?? && stockLog.continuity == false> style='color: red;'</#if>>
						<td>
							<label class="checkbox-inline">
								<input class="${stockLog.id}" name="ids" type="checkbox" value="${stockLog.id}">
								${stockLog.id}
							</label>
						</td>
						<td>${stockLog.sku}</td>
						<td>${stockLog.ownerUserId}</td>
						<td>${stockLog.originalQuantity}</td>
						<td>${stockLog.quantity}</td>

						<td>${stockLog.originalQuantity + stockLog.quantity}</td>

						<td>${util('name',stockLog.createBy)}</td>
						<td>${stockLog.creationDate}</td>
						<td>${stockLog.contentDetail}</td>
					</tr>
					</#list>
					</tbody>
				</table>
			</div>
			<div id="fixed-bottom" >
				<div id="pager"><ur class="pages"><li class="select"><select><option>10</option></select></ur></div>
			</div>
		</div>
	</div>


	<#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js?v=${.now?datetime}"></script>
<script type="text/javascript">

    $(document).ready(function(){
        let value = $("input[name='query.ownerUserId']").val();
        if (!value) {
            //todo zyf 暂时设置默认值为 2974390427
            $("input[name='query.ownerUserId']").val("2974390427")
        }
    })
	// 分页
	var total = "${domain.page.totalCount}";
	var pageNo = "${domain.page.pageNo}";
	var pageSize = "${domain.page.pageSize}";
	$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});
	var heights = $("body").height();
	if(heights>910){
		$("#fixed-bottom").addClass("fixed-bottom-height")
	}


	// 类型
	var typeArray = ${domain.typeList};
	$("input[name='query.type']").select2({
		data: typeArray,
		placeholder: "库存类型",
		multiple: false,
		allowClear: true
	});


	$.getJSON(CONTEXT_PATH + "system/saleusers/userByRole?roleId=", function(json){
		if (json) {
			$("input[name='query.createBy']").select2({
				data : json,
				placeholder : "操作人",
				allowClear : true
			});
		} else {
			$("input[name='query.createBy']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
		}
	});


	// 全选
	var checkAll = $("input[name='checkAll']");
	// 子选项
	var itemIds = $("input[name='ids']");
	checkAll.change(
			function() {
				itemIds.prop("checked", $(this).prop("checked"));
				itemIds.each(function() {
					var f = $(this).is(":checked");
					var checkClass = $(this).prop("class");
					$("." + checkClass).each(function() {
						$(this).prop("checked", f);
					})
				})
			}
	);


	function checkParam() {
		var sku = $("input[name='query.sku']").val();
		var type = $("input[name='query.type']").val();
		if (!sku){
			layer.alert("请输入SKU", "error");
			return false;
		}
		if (sku.indexOf("=") != -1){
			sku = sku.split("=")[0];
			$("input[name='query.sku']").val(sku);
		}
		if (!type){
			layer.alert("请输入库存类型", "error");
			return false;
		}
		return true;
	}


	// 获取选中的记录
	function getCheckedIds() {
		var checkedIds = $("input[name='ids']:checked");
		return checkedIds;
	}

	// 导出
	function downloadRecord() {
		var sku = $("input[name='query.sku']").val();
		var type = $("input[name='query.type']").val();
		if (sku==null){
			layer.alert("请输入SKU", "error");
			return false;
		}
		if (sku.indexOf("=") != -1){
			sku = sku.split("=")[0];
			$("input[name='query.sku']").val(sku);
		}
		if (type==null){
			layer.alert("请输入库存类型", "error");
			return false;
		}
		var checkedDatas = getCheckedIds();
		if(checkedDatas.length == 0) {
			var param = $("#domain").serialize();

			window.open(CONTEXT_PATH + "pacStockLog/download?" + param);
		} else {
			var ids = checkedDatas.serialize();
			window.open(CONTEXT_PATH + "pacStockLog/download?" + ids);
		}
	}
</script>
</body>
</html>