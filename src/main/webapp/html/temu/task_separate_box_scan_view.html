<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
    <head>
        <title>易世通达仓库管理系统</title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <#include "/common/include.html">
        <style type="text/css">
            .print-content-size{
                width: 100mm;
                height: 100mm;
            }
            .modal-body input {
                width: 240px;
            }
            #task-list td {
                vertical-align:middle;
            }
            .form-control{
                height:42px;
            }
            .my-success {
                background-color: #19be6b;
            }
            .my-warning {
                background-color: #ff9900;
            }
            .my-disabled {
                background-color: #c5c8ce;
            }
        </style>
    </head>
    <body>
        <@header method="header" active="15060000"><#include "/ftl/header.ftl"></@header>
        <div id="page" style="background-color: rgb(231, 237, 248)">
            <!-- 内容 -->
            <div class="container-fluid" style="background-color: white;border: none">
                <div class="row" id="show_contents">
                    <#if domain.errorMsg??>
                        <span id="scan-error">${domain.errorMsg}</span>
                    <#elseif domain.temuPickBox?? && domain.packageInfo?? && domain.packageInfo.sourceFrom?? && domain.packageInfo.sourceFrom == 1>

                        <div class="row">
                            <div style="text-align: center;font-size: 60px;">
                                <strong id="box-number">${domain.temuPickBox.number}</strong>
                            </div>
                            <iframe src="${domain.packageInfo.temuTagUrl}" frameborder="0" marginheight="0" marginwidth="0" frameborder="0" scrolling="auto" id="printTemuTagUrlFrame" name="printTemuTagUrlFrame" width="100%" height="500px"></iframe>
                        </div>
                    <#elseif domain.boxMarkInfoResponse?? && domain.temuPickBox?? && domain.packageInfo??>
                        <#assign boxInfo = domain.boxMarkInfoResponse>
                        <#assign pickBox = domain.temuPickBox>
                        <#assign order = domain.packageInfo>
                        <div class="row">
                            <div style="width: 70%;float: right; font-size: medium;">
                                <strong>当前扫描：<span style="color: darkred;">${domain.taskNoAndSku}</span></strong>
                                <br/>
                                <span>发货单号：${order.deliverOrderNo}</span>
                                <br/>
                                <span>收货仓库：${order.receiveHouse}</span>
                                <br/>
                                <span>快递单号（包裹号）：${boxInfo.packageSn}</span>
                            </div>
                        </div>
                        <div id="print_content" style="float: left;">
                            <div style="width: 100mm;height: 98.96mm;box-sizing:border-box ;padding: 3mm; " class="box">
                                <!--      发货日期 字号48px/4mm  -->
                                <div style="font-size:4mm;margin-left: 5mm;">
                                    发货日期:<span style="margin-left: 5px">${boxInfo.deliverTime?number_to_date?string("yyyy-MM-dd")}</span>
                                    <span style="float: right; margin-right: 5mm;"><strong>${pickBox.number}</strong></span>
                                </div>
                                <!--        二维码区域-->
                                <div style="width: 90mm;margin-top: 5px">
                                    <!--            因为二维码有空白,所以往左边偏移20mm-->
                                    <img src="${CONTEXT_PATH}servlet/barcode?keycode=${boxInfo.packageSn}&amp;width=300&amp;height=50" style="width: 90mm;height: 10mm;">
                                </div>
                                <!--        包装信息-标头-->
                                <div style="margin-top: 1mm;margin-left: 5mm">
                                    <span style="font-size: 5mm;font-weight: bold">第${boxInfo.packageIndex}包(共${boxInfo.totalPackageNum}包)</span>
                                    <span style="font-size: 3.33mm;vertical-align: top;margin-left: 20mm">${boxInfo.packageSn}</span>
                                </div>
                                <!--        包装信息-表格-->
                                <div style="width: 90mm; height: 199px; border: 1px solid black;">
                                    <!--            表头-->
                                    <div style="border-bottom: 1px solid black; height: 30px; display: flex;  text-align: center; font-weight: bold;">
                                        <div style="flex: 1; border-right: 1px solid black; font-size: 4mm;height: 30px;line-height: 30px">${boxInfo.typeStr}</div>
                                        <div style="flex: 3; font-size: 6mm;height: 30px;line-height: 30px"><#if order.type == 3>加急&nbsp;&nbsp;</#if>${boxInfo.subWarehouseName}</div>
                                    </div>
                                    <!--            商品名称-->
                                    <div style="font-size: 4mm;border-bottom: 1px solid black; height: 30px;line-height: 30px"><span style="margin-left: 5px">${boxInfo.supplierName}</span></div>
                                    <!--            商品信息-->
                                    <div style="font-size: 4.5mm;">
                                        <div style="font-weight: 700;font-size: 4.5mm;height:95px; overflow: hidden">${boxInfo.productName}</div>
                                        <!--                skC信息-->
                                        <div style="font-size: 4mm;">SKC${boxInfo.productSkcId}</span></div>
                                        <div style="font-size: 4mm;">
                                            <span>SKU货号${boxInfo.skcExtCode}</span>
                                            <span style="float: right;margin-right: 10mm;font-size: 5mm">×${boxInfo.packageSkcNum}件</span>
                                        </div>
                                    </div>
                                </div>
                                <div style="display: flex ;margin-top: 5px;width:90mm;">
                                    <div style="flex: 1; width: 50%">
                                        <#if boxInfo.deliveryMethod == 1>
                                            <div>自行配送</div>
                                            <div>司机:${boxInfo.driverName}</div>
                                            <div>手机号:${boxInfo.driverPhone}</div>
                                        <#elseif boxInfo.deliveryMethod == 2>
                                            <div>${boxInfo.expressCompany}</div>
                                            <div>${boxInfo.expressDeliverySn}</div>
                                        <#elseif boxInfo.deliveryMethod == 3>
                                            <div>第三方物流</div>
                                            <div>${boxInfo.expressCompany}</div>
                                            <div>${boxInfo.expressDeliverySn}</div>
                                        </#if>
                                    </div>
                                    <div style="width: 50%">
                                        <img src="${CONTEXT_PATH}servlet/barcode?keycode=${boxInfo.packageSn}&amp;width=300&amp;height=50" style="width: 45mm; height: 30px; margin-right: 4mm;">
                                        <div style="font-size: 3mm;margin-left: 10mm">${boxInfo.packageSn}</div>
                                    </div>
                                </div>
                            </div>
                            <!-- 单排 -->
                            <p style="page-break-after: always; margin: 0; padding: 0; line-height: 1px; font-size: 1px;clear: both;">
                                &nbsp;
                            </p>
                        </div>
                    </#if>
                </div>
            </div>
        </div>
    </body>
</html>