<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<meta content="text/html; charset=utf-8" http-equiv="Content-type">
	<title>Print</title>
	<style>
		@media print {
			.printbtn {
				display: none;
			}
		}
	</style>
</head>

<body style="padding: 0px; margin: 20px">
<#if !(param.scan)??>
	<div class="printbtn">
		<input type="hidden" id="print-size" value="${domain.printSku ? size}">
	</div>
</#if>

<jsp:useBean id="now" class="java.util.Date" />

<form id="print_content">
    <#if (domain.printSku)?? && (domain.printSku)?size gt 0>
        <div id="print-item-0">
            <#list domain.printSku as printSku>

                <#assign businessId = (param.inId)! printSku.businessId >

            <input type="hidden" name="whUniqueSkus[${printSku_index}].relationId" value="${businessId}">
            <input type="hidden" name="whUniqueSkus[${printSku_index}].type" value="${printSku.type }">
            <input type="hidden" name="whUniqueSkus[${printSku_index}].uuid" value="${printSku.code }">
            <input type="hidden" name="whUniqueSkus[${printSku_index}].sku" value="${printSku.sku }">
            <input type="hidden" name="whUniqueSkus[${printSku_index}].sourceFrom" value="${printSku.sourceFrom }">

            <!-- 32mm二维码 -->
            <div style="width:31.69mm; height:17.6mm;font-weight: 300; text-align: center; float: left; margin-top: 0mm;">
                <div style="width: 13mm;text-align:center;float: left; margin-top: 2mm;margin-left: 1.5mm;">
                    <img style="border: 0;margin: 0px 0px 0px 0px" src="${CONTEXT_PATH}servlet/qrcode?size=50&keycode=${printSku.uuid }">
                </div>

                <#if printSku.sku ? length lte 13 >
                    <div style="width: 16mm;float: left;height: 13mm; margin-top: 2.5mm;">
                        <!-- MAC90178-BK 10px会越界-->
                        <div style="font-weight: 600;font-size: 6pt; text-align: left; margin-left: 1mm;">${printSku.sku }</div>
                        <div style="font-weight: 600;font-size: 9pt;margin-top: -0.5mm; text-align: left; margin-left: 1mm;">
                            <#if printSku.location?? && printSku.location?length gt 12>
                                ${printSku.location?substring(0,12) }
                            <#else>
                                ${printSku.location }
                            </#if>
                        </div>
                        <div style="font-weight: 600;font-size: 6pt; text-align: left; margin-left: 1mm;">${printSku.code}</div>
                        <div style="font-weight: 600;font-size: 6pt; text-align: left; margin-left: 1mm;">
                            <#if printSku.skuName?? && printSku.skuName?length gt 10>
                                ${printSku.skuName?substring(0,10) }...
                            <#else>
                                ${printSku.skuName }
                            </#if>
                        </div>
                    </div>
                <#elseif printSku.sku ? length gt 13 >
                    <div style="width: 16mm;float: left;height: 13mm; margin-top: 1.8mm;">
                        <!-- 6EE100685-BK-XL 超过11位缩小字体-->
                        <div style="font-weight: 600;font-size: 6pt; text-align: left; margin-left: 1mm;">${printSku.sku }</div>
                        <div style="font-weight: 600;font-size: 9pt;margin-top: -1mm; text-align: left; margin-left: 1mm;">
                            <#if printSku.location?? && printSku.location?length gt 12>
                                ${printSku.location?substring(0,12) }
                            <#else>
                                ${printSku.location }
                            </#if>
                        </div>
                        <div style="font-weight: 600;font-size: 6pt;margin-top: -0.5mm; text-align: left; margin-left: 1mm;">${printSku.code}</div>
                        <div style="font-weight: 600;font-size: 6pt; text-align: left; margin-left: 1mm;">
                            <#if printSku.skuName?? && printSku.skuName?length gt 10>
                                ${printSku.skuName?substring(0,10) }...
                            <#else>
                                ${printSku.skuName }
                            </#if>
                        </div>
                    </div>
                </#if>
                <div style="width: 31.69mm;float: left;margin-top: 0.5mm;">
                    <div style="font-weight: 600;font-size: 6pt;text-align: left;margin-left: 2mm;">
                        <#if domain.printSkuUser?length gt 18 >
                            ${domain.printSkuUser?substring(0,18)}-${printSku.warehouseId}
                        <#else>
                            ${domain.printSkuUser}-${printSku.warehouseId}
                        </#if>
                    </div>
                </div>
            </div>
            <!-- 单排 -->
            <p style="page-break-after: always; margin: 0; padding: 0; line-height: 1px; font-size: 1px;clear: both;">&nbsp;</p>
        </div><div id="print-item-${printSku_index }">
        </#list>
    </div>
        <#if domain.shelfStraightHair=true>
            <input type="hidden" id="shelfStraightHair" value="true">
        <#else >
            <input type="hidden" id="shelfStraightHair" value="false">
        </#if>

        <#if domain.freeCheck=true>
            <input type="hidden" id="freeCheck" value="true">
        <#else >
            <input type="hidden" id="freeCheck" value="false">
        </#if>
        <#if domain.noLabel=true>
            <input type="hidden" id="noLabel" value="true">
        <#else >
            <input type="hidden" id="noLabel" value="false">
        </#if>
    <#elseif (domain.returnExpressId)??>
        <h4 style="color: red;"><strong>有账号正在打印该sku的二维码，请稍后再补标，入库单id【${domain.returnExpressId}】！</strong></h4>
    </#if>
</form>

<object width="0" height="0" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" id="LODOP_OB">
	<embed width="0" height="0" type="application/x-print-lodop" id="LODOP_EM">
</object>

<!-- 打印插件 -->
<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
<script src="${CONTEXT_PATH }js/assets/plugins/jquery-1.10.2.min.js" type="text/javascript" ></script>
<script language="javascript">

    var LODOP, P_ID="", waiting=false; //声明为全局变量
    function CheckIsInstall() {
        try {
            var LODOP = getLodop(document.getElementById('LODOP_OB'),
                document.getElementById('LODOP_EM'));
            if ((LODOP != null) && (typeof (LODOP.VERSION) != "undefined"))
                return LODOP.VERSION;
        } catch (err) {
            //layer.alert("Error:本机未安装或需要升级!");
        }
        return false;
    }
    function myPrint() {
        debugger;
         myPagePrint2($("#print-size").val());
    };

    // 分页打印
    function myPagePrint() {

        var start = $("#start").val();
        var end = $("#end").val();

        if($.trim(start) == "" || !testNumber(start)) {
            layer.alert("开始页不能为空且大于等于1");
            $("#start").focus();
            return;
        }

        if(parseInt($.trim(start)) < 1) {
            layer.alert("开始页不能为空且大于等于1");
            $("#start").focus();
            return;
        }

        if($.trim(end) != "" && !testNumber(end)) {
            layer.alert("请输入正整数");
            $("#end").focus();
            return;
        }

        if(parseInt($.trim(end)) < parseInt($.trim(start))) {
            layer.alert("开始页不能为空且大于等于1");
            return;
        }

        LODOP = getLodop(document.getElementById('LODOP_OB'), document
            .getElementById('LODOP_EM'));
        LODOP.PRINT_INIT("打印");

        var innerHtml = "";

        $("[id^='print-item-']").each(function(i, obj) {

            var index = i + 1;
            if($.trim(end) == "") {
                if(parseInt(start) == index) {
                    innerHtml = $(obj).html();
                    return false;
                }
            } else {
                if(index >= parseInt(start) && index <= parseInt(end)) {
                    innerHtml = innerHtml + $(obj).html();
                }
            }

        });

        LODOP.ADD_PRINT_HTM(0, 0, "32mm", "18mm", innerHtml);// 单排

        // LODOP.ADD_PRINT_HTM(0, 0, "102mm", "12mm", innerHtml);// 三排

        //LODOP.PREVIEW();
        LODOP.PRINT();

    };

    // 分页打印
    function myPagePrint2(size) {
        debugger;
        console.log("printQRcode printSize:"+size);
        var pageSize = 20;
        var pagePai = 1;
        var length = Math.ceil(size/(pageSize));

        for (var i = 1; i <= length; i++) {
            var start = (i-1)*pageSize + 1;
            var end = i*pageSize;
            myPagePrint3(start, end);
        }
        var shelfStraightHair=$("#shelfStraightHair").val();
        if (shelfStraightHair && shelfStraightHair == 'true'){
            // 打印“不上架直发”内容
            printTag("不上架直发");
        }

        // var freeCheck=$("#freeCheck").val();
        // if (freeCheck && freeCheck == 'true'){
        //     // 打印“入库单免检”内容
        //     printTag("入库单免检");
        // }

        var noLabel=$("#noLabel").val();
        if (noLabel && noLabel == 'true'){
            // 打印“不贴标入库”内容
            printTag("不贴标入库");
        }

        // 保存跟踪记录
        $.ajax({
            url : "${CONTEXT_PATH}uniqueSkus/trace",
            type : "POST",
            dataType : "json",
            data : $("#print_content").serialize()
        });
    };

    function printTag(tagName){
        if (tagName){
            LODOP.SET_PRINT_PAGESIZE(0, "32mm", "18mm", 'Note');
            LODOP.ADD_PRINT_TEXT(21,5,121,36,tagName);
            LODOP.SET_PRINT_STYLEA(0,"FontSize",15);
            LODOP.SET_PRINT_STYLEA(0,"Bold",1);
            LODOP.PRINT();
        }
    }

    // 分页打印
    function myPagePrint3(start, end) {

        LODOP = getLodop(document.getElementById('LODOP_OB'), document
            .getElementById('LODOP_EM'));
        LODOP.PRINT_INIT("打印");

        var innerHtml = "";

        $("[id^='print-item-']").each(function(i, obj) {

            var index = i + 1;
            if($.trim(end) == "") {
                if(parseInt(start) == index) {
                    innerHtml = $(obj).html();
                    return false;
                }
            } else {
                if(index >= parseInt(start) && index <= parseInt(end)) {
                    innerHtml = innerHtml + $(obj).html();
                }
            }

        });
        LODOP.ADD_PRINT_HTM(0, 0, "32mm", "18mm", innerHtml);// 单排
        // LODOP.ADD_PRINT_HTM(0, 0, "102mm", "12mm", innerHtml);// 三排
        LODOP.PRINT();
    };

    function myPreview() {
        CreatePrintPage();
        LODOP.PREVIEW();
    };
    function myPrintDesign() {
        CreatePrintPage();
        LODOP.PRINT_DESIGN();
    };
    function CreatePrintPage() {
        LODOP = getLodop(document.getElementById('LODOP_OB'), document
            .getElementById('LODOP_EM'));
        LODOP.PRINT_INIT("打印");
        try {
            if (typeof (eval(CreatePrintPageWithImage)) == 'function') {
                return CreatePrintPageWithImage();
            }
        } catch (e) {
        }

        // 单排
        LODOP.ADD_PRINT_HTM(0, 0, "32mm", "18mm", document
            .getElementById('print_content').innerHTML);
        // 三排
        /* LODOP.ADD_PRINT_HTM(0, 0, "102mm", "12mm", document
                .getElementById('print_content').innerHTML); */
    };
    //表格打印采购单详情处使用
    function myPrinttable() {
        CreatePrintpagetable();
        LODOP.PRINT();
    };
    function myPreviewtable() {
        CreatePrintpagetable();
        LODOP.PREVIEW();
    };
    function CreatePrintpagetable() {
        LODOP = getLodop(document.getElementById('LODOP_OB'), document
            .getElementById('LODOP_EM'));
        LODOP.PRINT_INIT("打印");
        try {
            if (typeof (eval(CreatePrintPageWithImage)) == 'function') {
                return CreatePrintPageWithImage();
            }
        } catch (e) {
        }
        LODOP.ADD_PRINT_HTM(0, 0, "210mm", "297mm", document
            .getElementById('print_content').innerHTML);
        LODOP.SET_PRINT_PAGESIZE('2', "", "", '')
    }

    function testNumber(num) {
        var goodChar = "0123456789";
        var c;
        for(var i=0;i<num.length;i++){
            c=num.charAt(i);
            if(goodChar.indexOf(c) == -1){
                return false;
            }
        }

        if(Number(num) >= 2147483648) {
            return false;
        }

        return true;
    }
</script>
</body>
</html>