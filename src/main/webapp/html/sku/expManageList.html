<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
    <#include "/common/include.html">
    <style type="text/css">
        .top_bar{
            position:fixed;top:0px;
        }
        #task-list td {
            vertical-align:middle;
        }
        .table thead > tr > th{
            vertical-align:middle;
        }
        .exp-search-nav{
            margin: 20px;
        }
        .exp-search-nav>div{
            margin-right: 20px;
            width: 120px;
            border-radius: 10px !important;
        }
        .sync-description{
            width: 65% !important;
            position: absolute;
            display: inline-block;
            margin-left: 30px;
        }
    </style>
</head>
<body>
<@header method="header" active="11040000"><#include "/ftl/header.ftl"></@header>
<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li><a href="#">SKU</a></li>
                <li class="active">保质期批次</li>
            </ul>
        </div>
    </div>
    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">
        <div class="row">
            <div class="col-md-12">
                <#assign query=domain.query>
                <form action="${CONTEXT_PATH}expManage/search"
                      class="form-horizontal form-bordered form-row-stripped"
                      method="post" modelAttribute="domain" id="domain" name="expManageForm">
                    <!-- 分页信息 -->
                    <input id="page-no" type="hidden" name="page.pageNo" value="1">
                    <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
                    <input id="exp-flag" type="hidden" name="query.flag" value="">
                    <div class="form-group">
                        <label class="control-label col-md-1">批次号：</label>
                        <div class="col-md-3">
                            <input class="form-control" placeholder="多个查询以逗号分开" name="query.batchNo" type="text" value="${query.batchNo}">
                        </div>

                        <label class="control-label col-md-1">SKU：</label>
                        <div class="col-md-3">
                            <input class="form-control" placeholder="多个查询以逗号分开" name="query.sku" type="text" value="${query.sku}">
                        </div>

                        <label class="control-label col-md-1">来源：</label>
                        <div class="col-md-3">
                            <input class="form-control" name="query.source" type="text" value="${query.source}">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-1">全部出库：</label>
                        <div class="col-md-3">
                            <select class="form-control" name="query.allCheckOut" value="${query.allCheckOut}">
                                <option ></option>
                                <option <#if (query.allCheckOut)?? && query.allCheckOut == false>selected</#if> value="false">否</option>
                                <option <#if (query.allCheckOut)?? && query.allCheckOut == true>selected</#if> value="true">是</option>
                            </select>
                        </div>
                        <label class="control-label col-md-1">到期时间：</label>
                        <div class="col-md-3">
                            <div class="input-group">
                                <input class="form-control Wdate" type="text" name="query.fromExpTime" placeholder="" readonly="readonly" value="${query.fromExpTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                <span class="input-group-addon">到</span>
                                <input class="form-control Wdate" type="text" name="query.toExpTime" placeholder="" readonly="readonly" value="${query.toExpTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                            </div>
                        </div>
                        <label class="control-label col-md-1">库存ID</label>
                        <div class="col-md-3">
                            <input class="form-control" name="query.stockIdStr" placeholder="请输入库存ID，多个用英文逗号分割" type="text" value="${query.stockIdStr}">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-1">入库单ID</label>
                        <div class="col-md-3">
                            <input class="form-control" name="query.relationId"  type="text" value="${query.relationId}">
                        </div>
                        <label class="control-label col-md-1">库位</label>
                        <div class="col-md-3">
                            <input class="form-control" name="query.locationNumber" placeholder="请输入库位，多个用英文逗号分割" type="text" value="${query.locationNumber}">
                        </div>
                    </div>
                    <div class="exp-search-nav">
                        <div class="btn blue" onclick="checkExpflag(1)">
                            <#if !(domain.flagCountMap)?? || !(domain.flagCountMap["1"]??)>
                                促销（<span id="">0</span>）
                            <#else >
                                促销（<span id="">${domain.flagCountMap["1"]}</span>）
                            </#if>
                        </div>
                        <div class="btn yellow" onclick="checkExpflag(2)">
                            <#if !(domain.flagCountMap)?? || !(domain.flagCountMap["2"]??)>
                                临期（<span id="">0</span>）
                            <#else >
                                临期（<span id="">${domain.flagCountMap["2"]}</span>）
                            </#if>
                        </div>
                        <div class="btn red" onclick="checkExpflag(3)">
                            <#if !(domain.flagCountMap)?? || !(domain.flagCountMap["3"]??)>
                                过期（<span id="">0</span>）
                            <#else >
                                过期（<span id="">${domain.flagCountMap["3"]}</span>）
                            </#if>
                        </div>
                        <div class="col-md-7 sync-description">
                            <span>数据更新时间：${domain.expSkuLastCalcTime}</span>
                            <br/>
                            <span>促销：需要加快库存消化速度；</span>
                            <span>临期：接近保质期的批次,注意提醒外借部分尽快归还或转核销；</span>
                            <span>过期：批次已过保质期，需要报废，外借未归还的只能转核销</span>
                        </div>
                    </div>
                    <div>
                        <div class="pull-left col-md-10" style="margin: 10px;line-height:40px;">
                            <@header method="auth" authCode="QUALITY_GUARANTEE_PERIOD_BATCH_HISTORY_IMPORT">
                            <button id="addExpData" type="button" class="btn btn-default" >
                                <span class="icon-plus"></span> 历史数据录入
                            </button>
                            </@header>
                            <@header method="auth" authCode="QUALITY_GUARANTEE_PERIOD_HISTORY_DATA_IMPORT">
                            <span class="btn btn-default fileinput-button">
								<span class="icon-upload"> 历史数据导入</span>
								<input type="file" name="file" onchange="uploadExpBatch(this)" />
							</span>
                            </@header>
                            <@header method="auth" authCode="QUALITY_GUARANTEE_PERIOD_DOWNLOAD_PARADIGM">
                            <a class="btn btn-default" href="${CONTEXT_PATH}file/execl/importExpBatch.xlsx">
                                <i class="icon-download"></i> 下载模板
                            </a>
                            </@header>

                            <@header method="auth" authCode="QUALITY_GUARANTEE_PERIOD_GENERATE_PICKING_TASK">
                            <button type="button" class="btn  btn-default" onclick="createPickingTask()">
                                <i class="icon-pickingTask"></i> 生成核对任务
                            </button>
                            </@header>
                        </div>
                    </div>
                    <div class="col-md-offset-10" style="text-align: right">
                        <@header method="auth" authCode="QUALITY_GUARANTEE_PERIOD_DOWNLOAD">
                        <button type="button" class="btn btn-default" onclick="exportModal()">
                            <i class="icon-download"></i> 导出
                        </button>
                        </@header>
                        <button type="button" class="btn btn-default" onclick="expManageFormReset(this)">
                            <i class="icon-refresh"></i> 重置
                        </button>
                        <button type="submit" class="btn blue">
                            <i class="icon-search"></i> 查询
                        </button>
                    </div>
                </form>
            </div>
            <br/>
        </div>

        <div class="row">
            <div id="fixedDiv" class="col-md-12">
                <table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
                    <colgroup>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="6%"/>
                        <col width="6%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="5%"/>
                        <col width="5%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="6%"/>
                    </colgroup>
                    <thead>
                    <tr>
                        <th>选择 <input type="checkbox" name="check-all" onchange="checkChange(this)"/></th>
                        <th>sku</th>
                        <th>库存ID</th>
                        <th>库位</th>
                        <th>批次号</th>
                        <th>来源</th>
                        <th>入库单ID</th>
                        <th>生产日期</th>
                        <th>保质期天数</th>
                        <th>到期时间</th>
                        <th>在库库存</th>
                        <th>外借在途</th>
                        <th>采购入库</th>
                        <th>调拨入库</th>
                        <th>退件入库</th>
                        <th>订单出库</th>
                        <th>报废出库</th>
                        <th>不良品出库</th>
                        <th>调拨出库</th>
                        <th>盘点</th>
                        <th>核查变更</th>
                        <th>标签</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="col-md-12" id="task-list-warp">
                <table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                    <colgroup>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="6%"/>
                        <col width="6%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="5%"/>
                        <col width="5%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="6%"/>
                    </colgroup>
                    <thead>
                    <tr>
                        <th>选择 <input type="checkbox" name="check-all" id="check-all" onchange="checkChange(this)"/></th>
                        <th>sku</th>
                        <th>库存ID</th>
                        <th>库位</th>
                        <th>批次号</th>
                        <th>来源</th>
                        <th>入库单ID</th>
                        <th>生产日期</th>
                        <th>保质期天数</th>
                        <th>到期时间</th>
                        <th>在库库存</th>
                        <th>外借在途</th>
                        <th>采购入库</th>
                        <th>调拨入库</th>
                        <th>退件入库</th>
                        <th>订单出库</th>
                        <th>报废出库</th>
                        <th>不良品出库</th>
                        <th>调拨出库</th>
                        <th>盘点</th>
                        <th>核查变更</th>
                        <th>标签</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list domain.expManages as em>
                        <tr>
                            <td>
                                <label class="checkbox-inline"> <input name="outIds" id="outIds" value="${em.id}" text-align="center" class="ids" type="checkbox" /> ${em.id}
                                </label>
                            </td>
                            <td>${em.sku }</td>
                            <td>${em.stockId }</td>
                            <td>${em.locationNumber }</td>
                            <td>${em.batchNo }</td>
                            <td>${util('enumName', 'com.estone.statistics.enums.DrpTurnoverOderType', em.source)}</td>
                            <td>${em.stockInId }</td>
                            <td>
                                <#if em.proDate??>${em.proDate?string('yyyy-MM-dd')}</#if>
                            </td>
                            <td>${em.days }</td>
                            <td>
                                <#if em.expDate??>${em.expDate?string('yyyy-MM-dd') }</#if>
                            </td>
                            <td>${em.quantity }</td>
                            <td>${em.lendOnWayQuantity }</td>
                            <td>${em.checkInQuantity }</td>
                            <td>${em.allocationInQuantity }</td>
                            <td>${em.returnQuantity }</td>
                            <td>${em.deliverQuantity }</td>
                            <td>${em.scrapQuantity }</td>
                            <td>${em.badProductQuantity }</td>
                            <td>${em.allocationOutQuantity }</td>
                            <td>${em.inventoryQuantity }</td>
                            <td>${em.checkQuantity }</td>
                            <td>
                                <#if em.flag?? && em.flag == 1>
                                    <a class="btn btn-xs blue"
                                       style="border-radius: 5px !important;">${util('enumName', 'com.estone.sku.enums.ExpFlags', em.flag)}</a>
                                <#elseif em.flag?? && em.flag == 2>
                                    <a class="btn btn-xs yellow"
                                       style="border-radius: 5px !important;">${util('enumName', 'com.estone.sku.enums.ExpFlags', em.flag)}</a>
                                <#elseif em.flag?? && em.flag == 3>
                                    <a class="btn btn-xs red"
                                       style="border-radius: 5px !important;">${util('enumName', 'com.estone.sku.enums.ExpFlags', em.flag)}</a>
                                <#else >
                                    ${util('enumName', 'com.estone.sku.enums.ExpFlags', em.flag)}
                                </#if>
                            </td>
                            <td>
                                <button type="button" class="btn btn-xs btn-info"
                                        onclick="editExpDate(${em.id},'${em.batchNo}','${em.proDate}','${em.days }','${em.expDate }')">
                                    编辑保质期
                                </button>
                                <a class="btn btn-xs btn-info" href="${CONTEXT_PATH}expManage/expManagetems/${em.id}">详情</a>
                                <button type="button" class="btn btn-xs btn-info"
                                        onclick="viewLog(${em.id}, 'expManage')">日志
                                </button>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
            </div>
        </div>
        <div id="fixed-bottom">
            <div id="pager"></div>
        </div>
        <div id='exp-setting-contain-div' style="display: none;">
            <h4 style="margin: 20px;"></h4>
            <div id="exp-setting-contain" style="margin: 20px;">
                <#include "/checkin/expSettingContain.html">
            </div>
        </div>
        <div id='add-sku-batch-contain-div' style="display: none;">
            <#include "/sku/expManageAdd.html">
        </div>
        <div class="modal fade ui-popup" id="exportModal" tabindex="-1" role="dialog" aria-labelledby="exportLabel" aria-hidden="true">
            <div class="modal-dialog">
<#--                <@header method="auth" authCode="QUALITY_GUARANTEE_PERIOD_DOWNLOAD_GUARANTEE_BATCH">-->
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title" id="myModalLabel">导出保质期批次</h4>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label class="radio-inline">
                                <input type="radio" style="width: 25px;" name="exportType" value="ALL">所有
                            </label>
                            <label class="radio-inline">
                                <input type="radio" style="width: 25px;" name="exportType" value="CHECK">当前选择
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" onclick="exportExpManage()">导出</button>
                    </div>
                </div>
<#--                </@header>-->
            </div>
        </div>
    </div>
    <#include "/common/footer.html">
</div>
<script id="expManageAddTemplate" type="text/x-jquery-tmpl">
    <tr>
        <td><input class="form-control Wdate" name="proDate" type="text" readonly="readonly" value="" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',alwaysUseStartDate:true,onpicked:function (){ChangeValue(this)}})"></td>" +
        <td><input class="form-control" type="text" name="days"  value="" onblur="changeDays(this)" onkeypress="if(event.keyCode==13) { changeDays(this); return false;}"></td>" +
        <td><input class="form-control Wdate" name="expDate" type="text" readonly="readonly" value="" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',alwaysUseStartDate:true,onpicked:function (){ChangeValue(this)}})"></td>" +
        <td><input class="form-control" name="expQuantity" number="true" min="0"></td>" +
        <td><input class="form-control" name="stockId" number="true" min="0"></td>" +
        <td style='width:160px;'><a class="btn" onclick="addExpTemplate(this)">增加批次</a><a class="btn" onclick="removeExpTemplate(this)">删除</a></td>
    </tr>
</script>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="application/javascript">
    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";
    $("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

    var heights = $("body").height();
    if(heights>910){
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }

    var sourceStr = ${domain.sourceStr};
    $("input[name='query.source']").select2({
        data : sourceStr,
        placeholder : "来源",
        allowClear : true
    });

    function checkChange(obj) {
        $("#task-list").find("input[name='outIds']").prop("checked", $(obj).is(':checked'));
    }

    function expManageFormReset(obj) {
        formReset(obj);
        $('#exp-flag').val('');
    }

    function checkExpflag(flag) {
        $('#exp-flag').val(flag);
        $("#domain").submit();
    }

    function editExpDate(id, batchNo, proDate, days, expDate) {
        if (id == null || id==''){
            layer.alert("参数id为空");
            return false;
        }
        var html = $('#exp-setting-contain-div').html();
        var index = layer.open({
            type: 1,
            skin: 'layui-layer-rim', //加上边框
            title: '编辑保质期',
            area: ['auto', '300px'], //宽高
            shadeClose: false, //开启遮罩关闭
            btn: ['确定'],
            content: html,
            success: function (layero, index) {
                layero.find('h4').text('批次号：' + batchNo);
                layero.find("input[name='days']").val(days);
                if (expDate) {
                    layero.find("input[name='expDate']").val(new Date(expDate).format("yyyy-MM-dd"));
                }
                if (proDate) {
                    layero.find("input[name='proDate']").val(new Date(proDate).format("yyyy-MM-dd"));
                }
            },
            yes: function (index, layero) {
                var days = layero.find("input[name='days']").val();
                var expDate = layero.find("input[name='expDate']").val();
                var proDate = layero.find("input[name='proDate']").val();
                if (days == null || days == '' || expDate == null || expDate == '' || proDate == null || proDate == '') {
                    layer.alert("保质期信息不能为空！");
                    return false;
                }
                doEditExpDate(index,id, proDate, days, expDate);
            },
            cancel: function () {
                layer.close(index);
                setTimeout(function () {
                    window.location.reload();
                }, 500);
            }
        });

        function doEditExpDate(index,id, proDate, days, expDate) {
            $.ajax({
                url: CONTEXT_PATH + "expManage/editExpDate",
                type: "POST",
                contentType: "application/json;charset=utf-8",
                data: JSON.stringify({
                    id: id,
                    days: days,
                    fromExpTime: proDate,
                    toExpTime: expDate,
                }),
                success: function (result) {
                    if (result.status == 200) {
                        layer.alert("成功!");
                        layer.close(index);
                        setTimeout(function () {
                            window.location.reload();
                        }, 500);
                    } else {
                        layer.alert("失败：" + result.message);
                    }
                }
            });
        }

    }

    function ChangeValue(obj) {
        if ($dp.cal) {
            var objDiv = $(obj).parent().parent();
            var name = $(obj).attr('name');
            var days = $(objDiv).find("input[name='days']").val();
            var datetime = new Date(Date.parse($dp.cal.getDateStr().replace(/-/g, "/"))).getTime();
            if (name == 'proDate') {
                var expDate = $(objDiv).find("input[name='expDate']").val();
                if (days && days > 0) {
                    var endDate = new Date(datetime + days * 24 * 3600 * 1000);
                    $(objDiv).find("input[name='expDate']").val(endDate.format("yyyy-MM-dd"));
                } else if (expDate) {
                    var expTime = new Date(Date.parse(expDate.replace(/-/g, "/"))).getTime();
                    var day = Math.floor((expTime - datetime) / (24 * 3600 * 1000));
                    $(objDiv).find("input[name='days']").val(day);
                }
            } else {
                var proDate = $(objDiv).find("input[name='proDate']").val();
                if (days && days > 0) {
                    var startDate = new Date(datetime - days * 24 * 3600 * 1000);
                    $(objDiv).find("input[name='proDate']").val(startDate.format("yyyy-MM-dd"));
                } else if (proDate) {
                    var proTime = new Date(Date.parse(proDate.replace(/-/g, "/"))).getTime();
                    var day = Math.floor((datetime - proTime) / (24 * 3600 * 1000));
                    $(objDiv).find("input[name='days']").val(day);
                }
            }

        }

    }

    function changeDays(obj) {
        var days = $(obj).val();
        var objDiv = $(obj).parent().parent();
        var proDate = $(objDiv).find("input[name='proDate']").val();
        var expDate = $(objDiv).find("input[name='expDate']").val();
        if (days && days > 0) {
            if (proDate) {
                var datetime = new Date(Date.parse(proDate.replace(/-/g, "/"))).getTime();
                var endDate = new Date(datetime + days * 24 * 3600 * 1000);
                $(objDiv).find("input[name='expDate']").val(endDate.format("yyyy-MM-dd"));
            } else if (expDate) {
                var datetime = new Date(Date.parse(expDate.replace(/-/g, "/"))).getTime();
                var proDate = new Date(datetime - days * 24 * 3600 * 1000);
                $(objDiv).find("input[name='proDate']").val(proDate.format("yyyy-MM-dd"));
            }
        }
    }



    $('#addExpData').on('click',function () {
        var html = $('#add-sku-batch-contain-div').html();
        var index = layer.open({
            type: 1,
            skin: 'layui-layer-rim', //加上边框
            title: '历史数据录入',
            area: ['850px', '500px'], //宽高
            shadeClose: false, //开启遮罩关闭
            btn: ['保存'],
            content: html,
            yes: function (index, layero) {
                saveBatch(index,layero);
            },
            cancel: function () {
                layer.close(index);
                setTimeout(function () {
                    window.location.reload();
                }, 500);
            }
        });
    });

    function getExpSkuStock(obj) {
        var templateDiv = $(obj).parent().parent().parent();
        var i = $(templateDiv).find('#add-sku-batch-body').find('tr').length;
        if (i > 0) {
            if (confirm("是否重新扫描?")) {
                $(templateDiv).find('#add-sku-batch-body').find('tr').remove();
                doScanSku(obj,templateDiv);
            }
        } else {
            doScanSku(obj,templateDiv);
        }

    }

    function doScanSku(obj,templateDiv) {
        var expSku = $(obj).val();
        if (expSku == '') {
            layer.alert('sku不能为空！', 'error');
            $(obj).val("");
            return;
        }
        $.ajax({
            url: CONTEXT_PATH + "expManage/getExpSkuStock",
            type: "GET",
            data: {
                sku: expSku.trim()
            },
            success: function (result) {
                if (result.body != null && result.body.unSetNum && parseInt(result.body.unSetNum) > 0) {
                    var totalNum = result.body.totalNum;
                    var unSetNum = result.body.unSetNum;

                    if (unSetNum && parseInt(unSetNum) <= 0) {
                        layer.alert("SKU未配置保质期仓库库存不足！");
                        $(obj).val("");
                        return false;
                    }
                    $(templateDiv).find("#totalNum").text(totalNum);
                    $(templateDiv).find("#unSetNum").text(unSetNum);
                    $(templateDiv).find("#scan-sku").val(expSku);
                    var option = $('#expManageAddTemplate').html();
                    $(templateDiv).find("#add-sku-batch-body").append(option);
                    $(obj).val("");
                } else {
                    layer.alert("SKU：[" + expSku + "]不是保质期SKU或未配置保质期仓库库存不足", "error");
                    $(obj).val("");
                }
            }
        });
    }

    function addExpTemplate(obj) {
        var templateDiv = $(obj).parent().parent().parent();
        var i = $(templateDiv).find('tr').length;
        if (i >= 10) {
            layer.alert("一个SKU最多只能添加10条！");
            return false;
        }
        var html = $('#expManageAddTemplate').html();
        $(templateDiv).append(html);
    }

    function removeExpTemplate(obj) {
        $(obj).parent().parent().remove();
    }

    function saveBatch(index,obj){
        var sku = $(obj).find('#scan-sku').val();
        var expManageList = [];
        var flag = true;
        var exp_array = new Array();
        $(obj).find('#add-sku-batch-body').find('tr').each(function (index) {
            var expManage = {};
            var expQuantity = $(this).find('input[name="expQuantity"]').val();
            var days = $(this).find('input[name="days"]').val();
            var proDate = $(this).find('input[name="proDate"]').val();
            var expDate = $(this).find('input[name="expDate"]').val();
            var stockId = $(this).find('input[name="stockId"]').val();
            var exp_info = proDate + days + expDate + stockId;
            var number = $.inArray(exp_info, exp_array);
            if (number != -1) {
                layer.alert("第"+(index+1)+"行，保质期信息重复，请确认后再保存！");
                flag = false;
                return false;
            }
            var reg3 = /^[0-9]+$/;
            if (!reg3.test(expQuantity)) {
                layer.alert("第"+(index+1)+"行，请输入正确的数量！");
                flag = false;
                return false;
            }
            if (!reg3.test(stockId)) {
                layer.alert("第"+(index+1)+"行，请输入正确的库存ID！");
                flag = false;
                return false;
            }
            expManage.sku = sku;
            expManage.quantity = expQuantity;
            expManage.days = days;
            expManage.proDate = proDate;
            expManage.expDate = expDate;
            expManage.stockId = stockId;
            expManageList.push(expManage);
            exp_array.push(exp_info);
        });

        if (flag){
            $.ajax({
                url: CONTEXT_PATH + "expManage/addExpBatch",
                type: "POST",
                contentType: "application/json;charset=utf-8",
                data: JSON.stringify(expManageList),
                success: function (result) {
                    if (result.status == 200) {
                        layer.alert("成功!");
                        layer.close(index);
                        setTimeout(function () {
                            window.location.reload();
                        }, 500);
                    } else {
                        layer.alert("保存失败：" + result.message);
                    }
                }
            });
        }
    }

    // 导入
    function uploadExpBatch(target) {
        var filename = target.value;
        var ext, idx;
        if (filename == '') {
            $("#submit-upload").attr("disabled", true);
            alert("请选择需要上传的文件!");
            return;
        } else {
            idx = filename.lastIndexOf(".");
            if (idx != -1) {
                ext = filename.substr(idx + 1).toUpperCase();
                ext = ext.toLowerCase();
                if (ext != 'xls' && ext != 'xlsx') {
                    alert("只能上传.Excel类型的文件!");
                    return;
                }
            } else {
                alert("只能上传.Excel类型的文件!");
                return;
            }
        }

        var r = confirm("确定上传" + filename + "?");

        if(!r) {
            return;
        }

        var uploadUrl = CONTEXT_PATH + "expManage/uploadExpBatch";

        var searchUrl = $("#domain").attr("action");

        $("#domain").attr("action", uploadUrl);

        $("#domain").ajaxSubmit(function(data) {
            if (data.status == 200) {
                layer.alert(data.message);
                setTimeout(function() {
                    window.location.reload();
                }, 1000);
            } else {
                layer.alert(data.message);
                $(target).val(null);
            }

            $("#domain").attr("action", searchUrl);
        });

        $("#domain").attr("action", searchUrl);
    }

    // 获取选中的批次
    function getCheckedIds() {
        var checkedIds = $("#task-list").find("input[name='outIds']:checked");
        return checkedIds;
    }

    // 导出
    function exportModal() {
        $("#exportModal").modal({
            keyboard: true
        });
    }

    function exportExpManage(){
        var exportType = $("input[name='exportType']:checked").val();
        if (null == exportType) {
            layer.alert("请选择导出条件", "error");
            return false;
        }

        var expIds = getCheckedIds();
        if (exportType == "CHECK" && expIds.length == 0) {
            layer.alert("请勾选需要导出的数据", "error");
            return false;
        }
        var ids = "";
        for (var i = 0; i < expIds.length; i++) {
            var check = expIds[i];
            var id = $(check).val();
            ids += id;
            if (i != expIds.length - 1) {
                ids += ",";
            }
        }
        var searchUrl = $("#domain").attr("action");
        var downloadUrl = CONTEXT_PATH + "expManage/export";
        var form = document.getElementById("domain");
        var exportTypeInput = document.createElement("input");
        exportTypeInput.type="hidden";
        exportTypeInput.name="exportType";
        exportTypeInput.value=exportType;
        form.appendChild(exportTypeInput);
        var idsInput = document.createElement("input");
        idsInput.type="hidden";
        idsInput.name="query.idList";
        idsInput.value=ids;
        form.appendChild(idsInput);

        form.setAttribute("action", downloadUrl);
        form.submit();
        form.setAttribute("action", searchUrl);
        $("#exportModal").modal("hide");
    }


    // 生成拣货任务
    function createPickingTask() {
        var checkedDatas = getCheckedIds();
        if(checkedDatas.length == 0) {
            layer.alert("请选择要生成核对任务的批次", 'error');
            return;
        }
        if(confirm("核对任务按照SKU-库位级别，可能会涉及到其他批次的数据，是否确认生成")) {
            var ids = checkedDatas.serialize();
            debugger;
            $.getJSON(CONTEXT_PATH + "expManageCheckTask/createCheckTask?" + ids, function(result){
                if(result.status == 200) {
                    layer.alert("成功", "success");
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                } else {
                    customizeLayer(result.message, "error");
                }
            });
        }



    }

</script>
</body>
</html>