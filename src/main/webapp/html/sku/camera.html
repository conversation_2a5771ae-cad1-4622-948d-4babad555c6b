<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <#include "/common/include.html">
    <style>
        .exception-camera {
            border: solid 1px #cccc;
            text-align: center;
        }
    </style>
</head>
<body>
<div id="exception-camera" class="exception-camera" >
    <input type="hidden" name="whSku.sku" value="${domain.whSku.sku}"/>
    <input type="hidden" id="images" value=""/>
    <video id="video" width="500px" height="500px" autoplay="autoplay"></video>
    <canvas id="canvas" width="500px" height="500px" style="display: none;margin-left: 75px"></canvas>
    <span class="btn green" onclick="takePhoto()" style="margin-left: 10px ">
        <i class="icon-camera"></i>
        <span>拍照</span>
    </span>
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.region.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript">
    var cameraOppen = false;
    getMedia();

    function getMedia() {
        let constraints = {
            video: {width: 500, height: 500},
            audio: true
        };
        //获得video摄像头区域
        let video = document.getElementById("video");
        //这里介绍新的方法，返回一个 Promise对象
        // 这个Promise对象返回成功后的回调函数带一个 MediaStream 对象作为其参数
        // then()是Promise对象里的方法
        // then()方法是异步执行，当then()前的方法执行完后再执行then()内部的程序
        // 避免数据没有获取到
        let promise = navigator.mediaDevices.getUserMedia(constraints).catch(function (err) {
            //Only secure origins are allowed (see: https://goo.gl/Y0ZkNV).
            if (err.message.indexOf("Only secure origins are allowed") != -1) {
                layer.alert("相机启动失败, 当前浏览器不支持: " + err.message, 'error');
            } else if (err.message.indexOf("Requested device not found") != -1) {
                //NotFoundError,Requested device not found,
                layer.alert("相机启动失败, 请确认摄像头线缆是否插好: " + err.name + ", " + err.message, 'error');
            } else {
                layer.alert("相机启动失败: " + err.name + ", " + err.message, 'error');
            }
        });

        promise.then(function (MediaStream) {
            if (MediaStream) {
                video.srcObject = MediaStream;
                video.play();
                cameraOppen = true;
                $("#exception-camera").css("display", "block");
            }
        });
    }

    function takePhoto() {
        if (!cameraOppen) {
            layer.alert("相机没有准备好!", 'error');
            return;
        }
        //获得Canvas对象
        let video = document.getElementById("video");
        let canvas = document.getElementById("canvas");
        let ctx = canvas.getContext('2d');
        ctx.drawImage(video, 0, 0, 500, 500);
        var imgData = canvas.toDataURL("image/jpg");
        //将图像转换为base64数据
        var base64Data = imgData.split(",")[1];
        let sku = $("input[name='whSku.sku']").val();
        $.ajax({
            url: CONTEXT_PATH + "skus/uploadPackImageFromCamera",
            type: "POST",
            data: {sku: sku, imageStr: base64Data},
            success: function (data) {
                if (data.status == 200) {
                    images = JSON.parse(data.message);
                    layer.alert("照片上传成功!");
                    currentImageIndex = images.length - 1;
                    $('#display-image').attr('src', images[currentImageIndex]);
                    $('.span-img-length').text(images.length);
                    $('.span-img-index').text(currentImageIndex + 1);
                    $('#images').val(data.message);
                } else {
                    layer.alert("照片上传失败: " + data.message, 'error');
                }
            },
            error: function () {
                layer.alert('照片上传图片失败!', 'error');
            }
        });
    }
</script>
</body>
</html>