<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
	<@header method="header" active="11040000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">SKU</a></li>
					<li class="active">扫码补标</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<div class="row" style="padding-top: 80px;height: 760px">
			<div class="col-md-10">
				<form action="${CONTEXT_PATH}skus/printSkuQRCodeByScanSku"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="skuForm">
					<div class="form-body" style="width: 800px;margin: 0 auto;">
						<div class="form-group">
							<label class="control-label col-md-3">扫码打码：</label>
							<div class="col-md-8">
								<input class="form-control" type="text" id="scanCode" name="scanCode" onkeypress="if(event.keyCode==13) { scanSku(this); return false;}">
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-3"></label>
							<div class="col-md-8" style="color: grey;">
								说明：扫描唯一码生成新的唯一码打印
							</div>
						</div>
						<br/>
						<br/>
						<br/>
						<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list">
							<colgroup>
								<col width="10%" />
								<col width="20%" />
								<col width="25%" />
								<col width="25%" />
								<col width="25%" />
							</colgroup>
							<thead>
								<tr>
									<th>序号</th>
									<th>产品图片</th>
									<th>SKU</th>
									<th>二维码</th>
									<th>唯一码</th>
								</tr>
							</thead>
							<tbody id="check_scan_datas">

								<!--<tr style="text-align: center">
									<td>1</td>
									<td>3TT700280-BK</td>
									<td>
										<div style="width: 13mm;margin-left: 18mm;">
											<img src="${CONTEXT_PATH}servlet/qrcode?size=50&keycode=3TT700280-BK=190801000005">
										</div>
									</td>
									<td>3TT700280-BK=190801000005</td>
								</tr>-->

							</tbody>
						</table>
					</div>
				</form>
			</div>
			<!-- 打印区 -->
			<div class="col-md-2" style="border-left: 1px solid #eee">
				<!--<div class="panel-header" style="width: 100px;padding-top: 20px">
					<div class="panel-title"><i class=" icon-print"></i> 打印区</div>
					<div class="panel-icon icon -reload"></div>
					<div class="panel-tool"></div>
				</div>-->
				<div class="easyui-panel panel-body" data-options="iconCls:'icon-print'" title="">
					<div id="print_info"></div>

					<iframe style="width:220px;height:400px;border: 0 none;margin-top: 2px;" name="printHtml" id="printHtml"></iframe>

					<div id="idContainer"></div>

					<form action="" method="post" target="_self" id="print_form"></form>

					<!-- 加载打印内容 -->
					<div id="print" style="overflow: auto;max-height: 500px;">

					</div>
				</div>
			</div>
		</div>

	</div>
	<#include "/common/footer.html">
		<img id="enlarge" style='position:absolute;width:80%;height:90%;top:10%;left:15%;display:none;'/>
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.region.js"></script>
	<script type="text/javascript">
		var num = 0;
        jQuery(document).ready(function() {
            pageInit();
        });

        // 初始化
        function pageInit() {
            $('#scanCode').val('');
            $('#scanCode').focus();
            $('#check_scan_datas').html('');
        }

        function scanSku(obj){
            var scanCode = $('#scanCode').val();
            if(!scanCode || scanCode.trim() == ''){
                layer.alert("请输入有效码!",'error');
                return false;
            }
            if(scanCode.indexOf("=") == -1){
                layer.alert("扫描的不是唯一码，请扫描唯一码！",'error');
                $('#scanCode').val('');
                return false;
            }
            $.ajax({
                url:CONTEXT_PATH+"skus/printSkuQRCodeByScanSku",
                type:"POST",
                data:{
                    scanCode:scanCode.trim()
                },
                success : function(response){
                    if (response.status == '500') {
                        customizeLayer(response.message, 'error');
                        $('#scanCode').val('');
                        $('#scanCode').focus();
                        return;
                    } else if (response.status == '200') {
                        /*num += 1;*/
                        var whSku = JSON.parse(response.message);
                        var uuid = response.location;
                        var html = "<tr style='text-align: center' id='tr_"+ num +"'>";
                        html +="<td>" + num + "</td>";
                        html +="<td>";
                        html +="<img src="+whSku.imageUrl+" width='50px' height='50px;' onclick='enlarge(this)' />";
                        html +="</td>";
                        html +="<td>" + whSku.sku + "</td>";
                        html +="<td><div style='width: 13mm;margin-left: 18mm;'>";
                        html +="<img src='${CONTEXT_PATH}servlet/qrcode?size=50&keycode=" + uuid + "'>";
                        html +="</td>";
                        html +="<td>" + uuid + "</td>";
                        html +="</tr>" + + "</td>";
                        $("#check_scan_datas").html(html);
                        //printSKU(uuid);
                        // iframe跳转打印页面
                        var printPageUrl = '${CONTEXT_PATH}skus/printSkuQRCodeByUuidSku?';
                        $('#printHtml').attr('src', printPageUrl + "&" + "scanCode="+uuid);
                        //自动打印
                        setTimeout(IframeOnloadPrint, 100);
                    }
                    $('#scanCode').val('');
                    $('#scanCode').focus();
                },
                error:function(){
                    $('#scanCode').val("");
                    $('#scanCode').focus();
                    layer.alert('扫描失败，请重新扫描', 'error');
                }
            });

        }

        function printSKU(scanCode){
            $.ajax({
                url:CONTEXT_PATH+"skus/printSkuQRCodeByUuidSku",
                type:"POST",
                data:{
                    scanCode:scanCode.trim()
                },
                beforeSend : function() {
                    App.blockUI();
                },
                success : function(response){
                    App.unblockUI();
                    if (response.status == '500') {
                        customizeLayer(response.message, 'error');
                        $('#scanCode').val('');
                        $('#scanCode').focus();
                        return;
                    } else if (response.status == '200') {
                        $('#scanCode').val('');
                        $('#scanCode').focus();
                    }

                    if (response.match("^\{(.+:.+,*){1,}\}$")) {
                        var jsonObject = eval("(" + response + ")");
                        $.error(jsonObject.message);
                        return;
                    }
                    /*var responseHtml = $(response).find("#check_scan_datas").html();
                    $("#check_scan_datas").html(responseHtml);
                    var purchaseInfo = $("#check_scan_datas").find(".check_scan_datas-info").text();*/

                    $('#scanCode').val('');
                    $('#scanCode').focus();
                },
                error:function(){
                    App.unblockUI();
                    $('#scanCode').val("");
                    $('#scanCode').focus();
                    layer.alert('扫描失败，请重新扫描', 'error');
                }
            });
		}

        // 这里Go
        var printed = false;
        function IframeOnloadPrint(){
            var iframe=document.getElementById("printHtml");
            // 加载完iframe后执行
            if (iframe.attachEvent){
                iframe.attachEvent("onload", function(){
                    printed = true;
                    myPrint();
                });
            } else {
                iframe.onload = function(){
                    printed = true;
                    setTimeout(myPrint, 500);/*延时0.5秒打印*/
                    return;
                };
            }

            printed = false;
        }

        /** 打印 **/
        var LODOP; //声明为全局变量
        function myPrint() {
            App.unblockUI();

            //先判断 内页中是否有 打印 方法 有的话直接调用
            try{
                if(typeof(eval(window.frames["printHtml"].myPrint))=='function'){
                    return window.frames["printHtml"].myPrint();
                }
            }catch(e){
            }

            try{
                CreatePrintPage();
                LODOP.PRINT();
            }
            catch(e){

            }
        };

        // 产品图放大
        function enlarge(obj){
            var url = $(obj).attr("src");
            $("#enlarge").attr("src", url);
            $("#enlarge").show(300);
        }
        $("#enlarge").click(function() {$("#enlarge").hide(100);});
	</script>
</body>
</html>