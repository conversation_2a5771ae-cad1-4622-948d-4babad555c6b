<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <#include "/common/include.html">
    <link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>

    <style type="text/css">
        .top_bar{
            position:fixed;top:0px;
        }
        #task-list td {
            vertical-align:middle;
        }
        .table thead > tr > th{
            vertical-align:middle;
        }
        .modal{
            margin-top: 60px;
        }
        #scanCodeModal .modal-body{
            text-align: center;
            padding-bottom: 40px;
        }
        #myModal .input-group-addon:not(.black) {
            background-color: transparent;
            border: none;
        }
        .input-group.border {
            border-color: #e5e5e5;
        }
        .input-group.border input{
            height: 20px;
            margin-bottom: 3px;
            width: 50px;
        }
        .input-group.border span:not(.input-group-addon) {
            line-height: 30px;
        }
        .my-dropdown{
            width: 90%;
            position: absolute;
            margin-left: 10px;
            top: 60%;
            text-align: center;
        }
        .my-dropdown li{
            line-height: 30px;
        }
        .my-dropdown li:hover{
            cursor: pointer;
            color: #0b94ea;
        }
        .my-dropdown div{
            width: 30%;
            float: left;
        }
        #dialog-form{
            margin-bottom: 60px;
        }
        #dialog-form tr{
            line-height: 60px;
        }
    </style>
</head>
<body>
<@header method="header" active="11040000"><#include "/ftl/header.ftl"></@header>
<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li><a href="#">管理单品</a></li>
                <li class="active">SKU列表</li>
            </ul>
        </div>
    </div>
    <div class="container-fluid" style="background-color: white;border: none">
        <div class="row">
            <div class="col-md-12">
                <form action="${CONTEXT_PATH}skus/search"
                           class="form-horizontal form-bordered form-row-stripped"
                           method="post" modelAttribute="domain" id="domain" name="checkInForm">
                    <!-- 分页信息 -->
                    <input id="page-no" type="hidden" name="page.pageNo" value="1">
                    <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
                    <input id="compareSkuWarehouseId" type="hidden" name="compareSkuWarehouseId" value="${domain.compareSkuWarehouseId}">
                    <div class="form-group">
                        <label class="control-label col-md-1">SKU</label>
                        <div class="col-md-3">
                            <input id="uuidSku" class="form-control" type="text" name="query.skuStr" placeholder="请输入SKU" value="${domain.query.skuStr}" onkeypress="if(event.keyCode==13) { inputSku(this); return false;}">
                        </div>
                        <label class="control-label col-md-1">录入时间</label>
                        <div class="col-md-3">
                            <div class="input-group">

                                <input class="form-control Wdate" type="text" id="enrollBeginDate" name="query.enrollBeginDate" value="${domain.query.enrollBeginDate}" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                <span class="input-group-addon">到</span>
                                <input class="form-control Wdate" type="text" id="enrollEndDate"  name="query.enrollEndDate" value="${domain.query.enrollEndDate}" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                            </div>
                        </div>
                        <label class="control-label col-md-1">最后修改时间</label>
                        <div class="col-md-3">
                            <div class="input-group">
                                <input class="form-control Wdate" type="text" id="lastUpdateBeginDate" name="query.lastUpdateBeginDate" value="${domain.query.lastUpdateBeginDate}" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                <span class="input-group-addon">到</span>
                                <input class="form-control Wdate" type="text" id="lastUpdateEndDate" name="query.lastUpdateEndDate" value="${domain.query.lastUpdateEndDate}" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-1">SKU名称</label>
                        <div class="col-md-3">
                            <input class="form-control" type="text" name="query.likeSkuName" placeholder="请输入SKU名称" value="${domain.query.likeSkuName}">
                        </div>
                        <label class="control-label col-md-1">修改次数</label>
                        <div class="col-md-3">
                            <input class="form-control" name="query.goodsQuantity" type="number" value="${domain.query.goodsQuantity}">
                        </div>
                        <label class="control-label col-md-1">库区</label>
						<div class="col-md-3">
                            <input class="form-control" name="query.locationRegion" type="text" value="${domain.locationRegionStr}">
						</div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-1">单品状态</label>
                        <div class="col-md-3">
                            <input class="form-control" type="text" name="query.singleStatus" placeholder="单品状态" value="${domain.query.singleStatus}">
                        </div>
                        <label class="control-label col-md-1">销售属性</label>
                        <div class="col-md-3">
                            <input class="form-control" type="text" name="query.saleStatus" placeholder="销售属性" value="${domain.query.saleStatus}">
                        </div>

                        <label class="control-label col-md-1">7天销量</label>
                        <div class="col-md-3 input-group">
                            <input type="number" step="0.01" name="query.fromSevenDaysSaleQuantity" value="${domain.query.fromSevenDaysSaleQuantity }" class="Wdate form-control" digits="true"/>
                            <span class="input-group-addon">到</span>
                            <input type="number" step="0.01" name="query.toSevenDaysSaleQuantity" value="${domain.query.toSevenDaysSaleQuantity }" class="Wdate form-control" digits="true"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-1">14天销量</label>
                        <div class="col-md-3 input-group">
                            <input type="number" step="0.01" name="query.fromFourteenDaysSaleQuantity" value="${domain.query.fromFourteenDaysSaleQuantity }" class="Wdate form-control" digits="true"/>
                            <span class="input-group-addon">到</span>
                            <input type="number" step="0.01" name="query.toFourteenDaysSaleQuantity" value="${domain.query.toFourteenDaysSaleQuantity }" class="Wdate form-control" digits="true"/>
                        </div>
                        <label class="control-label col-md-1">30天销量</label>
                        <div class="col-md-3 input-group">
                            <input type="number" step="0.01" name="query.fromThirtyDaysSaleQuantity" value="${domain.query.fromThirtyDaysSaleQuantity }" class="Wdate form-control" digits="true"/>
                            <span class="input-group-addon">到</span>
                            <input type="number" step="0.01" name="query.toThirtyDaysSaleQuantity" value="${domain.query.toThirtyDaysSaleQuantity }" class="Wdate form-control" digits="true"/>
                        </div>
                        <label class="control-label col-md-1">90天销量</label>
                        <div class="col-md-3 input-group">
                            <input type="number" step="0.01" name="query.fromNinetyDaysSaleQuantity" value="${domain.query.fromNinetyDaysSaleQuantity }" class="Wdate form-control" digits="true"/>
                            <span class="input-group-addon">到</span>
                            <input type="number" step="0.01" name="query.toNinetyDaysSaleQuantity" value="${domain.query.toNinetyDaysSaleQuantity }" class="Wdate form-control" digits="true"/>
                        </div>
                    </div>
                    <div class="form-group">
                            <label class="control-label col-md-1">称重重量范围</label>
                            <div class="col-md-3 input-group">
                                <input type="number" step="0.01" name="query.customsBeginWeight" id="customsBeginWeight" value="${domain.query.customsBeginWeight }" class="Wdate form-control" digits="true"/>
                                <span class="input-group-addon">到</span>
                                <input type="number" step="0.01" name="query.customsEndWeight" id="customsEndWeight" value="${domain.query.customsEndWeight }" class="Wdate form-control" digits="true"/>
                            </div>
                            <label class="control-label col-md-1">核重重量差范围</label>
                            <div class="col-md-3 input-group">
                                <input type="number" step="0.01" name="query.differBeginWeight" id="differBeginWeight" value="${domain.query.differBeginWeight }" class="Wdate form-control" digits="true"/>
                                <span class="input-group-addon">到</span>
                                <input type="number" step="0.01" name="query.differEndWeight" id="differEndWeight" value="${domain.query.differEndWeight }" class="Wdate form-control" digits="true"/>
                            </div>
                            <label class="control-label col-md-1">仓库</label>
                            <div class="col-md-3 input-group">
                                <select name="query.warehouseIdInt" class="form-control" value="${domain.query.warehouseIdInt}" id="warehouse-id">
                                    <option value=""></option>
                                    <option value="1">汉海达仓</option>
                                    <option value="2">美景仓</option>
                                </select>
                            </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-md-1">库位</label>
                        <div class="col-md-3 input-group">
                            <input class="form-control" type="text" name="query.locationStr" placeholder="请输入库位" value="${domain.query.locationStr}" >
                        </div>

                        <label class="control-label col-md-1">加工属性</label>
                        <div class="col-md-3">
                            <input class="form-control" type="text" name="query.floorLocationCode"  value="${domain.query.floorLocationCode}">
                        </div>

                        <label class="control-label col-md-1">是否备货</label>
                        <div class="col-md-3 input-group">
                            <!--是否备货，1表示不备货，0表示备货-->
                            <select name="query.noStockUp" class="form-control" value="${domain.query.noStockUp}" id="sku-noStockUp">
                                <option value=""></option>
                                <option value="0">备货</option>
                                <option value="1">不备货</option>
                            </select>
                        </div>

                    </div>

                    <div class="form-group">
                        <label class="control-label col-md-1">分类搜索</label>
                        <div class="col-md-3 input-group">
                            <input class="form-control" type="text" name="query.categoryPath" placeholder="请输入分类路径" value="${domain.query.categoryPath}" >
                        </div>
                        <label class="control-label col-md-1">贵重物品(产品)</label>
                        <div class="col-md-3 input-group">
                            <select class="form-control" name="query.valuables" type="text" value="${domain.query.valuables}">
                                <option value=""></option>
<#--                                <option value="3" <#if domain.query.valuables == 3>selected="selected"</#if> >贵重物品(wms)</option>-->
                                <option value="1" <#if domain.query.valuables == 1>selected="selected"</#if> >是</option>
                                <option value="0" <#if domain.query.valuables == 0>selected="selected"</#if> >否</option>
                            </select>
                        </div>
                        <label class="control-label col-md-1">是否保质期SKU</label>
                        <div class="col-md-3 input-group">
                            <select name="query.skuExpirationDate" class="form-control" type="text" value="${domain.query.skuExpirationDate}" >
                                <option value=""></option>
                                <option value="0" <#if domain.query.skuExpirationDate == 0>selected="selected"</#if> >否</option>
                                <option value="1" <#if domain.query.skuExpirationDate == 1>selected="selected"</#if> >是</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-1">产品标识</label>
                        <div class="col-md-3">
                            <input class="form-control" type="text" name="query.productLogoCode"  value="${domain.query.productLogoCode}">
                        </div>
                        <label class="control-label col-md-1">产品标签</label>
                        <div class="col-md-3">
                            <input class="form-control" type="text" name="query.tags" placeholder="请输入产品标签" value="${domain.query.tags}">
                        </div>
                        <label class="control-label col-md-1">仓库标签</label>
                        <div class="col-md-3">
                            <#assign tags><#if domain.query.skuTagList??><#list domain.query.skuTagList as tag>${tag}<#if tag_has_next>,</#if></#list><#else></#if></#assign>
                            <input class="form-control" type="text" name="query.skuTagStr" id="skuTagStr" value="<#if tags??>${tags}</#if>">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-1">包装标准图片</label>
                        <div class="col-md-3">
                            <select name="query.packImageFilter" class="form-control" type="text" value="${domain.query.packImageFilter}" >
                                <option value=""></option>
                                <option value="true" <#if domain.query.packImageFilter == true>selected="selected"</#if> >有</option>
                                <option value="false" <#if domain.query.packImageFilter?? && domain.query.packImageFilter == false>selected="selected"</#if> >无</option>
                            </select>
                        </div>
                        <label class="control-label col-md-1">是否不规则</label>
                        <div class="col-md-3">
                            <select name="query.specification" placeholder="是表示不规则，否表示规则" class="form-control" type="text" value="${domain.query.specification}" >
                                <option value=""></option>
                                <option value="true" <#if domain.query.specification == 'true'>selected="selected"</#if> >是</option>
                                <option value="false" <#if domain.query.specification?? && domain.query.specification == 'false'>selected="selected"</#if> >否</option>
                            </select>
                        </div>
                        <label class="control-label col-md-1">最近返架完成时间</label>
                        <div class="col-md-3">
                            <div class="input-group">
                                <input class="form-control Wdate" type="text" id="lastUpdateBeginDate" name="query.lastReturnBeginDate" value="${domain.query.lastReturnBeginDate}" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                <span class="input-group-addon">到</span>
                                <input class="form-control Wdate" type="text" id="lastUpdateEndDate" name="query.lastReturnEndDate" value="${domain.query.lastReturnEndDate}" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-1">净重重量</label>
                        <div class="col-md-3 input-group">
                            <input type="number" step="0.01" name="query.minWeight" value="${domain.query.minWeight }" class="Wdate form-control" digits="true"/>
                            <span class="input-group-addon">到</span>
                            <input type="number" step="0.01" name="query.maxWeight" value="${domain.query.maxWeight }" class="Wdate form-control" digits="true"/>
                        </div>
                        <label class="control-label col-md-1">产品规格长度</label>
                        <div class="col-md-3 input-group">
                            <input type="number" step="0.01" name="query.minLength" value="${domain.query.minLength }" class="Wdate form-control" digits="true"/>
                            <span class="input-group-addon">到</span>
                            <input type="number" step="0.01" name="query.maxLength" value="${domain.query.maxLength }" class="Wdate form-control" digits="true"/>
                        </div>
                        <label class="control-label col-md-1">包装属性</label>
                        <div class="col-md-3">
                            <input class="form-control" type="text" name="query.packageAttribute"  value="${domain.query.packageAttribute}">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-1">标准尺寸更新次数</label>
                        <div class="col-md-3 input-group type-label">
                            <input type="number" name="query.minStandardSizeChangeCount" value="${domain.query.minStandardSizeChangeCount }" class="Wdate form-control" digits="true"/>
                            <span class="input-group-addon">到</span>
                            <input type="number" name="query.maxStandardSizeChangeCount" value="${domain.query.maxStandardSizeChangeCount }" class="Wdate form-control" digits="true"/>
                        </div>
                        <label class="control-label col-md-1">sku特殊标签</label>
                        <div class="col-md-3">
                            <input class="form-control" type="text" name="query.skuSpecialTagCode"  value="${domain.query.skuSpecialTagCode}">
                        </div>
                        <label class="control-label col-md-1">是否一品多位</label>
                        <div class="col-md-3">
                            <select name="query.skuMoreLocation" placeholder="是表示一品多位" class="form-control" type="text" value="${domain.query.skuMoreLocation}" >
                                <option value=""></option>
                                <option value="true" <#if domain.query.skuMoreLocation == 'true'>selected="selected"</#if> >是</option>
                                <option value="false" <#if domain.query.skuMoreLocation?? && domain.query.skuMoreLocation == 'false'>selected="selected"</#if> >否</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-1">是否有可用库存</label>
                        <div class="col-md-3">
                            <select name="query.hasSurplusStock" placeholder="是否有可用库存" class="form-control" type="text" value="${domain.query.hasSurplusStock}" >
                                <option value=""></option>
                                <option value="true" <#if domain.query.hasSurplusStock?? && domain.query.hasSurplusStock?string == 'true'>selected="selected"</#if> >是</option>
                                <option value="false" <#if domain.query.hasSurplusStock?? && domain.query.hasSurplusStock?string == 'false'>selected="selected"</#if> >否</option>
                            </select>
                        </div>

                        <label class="control-label col-md-1">SPU</label>
                        <div class="col-md-3">
                            <input class="form-control" type="text" name="query.spu" placeholder="请输入spu,多个请按英文逗号分隔" value="${domain.query.spu}" onkeypress="if(event.keyCode==13) { inputSku(this); return false;}">
                        </div>
                    </div>
                    <div style="padding-top: 10px">
                        <div class="pull-left col-md-10" style="margin-left: 10px;line-height:40px;">

                            <@header method="auth" authCode="OTHER_SKU_UPDATE">
                            <button type="button" class="btn btn-default" onclick="batchSyncSku();">
                                <span class="icon-refresh"></span> 更新
                            </button>
                            </@header>

                            <@header method="auth" authCode="OTHER_SKU_BATCH_PRINT_SKU_TAG">
                            <button id="printSkus" type="button" class="btn btn-default">
                                <span class="icon-print"></span> 批量打印SKU标签
                            </button>
                            </@header>

                            <@header method="auth" authCode="OTHER_SKU_WAREHOUSE_ATTRIBUTE_SET">
                            <button id="saleAttributeSetting" type="button" class="btn btn-default" data-toggle="modal" data-target="#myModal">
                                <span class="icon-refresh"></span> 仓库属性设置
                            </button>
                            </@header>

                            <@header method="auth" authCode="OTHER_SKU_SCAN_AND_PRINT_TAG">
                            <a class="btn btn-default" href='${CONTEXT_PATH}skus/toPrintSkuQRCodeByScanSku'>
                                <i class="icon-print"></i> 扫码打印标签
                            </a>
                            </@header>

                            <@header method="auth" authCode="OTHER_SKU_CHANGE">
                            <button id="batchEditProcess" type="button" class="btn btn-default" >
                                <span class="icon-gear"></span> 修改加工
                            </button>
                            </@header>


                            <@header method="auth" authCode="OTHER_SKU_GENERATE_WEIGH_TASK">
                            <button id="batchCreateVerifySkuWeightTask" type="button" class="btn btn-default" >
                                <span class="icon-ok"></span> 生成称重任务
                            </button>
                            </@header>
                            <@header method="auth" authCode="OTHER_SKU_SKU_MESSAGE_INPUT">
								<span class="btn btn-default fileinput-button">
									<span class="icon-plus"> SKU信息导入</span>
									<input type="file" name="file" onchange="importUpdateSkuInfo(this)" />
								</span>
                            </@header>
                            <div class="btn-group">
                                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                                    批量修改质检备注 <i class="icon-angle-down"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <@header method="auth" authCode="OTHER_SKU_BATCH_MODIFY_QC_REMARKS_DOWNLOAD_EXCEL">
                                    <li>
                                        <a class="btn btn-default" href="${CONTEXT_PATH}file/execl/import_update_sku_qc_category.xlsx">下载导入模板Excel</a>
                                    </li>
                                    </@header>
                                    <@header method="auth" authCode="OTHER_SKU_BATCH_MODIFY_QC_REMARKS">
                                    <span class="btn btn-default fileinput-button">
                                        <span class="icon-upload">批量修改质检备注</span>
                                        <input type="file" name="file" onchange="importUpdateSkuQcCategory(this)" />
                                    </span>
                                    </@header>
                                </ul>
                            </div>
                            <@header method="auth" authCode="OTHER_SKU_MODIFY_PRODUCT_SYSTEM_QC_REMARKS">
                            <button id="updateRemark" type="button" class="btn btn-default" >
                                <span class="icon-gear"></span> 修改产品系统质检备注
                            </button>
                            </@header>

                            <@header method="auth" authCode="OTHER_SKU_MODIFY_WAREHOUSE_QC_REMARKS">
                            <button id="updateSkuFeature" type="button" class="btn btn-default" >
                                <span class="icon-gear"></span> 修改仓库质检备注
                            </button>
                            </@header>

                            <@header method="auth" authCode="OTHER_SKU_MODIFY_STANDARD_WEIGHT">
                            <button id="updateNetWeight" type="button" class="btn btn-default">
                                <span class="icon-gear"></span> 修改标准重量
                            </button>
                            </@header>

                            <@header method="auth" authCode="OTHER_SKU_PRINT_EG_TAG">
                            <button id="printEurOrUkFlag" type="button" class="btn btn-default">
                                <span class="icon-print"></span> 打印欧代标签
                            </button>
                            </@header>
                            <@header method="auth" authCode="OTHER_SKU_PRINT_CE_TAG">
                                <button id="printCEFlag" type="button" class="btn btn-default">
                                    <span class="icon-print"></span> 打印CE标签
                                </button>
                            </@header>
                            <@header method="auth" authCode="OTHER_SKU_PRINT_OUT_PRODUCT_TAG">
                                <button id="printOutProductFlag" type="button" class="btn btn-default">
                                    <span class="icon-print"></span> 打印产品外标签
                                </button>
                            </@header>
                           <!-- <@header method="auth" authCode="OTHER_SKU_INPUT_QC_REMARK">
                            <span class="btn btn-default fileinput-button">
                                <span class="icon-plus"> 导入质检备注</span>
                                <input type="file" name="file" onchange="fileinputSubmit(this)" />
                            </span>
                            </@header>
                            <@header method="auth" authCode="OTHER_SKU_DOWNLOAD_INPUT_QC_REMARK_PARADIGM_EXCEL">
                            <a class="btn btn-default" href="${CONTEXT_PATH}file/execl/wh_sku_qc_category.xlsx">
                                 <i class="icon-download"></i> 下载导入质检备注模板Excel
                            </a>
                            </@header>-->
                            <@header method="auth" authCode="OTHER_SKU_DOWNLOAD_INPUT_SKU_MESSAGE_PARADIGM_EXCEL">
                            <a class="btn btn-default" href="${CONTEXT_PATH}file/execl/wh_sku_upload.xlsx">
                                <i class="icon-download"></i> 下载导入SKU信息导入模板Excel
                            </a>
                            </@header>
                            <@header method="auth" authCode="OTHER_SKU_BATCH_UPLOAD_IMAGES">
                            <!-- 所选要进行批量上传图片的skuId -->
                            <input type="hidden" name="selectedIds" id="selectedIds"/>
                            <span class="btn btn-default fileinput-button">
                                <span class="icon-plus" onclick="document.querySelector('#inputImage').click()">批量包装图片上传</span>
                                <input id="inputImage" name="images" type="file" multiple="multiple" onchange="fileChange(this);" style="display: none">
                            </span>
                            </@header>
                            <@header method="auth" authCode="OTHER_SKU_MODIFY_SKUS_TAGS">
                            <span class="btn btn-default fileinput-button">
                                <span class="icon-gear" onclick="modifySkusTags();">修改仓库标签</span>
                            </span>
                            </@header>

                            <@header method="auth" authCode="UPDATE_SKU_SPECIFICATION">
                            <button id="updateSpecification" type="button" class="btn btn-default" >
                                <span class="icon-gear"></span> 修改SKU是否不规则
                            </button>
                            </@header>

                        </div>
                        <div class="col-md-offset-10" style="text-align: right;margin-top:50px ">
                            <@header method="auth" authCode="OTHER_SKU_DOWNLOAD">
                            <button type="button" class="btn btn-default" onclick="downloadSkus()">
                                <i class="icon-download"></i> 导出
                            </button>
                            </@header>

                            <button type="button" class="btn btn-default" onclick="formReset(this)">
                                <i class="icon-refresh"></i> 重置
                            </button>
                            <button type="submit" class="btn blue" onclick="removeCompareSkuWarehouseId()">
                                <i class="icon-search"></i> 查询
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <br/>
        </div>

        <div class="row">
            <div id="fixedDiv" class="col-md-12">
                <table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab" style="table-layout: fixed;word-break:break-all; word-wrap:break-all;">
                    <colgroup>
                        <col width="5%"/>
                        <col width="5%"/>
                        <col width="5%"/>
                        <col width="3%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="3%"/>
                        <col width="3%"/>
                        <col width="4%"/>
                        <col width="7%"/>
                        <col width="5%"/>
                        <col width="3%"/>
                        <col width="4%"/>
                        <col width="3%"/>
                        <col width="3%"/>
                        <col width="3%"/>
                        <col width="3%"/>
                        <col width="3%"/>
                        <col width="3%"/>
                        <col width="3%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="3%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="5%"/>
                        <col width="4%"/>
                        <col width="5%"/>
                    </colgroup>
                    <thead>
                    <tr>
                        <th>全选/反选<br> <input type="checkbox" name="check-all" id="check-alls" onchange="checkChange(this)"/></th>
                        <th>图片</th>
                        <th>SKU</th>
                        <th>贵重<br>物品<br>(产品)</th>
                        <th>仓库标签</th>
                        <th>仓库<br>销售<br>属性</th>
                        <th>单品<br>状态</th>
                        <th>仓库</th>
                        <th>库位</th>
                        <th>标题</th>
                        <th>大类<br>名称</th>
                        <th>加工<br/>装袋</th>
                        <th>来货<br>是否<br>加工</th>
                        <th>7天<br>销量</th>
                        <th>14天<br>销量</th>
                        <th>30天<br>销量</th>
                        <th>90天<br>销量</th>
                        <th>销售<br>频次</th>
                        <th>动销<br>频次</th>
                        <th>净重(g)</th>
                        <th>标准<br>重量(g)</th>
                        <th>称重<br>重量(g)</th>
                        <th>核重<br>重量差<br/>(g)</th>
                        <th>是否<br>不规则</th>
                        <th>包材</th>
                        <th>最近返架<br/>完成时间</th>
                        <th>搭配耗材</th>
                        <th>标准尺<br>寸更新<br>次数</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="col-md-12" id="task-list-warp">

                <table class="table table-striped table-bordered table-hover table-condensed" id="task-list" style="table-layout: fixed;word-break:break-all; word-wrap:break-all;">
                    <colgroup>
                        <col width="5%"/>
                        <col width="5%"/>
                        <col width="5%"/>
                        <col width="3%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="3%"/>
                        <col width="3%"/>
                        <col width="4%"/>
                        <col width="7%"/>
                        <col width="5%"/>
                        <col width="3%"/>
                        <col width="4%"/>
                        <col width="3%"/>
                        <col width="3%"/>
                        <col width="3%"/>
                        <col width="3%"/>
                        <col width="3%"/>
                        <col width="3%"/>
                        <col width="3%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="3%"/>
                        <col width="4%"/>
                        <col width="4%"/>
                        <col width="5%"/>
                        <col width="4%"/>
                        <col width="5%"/>
                    </colgroup>
                    <thead>
                    <tr>
                        <th>全选/反选<br> <input type="checkbox" name="check-all" id="check-alls" onchange="checkChange(this)"/></th>
                        <th>图片</th>
                        <th>SKU</th>
                        <th>贵重<br>物品<br>(产品)</th>
                        <th>仓库标签</th>
                        <th>仓库<br>销售<br>属性</th>
                        <th>单品<br>状态</th>
                        <th>仓库</th>
                        <th>库位</th>
                        <th>标题</th>
                        <th>大类<br>名称</th>
                        <th>加工<br/>装袋</th>
                        <th>来货<br>是否<br>加工</th>
                        <th>7天<br>销量</th>
                        <th>14天<br>销量</th>
                        <th>30天<br>销量</th>
                        <th>90天<br>销量</th>
                        <th>销售<br>频次</th>
                        <th>动销<br>频次</th>
                        <th>净重(g)</th>
                        <th>标准<br>重量(g)</th>
                        <th>称重<br>重量(g)</th>
                        <th>核重<br>重量差<br/>(g)</th>
                        <th>是否<br>不规则</th>
                        <th>包材</th>
                        <th>最近返架<br/>完成时间</th>
                        <th>搭配耗材</th>
                        <th>标准尺<br>寸更新<br>次数</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list domain.whSkus as whSku>
                        <tr>
                            <td>
                                <label class="checkbox-inline"> <input name="outIds" id="outIds" value="${whSku.id}" text-align="center" class="ids" type="checkbox" /> ${whSku.id}
                                </label>
                            </td>


                            <td >
                                <#if (whSku.whSkuExtend.qcImageUrl)?? && whSku.whSkuExtend.qcImageUrl!=''>
                                    <img src="${whSku.whSkuExtend.qcImageUrl}" width="50px" height="50px;" onclick="enlarge(this)" />
                                <#else>
                                    <img src="${whSku.imageUrl}" width="50px" height="50px;" onclick="enlarge(this)" />
                                </#if>

                            </td>

                            <td>
                                <span id="${whSku.id}">${whSku.sku}</span>
                                <#if whSku.skuBarCode??>
                                    <br>
                                    <input type="hidden" id="${whSku.id}_bar_code" value="${whSku.skuBarCode}"/>
                                    <span style="color: #990000">${whSku.skuBarCode}</span>
                                </#if>
                                <input id="${whSku.id}_warehouseId" type="hidden" value="${whSku.warehouseId}"/>
                                <input id="${whSku.id}_feature" type="hidden" value="${whSku.feature}"/>
                                <span id="${whSku.id}_netWeight" style="display: none">${whSku.netWeight}</span>
                            </td>
                            <td>
                                <#if !(whSku.specialType)?? || whSku.specialType != '2009'>
                                    <span>否</span>
                                </#if>
<#--                                <#if whSku.cameraman?? && whSku.cameraman == 1>-->
<#--                                    <span>贵重物品(wms)</span><br>-->
<#--                                </#if>-->
                                <#if whSku.specialType?? && whSku.specialType == '2009'>
                                    <span>贵重物品(产品)</span><br>
                                </#if>
                            </td>
                            <td>
                                <#if whSku.whSkuExtend.tagList??>
                                    <#list whSku.whSkuExtend.tagList as tag>
                                        <span>${tag}</span>
                                        <#if tag_has_next>
                                            <hr/>
                                        </#if>
                                    </#list>
                                <#else>
                                    <span>-</span>
                                </#if>
                            </td>
                            <td>
                                <!--<#if whSku.saleAttributeSettingStr1 == ''>
                                    长呆滞
                                <#else>-->
                                    ${whSku.saleAttributeSettingStr1}
                               <!-- </#if>-->
                            </td>
                            <td>${util('enumDisplay','com.estone.sku.enums.WhSkuStatus',whSku.status+'')}</td>
                            <td>
                                <#if whSku.warehouseId == 1>
                                    汉海达仓
                                <#elseif whSku.warehouseId == 2>
                                    美景仓
                                <#elseif whSku.warehouseId == 3>
                                    南宁仓
                                <#else>
                                    ${whSku.warehouseId}
                                </#if>
                            </td>
                            <td>${whSku.locationNumber}</td>
                            <td id="${whSku.id}_title">${whSku.name}</td>
                            <td>${whSku.oneCategoryPath}</td>
                            <td>
                                 ${whSku.processTypeName}
                                <br/>
                                修改次数：${(whSku.goodsQuantity)! 0}
                            </td>
                            <td>
                                <#if whSku.checkInProcess != 1>
                                否
                                <#else>
                                是
                                </#if>
                            </td>
                            <td>${whSku.sevenDaysSaleQuantity}</td>
                            <td>${whSku.fourteenDaysSaleQuantity}</td>
                            <td>${whSku.thirtyDaysSaleQuantity}</td>
                            <td>${whSku.ninetyDaysSaleQuantity}</td>
                            <td>${whSku.thirtyDaysSalesOrders}</td>
                            <td>${whSku.thirtyDaysSalesDays}</td>
                            <td>
                                <#if whSku.weight??>
                                    ${whSku.weight}
                                </#if>
                            </td>
                            <td>
                                ${whSku.netWeight}
                            </td>
                            <td>
                                <#if whSku.customsWeight??>
                                    ${whSku.customsWeight}
                                </#if>
                            </td>
                            <td>
                                <#if whSku.customsWeight??>
                                    ${whSku.customsWeight-(whSku.weight)?default(0)}
                                </#if>
                            </td>
                            <td>
                                <#if whSku.specification=='true'>
                                    是
                               <#else >
                                    否
                                </#if>
                            </td>
                            <td>${whSku.packagingName}</td>
                            <td>${whSku.whSkuExtend.lastReturnCompleteDate}</td>
                            <td>${whSku.matchMaterialsName}</td>
                            <td>${whSku.standardSizeChangeCount}</td>
                            <td>
                                <a class="btn btn-xs btn-default" href="${CONTEXT_PATH}skus/skuDetail?whSkuId=${whSku.id }">
                                    <span class="icon-edit"></span>  查看详情
                                </a>
                                <@header method="auth" authCode="SKU_SYSTEM_LOG">
                                <button type="button" class="btn btn-xs btn-info" onclick="viewLog(${whSku.id}, 'sku')">日志</button>
                                </@header>

                                <@header method="auth" authCode="SKU_SYSTEM_ALL_LOG">
                                	<button type="button" class="btn btn-xs btn-info" onclick="viewLog(${whSku.id}, 'sku', 'true')">所有日志</button>
                                </@header>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
            </div>
        </div>
        <div id="fixed-bottom">
            <div id="pager"></div>
        </div>
    </div>
    <div id="processing-bagging" class="hide">
        <table id="condition" style="border: 10px;">
            <tr >
                <td>加工装袋</td>
                <td>
                    <select id="processingBagging">
                        <option></option>
                        <option value="true">是</option>
                        <option value="false">否</option>
                    </select>
                </td>
            </tr>
        </table>
    </div>
    <div id="dialog-form" title="打印份数" style="display: none">
        <table id="condition" style="border: 10px;">
            <tr>
                <td style="width: 20%;">打印份数：</td>
                <td style="width: 70%;">
                    <input class="form-control" type="text" name="printNum" id="printNum">
                </td>
            </tr>
            <tr>
                <td style="width: 20%;">补标原因：</td>
                <td style="width: 70%;">
                    <input class="form-control" type="text" name="printReason" id="printReason">
                </td>
            </tr>
        </table>
    </div>
    <div class="modal fade" id="updateNetWeightModal" tabindex="-1" role="dialog" aria-labelledby="updateNetWeightModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                    <h4 class="modal-title" id="updateNetWeightModalLabel">修改SKU标准重量（g）</h4>
                    <input type="hidden" id="checkedId" value=""/>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="form-body">
                            <div class="form-group" style="margin-bottom: 20px">
                                <label class="control-label col-md-3">原标准重量：</label>
                                <div class="col-md-7">
                                    <input id="oldNetWeight" style="color: red;font-size: large" readonly class="form-control" value=""/>
                                </div>
                            </div>
                            <br/>
                            <div class="form-group">
                                <label class="control-label col-md-3">新标准重量：</label>
                                <div class="col-md-7">
                                    <input class="form-control" type="text" name="newNetWeight" id="newNetWeight">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">
                        取消
                    </button>
                    <button type="button" class="btn btn-primary" id="saveNetWeight">
                        确认
                    </button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog" style="width:100%;">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                    <h4 class="modal-title" id="myModalLabel">仓库属性设置</h4>
                </div>
                <div class="modal-body">
                    <form id="attributeSettingForm">
                        <table class="table table-bordered">
                            <colgroup>
                                <col width="10%"/>
                                <col width="10%"/>
                                <col width="9%"/>
                                <col width="9%"/>
                                <col width="9%"/>
                                <col width="9%"/>
                                <col width="9%"/>
                                <col width="9%"/>
                                <col width="9%"/>
                                <col width="9%"/>
                                <col width="8%"/>
                            </colgroup>
                            <thead>
                                <tr>
                                    <th rowspan="2">30天销量</th>
                                    <th rowspan="2">90天销量</th>
                                    <th colspan="8" style="border-bottom:1px;">动销频次</th>
                                    <th rowspan="2">操作</th>
                                </tr>
                                <tr>
                                    <th>爆款</th>
                                    <th>热销</th>
                                    <th>畅销</th>
                                    <th>平销</th>
                                    <th>低销</th>
                                    <th>滞销</th>
                                    <th>短呆滞</th>
                                    <th>长呆滞</th>
                                </tr>
                            </thead>
                            <tbody id="myTemplate">
                                <#if (domain.saleAttributeSettingList)?? && domain.saleAttributeSettingList?size gt 0>
                                    <#list domain.saleAttributeSettingList as setting>
                                        <#if setting_has_next>
                                            <tr data-index="${setting_index}" data-level="${setting.level}">
                                                <td><div class="input-group"><input class="form-control" type="text" name="num[0]" value="${setting.numArray[0]}"><span class="input-group-addon">≥,></span><input class="form-control" type="text" name="num[1]" value="${setting.numArray[1]}"></div></td>
                                                <td><div class="input-group"><input class="form-control" type="text" name="num[2]" value="${setting.numArray[2]}"><span class="input-group-addon">≥,></span><input class="form-control" type="text" name="num[3]" value="${setting.numArray[3]}"></div></td>
                                                <td><div class="input-group"><input class="form-control" type="text" name="num[4]" value="${setting.numArray[4]}"><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[5]" value="${setting.numArray[5]}"></div></td>
                                                <td><div class="input-group"><input class="form-control" type="text" name="num[6]" value="${setting.numArray[6]}"><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[7]" value="${setting.numArray[7]}"></div></td>
                                                <td><div class="input-group"><input class="form-control" type="text" name="num[8]" value="${setting.numArray[8]}"><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[9]" value="${setting.numArray[9]}"></div></td>
                                                <td><div class="input-group"><input class="form-control" type="text" name="num[10]" value="${setting.numArray[10]}"><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[11]" value="${setting.numArray[11]}"></div></td>
                                                <td><div class="input-group"><input class="form-control" type="text" name="num[12]" value="${setting.numArray[12]}"><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[13]" value="${setting.numArray[13]}"></div></td>
                                                <td><div class="input-group"><input class="form-control" type="text" name="num[14]" value="${setting.numArray[14]}"><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[15]" value="${setting.numArray[15]}"></div></td>
                                                <td><div class="input-group"><input class="form-control" type="text" name="num[16]" value="${setting.numArray[16]}"><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[17]" value="${setting.numArray[17]}"></div></td>
                                                <td><div class="input-group"><input class="form-control" type="text" name="num[18]" value="${setting.numArray[18]}"><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[19]" value="${setting.numArray[19]}"></div></td>
                                                <td align="center"><a data-action="addTemplate">添加</a></td>
                                            </tr>
                                        </#if>
                                    </#list>
                                <#else>
                                    <tr data-index="0" data-level="0">
                                        <td><div class="input-group"><input class="form-control" type="number" name="num[0]" value=""><span class="input-group-addon">≥,></span><input class="form-control" type="text" name="num[1]" value=""></div></td>
                                        <td><div class="input-group"><input class="form-control" type="text" name="num[2]" value=""><span class="input-group-addon">≥,></span><input class="form-control" type="text" name="num[3]" value=""></div></td>
                                        <td><div class="input-group"><input class="form-control" type="text" name="num[4]" value=""><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[5]" value=""></div></td>
                                        <td><div class="input-group"><input class="form-control" type="text" name="num[6]" value=""><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[7]" value=""></div></td>
                                        <td><div class="input-group"><input class="form-control" type="text" name="num[8]" value=""><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[9]" value=""></div></td>
                                        <td><div class="input-group"><input class="form-control" type="text" name="num[10]" value=""><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[11]" value=""></div></td>
                                        <td><div class="input-group"><input class="form-control" type="text" name="num[12]" value=""><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[13]" value=""></div></td>
                                        <td><div class="input-group"><input class="form-control" type="text" name="num[14]" value=""><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[15]" value=""></div></td>
                                        <td><div class="input-group"><input class="form-control" type="text" name="num[16]" value=""><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[17]" value=""></div></td>
                                        <td><div class="input-group"><input class="form-control" type="text" name="num[18]" value=""><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[19]" value=""></div></td>
                                        <td align="center"><a data-action="addTemplate">添加</a></td>
                                    </tr>
                                </#if>
                            </tbody>
                        </table>
                        <div class="input-group border">
                            <span class="input-group-addon black">新品:</span>
                            <span>&nbsp;&nbsp;&nbsp;&nbsp;SKU创建时间在</span>
                            <input class="form-control" type="text" name="num1" value="${domain.saleAttributeSettingList[domain.saleAttributeSettingList?size - 1].num1}">
                            <span>天内 &nbsp;&nbsp;&nbsp;&nbsp;30天销量</span>
                            <input class="form-control" type="text" name="num2" value="${domain.saleAttributeSettingList[domain.saleAttributeSettingList?size - 1].num2}">
                            <span>≤,≤</span>
                            <input class="form-control" type="text" name="num3" value="${domain.saleAttributeSettingList[domain.saleAttributeSettingList?size - 1].num3}">
                            <span>&nbsp;&nbsp;&nbsp;&nbsp;动销频次</span>
                            <input class="form-control" type="text" name="num4" value="${domain.saleAttributeSettingList[domain.saleAttributeSettingList?size - 1].num4}">
                            <span>≤,≤</span>
                            <input class="form-control" type="text" name="num5" value="${domain.saleAttributeSettingList[domain.saleAttributeSettingList?size - 1].num5}">
                            <span>&nbsp;&nbsp;&nbsp;&nbsp;当SKU不满足此条件时则按照正常销量及动销频次计算销售属性</span>
                        </div>
                        <input type="hidden" name="paramId" id="paramId" value="${domain.systemParam.paramId}">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">
                        取消
                    </button>
                    <button type="button" class="btn btn-primary" id="saveAttributeSetting">
                        确认
                    </button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <div class="modal fade" id="scanCodeModal" tabindex="-1" role="dialog" aria-labelledby="scanCodeModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                    <h4 class="modal-title" id="scanCodeModalLabel">打印标签</h4>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="form-group">
                            <label class="control-label col-md-3">扫码打码：</label>
                            <div class="col-md-7">
                                <input class="form-control" type="text" id="scanCode" name="scanCode" onkeypress="if(event.keyCode==13) { scanSku(this); return false;}">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">
                        取消
                    </button>
                    <button type="button" class="btn btn-primary" id="toScanCode">
                        确认
                    </button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <div class="modal fade" id="updateRemarkModal" tabindex="-1" role="dialog" aria-labelledby="updateRemarkModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                    <h4 class="modal-title" id="updateRemarkModalLabel">修改产品系统质检备注</h4>
                </div>
                <div class="modal-body">
                    <form id="updateRemarkForm">
                    	<!-- 删除的质检备注 -->
						<input type="hidden" id="deleteSkuQcCategoryIds" value=""/>
						<input type="hidden" id="skuId" value=""/>
                    	<div class="form-body" style="margin-left: 7px;">
							<div class="form-group">
								<label class="control-label col-md-2">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SKU：</label>
								<div class="col-md-10" id="update-record-sku"></div>
							</div>
							<br/>
							<div class="form-group">
								<label class="control-label col-md-2">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;标题：</label>
								<div class="col-md-10" id="update-record-title"></div>
							</div>
							<br/>
							<div class="form-group">
								<label class="control-label col-md-2">产品系统质检备注:</label>
								<div class="col-md-10" id="update-record-remark">
									<table id="sku-qc-category" style="border-collapse:separate; border-spacing:10px;margin-top: -10px;"></table>
								</div>
							</div>
							<br/>
						</div>
                    </form>
                    <div style="text-align: right;">
	                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
	                    <button type="button" class="btn btn-primary" onclick="confirmUpdateRemark()">确认</button>
                    </div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <div class="modal fade" id="updateSkuFeatureModal" tabindex="-1" role="dialog" aria-labelledby="updateSkuFeatureModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                    <h4 class="modal-title" id="updateSkuFeatureModalLabel">修改仓库质检备注</h4>
                </div>
                <div class="modal-body">
                    <form id="updateSkuFeatureForm">
                        <!-- 删除的质检备注 -->
                        <input type="hidden" name="whSku.id" value=""/>
                        <input type="hidden" name="whSku.sku" value=""/>
                        <div class="form-body" style="margin-left: 7px;">
                            <div class="form-group">
                                <label class="control-label col-md-3" style="text-align: right">SKU：</label>
                                <div class="col-md-9" id="update-sku"></div>
                            </div>
                            <br/>
                            <div class="form-group">
                                <label class="control-label col-md-3" style="text-align: right">标题：</label>
                                <div class="col-md-9" id="update-sku-title"></div>
                            </div>
                            <br/>
                            <div class="form-group">
                                <label class="control-label col-md-3" style="text-align: right">仓库质检备注:</label>
                                <div class="col-md-9">
                                    <input class="form-control" name="whSku.feature" type="text" value="">
                                </div>
                            </div>
                            <br/>
                        </div>
                    </form>
                    <div style="text-align: right;">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="confirmUpdateSkuFeature()">确认</button>
                    </div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->

    <div class="modal fade" id="updateSkuBarCodeModal" tabindex="-1" role="dialog" aria-labelledby="updateSkuBarCodeModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                    <h4 class="modal-title" id="updateSkuBarCodeModalLabel">修改SKU编码</h4>
                    <input type="hidden" id="bar-code-sku-id" value=""/>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="form-body">
                            <div style="margin-bottom: 30px;" class="form-group">
                                <label class="control-label col-md-3">SKU：</label>
                                <div class="col-md-7" id="bar-code-sku"></div>
                            </div>
                            <br/>
                            <div style="margin-bottom: 30px;" class="form-group">
                                <label class="control-label col-md-3">标题：</label>
                                <div class="col-md-7" id="bar-code-title"></div>
                            </div>
                            <br/>
                            <div style="margin-bottom: 30px;" class="form-group">
                                <label class="control-label col-md-2">编码：</label>
                                <div class="col-md-7">
                                    <input type="hidden" id="dbSkuBarCode" value=""/>
                                    <input class="form-control" type="text" name="skuBarCode" id="skuBarCode" onkeypress="if(event.keyCode==13) {return false;}">
                                </div>

                            </div>
                            <br/>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">
                        取消
                    </button>
                    <button type="button" class="btn btn-primary" id="saveSkuBarCode">
                        确认
                    </button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->

    <div class="modal fade" id="updateSpecificationModal" tabindex="-1" role="dialog" aria-labelledby="updateSpecificationModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                    <h4 class="modal-title" id="updateSpecificationModalLabel">修改sku是否不规则</h4>

                </div>
                <div class="modal-body" style="height: 100px">
                    <form id="updateSpecificationForm">
                        <div class="form-body">
                            <label class="control-label col-md-3" >是否不规则</label>
                            <div class="col-md-7">
                                <select id="update-specification" style="width:200px;">
                                    <option></option>
                                    <option value="true">是</option>
                                    <option value="false">否</option>
                                </select>
                            </div>
                            <br/>
                        </div>
                        <div class="form-body">
                            <label class="control-label col-md-3">净重区间</label>
                            <div class="col-md-7 input-group">
                                <input type="number" step="0.1" id="netWeightMin"  placeholder="净重最小值" class="Wdate form-control" digits="true"/>
                                <span class="input-group-addon">到</span>
                                <input type="number" step="0.1" id="netWeightMax"  placeholder="净重最大值" class="Wdate form-control" digits="true"/>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmUpdateSpecification()">确认</button>
                </div>
            </div>
        </div>
    </div>

    <#include "/common/footer.html">
</div>
<script id="settingTemplate" type="text/x-jquery-tmpl">
    <tr data-index="" data-level="" data-name="">
        <td><div class="input-group"><input class="form-control" type="text" name="num[0]" value=""><span class="input-group-addon">≥,></span><input class="form-control" type="text" name="num[1]" value=""></div></td>
        <td><div class="input-group"><input class="form-control" type="text" name="num[2]" value=""><span class="input-group-addon">≥,></span><input class="form-control" type="text" name="num[3]" value=""></div></td>
        <td><div class="input-group"><input class="form-control" type="text" name="num[4]" value=""><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[5]" value=""></div></td>
        <td><div class="input-group"><input class="form-control" type="text" name="num[6]" value=""><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[7]" value=""></div></td>
        <td><div class="input-group"><input class="form-control" type="text" name="num[8]" value=""><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[9]" value=""></div></td>
        <td><div class="input-group"><input class="form-control" type="text" name="num[10]" value=""><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[11]" value=""></div></td>
        <td><div class="input-group"><input class="form-control" type="text" name="num[12]" value=""><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[13]" value=""></div></td>
        <td><div class="input-group"><input class="form-control" type="text" name="num[14]" value=""><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[15]" value=""></div></td>
        <td><div class="input-group"><input class="form-control" type="text" name="num[16]" value=""><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[17]" value=""></div></td>
        <td><div class="input-group"><input class="form-control" type="text" name="num[18]" value=""><span class="input-group-addon">≥,≥</span><input class="form-control" type="text" name="num[19]" value=""></div></td>
        <td align="center"><a data-action="addTemplate">添加</a></td>
    </tr>
</script>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript">

    $("#warehouse-id").val("${domain.query.warehouseIdInt}");
    $("#sku-noStockUp").val("${domain.query.noStockUp}");
    $("#sku-valuables").val("${domain.query.queryValuables}");
    <!--$("#saleAttributeValue").val("${domain.query.saleAttributeValue}");-->

    var statusArray =  ${domain.skuStatuses};
    $("input[name='query.singleStatus']").select2({
        data : statusArray,
        placeholder : "单品状态",
        multiple: true,
        allowClear : true
    });

    // 库区多选
    var locationRegionSelect = ${domain.locationRegionSelect};
    $("input[name='query.locationRegion']").select2({
        data : locationRegionSelect,
        placeholder : "库区",
        multiple: true,
        allowClear : true
    });

    var productLogoStr = ${domain.productLogoStr};
    $("input[name='query.productLogoCode']").select2({
        data : productLogoStr,
        placeholder : "状态",
        multiple: true,
        allowClear : true
    });

    var salesPropertys = ${domain.salesPropertys};
    $("input[name='query.saleStatus']").select2({
        data : salesPropertys,
        placeholder : "销售属性",
        multiple: true,
        allowClear : true
    });

    var floorLocationSelect=${domain.floorLocationSelect};
    $("input[name='query.floorLocationCode']").select2({
        data : floorLocationSelect,
        placeholder : "加工属性",
        multiple: true,
        allowClear : true
    });

    var skuSpecialTagSelect=${domain.skuSpecialTagSelect};
    $("input[name='query.skuSpecialTagCode']").select2({
        data : skuSpecialTagSelect,
        placeholder : "sku特殊标签",
        multiple: true,
        allowClear : true
    });

    var packageAttributes = ${domain.packageAttributes};
    $("input[name='query.packageAttribute']").select2({
        data : packageAttributes,
        placeholder : "包装属性",
        multiple: false,
        allowClear : true
    });

    function getSaleAttributeSetting(obj) {
        var saleAttributeType = $(obj).find("option:selected")[0].dataset.saleattributetype;
        var value = $(obj).find("option:selected")[0].dataset.value;
        $("input[name='query.saleAttributeType']").val(saleAttributeType);
        $("input[name='query.saleStatus']").val(value);
    }
    // 子选项
    var outIds = $("#task-list").find("input[name='outIds']");

    function checkChange(tar) {
        var f = $(tar).is(":checked");
        outIds.each(function() {
            $(this).attr('checked',f);
            if (f) {
                $(this).closest("tr").find("td").css("background-color", "#B1DB87");
            } else {
                $(this).closest("tr").find("td").css("background-color", "");
            }
        })
    }

    function getCheckedSkus(){
        var skus = $("input[name='query.skuStr']").val().trim();

        var outIds = $("#task-list").find("input[name='outIds']:checked");
        var selectSkus = "";
        for (var i = 0; i < outIds.length; i++) {
            var outId = outIds[i].value;
            sku = $("#" + outId).text();
            if (i == 0) {
                selectSkus += sku;
            } else {
                selectSkus += "," + sku;
            }
        }
        if (selectSkus != "") {
            skus = selectSkus;
        }
        return skus;
    }
    function getCheckedIds(){
        var outIds = $("#task-list").find("input[name='outIds']:checked");
        var checkIds = "";
        for (var i = 0; i < outIds.length; i++) {
            var outId = outIds[i].value;
            if (i == 0) {
                checkIds += outId;
            } else {
                checkIds += "," + outId;
            }
        }
        return checkIds;
    }
    function headersJoin(headers) {
        var checkHeaders = "";
        for (var i = 0; i < headers.length; i++) {
            var header = headers[i].value;
            if (i == 0) {
                checkHeaders += header;
            } else {
                checkHeaders += "," + header;
            }
        }
        return checkHeaders;
    }
    outIds.change(function() {
        var checkedLenght = outIds.filter(":checked").length;
        var length = outIds.length;
        // 全选
        var checkAll = $("#task-list").find("input[name='check-all']");
        checkAll.prop("checked", checkedLenght == length);

        // 改变背景色
        if ($(this).prop("checked")) {
            $(this).closest("tr").find("td").css("background-color", "#B1DB87");
        } else {
            $(this).closest("tr").find("td").css("background-color", "");
        }
    });

    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";
    $("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

    var heights = $("body").height();
    if(heights>910){
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }

    function removeCompareSkuWarehouseId() {
        $('#compareSkuWarehouseId').remove();
    }
    function updatePackagingMaterial(){
        $.ajax({
            url:CONTEXT_PATH+"sku/packagingMaterials/batchUpdate",
            type:"POST",
            success:function(result){
                if(result){
                    layer.alert("包材更新成功!");
                }else{
                    layer.alert("包材更新失败!");
                }
            }

        });
    }

    $('#batchEditProcess').on('click',function () {
        if (getCheckedIds().length == 0) {
            layer.alert("请先选择至少一个SKU！");
            return false;
        }
        dialog({
            title: '修改加工',
            url: CONTEXT_PATH + "skus/skuProcessingBaggingModel",
            width: 300,
            height: 120,
            top: 0,
            okValue: '确定',
            ok: function () {
                var exportWindow = $(this.iframeNode.contentWindow.document.body);
                var submitForm = exportWindow.find("#submit-form");

                var floorLocation = submitForm.find('#floorLocation').val();
                var checkInProcess = submitForm.find('#checkInProcess').val();

                // 导出当前选择
                if(floorLocation || checkInProcess) {
                    $.ajax({
                        url: CONTEXT_PATH + "skus/updateProcessingBagging",
                        type: "POST",
                        data: {ids: getCheckedIds(),floorLocation:floorLocation,checkInProcess:checkInProcess,istrue:"true"},
                        success: function(data){
                            layer.confirm(data.message,{
                                icon: 1,
                                btn: ['确定']
                            },function () {
                                location.reload();
                            })
                        }
                    });
                }
            },
            cancelValue: '关闭',
            cancel: function () {
                window.location.reload();
            }
        }).showModal();
    });

    $('#update-specification').change(function(){
        var bool = $(this).val();
        if (bool && bool=='false'){
            $('#netWeightMin').val(null);
            $('#netWeightMax').val(null);
            $('#netWeightMin').attr("disabled",true);
            $('#netWeightMax').attr("disabled",true);
        }
        if (bool && bool=='true'){
            $('#netWeightMax').attr("disabled",false);
            $('#netWeightMin').attr("disabled",false);
        }
    });


    //修改是否规则sku
    $('#updateSpecification').on('click',function () {
        var checkedIds = getCheckedIds();
        if (!checkedIds) {
            layer.alert("请先选择一个SKU！");
            return false;
        }
        var skuIdList = checkedIds.split(",");
        if (skuIdList.length != 1) {
            layer.alert("只能选择一个SKU！");
            return false;
        }
        $("#updateSpecificationModal").modal('show');
    });

    // 保存是否规则sku
    function confirmUpdateSpecification() {
        if (getCheckedIds().length == 0) {
            layer.alert("skuId为空");
            return false;
        }

        var specification = $("#updateSpecificationForm").find("#update-specification").val();
        if (!specification) {
            layer.alert("是否规则为空");
            return false;
        }

        var netWeightMax = $("#updateSpecificationForm").find("#netWeightMax").val();
        var netWeightMin = $("#updateSpecificationForm").find("#netWeightMin").val();

        if ("true"==specification &&  (!netWeightMax || !netWeightMin)) {
            layer.alert("修改为是不规则sku请填写sku净重区间");
            return false;
        }

        if ((netWeightMax.length>0 && netWeightMin.length<=0) || (netWeightMin.length>0 &&  netWeightMax.length<=0)){
            layer.alert("请填写完整sku净重区间");
            return false;
        }

        if (netWeightMax.length>0 && netWeightMin.length>0 &&  (netWeightMax<0 || netWeightMin<0)) {
            layer.alert("sku区间值不能为负数!");
            return false;
        }

        if (("true"==specification || (netWeightMax.length>0 && netWeightMin.length>0) ) &&  netWeightMax<netWeightMin) {
            layer.alert("净重最大值小于净重最小值");
            return false;
        }

        $.ajax({
            url: CONTEXT_PATH + "skus/updateSpecification",
            type: "POST",
            data: {ids: getCheckedIds(),specification:specification,netWeightMax:netWeightMax,netWeightMin:netWeightMin},
            success: function(data){
                layer.confirm(data.message,{
                    icon: 1,
                    btn: ['确定']
                },function () {
                    location.reload();
                })
            }
        });
    }
    $('#updateSpecificationModal').on('hide.bs.modal', function () {
        window.location.reload();
    });

    function updateCategory(){
        $.ajax({
            url:CONTEXT_PATH+"sku/categorys/batchUpdate",
            type:"POST",
            success:function(result){
                if(result){
                    layer.alert("类目更新成功!");
                }else{
                    layer.alert("类目更新失败!");
                }
            }
        });
    }

    function updateWhSku(){
        var enrollBeginDate=$("#enrollBeginDate").val().trim();
        var enrollEndDate=$("#enrollEndDate").val().trim();
        var lastUpdateBeginDate=$("#lastUpdateBeginDate").val().trim();
        var lastUpdateEndDate=$("#lastUpdateEndDate").val().trim();
        if(enrollBeginDate =="" && enrollEndDate=="" && lastUpdateBeginDate=="" && lastUpdateEndDate==""){
            layer.alert("请选择时间录入时候/最后修改时间");
            return;
        }

        if(enrollBeginDate > enrollEndDate){
            layer.alert("录入开始时间不能大于录入结束时间!");
            return;
        }

        if(lastUpdateBeginDate > lastUpdateEndDate){
            layer.alert("最后修改时间的开始时间不能大于最后修改时间的结束时间!")
            return;
        }

        $.ajax({
            url:CONTEXT_PATH+"skus/batchUpdate",
            type:"POST",
            data:{
                "enrollBeginDate":enrollBeginDate,
                "enrollEndDate":enrollEndDate,
                "lastUpdateBeginDate":lastUpdateBeginDate,
                "lastUpdateEndDate":lastUpdateEndDate
            },
            success:function(result){
                if(result){
                    layer.alert("SKU更新成功!");
                }else{
                    layer.alert("SKU更新失败!");
                }
            }
        });
    }

    function batchSyncSku() {
        if (getCheckedSkus().length == 0) {
            layer.alert("请输入需要同步的sku，多个sku以逗号分隔");
            return false;
        }
        $.ajax({
            url: CONTEXT_PATH+"skus/batchSyncSku",
            type: "POST",
            data: {skus: getCheckedSkus()},
            success: function(data){
                layer.alert(data.message);
                setTimeout(function () {
                    window.location.reload();
                }, 2000);
            }
        });
    }

    $(function () {
        refreshIndex();
    });
    $('#printSkus').on('click',function () {
        var outIds = $("#task-list").find("input[name='outIds']:checked");
        if (getCheckedIds().length == 0) {
            layer.alert("请先选择一个SKU！");
            return false;
        }
        if (outIds.length > 1) {
            layer.alert("只能选择一个SKU！");
            return false;
        }
        var localWarehouse = "${util('localWarehouseId')}";
        var skuIdList = getCheckedIds().split(",");
        for (var i = 0; i < skuIdList.length; i++) {
            var skuId = skuIdList[i];
            var warehouseId = $("#"+skuId+"_warehouseId").val();
            if(warehouseId != localWarehouse){
                layer.alert("非本仓SKU无法补标!", "error");
                return false;
            }

        }

        $.getJSON(CONTEXT_PATH + "expManage/checkExpSkuAndGetBatchInfo?scanSku=" + getCheckedSkus(), function (data) {
            if (data.status == 500){
                layer.alert(data.message, function(index){
                    layer.close(index);
                    window.location.reload();
                });
                return false;
            }
            var expSku = false;
            if (data.body.expManages && data.body.expManages.length > 0) {
                 var html = "<tr id=\"exp-sku-add-batch-tr\">\n" +
                    "         <td>补标批次：</td>\n" +
                    "         <td>\n" +
                    "             <input id=\"checkBatchNo\" class=\"form-control dropdown-toggle\" data-toggle=\"dropdown\" value=\"\" id=\"\"/>\n" +
                    "             <ul class=\"dropdown-menu my-dropdown\">\n" +
                    "                 <li>\n" +
                    "                     <div class=\"desc\">批次号</div>\n" +
                    "                     <div class=\"percent\">到期时间</div>\n" +
                    "                     <div class=\"percent\">剩余库存</div>\n" +
                    "                 </li>\n";
                for (var i = 0; i < data.body.expManages.length; i++) {
                    var expDate = new Date(data.body.expManages[i].expDate).format("yyyy-MM-dd");
                    var batchNo = data.body.expManages[i].batchNo;
                    html +="<li onclick='checkBatchNo(this)'>\n" +
                           "    <div name='batchNo'>"+batchNo+"</div>\n" +
                           "    <div name='expDate'>"+expDate+"</div>\n" +
                           "    <div name='quantity'>"+data.body.expManages[i].quantity+"</div>\n" +
                           "</li>\n";
                }
                html +="</ul></td></tr>";
                $('#dialog-form>table>tbody').append(html);
                expSku = true;
            }
            toPrintSkuQRCode(expSku);
        });
    });

    function toPrintSkuQRCode(expSku){
        dialog({
            title: '打印份数',
            content: $('#dialog-form'),
            width: 500,
            top: 0,
            okValue: '确定',
            ok: function () {
                var reg = /^\+?[1-9][0-9]*$/;
                var num = $('#printNum').val();
                var checkBatchNo = $('#checkBatchNo').val();
                var reason = $('#printReason').val();
                if(!reg.test(num)){
                    layer.alert("请输入正确的正整数", 'error');
                    $('#printNum').val('');
                    return false;
                }
                if (expSku && (checkBatchNo == null || checkBatchNo == '')){
                    layer.alert("保质期SKU请选择补标批次！", 'error');
                    return false;
                }
                $.ajax({
                    url: CONTEXT_PATH+"skus/printSkuQRCode",
                    type: "POST",
                    data: {outIds: getCheckedIds(),printNum:num,checkBatchNo:checkBatchNo,printReason:reason},
                    success: function(data){
                        var printWindow=window.open();
                        printWindow.document.write(data);
                        printWindow.focus();
                        window.location.reload();
                    }
                });
            },
            cancelValue: '关闭',
            cancel: function () {
                window.location.reload();
            }
        }).showModal();
    }

    function checkBatchNo(obj){
        var batchNo = $(obj).find("div[name='batchNo']").text();
        $(obj).parent().parent().find("#checkBatchNo").val(batchNo);
    }

    $("input[name='printReason']").select2({
        data : [{id : "标签损坏", text : "标签损坏"},{id : "产品油性/标签掉落", text : "产品油性/标签掉落"},{id : "断针/扫不上", text : "断针/扫不上"},
            {id : "贴标起皱/扫不上", text : "贴标起皱/扫不上"}, {id : "胶纸缠标/扫不上", text : "胶纸缠标/扫不上"},{id : "产品油性/标签腐蚀扫不上", text : "产品油性/标签腐蚀扫不上"},
            {id : "无标补标", text : "无标补标"},{id:"标签模糊",text:"标签模糊"},{id:"无唯一码",text:"无唯一码"}],
        placeholder : "补标原因",
        allowClear : true
    });

    $('#scanCodeModal').on('shown.bs.modal', function () {
        $('#scanCode').focus();
    });
    function scanSku(obj){
        var scanCode = $('#scanCode').val();
        if(!scanCode || scanCode.trim() == ''){
            layer.alert("请输入有效码!");
            return false;
        }
        if(!(scanCode.indexOf("=") == -1)){
            var realSku = scanCode.split('=')[0];
            scanCode = realSku;
        }else {
            layer.alert("扫描的不是唯一码，请扫描唯一码！",'error');
            $('#scanCode').val('');
            return false;
        }
        $.ajax({
            url:CONTEXT_PATH+"location/moves/skuDetail",
            type:"POST",
            data:{
                sku:scanCode.trim()
            },
            success:function(result){
                if(result.sku != null && result.sku != ''){
                    isScan = true;
                }else{
                    layer.alert("SKU:["+scanCode+"]不存在","error");
                    $('#scanCode').val("");
                }
            }
        });
    }

    $('#toScanCode').on('click',function () {
        $.ajax({
            url: CONTEXT_PATH+"skus/printSkuQRCodeByUuidSku",
            type: "POST",
            data: {scanCode: $('#scanCode').val()},
            success: function(data){
                var printWindow=window.open();
                printWindow.document.write(data);
                printWindow.focus();
                window.location.reload();
            }
        });
    });


    $('#myTemplate').on('click',"a[data-action]",function () {
        var action = $(this).data("action");
        var dataIndex = $(this).closest("tr");
        if(action == "addTemplate"){
            $('#myTemplate').append($('#settingTemplate').html());
        }else {
            if(confirm("确认删除?")){
                dataIndex.remove();
            }
        }
        refreshIndex();
    });


    function refreshIndex(){
        $('#myTemplate').find('tr').each(function (indexValue=0) {
            $(this).attr("data-index",indexValue);
            indexValue++;
            if(indexValue  == 1){
                $(this).find("a").attr("data-action","addTemplate");
                $(this).find("a").text("添加");
            }else {
                $(this).find("a").attr("data-action","removeTemplate");
                $(this).find("a").text("移除");
            }
        });
    }

    function isPositiveInteger(s) { //是否为正整数
        var re = /^[0-9]+$/ ;
        return re.test(s);
    }
    $('#saveAttributeSetting').on('click',function () {
        var re = /^[0-9]+$/; //是否为正整数
        var flag = true;
        var settingJsonArr = [];
        var count = 0;
        var formArr = $("#attributeSettingForm").serializeArray();
        for (var i = 0; i < formArr.length - 20; i+=20) {
            var objMap = {};
            var trArr = [];
            var number;
            for (var j = 0; j < 20; j++) {
                if ((formArr[i + j].value) && !re.test(formArr[i + j].value)) {
                    layer.alert('第' + (i + 1) + '列，' +  (j + 1) + '行请填写正整数！', 'error');
                    flag = false;
                    return false;
                }
                var numVal = parseInt(formArr[i + j].value);
                trArr.push(numVal);
                count = i + j;
                if (j > 1 && j%2 > 0 && number && numVal && !(number > numVal)) { // 是否连续校验 比较
                    layer.alert('第' + (i + 1) + '列，' +  (j + 1) + '行数据不连续！', 'error');
                    flag = false;
                    return false;
                }
                if (j > 1 && j%2 == 0 && number && numVal && number - 1 != numVal) { // 是否连续校验 比较
                    layer.alert('第' + (i + 1) + '列，' +  (j + 1) + '行数据不连续！', 'error');
                    flag = false;
                    return false;
                }
                if (j > 3) { // 是否连续校验 开头
                    number = numVal;
                }
            }
            objMap.numArray = trArr;
            objMap.level = i/20;
            settingJsonArr.push(objMap);
        }
        var num1 = formArr[++count].value;
        var num2 = formArr[++count].value;
        var num3 = formArr[++count].value;
        var num4 = formArr[++count].value;
        var num5 = formArr[++count].value;
        if ((num1 && !re.test(num1)) || (num2 && !re.test(num2)) || (num3 && !re.test(num3))
            || (num4 && !re.test(num4)) || (num5 && !re.test(num5))) {
            layer.alert('新品判断请填写正整数！', 'error');
            return false;
        }
        var objMap = {num1:num1, num2: num2, num3: num3, num4: num4, num5: num5, level: count/20};
        settingJsonArr.push(objMap);
        console.log(settingJsonArr);
        var param = {
            paramId:$('#paramId').val(),
            paramValue:JSON.stringify(settingJsonArr)
        };
        if(flag){
            $.post(CONTEXT_PATH + "skus/saveSalesAttributeSetting", param, function(data) {
                if (data.status == 200) {
                    layer.alert("修改成功！");
                    $('#myModal').modal('hide');
                } else {
                    customizeLayer(data.message, "error");
                }
            });
        }
    });
    $('#myModal').on('hide.bs.modal', function () {
        window.location.reload();
    });

    // 产品图放大
    function enlarge(obj){
        var url = $(obj).attr("src");
        var content = `<img style='width:600px;height:600px;' src='`+url+`'/>`
        layer.open({
            type: 1,
            title: false,
            closeBtn:0,
            area: ['600px','600px'],
            offset:'auto',
            fix: true,
            maxmin: false,
            shade:0.4,
            shadeClose:true,
            content: content
        });
    }

    /**
     * 审核称重
     */
    $("#batchCreateVerifySkuWeightTask").click(function () {
        var outIds = $("#task-list").find("input[name='outIds']:checked");
        var selectSkus = "";
        for (var i = 0; i < outIds.length; i++) {
            var outId = outIds[i].value;
            var sku = $("#" + outId).text();
            if (i == 0) {
                selectSkus += sku;
            } else {
                selectSkus += "," + sku;
            }
        }

        if (!selectSkus) {
            layer.alert("请先选择至少一个SKU！");
            return false;
        }

        var diglog = dialog({
            title:"生成称重任务",
            width:350,
            height:100,
            url: CONTEXT_PATH + "skus/verifySkuWeightTask",
            okValue: '确定',
            ok: function(){

                var exportWindow = $(this.iframeNode.contentWindow.document.body);

                var submitForm = exportWindow.find("#submit-form");

                var locationCount = submitForm.find("input[name='locationCount']").val();
                $.ajax({
                    url:CONTEXT_PATH +"skus/batchCreateVerifySkuWeightTask",
                    type:"POST",
                    data:{
                        skus:selectSkus,
                        locationCount:locationCount
                    },
                    success : function(response){
                        var message = '';
                        if (response.message != null) {
                            message = response.message
                        }
                        if (response.status == '500') {
                            customizeLayer(message, 'error');
                            return;
                        } else if (response.status == '200') {
                            layer.confirm(message,{
                                icon: 1,
                                btn: ['确定']
                            },function () {
                                window.location.reload();
                            })

                        }
                    },
                    error:function(){
                        layer.alert('操作失败!', 'error');
                    }
                });
            },
            cancelValue: '取消',
            cancel: function () {}
        });
        diglog.show();

    });


    /**
     * 导入SKU修改信息
     * @param target
     */
    function importUpdateSkuInfo(target) {

        //检测上传文件的类型
        var filename = target.value;

        var ext, idx;
        if (filename == '') {
            $("#submit-upload").attr("disabled", true);
            layer.alert("请选择需要上传的文件!");
            return;
        } else {
            idx = filename.lastIndexOf(".");
            if (idx != -1) {
                ext = filename.substr(idx + 1).toUpperCase();
                ext = ext.toLowerCase();

                if (ext != 'xls' && ext != 'xlsx') {
                    layer.alert("只能上传.Excel类型的文件!");
                    return;
                }
            } else {
                layer.alert("只能上传.Excel类型的文件!");
                return;
            }
        }

        var r = confirm("确定上传" + filename + "?");

        if(!r) {
            return;
        }

        var uploadUrl = CONTEXT_PATH + "skus/uploadUpdateSku";

        var searchUrl = $("#domain").attr("action");

        $("#domain").attr("action", uploadUrl);

        $("#domain").ajaxSubmit(function(data) {
            if (data.status == 200) {
                layer.confirm(data.message,{
                    icon: 1,
                    btn: ['确定']
                },function () {
                    window.location.reload();
                })
            } else {
                $(target).val(null);
                customizeLayer(data.message, "error");
            }

            $("#domain").attr("action", searchUrl);

        });

        $("#domain").attr("action", searchUrl);
    }

    /**
     * 批量修改质检备注
     */
    function importUpdateSkuQcCategory(target) {

        //检测上传文件的类型
        var filename = target.value;

        var ext, idx;
        if (filename == '') {
            $("#submit-upload").attr("disabled", true);
            layer.alert("请选择需要上传的文件!");
            return;
        } else {
            idx = filename.lastIndexOf(".");
            if (idx != -1) {
                ext = filename.substr(idx + 1).toUpperCase();
                ext = ext.toLowerCase();

                if (ext != 'xls' && ext != 'xlsx') {
                    layer.alert("只能上传.Excel类型的文件!");
                    return;
                }
            } else {
                layer.alert("只能上传.Excel类型的文件!");
                return;
            }
        }

        var r = confirm("确定上传" + filename + "?");

        if(!r) {
            return;
        }

        var uploadUrl = CONTEXT_PATH + "skus/importUpdateSkuQcCategory";

        var searchUrl = $("#domain").attr("action");

        $("#domain").attr("action", uploadUrl);

        $("#domain").ajaxSubmit(function(data) {
            if (data.status == 200) {
                layer.confirm(data.message,{
                    icon: 1,
                    btn: ['确定']
                },function () {
                    window.location.reload();
                })
            } else {
                $(target).val(null);
                customizeLayer(data.message, "error");
            }

            $("#domain").attr("action", searchUrl);

        });

        $("#domain").attr("action", searchUrl);
    }

 	// 修改质检备注
    $('#updateRemark').on('click',function () {
    	// 清空参数
    	$("#updateRemarkForm").find('#update-record-sku').text('');
    	$("#updateRemarkForm").find('#update-record-title').text('');
    	$("#updateRemarkForm").find('#sku-qc-category').html('');
    	$("#updateRemarkForm").find('#deleteSkuQcCategoryIds').val(null);
    	$("#updateRemarkForm").find('#skuId').val(null);

    	var checkedIds = getCheckedIds();
    	if (!checkedIds) {
    		layer.alert("请选择SKU", "error");
    		return false;
    	}
    	var skuIdList = checkedIds.split(",");
    	if (skuIdList.length != 1) {
            layer.alert("只能选择一个SKU", "error");
            return false;
        }

    	// sku编号
    	var skuId = skuIdList[0];
    	// SKU
    	var sku = $('#' + skuId).text();
    	if (!sku) {
    		layer.alert("SKU为空", "error");
            return false;
    	}
    	// 标题
    	var title = $('#' + skuId + '_title').text();

    	// 设置参数
    	$("#updateRemarkForm").find('#skuId').val(skuId);
    	$("#updateRemarkForm").find('#update-record-sku').html(sku);
    	$("#updateRemarkForm").find('#update-record-title').html(title);

    	$.ajax({
            url: CONTEXT_PATH + 'skus/getSkuQcCategory?sku=' + sku,
            type: "GET",
            success: function(result) {
            	var body = result.body;
            	var skuQcCategorys = body.skuQcCategorys;
            	var productQcCategoryList = body.productQcCategoryList;

            	var html = "";
            	if (skuQcCategorys && skuQcCategorys.length > 0) {
            		for (var i = 0; i < skuQcCategorys.length; i++) {
            			var skuQcCategory = skuQcCategorys[i];
            			html += '<tr id="sku-qc-category-tr-' + i + '">';
            			html += '	<td>';
            			html += '		<select class="form-control" style="width: 127px;" onchange="selectQcCategory(this, ' + i + ')">';
            			for (var j = 0; j < productQcCategoryList.length; j++) {
            				var productQcCategory = productQcCategoryList[j];
            				if (skuQcCategory.qcCategoryCode==productQcCategory.code) {
            					html += '	<option value="' + productQcCategory.code + '" selected="selected">' + productQcCategory.name + '</option>';
            				} else {
            					html += '	<option value="' + productQcCategory.code + '">' + productQcCategory.name + '</option>';
            				}
            			}
            			html += '		</select>';
            			html += '	</td>';
            			html += '	<td>';
    	            	html += '		<input type="hidden" id="skuQcCategorysId" class="form-control" name="whSku.skuQcCategorys[' + i + '].id" value="' + skuQcCategory.id + '">';
    	            	html += '		<input type="hidden" id="skuQcCategorysCode" class="form-control" name="whSku.skuQcCategorys[' + i + '].qcCategoryCode" value="' + skuQcCategory.qcCategoryCode + '">';
    	            	html += '		<input type="hidden" id="skuQcCategorysName" class="form-control" name="whSku.skuQcCategorys[' + i + '].qcCategoryName" value="' + skuQcCategory.qcCategoryName + '">';
    	            	html += '		<input type="text" id="skuQcCategorysRemark" class="form-control" style="width: 225px;" name="whSku.skuQcCategorys[' + i + '].qcCategoryRemark" value="' + skuQcCategory.qcCategoryRemark + '">';
    	            	html += '	</td>';
    	            	html += '	<td>';
    	            	if (i == 0) {
	    	            	html += '	<a href="javascript:void(0);" onclick="addSkuQcCategory()">添加</a>';
    	            	} else {
	    	            	html += '	<a href="javascript:void(0);" onclick="deleteSkuQcCategory(' + i + ')">删除</a>';
    	            	}
    	            	html += '	</td>';
            			html += '</tr>';
            		}
	            	$("#updateRemarkForm").find('#sku-qc-category').html(html);
           		} else {
           			addSkuQcCategory();
            	}
            },
            error: function() {
            	layer.alert("获取SKU质检备注失败", "error");
            }
        });
    	$("#updateRemarkModal").modal();
    });

    //重置标准重量
    $('#updateNetWeight').on('click',function () {
        var checkedIds = getCheckedIds();
        if (!checkedIds) {
            layer.alert("请选择SKU", "error");
            return false;
        }
        var skuIdList = checkedIds.split(",");
        if (skuIdList.length != 1) {
            layer.alert("只能选择一个SKU", "error");
            return false;
        }
        var oldNetWeight = $('#'+checkedIds+'_netWeight').text();
        if(oldNetWeight == ''){
            oldNetWeight = '空';
        }
        $('#oldNetWeight').val(oldNetWeight);
        $('#checkedId').val(checkedIds);
        $("#updateNetWeightModal").modal('show');
    });

    $('#saveNetWeight').on('click',function () {
        //3.设置提交按钮失效，以实现防止按钮重复点击
        $('#saveNetWeight').attr("disabled", true);

        var checkedId = $('#checkedId').val();
        var oldNetWeight = $('#oldNetWeight').val();
        var reg = /^\+?[1-9][0-9]*$|^[+]{0,1}(\d+\.\d{1,2})$/;
        var netWeight = $('#newNetWeight').val();
        if(netWeight == ''){
            layer.alert('新的标准重量不能为空！','error');
            $('#saveNetWeight').removeAttr('disabled');
            return false;
        }
        if(!reg.test(netWeight)){
            layer.alert("请输入正确的重量，支持小数点后两位", 'error');
            $('#newNetWeight').val('');
            $('#saveNetWeight').removeAttr('disabled');
            return false;
        }
        if(oldNetWeight == netWeight){
            layer.alert("新的标准重量和原来标准重量相等！", 'error');
            $('#newNetWeight').val('');
            $('#saveNetWeight').removeAttr('disabled');
            return false;
        }
        $.ajax({
            url: CONTEXT_PATH + 'skus/updateNetWeight',
            type: "POST",
            data: {id: checkedId,netWeight:netWeight},
            success: function(response) {
                if (response.status == '500') {
                    customizeLayer(response.message, 'error');
                } else{
                    layer.alert('修改成功！');
                }
            }
        });
        setTimeout(function() {
            //提交完成后按钮重新设置有效
            $('#saveNetWeight').removeAttr('disabled');
            $("#updateNetWeightModal").modal('hide');
        }, 1500);
    });

    $('#updateNetWeightModal').on('hide.bs.modal', function () {
        window.location.reload();
    });

    var qcCategoryCode_0 = "";
	var qcCategoryName_0 = "";

	<#list domain.productQcCategoryList as productQcCategory>
		<#if productQcCategory_index == 0>
			qcCategoryCode_0 = "${productQcCategory.code}";
			qcCategoryName_0 = "${productQcCategory.name}";
		</#if>
	</#list>

 	// 添加质检类目
	function addSkuQcCategory() {
		var index = $("#updateRemarkForm").find("#sku-qc-category tr").length;
		var html = '';
		html += '<tr id="sku-qc-category-tr-' + index +'">';
		html += '	<td>';
		html += '		<select class="form-control" onchange="selectQcCategory(this, ' + index + ')">';
		html += '			<#list domain.productQcCategoryList as productQcCategory>';
		html += '				<option value="${productQcCategory.code}">${productQcCategory.name}</option>';
		html += '			</#list>';
		html += '		</select>';
		html += '	</td>';
		html += '	<td>';
		html += '		<input type="hidden" id="skuQcCategorysId" class="form-control" name="whSku.skuQcCategorys[' + index +'].id" value="">';
		html += '		<input type="hidden" id="skuQcCategorysCode" class="form-control" name="whSku.skuQcCategorys[' + index + '].qcCategoryCode" value="' + qcCategoryCode_0 + '">';
		html += '		<input type="hidden" id="skuQcCategorysName" class="form-control" name="whSku.skuQcCategorys[' + index + '].qcCategoryName" value="' + qcCategoryName_0 + '">';
		html += '		<input type="text" id="skuQcCategorysRemark" class="form-control" style="width: 225px;" name="whSku.skuQcCategorys[' + index +'].qcCategoryRemark" value="">';
		html += '	</td>';
		html += '	<td>';
		html += '		<a href="javascript:void(0);" onclick="deleteSkuQcCategory(' + index +')">删除</a>';
		html += '	</td>';
		html += '</tr>';

		$("#updateRemarkForm").find('#sku-qc-category').append(html);
	}

	// 删除质检类目
	function deleteSkuQcCategory(index) {
        // 删除的质检备注id
        var skuQcCategoryId = $('input[name="whSku.skuQcCategorys[' + index +'].id"]').val();

		// 删除tr
		$("#updateRemarkForm").find("#sku-qc-category-tr-" + index).remove();

		// 重新编排
		$("#updateRemarkForm").find("#sku-qc-category tr").each(function(i) {
			$(this).attr("id", "sku-qc-category-tr-" + i);
			$(this).find("select").attr("onchange", "selectQcCategory(this, " + i +")");
			if (i != 0) {
				$(this).find("a").attr("onclick", "deleteSkuQcCategory(" + i +")");
			}
			$(this).find('#skuQcCategorysId').attr('name', 'whSku.skuQcCategorys[' + i +'].id');
			$(this).find('#skuQcCategorysCode').attr('name', 'whSku.skuQcCategorys[' + i +'].qcCategoryCode');
			$(this).find('#skuQcCategorysName').attr('name', 'whSku.skuQcCategorys[' + i +'].qcCategoryName');
			$(this).find('#skuQcCategorysRemark').attr('name', 'whSku.skuQcCategorys[' + i +'].qcCategoryRemark');
			refreshIndex(i, this);
		});

		// 记录删除的已有的质检备注
		if (skuQcCategoryId && skuQcCategoryId != 0) {
			var deleteSkuQcCategoryIds = $("#updateRemarkForm").find("#deleteSkuQcCategoryIds").val();
			deleteSkuQcCategoryIds += skuQcCategoryId + ",";
			$("#updateRemarkForm").find("#deleteSkuQcCategoryIds").val(deleteSkuQcCategoryIds);
		}
	}

	// 选择质检备注
	function selectQcCategory(obj, index) {
		var qcCategoryCode = $(obj).find("option:selected").val();
		var qcCategoryName = $(obj).find("option:selected").text();
		$("#updateRemarkForm").find('input[name="whSku.skuQcCategorys[' + index + '].qcCategoryCode"]').val(qcCategoryCode);
		$("#updateRemarkForm").find('input[name="whSku.skuQcCategorys[' + index + '].qcCategoryName"]').val(qcCategoryName);
	}

	// 保存质检备注
	function confirmUpdateRemark() {
		var sku = $("#updateRemarkForm").find("#update-record-sku").text();
		if (!sku) {
			layer.alert("SKU为空");
			return false;
		}

		var skuId = $("#updateRemarkForm").find('#skuId').val();
		if (!skuId) {
			layer.alert("skuId为空");
			return false;
		}

		$("#updateRemarkForm").find("#sku-qc-category tr").each(function(index) {
			var qcCategoryRemark = $(this).find("input[name='whSku.skuQcCategorys[" + index + "].qcCategoryRemark']").val();
			if (!qcCategoryRemark.trim()) {
				layer.alert("质检备注项不能为空", "error");
				return false;
			}
		});

		var params = $("#updateRemarkForm").serialize();

		params += "&whSku.id=" + skuId + "&whSku.sku=" + sku;

		var deleteSkuQcCategoryIds = $("#updateRemarkForm").find("#deleteSkuQcCategoryIds").val();
		if (deleteSkuQcCategoryIds) {
			params += "&deleteSkuQcCategoryIds=" + deleteSkuQcCategoryIds;
		}

		$.post(CONTEXT_PATH + "skus/updateSkuQcCategory", params, function(data) {
			if (data.status == 200) {
                layer.confirm(data.message,{
                    icon: 1,
                    btn: ['确定']
                },function () {
                    window.location.reload();
                })

            } else {
				customizeLayer(data.message, "error");
			}
		});
	}

    $('#updateRemarkModal').on('hide.bs.modal', function () {
        window.location.reload();
    });


    $('#updateSkuFeature').on('click',function () {
        var checkedIds = getCheckedIds();
        if (!checkedIds) {
            layer.alert("请选择SKU", "error");
            return false;
        }
        var skuIdList = checkedIds.split(",");
        if (skuIdList.length != 1) {
            layer.alert("只能选择一个SKU", "error");
            return false;
        }

        var checkedIds = getCheckedIds();
        if (!checkedIds) {
            layer.alert("请选择SKU", "error");
            return false;
        }
        var skuIdList = checkedIds.split(",");
        if (skuIdList.length != 1) {
            layer.alert("只能选择一个SKU", "error");
            return false;
        }

        // sku编号
        var skuId = skuIdList[0];
        // SKU
        var sku = $('#' + skuId).text();
        if (!sku) {
            layer.alert("SKU为空", "error");
            return false;
        }
        // 标题
        var title = $('#' + skuId + '_title').text();
        var skuFeature = $('#'+ skuId + '_feature').val();

        // 设置参数
        $("#updateSkuFeatureForm").find("input[name='whSku.id']").val(skuId);
        $("#updateSkuFeatureForm").find("input[name='whSku.sku']").val(sku);
        $("#updateSkuFeatureForm").find("#update-sku").text(sku);
        $("#updateSkuFeatureForm").find("#update-sku-title").text(title);
        $("#updateSkuFeatureForm").find("input[name='whSku.feature']").val(skuFeature);
        $("#updateSkuFeatureModal").modal('show');
    });

    function confirmUpdateSkuFeature() {
        var sku = $("#updateSkuFeatureForm").find("input[name='whSku.sku']").val();
        if (!sku) {
            layer.alert("SKU为空");
            return false;
        }

        var skuId = $("#updateSkuFeatureForm").find("input[name='whSku.id']").val();
        if (!skuId) {
            layer.alert("skuId为空");
            return false;
        }

        var skuFeature = $('#updateSkuFeatureForm').find("input[name='whSku.feature']").val();
        if (!skuFeature) {
            layer.alert("仓库质检备注为空");
            return false;
        }

        var params = $("#updateSkuFeatureForm").serialize();

        $.post(CONTEXT_PATH + "skus/updateSkuFeature", params, function(data) {
            if (data.status == 200) {
                layer.confirm(data.message,{
                    icon: 1,
                    btn: ['确定']
                },function () {
                    window.location.reload();
                })

            } else {
                customizeLayer(data.message, "error");
            }
        });
    }

    $('#updateSkuFeatureModal').on('hide.bs.modal', function () {
        window.location.reload();
    });

    function downloadSkus() {
        var diglog = dialog({
            title:"SKU导出",
            width: 700,
            height:410,
            url: CONTEXT_PATH + "skus/downloadmode?type=2",
            okValue: '确定',
            ok: function () {
                var exportWindow = $(this.iframeNode.contentWindow.document.body);

                var submitForm = exportWindow.find("#submit-form");

                var exportType = submitForm.find("input[name='exportType']:checked").val();

                var headers =  submitForm.find("input[name='selectHeaders']:checked");

                var ids = getCheckedIds();
                var params = $('#domain').serialize();
                if (exportType == 1){
                    if(ids.length == 0 && $('#uuidSku').val() == '' && total > 500000){
                        layer.alert("最多导出500000条数据","error");
                        return false;
                    }
                } else if(exportType == 3) {// 导出当前选择
                    if(ids.length == 0) {
                        layer.alert("请选择要操作的数据");
                        return false;
                    }
                    params = params +"&query.ids="+ids;
                }
                if(headers.length == 0){
                    layer.alert("请选择要导出的表头字段！","error");
                    return false;
                }
                var headersStr = headersJoin(headers);
                params = params +"&query.pHeaders="+headersStr+"&exportType="+exportType;
                //还原分页
                $("#page-no").val("${domain.page.pageNo}");

                // postExcelFile(params,"download?");
                $.post(CONTEXT_PATH + "skus/download", params, function(data){
                    if (data.status == 200) {
                        if (data.message==null || data.message==''){
                            layer.alert('成功',function (index) {
                                layer.close(index);
                                diglog.close().remove();
                                location.reload();
                            });
                        }else{
                            customizeLayer(data.message);
                        }
                    } else {
                        customizeLayer(data.message);
                    }
                });
                $("#page-no").val("1");

                setTimeout(function () {
                    diglog.close().remove();
                }, 100);

                return true;
            },
            cancelValue:'取消',
            cancel: function () {}

        });
        diglog.show();
    }

    function postExcelFile(params, url) {
        downloadByPostForm(params, url);
        /*var form = document.createElement("form");
        form.style.display = 'none';
        form.action = url + params;
        form.method = "post";
        document.body.appendChild(form);
        form.submit();
        form.remove();*/
    }

    $('#updateSkuBarCode').on('click',function () {
        var checkedIds = getCheckedIds();
        if (!checkedIds) {
            layer.alert("请选择SKU", "error");
            return false;
        }
        var skuIdList = checkedIds.split(",");
        if (skuIdList.length != 1) {
            layer.alert("只能选择一个SKU", "error");
            return false;
        }

        $('#bar-code-sku-id').val(checkedIds);
        $('#bar-code-sku').text($('#' + checkedIds).text());
        $('#bar-code-title').text($('#' + checkedIds + '_title').text());
        $('#skuBarCode').val($('#' + checkedIds + '_bar_code').val());
        $('#dbSkuBarCode').val($('#' + checkedIds + '_bar_code').val());
        $("#updateSkuBarCodeModal").modal('show');

    });

    $('#saveSkuBarCode').on('click',function () {
        //3.设置提交按钮失效，以实现防止按钮重复点击
        $('#saveSkuBarCode').attr("disabled", true);

        var skuId = $('#bar-code-sku-id').val();
        var sku = $('#bar-code-sku').text();
        var skuBarCode = $('#skuBarCode').val();
        var dbSkuBarCode = $('#dbSkuBarCode').val();
        if(skuId == '' || sku == ''){
            layer.alert('SKU不能为空！','error');
            $('#saveSkuBarCode').removeAttr('disabled');
            return false;
        }

        if(skuBarCode == ''){
            layer.alert("SKU编码不能为空！", 'error');
            $('#saveSkuBarCode').removeAttr('disabled');
            return false;
        }

        if(skuBarCode == dbSkuBarCode){
            layer.alert("输入的SKU编码与原编码相同，请重新输入！", 'error');
            $('#saveSkuBarCode').removeAttr('disabled');
            return false;
        }

        $.ajax({
            url: CONTEXT_PATH + 'barCodeSku/updateSkuBarCode',
            type: "POST",
            data: {sku: sku,barCode:skuBarCode,skuId: skuId},
            success: function(response) {
                if (response.status == '500') {
                    customizeLayer(response.message, 'error');
                    $('#skuBarCode').val('');
                } else{
                    layer.confirm('修改成功！',{
                        icon: 1,
                        btn: ['确定']
                    },function () {
                        $("#updateSkuBarCodeModal").modal('hide');
                    })
                }
            }
        });
        setTimeout(function() {
            //提交完成后按钮重新设置有效
            $('#saveSkuBarCode').removeAttr('disabled');
        }, 1500);
    });



    $('#updateSkuBarCodeModal').on('hide.bs.modal', function () {
        window.location.reload();
    });

    $('#printEurOrUkFlag').on('click',function () {
        dialog({
            title: '打印份数',
            content: $('#dialog-form'),
            width: 400,
            height: 100,
            top: 0,
            okValue: '确定',
            dragStart: function () {
            },
            ok: function () {
                var reg = /^\+?[1-9][0-9]*$/;
                var num = $('#printNum').val();
                if(!reg.test(num)){
                    layer.alert("请输入正确的正整数", 'error');
                    $('#printNum').val('');
                    return false;
                }
                $.ajax({
                    url: CONTEXT_PATH+"skus/printEurOrUkFlag?quantity="+num,
                    type: "GET",
                    success: function(data){
                        var printWindow=window.open();
                        printWindow.document.write(data);
                        printWindow.focus();
                        window.location.reload();
                    }
                });
            },
            cancelValue: '关闭',
            cancel: function () {
                return;
            }
        }).showModal();
    })

    $('#printCEFlag').on('click',function () {
        var outIds = $("#task-list").find("input[name='outIds']:checked");
        if(outIds.length != 1){
            layer.alert("选中的sku不为1个！",'error');
            return;
        }
        var skuId = outIds[0].value;
        $.ajax({
            url: CONTEXT_PATH+"skus/printCEFlag?skuId="+skuId,
            type: "GET",
            success: function(data){
                var printWindow=window.open();
                printWindow.document.write(data);
                printWindow.focus();
                window.location.reload();
            }
        });
    })

    $('#printOutProductFlag').on('click',function () {
        var outIds = $("#task-list").find("input[name='outIds']:checked");
        if(outIds.length != 1){
            layer.alert("选中的sku不为1个！",'error');
            return;
        }
        var skuId = outIds[0].value;
        $.ajax({
            url: CONTEXT_PATH+"skus/printOutProductFlag?skuId="+skuId,
            type: "GET",
            success: function(data){
                var printWindow=window.open();
                printWindow.document.write(data);
                printWindow.focus();
                window.location.reload();
            }
        });
    })

    function fileinputSubmit(target) {

        //检测上传文件的类型
        var filename = target.value;

        var ext, idx;
        if (filename == '') {
            $("#submit-upload").attr("disabled", true);
            layer.alert("请选择需要上传的文件!");
            return;
        } else {
            idx = filename.lastIndexOf(".");
            if (idx != -1) {
                ext = filename.substr(idx + 1).toUpperCase();
                ext = ext.toLowerCase();

                if (ext != 'xls' && ext != 'xlsx') {
                    layer.alert("只能上传.Excel类型的文件!");
                    return;
                }
            } else {
                layer.alert("只能上传.Excel类型的文件!");
                return;
            }
        }

        var r = confirm("确定上传" + filename + "?");

        if(!r) {
            return;
        }

        var uploadUrl = CONTEXT_PATH + "skus/manage/upload";

        var searchUrl = $("#domain").attr("action");

        $("#domain").attr("action", uploadUrl);

        $("#domain").ajaxSubmit(function(data) {
            if (data.status == 200) {
                layer.alert("成功！");
                setTimeout(function() {
                    window.location.reload();
                }, 1000);
            } else {
                customizeLayer(data.message, "error");
                $(target).val(null);
            }

            $("#domain").attr("action", searchUrl);
        });

        $("#domain").attr("action", searchUrl);
    }



    function fileChange(target) {
        var filename = target.value;
        if (filename == '') {
            layer.alert("请选择需要上传的文件!");
            return;
        } else {
            var ext, idx;
            idx = filename.lastIndexOf(".");
            if (idx != -1) {
                ext = filename.substr(idx + 1).toLowerCase();
                if (ext != 'jpg' && ext != 'jpeg' && ext != 'png' && ext !='gif') {
                    layer.alert("不支持" + ext + "文件上传!");
                    var file = $(target)
                    file.after(file.clone().val(""));
                    file.remove();
                    return;
                }
            } else {
                layer.alert("只能上传图片文件!");
                var file = $(target)
                file.after(file.clone().val(""));
                file.remove();
                return;
            }
        }
        var files = target.files;
        if(files.length>1){
            layer.alert("只能上传1张图片！",'error');
            return;
        }
        var selectedIds = getCheckedIds();
        if (selectedIds.length == 0) {
            layer.alert("请先选择至少一个SKU！");
            return false;
        }
        var r = confirm("确定上传?");
        if(!r) {
            return;
        }
        $("#selectedIds").val(selectedIds);
        uploadImage();
        $("#selectedIds").val("");
    }

    function uploadImage(){
        var uploadUrl = CONTEXT_PATH + "skus/batchUploadPackImage";

        var commitImages = $("#domain");
        var searchUrl = commitImages.attr("action");
        commitImages.attr("action", uploadUrl);
        commitImages.ajaxSubmit(function(data) {
            console.log(data);
            if (data.status == 200) {
                alert("图片上传成功!");
                setTimeout(function(){window.location.reload()},2*1000);
            }else {
                customizeLayer("图片上传失败: "+data.message, 'error');
            }
        });
        commitImages.attr("action", searchUrl);
    }

    function modifySkusTags(){
        var selectedIds = getCheckedIds();
        if (selectedIds.length == 0) {
            layer.alert("请先选择至少一个SKU！");
            return false;
        }
        $.ajax({
            url: CONTEXT_PATH+"skus/modifySkuTags",
            type: "GET",
            success: function(data){
                layer.open({
                    type:0,
                    title:"修改标签",
                    content:data,
                    shade:0.4,
                    fixed:true,
                    zIndex:0,
                    area: ['400px', '230px'], //宽高
                    scrollbar:false,
                    shadeClose:false,
                    btn:['确定','取消'],
                    btn1:function(index){
                        var type = $("#skuTagType option:selected").val();
                        var skuTags = $("#skuTags").val();
                        if (!skuTags){
                            layer.alert("请先选择至少一个标签！");
                            layer.close(index);
                            return;
                        }
                        modifySkuTag(type,skuTags,selectedIds);
                        layer.close(index);
                    },
                    btn2:function(index){
                        layer.close(index);
                    }
                });
            }
        });
    }

    function modifySkuTag(type,skuTags,selectedIds){
        if (!type || !skuTags || !selectedIds){
            customizeLayer("无选择数据!",'error');
            return;
        }
        $.ajax({
            url:CONTEXT_PATH+"skus/modifySkusTags",
            type:"POST",
            contentType: "application/json;charset=utf-8",
            dataType : "json",
            data:JSON.stringify({'type':type,'skuTags':skuTags.split(','),'selectedIds':selectedIds.split(',')}),
            success:function(data){
                if(data.status == '200'){
                    layer.alert('修改成功','success');
                    location.reload();
                }else{
                    customizeLayer(data.message,'error');
                }
            },
            error:function(){
                layer.alert("请求失败!",'error');
            }
        });
    }

    //增加仓库标签的选择修改
    var tagsSelect=${domain.skuTagsSelect};
    $("input[name='query.skuTagStr']").select2({
        data : tagsSelect,
        placeholder : "请选择，支持多选",
        multiple: true,
        allowClear : true
    });
</script>
</body>
</html>