<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <#include "/common/include.html">
    <style type="text/css">
        .group-width{
            width: 100% !important;
        }
        .control-select{
            width: 10% !important;
        }
        .control-input{
            width: 30% !important;
        }
        .from-margin{
            margin-left: 2.5% !important;
        }
        #dialog-form{
            text-align: center;
        }
        .radio-inline{
            font-size: 14px;
            margin-right: 20px;
        }
    </style>
</head>
<body>
<@header method="header" active="11040000"><#include "/ftl/header.ftl"></@header>
<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li><a href="#">库存管理</a></li>
                <li class="active">其他</li>
                <li class="active">SKU核重</li>
            </ul>
        </div>
    </div>
    <div class="container-fluid" style="background-color: white;border: none">
        <div class="row">
            <div class="col-md-12">
                <form action="${CONTEXT_PATH}verifySku/search"
                           class="form-horizontal form-bordered form-row-stripped"
                           method="post" modelAttribute="domain" id="domain" name="checkInForm">
                    <!-- 分页信息 -->
                    <input id="page-no" type="hidden" name="page.pageNo" value="1">
                    <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
                    <div class="form-group">
                        <label class="control-label col-md-1">审核状态：</label>
                        <div class="col-md-3">
                            <#if domain.query.status??>
                                <input class="form-control" id="status" name="query.status" value="${domain.query.status}"/>
                                <#else >
                                <input class="form-control" id="status" name="query.status" value="1"/>
                            </#if>
                        </div>
                        <label class="control-label col-md-1">称重时间：</label>
                        <div class="col-md-3">
                            <div class="input-group">
                                <input class="form-control Wdate" type="text" readonly="readonly" placeholder="" name="query.fromWeighingDate" value="${domain.query.fromWeighingDate}" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                <span class="input-group-addon">到</span>
                                <input class="form-control Wdate" type="text" readonly="readonly" placeholder="" name="query.toWeighingDate" value="${domain.query.toWeighingDate}" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                            </div>
                        </div>
                        <label class="control-label col-md-1">SKU</label>
                        <div class="col-md-3">
                            <input class="form-control" name="query.sku" value="${domain.query.sku}"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-1">重量差：</label>
                        <div class="col-md-3">
                            <div class="input-group group-width">
                                <select id="fromCompareType" name="query.fromCompareType" class="form-control control-select">
                                    <option <#if domain.query.fromCompareType == '>'>selected</#if> value=">"> > </option>
                                    <option <#if domain.query.fromCompareType == '>='>selected</#if> value=">="> >= </option>
                                </select>
                                <input id="fromWeightDiff" name="query.fromWeightDiff" value="${domain.query.fromWeightDiff}" class="form-control control-input from-margin"/>
                                <select id="conditionType" name="query.conditionType" class="form-control control-select from-margin">
                                    <option <#if domain.query.conditionType == 'AND'>selected</#if> value="AND">且</option>
                                    <option <#if domain.query.conditionType == 'OR'>selected</#if> value="OR">或</option>
                                </select>
                                <select id="toCompareType" name="query.toCompareType" class="form-control control-select from-margin">
                                    <option <#if domain.query.toCompareType == '<'>selected</#if> value="<"> < </option>
                                    <option <#if domain.query.toCompareType == '<='>selected</#if> value="<="> <= </option>
                                </select>
                                <input id="toWeightDiff" name="query.toWeightDiff" value="${domain.query.toWeightDiff}" class="form-control control-input from-margin"/>
                            </div>
                        </div>
                        <label class="control-label col-md-1">称重人：</label>
                        <div class="col-md-3">
                            <input class="form-control" name="query.weighingBy" value="${domain.query.weighingBy}"/>
                        </div>
                        <label class="control-label col-md-1">审核人：</label>
                        <div class="col-md-3">
                            <input class="form-control" name="query.verifyBy" value="${domain.query.verifyBy}"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-1">审核时间：</label>
                        <div class="col-md-3">
                            <div class="input-group">
                                <input class="form-control Wdate" type="text" readonly="readonly" placeholder="" name="query.fromVerifyDate" value="${domain.query.fromVerifyDate}" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                <span class="input-group-addon">到</span>
                                <input class="form-control Wdate" type="text" readonly="readonly" placeholder="" name="query.toVerifyDate" value="${domain.query.toVerifyDate}" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                            </div>
                        </div>
                        <label class="control-label col-md-1">重量差比例：</label>
                        <div class="col-md-3">
                            <div class="input-group group-width">
                                <select id="scaleFromCompareType" name="query.scaleFromCompareType" class="form-control control-select">
                                    <option <#if domain.query.scaleFromCompareType == '>'>selected</#if> value=">"> > </option>
                                    <option <#if domain.query.scaleFromCompareType == '>='>selected</#if> value=">="> >= </option>
                                </select>
                                <input id="fromWeightDiffScale" name="query.fromWeightDiffScale" value="${domain.query.fromWeightDiffScale}" class="form-control from-margin" style="width: 25%;"/>
                                %
                                <select id="scaleConditionType" name="query.scaleConditionType" class="form-control control-select from-margin">
                                    <option <#if domain.query.scaleConditionType == 'AND'>selected</#if> value="AND">且</option>
                                    <option <#if domain.query.scaleConditionType == 'OR'>selected</#if> value="OR">或</option>
                                </select>
                                <select id="scaleToCompareType" name="query.scaleToCompareType" class="form-control control-select from-margin">
                                    <option <#if domain.query.scaleToCompareType == '<'>selected</#if> value="<"> < </option>
                                    <option <#if domain.query.scaleToCompareType == '<='>selected</#if> value="<="> <= </option>
                                </select>
                                <input id="toWeightDiffScale" name="query.toWeightDiffScale" value="${domain.query.toWeightDiffScale}" class="form-control from-margin" style="width: 25%;"/>
                                %
                            </div>
                        </div>

                        <label class="control-label col-md-1">标准重量差：</label>
                        <div class="col-md-3">
                            <div class="input-group group-width">
                                <select id="netFromCompareType" name="query.netFromCompareType" class="form-control control-select">
                                    <option <#if domain.query.netFromCompareType == '>'>selected</#if> value=">"> > </option>
                                    <option <#if domain.query.netFromCompareType == '>='>selected</#if> value=">="> >= </option>
                                </select>
                                <input id="fromSkuNetWeightDiff" name="query.fromSkuNetWeightDiff" value="${domain.query.fromSkuNetWeightDiff}" class="form-control control-input from-margin"/>
                                <select id="netConditionType" name="query.netConditionType" class="form-control control-select from-margin">
                                    <option <#if domain.query.netConditionType == 'AND'>selected</#if> value="AND">且</option>
                                    <option <#if domain.query.netConditionType == 'OR'>selected</#if> value="OR">或</option>
                                </select>
                                <select id="netToCompareType" name="query.netToCompareType" class="form-control control-select from-margin">
                                    <option <#if domain.query.netToCompareType == '<'>selected</#if> value="<"> < </option>
                                    <option <#if domain.query.netToCompareType == '<='>selected</#if> value="<="> <= </option>
                                </select>
                                <input id="toSkuNetWeightDiff" name="query.toSkuNetWeightDiff" value="${domain.query.toSkuNetWeightDiff}" class="form-control control-input from-margin"/>
                            </div>
                        </div>

                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-1">审核来源：</label>
                        <div class="col-md-3">
                            <input class="form-control"  name="query.verifyFrom" value="${domain.query.verifyFrom}"/>
                        </div>
                        <label class="control-label col-md-1">SKU称重频次：</label>
                        <div class="col-md-3">
                            <div class="input-group">
                                <input class="form-control Wdate" type="number" min="0" max="10000" onkeyup="" placeholder=">=" name="query.fromSkuFrequency" value="${domain.query.fromSkuFrequency}">
                                <span class="input-group-addon">到</span>
                                <input class="form-control Wdate" type="number" min="0" max="10000" onkeyup="" placeholder="<=" name="query.toSkuFrequency" value="${domain.query.toSkuFrequency}">
                            </div>
                        </div>
                        <label class="control-label col-md-1">是否不规则：</label>
                        <div class="col-md-3">
                            <select id="specification" name="query.specification" value="${domain.query.specification}" class="form-control" >
                                <option value=""></option>
                                <option value="是 -> 是">是 -> 是</option>
                                <option value="否 -> 否">否 -> 否</option>
                                <option value="是 -> 否">是 -> 否</option>
                                <option value="否 -> 是">否 -> 是</option>
                            </select>

                        </div>
                    </div>
                    <div style="padding-top: 10px">
                        <div class="pull-left" style="margin-left: 10px;">
                            <@header method="auth" authCode="SKU_WEIGH_EXAMINE_CHECK">
                                <button type="button" class="btn btn-default blue" onclick="batchVerifySku(undefined, 'false')">
                                    审核
                                </button>
                            </@header>
                            <@header method="auth" authCode="SKU_WEIGH_EXAMINE_CHECK_SUPER">
                                <button type="button" class="btn btn-default blue" onclick="batchVerifySku(undefined, 'true')">
                                    管理员审核
                                </button>
                            </@header>
                        </div>
                        <div class="col-md-offset-10" style="text-align: right">
                            <button type="submit" class="btn blue">
                                <i class="icon-search"></i> 查询
                            </button>
                            <@header method="auth" authCode="SKU_WEIGH_EXAMINE_DOWNLOAD">
                            <button type="button" class="btn btn-default" id="downloadVerifySku">
                                <i class="icon-download"></i> 导出
                            </button>
                            </@header>
                            <button type="button" class="btn btn-default" onclick="searchFormReset(this)">
                                <i class="icon-refresh"></i> 重置
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <br/>
        </div>

        <div class="row">
            <div id="fixedDiv" class="col-md-12">
                <table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
                    <colgroup>
                        <col width="4%" />
                        <col width="5%" />
                        <col width="7%" />
                        <col width="6%" />
                        <col width="8%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="8%" />
                        <col width="8%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                    </colgroup>
                    <thead>
                    <tr>
                        <th>全选/反选 <input type="checkbox" name="check-all" id="check-alls" onchange="checkChange(this)"/></th>
                        <th>产品图片</th>
                        <th>SKU</th>
                        <th>库位</th>
                        <th>标题</th>
                        <th>净重（g）</th>
                        <th>标准重量（g）</th>
                        <th>称重重量（g）</th>
                        <th>原称重重量（g）</th>
                        <th>重量差（g）</th>
                        <th>标准重量差（g）</th>
                        <th>交运重量（g）</th>
                        <th>系统包装属性</th>
                        <th>包装属性</th>
                        <th>是否不规则</th>
                        <th>称重人/称重时间</th>
                        <th>审核人/审核时间</th>
                        <th>称重原因</th>
                        <th>审核来源</th>
                        <th>状态</th>
                        <th>SKU称重频次</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="col-md-12" id="task-list-warp">

                <table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                    <colgroup>
                        <col width="4%" />
                        <col width="5%" />
                        <col width="7%" />
                        <col width="6%" />
                        <col width="8%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="8%" />
                        <col width="8%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                    </colgroup>
                    <thead>
                    <tr>
                        <th>全选/反选 <input type="checkbox" name="check-all" id="check-all" onchange="checkChange(this)"/></th>
                        <th>产品图片</th>
                        <th>SKU</th>
                        <th>库位</th>
                        <th>标题</th>
                        <th>净重（g）</th>
                        <th>标准重量（g）</th>
                        <th>称重重量（g）</th>
                        <th>原称重重量（g）</th>
                        <th>重量差（g）</th>
                        <th>标准重量差（g）</th>
                        <th>交运重量（g）</th>
                        <th>系统包装属性</th>
                        <th>包装属性</th>
                        <th>是否不规则</th>
                        <th>称重人/称重时间</th>
                        <th>审核人/审核时间</th>
                        <th>称重原因</th>
                        <th>审核来源</th>
                        <th>状态</th>
                        <th>SKU称重频次</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list domain.verifySkuWeights as verifySkuWeight>
                        <tr>
                            <td>
                                <label class="checkbox-inline"> <input name="outIds" id="outIds" value="${verifySkuWeight.id}" text-align="center" class="ids" type="checkbox" /> ${verifySkuWeight.id}
                                </label>
                            </td>


                            <td ><img src="${verifySkuWeight.whSku.imageUrl}" width="50px" height="50px;" onclick="enlarge(this)" /></td>

                            <td>
                                <#if verifySkuWeight.whSku.id??>
                                    <a href="${CONTEXT_PATH}skus/skuDetail?whSkuId=${verifySkuWeight.whSku.id}" target="_blank">${verifySkuWeight.sku}</a>
                                <#else>
                                    ${verifySkuWeight.sku}
                                </#if>
                            </td>
                            <td>${verifySkuWeight.whSku.locationNumber}</td>
                            <td>${verifySkuWeight.whSku.name}</td>
                            <td>${verifySkuWeight.weight}</td>
                            <td>${verifySkuWeight.whSku.netWeight}</td>
                            <td>${verifySkuWeight.lastWeighingWeight}<#if verifySkuWeight.qcFirstWeight??></br>(首次称重:</br>${verifySkuWeight.qcFirstWeight})</#if></td>
                            <td>${verifySkuWeight.preVerifySkuWeight!'-'}</td>
                            <td>${verifySkuWeight.weightDifference}</td>
                            <td>${verifySkuWeight.skuNetWeightDiff}</td>
                            <td>
                                <#list verifySkuWeight.scanDTOS as scanDTO>
                                    ${scanDTO.weight}<#if scanDTO_has_next>,</#if>
                                </#list>
                            </td>
                            <td>${verifySkuWeight.systemPackageAttribute }</td>
                            <td>${verifySkuWeight.packagingAttributeStr }</td>
                            <td>${verifySkuWeight.specification}</td>
                            <td>${util('name',verifySkuWeight.weighingBy)}</br>${verifySkuWeight.weighingDate}</td>
                            <td>${util('name',verifySkuWeight.verifyBy)}</br>${verifySkuWeight.verifyDate}</td>
                            <td>${verifySkuWeight.remark}</td>
                            <td>${util('enumName', 'com.estone.sku.enums.VerifySkuFrom',verifySkuWeight.verifyFrom)}</td>
                            <td>${util('enumName', 'com.estone.sku.enums.VerifySkuStatus',verifySkuWeight.status)}</td>
                            <td>${verifySkuWeight.skuFrequency}</td>
                            <td>
                                <#if verifySkuWeight.status == 1><button type="button" class="btn btn-xs yellow" onclick="batchVerifySku(${verifySkuWeight.id}, 'false')">审核</button></#if>
                                <button type="button" class="btn btn-xs btn-info" onclick="viewLog(${verifySkuWeight.id}, 'verifySkuWeight')">日志</button>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
            </div>
        </div>
        <div id="fixed-bottom">
            <div id="pager"></div>
        </div>
    </div>
    <div id="dialog-form" title="审核称重结果" style="display: none">
        <form>
            <div class="form-group" style="height: 50px;font-size: 16px;">
                <label class="control-label col-md-1"></label>
                <div class="col-md-3">
                    <select id="checkRemark" class="control-label" style="width: 200px;">
                        <option value="">选择称重原因</option>
                        <option value="包装属性不符">包装属性不符</option>
                        <option value="商品材质密度差异">商品材质密度差异</option>
                        <option value="不规则产品">不规则产品</option>
                        <option value="开发要求更改尺寸">开发要求更改尺寸</option>
                        <option value="少配件">少配件</option>
                        <option value="新品首单">新品首单</option>
                        <option value="供应商首单">供应商首单</option>
                        <option value="SKU参数缺少">SKU参数缺少</option>
                        <option value="SKU信息变更">SKU信息变更</option>
                        <option value="开发换款换图">开发换款换图</option>
                        <option value="开发换款加图">开发换款加图</option>
                        <option value="入错库">入错库</option>
                        <option value="开发要求更改重量">开发要求更改重量</option>
                    </select>
                </div>
            </div>
            <fieldset>
                <label class="radio-inline">
                    <input type="radio" name="checkResult" checked value="PASS">
                    <span>审核通过</span>
                </label>
                <label class="radio-inline">
                    <input type="radio" name="checkResult" value="RETURN_DOWN">
                    <span>驳回</span>
                </label>
                <label class="radio-inline">
                    <input type="radio" name="checkResult" value="RETURN_AND_REGENERATE">
                    <span>驳回并生成新的称重任务</span>
                </label>
            </fieldset>
            </br>
            <div class="form-group hidden" id="recheckDiv" style="text-align: right;">
                <label class="control-label col-md-4">复核称重重量</label>
                <div class="col-md-5">
                    <input type="number" min="0" name="recheckWeight" />
                </div>
                <label class="control-label col-md-1">g</label>
            </div>
        </form>
    </div>
    <#include "/common/footer.html">
    <img id="enlarge" style='position:absolute;width:80%;height:90%;top:10%;left:15%;display:none;'/>
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript">

    $("#specification").val("${domain.query.specification}");

    var reg = /^[+-]?\d*\.?\d{0,2}$/;
    $(document).ready(function(){
        var submitForm = $('#domain');
        submitForm.validate({
            submitHandler : function(form) {
                var fromWeightDiff = $('#fromWeightDiff').val();
                var toWeightDiff = $('#toWeightDiff').val();
                if (fromWeightDiff != '' && !reg.test(fromWeightDiff)) {
                    layer.alert("请输入正确的数字，支持到小数点后两位!", 'error');
                    $('#fromWeightDiff').val('');
                    return;
                }
                if (toWeightDiff != '' && !reg.test(toWeightDiff)) {
                    layer.alert("请输入正确的数字，支持到小数点后两位!", 'error');
                    $('#toWeightDiff').val('');
                    return;
                }
                App.blockUI();

                form.submit();
            }
        });
    });

    function searchFormReset(obj) {
        formReset(obj);
        $('#fromCompareType').val('>');
        $('#conditionType').val('AND');
        $('#toCompareType').val('<');
        $('#netFromCompareType').val('>');
        $('#netConditionType').val('AND');
        $('#netToCompareType').val('<');
        $('#scaleFromCompareType').val('>');
        $('#scaleConditionType').val('AND');
        $('#scaleToCompareType').val('<');
        $("#status").val("1").trigger("change");
    }

    $.getJSON(CONTEXT_PATH + "system/saleusers/userByRole", function(json){
        if (json) {
            $("input[name='query.weighingBy']").select2({
                data : json,
                placeholder : "称重人",
                allowClear : true
            });

            $("input[name='query.verifyBy']").select2({
                data : json,
                placeholder : "审核人",
                allowClear : true
            });
        } else {
            $("input[name='query.weighingBy']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
            $("input[name='query.verifyBy']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
        }
    });

    var statusArray =  ${domain.statuses};
    $("input[name='query.status']").select2({
        data : statusArray,
        placeholder : "状态"
    });

    var verifySkuFromArray =  ${domain.verifySkuFrom};
    $("input[name='query.verifyFrom']").select2({
        data : verifySkuFromArray,
        placeholder : "来源"
    });

    // 子选项
    var outIds = $("#task-list").find("input[name='outIds']");

    function checkChange(tar) {
        var f = $(tar).is(":checked");
        outIds.each(function() {
            $(this).attr('checked',f);
            if (f) {
                $(this).closest("tr").find("td").css("background-color", "#B1DB87");
            } else {
                $(this).closest("tr").find("td").css("background-color", "");
            }
        })
    }

    function getCheckedIds(){
        var outIds = $("#task-list").find("input[name='outIds']:checked");
        var checkIds = "";
        for (var i = 0; i < outIds.length; i++) {
            var outId = outIds[i].value;
            if (i == 0) {
                checkIds += outId;
            } else {
                checkIds += "," + outId;
            }
        }
        return checkIds;
    }

    outIds.change(function() {
        var checkedLenght = outIds.filter(":checked").length;
        var length = outIds.length;
        // 全选
        var checkAll = $("#task-list").find("input[name='check-all']");
        checkAll.prop("checked", checkedLenght == length);

        // 改变背景色
        if ($(this).prop("checked")) {
            $(this).closest("tr").find("td").css("background-color", "#B1DB87");
        } else {
            $(this).closest("tr").find("td").css("background-color", "");
        }
    });

    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";
    $("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

    var heights = $("body").height();
    if(heights>910){
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }

    $('#downloadVerifySku').on('click',function () {

        var params = $('#domain').serializeArray();
        var ids = getCheckedIds();
        if(ids.length == 0 && total > 500000){
            layer.alert("最多导出500000条数据","error");
            return false;
        }
        params.push({name:"query.ids",value:ids});

        var url= CONTEXT_PATH + "verifySku/download";

        var tempForm = document.createElement("form");
        tempForm.id="tempForm";
        tempForm.method="post";
        tempForm.action=url;
        tempForm.target="blank";

        for (var i = 0; i < params.length; i++) {
            if (params[i] != null && params[i] != "" && params[i] != undefined) {
                var input = document.createElement("input");
                input.type = "hidden";
                input.name = params[i].name;
                input.value = params[i].value;
                tempForm.appendChild(input);
            }
        }

        console.log(tempForm);
        if (tempForm.attachEvent) {  // IE
            tempForm.attachEvent("onsubmit",function(){ window.open('about:blank','blank'); });
        } else if (tempForm.addEventListener) {  // DOM Level 2 standard
            tempForm.addEventListener("onsubmit",function(){ window.open('about:blank','blank'); });
        }
        document.body.appendChild(tempForm);
        if (document.createEvent) { // DOM Level 2 standard
            evt = document.createEvent("MouseEvents");
            evt.initMouseEvent("submit", true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
            tempForm.dispatchEvent(evt);
        } else if (tempForm.fireEvent) { // IE
            tempForm.fireEvent('onsubmit');
        }
        //必须手动的触发
        tempForm.submit();
        document.body.removeChild(tempForm);
    });

    // 产品图放大
    function enlarge(obj){
        var url = $(obj).attr("src");
        $("#enlarge").attr("src", url);
        $("#enlarge").show(300);
    }

    $("#enlarge").click(function() {$("#enlarge").hide(100);});

    $("input[name='checkResult']").change(function (){
        let val = $(this).val();
        let $dialog = $('#dialog-form');
        if(val == 'PASS'){
            $dialog.find("#recheckDiv").addClass("hidden");
            $dialog.find("#recheckDiv").find("input[name='recheckWeight']").val("");
        }else{
            $dialog.find("#recheckDiv").removeClass("hidden");
        }
    })

    /**
     * 审核称重
     */
    function batchVerifySku(id, superAudit){
        var ids = id;
        if(!ids){
            ids = getCheckedIds();
        }
        if (ids.length == 0) {
            layer.alert("请先选择至少一个SKU！");
            return false;
        }
        let $dialog = $('#dialog-form');
        if (id){
            $dialog.find("#recheckDiv").addClass("hidden");
        }else{
            $dialog.find("#recheckDiv").remove();
        }
        dialog({
            title: '审核称重结果',
            content: $dialog,
            width: 400,
            height: 150,
            top: 0,
            okValue: '确定',
            ok: function () {
                var checkResult = $('#dialog-form').find("input[name='checkResult']:checked").val();
                var remark = $('#dialog-form').find('#checkRemark').val();
                if (!remark){
                    layer.alert("请选择称重原因！","error");
                    return false;
                }
                if(!checkResult){
                    layer.alert("请选择审核称重结果！","error");
                    return false;
                }
                let recheckWeight = $dialog.find("#recheckDiv").find("input[name='recheckWeight']").val();
                var reg = /^\+?[1-9][0-9]*$|^[+]{0,1}(\d+\.\d{1,2})$/;
                if(recheckWeight != undefined && recheckWeight != "" && !reg.test(recheckWeight)){
                    layer.alert("复核称重重量需大于0且最多2位小数！","error");
                    return false;
                }
                $.ajax({
                    url: CONTEXT_PATH+"verifySku/checkSkuWeight",
                    type: "POST",
                    data: {ids: ids,checkResult:checkResult,remark:remark, recheckWeight:recheckWeight, superAudit:superAudit},
                    success: function(data){
                        if(data.status == 200){
                             layer.alert(data.message);
                            setTimeout(function() {
                                window.location.reload();
                            }, 10000);
                        }else {
                            // customizeLayer(data.message,'error');
                            dialog({
                                content: data.message,
                                width: 500,
                                height: 500,
                                top: 0,
                                okValue: '确定',
                                ok: function () {
                                    window.location.reload();
                                }
                            }).showModal();
                        }

                    }
                });
            },
            cancelValue: '取消操作',
            cancel: function () {
                window.location.reload();
            }
        }).showModal();
    }
</script>
</body>
</html>