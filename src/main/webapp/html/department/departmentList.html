<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">

		.modal-body input {
			width: 240px;
		}
	</style>
</head>
<body>
<@header method="header" active="14040000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">系统配置</a></li>
				<li class="active">用户</li>
				<li class="active">部门管理</li>
			</ul>
		</div>
	</div>
	<div class="container-fluid" style="background-color: white;border: none;padding: 50px;">
		<div class="row">
			<div class="portlet-body form" style="display: block;">
				<form action="${CONTEXT_PATH}system/departments"
					  class="form-horizontal form-bordered form-row-stripped"
					  method="post" modelAttribute="domain" name="deptForm" id ="domain">
					<div>
						<div style="margin-bottom: 10px;margin-left: 10px;">
							<div id="top_tool_bar">
								<div class="btn-group">
									<@header method="auth" authCode="ADD_DEPT">
										<button type="button"  class="btn blue" onclick="addDept()">
											<i class="icon-file-alt"></i> 添加部门
										</button>
									</@header>
								</div>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="row">
				<div class="col-md-5">
					<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list">
						<colgroup>
							<col width="10%" />
							<col width="20%" />
							<col width="5%" />
						</colgroup>
						<thead>
						<tr>
							<th>id</th>
							<th>部门</th>
							<th>操作</th>
						</tr>
						</thead>
						<tbody>
						<#list domain.departments as dept>
							<tr id="tr-item-${dept_index}">
								<td>${dept.departmentId}</td>
								<td>${dept.departmentName }</td>
								<td>
									<@header method="auth" authCode="EDIT_DEPT">
										<a class="btn btn-xs btn-default" onclick="editDept('${dept.departmentId}','${dept.departmentName}')">编辑</a>
									</@header>
								</td>
							</tr>
						</#list>
						</tbody>
					</table>
				</div>

			</div>
		</div>
	</div>
	<#include "/common/footer.html">
</div>
<script type="text/javascript">
	function addDept(){
		layer.prompt({
			title: '添加部门',
			formType: 0,
			value: '',
			maxlength: 140,
		}, function(value, index, elem){
			var department = {departmentName: value};
			$.ajax({
				url: "${CONTEXT_PATH}system/departments/create",
				type: "post",
				contentType: "application/json;charset=utf-8",
				dataType : "json",
				data: JSON.stringify(department),
				success: function (data) {
					if(data.status == 200){
						layer.alert("成功",function () {
							window.location.reload();
						});
					}else{
						layer.alert(data.message,function () {
							window.location.reload();
						});
					}
				}
			});
			layer.close(index);
		});
	}
	function editDept(id, name){
		layer.prompt({
			title: '编辑',
			formType: 0,
			value: name,
			maxlength: 140,
		}, function(value, index, elem){
			$.ajax({
				url: "${CONTEXT_PATH}system/departments/update",
				type: "post",
				data: {
					id: id,
					deptName: value
				},
				success: function (data) {
					if(data.status == 200){
						layer.alert("修改成功",function () {
							window.location.reload();
						});
					}else{
						layer.alert(data.message,function () {
							window.location.reload();
						});
					}
				}
			});
			layer.close(index);
		});
	}

</script>
</body>
</html>