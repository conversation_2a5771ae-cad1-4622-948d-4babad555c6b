<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

    <head>
        <title>易世通达仓库管理系统</title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <#include "/common/include.html">
        <style type="text/css">
            body {
                margin: 0mm;
                padding: 0mm;
                font-family: Arial;
            }

            .head1 {
                height: 5.5mm;
                width: 20mm;
                font-size: 4mm;
                text-align: center;
                border: 1px solid black;
                color: #000;
            }

            .head2 {
                height: 5.5mm;
                width: 35mm;
                font-size: 4mm;
                text-align: center;
                border: 1px solid black;
                color: #000;
                /* font-weight: bold; */
            }

            .head3 {
                height: 5.5mm;
                width: 23mm;
                font-size: 4mm;
                text-align: center;
                border: 1px solid black;
                color: #000;
            }

            .head4 {
                height: 5.5mm;
                width: 32mm;
                font-size: 4mm;
                text-align: center;
                border: 1px solid black;
                color: #000;
                /* font-weight: bold; */
            }

            .head6 {
                height: 5.5mm;
                width: 50mm;
                font-size: 4mm;
                text-align: center;
                border: 1px solid black;
                color: #000;
            }

            .head7 {
                height: 5.5mm;
                width: 45mm;
                font-size: 4mm;
                text-align: left;
                border: 1px solid black;
                color: #000;
            }

            .td2 {
                height: 5.5mm;
                width: 50mm;
                font-size: 4mm;
                text-align: center;
                border: 1px solid black;
                color: #000;
            }

            .td6 {
                height: 5.5mm;
                width: 40mm;
                font-size: 4mm;
                text-align: center;
                border: 1px solid black;
                color: #000;
            }

            .td7 {
                height: 5.5mm;
                width: 185mm;
                font-size: 4mm;
                text-align: left;
                border: 1px solid black;
                color: #000;
            }


            .foot {
                height: 5.5mm;
                font-size: 4mm;
                text-align: left;
                border: 1px solid black;
                color: #000;
            }

            table,
            td,
            th {
                border: 1px solid black;
                border-collapse: collapse;
                color: #000;
            }

            .td_foot {
                width: 30mm;
                font-size: 3mm;
                text-align: center;
                border: 1px solid black;
                color: #000;
                word-break:break-all;
            }
        </style>
    </head>

    <body>
        <@header method="header" active="15040400"><#include "/ftl/header.ftl"></@header>

        <div id="page" style="background-color: rgb(231, 237, 248)">
            <div class="row">
                <div class="col-md-12" style="padding: 0">
                    <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                        <li>
                            <a href="#">扫描交运</a>
                        </li>
                        <li class="active">中转仓装车详情</li>
                    </ul>
                </div>
            </div>

            <!-- 内容 -->
            <div class="container-fluid" style="background-color: white; border: none">
                <div class="row"></div>

                <div class="row">
                    <table id="blockcont000">
                        <caption>
                            <h3>物流发货单</h3>
                        </caption>
                        <#assign whPackCarRecord = domain.whPackCarRecord>
                        <thead>
                        <tr>
                            <td class="head1">物流公司:</td>
                            <td class="head2">${whPackCarRecord.shippingCompanyName }</td>
                            <td class="head1">客户名称:</td>
                            <td class="head2">深圳市易世通达贸易有限公司</td>
                            <td class="head3">揽收仓库:</td>
                            <td class="head4">明垣智谷仓</td>
                            <td class="head7">编号:${whPackCarRecord.loadNo}</td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td class="head1">序号</td>
                            <td class="td2" colspan="2">物流渠道</td>
                            <td class="head2">大包裹件数</td>
                            <td class="head3">大包裹重量</td>
                            <td class="head4">小包裹件数</td>
                            <td class="td6">备注</td>
                        </tr>
                        <#list domain.packCarDetailRecordVos?keys as key>
                            <tr>
                                <td class="head1">${key_index+1 }</td>
                                <td class="td2" colspan="2">${domain.packCarDetailRecordVos[key].logisticsCompanyName}</td>
                                <td class="head2">${domain.packCarDetailRecordVos[key].totalBagNum}</td>
                                <td class="head3">${domain.packCarDetailRecordVos[key].totalWeight/1000}</td>
                                <td class="head4">
                                    ${domain.packCarDetailRecordVos[key].totalNum}
                                </td>
                                <td class="td6" colspan="2"></td>
                            </tr>
                        </#list>

                        <tr>
                            <td class="head1">合计</td>
                            <td class="td2" colspan="2">————</td>
                            <td class="head2" style="font-weight: bold;">${domain.allTotalBagNum }</td>
                            <td class="head3" style="font-weight: bold;">${domain.allTotalWeight/1000 }</td>
                            <td class="head4" style="font-weight: bold;">${domain.allTotalNum }</td>
                            <td class="td6" colspan="2"></td>
                        </tr>
                        <tr>
                            <td class="head1">说明</td>
                            <td colspan="6" class="td7">
                                1、提货物流公司收货人须与我公司发货员当面交接清楚后签字确认，后续所有查证以本《物流发货单》记录为准； 2、本表单一式三份，公司发货员 / 账务文员 / 物流公司收货人各执一份，具有同等法律效力。</td>
                        </tr>
                        </tbody>

                        <tfoot>
                        <tr>
                            <td class="head1">发件人</br>名字:</td>
                            <td class="head2"></td>
                            <td class="head1">联系</br>电话:</td>
                            <td class="head1"></td>
                            <td class="head3">揽收司机</br>签字:</td>
                            <td class="head4"></td>
                            <td class="head7">揽收时间:
                                <br/>
                                ${whPackCarRecord.loadDate?date } ${.now?time}
                            </td>
                        </tr>
                        </tfoot>
                    </table>
                    <#if domain.pickupOrderNoList?? && domain.pickupOrderNoList?size gt 0>
                        <table id="item_table">
                            <tbody>
                            <tr style="height: 6mm;text-align: center;font-size: 4mm;border-top: 1px solid white;"><td colspan="7">小包国际运单号明细</td></tr>
                            <#list domain.pickupOrderNoList?chunk(7, '') as pickupNoRow>
                            <tr>
                                <#list pickupNoRow as pickupNo>
                                    <td class="td_foot">${pickupNo }</td>
                                </#list>
                            </tr>
                            </#list>
                            </tbody>
                        </table>
                        <div style="height: 10mm;"></div>
                    </#if>
                    <div>
                        <div class="col-md-offset-10" style="text-align: right">
                            <button type="button" class="btn blue" onclick="pint(${whPackCarRecord.id })">
                                <i class="icon-print"></i> 打印
                            </button>
                            <button type="button" class="btn blue" onclick="back(${domain.isTransfer })">
                                <i class="icon-backward"></i> 返回
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <#include "/common/footer.html">
        </div>

        <script type="text/javascript">
            // 打印
            function pint(id) {
                window.open(CONTEXT_PATH + "scan/whPackCarRecord/detail?printType=1&id=" + id);
            }
            // back
            function back(isTransfer) {
                //window.open(CONTEXT_PATH + "scan/whPackCarRecord");
                window.location.href = CONTEXT_PATH + "scan/whPackCarRecord?isTransfer=" + isTransfer;
            }
        </script>
    </body>

</html>