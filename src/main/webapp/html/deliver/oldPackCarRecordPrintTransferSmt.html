<#include "/common/include.html">
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

    <head>

        <div id="style">
            <style type="text/css">
                body {
                    margin: 0mm;
                    padding: 0mm;
                    font-family: Arial;
                }

                .head1 {
                    height: 5.5mm;
                    width: 20mm;
                    font-size: 4mm;
                    text-align: center;
                    border: 1px solid black;
                    color: #000;
                }

                .head2 {
                    height: 5.5mm;
                    width: 35mm;
                    font-size: 4mm;
                    text-align: center;
                    border: 1px solid black;
                    color: #000;
                    /* font-weight: bold; */
                }

                .head6 {
                    height: 5.5mm;
                    width: 50mm;
                    font-size: 4mm;
                    text-align: center;
                    border: 1px solid black;
                    color: #000;
                }

                .head7 {
                    height: 5.5mm;
                    width: 45mm;
                    font-size: 4mm;
                    text-align: left;
                    border: 1px solid black;
                    color: #000;
                }

                .td2 {
                    height: 5.5mm;
                    width: 50mm;
                    font-size: 4mm;
                    text-align: center;
                    border: 1px solid black;
                    color: #000;
                }

                .head3 {
                    height: 5.5mm;
                    width: 23mm;
                    font-size: 4mm;
                    text-align: center;
                    border: 1px solid black;
                    color: #000;
                }

                .head4 {
                    height: 5.5mm;
                    width: 32mm;
                    font-size: 4mm;
                    text-align: center;
                    border: 1px solid black;
                    color: #000;
                    /* font-weight: bold; */
                }

                .td6 {
                    height: 5.5mm;
                    width: 40mm;
                    font-size: 4mm;
                    text-align: center;
                    border: 1px solid black;
                    color: #000;
                }

                .td7 {
                    height: 5.5mm;
                    width: 185mm;
                    font-size: 4mm;
                    text-align: left;
                    border: 1px solid black;
                    color: #000;
                }

                .td8 {
                    height: 5.5mm;
                    width: 64mm;
                    font-size: 4mm;
                    text-align: center;
                    border: 1px solid black;
                    color: #000;
                    word-break:break-all;
                }

                .td_foot {
                    width: 30mm;
                    font-size: 3mm;
                    text-align: center;
                    border: 1px solid black;
                    color: #000;
                    word-break:break-all;
                }

                .foot {
                    height: 5.5mm;
                    font-size: 4mm;
                    text-align: left;
                    border: 1px solid black;
                    color: #000;
                }

                table,
                td,
                th {
                    border: 1px solid black;
                    border-collapse: collapse;
                    color: #000;
                }
            </style>
        </div>
        <div>
            <object  id="LODOP_OB" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" width=0 height=0>
                <embed id="LODOP_EM" type="application/x-print-lodop" width=0 height=0></embed>
            </object>
        </div>
        <script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
        <script src="${CONTEXT_PATH }js/print.js" type="text/javascript" ></script>
        <script>
            window.onload = function () {
                printerList();
            };
            /*jQuery(document).ready(function() {
                setTimeout(preprint, 1000);
            })*/
            function preprint() {
                var LODOP = getLodop(document.getElementById('LODOP_OB'), document
                    .getElementById('LODOP_EM'));
                LODOP.PRINT_INIT("物流发货单");
                LODOP.SET_PRINT_PAGESIZE(3, "210mm", "", "");
                LODOP.ADD_PRINT_HTM("2mm", "10mm", "190mm", "100%", document
                        .getElementById("style").innerHTML +
                    document.getElementById("div").innerHTML);
                LODOP.PREVIEW();
                // LODOP.PRINT();
            }

            //选择打印机后存储到cookie
            function changePrinter() {
                var Days = 300;
                var exp = new Date();
                //设置cookie过期时间
                exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
                document.cookie = "printn=" + $("#printer").val() + ";expires=" + exp.toGMTString();
            }

            // 分页打印
            function myPagePrint4() {

                LODOP = getLodop(document.getElementById('LODOP_OB'), document.getElementById('LODOP_EM'));
                LODOP.PRINT_INIT("打印");
                getPrinter();
                var innerHtml = "";

                $("[id^='print-item-']").each(function(i, obj) {
                    if (i > 0){
                        LODOP.NewPage();
                    }
                    LODOP.ADD_PRINT_HTM(0, 1, "210mm", "297mm", document.getElementById("style").innerHTML + $(obj).html());
                });
                LODOP.PRINT();
                // LODOP.PREVIEW();
            };

            function myPreview(){
                CreatePrintPage();
                LODOP.PREVIEW();
            }

            function CreatePrintPage() {
                LODOP = getLodop(document.getElementById('LODOP_OB'), document.getElementById('LODOP_EM'));
                LODOP.PRINT_INIT("打印");
                getPrinter();
                $("[id^='print-item-']").each(function(i, obj) {
                    if (i > 0){
                        LODOP.NewPage();
                    }
                    LODOP.ADD_PRINT_HTM(0, 1, "210mm", "297mm", document.getElementById("style").innerHTML + $(obj).html());
                });
            };

        </script>
    </head>

    <body>
        <div class="printbtn">
            <button onclick="myPreview();">打印预览</button>
            &nbsp;
            <button onclick="myPagePrint4(1);">打印</button>
            <input type="hidden" id="print-size" value="1">
            &nbsp;
        </div>
        <h5>
            选择打印机&nbsp;&nbsp;
            <select id="printer" onchange="changePrinter()"></select>
        </h5>
        <div id="div">
        <div id="print-item-a">
            <table id="blockcont000">
                <caption>
                    <h3>揽收交接清单</h3>
                </caption>
                <#assign whPackCarRecord = domain.whPackCarRecord>
                <thead>
                <tr>
                    <td class="head1">物流公司:</td>
                    <td class="head2">${whPackCarRecord.shippingCompanyName }</td>
                    <td class="head1">客户名称:</td>
                    <td class="head2">深圳市易世通达贸易有限公司</td>
                    <td class="head3">揽收仓库:</td>
                    <td class="head4">明垣智谷仓</td>
                    <td class="head7">编号:${whPackCarRecord.loadNo}</td>
                </tr>
                </thead>

                <tbody>
                <tr>
                    <td class="head1">序号</td>
                    <td class="td2" colspan="2">物流渠道</td>
                    <td class="head2">大包裹件数</td>
                    <td class="head3">大包裹重量</td>
                    <td class="head4">小包裹件数</td>
                    <td class="td6">备注</td>
                </tr>

                <#list domain.packCarDetailRecordVos?keys as key>
                    <tr>
                        <td class="head1">${key_index+1 }</td>
                        <td class="td2" colspan="2">${domain.packCarDetailRecordVos[key].logisticsCompanyName}</td>
                        <td class="head2">${domain.packCarDetailRecordVos[key].totalBagNum}</td>
                        <td class="head3">${domain.packCarDetailRecordVos[key].totalWeight/1000}</td>
                        <td class="head4">
                            ${domain.packCarDetailRecordVos[key].totalNum}
                        </td>
                        <td class="td6" colspan="2"></td>
                    </tr>
                </#list>

                <tr>
                    <td class="head1">合计</td>
                    <td class="td2" colspan="2">————</td>
                    <td class="head2" style="font-weight: bold;">${domain.allTotalBagNum }</td>
                    <td class="head3" style="font-weight: bold;">${domain.allTotalWeight/1000 }</td>
                    <td class="head4" style="font-weight: bold;">${domain.allTotalNum }</td>
                    <td class="td6" colspan="2"></td>
                </tr>
                <tr>
                    <td class="head1">说明</td>
                    <td colspan="6" class="td7">
                        1、提货物流公司收货人须与我公司发货员当面交接清楚后签字确认，后续所有查证以本《物流发货单》记录为准； 2、本表单一式三份，公司发货员 / 账务文员 / 物流公司收货人各执一份，具有同等法律效力。
                    </td>
                </tr>
                </tbody>

                <tfoot>
                <tr>
                    <td class="head1">发件人</br>名字:</td>
                    <td class="head2"></td>
                    <td class="head1">联系</br>电话:</td>
                    <td class="head1"></td>
                    <td class="head3">揽收司机</br>签字:</td>
                    <td class="head4"></td>
                    <td class="head7">揽收时间:
                        <br/>
                        ${whPackCarRecord.loadDate?date } ${.now?time}
                    </td>
                </tr>
                </tfoot>
            </table>
            <#if domain.pickupOrderNoList?? && domain.pickupOrderNoList?size gt 0>
            <table id="item_table">
                <tbody>
                <tr style="height: 6mm;text-align: center;font-size: 4mm;border-top: 1px solid white;"><td colspan="7">揽收单明细</td></tr>
                    <#list domain.pickupOrderNoList?chunk(7, '') as pickupNoRow>
                        <tr>
                            <#list pickupNoRow as pickupNo>
                                <td class="td_foot" style="word-break:break-all;">${pickupNo }</td>
                            </#list>
                        </tr>
                    </#list>
                </tbody>
            </table>
            <div style="height: 10mm;"></div>
            </#if>
        </div>
            <#if domain.shipmentList?? && domain.shipmentList?size gt 0>
                <#list domain.shipmentList as shipment>
                <div id="print-item-${shipment_index }">
                    <table class="item_table2">
                        <caption>
                            <h3>揽收交接清单(小包明细)</h3>
                        </caption>
                        <thead>
                        <tr>
                            <td class="head1">交接单<br/>编号</td>
                            <td class="td6" style="word-break:break-all;">${whPackCarRecord.loadNo}</td>
                            <td class="head3">结袋卡号</td>
                            <td class="td8" colspan="2">${shipment.bagNo}</td>
                            <td class="head3">揽收单号</td>
                            <td class="td6" style="word-break:break-all;">${shipment.pickupOrderNo}</td>
                        </tr>
                        </thead>
                    </table>
                    <table class="item_table2">
                        <tbody>
                        <#if shipment.trackingNoList?? && shipment.trackingNoList?size gt 0>
                        <tr style="height: 6mm;text-align: center;font-size: 4mm;border-top: 1px solid white;"><td colspan="7">小包明细</td></tr>
                        <#list shipment.trackingNoList?chunk(7, '') as trackingNoRow>
                        <tr>
                            <#list trackingNoRow as trackingNo>
                            <td class="td_foot" style="word-break:break-all;">${trackingNo }</td>
                        </#list>
                        </tr>
                        </#list>
                    </#if>
                    </tbody>
                    </table>
                </div>
                </#list>
            </#if>
        </div>
    </body>

</html>