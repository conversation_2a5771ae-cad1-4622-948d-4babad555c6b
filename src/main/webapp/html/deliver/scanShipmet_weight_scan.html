<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	</head>

	<body>
		<dialog>
			<div id="check_scan_datas" class="border-gray p5">
				<#if !domain.whScanShipments>
					<div class="alert alert-danger">
							<h3>结袋称重失败: ${domain.weightErrorMes}</h3>
					</div>
				<#else>
					<#assign checkConf = domain.reconfirm && domain.reconfirm == true>
					<#if checkConf>
						<dialog id="reconf" style="width: 30%;border:1px solid #ddd; padding: 15px;text-align: left;margin-left:5%;" open>
							<h3 style="">结袋卡核重拦截</h3>
							<hr/>
							<span>结袋卡称重与扫描的重量差超过配置的允许范围，请确认称重重量是否无误，如果称重重量无误，请拦截该大包核重或者更换有强制核重权限的账号继续操作。</span>
							<br/>
							<br/>
							<span>拦截下来的包裹需要独立放置并检查结袋卡内小包是否无误。</span>
							<br/>
							<br/>
							<br/>
							<div style="display: flex; justify-content: flex-end;">
								<button type="button" class="btn btn-default" onclick="cancelCheck()">取消</button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<@header method="auth" authCode="SHIPMENT_FORCED_CHECK">
								<button type="button" class="btn btn-info" onclick="confirmCheck('${domain.bagNo}', '${domain.weight}')">强制核重</button>
								</@header>
							</div>
						</dialog>
					</#if>
					<#list domain.whScanShipments as whScanShipment>
						<form class="form-horizontal submit-form mb20 ${checkConf?string('hide', '')}"
							  name="submitForm" id="submit-form-${whScanShipment_index}" action="" method="POST">
							<div id="common-param-${whScanShipment_index}">
							</div>
							<table class="table table-bordered table-striped table-condensed whScanShipment">
								<colgroup>
									<col width="15%" />
									<col width="8%" />
									<col width="5%" />
									<col width="8%" />
									<col width="8%" />
									<col width="8%" />
									<col width="8%" />
									<col width="8%" />
									<col width="5%" />
									<col width="8%" />
									<col width="5%" />
									<col width="12%" />
								</colgroup>
								<thead>
									<tr class="">
										<th>结袋卡号</th>
										<th>物流渠道</th>
										<th>包裹数量</th>
										<th>扫描员</th>
										<th>核重员</th>
										<th>包裹累计重量(g)</th>
										<th>实际重量(g)</th>
										<th>重量差(g)</th>
										<th>结袋包材</th>
										<th>装车员</th>
										<th>状态</th>
										<th>核重时间</th>
									</tr>
								</thead>
								<tbody>
									<#if ( whScanShipment.minScope?? && whScanShipment.maxScope??) &&(whScanShipment.weightDiff lt whScanShipment.minScope || whScanShipment.weightDiff gt whScanShipment.maxScope) >
										<tr style="color: red;" class="whScanShipment-tr" id="whScanShipment-tr-${whScanShipment_index}">
										<#else>
										<tr class="whScanShipment-tr" id="whScanShipment-tr-${whScanShipment_index}">
									</#if>
										<td>${whScanShipment.bagNo}</td>
										<td>${whScanShipment.logisticsCompanyCode }</td>
										<td>${whScanShipment.scanApvQuantity}</td>
										<td>${util('name',whScanShipment.scanner)}</td>
										<td>${util('name',whScanShipment.checkWeightUser)}</td>
										<td>${whScanShipment.systemTotalWeight }</td>
										<td>${whScanShipment.actualTotalWeight}</td>
										<td>${whScanShipment.weightDiff}</td>
										<td>
											<#if whScanShipment.packagingMaterialName??>${whScanShipment.packagingMaterialName}</#if>
											<#if whScanShipment.packagingMaterialWeight??>-${whScanShipment.packagingMaterialWeight}g</#if>
										</td>
										<td>${util('name',whScanShipment.loadUser)}</td>
										<td>${whScanShipment.statusName}</td>
										<td>
											${whScanShipment.checkWeightDate }
										</td>
									</tr>
								</tbody>
							</table>
						</form>
					</#list>
				</#if>
			</div>
		</div>
	</body>

</html>