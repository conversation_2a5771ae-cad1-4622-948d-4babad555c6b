<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<#include "/common/include.html">
		<style type="text/css">
			.modal-body input {
				width: 240px;
			}
			
			.top_bar {
				position: fixed;
				top: 0px;
			}
			
			#task-list td {
				vertical-align: middle;
			}
			
			.scan-a {
				width: 100px;
				height: 100px;
				line-height: 85px;
				text-align: center;
				margin-bottom: 50px;
			}
			
			#d-top {
				right: 15px;
			}
		</style>
	</head>

	<body>
		<@header method="header" active="12070000"><#include "/ftl/header.ftl"></@header>

		<div id="page" style="background-color: rgb(231, 237, 248)">
			<div class="row">
				<div class="col-md-12" style="padding: 0">
					<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
						<li>
							<a href="#">扫描交运</a>
						</li>
						<li class="active">结袋称重</li>
					</ul>
				</div>
			</div>

			<!-- 内容 -->
			<div class="container-fluid" style="background-color: white;border: none">

				<!-- END PAGE HEADER-->
				<div class="row">
					<div class="col-md-12" style="text-align: right;">
						<div class="form-group">
							<div class="panel-header" style="overflow: hidden;">
								<div style="text-align: left;margin-left: 180px" class="fl panel-title2" id="panel-title">
									<h1 style="color:blue;font-size:48px;">成功:<b>0</b></h1>
								</div>
							</div>

						</div>
						<div class="form-group">

							<label class="control-label col-md-1">结袋卡号</label>
							<div class="col-md-2">
								<input type="text" class="form-control" name="bagNo" id="bagNo" value="" onkeypress="if(event.keyCode==13) { inputBagNo(this); return false;}" tabindex="4">
								<!-- <input type="hidden" id="hidden-bagNo" value=""> -->
							</div>

							<label style="color: red" class="control-label col-md-1">结袋包材</label>
							<div class="col-md-2">
								<select id="packagingMaterialId" name="packagingMaterialId" class="form-control input-large input-inline">
									<option selected="selected" value="">无</option>
									<#if domain.whPackagingMaterialManagements??>
										<#list domain.whPackagingMaterialManagements as whPackagingMaterialManagement>
											<option value="${whPackagingMaterialManagement.id}" >${whPackagingMaterialManagement.name}-${whPackagingMaterialManagement.weight}g</option>
										</#list>
									</#if>
								</select>
							</div>
							<label class="control-label col-md-1">重量</label>
							<div class="col-md-2">
								<input type="text" class="form-control" name="weight" id="weight" value="" onkeypress="if(event.keyCode==13) { inputWeight(this); return false;}">
							</div>
						</div>

						<div class="clearfix" style="border-bottom: 1px solid #ccc; margin-top: 60px;"></div>

						<div style="margin-top: 20px;">
							<div id="check_scan_datas" class="border-gray p5 col-md-11" style="min-height:500px;">

							</div>
						</div>
					</div>
				</div>
				<!-- end row -->

			</div>
			<#include "/common/footer.html">
		</div>
		<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.sound.js?v=${.now?datetime}"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/web-storage-cache.js"></script>
		<script type="text/javascript">
			var cacheKey = "scanShipment_success";
			$(document).ready(function() {
				pageInit();

				var storage = new WebStorageCache();
				if(storage.get(cacheKey)) {
					lastSuc = storage.get(cacheKey);
					$('#panel-title').html('<h1  style="color:blue;font-size:48px;">成功：<b>' + lastSuc + '</b></h1>');
				}

			});

			// 初始化
			function pageInit() {
				$('#bagNo').val("");
				$('#weight').val('');
				$('#weight').focus();
				$('#check_scan_datas').html('');
			}

			// 输入电子秤重量
			function inputWeight(obj) {
				if(!obj.value || obj.value.replace(/\s/g, '') == '') {
					layer.alert("请称重!", 'error');
					$('#weight').val('');
					$('#weight').focus();
					return;
				}
				debugger;
				var weight = obj.value.replace(/\s/g, '');
				var reg = /^[-\+]?\d+(\.\d+)?$/; //Double
				if(!reg.test(weight)) {
					layer.alert("请输入正确的重量", 'error');
					$('#weight').val('');
					$('#weight').focus();
					return;
				}

				if(weight>1000){
					layer.alert("输入重量大于1000Kg,请确认重新输入", 'error');
					$('#weight').val('');
					$('#weight').focus();
					return;
				}

				if(weight<=0){
					layer.alert("输入重量小于等于0g,请确认重新输入", 'error');
					$('#weight').val('');
					$('#weight').focus();
					return;
				}

				$('#bagNo').val('');
				$('#bagNo').focus();
			}

			//输入结袋号触发
			function inputBagNo(obj) {
				if(!obj.value || obj.value.replace(/\s/g, '') == '') {
					layer.alert("请输入快递单号!", 'error');
					$('#bagNo').val('');
					$('#bagNo').focus();
					return;
				}
				var bagNo = obj.value.replace(/\s/g, '').trim();
				if(bagNo.length < 10) {
					layer.alert("请输入正确的快递单号!", 'error');
					$('#bagNo').val('');
					$('#bagNo').focus();
					return;
				}
				var weight = $("#weight").val().trim();
				if(weight == '') {
					layer.alert("请称重!", 'error');
					$('#weight').val('');
					$('#weight').focus();
					return;
				} else {
					var reg = /^[-\+]?\d+(\.\d+)?$/; //Double
					if(!reg.test(weight)) {
						layer.alert("请输入正确的重量", 'error');
						$('#weight').val('');
						$('#weight').focus();
						return;
					}
				}
				//$('#weight').value = (weight * 1000).toFixed(1);
				weight = weight * 1000;// 电子秤单位是KG
				submitTable(bagNo, weight, null);
			}

			// 提交数据
			function submitTable(bagNo, weight, forced) {
				var packagingMaterialId = $("#packagingMaterialId").val();
				// 获取快递单相关入库单
				var r = $.ajax({
					url: CONTEXT_PATH + "scan/whScanShipment/checkWeight",
					data: { bagNo: bagNo, weight: weight, packagingMaterialId: packagingMaterialId, forced: forced },
					timeout: 30000,
					beforeSend: function() {
						App.blockUI();
						//$("#bagNo").attr("disabled", true);
					},
					success: function(response) {
						App.unblockUI();
						if(response.match("^\{(.+:.+,*){1,}\}$")) {
							var jsonObject = eval("(" + response + ")");
							$.error(jsonObject.message);
							return;
						}

						var responseHtml = $(response).find("#check_scan_datas").html();
						$("#check_scan_datas").html(responseHtml);

						if($("#check_scan_datas").find(".whScanShipment").length > 0) {
							audioPlay("success");
							calsf();
							// 防止重复扫
							setTimeout(removeDisabled, 1000);
						} else {
							audioPlay("error");
						}

						$('#bagNo').val('');
						$('#weight').val('');
						$('#weight').focus();
					},
					error: function() {
						App.unblockUI();
						$('#bagNo').val("");
						$('#bagNo').focus();
						layer.alert('扫描失败，请重新扫描', 'error');
						audioPlay("error");
					}
				});
			}

			function confirmCheck(bagNo, weight) {
				submitTable(bagNo, weight, true);
			}

			function cancelCheck() {
				document.getElementById('reconf').close();
				$('form[name="submitForm"]').removeClass("hide");
			}

			function removeDisabled() {
				$("#bagNo").removeAttr("disabled");
				$('#bagNo').val("");
				$('#weight').val('');
				$('#weight').focus();
				//$('#bagNo').focus();
			}

			// 统计扫描成功和失败的数量
			function calsf() {
				var storage = new WebStorageCache();
				// 一开始没有缓冲，所以这次数量算1
				var lastSuc = 0;
				if(storage.get(cacheKey)) {
					lastSuc = storage.get(cacheKey);
					lastSuc++;
				} else {
					lastSuc = 1;
				}
				storage.set(cacheKey, lastSuc, { exp: 5 * 60 * 60 });
				$('#panel-title').html('<h1 style="color:blue;font-size:48px;">成功：<b>' + lastSuc + '</b></h1>');
			}

			// 计数
			function calsfPiece() {
				var storage = new WebStorageCache();
				var lastSuc = 0;
				if(storage.get(pieceCacheKey)) {
					lastSuc = storage.get(pieceCacheKey);
					lastSuc = lastSuc + 1;
				} else {
					lastSuc = 1;
				}

				storage.set(pieceCacheKey, lastSuc, { exp: 5 * 60 * 60 });
				return lastSuc;
			}
		</script>
		<!-- END JAVASCRIPTS -->
	</body>

</html>