<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<#include "/common/include.html">
        <#include "/common/webSocket.html">
		<style type="text/css">
			.modal-body input {
				width: 240px;
			}
			
			.return-model {
				padding: 12px;
			}
			
			#return-info-btn {
				color: #ffff;
				background-color: #5bc0de;
				padding: 3px;
			}
		</style>
	</head>

	<body>
		<@header method="header" active="12070000"><#include "/ftl/header.ftl"></@header>

		<div id="page" style="background-color: rgb(231, 237, 248)">
			<div class="row">
				<div class="col-md-12" style="padding: 0">
					<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
						<li>
							<a href="#">扫描交运</a>
						</li>
						<li class="active">装车发货管理</li>
					</ul>
				</div>
			</div>

			<!-- 内容 -->
			<div class="container-fluid" style="background-color: white;border: none">
				<div class="row">
					<div class="col-md-12">
						<#assign query = domain.query>
						<form action="${CONTEXT_PATH}scan/whPackCarRecord/search" class="form-horizontal form-bordered form-row-stripped" method="post" modelAttribute="domain" name="packCarRecordForm" id="domain">
							<!-- 分页信息 -->
							<input id="page-no" type="hidden" name="page.pageNo" value="1">
							<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">

							<div class="form-body">
								<div class="form-group">
									<label class="control-label col-md-1">ID</label>
									<div class="col-md-3">
										<input class="form-control" id="queryId" name="query.id" type="number" placeholder="请输入ID" value="${query.id}">
									</div>

									<label class="control-label col-md-1">装车编号</label>
									<div class="col-md-3">
										<input class="form-control" name="query.loadNo" type="text" placeholder="装车编号" value="${query.loadNo}">
									</div>

									<label class="control-label col-md-1">物流公司</label>
									<div class="col-md-3">
										<input class="form-control" name="query.shippingCompanyCode" type="text" placeholder="物流公司" value="${query.shippingCompanyCode}">
									</div>
								</div>

								<div class="form-group">
									<label class="control-label col-md-1">装车时间</label>
									<div class="col-md-3">
										<div class="input-group">
											<input class="form-control Wdate" type="text" name="query.fromLoadDate" placeholder="" readonly="readonly" value="${query.fromLoadDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
											<span class="input-group-addon">到</span>
											<input class="form-control Wdate" type="text" name="query.toLoadDate" placeholder="" readonly="readonly" value="${query.toLoadDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
										</div>
									</div>

									<label class="control-label col-md-1">装车人</label>
									<div class="col-md-3">
										<input class="form-control" name="query.loadUser" type="text" value="${query.loadUser}">
									</div>

									<label class="control-label col-md-1 type-label">打印次数</label>
									<div class="col-md-3 input-group type-label">
										<input type="number" name="query.thanQuantity" value="${query.thanQuantity }"
											   class="Wdate form-control" digits="true"/>
										<span class="input-group-addon">到</span>
										<input type="number" name="query.lessQuantity" value="${query.lessQuantity }"
											   class="Wdate form-control" digits="true"/>
									</div>
								</div>

								<div class="form-group">
									<label class="control-label col-md-1">揽收单号</label>
									<div class="col-md-3">
										<input class="form-control" name="query.pickupOrderNo" type="text" placeholder="多个查询以逗号分开" value="${query.pickupOrderNo}">
									</div>
									<label class="control-label col-md-1">快递单号</label>
									<div class="col-md-3">
										<input class="form-control" name="query.expressOrderNo" type="text" placeholder="多个查询以逗号分开" value="${query.expressOrderNo}">
									</div>
									<label class="control-label col-md-1">提货方式</label>
									<div class="col-md-3">
										<input class="form-control" name="query.deliveryType" type="text" placeholder="提货方式" value="${query.deliveryType}">
									</div>
								</div>
								<div class="form-group">
									<label class="control-label col-md-1">南宁仓包裹</label>
									<div class="col-md-3 input-group">
										<select class="form-control" name="query.isNanNingShop" value="${query.isNanNingShop}">
											<option ></option>
											<option <#if (query.isNanNingShop)?? && query.isNanNingShop == true>selected</#if> value="true">是</option>
											<option <#if (query.isNanNingShop)?? && query.isNanNingShop == false>selected</#if> value="false">否</option>
										</select>
									</div>
								</div>
							</div>
							<div>
								<div class="col-md-offset-10" style="text-align: right">
                                    <@header method="auth" authCode="LOAD_CAR_DOWNLOAD_DETAIL">
                                    <button type="button" class="btn btn-default" onclick="downloadDetails()">
                                        <i class="icon-download"></i> 导出明细
                                    </button>
                                    </@header>
									<button type="button" class="btn btn-default" onclick="formReset(this)">
										<i class="icon-refresh"></i> 重置
									</button>
									<button type="submit" class="btn blue">
										<i class="icon-search"></i> 查询
									</button>
								</div>
							</div>
						</form>
					</div>
					<br/>
				</div>

				<div class="row">
					<div class="col-md-12">
						<!-- 内容  -->
						<table class="table table-bordered table-hover table-condensed" id="packCarRecord-list">
							<colgroup>
								<col width="5%" />
								<col width="10%" />
								<col width="10%" />
								<col width="10%" />
								<col width="10%" />
								<col width="10%" />
								<col width="8%" />
								<col width="8%" />
								<col width="8%" />
								<col width="7%" />
								<col width="14%" />
							</colgroup>
							<thead>
								<tr>
									<th><input type="checkbox" id="check-all" name="checkAll">全选</th>
									<th>发货日期</th>
									<th>物流公司</th>
									<th>扫描总袋数</th>
									<th>装车员</th>
									<th>打印次数</th>
									<th>打印操作人</th>
									<th>上传人</th>
									<th>交接单</th>
									<th>提货方式</th>
									<th>操作</th>
								</tr>
							</thead>
							<tbody>
								<#list domain.whPackCarRecords as whPackCarRecord>
									<tr>
										<td>
											<input type="checkbox" value="${whPackCarRecord.id}" name="ids"> ${whPackCarRecord.id}
										</td>
										<td>
											${whPackCarRecord.loadDate?date }
										</td>
										<td>${whPackCarRecord.shippingCompanyName}</td>
										<td>${whPackCarRecord.totalBagNum}</td>
										<td>${util('name',whPackCarRecord.loadUser)}</td>
										<td>${whPackCarRecord.printNum}</td>
										<td>${util('name',whPackCarRecord.printUser)}</td>
										<td>${util('name',whPackCarRecord.uploadUser)}</td>
										<td>
											<#if whPackCarRecord.loadInvoiceUrl??>
												<img src="${whPackCarRecord.loadInvoiceUrl}" width="50px" height="50px;" onclick="enlarge(this)" />
											</#if>
										</td>
										<td>${util('enumName', 'com.estone.scan.deliver.enums.DeliveryTypeEnum', whPackCarRecord.deliveryType) }</td>
										<td>
											<button type="button" class="btn btn-info btn-xs" onclick="location.href='${CONTEXT_PATH}scan/whPackCarRecord/detail?printType=1&id=${whPackCarRecord.id}'">打印</button>
											<button type="button" class="btn btn-info btn-xs" onclick="location.href='${CONTEXT_PATH}scan/whPackCarRecord/detail?printType=0&id=${whPackCarRecord.id}'">查看</button>
											<button type="button" class="btn btn-info btn-xs" onclick="downloadDetail(${whPackCarRecord.id})">上传到文件服务器</button>
											<br/>
											<span class="btn btn-info btn-xs fileinput-button file0">
												<span class="icon-upload" onclick="$(this).next().click()">上传交接单</span>
												<input type="file" name="file" onchange="fileinputSubmit(${whPackCarRecord.id}, this)" style="display: none"></input>
											</span>
											<#if whPackCarRecord.deliverFileUrl??>
												<a class="btn btn-info btn-xs" href="${whPackCarRecord.deliverFileUrl}">下载</a>
											</#if>
											<#if whPackCarRecord.isPrintLanShouNo >
                                                <#if whPackCarRecord.shopeePdf??>
												    <!--<button type="button" class="btn btn-info btn-xs" onclick="printPdf(${whPackCarRecord.shopeePdf})">打印揽收面单</button>-->
                                                    <button type="button" class="btn btn-info btn-xs" onclick="printShopeePDF(${whPackCarRecord.id})">打印揽收面单</button>
                                                <#else>
												    <button type="button" class="btn btn-info btn-xs" id="uploadDataBtn_${whPackCarRecord.id}" onclick="printLanShouTag(${whPackCarRecord.id})">生成提货码</button>
                                                </#if>
											</#if>
											<#if whPackCarRecord.shippingCompanyCode?? && whPackCarRecord.shippingCompanyCode == 'SMTYXC_JB'>
												<button type="button" class="btn btn-info btn-xs"
														onclick="downloadPacDetail(${whPackCarRecord.id})">预报导出
												</button>
											</#if>
											<#if whPackCarRecord.deliveryType?? && whPackCarRecord.deliveryType == 3>
												<button type="button" class="btn btn-info btn-xs"
														onclick="showEditModal('${whPackCarRecord.id}', '${whPackCarRecord.loadDate?date }',
														'${whPackCarRecord.shippingCompanyName}', '${whPackCarRecord.totalBagNum}')">编辑提货
												</button>
											</#if>
											<button type="button" class="btn btn-info btn-xs" onclick="location.href='${CONTEXT_PATH}scan/whPackCarRecord/detail?printType=1&printJit=1&id=${whPackCarRecord.id}'">打印JIT交接单</button>
										</td>
									</tr>
								</#list>
							</tbody>
						</table>
						<!-- 内容end -->
					</div>
				</div>
				<div id="fixed-bottom">
					<div id="pager"></div>
				</div>
			</div>
			<#include "/common/footer.html">
		</div>

		<div style="display:none;" id="shippingCompanyList">
			[{"id":"", "text":""}
			<#list domain.collectCompanyList as shippingCompany>,{"id":"${shippingCompany.code}", "text":"${shippingCompany.code}-${shippingCompany.name}"}</#list>]
		</div>
		<div style="display: none" id="printWindow">
			<iframe src="javascript:void(0);" frameborder="0" marginheight="0" marginwidth="0" scrolling="auto"
					id="printFrame" name="printFrame" width="100%" height="600px"></iframe>
		</div>

		<div style="margin-top: 100px" class="modal fade" id="editDeliveryInfoModal" tabindex="-1" role="dialog" aria-labelledby="editDeliveryInfoModalLabel" aria-hidden="true">
			<#include "/deliver/packCarDeliveryInfoEdit.html">
		</div>

		<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
		<script type="text/javascript">
			// 分页
			var total = "${domain.page.totalCount}";
			var pageNo = "${domain.page.pageNo}";
			var pageSize = "${domain.page.pageSize}";
			$("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });

			var heights = $("body").height();
			if(heights > 910) {
				$("#fixed-bottom").addClass("fixed-bottom-height")
			}

			// 物流公司
			var shippingCompanyArray = jQuery.parseJSON($("#shippingCompanyList").text());
			$("input[name='query.shippingCompanyCode']").select2({
				data: shippingCompanyArray,
				placeholder: "物流公司",
				multiple: false,
				allowClear: true
			});

			// 物流公司
			var deliveryTypeArray = ${domain.deliveryTypeSelect };
			$("input[name='query.deliveryType']").select2({
				data: deliveryTypeArray,
				placeholder: "提货方式",
				multiple: false,
				allowClear: true
			});

			// 全选
			var checkAll = $("input[name='checkAll']");

			// 子选项
			var itemIds = $("input[name='ids']");
			checkAll.change(
				function() {
					itemIds.prop("checked", $(this).prop("checked"));
					itemIds.each(function() {
						var f = $(this).is(":checked");
						var checkClass = $(this).prop("class");
						$("." + checkClass).each(function() {
							$(this).prop("checked", f);
						})
					})
				}
			);
			// 获取选中的结袋卡
			function getCheckedIds() {
				var checkedIds = $("input[name='ids']:checked");
				return checkedIds;
			}

            //导出
            function downloadDetails(){

                let uuid = getFileQueueUUID();

                var checkedIds = getCheckedIds();
                var diglog = dialog({
                    title: '导出',
                    width: 350,
                    height:100,
                    url: CONTEXT_PATH + "scan/whPackCarRecord/downloadmode?type=2&uuid=" + uuid,
                    okValue: '确定',
                    ok: function () {
                        var exportWindow = $(this.iframeNode.contentWindow.document.body);

                        $("#page-no").val("${domain.page.pageNo}");
                        var submitForm = exportWindow.find("#submit-form");

                        var exportType = submitForm.find("input[name='exportType']:checked").val();

                        var submitFormParam = $('#domain').serialize();

                        // 导出当前选择
                        if(exportType == 3) {
                            if(checkedIds.length == 0) {
                                layer.alert("请选择要操作的数据");
                                return false;
                            }else{
                                submitFormParam = submitFormParam + "&" +checkedIds.serialize();
                            }
                        }

                        submitFormParam = submitFormParam + "&exportType=" + exportType + "&uuid=" + uuid;

                        //还原分页
                        // $("#page-no").val("${domain.page.pageNo}");

                        downloadByPostForm(submitFormParam, CONTEXT_PATH + "scan/whPackCarRecord/download");

                        $("#page-no").val("1");

                        beginExport(uuid, '导出装车发货明细');
                        setTimeout(function () {
                            diglog.close().remove();
                        }, 100);

                        return true;
                    },
                    cancelValue: '取消',
                    cancel: function () {}
                });
                diglog.show();
            }

			// 装车人
			$.getJSON(CONTEXT_PATH + "system/saleusers/userByRoleName?roleName=扫描交运", function(json) {
				if(json) {
					$("input[name='query.loadUser']").select2({
						data: json,
						placeholder: "装车人",
						allowClear: true
					});
				} else {
					$("input[name='query.loadUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
				}
			});
			
			// 上传扫描详情到FTP
			function downloadDetail(id){
				$.getJSON(CONTEXT_PATH + "scan/whPackCarRecord/generateScanShippmentDetails?id="+id, function(data) {
					if (data.status == 200) {
		                alert("上传成功!");
						window.location.reload();
		            }else {
		                layer.alert("上传失败: "+data.message, 'error');
		            }
				});
			}
            function printLanShouTag(id) {
                //var url = CONTEXT_PATH+"scan/whPackCarRecord/printLanShouTag?id=" + id;
                var url = CONTEXT_PATH+"scan/whPackCarRecord/applyLanShouTag?id=" + id;
                $("#uploadDataBtn_" +id).attr("disabled", true);
                $.getJSON(url, function (data) {
                    if (data.status == '500') {
                        layer.alert('操作失败：' + data.message, 'error');
                    } else if (data.status == '200') {
                        layer.alert(data.message);
                    }
                });
            }
            function printShopeePDF(id) {
                var url = CONTEXT_PATH+"scan/whPackCarRecord/printShopeePDF?id=" + id;
				printPdf(url);
            }

            function printPdf(url) {
                $('#printFrame').attr('src', url);
                let printFrame = document.getElementById("printFrame");
                printFrame.onload = function() {
                    var outerText = printFrame.contentDocument.body.outerText;
                    if (typeof outerText != "undefined" && outerText != null && outerText.length >0 ){
                        var responseJson = getJson(outerText);
                        if (typeof responseJson == "object"){
                            layer.alert(responseJson.message,"error");
                            return;
                        }
                    }
                    setTimeout(function() {
                        printFrame.contentWindow.print();
                    }, 500);
                };
            }
            function getJson(jsonText) {
                try {
                    return JSON.parse(jsonText);
                } catch(e) {
                }
                return null;
            }

			function downloadPacDetail(id) {
				if (id) {
					window.open(CONTEXT_PATH + "scan/whPackCarRecord/downloadPac?id=" + id);
				}
			}

			// 上传装车交接清单
			function fileinputSubmit(id, target) {
				//检测上传文件的类型
				var filename = target.value;
				var ext, idx;
				if (filename == '') {
					$("#submit-upload").attr("disabled", true);
					layer.alert("请选择需要上传的文件!");
					return;
				} else {
					idx = filename.lastIndexOf(".");
					if (idx != -1) {
						ext = filename.substr(idx + 1).toUpperCase();
						ext = ext.toLowerCase();
						if (ext != 'jpg' && ext != 'jpeg' && ext != 'png' && ext !='gif') {
							layer.alert("只能上传jpg,jpeg,png,gif类型的文件!");
							return;
						}
					} else {
						layer.alert("只能上传jpg,jpeg,png,gif类型的文件!");
						return;
					}
				}
				var r = confirm("确定上传" + filename + "?");
				if(!r) {
					return;
				}

				var formData = new FormData();
				formData.append("file", target.files[0]);
				formData.append("id", id);
				$.ajax({
					url: CONTEXT_PATH + 'scan/whPackCarRecord/uploadPackCarImg',
					type: "POST",
					data: formData,
					contentType: false,
					processData: false,
					success: function (data) {
						if (data.status == 200) {
							layer.alert("图片上传成功!");
							window.location.reload();
						} else {
							layer.alert("图片上传失败: " + data.message);
						}
						$(target).remove;
					},
					error: function () {
						layer.alert("系统异常，操作失败!");
					}
				});
			}

			// 产品图放大
			function enlarge(obj){
				var url = $(obj).attr("src");
				var content = `<img style='width:600px;height:600px;' src='`+url+`'/>`
				layer.open({
					type: 1,
					title: false,
					closeBtn:0,
					area: ['600px','600px'],
					offset:'auto',
					fix: true,
					maxmin: false,
					shade:0.4,
					shadeClose:true,
					content: content
				});
			}

			function showEditModal(packCarId, packDate, shippingComp, bagNum){
				setEditModalData(packCarId, packDate, shippingComp, bagNum)
				$("#editDeliveryInfoModal").modal('show');
			}

			function setEditModalData(packCarId, packDate, shippingComp, bagNum){
				$("#editDeliveryInfoModal").find("#packDate").text(packDate);
				$("#editDeliveryInfoModal").find("#shippingComp").text(shippingComp);
				$("#editDeliveryInfoModal").find("#bagNum").text(bagNum);
				$("#editDeliveryInfoModal").find("input[name='packCarId']").val(packCarId);
			}

			$('#saveData').on('click', function () {
				//3.设置提交按钮失效，以实现防止按钮重复点击
				let packCarId = $("#editDeliveryInfoModal").find("input[name='packCarId']").val();
				let expressOrderNo = $("#editDeliveryInfoModal").find("input[name='expressOrderNo']").val();

				if(packCarId == undefined || packCarId == ''){
					layer.alert('装车ID不能为空', 'error');
					return false;
				}
				if(expressOrderNo == undefined || expressOrderNo == ''){
					layer.alert('车牌号不能为空', 'error');
					return false;
				}
				$('#saveData').attr("disabled", true);

				$.ajax({
					url: CONTEXT_PATH + 'scan/whPackCarRecord/updateDeliveryInfo',
					type: "POST",
					data: {
						packCarId: packCarId,
						expressOrderNo: expressOrderNo
					},
					success: function(response) {
						if (response.status == '500') {
							customizeLayer(response.message, 'error');
						} else{
							layer.alert('修改成功！');
						}
					},
					error:function () {
						layer.alert("系统异常，操作失败!",'error');
					}
				});
				setTimeout(function() {
					//提交完成后按钮重新设置有效
					$('#saveData').removeAttr('disabled');
					$("#editDeliveryInfoModal").modal('hide');
					window.location.reload();
				}, 1500);
			});
		</script>
	</body>

</html>