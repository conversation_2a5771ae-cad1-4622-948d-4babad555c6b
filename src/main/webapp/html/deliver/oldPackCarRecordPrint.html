<#include "/common/include.html">
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

    <head>

        <div id="style">
            <style type="text/css">
                body {
                    margin: 0mm;
                    padding: 0mm;
                    font-family: Arial;
                }

                .head1 {
                    height: 5.5mm;
                    width: 20mm;
                    font-size: 4mm;
                    text-align: center;
                    border: 1px solid black;
                    color: #000;
                }

                .head2 {
                    height: 5.5mm;
                    width: 35mm;
                    font-size: 4mm;
                    text-align: center;
                    border: 1px solid black;
                    color: #000;
                    /* font-weight: bold; */
                }

                .head6 {
                    height: 5.5mm;
                    width: 50mm;
                    font-size: 4mm;
                    text-align: center;
                    border: 1px solid black;
                    color: #000;
                }

                .head7 {
                    height: 5.5mm;
                    width: 45mm;
                    font-size: 4mm;
                    text-align: left;
                    border: 1px solid black;
                    color: #000;
                }

                .td2 {
                    height: 5.5mm;
                    width: 50mm;
                    font-size: 4mm;
                    text-align: center;
                    border: 1px solid black;
                    color: #000;
                }

                .td6 {
                    height: 5.5mm;
                    width: 40mm;
                    font-size: 4mm;
                    text-align: center;
                    border: 1px solid black;
                    color: #000;
                }

                .td7 {
                    height: 5.5mm;
                    width: 185mm;
                    font-size: 4mm;
                    text-align: left;
                    border: 1px solid black;
                    color: #000;
                }

                .foot {
                    height: 5.5mm;
                    font-size: 4mm;
                    text-align: left;
                    border: 1px solid black;
                    color: #000;
                }

                table,
                td,
                th {
                    border: 1px solid black;
                    border-collapse: collapse;
                    color: #000;
                }
            </style>
        </div>
        <div>
            <object  id="LODOP_OB" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" width=0 height=0>
                <embed id="LODOP_EM" type="application/x-print-lodop" width=0 height=0></embed>
            </object>
        </div>
        <script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
        <script>
            jQuery(document).ready(function() {
                setTimeout(preprint, 10000);
            })
            function preprint() {
                var LODOP = getLodop(document.getElementById('LODOP_OB'), document
                    .getElementById('LODOP_EM'));
                LODOP.PRINT_INIT("物流发货单");
                LODOP.SET_PRINT_PAGESIZE(1, "210mm", "140mm", "");
                LODOP.ADD_PRINT_TABLE("2mm", "10mm", "190mm", "120mm", document
                        .getElementById("style").innerHTML +
                    document.getElementById("div").innerHTML);
                // LODOP.PREVIEW();
                LODOP.PRINT();
            }

        </script>
    </head>

    <body>
        <div id="div">

            <table id="blockcont000">
                <caption>
                    <h3>物流发货单</h3>
                </caption>
                <#assign whPackCarRecord = domain.whPackCarRecord>
                <thead>
                <tr>
                    <td class="head1">物流公司:</td>
                    <td class="head2">${whPackCarRecord.shippingCompanyName }</td>
                    <td class="head1">客户名称:</td>
                    <#if (whPackCarRecord.shippingCompanyCode??) && (whPackCarRecord.shippingCompanyCode == 'SZSD' || whPackCarRecord.shippingCompanyCode == 'SZPOST')>
                        <td class="head2">易通达</td>
                    <#elseif (whPackCarRecord.shippingCompanyCode??) && (whPackCarRecord.shippingCompanyCode == 'MY-4PX')>
                        <td class="head2">香港铭宇</td>
                    <#else >
                        <td class="head2">深圳市易世通达贸易有限公司</td>
                    </#if>
                    <td class="head1">发货仓库:</td>
                    <td class="head2">${util('wh',whPackCarRecord.warehouseId)}</td>
                    <td class="head7">编号:${whPackCarRecord.loadNo}</td>

                </tr>
                </thead>

                <tbody>
                <tr>
                    <td class="head1">序号</td>
                    <td class="td2" colspan="2">物流渠道</td>
                    <td class="head2">袋数</td>
                    <td class="head1">件数</td>
                    <td class="head1">重量</td>
                    <td class="td6">备注</td>
                </tr>

                <#list domain.packCarDetailRecordVos?keys as key>
                    <tr>
                        <td class="head1">${key_index+1 }</td>
                        <td class="td2" colspan="2">${domain.packCarDetailRecordVos[key].logisticsCompanyName}</td>
                        <td class="head2">${domain.packCarDetailRecordVos[key].totalBagNum}</td>
                        <td class="head1">
                            ${domain.packCarDetailRecordVos[key].totalNum}
                        </td>
                        <td class="head1">${domain.packCarDetailRecordVos[key].totalWeight/1000}</td>
                        <td class="td6" colspan="2"></td>
                    </tr>
                    <#if domain.packCarDetailRecordVos[key].pickupOrderNoList??>
                        <tr>
                            <td colspan="7" style="font-size:12px;">&nbsp;&nbsp;&nbsp;&nbsp;包含的揽收面单明细：
                                <#list domain.packCarDetailRecordVos[key].pickupOrderNoList as pickupOrderNo>
                                    ${pickupOrderNo }
                                    <#if pickupOrderNo_index < domain.packCarDetailRecordVos[key].pickupOrderNoList?size-1 >,</#if>
                                </#list>
                            </td>
                        </tr>
                    </#if>
                </#list>

                <tr>
                    <td class="head1">合计</td>
                    <td class="td2" colspan="2">————</td>
                    <td class="head2" style="font-weight: bold;">${domain.allTotalBagNum }</td>
                    <td class="head1" style="font-weight: bold;">${domain.allTotalNum }</td>
                    <td class="head1" style="font-weight: bold;">${domain.allTotalWeight/1000 }</td>
                    <td class="td6" colspan="2"></td>
                </tr>
                <tr>
                    <td class="head1">说明</td>
                    <td colspan="6" class="td7">
                        1、提货物流公司收货人须与我公司发货员当面交接清楚后签字确认，后续所有查证以本《物流发货单》记录为准； 2、本表单一式三份，公司发货员 / 账务文员 / 物流公司收货人各执一份，具有同等法律效力。
                    </td>
                </tr>
                </tbody>

                <tfoot>
                <tr>
                    <td class="head1">发货人:</td>
                    <td class="head2"></td>
                    <td class="head1">收货人:</td>
                    <td class="head1"></td>
                    <td class="head1"> 联系方式:</td>
                    <td class="head1"></td>
                    <td class="head7">收货日期:
                        <br/>
                        ${whPackCarRecord.loadDate?date } ${.now?time}
                    </td>
                </tr>
                </tfoot>
            </table>
        </div>
    </body>

</html>