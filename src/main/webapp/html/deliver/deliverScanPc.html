<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
    <style type="text/css">
		.content-title{
			margin-bottom: 60px;
		}
        .content-title label{
            text-align: right;
			margin-top: 0px !important;
        }
		.readonly-row{
			padding: 20px 20px 0px 20px;
		}
		.write-row{
			background: white;
			padding: 20px 20px 0px 20px;
		}
		.btn-float a{
            margin: 10px 20px;
        }
        .table-striped td img{
            width: 80px;
            height: 80px;
        }
        .table tbody>tr>th,td{
            text-align: center;
            vertical-align: middle !important;
        }
		.page-info{
			background-color: #f2f2f2;
		}
		.page-table{
			background-color: white;
			width: 100%;
		}

    </style>
</head>
<body>
<@header method="header" active="12070000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li><a href="#">本地仓</a></li>
                <li class="active">交运扫描</li>
            </ul>
        </div>
    </div>

    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">

        <div class="row readonly-row">
            <div class="col-md-12 page-info">
				<div class="form-body">
					<div class="form-group content-title">
						<input id="shopeeAccount" class="form-control hide" type="text" value=""/>
						<input id="weightTotal" class="form-control hide" type="text" value="${domain.weightTotal!0 }"/>
						<label class="control-label col-md-1">物流：</label>
						<div class="col-md-2">
							<input id="sfmCode" class="form-control" type="text" value="${domain.sfmCode }" readonly/>
						</div>
						<label class="control-label col-md-1">单号：</label>
						<div class="col-md-2">
							<input id="orderNo" class="form-control" type="text" onkeypress="if(event.keyCode==13) { inputnext(); return false;}"/>
						</div>
						<label class="control-label col-md-1">重量(g)：</label>
						<div class="col-md-2">
							<input id="weight" class="form-control" type="text" onkeypress="if(event.keyCode==13) { inputnext(); return false;}"/>
						</div>
						<label class="control-label col-md-1"></label>
						<div class="col-md-1">
							<div style="border-radius: 8px !important;width: 100%;" type="button"
								 class="btn blue" onclick="createShipment();">结袋
							</div>
						</div>
					</div>
				</div>
			</div>

		<div class="write-row">
		</div>
    </div>
	<div class="container-fluid" style="background-color: white;border: none">
		<div class="row readonly-row">
			<div class="col-md-12 page-info">
				<div class="col-md-8 content-title">
					<table class="table table-condensed table-bordered  table-striped page-table">
						<thead>
							<tr>
								<th>追踪号</th>
								<th>发货单号</th>
								<th>交运重量(g)</th>
								<th>交运时间</th>
							</tr>
						</thead>
						<tbody id="record">
							<#if domain.scanRecordDTO??>
								<#list domain.scanRecordDTO.contents as row>
									<tr>
										<td>${row.orderNo}</td>
										<td>${row.apvNo}</td>
										<td>${row.weight}</td>
										<td>${row.deliverTime}</td>
									</tr>
								</#list>
							</#if>
						</tbody>
					</table>
				</div>
				<div class="col-md-4" style="padding-left: 80px;">
					<h1 style="color:blue;">计数: <span id="scanCount"><#if domain.scanRecordDTO??>${domain.scanRecordDTO.contents?size}<#else>0</#if></span></h1>
					<h1>当前扫描：<span id="scan-history" style="color:red;font-size: 32px;"></span></h1>
					<h1>扫描结果：
						<span id="error" style="font-weight:bold;color:red;"></span>
						<span id="success" style="font-weight:bold;color:green;"></span>
					</h1>
					<h2>
						<span id="errorMes" style="color:red;"></span>
						<span id="successMes" style="color:green;"></span>
					</h2>
				</div>
			</div>
		</div>
		<div class="write-row">
		</div>
	</div>
	<div style="position: absolute; bottom: 1%;left: 44%;">
		<#include "/common/footer.html">
	</div>
</div>

<script type="text/javascript">

	var scannedOrderNos = []; // 已扫描追踪号
	window.onload = function () {
		let trs = $("#record tr");
		trs.each(function () {
			let orderNo = $(this).find("td:first").text();
			scannedOrderNos.push(orderNo);
		});
		$("#scanCount").text(trs.length);
	};

	// 产品图放大
	function enlarge(obj){
		debugger;
		var url = $(obj).attr("src");
		var content = `<img style='width:600px;height:600px;' src='`+url+`'/>`
		layer.open({
			type: 1,
			title: false,
			closeBtn:0,
			area: ['600px','600px'],
			offset:'auto',
			fix: true,
			maxmin: false,
			shade:0.4,
			shadeClose:true,
			content: content
		});
	}

	// 结袋
	function createShipment() {
		var contents = $("#record tr");
		if (contents.length == 0) {
			layer.alert('无扫描交运记录', 'error');
			return false;
		}
		let orderArr = [];
		contents.each(function () {
			let orderNo = $(this).find("td:eq(0)").text();
			let apvNo = $(this).find("td:eq(1)").text();
			let weight = $(this).find("td:eq(2)").text();
			let time = $(this).find("td:eq(3)").text();
			orderArr.push(apvNo + "," + orderNo + "," + weight + "," + time);
		});

		let weightTotal = $("#weightTotal").val();
		let sfmCode = $("#sfmCode").val();

		var data = {
			systemTotalWeight : weightTotal,
			logisticsCompanyCode : sfmCode,
			orderNos : orderArr
		}
		$.ajax({
			url: CONTEXT_PATH + 'scan/pcDeliverOrder/generatePocketCard',
			type: "POST",
			contentType: "application/json;charset=utf-8",
			dataType : "json",
			data: JSON.stringify(data),
			success: function(response) {
				if (response.success == true) {
					console.log(response.result);
					$("#scan-history").text("");
					$("#scanCount").text(0);
					$("#weightTotal").val(0);
					$("#success").text("");
					$("#error").text("");
					$("#errorMes").text("");
					$('#sfmCode').val("");
					$('#weight').val("");
					$('#orderNo').val("");
					$("#record").html("");
					$('#orderNo').focus();
					scannedOrderNos = [];
					// 打印结袋卡
					if (response.result != undefined && response.result != '') {
						let pdfUrls = CONTEXT_PATH + response.result;
						let split = pdfUrls.split(",");
						split.forEach(function (pdfUrl) {
							window.open(pdfUrl);
						});
					}
				} else {
					if (response.errorMsg != undefined && response.errorMsg != ''){
						customizeLayer(response.errorMsg);
					}
				}
			},
			error:function () {
				layer.alert("系统异常，操作失败!",'error');
			}
		});

	}

	function inputnext() {
		var orderNo = $("#orderNo").val();
		var weight = $("#weight").val();
		var sfmCode = $("#sfmCode").val();
		if (orderNo == undefined || orderNo == '') {
			layer.alert("扫描单号不能为空", "error");
			return false;
		}
		if (weight == undefined || weight == '') {
			layer.alert("重量不能为空", "error");
			return false;
		}
		var reg2 = /^\+?[1-9][0-9]*$|^[+]{0,1}(\d+\.\d{1,3})$/;
		if(!reg2.test(weight)){
			layer.alert(weight + '格式错误！支持小数点后三位', 'error');
			return false;
		}
		if (scannedOrderNos.contains(orderNo)) {
			layer.alert("该追踪号已扫描", "error");
			$("#orderNo").val("");
			$("#weight").val("");
			return false;
		}

		if (sfmCode == undefined || sfmCode == '') {
			var contents = $("#record tr");
			if (contents.length > 0) {
				layer.alert('物流信息缺失', 'error');
				return false;
			}
			// 获取物流信息
			getLogisticsCompany(orderNo);
			sfmCode = $("#sfmCode").val();
			if (sfmCode == undefined || sfmCode == '') {
				return false;
			}
		}
		$("#scan-history").text(orderNo);

		var shopeeAccount = $("#shopeeAccount").val();
		var data = {
			orderNo : orderNo,
			logisticsCompanyCode : sfmCode,
			actualWeight : weight,
			shopeeAccount : shopeeAccount
		}
		$.ajax({
			url: CONTEXT_PATH + 'scan/pcDeliverOrder/checkAndDeliver',
			type: "POST",
			contentType: "application/json;charset=utf-8",
			dataType : "json",
			data: JSON.stringify(data),
			success: function(response) {
				if (response.success == true) {
					let result = response.result;
					let count = $("#scanCount").text();
					$("#scanCount").text(parseInt(count) + 1);
					let weightTotal = $("#weightTotal").val();
					$("#weightTotal").val(parseInt(weightTotal) + parseInt(weight));
					$("#record").prepend("<tr><td>"+ result.orderNo +"</td><td>"+ result.apvNo +"</td><td>"+ result.weight +"</td><td>"+ result.deliverTime +"</td></tr>")
				}
				handleResultMsg(response.success, response.errorMsg);
			},
			error:function () {
				layer.alert("系统异常，操作失败!",'error');
			}
		});
	}

	function isReweight(data){
		data.isReweight = true;
		$.ajax({
			url: CONTEXT_PATH + 'scan/pcDeliverOrder/checkAndDeliver',
			type: "POST",
			contentType: "application/json;charset=utf-8",
			dataType : "json",
			data: JSON.stringify(data),
			success: function(response) {
				if (response.success == true) {
					let result = response.result;
					let count = $("scanCount").text();
					$("scanCount").text(count + 1);
					$("#record").prepend("<tr><td>"+ result.orderNo +"</td><td>"+ result.apvNo +"</td><td>"+ result.weight +"</td><td>"+ result.deliverTime +"</td></tr>")
				}
				handleResultMsg(response.success, response.errorMsg);
			},
			error:function () {
				layer.alert("系统异常，操作失败!",'error');
			}
		});
	}

	function handleResultMsg(success, msg){
		if(success == true) {
			$("#success").text("成功");
			$("#error").text("");
			$("#errorMes").text("");
		} else {
			$("#success").text("");
			$("#error").text("失败");
			$("#errorMes").text(msg);
		}
		$('#orderNo').val("");
		$('#weight').val("");
		$('#orderNo').focus();
	}

	// 首次扫描查询物流信息
	function getLogisticsCompany(orderNo){
		$.ajax({
			url: CONTEXT_PATH+"scan/deliverOrder/getLogisticsCompanyCodeByOrderNo?orderNo=" + orderNo,
			type: "GET",
			async: false,
			success: function(data) {
				if (data.success == true) {
					var result = data.result;
					if (result == undefined || result == '') {
						layer.alert('返回物流信息为空', 'error');
					}
					if (result.indexOf("|") != -1) {
						var split = result.split("|");
						result = split[0];
						$("#shopeeAccount").val(split[1]);
					}
					if (result.indexOf(",") != -1) {
						result = result.split(",")[0];
					}
					$("#sfmCode").val(result);
				} else {
					customizeLayer(data.errorMsg);
				}
			}
		});
	}

</script>
</body>
</html>