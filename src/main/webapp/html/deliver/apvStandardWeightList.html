<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<#include "/common/include.html">
		<style type="text/css">
			.modal-body input {
				width: 240px;
			}
			
			#task-list td {
				vertical-align: middle;
			}
		</style>
	</head>

	<body>
		<@header method="header" active="12070000"><#include "/ftl/header.ftl"></@header>

		<div id="page" style="background-color: rgb(231, 237, 248)">
			<div class="row">
				<div class="col-md-12" style="padding: 0">
					<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
						<li>
							<a href="#">扫描交运</a>
						</li>
						<li class="active">标准重量查询</li>
					</ul>
				</div>
			</div>
			<!-- 内容 -->
			<div class="container-fluid" style="background-color: white;border: none">
				<#assign query = domain.query>
				<!-- END PAGE HEADER-->
				<div class="row">
					<div class="col-md-12">
						<form action="${CONTEXT_PATH}scan/standardWeight/search" class="form-horizontal form-bordered form-row-stripped" method="post" modelAttribute="domain" name="apvForm" id="domain">
							<!-- 分页信息 -->
							<input id="page-no" type="hidden" name="page.pageNo" value="1">
							<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
							<div class="form-body">
								<div class="form-group">
									<label class="control-label col-md-1">交运时间</label>
									<div class="col-md-3">
										<div class="input-group">
											<input class="form-control Wdate" type="text" name="query.fromDeliverDate" placeholder="" readonly="readonly" value="${query.fromDeliverDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
											<span class="input-group-addon">到</span>
											<input class="form-control Wdate" type="text" name="query.toDeliverDate" placeholder="" readonly="readonly" value="${query.toDeliverDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
										</div>
									</div>

									<label class="control-label col-md-1">发货单号</label>
									<div class="col-md-3">
										<input class="form-control" type="text" name="query.apvNo" placeholder="请输入发货单号" value="${query.apvNo}" id="apvNo-id">
									</div>

									<label class="control-label col-md-1">DD号</label>
									<div class="col-md-3">
										<input class="form-control" type="text" name="query.salesRecordNumber" value="${query.salesRecordNumber}">
									</div>
								</div>
								
								<div class="form-group">
									<label class="control-label col-md-1">SKU</label>
									<div class="col-md-3">
										<input class="form-control" type="text" name="query.likeSku" value="${query.likeSku}" id="uuidSku" onkeypress="if(event.keyCode==13) { inputSku(this); return false;}">
									</div>
									
									<label class="control-label col-md-1">超出范围:</label>
									<div class="col-md-3">
										<select class="form-control" name="query.isOverStandardWeightDiff" value="${query.isOverStandardWeightDiff}">
											<option value="">全部</option>
											<option <#if query.isOverStandardWeightDiff == true>selected</#if> value="true">是</option>
											<option <#if (query.isOverStandardWeightDiff)?? && query.isOverStandardWeightDiff == false>selected</#if> value="false">否</option>
										</select>
									</div>
									
									<label class="control-label col-md-1">标准重量差（g）</label>
									<div class="col-md-3">
										<div class="input-group">
											<input class="form-control Wdate" type="number" onkeyup="" placeholder=">=" name="query.fromWeightDiff" value="${query.fromWeightDiff}">
											<span class="input-group-addon">到</span>
											<input class="form-control Wdate" type="number" onkeyup="" placeholder="<=" name="query.toWeightDiff" value="${query.toWeightDiff}">
										</div>
									</div>
									
									<label class="control-label col-md-1">商品净重差（g）</label>
									<div class="col-md-3">
										<div class="input-group">
											<input class="form-control Wdate" type="number" onkeyup="" placeholder=">=" name="query.fromGoodsWeightDiff" value="${query.fromGoodsWeightDiff}">
											<span class="input-group-addon">到</span>
											<input class="form-control Wdate" type="number" onkeyup="" placeholder="<=" name="query.toGoodsWeightDiff" value="${query.toGoodsWeightDiff}">
										</div>
									</div>
									
									<label class="control-label col-md-1">发货单类型</label>
									<div class="col-md-3">
										<input class="form-control" name="query.apvType" type="text" value="${query.apvType}">
									</div>

									<label class="control-label col-md-1">标准重量差百分比(%)</label>
									<div class="col-md-3">
										<div class="input-group">
											<input class="form-control Wdate" type="number" min="0" max="10000" onkeyup="" placeholder=">=" name="query.fromGoodsWeightDiffPercentage" value="${query.fromGoodsWeightDiffPercentage}">
											<span class="input-group-addon">到</span>
											<input class="form-control Wdate" type="number" min="0" max="10000" onkeyup="" placeholder="<=" name="query.toGoodsWeightDiffPercentage" value="${query.toGoodsWeightDiffPercentage}">
										</div>
									</div>
								</div>

								<div class="form-group">

									<label class="control-label col-md-1">物流方式</label>
									<div class="col-md-3">
										<input class="form-control" type="text" name="query.logisticsCompany" value="${query.logisticsCompany}">
									</div>
									<label class="control-label col-md-1">账单重量差（g）</label>
									<div class="col-md-3">
										<div class="input-group">
											<input class="form-control Wdate" type="number" onkeyup="" placeholder=">=" name="query.fromBillWeightDiff" value="${query.fromBillWeightDiff}">
											<span class="input-group-addon">到</span>
											<input class="form-control Wdate" type="number" onkeyup="" placeholder="<=" name="query.toBillWeightDiff" value="${query.toBillWeightDiff}">
										</div>
									</div>
									<label class="control-label col-md-1">账单重量差百分比(%)</label>
									<div class="col-md-3">
										<div class="input-group">
											<input class="form-control Wdate" type="number" min="0" max="10000" onkeyup="" placeholder=">=" name="query.fromBillWeightDiffPercentage" value="${query.fromBillWeightDiffPercentage}">
											<span class="input-group-addon">到</span>
											<input class="form-control Wdate" type="number" min="0" max="10000" onkeyup="" placeholder="<=" name="query.toBillWeightDiffPercentage" value="${query.toBillWeightDiffPercentage}">
										</div>
									</div>
								</div>
								<div class="form-group">
									<label class="control-label col-md-1">物流单号</label>
									<div class="col-md-3">
										<input class="form-control" type="text" name="query.trackingNumber" value="${query.trackingNumber}">
									</div>
									<label class="control-label col-md-1">预估重量差（g）</label>
									<div class="col-md-3">
										<div class="input-group">
											<input class="form-control Wdate" type="number" onkeyup="" placeholder=">=" name="query.fromShipFreightDiff" value="${query.fromShipFreightDiff}">
											<span class="input-group-addon">到</span>
											<input class="form-control Wdate" type="number" onkeyup="" placeholder="<=" name="query.toShipFreightDiff" value="${query.toShipFreightDiff}">
										</div>
									</div>
									<label class="control-label col-md-1">预估重量差百分比(%)</label>
									<div class="col-md-3">
										<div class="input-group">
											<input class="form-control Wdate" type="number" min="0" max="10000" onkeyup="" placeholder=">=" name="query.fromShipFreightDiffPercentage" value="${query.fromShipFreightDiffPercentage}">
											<span class="input-group-addon">到</span>
											<input class="form-control Wdate" type="number" min="0" max="10000" onkeyup="" placeholder="<=" name="query.toShipFreightDiffPercentage" value="${query.toShipFreightDiffPercentage}">
										</div>
									</div>
								</div>
							</div>
							<div>
								<div class="pull-left" style="margin-bottom: 10px;">
								</div>
								<div class="col-md-offset-10" style="text-align: right">
									<button type="submit" class="btn blue">
										<i class="icon-search"></i> 查询
									</button>
                                    <@header method="auth" authCode="STANDARD_WEIGHT_SEARCH_DOWNLOAD">
									<button type="button" class="btn btn-default" onclick="download()">
										<i class="icon-download"></i> 导出
									</button>
                                    </@header>
								</div>
							</div>
						</form>
					</div>
					<br/>
				</div>

				<div class="row">
					<div class="col-md-12" id="task-list-warp">
						<!-- 内容 -->
						<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
							<colgroup>
								<col width="3%" />
								<col width="8%" />
								<col width="8%" />
								<col width="10%" />
								<col width="6%" />
								<col width="5%" />
								<col width="8%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
							</colgroup>
							<thead>
								<tr>
									<th>
										<label class="checkbox-inline"><input type="checkbox"  id="check-all" name="checkAll">编号</label>
									</th>
									<th>发货单号</th>
									<th>DD号</th>
									<th>SKU</th>
									<th>SKU净重/标准重(g)</th>
									<th>数量</th>
									<th>交运时间</th>
									<th>商品净重(g)</th>
									<th>标准重量(g)</th>
									<th>称重重量(g)</th>
									<th>标准重量差(g)</th>
									<th>商品净重差(g)</th>
									<th>标准重量差百分比</th>

									<th>预估重量</th>
									<th>预估重量差</th>
									<th>账单重量</th>
									<th>账单重量差</th>
									<th>账单重量差百分比</th>
									<th>物流方式</th>
									<th>是否超出重量差</th>


								</tr>
							</thead>
							<tbody>
								<#list domain.whApvs as apv>
									<tr id="tr-apv-${apv.id}" class="tr-apv-${apv.id}">
										<td>
											<label class="checkbox-inline">
											<input type="checkbox" name="apvIds" value="${apv.id}" /> ${apv.id}
											</label>
										</td>
										<td>${apv.apvNo }<br/></td>
										<td>${apv.salesRecordNumber}</td>
										<td>
											<#list apv.whApvItems as apvItem>
												${apvItem.sku }（${util('wh',apvItem.whSku.warehouseId)}）<br />
											</#list>
										</td>
										<td>
											<#list apv.whApvItems as apvItem>
												<#if (apvItem.whSku.weight??) && (apvItem.whSku.weight > 0)>
													${apvItem.whSku.weight }
												</#if>
												— 
												<#if (apvItem.whSku.netWeight??) && (apvItem.whSku.netWeight > 0) >
													${apvItem.whSku.netWeight }
												</#if>
												<br />
											</#list>
										</td>
										<td>
											<#list apv.whApvItems as apvItem>
												${apvItem.saleQuantity }<br />
											</#list>
										</td>
										<td>${apv.apvTrack.deliverTime }</td>
										<td>${apv.skuWeightCount }</td>
										<td>${apv.skuStandardWeightCount }
											<#if ( apv.skuStandardWeightCount??) && (apv.standardWeightFull == false) >
												<span style="color: red;">(未完)</span>
											</#if>
										</td>
										<td>${apv.actualWeight }</td>
										<td>${apv.skuStandardWeightDiff }</td>
										<td>${apv.skuGoodsWeightDiff }</td>
										<td>${apv.weightDiffPercentage*100 }%</td>


										<td>${apv.shipFreight }</td>
										<td>${apv.shipFreightDiff }</td>
										<td>${apv.billWeightCount }</td>
										<td>${apv.billWeightDiff }</td>
										<td>${apv.billWeightDiffPercentage*100}%</td>
										<td>${apv.logisticsCompany }</td>
										<td>
											<#if apv.apvTrack.isOverStandardWeightDiff??>
											<#if apv.apvTrack.isOverStandardWeightDiff == true >
											是
											<br/>
											<button type="button" class="btn btn-xs btn-info" onclick="viewLog(${apv.id}, 'whapv')">日志</button>
											</#if>
											<#if apv.apvTrack.isOverStandardWeightDiff == false >
											否
											</#if>
											</#if>
										</td>
									</tr>
								</#list>
							</tbody>
						</table>
						<!-- 内容end -->
					</div>
				</div>
				<div id="fixed-bottom">
					<div id="pager"></div>
				</div>
			</div>

			<#include "/common/footer.html">
		</div>
		<script type="text/javascript">var initSelect2 = true;</script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
		<script type="text/javascript">
			jQuery(document).ready(function() {

				var domainForm = $('#domain');
				domainForm.validate({});
			});

			// 分页
			var total = "${domain.page.totalCount}";
			var pageNo = "${domain.page.pageNo}";
			var pageSize = "${domain.page.pageSize}";
			$("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });
			
			// 全选
			var checkAll = $("input[name='checkAll']");

			// 子选项
			var itemIds = $("input[name='apvIds']");
			checkAll.change(
			  function () {
				  itemIds.prop("checked", $(this).prop("checked"));
				  itemIds.each(function(){
					  var f = $(this).is(":checked");
						var checkClass = $(this).prop("class");
						$("." + checkClass).each(function(){
							$(this).prop("checked",f);
						})
				  })
			  }
			);
			$("input[name='query.apvType']").select2({
				data : [{id : "SS", text : "单品单件"}, {id : "SM", text : "单品多件"}, {id : "MM", text : "多品多件"}],
				placeholder : "发货单类型",
				multiple: true,
				allowClear : true
			});

			// 获取选中的单
			function getCheckedIds() {
				var checkedIds = $("input[name='apvIds']:checked");
				return checkedIds;
			}
			
			// 下载
			function download(){
				var checkedDatas = getCheckedIds();
				if(checkedDatas.length == 0) {
					var param = $("#domain").serialize();
					if(total > 100000){
					    layer.alert("导出数据不能超过100000条！","error");
						return;
					}
					window.open(CONTEXT_PATH + "scan/standardWeight/download?" + param); 
				}else{
					downloadByPost();
				}
			}


			// 超过500条不能用GET请求
			function downloadByPost(){
				var checkedDatas = getCheckedIds();
				var apvIds = "";
				for (var i = 0; i < checkedDatas.length; i++) {
					var apv = checkedDatas[i];
					var apvId = $(apv).val();
					apvIds += apvId;
					if(i != checkedDatas.length-1){
						apvIds += ",";
					}
				}
				var url= CONTEXT_PATH + "scan/standardWeight/download";
				var tempForm = document.createElement("form");           
				tempForm.id="tempForm";           
				tempForm.method="post";              
				tempForm.action=url;       
				tempForm.target="blank";           
				var hideInput = document.createElement("input");           
				hideInput.type="hidden";           
				hideInput.name="apvIds";  
				hideInput.value= apvIds;         
				tempForm.appendChild(hideInput);
				if (tempForm.attachEvent) {  // IE 
					tempForm.attachEvent("onsubmit",function(){ window.open('about:blank','blank'); });  
				} else if (tempForm.addEventListener) {  // DOM Level 2 standard  
					tempForm.addEventListener("onsubmit",function(){ window.open('about:blank','blank'); });  
				}              
				document.body.appendChild(tempForm);   
				if (document.createEvent) { // DOM Level 2 standard  
					evt = document.createEvent("MouseEvents");  
					evt.initMouseEvent("submit", true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);  
					tempForm.dispatchEvent(evt);  
				} else if (tempForm.fireEvent) { // IE  
					tempForm.fireEvent('onsubmit');  
				} 
				//必须手动的触发        
				tempForm.submit();         
				document.body.removeChild(tempForm);
			}

			// 下拉框
			initShippingMethod('query.logisticsCompany');

			function initShippingMethod(name){
				$.getJSON(CONTEXT_PATH + "apvs/getShippingMethod", function(json){
					if (json) {
						$("input[name='" + name + "']").select2({
							data : json,
							placeholder : "运输方式",
							multiple: true,
							allowClear : true
						});
					} else {
						$("input[name='" + name + "']").attr("placeholder", "没有权限数据").attr("readonly", true);
					}
				});
			}

		</script>
	</body>

</html>