<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
    <style type="text/css">
        .modal-body input {
            width: 240px;
        }
        #task-list td {
            vertical-align:middle;
        }
    </style>
</head>
<body>
<@header method="header" active="-1"><#include "/ftl/header.ftl"></@header>
<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="container bottomblank" style="width: 960px;margin-top: 50px;">
                    <div class="panel panel-default form-horizontal form-bordered min">
                        <div class="panel-heading">
                            <h3 class="panel-title text-lan">
                                <i class="ico-tree5 mr5"></i><span style="color: red;font-weight: 600;font-size: 15px;">500: 系统发生错误, 请稍候重试!</span>
								<#if systemError?if_exists && systemError>
									</b>点击<a href="${CONTEXT_PATH}" style="color: blue;">返回</a>
								</#if>
                            </h3>
                            <div class="panel-toolbar text-right">
                                <div class="option"><button data-toggle="panelcollapse" class="btn up" type="button"><i class="arrow"></i></button></div>
                            </div>
                        </div>
                        <div class="panel-body panel-collapse pull out">
                            <div class="form-group bdr0">
                                <label class="col-sm-1 control-label">错误信息</label>
                                <div class="col-sm-11">
					                <#if exception?if_exists>
                                        <div class='alarm'>
                                            <b>${exception}</b>
                                            <p>
												<#list exception.stackTrace as element>
													${element}<br/>
												</#list>
                                            </p>
                                        </div>
									</#if>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <#include "/common/footer.html">
</div>
</body>
</html>