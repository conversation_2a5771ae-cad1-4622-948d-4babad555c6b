<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		.top_bar{
			position:fixed;top:0px;
		}
		#task-list td {
			vertical-align:middle;
		}
		/*  Pink */
		.btn.pink {
			color: white;
			text-shadow: none;
			background-color: #fd50c6;
		}
		.btn.pink:hover,
		.btn.pink:focus,
		.btn.pink:active,
		.btn.pink.active,
		.btn.pink.disabled,
		.btn.pink[disabled] {
			background-color: #d03ea1 !important;
			color: #fff !important;
			outline: none !important;
		}
	</style>
</head>
<body>
<@header method="header" active="12030000" ><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">拣货</a></li>
				<li class="active">异常补货任务</li>
			</ul>
		</div>
	</div>

	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
		<div class="row">
			<div class="col-md-12">
				<#assign query = domain.query>
				<form action="${CONTEXT_PATH}smPickingTask/search"
					  class="form-horizontal form-bordered form-row-stripped"
					  method="post" modelAttribute="domain" name="smPickingTaskForm" id ="domain">
					<!-- 分页信息 -->
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-1">任务号</label>
							<div class="col-md-3">
								<input class="form-control" type="text" name="query.taskNo" placeholder="请输入任务号" value="${query.taskNo }">
							</div>
							<label class="control-label col-md-1">周转筐</label>
							<div class="col-md-3">
								<input class="form-control" name="query.boxNo"  placeholder="请输入周转筐" type="text" value="${query.boxNo}">
							</div>
							<label class="control-label col-md-1">任务状态</label>
							<div class="col-md-3">
								<input class="form-control" name="query.taskStatus" type="text" value="${query.taskStatus}">
							</div>
						</div>

						<div class="form-group">
							<label class="control-label col-md-1">任务ID</label>
							<div class="col-md-3">
								<input class="form-control" type="text" name="query.id" placeholder="请输入任务ID" value="${query.id }" id= "task-id">
							</div>

							<label class="control-label col-md-1">创建时间</label>
							<div class="col-md-3">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.fromCreatedDate" placeholder="" readonly="readonly" value="${query.fromCreatedDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.toCreatedDate" placeholder="" readonly="readonly" value="${query.toCreatedDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>

							<label class="control-label col-md-1">完成时间</label>
							<div class="col-md-3">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.fromReceiveDate" placeholder="" readonly="readonly" value="${query.fromReceiveDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.toReceiveDate" placeholder="" readonly="readonly" value="${query.toReceiveDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>
						</div>
						<div class="form-group">

							<label class="control-label col-md-1">SKU</label>
							<div class="col-md-3">
								<input class="form-control" type="text" name="query.sku" placeholder="请输入SKU" value="${query.sku }" id= "uuidSku" onkeypress="if(event.keyCode==13) { inputSku(this); return false;}">
							</div>

							<label class="control-label col-md-1">发货单号</label>
							<div class="col-md-3">
								<input class="form-control" type="text" name="query.apvNo" placeholder="请输入发货单号" value="${query.apvNo }" id= "task-id">
							</div>

							<label class="control-label col-md-1">标签</label>
							<div class="col-md-3">
								<input class="form-control" name="query.taskTypes" type="text" value="${query.taskTypes}">
							</div>
						</div>
						<div class="form-group">

							<label class="control-label col-md-1">完成人</label>
							<div class="col-md-3">
								<input class="form-control" name="query.receivePerson" type="text" value="${query.receivePerson}">
							</div>
						</div>
					</div>
					<div>
						<div class="pull-left" style="margin-bottom: 10px;">
							<@header method="auth" authCode="SM_BATCH_LIST_DISCARD_ORDER">
							<button type="button" class="btn  btn-default" onclick="discardedTask()">
								<i class="icon-pickingTask"></i> 废除任务
							</button>
						</@header>
						<@header method="auth" authCode="SM_BATCH_PRINT_QR_CODE">
						<button type="button" class="btn  btn-default" onclick="batchPrintQRCode()">
							<i class="icon-pickingTask"></i> 打印任务号
						</button>
					</@header>
			</div>

			<div class="col-md-offset-12" style="text-align: right">
				<@header method="auth" authCode="SM_BATCH_LIST_DOWNLOAD">
				<button type="button" class="btn btn-default" onclick="downloadPickingTask('task')">
					<i class="icon-download"></i> 导出
				</button>
				</@header>
				<@header method="auth" authCode="SM_BATCH_LIST_DOWNLOAD_LIST">
				<button type="button" class="btn btn-default" onclick="downloadPickingTask('item')">
					<i class="icon-download"></i> 导出明细
				</button>
				</@header>
				<button type="button" class="btn btn-default" onclick="formReset(this)">
					<i class="icon-refresh"></i> 重置
				</button>
				<button type="submit" class="btn blue">
					<i class="icon-search"></i> 查询
				</button>
			</div>
</div>
</form>
</div>
<br/>
</div>

<div class="row">
	<div id="fixedDiv" class="col-md-12">
		<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
			<colgroup>
				<col width="6%">
				<col width="8%">
				<col width="8%">
				<col width="5%">
				<col width="5%">
				<col width="5%">
				<col width="5%">
				<col width="5%">
				<col width="5%">
				<col width="5%">
				<col width="3%">
				<col width="5%">
				<col width="9%">
				<col width="9%">
				<col width="5%">
			</colgroup>
			<thead>
			<tr>
				<th>任务ID</th>
				<th>任务号</th>
				<th>周转筐</th>
				<th>订单数</th>
				<th>SKU数</th>
				<th>库位数</th>
				<th>包装扫描件数</th>
				<th>缺货数件</th>
				<th>已拣数量</th>
				<th>任务状态</th>
				<th>是否打印</th>
				<th title="显示周转筐中发货类型多的一类">标签</th>
				<th>完成人/完成时间</th>
				<th>创建人/创建时间</th>
				<th>日志</th>
			</tr>
			</thead>
		</table>
	</div>
	<div class="col-md-12" id="task-list-warp">
		<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
			<colgroup>
				<col width="6%">
				<col width="8%">
				<col width="8%">
				<col width="5%">
				<col width="5%">
				<col width="5%">
				<col width="5%">
				<col width="5%">
				<col width="5%">
				<col width="5%">
				<col width="3%">
				<col width="5%">
				<col width="9%">
				<col width="9%">
				<col width="5%">
			</colgroup>
			<thead>
			<tr>
				<th><label class="checkbox-inline"><input type="checkbox" name="checkAll"> 任务ID</label></th>
				<th>任务号</th>
				<th>周转筐</th>
				<th>订单数</th>
				<th>SKU数</th>
				<th>库位数</th>
				<th>包装扫描件数</th>
				<th>缺货数件</th>
				<th>已拣数量</th>
				<th>任务状态</th>
				<th>是否打印</th>
				<th title="显示周转筐中发货类型多的一类">标签</th>
				<th>完成人/完成时间</th>
				<th>创建人/创建时间</th>
				<th>日志</th>
			</tr>
			</thead>
			<tbody>
			<#list domain.smPackingPickingTasks as pickingTask >
			<tr>
				<td><label class="checkbox-inline"><input type="checkbox" name="taskIds" value="${pickingTask.id}" /> ${pickingTask.id }</label></td>
				<td>
					</label>
					<a href = "${CONTEXT_PATH}smPickingTaskItem/getSmPickingTask?query.taskId=${pickingTask.id}">
						<span class="help-block">${pickingTask.taskNo }</a></span>
				</td>
				<td>${pickingTask.boxNo } </td>
				<td>${pickingTask.orderQuantity } </td>
				<td>${pickingTask.skuQuantity } </td>
				<td>${pickingTask.locationQuantity } </td>
				<td>${pickingTask.scanQuantity }</td>
				<td>${pickingTask.lessQuantity }</td>
				<td>${pickingTask.pickQuantity } </td>

				<td id="${pickingTask.id}_taskStatus">
					${util('enumName',"com.estone.apv.enums.SmPickingTaskStatus", pickingTask.taskStatus)}
				</td>

				<td>${util('enumName',"com.estone.picking.enums.PickingTaskIsPrinting", pickingTask.isPrinting)}</td>

				<td>${util('enumName', 'com.estone.apv.common.ApvOrderType', pickingTask.taskType)} </td>

				<td>${util('name',pickingTask.receivePerson)} <br/>
					${pickingTask.pickingEndDate } </td>
				<td>${util('name',pickingTask.createdBy)} <br/>
					${pickingTask.createdDate } </td>

				<td>
					<button type="button" class="btn btn-xs btn-info" onclick="viewLog(${pickingTask.id}, 'smPackingPickingTask')">日志</button>
				</td>
			</tr>
			</#list>
			</tbody>
		</table>
	</div>
</div>
<div id="fixed-bottom">
	<div id="pager"></div>
</div>
</div>

	<!-- 打印弹窗 -->
   	<div class="modal fade ui-popup" id="print_modal" tabindex="-1" role="dialog" aria-labelledby="organization-add-label" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content"></div>
		</div>
	</div>

<#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript">
    // 分页
    var total = "${domain.page.totalCount}";
	var pageNo = "${domain.page.pageNo}";
	var pageSize = "${domain.page.pageSize}";
	$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

    var heights = $("body").height();
    if(heights>910){
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }

	//打印任务号
	function batchPrintQRCode() {
		var checkedDatas = getCheckedTaskIds();
		debugger;
		if(checkedDatas.length == 0) {
			layer.alert("请选择要操作的任务",'error');
			return;
		}else if (checkedDatas.length > 100) {
			layer.alert("批量打印不能超过100个任务!",'error');
			return;
		}
		var trueArray = new Array();
		var falseArray = new Array();
		for (var i = 0; i < checkedDatas.length; i++) {
			var checked = checkedDatas[i];
			var status = $("#"+checked.value+"_taskStatus").text();
			debugger;
			if (status.indexOf('未领取') != -1) {
				trueArray.push(checked.value);
			}else {
				falseArray.push(checked.value);
			}
		}

		if (falseArray.length>0) {
			layer.alert("选择了的:"+falseArray.length+"条不符合的任务将不会打印", 'error');
		}


		var taskIds = "";
		for (var i = 0; i < trueArray.length; i++) {
			taskIds += "taskIds=" + trueArray[i] + "&";
		}

		if (trueArray.length>0) {
			$("#print_modal").removeData("bs.modal");
			$("#print_modal").modal({
				remote: CONTEXT_PATH + "smPickingTask/qRCodePrint?" + taskIds
			});
		}
	}


    $.getJSON(CONTEXT_PATH + "system/saleusers/userByRole", function(json){
        if (json) {
            $("input[name='query.receivePerson']").select2({
                data : json,
                placeholder : "完成人",
                allowClear : true
            });
        } else {
            $("input[name='query.receivePerson']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
        }
    });

    var taskTypeJson = ${domain.taskTypeJson};
    $("input[name='query.taskTypes']").select2({
        data : taskTypeJson,
        placeholder : "标签",
		multiple:true,
        allowClear : true
    });

    var taskStatusArray = ${domain.taskStatuss};
    $("input[name='query.taskStatus']").select2({
        data : taskStatusArray,
        placeholder : "任务状态",
        allowClear : true
    });



    $("#task-list").find("input[name='checkAll']").change(
        function () {
            var checkBox = $("#task-list").find(":checkbox");
            checkBox.prop("checked", $(this).prop("checked"));
            checkBox.each(function(){
                var f = $(this).is(":checked");
                var checkClass = $(this).prop("class");
                $("." + checkClass).each(function(){
                    $(this).prop("checked",f);
                })
            })
        }
    );
	
	// 获取选中的任务
	function getCheckedTaskIds() {
		var checkedTaskIds = $("input[name='taskIds']:checked");
		return checkedTaskIds;
	}

	//导出
	// 下载
	function downloadPickingTask(downloadFor){
		var checkedDatas = getCheckedTaskIds();
		
		var diglog = dialog({
			title: '导出',
			width: 350,
			height:100,
			url: CONTEXT_PATH + "smPickingTask/downloadmode",
			okValue: '确定',
		    ok: function () {
		    	debugger;
		    	var exportWindow = $(this.iframeNode.contentWindow.document.body);
		    	
		    	var submitForm = exportWindow.find("#submit-form");
		    	
		    	var exportType = submitForm.find("input[name='exportType']:checked").val();
		    	
		    	var submitFormParam = submitForm.serialize();
		    	
		    	// 导出当前选择
		    	if(exportType == 3) {
					if(checkedDatas.length == 0) {
						layer.alert("请选择要操作的数据!");
						return false;
					} else if (checkedDatas.length > 300) {
						layer.alert("选择数量不能超过300!");
						return false;
					}
					
					submitFormParam = submitFormParam + "&" +checkedDatas.serialize();
			    	
		    	}
		    	
		    	//还原分页
		    	$("#page-no").val("${domain.page.pageNo}");
		    	
		    	
		    	var action = document.smPickingTaskForm.action;
		    	var target = document.smPickingTaskForm.target;
		    	var method = document.smPickingTaskForm.method;
		    	document.smPickingTaskForm.action= CONTEXT_PATH + "smPickingTask/download?" + submitFormParam + "&downloadFor=" +downloadFor;
		    	document.smPickingTaskForm.target="_blank";
		    	document.smPickingTaskForm.method="POST";
		    	document.smPickingTaskForm.submit();
		    	document.smPickingTaskForm.target=target;
		    	document.smPickingTaskForm.action=action;
		    	document.smPickingTaskForm.method=method;
		    	
		    	$("#page-no").val("1");
		    	
		    	setTimeout(function () {
		    		diglog.close().remove();
		    	}, 100);
		    	
		    	return true;
		    },
		    cancelValue: '取消',
		    cancel: function () {}
		});
		diglog.show();
	}
	
	//废除任务
	function discardedTask(){
		var checkedDatas = getCheckedTaskIds();
		
		if(checkedDatas.length == 0) {
			layer.alert("请选择要操作的任务",'error');
			return;
		}
		
		var taskIds = checkedDatas.serialize();
		
		var r = confirm("确定是否废除任务？");
    	if (r) {
    		$.get(CONTEXT_PATH + "smPickingTask/smDiscardedTask?"+taskIds, function(data){
    			if (data.status == 200) {
					layer.confirm(data.message,{
						icon: 1,
						btn: ['确定']
					},function () {
						window.location.reload();
					})
    			} else {
    				customizeLayer(data.message);
    			}
    		});
    	}	
	}

</script>
</body>
</html>