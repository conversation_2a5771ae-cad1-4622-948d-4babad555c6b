<div class="shipping-info">
    <h3>拆分后发货单信息</h3>

    <div class="notice">
        <p class="red">注意：当可包装的拆分订单处理完成后，剩余SKU请交给库内返架</p>
    </div>

    <#if (domain)!>
        <#assign apvList = domain.apvList>
        <#if (apvList)!>
            <#list apvList as apv>
                <div class="shipping-container">
                    <div class="shipping-header">
                        <div class="shipping-number">发货单号</div>
                        <div class="shipping-status">${apv.apvNo}</div>
                    </div>
                    <div class="shipping-size">
                        <div class="size">${apv.waybillSize}</div>
                        <div class="status">${apv.statusName}</div>
                    </div>
                    <table class="order-table">
                        <thead>
                        <tr>
                            <th>SKU</th>
                            <th>订单数量</th>
                        </tr>
                        </thead>
                        <tbody>
                        <#list apv.whApvItems as item>
                            <tr>
                                <td>${item.sku}</td>
                                <td>${item.saleQuantity}</td>
                            </tr>
                        </#list>
                        </tbody>
                    </table>
                </div>
            </#list>
        </#if>
    </#if>
</div>

<style>
    .shipping-info {
        font-family: Arial, sans-serif;
        width: 100%;
        max-width: 500px;
        margin: 0 auto;
        background-color: #f5f5f5;
        padding: 15px;
    }

    h3 {
        margin-top: 0;
        font-size: 16px;
        font-weight: normal;
    }

    .notice {
        margin-bottom: 15px;
    }

    .warning {
        color: red;
        font-size: 14px;
        margin: 0;
    }

    .shipping-container {
        margin-bottom: 20px;
        background-color: white;
        border: 1px solid #ddd;
    }

    .shipping-header, .shipping-size {
        display: flex;
        border-bottom: 1px solid #ddd;
    }

    .shipping-number, .size {
        background-color: #eee;
        padding: 8px;
        width: 30%;
        text-align: center;
        font-weight: bold;
    }

    .shipping-status, .status {
        padding: 8px;
        width: 70%;
        text-align: center;
    }

    .order-table {
        width: 100%;
        border-collapse: collapse;
    }

    .order-table th {
        background-color: #eee;
        padding: 8px;
        text-align: center;
        border-bottom: 1px solid #ddd;
    }

    .order-table td {
        padding: 8px;
        text-align: center;
        border-bottom: 1px solid #ddd;
    }
</style>