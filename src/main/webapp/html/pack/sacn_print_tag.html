<style type="text/css">
    .iframe-container iframe {
        float: left;
        width: 20%; /* 平分宽度，可以根据需求调整 */
        height: 300px; /* 设置高度适应你的PDF文件 */
    }

</style>
<#assign apv = domain.whAsnExtra >
<#assign fba = domain.whFbaAllocation >
<#if (domain.printUrl)!>
    <div style="border-bottom: 1px #CCC dotted; height: 300px;">
        <input type="hidden" name="printCopies" value="${domain.printCopies}"/>
        <input type="hidden" name="printUrl" value="${CONTEXT_PATH}${domain.printUrl}"/>
        <div>其它标签</div>
        <div class="iframe-container">
            <iframe src="${CONTEXT_PATH}${domain.printUrl}"></iframe>
        </div>
    </div>
<#elseif (apv)!>
    <!-- 不为空操作 -->
    <div style="border-bottom: 1px #CCC dotted; height: 300px;">
        <input type="hidden" name="purposeHouse" value="${fba.purposeHouse}"/>
        <#list apv.base64List as base>
            <input type="hidden" name="base64" value="${base}"/>
        </#list>
        <#if apv.base64SkuList??>
            <#list apv.base64SkuList as base>
                <input type="hidden" name="base64Sku" value="${base}"/>
            </#list>
        </#if>
        <!--此处用于需要打印多个sku合并后的标签的情况 -->
        <#if apv.mergeSku??>
            <#list apv.mergeSku as sku>
                <#list apv.mergeSkuMap[sku] as mergeSkuPdfList>
                    <#list mergeSkuPdfList as mergeSkuPdf>
                        <input type="hidden" name="${sku}_base64Sku" value="${mergeSkuPdf}"/>
                    </#list>
                </#list>
                <input type="hidden" name="${sku}_printCopies" value="${apv.mergeSkuCopies[sku]}"/>
            </#list>
            <#--用于拼凑出用逗号隔开的sku列表。别碰它！！！此处不可有缩进等格式，否则会出现大量的空格-->
            <#assign skuList><#list apv.mergeSku as sku>${sku}<#if sku_index + 1 != apv.mergeSku?size>,</#if></#list></#assign>
            <input type="hidden" id="mergeSkuList" value="${skuList}"/>
        </#if>
        <input type="hidden" name="printCopies" value="${domain.printCopies}"/>
        <div>其它标签</div>
        <div class="iframe-container">
            <#list apv.pdfUrlList as url>
                <iframe src="${url}"></iframe>
            </#list>
        </div>
    </div>
<#else >
    <!-- 不为空操作 -->
    <div class="alert alert-danger">
        <strong id="scan-error">
            错误信息: ${domain.errorMsg}
        </strong>
    </div>
</#if>