<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
	</style>
</head>
<body>
	<@header method="header" active="12050000" ><#include "/ftl/header.ftl"></@header>
	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">发货扫描</a>
					<li class="active">热销多件合单扫描</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
			<div class="row">
				<div>
					<div class=" panel layout-panel layout-panel-west" >
						<div class="panel-tool"></div>
					</div>
					<div >
						<h3 style="display: inline-block">扫描区域</h3>
						<input  type="hidden" value="" id="apv-no-now"/>
						<input  type="hidden" value="" id="ship-service-now"/>
						<label>拣货任务号</label>
						<input type="text" class="input-mini" name="orderid" id="orderid" onkeypress="if(event.keyCode==13) { inputorderid(this); return false;}"tabindex="4">
						<label style="margin-left: 10px">SKU</label>
						<input type="text" class="input-mini" name="apvid" id="apvid" onpaste="return false" onkeypress="if(event.keyCode==13) { inputnext(this); return false;}"tabindex="4">
						<label style="margin-left: 10px">SKU</label> 
						<input type="text" class="input-mini" name="sku" id="sku" onpaste="return false" onkeypress="if(event.keyCode==13) { inputsku(this, 0); return false;}">
						<div style="display: inline-block;margin-left: 10px" class="panel-title2" id="panel-title-task"><h1  style="color:blue;font-size:24px;">当前成功:<b>0</b></h1></div>
						<div style="display: inline-block;margin-left: 10px" class="panel-title2" id="panel-floor-task"></div>
						<div style="display: inline-block;margin: 0 10px" class="panel-title2" id="panel-title-piece-task"><h1  style="color:red;font-size:24px;">当前计数:<b>0</b></h1></div>
						<div style="display: inline-block;margin-left: 10px" class="panel-title2" id="panel-title"><h1  style="color:blue;font-size:36px;">总成功:<b>0</b></h1></div>
						<div style="display: inline-block;margin-left: 10px" class="panel-title2" id="panel-floor"></div>
						<div style="display: inline-block;margin: 0 10px" class="panel-title2" id="panel-title-piece"><h1  style="color:red;font-size:36px;">总计数:<b>0</b></h1></div>
						<!--<button type="button" class="btn red" onclick="rePrint()">
       						<i class="icon-print"></i> 重新打印
       					</button>-->
       					<button type="button" class="btn red" onclick="failPrint()">
       						<i class="icon-print"></i> 打印失败面单
       					</button>
						<span id="check-quantity" style="font-size: 36px;font-weight: 900;color: red;margin-left: 20px"></span>
					</div>
					<div class="panel-header" style="overflow: hidden;">
					</div>
					<div>
						<div id="check_scan_datas" class="border-gray p5"></div>
						<div id="scan_datas" class="border-gray mt5" style="height: 200px;"></div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-6">
					<iframe src="javascript:void(0);" frameborder="0" marginheight="0" marginwidth="0" frameborder="0" scrolling="auto" id="shippingOrderFrame" name="shippingOrderFrame" width="100%" height="500px"></iframe>
				</div>
			</div>
			<iframe style="width:1px;height:1px;border: 0 none;" name="printHtml" id="printHtml"></iframe>
		</div>
	</div>
	<#include "/common/footer.html">
	
	<!-- 打印插件 -->
   	<script type="text/javascript" src="${CONTEXT_PATH }js/LodopFuncs.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/jquery.sound.js?v=${.now?datetime}"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/packing.js?v=${.now?datetime}"></script>
	<script type="text/javascript">
		// 唯一码缓存
		var uuIdCacheKey = 'CHECK_PACKING_FOR_UUID_CACHE_KEY_SS_FIRE'+ '_' +  new Date().getTime();
		// 打印地址
		var printUrl = "${CONTEXT_PATH}apv/packs/oms/print?apvNo=";
		// 页面缓存
		var cacheKey = "basket_check_success";
		var pieceCacheKey = "basket_check_piece_success";
		// 上一次扫描的发货单
		var lastApvId;
		// 页面初始化
		$(document).ready(function() {
			// 页面进入时清除唯一码缓存
			initSkuUuIdStorageCache(uuIdCacheKey);
			pageInit();
			$('#orderid').val('');
			$('#orderid').select().focus();
	  		var storage = new WebStorageCache();
	  		if (storage.get(cacheKey)) {
	  			lastSuc = storage.get(cacheKey);
	  			$('#panel-title').html('<h1  style="color:blue;font-size:36px;">总成功：<b>'+lastSuc+'</b></h1>');
	  		}
	  		if (storage.get(pieceCacheKey)) {
	  			lastSuc = storage.get(pieceCacheKey);
	  			$('#panel-title-piece').html('<h1  style="color:red;font-size:36px;">总计数:<b>'+lastSuc+'</b></h1>');
	  		}
	  		$('#orderid').focus();
	    });
	
		// 初始化
		function pageInit() {
			$('#apvid').val('');
			$('#apvid').select().focus();
			$('#sku').val('');
			$('#check_scan_datas').html('');
		}
	    
	    //拣货任务号 触发
	    function inputorderid(obj){
	    	if(!obj.value || obj.value.replace(/\s/g,'') == ''){
				layer.alert("请输入拣货任务号!");
				return ;
			}
			var orderId = obj.value.replace(/\s/g,'');
			orderId = $.trim(orderId);
			var r= $.ajax({
	        	url : CONTEXT_PATH + "apv/packs/sm/fire/check/taskNo",
				data : {taskNo : orderId},
	            timeout : 100000,
	            success : function(response){
	            	if(response.status == '200') {
	            		taskCalsf();
	            		$('#apvid').select().focus();
	            	} else {
						layer.alert(response.message,{closeBtn: 0},function (index) {
							layer.close(index);
							$('#orderid').val('');
							// 找不到订单
							$('#orderid').select().focus();
						});
	            	}
	             },
	             error:function(){
	                 layer.alert('扫描失败，请重新扫描');
	             }
	        }); 
	    }
	    
	    // 扫描第一个sku
		function inputnext(obj) {
			var orderid = $("#orderid").val();
			if(!obj.value || obj.value.replace(/\s/g,'') == ''){
				layer.alert("请扫描sku!");
				return ;
			}
			var apvId = obj.value.replace(/\s/g,'');
			apvId = $.trim(apvId);

			//兼容SKU编码
            //apvId = getSkuByBarCode(obj);

			var uuid = apvId.trim();
            if(!(apvId.indexOf("=") == -1)) {
                var realSku = apvId.split('=')[0];
                $('#apvid').val(realSku);
                apvId = realSku;
            }
			var r= $.ajax({
	        	url : CONTEXT_PATH + "apv/packs/sm/fire/check/sku",
				data : {sku : apvId, lastApvId : lastApvId, orderid: orderid, uuid: uuid},
	            timeout : 90 * 1000,
	            success : function(response) {
	            	$("#check_scan_datas").html(response);
				    if (response.length > 230) {
				    	var errorMsg = $("#check_scan_datas").find("#scan-error").html();
	                    if(errorMsg){
	                        layer.alert(errorMsg, {closeBtn: 0},function (index) {
	                        	layer.close(index);
								$('#apvid').val("");
								$('#apvid').focus();
								addWhUniqueSkuLog(uuid, '');
							});
	                        return;
	                    }
	                 	// 第一次扫描初始化唯一码缓存
	                    initSkuUuIdStorageCache(uuIdCacheKey);
	                    checkSkuUuIdStorageCache(uuIdCacheKey, uuid);
				    	// 扫描成功
						$('#sku').select().focus();
						// 自动扫第一个
				    	$("#sku").val(apvId);
						inputsku(document.getElementById("sku"), 1);
                        isFocus = false;
						// 记录最后一次扫描的订单
						lastApvId = $('#check_scan_datas').find("input[name='apvId']").val();
						var apvNo = $('#check_scan_datas').find("input[name='apvNo']").val();
						//保存apvNo
	            		$("#apv-no-now").val(apvNo);
						var orderOrigin = $('#check_scan_datas').find("input[name='shipService']").val();
	            		$("#ship-service-now").val(orderOrigin);
	            		// 添加唯一码日志
	            		addWhUniqueSkuLog(uuid, apvNo);
						// 要核对的数量
						var quantity = $('#check_scan_datas').find("input[name='totalSaleQuantity']").val();
						$("#check-quantity").text(quantity);
						audioPlay('success');
                        $('#sku').select().focus();
					}else {
						// 找不到订单
						$('#apvid').select().focus();
						// 唯一码日志
						addWhUniqueSkuLog(uuid, '');
						audioPlay('error');
					}
	             },
	             error:function() {
	                 layer.alert('扫描失败，请重新扫描');
	             }
	        }); 
		}
		
		//检查数量手动录入是否勾选
		function QuantityON(){
			return false;
		}
		
		// 扫描第二个sku
		function inputsku(obj) {
			var sku=obj.value;
			var uuid= sku;
			//订单Id,切分二维码. 如果存在的话
            if(!(sku.indexOf("=") == -1)){
                var realSku = sku.split('=')[0];
                $('#sku').val(realSku);
                sku = realSku;
            }
            if (QuantityON()) {
				
			} else {
				checkIn(sku, uuid);
			}
		}
		
		// 扫描第二个sku
		function inputsku(obj, type){
			var sku=obj.value;
			var uuid = sku.trim();
			//订单Id,切分二维码. 如果存在的话
            if(!(sku.indexOf("=") == -1)){
                var realSku = sku.split('=')[0];
                $('#sku').val(realSku);
                sku = realSku;
            }
            if (type != 1) {
            	checkScanPackingUniqueSku(sku, uuid);
			}else {
				checkIn(sku, uuid);
			}
		}
		
		//核对sku和数量 .正确(待检区 消失   已检查区  对应的sku 数量增加...)
		function checkIn(sku, uuid) {
			sku = $.trim(sku.toUpperCase());
			if(completeCheck()){
				layer.alert('检查完毕');
				return;
			}
			if ($('[id="check_sku_'+sku+'"]').length==0) {
				clear('sku');
				$('#sku').select().focus();
				layer.alert("SKU不存在或已检查完毕");
				return;
			}
			
			if ($('[id="check_sku_'+sku+'"]').length==0) {
	            sku = "JR" + sku;
	            if ($('[id="check_sku_' + sku + '"]').length == 0) {
	                clear();
	                $('#sku').select().focus();
	                layer.alert("SKU不存在或已检查完毕",{closeBtn: 0},function (index) {
						layer.close(index);
	                	addWhUniqueSkuLog(uuid, '');
					});
	                return;
	            }
	        }
	
			if ($('[id="check_sku_'+sku+'"]').length==0) {
				// 声音
				audioPlay('error');
				return;
			}
			
			var q = parseInt($('[id="check_quantity_'+sku+'"]').eq(0).text());
			if (q == 0 || 1 > q){
				layer.alert('拿多了？',{closeBtn: 0},function (index) {
					layer.close(index);
					clear('quantity');
					audioPlay('error');
				});
				return;
			}
			
			//减少数量
			$('[id="check_quantity_'+sku+'"]').eq(0).html(q - 1);
			if (q - 1 == 0){
				$('[id="check_sku_'+sku+'"]').eq(0).parents('tr:eq(0)').remove();
			}
			
			//增加已检查数量
			var org_q = parseInt($('[name="checked_quantity_'+sku+'"]').eq(0).val());
			$('[name="checked_quantity_'+sku+'"]').eq(0).val(org_q + 1);
			// 检查是否完成
			clear('sku');
		}
		
		//是否已经检查完(检查完后清空页面) 
		function completeCheck() {
			tr=$('.uncheck').find('tr');
			if (tr.length<=1) {
				return true;
			}
			return false;
		}
		
		//清空核对区
		function clear(situation) {
			if (completeCheck()) {
				calsf();
				pageInit();
			} else {
				if (situation=='quantity' && QuantityON()) {
					$('#quantity').select().focus();
				} else {
					$('#sku').val("");
					$('#sku').focus();
				}
			}
		};
		
		//清除计数
		function cleanCount() {
			var storage = new WebStorageCache();
			storage.delete(cacheKey);
			$('#panel-title').html('<h1  style="color:blue;font-size:48px;">成功：<b>'+0+'</b></h1>');
			storage.delete(pieceCacheKey);
			$('#panel-title-piece').html('<h1  style="color:red;font-size:48px;">计数:<b>'+0+'</b></h1>');
		}
		
		// 统计扫描成功和失败的数量
	    function calsf(){
	  		var storage = new WebStorageCache();
	  		var lastSuc = 0;
	  		if (storage.get(cacheKey)) {
	  			lastSuc = storage.get(cacheKey);
	  		}
	  		var suc = parseInt(1) + lastSuc;
	  		storage.set(cacheKey, suc, {exp : 5 * 60 * 60});
	    	$('#panel-title').html('<h1  style="color:blue;font-size:36px;">总成功：<b>'+suc+'</b></h1>');
	    	// 记录后台
	    	var apvId = $('#check_scan_datas').find("input[name='apvId']").val();
	    	var quantity = $('#check_scan_datas').find("input[name='totalSaleQuantity']").val();
	    	var taskNo = $("#orderid").val();
	    	//当前任务成功数量
	    	var taskSuc = 0;
	    	var taskKey = taskNo + "_" + cacheKey;
	    	if (storage.get(taskKey)) {
	    		taskSuc = storage.get(taskKey);
	  		}
	    	var taskSuc = 1 + taskSuc;
	    	storage.set(taskKey, taskSuc, {exp : 5 * 60 * 60});
	    	$('#panel-title-task').html('<h1  style="color:blue;font-size:24px;">当前成功 （<b>'+taskSuc+'</b>）</h1>');
	    	// PASS
			$.get("${CONTEXT_PATH}apv/packs/sm/fire/check/pass", {"apvId" : apvId, "totalQuantity" : quantity, "taskNo" : taskNo}, function(response) {
				if(response.status == '200'){
			    	// 计件
					calsfPiece(quantity);
					printApvNo($("#apv-no-now").val());
					//最后一个扫描完成
					if(response.message == taskNo){
						$("#orderid").val('');
						$('#orderid').select().focus();
					}
		      	} else {
					layer.alert("数据提交失败!请重试。");
		      	}
			});
	    }
		
	 	// 计数
		function calsfPiece(quantity) {
	 		if(!quantity) {
	 			quantity = 1;
	 		}
	  		var storage = new WebStorageCache();
	  		var lastSuc = 0;
	  		if (storage.get(pieceCacheKey)) {
	  			lastSuc = storage.get(pieceCacheKey);
	  			lastSuc = parseInt(lastSuc) + parseInt(quantity);
	  		} else {
	  			lastSuc = quantity;
	  		}
	  		storage.set(pieceCacheKey, lastSuc , {exp : 5 * 60 * 60});
	    	$('#panel-title-piece').html('<h1  style="color:red;font-size:36px;">总计数:<b>'+lastSuc+'</b></h1>');
			var taskNo = $("#orderid").val();
	    	//当前计数
	    	var taskSuc = 0;
	    	var taskPieceKey = taskNo + "_" + pieceCacheKey;
	    	if (storage.get(taskPieceKey)) {
	    		taskSuc = storage.get(taskPieceKey);
	    		taskSuc = parseInt(taskSuc) + parseInt(quantity);
	  		} else {
	  			taskSuc = quantity;
	  		}
	    	storage.set(taskPieceKey, taskSuc, {exp : 5 * 60 * 60});
	    	$('#panel-title-piece-task').html('<h1  style="color:blue;font-size:24px;">当前计数 （<b>'+taskSuc+'</b>）</h1>');
	    }
	 	
	 	//任务统计
	 	function taskCalsf(){
	 		var storage = new WebStorageCache();
			var taskNo = $("#orderid").val();
	    	//当前任务成功数量
	    	var taskSuc = 0;
	    	var taskKey = taskNo + "_" + cacheKey;
	    	if (storage.get(taskKey)) {
	    		taskSuc = storage.get(taskKey);
	  		}
	    	storage.set(taskKey, taskSuc, {exp : 5 * 60 * 60});
	    	$('#panel-title-task').html('<h1  style="color:blue;font-size:24px;">当前成功 （<b>'+taskSuc+'</b>）</h1>');
	    	var taskPieceSuc = 0;
			var taskPieceKey = taskNo + "_" + pieceCacheKey;
	    	if (storage.get(taskPieceKey)) {
	    		taskPieceSuc = storage.get(taskPieceKey);
	  		} 
	    	storage.set(taskPieceKey, taskPieceSuc, {exp : 5 * 60 * 60});
	    	$('#panel-title-piece-task').html('<h1  style="color:blue;font-size:24px;">当前计数 （<b>'+taskPieceSuc+'</b>）</h1>');
	 	}
	    
		
	 	function printApvNo(apvNo) {
            document.getElementById('shippingOrderFrame').src = printUrl + apvNo;
            $('#apvid').val('');
			$('#apvid').focus();
			isFocus = true;
	 		setTimeout(focusSku, 1000);
		}
		
		var isFocus = false;
		$("body").click(function() {isFocus = false;});
	    function focusSku() {
	    	if (isFocus) {
	    		$('#apvid').focus();
		    	setTimeout(focusSku, 1000);
			}
	    }
		
		// 重新打印
		function rePrint() {
			printApvNo($("#apv-no-now").val());
		}
		
	
		//打印失败面单
		function failPrint() {
			var apvNo = $("#apv-no-now").val();
			if(apvNo) {
				document.getElementById('shippingOrderFrame').src = CONTEXT_PATH + "apvs/failPrint?apvNo=" + apvNo;
			}
			$('#apvid').val('');
			$('#apvid').focus();
		}
		
		// 初始化打印页面
		document.getElementById("shippingOrderFrame").onload = function() {
	        setTimeout(function() {
	            if(typeof(eval(window.frames["shippingOrderFrame"].myPrint))=='function'){
	                window.frames["shippingOrderFrame"].myPrint();
	            }
	        }, 300);
	    };
		
		// 打印剩余SKU
		function printRemainder() {
			LODOP=getLodop(document.getElementById('LODOP_OB'),document.getElementById('LODOP_EM'));
			LODOP.PRINT_INIT("打印运单");
			//打印全部
			var clone = $("#check_scan_datas .error").clone();
		    clone.find("table tr").each(function() {
		    	$(this).find("th:eq(0)").remove();
		    	$(this).find("td:eq(0)").remove();
		    	// 使用订单数量
		    	$(this).find(".check_quantity").text($("#check-quantity").text());
		    });
			LODOP.ADD_PRINT_HTM(0,0,"95mm","95mm", clone.html());
			LODOP.PRINT();
		}
		
		/** 打印 **/
		var LODOP; //声明为全局变量
		function myPrint() {
			App.unblockUI();
			//先判断 内页中是否有 打印 方法 有的话直接调用
			try {
				if (typeof(eval(window.frames["printHtml"].myPrint))=='function') {
					return window.frames["printHtml"].myPrint();
				}
			} catch(e) {
				
			}
			try{
				CreatePrintPage();
				LODOP.PRINT();
			}
			catch(e){
				
			}
		};
		
		function myPrint2() {
			if(confirm("是否确认要手动打印？")) {
				try{
					if(typeof(eval(window.frames["printHtml"].myPrint))=='function'){
						return window.frames["printHtml"].myPrint();
				}
				}catch(e){
						
				}
				CreatePrintPage();
				LODOP.PRINT();
			}
		};
		
		function myPreview() {
			//先判断 内页中是否有 打印 方法 有的话直接调用
			try{
				if(typeof(eval(window.frames["printHtml"].myPreview))=='function'){
					return window.frames["printHtml"].myPreview();
				}
			} catch(e) {
				
			}
			CreatePrintPage();
			LODOP.PREVIEW();
		}
		
		function CreatePrintPage() {
			LODOP=getLodop(document.getElementById('LODOP_OB'),document.getElementById('LODOP_EM'));
			LODOP.PRINT_INIT("打印运单");
			//打印全部
			LODOP.ADD_PRINT_HTM(0,0,"95mm","95mm",GetIframeInnerHtml(window.frames["printHtml"]));
		};
			
		function CheckLodopIsInstall() {
			LODOP=getLodop(document.getElementById('LODOP_OB'),document.getElementById('LODOP_EM'));
			LODOP.PRINT_INIT("打印运单");
			try{
				var LODOP=getLodop(document.getElementById('LODOP_OB'),document.getElementById('LODOP_EM'));
				if ((LODOP!=null)&&(typeof(LODOP.VERSION)!="undefined")){
					// $('#print_info').text("本机已成功安装过Lodop控件!\n 版本号:"+LODOP.VERSION);
				}
			} catch(err) {
				$('#print_info').text("Error:本机未安装Lodop 或需要升级!");
			}
		}
		
		function GetIframeInnerHtml(objIFrame) {
			var iFrameHTML = objIFrame.document.getElementById('print_content').innerHTML;
		}
		
		// iframe加载完毕后，打印iframe的内容
		var printed = false;
		function IframeOnloadPrint() {
			var iframe=document.getElementById("printHtml");
			if (iframe.attachEvent) {
			    iframe.attachEvent("onload", function(){
			    	printed = true;
				    myPrint();
			    });
			} else {
			    iframe.onload = function(){
			    	printed = true;
			    	myPrint();
			    	return;
			    };
			}
			
			printed = false;
		}
		
		// 撤销减数量
	    function subtractsf() {
	  		var storage = new WebStorageCache();
	  		var lastSuc = 0;
	  		if (storage.get(cacheKey)) {
	  			var lastSuc = storage.get(cacheKey);
	  			var suc = lastSuc - 1;
	  			if(suc < 0) {
	  				suc = 0;
	  			}
	  			storage.set(cacheKey, suc, {exp : 5 * 60 * 60});
	  			$('#panel-title-piece').html('<h1  style="color:red;font-size:48px;">计数:<b>'+lastSuc+'</b></h1>');
	  		}
	    }
		
	 	// 添加唯一码包装日志
	    function addWhUniqueSkuLog(uuid, apvNo) {
	        var r = $.ajax({
	            type : "get",
	            url :CONTEXT_PATH+"apv/packs/addWhUniqueSkuLog" ,
				data : {uuid : uuid, apvNo: apvNo, packType: 11},
	            timeout : 100000,
	            beforeSend : function() {
	            },
	            success : function(responese) {

	            },
	            error : function() {
	            }
	        });
			addPackExceptionRecord(uuid, apvNo);
	    }

		// 新增包装异常记录-未匹配发货单
		function addPackExceptionRecord(uuid, apvNo){
			if(apvNo != undefined && apvNo != ''){
				return;
			}
			let taskNo = $('#orderid').val();
			$.ajax({
				type: "POST",
				url: CONTEXT_PATH+"apv/packs/createPackExceptionUuidItem",
				data: {uuid: uuid, taskNo: taskNo, scanPage: 11, packExceptionType: 2},
				success: function(response) {
				},
				error:function () {
				}
			});
		}
		
	 	// 校验唯一码重复扫描
	    function checkScanPackingUniqueSku(sku, uuid) {
	        var r = $.ajax({
	            type : "get",
	            url :CONTEXT_PATH+"apv/packs/checkScanPackingUniqueSku" ,
	            data : {uuid : uuid},
	            timeout : 100000,
	            beforeSend : function() {
	                App.blockUI();
	            },
	            success : function(responese) {
	                App.unblockUI();
	                if (responese.status == '500') {
	                    layer.alert(responese.message, {closeBtn: 0},function (index) {
	                    	layer.close(index);
							$('#sku').val("");
							$('#sku').focus();
						});
	                } else {
	                    // 前端缓存校验是否重复扫描
	                    if(!checkSkuUuIdStorageCache(uuIdCacheKey, uuid)){
	                        layer.alert("唯一码重复扫描！", {closeBtn: 0},function (index) {
								layer.close(index);
								$('#sku').val("");
								$('#sku').focus();
							});
	                        return;
	                    }
	                    checkIn(sku, uuid);
	                }
	            },
	            error : function() {
	                App.unblockUI();
	                layer.alert('扫描失败，请重新扫描', {closeBtn: 0},function (index) {
	                	layer.close(index);
						$('#sku').val("");
						$('#sku').focus();
					});
	            }
	        });
	    }
	</script>
</body>
</html>