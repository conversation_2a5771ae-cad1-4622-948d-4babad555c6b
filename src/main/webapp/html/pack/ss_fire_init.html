<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		.fire-apv-td {
			background-color: #cccccc;
		    font-weight: bold;
		    direction: ltr;
		}
		.apv-td-width {
			width: 25%;
		}
		#scan_datas table tr td {
			border:1px solid #A8A8A8 !important; 
		}
		.input-class {
			outline-color: invert ;
			outline-style: none ;
			outline-width: 0px ;
			border: none ;
			border-style: none ;
			text-shadow: none ;
			-webkit-appearance: none ;
			-webkit-user-select: text ;
			outline-color: transparent ;
			box-shadow: none;
		}
	</style>
</head>
<body>
	<@header method="header" active="12050000" ><#include "/ftl/header.ftl"></@header>
	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">发货扫描</a>
					<li class="active">热销单件合单扫描</li>
				</ul>
			</div>
		</div>
		<#include "/common/pack_bgcolor_selector.html">

		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none;height: 100%;">
			<div class="row">
				<div id="scan-area">
					<div class="w300px panel layout-panel layout-panel-west" style="width: 250px; left: 0px; top: 0px;">
						<div class="panel-tool"></div>
					</div>
					<div class="panel-body layout-body mt10" data-options="region:'west',title:'扫描区'," title="" id="input_scan">
						<h3 style="display: inline-block">扫描区域</h3>
						
						<label>拣货任务号</label>
						<input type="text" class="input-mini" name="taskno" id="taskno" onkeypress="if(event.keyCode==13) { inputtaskno2(this); return false;}"tabindex="4"/>
						<input type="hidden" id="taskno_hidden" value=""/>
						
						<label style="margin-left: 15px">SKU</label>
						<input class="input-medium" type="text" tabindex="4" onpaste="return false" onkeypress="if(event.keyCode==13) { inputnext(this); return false;}" id="sku" name="sku">
                        <button type="button" class="btn red" onclick="failPrint()">
							<i class="icon-print"></i> 打印失败面单
						</button>
						<span id="panel-title-task" style="display: inline-block;margin-left: 20px"><h1 style="color:blue;font-size:24px;">当前成功 <b>0</b>  </h1></span>
						<div style="display: inline-block;margin-left: 10px" class="panel-title2" id="panel-floor-task"></div>
						<span id="panel-title" style="display: inline-block;margin-left: 20px"><h1 style="color:blue;font-size:36px;">总成功 <b>0</b>  </h1></span>
					</div>
				</div>
				<!-- start row -->
				<div class="row">
					<div id="pick-no-scan" style="width: 75%;display: none;" class="col-md-9">
						<div id="check-area" style="padding: 10px;">
							<div class="panel-header" style="overflow: hidden;">
								<div style="width: 90px;" class="fl panel-title2" id="panel-floor"></div>
							</div>
							<div style="height: 650px;float: left;width: 50%;">
								<!-- 包装信息 -->
								<div id="scan_datas">
									<table class="table table-bordered table-hover table-condensed" style="height: 100%;width: 100%;">
										<tbody>
											<tr>
												<td colspan="4" style="text-align: left;">
													<a class="btn yellow" href="javascript:void(0);" onclick="revoke2()">撤销 <i class="icon-undo"></i></a>
												</td>
											</tr>
											<#if apv.buyerCheckoutList?seq_contains('EUR')>
												<tr>
													<td style="background-color: #ffdf25;border-right: none;font-weight:900; font-size: 30px; width: 200px;line-height: 80px;" class="fire-apv-td">
														${util('enumName', 'com.estone.apv.enums.ApvTaxTypeEnum', 'EUR')}标签
													</td>
													<td style="background-color: #ffdf25;border-left: none;" colspan="3">
														<div>
															<div style="text-align: center;margin-top: 0mm;float: left;">
																<div style="float: left;margin-top: 5px;">
																	<div style="font-weight: 800;border: 1px solid #000000;float: left;width: 60px;line-height: 40px;">EC</div>
																	<div style="font-weight: 800;border: 1px solid #000000;float: left;width: 60px;border-left: none;line-height: 40px;">REP</div>
																</div>
																<div style="float: left;margin-left: 2mm;">
																	<!-- MAC90178-BK 10px会越界-->
																	<div style="font-weight: 600;font-size: 12px; text-align: left;">SHUNSHUN GmbH</div>
																	<div style="font-weight: 600;font-size: 12px;text-align: left;">Römeräcker 9 Z2021,76351</div>
																	<div style="font-weight: 600;font-size: 11px; text-align: left;">Linkenheim-Hochstetten,Germany</div>
																</div>
															</div>
														</div>
													</td>
												</tr>
												<tr style="height: 25px;"></tr>
											</#if>
											<tr>
												<td class="fire-apv-td">发货单号</td>
												<td colspan="3">
													<input id="logisticsType" type="hidden" name="logisticsType" value="${apv.logisticsType }" />
													<input id="apvNo" type="text" readonly="readonly" class="input-class" style="font-weight: bold;">
                                                    <#if apv.buyerCheckoutList?seq_contains('RFP')>
                                                        <span style="background-color: red;border-right: none;font-weight:900; font-size: 30px;width: 200px;line-height: 80px;" class="fire-apv-td">
                                                            ${util('enumName', 'com.estone.apv.enums.ApvTaxTypeEnum', 'RFP')}
                                                        </span>
                                                    </#if>
												</td>
											</tr>
											<tr style="height: 25px;"></tr>
											<tr>
												<td class="fire-apv-td">SKU</td>
												<td class="apv-td-width" style="text-align: left;">
													<input id="sku2" type="text" name="sku" readonly="readonly" class="input-class">
												</td>
												<td class="fire-apv-td">数量</td>
												<td class="apv-td-width">
													<span id="saleQuantity" style="font-size: 20px;font-weight: 900;"></span>
												</td>
											</tr>
											<tr>
												<td class="fire-apv-td">商品名称</td>
												<td colspan="3" style="text-align: left;">
													<span id="sku-name"></span>
												</td>
											</tr>
											<tr>
												<td class="fire-apv-td">包材</td>
												<td style="text-align: left;">
													<span id="material" style="font-size: 24px;font-weight: 900;"></span>
												</td>
												<td class="fire-apv-td">搭配包材</td>
												<td>
													<span id="colloPackingMaterials" style="font-size: 24px;font-weight: 900;"></span>
												</td>
											</tr>
											<tr>
												<td class="fire-apv-td">是否需要原包装</td>
												<td colspan="3" style="text-align: left;">
													<span id="isOriginalPackage" style="font-size: 24px;font-weight: 900;color: red;"></span>
												</td>
											</tr>
											<tr>
												<td class="fire-apv-td">包装备注</td>
												<td colspan="3" style="text-align: left;">
													<span id="packingMemo" style="font-size: 24px;font-weight: 900;color: red;"></span>
												</td>
											</tr>
											<tr>
												<td colspan="4">
													<img id="sku-img" src="#" width="300px" height="250px;" />
												</td>
											</tr>
                                            <tr>
                                                <td colspan="4">
                                                    <a class="btn yellow" href="javascript:void(0);" id="packaging_error_btn">包装信息纠错</a>
                                                </td>
                                            </tr>
										</tbody>
									</table>
								</div>
							</div>
							<!-- 面单打印 -->
							<div id="print_area-scan" class="row" style="float: right;width: 50%;height: 650px;">
								<iframe src="javascript:void(0);" frameborder="0" marginheight="0" marginwidth="0" frameborder="0" scrolling="auto" id="shippingOrderFrame" name="shippingOrderFrame" width="100%" height="500px"></iframe>
							</div>
						</div>
					</div>
					<div id="check-apv-scan" style="width: 25%;height: 650px;display: none;float: right;overflow-y: scroll;" class="col-md-3">
						<table id="apv_list_table" class="table table-striped table-bordered table-hover table-condensed mt10">
							<colgroup>
								<col width="10%" />
								<col width="60%" />
								<col width="30%" />
							</colgroup>
							<thead>
								<tr >
									<th>序号</th>
									<th>YST</th>
									<th>状态</th>
								</tr>
							</thead>
							<tbody id="apv_list_tbody">
							
							</tbody>
						</table>
					</div>
				</div>
			</div>
			<!-- end row -->
		</div>
		<#include "/common/footer.html">
	</div>
	
	<object width="0" height="0" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" id="LODOP_OB">
	  <embed width="0" height="0" type="application/x-print-lodop" id="LODOP_EM">
   	</object>
	
	<!-- 	打印插件 -->
   	<script type="text/javascript" src="${CONTEXT_PATH }js/LodopFuncs.js"></script>
   	<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
   	
   	<script type="text/javascript" src="${CONTEXT_PATH }js/jquery.sound.js?v=${.now?datetime}"></script>
   	<script type="text/javascript" src="${CONTEXT_PATH }js/pages/pack-style.js?v=${.now?datetime}"></script>
	
	<script type="text/javascript">
		// 打印地址
		var printUrl = "${CONTEXT_PATH }apv/packs/oms/print?apvNo=";

		// 热销唯一码
		var publicSku;
		
		// 发货单数据JSON
		var apvDTOs;
		
		// 发货单id
		var apvDTOId;
		
		//记录扫描的apvNo
		var apvNo;

		var cacheKey = "scan_success";
		$(document).ready(function(){
			input_init();
	  		var storage = new WebStorageCache();
	  		if (storage.get(cacheKey)) {
	  			lastSuc = storage.get(cacheKey);
	  			$('#panel-title').html('<h1  style="color:blue;font-size:36px;">总成功：<b>'+lastSuc+'</b></h1>');
	  		}
		});
		
		//清除计数
		function cleanCount(){
			var storage = new WebStorageCache();
			storage.delete(cacheKey);
			$('#panel-title').html('<h1  style="color:blue;font-size:36px;">总成功：<b>'+0+'</b></h1>');
		}
		
		// 初始化
		function input_init(){
			$('#taskno').val('');
			$('#taskno').focus();
			$('#sku').val('');
		}
		
		//成功后初始化
		function success_init(){
			$('#sku').val('');
			$('#sku').focus();
		}
		
		// 扫描拣货任务号JSON
		function inputtaskno2(obj){
			if(!obj.value || obj.value.replace(/\s/g,'') == ''){
				layer.alert("请输入拣货任务号!");
				return ;
			}
			var taskNo = obj.value.replace(/\s/g,'');
			taskNo = $.trim(taskNo);
			 var r= $.ajax({
				url : CONTEXT_PATH + "apv/packs/ss/fire/check/taskNo/json",
				data : {taskNo : taskNo},
	            timeout : 100000,
	            success : function(response){
	            	if(response.status == '200'){
	            		$("#taskno_hidden").val(taskNo);
	            		$("#check-apv-scan").css("display", "block");
	            		apvDTOs = JSON.parse(response.message);
	            		var html = '';
	            		for (var i = 0; i < apvDTOs.length; i++) {
	            			var apvDTO = apvDTOs[i];
	            			var yst = apvDTO.apvNo;
	            			var waybillSize = apvDTO.waybillSize;
	            			var ystId = apvDTO.id;
                            publicSku = apvDTO.sku;
	            			var status = apvDTO.status;
	            			if (status == 16) {
	            				html += '<tr id="tr_apv_'+yst+'">'
            					+'<td id="apv_'+i+'" style="color: red;">'
            						+ (i+1)
            					+'</td>'
            					+'<td id="apvNo_'+yst+'" style="color: red;">'
            						+ yst + '('+waybillSize+')'
            					+'</td>'
            					+'<td id="apvStatus_'+yst+'" style="color: red;">'
            						+ "等待发货"
            					+'</td>'
            					+'</tr>';
							}else {
	            				html += '<tr id="tr_apv_'+yst+'">'
            					+'<td id="apv_'+i+'">'
            						+ (i+1)
            					+'</td>'
            					+'<td id="apvNo_'+yst+'">'
									+ yst + '('+waybillSize+')'
            					+'</td>'
            					+'<td id="apvStatus_'+yst+'">'
            						+ "单件合单"
            					+'</td>'
            					+'</tr>';
							}
						}
	            		$("#apv_list_tbody").html(html);
	            		taskCalsf();
	            		$('#sku').select().focus();
	            	}else{
	            		var msg = response.message;
						toastr.options = {
				            closeButton: true,
				            debug: false,
				            timeOut: "5000",
				            positionClass: 'toast-top-center'
					    };
						toastr['error'](msg, "查询失败");
						$('#taskno').val('');
						$('#taskno').focus();
	            	}
	             },
	             error:function(){
	                 layer.alert('扫描失败，请重新扫描');
	             }
	        }); 
		}
	 
		//扫描sku
		function inputnext(obj) {
			// 检查是否还有输入框未输入值
			var visible_inputs = $('#input_scan').find("input:visible").length;
			for (i = 0; i < visible_inputs; i++) {
				var e = $('#input_scan').find("input:visible").get(i);
				if ($(e).val() == '') {
					$(e).focus();
					return false;
				}
			}
			input_submit2(obj);
			success_init();
		}
		
		// 统计扫描成功数量
	    function calsf(){
	  		var storage = new WebStorageCache();
	  		var lastSuc = 0;
	  		if (storage.get(cacheKey)) {
	  			lastSuc = storage.get(cacheKey);
	  		}
	  		var suc = 1 + lastSuc;
	  		storage.set(cacheKey, suc, {exp : 5 * 60 * 60});
	    	$('#panel-title').html('<h1  style="color:blue;font-size:36px;">总成功：<b>'+suc+'</b></h1>');
	    	//当前任务成功数量
	    	var taskSuc = 0;
	    	var taskno = $("#taskno").val();
	    	var taskKey = taskno + "_" + cacheKey;
	    	if (storage.get(taskKey)) {
	    		taskSuc = storage.get(taskKey);
	  		}
	    	var taskSuc = 1 + taskSuc;
	    	storage.set(taskKey, taskSuc, {exp : 5 * 60 * 60});
	    	$('#panel-title-task').html('<h1  style="color:blue;font-size:24px;">当前成功 （<b>'+taskSuc+'</b>）</h1>');
	    }
		
		//当前任务数量
		function taskCalsf(){
			//当前任务成功数量
	    	var taskSuc = 0;
	    	var taskno = $("#taskno").val();
	    	var taskKey = taskno + "_" + cacheKey;
	    	var storage = new WebStorageCache();
	    	if (storage.get(taskKey)) {
	    		taskSuc = storage.get(taskKey);
	  		}
	    	storage.set(taskKey, taskSuc, {exp : 5 * 60 * 60});
	    	$('#panel-title-task').html('<h1  style="color:blue;font-size:24px;">当前成功 （<b>'+taskSuc+'</b>）</h1>');
		}
		
	 	// 撤销减数量
	    function subtractsf(){
	  		var storage = new WebStorageCache();
	  		var lastSuc = 0;
	  		if (storage.get(cacheKey)) {
	  			var lastSuc = storage.get(cacheKey);
	  			var suc = lastSuc - 1;
	  			if(suc < 0) {
	  				suc = 0;
	  			}
	  			storage.set(cacheKey, suc, {exp : 5 * 60 * 60});
		    	$('#panel-title').html('<h1  style="color:blue;font-size:36px;">总成功：<b>'+suc+'</b></h1>');
	  		}
	  		//当前任务成功数量
	    	var taskSuc = 0;
	    	var taskno = $("#taskno").val();
	    	var taskKey = taskno + "_" + cacheKey;
	    	if (storage.get(taskKey)) {
	    		taskSuc = storage.get(taskKey);
	  		}
	    	var taskSuc = taskSuc - 1;
	    	if(taskSuc < 0) {
	    		taskSuc = 0;
  			}
	    	storage.set(taskKey, taskSuc, {exp : 5 * 60 * 60});
	    	$('#panel-title-task').html('<h1  style="color:blue;font-size:24px;">当前成功 （<b>'+taskSuc+'</b>）</h1>');
	    }
		
		//获取打印和已经扫描的数据 JSON 
		function input_submit2(obj) {
			//防止查询大数据
			if(!obj.value || obj.value.replace(/\s/g,'') == ''){
				layer.alert("请输入有效sku!");
				return ;
			}
			var uuid = obj.value.trim();
			checkScanPackingUniqueSku(uuid);
		}
		
		// 校验唯一码重复扫描
		function checkScanPackingUniqueSku(uuid) {
			var r = $.ajax({
				type : "get",
				url :CONTEXT_PATH+"apv/packs/checkScanPackingUniqueSku" ,
				data : {uuid : uuid},
				timeout : 100000,
				beforeSend : function() {
					App.blockUI();
				},
				success : function(responese) {
					App.unblockUI();
					if (responese.status == 500) {
						layer.alert(responese.message);
						return false;
					} else {
						input_submit3(uuid);
					}
				},
				error : function() {
					App.unblockUI();
					layer.alert('扫描失败，请重新扫描', 'error');
				}
			});
		}
		
		function input_submit3(val) {
			var taskNo = $("#taskno_hidden").val();
			if (!taskNo) {
				layer.alert("请扫描拣货任务号", "error");
				return false;
			}
			
			var uuid = val;
			if(!(val.indexOf("=") == -1)) {
				var realSku = val.split('=')[0];
				$('#sku').val(realSku);
				val = realSku;
			}

			if (publicSku != val) {
                layer.alert("请输入正确的SKU", "error");
                return false;
            }
			
			$("#pick-no-scan").css("display", "none");
       	 	$("#print_area-scan").css("display", "none");
			
			$('#logisticsType').val('');
			$('#apvNo').val('');
			$('#sku2').val('');
			$('#saleQuantity').text('');
			$('#material').text('');
			$('#sku-name').text('');
			$('#sku-img').attr('src', '#');
			$('#colloPackingMaterials').text('');
			$('#isOriginalPackage').text('');
			$('#packingMemo').text('');
            $('#packaging_error_btn').off('click');
			
			var r= $.ajax({
	        	url : CONTEXT_PATH + "apv/packs/ss/fire/pass",
				data : {taskNo : taskNo},
	            timeout : 100000,
	            success : function(response) {
	            	if (response.status == '200'){
	       				var apvDTO = response.body.whApv;
	       				var yst = apvDTO.apvNo;
	           			var orderOrigin = apvDTO.shipService;
	           			var ystId = apvDTO.id;
	           			var sku2 = apvDTO.sku;
	           			var status = apvDTO.status;
	           			
	           			// 全局参数
           				apvNo = yst;
           				apvDTOId = ystId;
           				
           				// 显示订单包装详情
           				$("#pick-no-scan").css("display", "block");
           				$("#print_area-scan").css("display", "block");
           				
           				// 下方订单包装详情展示
    					$('#logisticsType').val(apvDTO.logisticsType);
    					$('#apvNo').val(yst);
    					$('#sku2').val(apvDTO.sku);
    					$('#saleQuantity').text(apvDTO.saleQuantity);
    					$('#material').text(apvDTO.material);
    					$('#sku-name').text(apvDTO.name);
    					$('#colloPackingMaterials').text(apvDTO.colloPackingMaterials);
    					$('#sku-img').attr('src', apvDTO.imageUrl);
    					$('#isOriginalPackage').text(apvDTO.isOriginalPackage);
    					$('#packingMemo').text(apvDTO.packingMemo);
                        $('#packaging_error_btn').on('click',function(){createPackagingErrorInformation(apvDTO.sku);});
    				
    					// 更新右侧列表
           				$("#apvStatus_"+yst).text("等待发货");
           				$("#tr_apv_"+yst).css("color", "red");
           				
           				// 添加唯一码成功日志
    					addWhUniqueSkuLog(uuid, yst);
    					//统计数量(成功和失败)
    				    calsf();
    				    audioPlay('success');
    				    //打印面单
    				    printApvNo(apvNo);
    				    success_init();
	            	} else {
	            		// 匹配失败，记录唯一码日志
	            		addWhUniqueSkuLog(uuid, '');
	            		var msg = response.message;
						layer.alert('匹配订单失败：msg=' + msg, 'error');
	            	}
	             },
	             error:function() {
	                 layer.alert('匹配订单失败！', 'error');
	             }
	        });
		}
		
		// 添加唯一码包装日志
	    function addWhUniqueSkuLog(uuid, apvNo) {
	        var r = $.ajax({
	            type : "get",
	            url :CONTEXT_PATH+"apv/packs/addWhUniqueSkuLog" ,
				data : {uuid : uuid, apvNo: apvNo, type: 1, packType: 10},
	            timeout : 100000,
	            beforeSend : function() {
	            	
	            },
	            success : function(responese) {

	            },
	            error : function() {
	            }
	        });
			addPackExceptionRecord(uuid , apvNo);
	    }

		// 新增包装异常记录-未匹配发货单
		function addPackExceptionRecord(uuid, apvNo){
			if(apvNo != undefined && apvNo != ''){
				return;
			}
			let taskNo = $('#taskno').val();
			$.ajax({
				type: "POST",
				url: CONTEXT_PATH+"apv/packs/createPackExceptionUuidItem",
				data: {uuid: uuid, taskNo: taskNo, scanPage: 10, packExceptionType: 2},
				success: function(response) {
				},
				error:function () {
				}
			});
		}
		
	 	function printApvNo(apvNo){
            document.getElementById('shippingOrderFrame').src = printUrl + apvNo;
	 		$('#sku').focus();
	 		isFocus = true;
	 		setTimeout(focusSku, 1000);
		}
		
		var isFocus = false;
		$("body").click(function() {isFocus = false;});
	    function focusSku(){
	    	if (isFocus) {
	    		$('#sku').focus();
		    	setTimeout(focusSku, 1000);
			}
	    }
	 	
		// 撤销已经打印的订单
		function revoke2() {
			var apvId = apvDTOId;
			if(confirm("确定要撤销已经打印的订单？")) {
				var r = $.ajax({
					type : "get",
					url :CONTEXT_PATH+"apv/packs/revoke" ,
					data : {
						"apvId": apvId,
						"apvNo": $('#apvNo').val() 
					},
					timeout : 100000,
					beforeSend : function() {
						
					},
					success : function(r) {
						// 隐藏apv包装区和打印区
						$("#pick-no-scan").css("display", "none");
						$("#print_area-scan").css("display", "none");
						
						$('#logisticsType').val('');
						$('#apvNo').val('');
						$('#sku2').val('');
						$('#saleQuantity').text('');
						$('#material').text('');
						$('#sku-name').text('');
						$('#sku-img').attr('src', "#");
						for (var i = 0; i < apvDTOs.length; i++) {
       						var apvDTO = apvDTOs[i];
       						var yst = apvDTO.apvNo;
       						var ystId = apvDTO.id;
       						var waybillSize = apvDTO.waybillSize;
       						if (ystId == apvId) {
								apvDTO.status = 10;
								var html = ''
									+'<td id="apv_'+i+'">'
            							+ (i+1)
            						+'</td>'
   									+'<td id="apvNo_'+yst+'">'
										+ yst + '('+waybillSize+')'
   									+'</td>'
   									+'<td id="apvStatus_'+yst+'">'
   										+ "单件合单"
   									+'</td>';
				
       							$("#tr_apv_"+yst).html(html);
       							break;
       						}
						}
						
						$('#sku').focus();
						subtractsf();
						var msg = "修改成功";
						
						toastr.options = {
					            closeButton: true,
					            debug: false,
					            timeOut: "5000",
					            positionClass: 'toast-top-center'
					    };
						toastr['success'](msg, "操作成功");
					},
					error : function() {
					}
				});
			}
		}
		
		function failPrint(){
			if(apvNo){
				document.getElementById('shippingOrderFrame').src = CONTEXT_PATH + "apvs/failPrint?apvNo=" + apvNo;
			}
			$('#sku').focus();
		}
		
		/** 打印 **/
		var LODOP; //声明为全局变量
		function myPrint() {
			App.unblockUI();
			//先判断 内页中是否有 打印 方法 有的话直接调用
			try{
			if(typeof(eval(window.frames["printHtml"].myPrint))=='function'){
				return window.frames["printHtml"].myPrint();
			}
			}catch(e){
			}
			try{
				CreatePrintPage();
				LODOP.PRINT();
			}
			catch(e){
			}
		};
		
		function myPrint2() {
			if(confirm("是否确认要手动打印？")) {
				try{
					if(typeof(eval(window.frames["printHtml"].myPrint))=='function'){
						return window.frames["printHtml"].myPrint();
					}
				}catch(e){
				}
				CreatePrintPage();
				LODOP.PRINT();
			}
		};
		
		function myPreview() {
			//先判断 内页中是否有 打印 方法 有的话直接调用
			try{
				if(typeof(eval(window.frames["printHtml"].myPreview))=='function'){
					return window.frames["printHtml"].myPreview();
				}
			}catch(e){
				
			}
			CreatePrintPage();
			LODOP.PREVIEW();
		}
		
		function CreatePrintPage() {
			LODOP=getLodop(document.getElementById('LODOP_OB'),document.getElementById('LODOP_EM'));
			LODOP.PRINT_INIT("打印运单");
			//打印全部
			LODOP.ADD_PRINT_HTM(0,0,"100mm","100mm",GetIframeInnerHtml(window.frames["printHtml"]));
		};
			
		function CheckLodopIsInstall() {
			LODOP=getLodop(document.getElementById('LODOP_OB'),document.getElementById('LODOP_EM'));
			LODOP.PRINT_INIT("打印运单");
			try{
			var LODOP=getLodop(document.getElementById('LODOP_OB'),document.getElementById('LODOP_EM'));
			if ((LODOP!=null)&&(typeof(LODOP.VERSION)!="undefined")){
				// $('#print_info').text("本机已成功安装过Lodop控件!\n 版本号:"+LODOP.VERSION);
			}
			}catch(err){
				$('#print_info').text("Error:本机未安装Lodop 或需要升级!");
			}
		}
		
		function GetIframeInnerHtml(objIFrame) {
			var iFrameHTML = objIFrame.document.getElementById('print_content').innerHTML;
			return iFrameHTML;
		}
		
		// iframe加载完毕后，打印iframe的内容
		var printed = false;
		function IframeOnloadPrint(){
			var iframe=document.getElementById("printHtml");
			if (iframe.attachEvent){
			    iframe.attachEvent("onload", function(){
			    	printed = true;
				    myPrint();
			    });
			} else {
			    iframe.onload = function(){
			    	printed = true;
			    	myPrint();
			    	return;
			    };
			}
			printed = false;
		}
		
		document.getElementById("shippingOrderFrame").onload = function() {
        	setTimeout(function() {
				if(typeof(eval(window.frames["shippingOrderFrame"].myPrint))=='function'){
					window.frames["shippingOrderFrame"].myPrint();
				}
			}, 300);
		};


        function createPackagingErrorInformation(sku){
            var content = `<span>SKU:`+sku+`</span>
                                   <br/>
                                   <br/>
                                   <div>
                                       <h4 class="modal-title" id="selectDownloadHeadersModalLabel">错误信息：</h4>
                                       <div id="table-header" style="width:330px;">
                                           <label class="radio-inline" style="width:100px;">
                                               <input type="checkbox" name="questionFields" value="1">包材
                                           </label>
                                           <label class="radio-inline" style="width:100px;">
                                               <input type="checkbox" name="questionFields" value="2">搭配包材
                                           </label>
                                           <label class="radio-inline" style="width:100px;">
                                               <input type="checkbox" name="questionFields" value="3">包装图片
                                           </label>
                                       </div>
                                   </div>`;
            var diglog = dialog({
                title: '包装信息纠错',
                width: 350,
                height:100,
                content:content,
                okValue: '确定',
                ok: function () {
                    var questionFields = [];
                    var questions = $("input[name='questionFields']:checked");
                    if (!questions || questions.length == 0){
                        layer.alert("未选择错误类型!","error");
                        return;
                    }
                    $.each(questions,function(index,item){
                        var val = $(item).val();
                        questionFields.push(val);
                    });
                    var questionStr = "";
                    for (var i = 0; i < questionFields.length; i++) {
                        var questionField = questionFields[i];
                        questionStr += questionField;
                        if (i != questionFields.length - 1) {
                            questionStr += ",";
                        }
                    }
                    $.ajax({
                        url: CONTEXT_PATH + "/error/information/create",
                        type: "post",
                        data: {sku:sku,questionFields:questionStr},
                        success:function(response){
                            if (response.status == '500') {
                                customizeLayer(response.message, 'error');
                            } else{
                                alert('已创建纠错信息!');
                            }
                        },
                        error:function () {
                            layer.alert("系统异常，操作失败!",'error');
                        }
                    });
                },
                cancelValue: '取消',
                cancel: function () {}
            });
            diglog.show();
        }
	</script>
</body>
</html>