<style type="text/css">
    #pcak-info-table {
        width: 500px;
        margin-top: 10px;
    }
    #pcak-info-table #btn td{
        border: none;
        background-color: transparent;
        padding-left: 0px;
    }
    #pcak-info-table #apvNo td {
        height: 36px;
        text-align: center;
    }
    #pcak-info-table .br td {
        height: 15px;
        border: none;
        background-color: transparent;
    }
    #pcak-info-table td {
        border: 1px solid #bcbcbc;
        width: 140px;
        height: 50px;
        padding-left: 10px;
        background-color: #F9F9F9;
        font-size: 18px;
    }
    #pcak-info-table td.form-label {
        text-align: center;
        background-color: #d7d7d7;
        font-weight: bold;
        font-size: 14px;
    }
    #pcak-info-table td.red {
        color: red;
    }
    .size-blod {
        font-weight: bold;
        font-size: 30px;
    }
    .skuImgMain>img {
        width: 500px;
        height: 500px;
        float: left;
    }

    .skuImgOthersOl {
        float: left;
        width: 90px;
        height: 500px;
        overflow: hidden;
        overflow-y: auto;
        white-space:nowrap;
        padding: 0px;
    }
    .skuImgOthers {
        width: 64px;
        height: 64px;
        margin-top: 10px;
        border: 1px solid #e8e8e8;
    }
    .skuImgOthers>img {
        width: 100%;
        height: 100%;
    }
</style>
<#assign apv = domain.whFbaAllocation >
   
	<#if (apv)!>
		<!-- 不为空操作 -->
		<div style="border-bottom: 1px #CCC dotted; height: 500px;" id="scan-apv-${apv.id }" class="scan_success">
			<div class="scan_success_sub  col-md-5" style="height: 500px">
                <input type="hidden" name="id" value="${apv.id }"/>
				<input type="hidden" name="apvNo" value="${apv.fbaNo }"/>
                <input type="hidden" name="trackingNumber" value="${apv.trackingNumber}"/>
                <input type="hidden" name="purposeHouse" value="${apv.purposeHouse}"/>
                <input type="hidden" name="jitAsn" value="${apv.isAsn?string("true","false") }" />
                <#if apv.whAsnExtra.boxMarkUrl??>
                    <input type="hidden" name="boxMarkUrl" value="${apv.whAsnExtra.boxMarkUrl }" />
                </#if>
                <input type="hidden" name="asnFirst" value="${apv.asnFirst?string("true","false") }" />
				<input  type="hidden"  class="form-control input-small input-inline" size="8" value="${apv.id}" name="apvId" readonly="readonly">
				<#if (domain.apvWaybill)!>
					<!-- 不为空操作 -->
					<input type="hidden" name="apvWaybill.id" value="${domain.apvWaybill.id}"/>
					<input type="hidden" name="apvWaybill.printUrl" value="${domain.apvWaybill.printUrl}"/>
				</#if>
                <input type="hidden" name="taskNo" id="taskNo" value="${domain.taskNo}"/>
                <input type="hidden" name="taskType" id="taskType" value="${domain.taskType}"/>
				<table id="pcak-info-table">
					<tbody>
	                  <#list apv.items as item>
                        <tr id="btn">
                          <td colspan="4">
                              <a class="btn yellow" href="javascript:void(0);" onclick="revoke(${apv.id}, '${apv.fbaNo }')">撤销 <i class="icon-undo"></i></a>
                          </td>
                        </tr>
						<tr class="br"><td colspan="4"></td></tr>
                        <tr id="purposeHouse">
                          <td class="form-label">平台</td>
                          <td colspan="3" class="size-blod">${apv.purposeHouse}</td>
                        </tr>
                        <tr id="apvNo">
							<td class="form-label">发货单号</td>
                            <td colspan="3" class="size-blod">${apv.fbaNo}</td>
						</tr>
						<tr>
							<td class="form-label">SKU
                                <#if item.temuCodeUrl??>
                                    <input type="hidden" id="temuCodeUrl-${item.productSku ? upper_case ? trim }" value="${item.temuCodeUrl}">
                                </#if>
                            </td>
                            <td class="size-blod">${item.productSku}</td>
                            <td class="form-label">数量</td>
                            <td>${item.quantity}</td>
						</tr>
                        <tr>
                          <td class="form-label">商品名称</td>
                          <td colspan="3">${item.whSku.name}</td>
                        </tr>
                        <tr>
                          <td class="form-label">袋子</td>
                          <td class="size-blod">${item.whSku.packagingName}</td>
                          <td class="form-label">辅助耗材</td>
                           <#if (item.whSku.matchMaterialsName)?? && item.whSku.matchMaterialsName?index_of("充气条") != -1 >
                               <td class="size-blod" style="font-size: 25px;background-color: red;color: white;">${item.whSku.matchMaterialsName}</td>
                           <#elseif (item.whSku.matchMaterialsName)?? && item.whSku.matchMaterialsName?index_of("珍珠棉") != -1>
                               <td class="size-blod" style="font-size: 25px;background-color: #009DD9;color: white;">${item.whSku.matchMaterialsName}</td>
                           <#elseif (item.whSku.matchMaterialsName)?? && item.whSku.matchMaterialsName?index_of("充气袋") != -1>
                               <td class="size-blod" style="font-size: 25px;background-color: #FFA500FF;color: white;">${item.whSku.matchMaterialsName}</td>
                           <#else>
                               <td class="size-blod" style="font-size: 25px;">${item.whSku.matchMaterialsName}</td>
                           </#if>
                        </tr>
                        <tr>
                          <td class="form-label">是否需要原包装</td>
                          <td colspan="3" class="red">
                            <#list domain.whSkuWithPmsInfos as skuWithPms>
                                <#if skuWithPms.sku == item.productSku>
                                    <#if (skuWithPms.isOriginalPackage)! && skuWithPms.isOriginalPackage == true>
                                            需要原包装
                                    <#else>
                                            不需要原包装
                                    </#if>
                                    <br><span style="color: black">包装备注：</span>${skuWithPms.packingMemo }
                                </#if>
                            </#list>
                          </td>
                        </tr>
                        <tr>
                          <td class="form-label">商品特性</td>
                          <td colspan="3" class="red size-blod">
                            <#list domain.whSkuWithPmsInfos as skuWithPms>
                                <#if skuWithPms.sku == item.productSku>
                                    ${skuWithPms.skuLabelName }
                                    <#if skuWithPms.tag?? && skuWithPms.tag?index_of("磁性")!=-1>
                                        <br>${skuWithPms.tag }
                                    </#if>
                                </#if>
                            </#list>
                              <#if domain.skuTagMap?? && domain.skuTagMap[item.productSku]??>
                                  <span style="margin-left: 20px;">,${domain.skuTagMap[item.productSku]}</span>
                              </#if>
                          </td>
                        </tr>
<!--                        <tr>-->
<!--                          <td colspan="4">-->
<!--                              <a class="btn yellow" href="javascript:void(0);" onclick="createPackagingErrorInformation('${item.sku}')">包装信息纠错</a>-->
<!--                          </td>-->
<!--                        </tr>-->
	                </#list>
					</tbody>
				</table>
			</div>

			<script type="text/javascript">
                function createPackagingErrorInformation(sku){
                    var content = `<span>SKU:`+sku+`</span>
                                   <br/>
                                   <br/>
                                   <div>
                                       <h4 class="modal-title" id="selectDownloadHeadersModalLabel">错误信息：</h4>
                                       <div id="table-header" style="width:330px;">
                                           <label class="radio-inline" style="width:100px;">
                                               <input type="checkbox" name="questionFields" value="1">包材
                                           </label>
                                           <label class="radio-inline" style="width:100px;">
                                               <input type="checkbox" name="questionFields" value="2">搭配包材
                                           </label>
                                           <label class="radio-inline" style="width:100px;">
                                               <input type="checkbox" name="questionFields" value="3">包装图片
                                           </label>
                                       </div>
                                   </div>`;
                    var diglog = dialog({
                        title: '包装信息纠错',
                        width: 350,
                        height:100,
                        content:content,
                        okValue: '确定',
                        ok: function () {
                            var questionFields = [];
                            var questions = $("input[name='questionFields']:checked");
                            if (!questions || questions.length == 0){
                                layer.alert("未选择错误类型!","error");
                                return;
                            }
                            $.each(questions,function(index,item){
                                var val = $(item).val();
                                questionFields.push(val);
                            });
                            var questionStr = "";
                            for (var i = 0; i < questionFields.length; i++) {
                                var questionField = questionFields[i];
                                questionStr += questionField;
                                if (i != questionFields.length - 1) {
                                    questionStr += ",";
                                }
                            }
                            $.ajax({
                                url: CONTEXT_PATH + "/error/information/create",
                                type: "post",
                                data: {sku:sku,questionFields:questionStr},
                                success:function(response){
                                    if (response.status == '500') {
                                        customizeLayer(response.message, 'error');
                                    } else{
                                        alert('已创建纠错信息!');
                                    }
                                },
                                error:function () {
                                    layer.alert("系统异常，操作失败!",'error');
                                }
                            });
                        },
                        cancelValue: '取消',
                        cancel: function () {}
                    });
                    diglog.show();
                }
	    	</script>

            <div class="col-md-7">
                <table style="margin-left: 50px;">
                    <thead>
                        <tr>
                            <th colspan="2">
                                <h2 style="text-align: center;font-size: 25px;font-weight: bolder;">标准包装图片</h2>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <#if (apv.items[0].whSku.packImageList)!>
                            <tr>
                                <td class="col-md-10">
                                    <div class="skuImgMain"><img src="${apv.items[0].whSku.packImageList[0]}" onclick="enlarge(this)"></div>
                                </td>
                                <#--<td class="col-md-2">
                                    <ol class="skuImgOthersOl">
                                    <#list apv.whApvItems[0].whSku.packImageList as image>
                                        <div class="skuImgOthers"><img src="${image}" onerror="javascript:this.parentElement.style.display='none'"/></div>
                                    </#list>
                                    </ol>
                                </td>-->
                            </tr>
                            <#else>
                                <tr>
                                    <td class="col-md-10"></td>
                                    <td class="col-md-2"></td>
                                </tr>
                        </#if>
                    </tbody>
                </table>
            </div>
            <img id="enlarge" style='position:fixed;width:800px;height:800px;top:10%;right:15%;display:none;'/>
		</div>
	<#else>
		<!-- 为空操作 -->
        <#if (domain.errorMsg)!>
            <!-- 不为空操作 -->
            <div class="alert alert-danger">
                <strong id = "scan-error">
                    SKU：${domain.query.sku} ${domain.errorMsg}
                </strong>
            </div>
        <#else>
            <div class="alert alert-danger">
                <strong id = "scan-error">
                    SKU：${domain.query.sku} 的发货单不存在,单件合单扫描扫描只处理发货单状态为【单件合单】拣货完成  锁定的发货单！！
                </strong>
            </div>
        </#if>
	</#if>
    <script type="text/javascript">
        function enlarge(obj){
            var url = $(obj).attr("src");
            $("#enlarge").attr("src", url);
            $("#enlarge").show(300);
        }

        $("#enlarge").click(function() {$("#enlarge").hide(100);});

    </script>