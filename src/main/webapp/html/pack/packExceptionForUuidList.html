<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
    <#include "/common/include.html">
        <style type="text/css">
            #editVolumeAndWeightModal .modal-body input {
                width: 100%;
            }
            .control-label {
                margin-top: 2px;
            }
            .form-bordered .control-label {
                padding-top: 14px;
            }
            .form-horizontal .control-label {
                text-align: right;
            }
            .col-md-1 {
                padding-left: 10px;
                padding-right: 0px;
                width: 5%;
                font-size: 12px;
            }
            .col-md-2 {
                padding-left: 10px;
                padding-right: 0px;
                width: 7.5%;
                font-size: 12px;
            }
            .form-control {
                height: 30px;
                padding: 0px 4px;
                border: 1px solid #ddd;
                color: #000;
                font-size: 10px;
                font-weight: normal;
            }
            .form-bordered .form-group > div {
                padding: 4px 10px 0;
            }
            /*启用table滚动条*/
            .my-div-table{
                overflow-x: auto;
                overflow-y: auto;
                height: auto;
                width: auto;
                padding-bottom: 38px;
                /*width:1920px;*/
            }
            #box_info_table tr th{
                width:160px;
            }
            .form-control2 {
                height: 35px;
                width: 30px;
                padding: 0px 4px;
                border: 1px solid #ddd;
                color: #000;
                font-size: 16px;
                font-weight: normal;
            }
        </style>
</head>
<body>
<@header method="header" active="12010000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li><a href="#">出库管理</a></li>
                <li class="active">唯一码包装异常明细</li>
            </ul>
        </div>
    </div>

    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">
        <div class="row">
            <div class="col-md-12">
                <#assign query = domain.query>
                    <form action="${CONTEXT_PATH}apv/packExceptionUuidItem/search" class="form-horizontal form-bordered form-row-stripped"
                          method="post" modelAttribute="domain" name="packExceptionUuidItemForm" id="domain">
                        <!-- 分页信息 -->
                        <input id="page-no" type="hidden" style="display:none" name="page.pageNo" value="1">
                        <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">

                        <div class="form-body">
                            <div class="form-group">
                                <label class="control-label col-md-1">SKU：</label>
                                <div class="col-md-2">
                                    <input class="form-control" type="text" name="query.sku" placeholder="多个查询逗号分开" value="${query.sku }">
                                </div>

                                <label class="control-label col-md-1">唯一码：</label>
                                <div class="col-md-2">
                                    <input class="form-control" name="query.uuid" type="text" value="${query.uuid}">
                                </div>

                                <label class="control-label col-md-1">异常类型：</label>
                                <div class="col-md-2">
                                    <input class="form-control" type="text" name="query.exceptionType"  value="${query.exceptionType}">
                                </div>

                                <label class="control-label col-md-1">扫描人：</label>
                                <div class="col-md-2">
                                    <input class="form-control" type="text" name="query.scanUser"  value="${query.scanUser}">
                                </div>

                                <label class="control-label col-md-1">扫描时间：</label>
                                <div class="col-md-2">
                                    <input class="form-control Wdate" type="text" name="query.fromScanTime" placeholder="" readonly="readonly" value="${query.fromScanTime }"
                                           onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                                <label class="control-label col-md-1" style="font-weight: bolder">到</label>
                                <div class="col-md-2">
                                    <input class="form-control Wdate" type="text" name="query.toScanTime" placeholder="" readonly="readonly" value="${query.toScanTime }"
                                           onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="pull-left">
                            </div>
                            <div class="col-md-offset-12" style="text-align: right">
                                <@header method="auth" authCode="PACK_EXCEPTION_FOR_UUID_DOWNLOAD_DETAIL">
                                    <button type="button" class="btn btn-default" onclick="downloadCheck()">
                                        <i class="icon-download"></i> 导出明细
                                    </button>
                                </@header>
                                <button type="button" onclick="formReset(this)" class="btn btn-default">
                                    <i class="icon-refresh"></i> 重置
                                </button>
                                <button type="submit" class="btn blue">
                                    <i class="icon-search"></i> 查询
                                </button>
                            </div>
                        </div>
                    </form>
            </div>
        </div>
        <br/>
    </div>

    <div class="row">
        <div id="myFixedDiv" class="col-md-12">
            <table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
                <colgroup>
                    <col width="4%"/>
                    <col width="15%"/>
                    <col width="10%"/>
                    <col width="8%"/>
                    <col width="8%"/>
                    <col width="10%"/>
                    <col width="8%"/>
                    <col width="8%"/>
                    <col width="15%"/>
                </colgroup>
                <thead>
                    <tr>
                        <th><input type="checkbox" id="check-all" name="checkAll"> ID</th>
                        <th>唯一码</th>
                        <th>异常类型</th>
                        <th>扫描人</th>
                        <th>扫描界面</th>
                        <th>拣货任务</th>
                        <th>扫描时间</th>
                        <th>库内返架单<br/>操作时间</th>
                        <th>已绑定的发货单</th>
                    </tr>
                </thead>
            </table>
        </div>
        <div class="col-md-12 my-div-table" id="task-list-warp">
            <!-- 内容 -->
            <table style="background-color: #ffffff" class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                <colgroup>
                    <col width="4%"/>
                    <col width="15%"/>
                    <col width="10%"/>
                    <col width="8%"/>
                    <col width="8%"/>
                    <col width="10%"/>
                    <col width="8%"/>
                    <col width="8%"/>
                    <col width="15%"/>
                </colgroup>
                <tbody>
                <#list domain.packExceptionUuidItems as item>
                    <tr class="caret_tr_hidden" id="caret_tr_hidden_${item.id}">
                        <td>
                            <input type="checkbox" value="${item.id}" name="ids">
                            ${item.id}<br/>
                        </td>
                        <td>${item.uuid }</td>
                        <td>${util('enumName', 'com.estone.apv.enums.PackExceptionTypeEnum', item.exceptionType)}</td>
                        <td>${util('name', item.scanUser)}</td>
                        <td>${item.scanPage }</td>
                        <td>${item.pickingTackNo }</td>
                        <td>${item.scanTime }</td>
                        <td>
                            <#if item.returnLog?? >
                                ${item.returnLog.creationDate}
                            </#if>
                        </td>
                        <td>${item.bindingApvNo }</td>
                    </tr>
                </#list>
                </tbody>
            </table>
        </div>
    </div>

    <div id="fixed-bottom">
        <div id="pager"></div>
    </div>
</div>

<script type="text/javascript" src="${CONTEXT_PATH}js/pages/pms.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript">
    document.getElementById("fixedTab").style.width = $('#task-list').css('width');
    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";
    $("#pager").initPager({total: total, pageNo: pageNo, pageSize: pageSize});

    var heights = $("body").height();
    if(heights > 910) {
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }

    $.getJSON(CONTEXT_PATH + "system/saleusers/userByRole", function(json){
        if (json) {
            $("input[name='query.scanUser']").select2({
                data : json,
                placeholder : "扫描人",
                allowClear : true
            });
        } else {
            $("input[name='query.scanUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
        }
    });

    var exceptionTypeJson = ${domain.typeSelectJson};
    $("input[name='query.exceptionType']").select2({
        data : exceptionTypeJson,
        placeholder : "异常类型",
        allowClear : true
    });

    // 全选
    var checkAll = $("input[name='checkAll']");

    // 子选项
    var itemIds = $("input[name='ids']");
    checkAll.change(
        function () {
            itemIds.prop("checked", $(this).prop("checked"));
            itemIds.each(function () {
                var f = $(this).is(":checked");
                var checkClass = $(this).prop("class");
                $("." + checkClass).each(function () {
                    $(this).prop("checked", f);
                })
            })
        }
    );

    // 获取选中的入库单
    function getCheckedIds() {
        var checkedIds = $("input[name='ids']:checked");
        return checkedIds;
    }

    // 下载
    function downloadCheck() {
        var checkedDatas = getCheckedIds();
        if (checkedDatas.length == 0) {
            var param = $("#domain").serialize();
            if (total > 100000) {
                layer.alert("导出数据不能超过100000条！", "error");
                return;
            }
            window.open(CONTEXT_PATH + "apv/packExceptionUuidItem/download?" + param);
        } else {
            downloadCheckByPost();
        }
    }


    // 超过500条不能用GET请求
    function downloadCheckByPost() {
        var checkedDatas = getCheckedIds();
        var ids = "";
        for (var i = 0; i < checkedDatas.length; i++) {
            var check = checkedDatas[i];
            var id = $(check).val();
            ids += id;
            if (i != checkedDatas.length - 1) {
                ids += ",";
            }
        }
        var url = CONTEXT_PATH + "apv/packExceptionUuidItem/download";
        var tempForm = document.createElement("form");
        tempForm.id = "tempForm";
        tempForm.method = "post";
        tempForm.action = url;
        tempForm.target = "blank";
        var hideInput = document.createElement("input");
        hideInput.type = "hidden";
        hideInput.name = "ids";
        hideInput.value = ids;
        tempForm.appendChild(hideInput);
        if (tempForm.attachEvent) {  // IE
            tempForm.attachEvent("onsubmit", function () {
                window.open('about:blank', 'blank');
            });
        } else if (tempForm.addEventListener) {  // DOM Level 2 standard
            tempForm.addEventListener("onsubmit", function () {
                window.open('about:blank', 'blank');
            });
        }
        document.body.appendChild(tempForm);
        if (document.createEvent) { // DOM Level 2 standard
            evt = document.createEvent("MouseEvents");
            evt.initMouseEvent("submit", true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
            tempForm.dispatchEvent(evt);
        } else if (tempForm.fireEvent) { // IE
            tempForm.fireEvent('onsubmit');
        }
        //必须手动的触发
        tempForm.submit();
        document.body.removeChild(tempForm);
    }

</script>
</body>
</html>