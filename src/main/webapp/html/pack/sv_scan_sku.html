<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		#pcak-info-table {
			width: 100%;
			margin-top: 10px;
		}
		#pcak-info-table #btn td{
			border: none;
			background-color: transparent;
			padding-left: 0px;
		}
		#pcak-info-table #apvNo td {
			height: 36px;
			text-align: center;
		}
		#pcak-info-table .br td {
			height: 15px;
			border: none;
			background-color: transparent;
		}
		#pcak-info-table td {
			border: 1px solid #bcbcbc;
			width: 140px;
			height: 50px;
			padding-left: 10px;
			background-color: #F9F9F9;
			font-size: 18px;
		}
		#pcak-info-table td.form-label {
			text-align: center;
			background-color: #d7d7d7;
			font-weight: bold;
			font-size: 14px;
		}
		#pcak-info-table td.red {
			color: red;
		}
		.size-blod {
			font-weight: bold;
			font-size: 30px;
		}
	</style>
</head>
<body>
 
	<#assign whApv = domain.whApv >
	<#if (whApv)! && (domain.apvHistoryStatus == '12')>
	   <div>
		<div id="check_scan_content" class="border-gray p5">
	
			<div>
				<input type="hidden" name="apvId" value="${domain.whApv.id }" />
				<input type="hidden" name="apvNo" value="${domain.whApv.apvNo }" />
				<input type="hidden" name="shipService" value="${domain.whApv.shipService }" />
				<input type="hidden" name="totalSaleQuantity" value="${domain.whApv.totalSaleQuantity }" />
				<input type="hidden" name="logisticsType" value="${domain.whApv.logisticsType }" />
				<input type="hidden" name="logisticsCompany" value="${domain.whApv.logisticsCompany }" />
				<input type="hidden" name="waybillSize" value="${domain.waybillSize }" />
				<input type="hidden" name="apvHistoryStatus" value="12" />
				<#if whApv.buyerCheckoutList?seq_contains('GPSR')>
					<input type="hidden" name="gpsrPlatform" value="${whApv.platform}"/>
				</#if>

				<#if (domain.apvWaybill)!>
					<input type="hidden" name="apvWaybill.id" value="${domain.apvWaybill.id}"/>
					<input type="hidden" name="apvWaybill.printUrl" value="${domain.apvWaybill.printUrl}"/>
					<input type="hidden" name="apvWaybill.dataJson" value="${domain.apvWaybill.dataJson}"/>
					<input type="hidden" name="cainiaoPrint" value="${domain.cainiaoPrint}"/>
				</#if>

				<table class="table table-condensed">
					<tbody>
						<tr style="height: 30px"></tr>
						<#if whApv.buyerCheckoutList?seq_contains('GPSR')>
							<tr class="br"><td colspan="5"><span style="color: red;font-size: 30px;font-weight: bold; margin-left: 20px">GPSR包装</span> </td></tr>
						</#if>
						<#if whApv.buyerCheckoutList?seq_contains('EUR')>
							<tr style="background-color: #ffdf25;">
								<td style="border-right: none;font-weight:900;font-size: 30px;text-align: center;">
                                    <div style="float: left;line-height: 100px;margin: 0px 0px 0px 10px;">
                                        ${util('enumName', 'com.estone.apv.enums.ApvTaxTypeEnum', 'EUR')}标签
                                    </div>
									<div>
										<div style="text-align: center;margin-top: 0mm;float: left;margin: 30px 20px;">
											<div style="float: left;">
												<div style="font-weight: 800;border: 1px solid #000000;float: left;width: 70px;">EC</div>
												<div style="font-weight: 800;border: 1px solid #000000;float: left;width: 70px;border-left: none;">REP</div>
											</div>
											<div style="float: left;margin-left: 2mm;">
												<!-- MAC90178-BK 10px会越界-->
												<div style="font-weight: 600;font-size: 12px; text-align: left;">SHUNSHUN GmbH</div>
												<div style="font-weight: 600;font-size: 12px;text-align: left;">Römeräcker 9 Z2021,76351</div>
												<div style="font-weight: 600;font-size: 11px; text-align: left;">Linkenheim-Hochstetten,Germany</div>
											</div>
										</div>
									</div>
								</td>
							</tr>
							<tr style="height: 30px"></tr>
						</#if>
						<tr>
							<td width="60%" class="error">
								<h4>待检区 （发货单号：${domain.whApv.apvNo }）</h4>
                                <#if whApv.buyerCheckoutList?seq_contains('RFP')>
                                    <span style="background-color: red;border-right: none;font-weight:900; font-size: 30px;width: 200px;line-height: 80px;">
                                        ${util('enumName', 'com.estone.apv.enums.ApvTaxTypeEnum', 'RFP')}
                                    </span>
                                </#if>
								<table class="table table-condensed table-bordered notice uncheck ">
									<tbody>
										<tr>
											<!-- <th align="center">图片</th> -->
											<th align="center">应发数量</th>
											<th align="center">未扫描数量</th>
											<th align="center">SKU</th>
											<th align="center">库位号</th>
											<th align="center">包材</th>
											<th align="center">搭配包材</th>
											<th align="center">是否带原包装</th>
											<th align="center">包装备注</th>
											<th align="center" style="width: 100px">商品名</th>
										</tr>
										<#list whApv.whApvItems as item>
											<tr>
												<#assign sku = item.sku?upper_case?trim >
												<td align="center" style="font-size: 24px;font-weight: 900;">${item.saleQuantity }</td>
												<td align="center" class="check_quantity" style="font-size: 24px;font-weight: 900;" id="check_quantity_${sku}">${item.saleQuantity }</td>
												<td class="check_sku" id="check_sku_${sku}">
                                                    ${(item.sku)?upper_case }
                                                    <br/>
                                                    <a class="btn yellow" href="javascript:void(0);" onclick="createPackagingErrorInformation('${item.sku}')">包装信息纠错</a>
                                                </td>
												<td>${item.whSku.locationNumber }</td>
												<td><span style="font-size: 24px;"><b>${item.whSku.packagingName }</b></span></td>
												<#if (item.whSku.matchMaterialsName)?? && item.whSku.matchMaterialsName?index_of("充气条") != -1 >
													<td class="size-blod" style="font-size: 25px;background-color: red;color: white;">${item.whSku.matchMaterialsName}</td>
												<#elseif (item.whSku.matchMaterialsName)?? && item.whSku.matchMaterialsName?index_of("珍珠棉") != -1>
													<td class="size-blod" style="font-size: 25px;background-color: #009DD9;color: white;">${item.whSku.matchMaterialsName}</td>
												<#elseif (item.whSku.matchMaterialsName)?? && item.whSku.matchMaterialsName?index_of("充气袋") != -1>
													<td class="size-blod" style="font-size: 25px;background-color: #FFA500FF;color: white;">${item.whSku.matchMaterialsName}</td>
												<#else>
													<td class="size-blod" style="font-size: 25px;">${item.whSku.matchMaterialsName}</td>
												</#if>
												<td style="font-size: 24px;font-weight: 900;">
													<#list domain.whSkuWithPmsInfos as skuWithPms>
														<#if skuWithPms.sku == item.sku>
															<#if (skuWithPms.isOriginalPackage)! && skuWithPms.isOriginalPackage == true>
																需要原包装
															<#else>
																不需要原包装
															</#if>
															<br><span style="color: black">包装备注：</span>${skuWithPms.packingMemo }
														</#if>
													</#list>
												</td>
												<td style="font-size: 24px;font-weight: 900;">
													<#list domain.whSkuWithPmsInfos as skuWithPms>
														<#if skuWithPms.sku == item.sku>
															${skuWithPms.skuLabelName }
															<#if skuWithPms.tag?? && skuWithPms.tag?index_of("磁性")!=-1>
																<br>${skuWithPms.tag }
															</#if>
														</#if>
													</#list>
													<#if domain.skuTagMap?? && domain.skuTagMap[item.sku]??>
														<span style="margin-left: 20px;">,${domain.skuTagMap[item.sku]}</span>
													</#if>
												</td>
												<td style="width: 100px">${item.whSku.name }</td>
											</tr>
										</#list>
									</tbody>
								</table>
							</td>
							<td class="success" style="display: none;">
								<h4>已检查</h4>
								<table class="table table-condensed table-bordered notice checked">
									<tbody>
										<tr>
											<th>仓库</th>
											<th>SKU</th>
											<th>库位号</th>
											<th>数量</th>
										</tr>
										<#list whApv.whApvItems as item>
											<tr>
												<td>${util("wh",item.whSku.warehouseId)}</td>
												<td>${item.sku }</td>
												<td>${item.whSku.locationNumber }</td>
												<td><input readonly="readonly" size="5" name="checked_quantity_${item.sku?upper_case }" value="0"></td>
											</tr>
										</#list>
									</tbody>
								</table>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		 </div>
		</div>
	</#if>
	<#if (whApv)! && (domain.apvHistoryStatus == '10')>
		<#assign apv = domain.whApv >
		<div>
   		<div id="check_scan_content" class="border-gray p5">
			<div id="scan_datas" class="col-md-6">
			<div style="border-bottom: 1px #CCC dotted; height: 700px;" id="scan-apv-${apv.id }" class="scan_success">
				<div class="scan_success_sub" style="height: 700px">
					<input type="hidden" name="apvNo" value="${apv.apvNo }"/>
					<input type="hidden" name="waybillSize" value="${domain.waybillSize }" />
					<input type="hidden" name="shipService" value="${apv.shipService }"/>
					<input  type="hidden"  class="form-control input-small input-inline" size="8" value="${apv.id}" name="apvId" readonly="readonly">
					<input type="hidden" name="apvHistoryStatus" value="10" />
					<#if apv.buyerCheckoutList?seq_contains('GPSR')>
						<input type="hidden" name="gpsrPlatform" value="${apv.platform}"/>
					</#if>
					<#if (domain.apvWaybill)!>
						<!-- 不为空操作 -->
						<input type="hidden" name="apvWaybill.id" value="${domain.apvWaybill.id}"/>
						<input type="hidden" name="apvWaybill.printUrl" value="${domain.apvWaybill.printUrl}"/>
					</#if>
					<table id="pcak-info-table">
						<tbody>
		                  <#list apv.whApvItems as item>
							  <tr class="br"><td colspan="5"></td></tr>
							  <#if whApv.buyerCheckoutList?seq_contains('GPSR')>
								  <tr class="br"><td colspan="5"><span style="color: red;font-size: 30px;font-weight: bold; margin-left: 20px">GPSR包装</span> </td></tr>
							  </#if>
							  <tr id="apvNo">
								  <input type="hidden" name="logisticsType" value="${apv.logisticsType }" />
								  <td class="form-label">发货单号</td>
								  <#if apv.buyerCheckoutList?seq_contains('RFP')>
									  <td colspan="2" class="size-blod">${apv.apvNo}</td>
									  <td class="fire-apv-td">
                                  <span style="background-color: red;border-right: none;font-weight:900; font-size: 30px;width: 200px;line-height: 80px;">
                                      ${util('enumName', 'com.estone.apv.enums.ApvTaxTypeEnum', 'RFP')}
                                  </span>
									  </td>
								  <#else>
									  <td colspan="3" class="size-blod">${apv.apvNo}</td>
								  </#if>
								  <td>
									  <a class="btn yellow" href="javascript:void(0);" onclick="revoke(${apv.id}, '${apv.apvNo }')">撤销 <i class="icon-undo"></i></a>
								  </td>
							  </tr>
							  <tr class="br"><td colspan="4"></td></tr>

							  <#if apv.buyerCheckoutList?seq_contains('EUR')>
								  <tr>
									  <td style="background-color: #ffdf25;border-right: none;font-weight:900; font-size: 30px;width: 200px;line-height: 80px;" class="fire-apv-td">
										  ${util('enumName', 'com.estone.apv.enums.ApvTaxTypeEnum', 'EUR')}标签
									  </td>
									  <td style="background-color: #ffdf25;border-left: none;" colspan="4">
										  <div>
											  <div style="text-align: center;float: left;">
												  <div style="float: left;margin-top: 5px;">
													  <div style="font-weight: 800;border: 1px solid #000000;float: left;width: 60px;line-height: 40px;">EC</div>
													  <div style="font-weight: 800;border: 1px solid #000000;float: left;width: 60px;border-left: none;line-height: 40px;">REP</div>
												  </div>
												  <div style="float: left;margin-left: 2mm;">
													  <!-- MAC90178-BK 10px会越界-->
													  <div style="font-weight: 600;font-size: 12px; text-align: left;">SHUNSHUN GmbH</div>
													  <div style="font-weight: 600;font-size: 12px;text-align: left;">Römeräcker 9 Z2021,76351</div>
													  <div style="font-weight: 600;font-size: 11px; text-align: left;">Linkenheim-Hochstetten,Germany</div>
												  </div>
											  </div>
										  </div>
									  </td>
								  </tr>
								  <tr class="br"><td colspan="5"></td></tr>
							  </#if>
							  <tr>
								  <td class="form-label">SKU</td>
								  <td class="size-blod check_sku" id="check_sku_${sku}">${item.sku}</td>
								  <td class="form-label">商品名称</td>
								  <td colspan="2">${item.whSku.name}</td>
							  </tr>
							  <tr>
								  <td class="form-label">袋子</td>
								  <td class="size-blod">${item.whSku.packagingName}</td>
								  <td class="form-label">辅助耗材</td>
								  <#--<td colspan="2" class="size-blod" style="font-size: 25px">${item.whSku.matchMaterialsName}</td>-->
								  <#if (item.whSku.matchMaterialsName)?? && item.whSku.matchMaterialsName?index_of("充气条") != -1 >
									  <td colspan="2" class="size-blod" style="font-size: 25px;background-color: red;color: white;">${item.whSku.matchMaterialsName}</td>
								  <#elseif (item.whSku.matchMaterialsName)?? && item.whSku.matchMaterialsName?index_of("珍珠棉") != -1>
									  <td colspan="2" class="size-blod" style="font-size: 25px;background-color: #009DD9;color: white;">${item.whSku.matchMaterialsName}</td>
								  <#elseif (item.whSku.matchMaterialsName)?? && item.whSku.matchMaterialsName?index_of("充气袋") != -1>
									  <td colspan="2" class="size-blod" style="font-size: 25px;background-color: #FFA500FF;color: white;">${item.whSku.matchMaterialsName}</td>
								  <#else>
									  <td colspan="2" class="size-blod" style="font-size: 25px;">${item.whSku.matchMaterialsName}</td>
								  </#if>
							  </tr>
							  <tr>
								  <td class="form-label">是否需要原包装</td>
								  <td colspan="4" class="red">
									  <#list domain.whSkuWithPmsInfos as skuWithPms>
										  <#if skuWithPms.sku == item.sku>
											  <#if (skuWithPms.isOriginalPackage)! && skuWithPms.isOriginalPackage == true>
												  需要原包装
											  <#else>
												  不需要原包装
											  </#if>
											  <br><span style="color: black">包装备注：</span>${skuWithPms.packingMemo }
										  </#if>
									  </#list>
								  </td>
							  </tr>
							  <tr>
								  <td class="form-label">商品特性</td>
								  <td colspan="4" class="red size-blod">
									  <#list domain.whSkuWithPmsInfos as skuWithPms>
										  <#if skuWithPms.sku == item.sku>
											  ${skuWithPms.skuLabelName }
											  <#if skuWithPms.tag?? && skuWithPms.tag?index_of("磁性")!=-1>
												  <br>${skuWithPms.tag }
											  </#if>
										  </#if>
									  </#list>
									  <#if domain.skuTagMap?? && domain.skuTagMap[item.sku]??>
										  <span style="margin-left: 20px;">,${domain.skuTagMap[item.sku]}</span>
									  </#if>
								  </td>
							  </tr>
							  <tr>
								  <td class="form-label">耗材图片</td>
								  <td colspan="4">
									  <#if (item.whSku.matchMaterialPictureList)!>
										  <#list item.whSku.matchMaterialPictureList as image>
											  <div style="float: left;margin-left: 10px;">
												  <img alt="产品缩略图" border="0" width="80px" height="74px" src="${image}"
													   onclick="enlarge(this)"
													   onerror="javascript:this.parentElement.style.display='none'"/>
											  </div>
										  </#list>
									  </#if>
								  </td>
							  </tr>
							  <tr>
								  <td colspan="5">
									  <a class="btn yellow" href="javascript:void(0);" onclick="createPackagingErrorInformation('${item.sku}')">包装信息纠错</a>
								  </td>
							  </tr>
		                </#list>
						</tbody>
					</table>
				</div>

			</div>
			</div>
		</div>
		<div>
	</#if>


	<#if (whApv)!>
	<#else>
	<div>
		<#if (domain.errorMsg)!>
			<!-- 不为空操作 -->
			<div id="check_scan_content" class="border-gray p5">
				<div id = "scan-error" style="font-size:15px ;font-weight:bold" class="error scan_error">产品:${param['sku'] }  ${domain.errorMsg}</div>
			</div>
		<#else>
			<div id="check_scan_content" class="border-gray p5">
				<div id = "scan-error" style="font-size:15px ;font-weight:bold" class="error scan_error">产品:${param['sku'] }  不存在或者不在[单品合单]状态中!</div>
			</div>
		</#if>
	</div>
	</#if>
  </body>
</html>