<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		#task-list td {
			vertical-align:middle;
		}
		.scan-a {
			width: 100px;
			height: 100px;
			line-height: 85px;
			text-align: center;
			margin-bottom: 50px;
		}
		.form-control{
			height:42px;
		}
		.my-success {
			background-color: #19be6b;
		}
		.my-info {
			background-color: #2db7f5;
		}
		.my-current {
			background-color: red;
		}
		.my-warning {
			background-color: #ff9900;
		}
		.my-disabled {
			background-color: #c5c8ce;
		}
	</style>
</head>
<body>
	<@header method="header" active="12040000"><#include "/ftl/header.ftl"></@header>
	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">发货管理</a></li>
					<li class="active">退换货播种</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<div class="row">
			<div class="form-body">
				<div class="form-group">
					<input type="hidden" id="scan-count" />
					<input id="grid-location" value="1" type="hidden">
					<label class="control-label col-md-1" style="text-align: right; line-height: 42px">拣货任务号</label>
					<div class="col-md-2">
						<input type="text" class="form-control input-medium" name="rfoTaskNo" id="rfoTaskNo" onkeypress="if(event.keyCode==13) { inputnext(this); return false;}"tabindex="4">
					</div>
					<label class="control-label col-md-1" style="text-align: right; line-height: 42px">SKU/唯一码</label>
					<div class="col-md-2">
						<input type="text" class="form-control input-medium" name="sku" id="sku" onkeypress="if(event.keyCode==13) { inputsku(this); return false;}">
					</div>
					<div class="col-md-5"  style="">
						<button type="button" class="btn red" style="vertical-align: text-bottom;" onclick="finish_1()">
							<i class="glyphicon glyphicon-print"></i> 播种完成
						</button>
						<div style="display: inline-block;">
						<span style="color:blue;font-size:31px;line-height: 20px; margin-left: 8px;">成功:<b id="panel-title">0</b></span>
						<span style="color:red;font-size:31px;line-height: 20px;margin-left: 8px">计数:<b id="panel-title-piece">0</b></span>
							<div style="clear:both;line-height: 30px">
								<span style="color:blue;font-size:18px;line-height: 10px; margin-left: 8px;">已拣总数:<b id="pick-title">0</b></span>
								<span style="color:blue;font-size:18px;line-height: 10px; margin-left: 8px">已播总数:<b id="sowing-title">0</b></span>
								<span style="color:red;font-size:18px;line-height: 10px; margin-left: 8px">差异数:<b id="pick-sowing-title">0</b></span>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div style="margin: 35px 0 15px;border-bottom: 1px solid #ccc"></div>

			<div id="check-area">
				<div class="panel-header" style="overflow: hidden;">

				</div>
				<div style="height: 441px;">
				  <div id="scan_datas">

				  </div>
				</div>
			</div>

			<div>
				<iframe style="width:260px;height:270px;border: 0 none;" name="boxNoAndRfoNoPrintHtml" id="boxNoAndRfoNoPrintHtml"></iframe>
			</div>

		</div><!-- end row -->

		<div id="info" onclick="{$('#info').hide();}" style='position:absolute;font-size:120px;text-align:center;width:500px;height:500px;line-height:500px;top:180px;left:50%;background-color:#7bbbd6;display:none;'></div>
	</div>
	<#include "/common/footer.html">
	</div>
	<object width="0" height="0" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" id="LODOP_OB">
		<embed width="0" height="0" type="application/x-print-lodop" id="LODOP_EM">
	</object>

	<!-- 	打印插件 -->
	<script type="text/javascript" src="${CONTEXT_PATH }js/LodopFuncs.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/jquery.sound.js?v=${.now?datetime}"></script>


	<script type="text/javascript">
		$(function () {
			$('[data-toggle="popover"]').popover()
		})

		var lessGridNoStr = "";

		var gridDiffBox = false;
		var cacheKey = "rfo_grid_success";

		//计件缓存key
		var pieceCacheKey = "rfo_grid_piece";

		$(document).ready(function () {
			input_init();
			var storage = new WebStorageCache();
			if (storage.get(cacheKey)) {
				lastSuc = storage.get(cacheKey);
				$('#panel-title').text(lastSuc);
			}

			if (storage.get(pieceCacheKey)) {
				suc = storage.get(pieceCacheKey);
				$('#panel-title-piece').text(suc);
			}


			$('#rfoTaskNo').focus();

			// 记录位置
			var locationCacheKey = "rfo_grid_location_cache";
			if (storage.get(locationCacheKey)) {
				$("#grid-location").val(storage.get(locationCacheKey));
			}
			$("#grid-location").change(function () {
				storage.set(locationCacheKey, this.value);
			});

		});

		// 初始化
		function input_init() {
			$('#rfoTaskNo').val('');
			$('#sku').val('');
		}

		// 输入单号
		function inputnext(obj) {
			$("#info").hide();
			if (!obj.value || obj.value.replace(/\s/g, '') == '') {
				layer.alert("请输入拣货任务号!");
				return;
			}

			var r = $.ajax({

				url: CONTEXT_PATH + "rfoGrid/grid/taskNo",
				data: {rfoTaskNo: $.trim($("#rfoTaskNo").val())},
				timeout: 100000,
				beforeSend: function () {
					App.blockUI();
				},
				success: function (response) {


					App.unblockUI();

					var responseHtml = $(response).find("#scan_datas").html();

					$("#scan_datas").html(responseHtml);

					var errorMsg = $(response).find("#scan_datas").find("#scan-error").val();
					if (errorMsg) {
						$('#rfoTaskNo').val('');
						$('#rfoTaskNo').select().focus();
						layer.alert(errorMsg);
					} else {
						var scanNo = $.trim($("#rfoTaskNo").val());
						if (scanNo.indexOf("BZCY") != -1) {
							gridDiffBox = true;
						}
						$('#sku').select().focus();

						lessGridNoStr = $(response).find("#lessGridNoStr").val();

						//记录扫描完成的apv数量
						$("#scan-count").val($(response).find("#scan-success-num").val());

						$('[data-toggle="popover"]').popover();

						//已拣总数
						var pick_num = $(response).find("#scan-pickingQuantity").val();
						$('#pick-title').text(pick_num);

						//已播总数
						var sowing_num = $(response).find("#scan-sowingQuantity").val();
						$('#sowing-title').text(sowing_num);

						//差异数
						$('#pick-sowing-title').text(pick_num - sowing_num);
					}

				},
				error: function () {
					App.unblockUI();
					layer.alert('扫描失败，请重新扫描/');
				}
			});
		}

		// 统计扫描成功数量
		function calsf() {

			var storage = new WebStorageCache();

			var lastSuc = 0;
			if (storage.get(cacheKey)) {
				lastSuc = storage.get(cacheKey);
			}

			var suc = 1 + lastSuc;

			storage.set(cacheKey, suc, {exp: 5 * 60 * 60});
			$('#panel-title').text(suc);

		}

		function calsfPiece() {

			var storage = new WebStorageCache();

			var lastSuc = 0;
			if (storage.get(pieceCacheKey)) {
				lastSuc = storage.get(pieceCacheKey);
			}

			var suc = 1 + lastSuc;

			storage.set(pieceCacheKey, suc, {exp: 5 * 60 * 60});

			$('#panel-title-piece').text(suc);
		}


		// 撤销减数量
		function subtractsf() {

			var storage = new WebStorageCache();

			var lastSuc = 0;
			if (storage.get(cacheKey)) {

				var lastSuc = storage.get(cacheKey);

				var suc = lastSuc - 1;

				if (suc < 0) {
					suc = 0;
				}

				storage.set(cacheKey, suc, {exp: 5 * 60 * 60});
				$('#panel-title').text(suc);
			}
		}

		//获取打印和已经扫描的数据
		function inputsku() {
			$("#info").hide();
			var realSku = $.trim($('#sku').val());
			var uuid = realSku;

			//防止查询大数据
			if (!realSku || realSku.trim() == '') {
				layer.alert("请输入有效sku!");
				return;
			}


			// 任务号
			var rfoTaskNo = $.trim($("#rfoTaskNo").val());

			if (!(realSku.indexOf("=") == -1)) {
				var realSku = realSku.split('=')[0];
				$('#sku').val(realSku);
			}


			//核对提交数据
			var r = $.ajax({
				type: "get",
				async: false,
				url: CONTEXT_PATH + "rfoGrid/grid/sku",
				data: {taskNo: rfoTaskNo, sku: realSku, uuid: uuid},
				timeout: 100000,
				beforeSend: function () {
					App.blockUI(null, null, 10000);
				},
				success: function (response) {
					App.unblockUI();
					var responseHtml = $(response).find("#scan_datas").html();
					var errorMsg = $(response).find("#scan_datas").find("#scan-error").val();
					if (errorMsg) {
						audioPlay("error");
						layer.open({
							content: errorMsg,
							yes: function (index, layero) {
								$('#sku').val("");
								$('#sku').focus();
								layer.close(index);
							},
							cancel: function () {
								$('#sku').val("");
								$('#sku').focus();
							}
						});
						return;
					}

					var apvId = $(response).find("#scan_datas").find("#scan-apvId").val();

					var number = $(response).find("#scan_datas").find("#scan-number").val();

					var status = $(response).find("#scan_datas").find("#scan-status").val();
					lessGridNoStr = $(response).find("#scan_datas").find("#lessGridNoStr").val();

					$("#scan_datas").html(responseHtml);
					$('[data-toggle="popover"]').popover();

					if (apvId) {

						// 显示订单
						$("#apv-grid-" + number).find(".grid-apv-id").text(apvId);
						$("#info").html(number).show();
						// $("#info").html(number).show(300).delay(3000).hide(300);

						$("#scan-record").html("<h1 style=\"color:blue;font-size:31px;\">箱号：<b>" + number + "</b></h1>");

						calsfPiece();

						$('#sku').val("");

						$('#sku').focus();

						// 完成，打印面单
						if (status == 2) {

							//扫描完成的apv
							var scanCount = $("#scan-count").val();
							$("#scan-count").val(parseInt(scanCount) + 1);

							// 延迟播放
							setTimeout(sayOk, 700);

							// 成功
							calsf();


						}
						audioPlay(number);

					} else {

						audioPlay("error");

						var msg = "SKU不存在或已检查完毕";
						toastr.options = {
							closeButton: true,
							debug: false,
							timeOut: "3000",
							positionClass: 'toast-top-center',
							onclick: null
						};
						$('#sku').val("");
						$('#sku').focus();
						layer.alert(msg);


					}
					//已播总数
					var sowing_num = $("#sowing-title").text();
					var sowing_num_after = (sowing_num * 1) + 1;
					$('#sowing-title').text(sowing_num_after);

					//差异数
					var pick_num = $("#pick-title").text();
					var pick_num_after = (pick_num * 1) - (sowing_num * 1) - 1;
					$('#pick-sowing-title').text(pick_num_after);

				},
				error: function () {
					App.unblockUI();
					layer.alert('扫描失败，请重新扫描');
				}
			});
		}

		function sayOk() {
			audioPlay("success");
		}

		function removeDisabled() {
			$("#sku").removeAttr("disabled");
		}

		// 自动获取焦点
		$('#scan-area,#check-area,#print-area').click(function (e) {
			if (e.target.tagName != 'SELECT' && e.target.tagName != 'OPTION' && e.target.id != 'rfoTaskNo') {
				$('#sku').select().focus();
			}
		});

		// 播种完成第一步
		function finish_1() {
			var gridStatus = $('#gridStatus').val();
			if (gridStatus == 2) {
				layer.alert("拣货任务已播种完成！", {closeBtn: 0}, function (index) {
					layer.close(index);
					window.location.reload();
				});
				return;
			}
			$("#info").hide();
			var boxCayi = '';
			if (lessGridNoStr != null && lessGridNoStr != '') {
				var diglog = dialog({
					title: '绑定播种差异周转筐',
					width: 350,
					height: 100,
					url: CONTEXT_PATH + "rfoGrid/binding/gridDiff",
					okValue: '确定',
					ok: function () {
						var exportWindow = $(this.iframeNode.contentWindow.document.body);
						var submitForm = exportWindow.find("#submit-form");
						boxCayi = $(submitForm).find("#grid-diff-box").val();

						if (!boxCayi || boxCayi == '' || boxCayi.indexOf('BZCY') == -1) {
							layer.alert("请绑定播种差异周转筐!");
							$(submitForm).find("#grid-diff-box").val('');
							return false;
						}

						finish_version2(boxCayi, lessGridNoStr);
						setTimeout(function () {
							diglog.close().remove();
						}, 100);
					},
				});
				diglog.show();
			} else {
				finish_version2('', '');
			}
		}

		function finish_version2(boxCayi, lessGridNoStr) {
			var taskNo = $("#rfoTaskNo").val();
			var taskType = $('#taskType').val();
			v = $.trim(taskNo);
			if (!taskNo) {
				layer.alert("请扫描拣货任务/周转筐!");
				return;
			}
			if (lessGridNoStr != null && lessGridNoStr != '') {
				if (boxCayi != '' && !confirm('请将播种差异的格子：' + lessGridNoStr + '货物放入播种差异周转筐' + boxCayi + '中')) {
					return false;
				}

				bindingStockout_version2(boxCayi, taskNo);
			} else {
				// 普通完成
				$.getJSON(CONTEXT_PATH + "rfoGrid/complete", {"taskNo": taskNo}, function (data) {
					setTimeout(function () {
						$("#submit-form").find("input[name='selectedExportFields']").prop("checked", true);
					}, 300);
					layer.alert("播种完成！", {closeBtn: 0}, function (index) {
						layer.close(index);
						window.location.reload();
					});
				})
			}
		}

		function bindingStockout_version2(boxCayi, taskNo) {
			var result;
			var differ = $("#pick-sowing-title").text();
			$.ajax({
				url: CONTEXT_PATH + "rfoGrid/binding/caYiBox",
				type: 'post',
				async: false,//使用同步的方式,true为异步方式
				data: {"boxCaYi": boxCayi, "taskNo": taskNo, "differ": differ, "lessGridNoStr": lessGridNoStr},
				success: function (data) {
					if (data.status == 200) {
						result = "sussecc";
						layer.alert("绑定成功！", {closeBtn: 0}, function (index) {
							layer.close(index);
							window.location.reload();
						});
					} else {
						layer.alert(data.message);
						return false;
					}
				},
				fail: function () {
				}
			});

			if (result && result == 'sussecc') {
				return true;
			} else {
				return false;
			}
		}

		function gridFinishForAjaxGet(url) {
			$.ajax({
				url: url,
				type: 'GET',
				async: false,//使用同步的方式,true为异步方式
				data: {},
				success: function (data) {
					if (data.status == 200) {
						layer.alert("操作成功！", {closeBtn: 0}, function (index) {
							layer.close(index);
							window.location.reload();
						});
					} else {
						layer.alert(data.message);
						return false;
					}
				},
				fail: function () {
				}
			});
		}

		function printBoxNoAndRfoNo() {
			var taskNo = $("#rfoTaskNo").val();
			window.open(CONTEXT_PATH + "rfoGrid/printBoxNoAndRfoNo?serialNumber=" + taskNo);
		}
	</script>
</body>
</html>