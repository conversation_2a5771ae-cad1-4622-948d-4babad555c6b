<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <#include "/common/include.html">
    <link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
    <style type="text/css">
        .modal-body input {
            width: 240px;
        }
        .btn-xs {
            padding: 0px 5px;
        }

        #task-list td {
            vertical-align: middle;
        }
        #task-list thead>tr>th {
            vertical-align: middle;
        }
        #fixedDiv thead>tr>th {
            vertical-align: middle;
        }

    </style>
</head>

<body>
<@header method="header" active="11080000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li>
                    <a href="#">库存管理</a>
                </li>
                <li class="active">内购拣货</li>
            </ul>
        </div>
    </div>
    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">
        <div class="row">
            <div class="col-md-12">
                <#assign query=domain.query>
                <form action="${CONTEXT_PATH}spoTask/search"
                      class="form-horizontal form-bordered form-row-stripped" method="post" modelAttribute="domain"
                      name="spoTaskForm" id="domain">
                    <!-- 分页信息 -->
                    <input id="page-no" type="hidden" name="page.pageNo" value="1">
                    <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
                    <div class="form-body">
                        <div class="form-group">
                            <label class="control-label col-md-1">创建时间:</label>
                            <div class="col-md-2">
                                <div class="input-group">
                                    <input class="form-control Wdate" type="text" name="query.fromCreatedDate" placeholder="" readonly="readonly" value="${query.fromCreatedDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    <span class="input-group-addon">到</span>
                                    <input class="form-control Wdate" type="text" name="query.toCreatedDate" placeholder="" readonly="readonly" value="${query.toCreatedDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>
                            <label class="control-label col-md-1">内购订单号:</label>
                            <div class="col-md-2">
                                <input class="form-control" type="text" name="query.spoNo" placeholder="多个逗号分开" placeholder="" value="${query.spoNo}">
                            </div>
                            <label class="control-label col-md-1">任务号:</label>
                            <div class="col-md-2">
                                <input class="form-control" type="text" name="query.taskNo" placeholder="多个逗号分开" placeholder="" value="${query.taskNo}">
                            </div>
                            <label class="control-label col-md-1">领取人:</label>
                            <div class="col-md-2">
                                <input class="form-control" name="query.receivePerson" type="text" value="${query.receivePerson}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-md-1">完成时间:</label>
                            <div class="col-md-2">
                                <div class="input-group">
                                    <input class="form-control Wdate" type="text" name="query.fromPickingEndDate" placeholder="" readonly="readonly" value="${query.fromPickingEndDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    <span class="input-group-addon">到</span>
                                    <input class="form-control Wdate" type="text" name="query.toPickingEndDate" placeholder="" readonly="readonly" value="${query.toPickingEndDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>
                            <label class="control-label col-md-1">SKU:</label>
                            <div class="col-md-2">
                                <input class="form-control" type="text" name="query.sku" placeholder="多个逗号分开" value="${query.sku }">
                            </div>
                            <label class="control-label col-md-1">状态:</label>
                            <div class="col-md-2">
                                <input class="form-control" type="text" name="query.taskStatus" value="${query.taskStatus}">
                            </div>
                            <label class="control-label col-md-1">打印状态</label>
                            <div class="col-md-2">
                                <input class="form-control" name="query.isPrinting" type="text" value="${query.isPrinting}">
                            </div>
                        </div>
                    </div>

                    <div>
                        <div class="pull-left" style="margin-left: 10px;">
                            <@header method="auth" authCode="PRINT_SPO_QR_CODE">
                            <button type="button" id="syncBtn" class="btn btn-default" onclick="batchPrintSpoQRCode()">
                                打印任务号
                            </button>
                            </@header>
                            <@header method="auth" authCode="DISCARD_SPO_TASK">
                            <button type="button" id="preBtn" class="btn btn-default" onclick="discardedSpoTask()">
                                 废弃任务
                            </button>
                            </@header>
                        </div>
                        <div class="col-md-offset-9" style="text-align: right">
                            <@header method="auth" authCode="DOWNLOAD_SPO_TASK_INFO">
                            <button type="button" class="btn btn-default" onclick="downloadTaskInfo()">
                                <i class="icon-download"></i> 导出明细
                            </button>
                            </@header>
                            <button type="button" class="btn default" onclick="formReset(this)">
                                <i class="icon-refresh"></i> 重置
                            </button>
                            <button type="submit" class="btn blue">
                                <i class="icon-search"></i> 查询
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <br/>
        </div>

        <div class="row">
            <div id="fixedDiv" class="col-md-12">
                <table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
                    <colgroup>
                        <col width="5%" />
                        <col width="10%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="10%" />
                        <col width="10%" />
                        <col width="10%" />
                        <col width="10%" />
                        <col width="10%" />
                        <col width="10%" />
                        <col width="10%" />
                    </colgroup>
                    <thead>
                    <tr>
                        <th>ID</th>
                        <th>任务号</th>
                        <th>内购订单</th>
                        <th>SKU</th>
                        <th>PCS</th>
                        <th>拣货数量</th>
                        <th>拣货差异</th>
                        <th>任务状态</th>
                        <th>打印状态</th>
                        <th>创建人/创建时间</th>
                        <th>领取人/领取时间</th>
                        <th>完成时间</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="col-md-12" id="task-list-warp">
                <table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                    <colgroup>
                        <col width="5%" />
                        <col width="10%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="10%" />
                        <col width="10%" />
                        <col width="10%" />
                        <col width="10%" />
                        <col width="10%" />
                        <col width="10%" />
                        <col width="10%" />
                    </colgroup>
                    <thead>
                    <tr>
                        <th><label class="checkbox-inline"><input type="checkbox" id="check-all" name="checkAll"> ID</label></th>
                        <th>任务号</th>
                        <th>内购订单</th>
                        <th>SKU</th>
                        <th>PCS</th>
                        <th>拣货数量</th>
                        <th>拣货差异</th>
                        <th>任务状态</th>
                        <th>打印状态</th>
                        <th>创建人/创建时间</th>
                        <th>领取人/领取时间</th>
                        <th>完成时间</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list domain.shopOrderPickingTasks as task>
                        <tr>
                            <td>
                                <label class="checkbox-inline"><input type="checkbox" value="${task.id}" name="ids"> ${task.id}</label>
                            </td>
                            <td>${task.taskNo}</td>
                            <td>${task.orderNum}</td>
                            <td>${task.skuNum}</td>
                            <td>${task.skuPcs}</td>
                            <td>${task.pickNum}</td>
                            <td>${task.pickDiff}</td>
                            <td id="${task.id}_taskStatus">
                                ${util('enumName', 'com.estone.checkout.enums.RfoPickingTaskStatus', task.taskStatus)}
                            </td>
                            <td>
                                ${util('enumName',"com.estone.picking.enums.PickingTaskIsPrinting", task.isPrinting)}
                            </td>
                            <td>${util('name',task.createdBy)}<br>${task.createdDate}</td>
                            <td>${util('name',task.receivePerson)}<br>${task.receiveDate}</td>
                            <td>${task.pickingEndDate}</td>
                            <td>
                                <button type="button" class="btn btn-xs btn-info" onclick="getDetail(${task.id})">详情</button>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
            </div>
        </div>
        <div id="fixed-bottom">
            <div id="pager"></div>
        </div>
    </div>

    <!-- 打印弹窗 -->
    <div class="modal fade ui-popup" id="print_modal" tabindex="-1" role="dialog" aria-labelledby="organization-add-label" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content"></div>
        </div>
    </div>

    <#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
<script type="text/javascript">
    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";

    $("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });
    var heights = $("body").height();
    if(heights > 910) {
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }

    // 全选
    var checkAll = $("input[name='checkAll']");
    // 子选项
    var itemIds = $("input[name='ids']");
    checkAll.change(
        function() {
            itemIds.prop("checked", $(this).prop("checked"));
            itemIds.each(function() {
                var f = $(this).is(":checked");
                var checkClass = $(this).prop("class");
                $("." + checkClass).each(function() {
                    $(this).prop("checked", f);
                })
            })
        }
    );

    // 状态
    var taskStatusArray = ${domain.taskStatusJson};
    $("input[name='query.taskStatus']").select2({
        data : taskStatusArray,
        placeholder : "状态",
        allowClear : true
    });

    var isPrintingArray = ${domain.isPrintings};
    $("input[name='query.isPrinting']").select2({
        data : isPrintingArray,
        placeholder : "打印状态",
        allowClear : true
    });

    // 领取人
    $.getJSON(CONTEXT_PATH + "system/saleusers/userByRole", function(json){
        if (json) {
            $("input[name='query.receivePerson']").select2({
                data : json,
                placeholder : "领取人",
                allowClear : true
            });
        } else {
            $("input[name='query.receivePerson']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
        }
    });

    /*明细*/
    function getDetail(id) {
        window.open(CONTEXT_PATH + "spoTask/spoDetail?id=" + id);
    }

    function batchPrintSpoQRCode() {
        var checkedIds = $("input[name='ids']:checked");

        if(checkedIds.length == 0) {
            layer.alert("请选择要操作的任务",'error');
            return;
        }else if (checkedIds.length > 100) {
            layer.alert("批量打印不能超过100个任务!",'error');
            return;
        }
        var trueArray = new Array();
        var falseArray = new Array();

        for (var i = 0; i < checkedIds.length; i++) {
            var checked = checkedIds[i];
            var status = $("#"+checked.value+"_taskStatus").text();
            if (status.indexOf('待领取') != -1) {
                trueArray.push(checked.value);
            }else {
                falseArray.push(checked.value);
            }
        }

        if (falseArray.length>0) {
            layer.alert("选择了:"+falseArray.length+"条不符合的任务将不会打印");
        }


        var taskIds = "";
        for (var i = 0; i < trueArray.length; i++) {
            taskIds += "ids=" + trueArray[i] + "&";
        }

        if (trueArray.length>0) {
            layer.prompt({
                formType: 2,
                title: '请输入打印备注',
                yes: function (index, layero) {
                    var value = layero.find(".layui-layer-input").val();
                    var url = CONTEXT_PATH + "spoTask/qRCodePrint?" + taskIds;
                    if (value) {
                        url = url + "&remark=" + value;
                    }
                    $("#print_modal").removeData("bs.modal");
                    $("#print_modal").modal({
                        remote: url
                    });
                    layer.close(index);
                }
            });
        }
    }

    function getCheckedIds() {
        var outIds = $("input[name='ids']:checked");
        var checkIds = "";
        for (var i = 0; i < outIds.length; i++) {
            var outId = outIds[i].value;
            if (i == 0) {
                checkIds += outId;
            } else {
                checkIds += "," + outId;
            }
        }
        return checkIds;
    }


    function discardedSpoTask() {
        var checkedDatas = getCheckedIds();
        if(checkedDatas.length == 0) {
            layer.alert("请选择要操作的单据");
            return;
        }

        $.ajax({
            url: CONTEXT_PATH + "spoTask/discardedRfoTask",
            type: "POST",
            data: {ids:getCheckedIds()},
            success: function(data){
                layer.alert(data.message,{closeBtn: 0}, function (index) {
                    layer.close(index);
                    window.location.reload();
                });
            }
        });
    }


    //导出
    function downloadTaskInfo() {
        var diglog = dialog({
            title:"导出",
            width: 350,
            height:100,
            url: CONTEXT_PATH + "returnFormOrder/downloadmode",
            okValue: '确定',
            ok: function () {
                var exportWindow = $(this.iframeNode.contentWindow.document.body);

                var submitForm = exportWindow.find("#submit-form");

                var exportType = submitForm.find("input[name='exportType']:checked").val();

                var params = $('#domain').serialize();

                // 导出当前选择
                if(exportType == 3) {
                    var ids = getCheckedIds();
                    if(ids.length == 0) {
                        layer.alert("请选择要操作的数据!");
                        return false;
                    }
                    params = params + "&ids=" + ids;
                }
                params = params + "&exportType=" + exportType
                //downloadByPostForm(params, CONTEXT_PATH + "spoTask/downloadTask");
                $.post(CONTEXT_PATH + "spoTask/downloadTask", params, function(data){
                    if (data.status == 200) {
                        if (data.message==null || data.message==''){
                            layer.alert('成功',function (index) {
                                layer.close(index);
                                diglog.close().remove();
                                location.reload();
                            });
                        }else{
                            customizeLayer(data.message);
                        }
                    } else {
                        customizeLayer(data.message);
                    }
                });

                return true;
            },
            cancelValue:'取消',
            cancel: function () {}

        });
        diglog.show();
    }

</script>
</body>

</html>