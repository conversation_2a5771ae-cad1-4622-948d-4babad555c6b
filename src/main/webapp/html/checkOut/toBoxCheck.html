<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<#include "/common/include.html">
</head>
<body class="page-header-fixed page-full-width">
	<div id="order-log-area" style="max-height: 405px;overflow: auto">
		<div class="form-group">
			<h4><span style="color: red">请确认装箱数量是否等于已捡数量</span></h4>
		</div>
		<table class="table table-condensed table-bordered  table-striped">
			<thead>
			<tr>
				<th>SKU</th>
				<th>SKU图片</th>
				<th>备注</th>
				<#list domain.boxList as box>
					<#if box_has_next>
						<th class="box-line" name="box_${box}">箱号${box}</th>
					<#else >
						<th>${box}</th>
					</#if>
				</#list>
			</tr>
			</thead>
			<tbody id="boxSku-body">
			<#list (domain.skuAndBoxMap)?keys as skuKey>
				<tr name="sku-tr">
					<td name="${skuKey}">${skuKey}</td>
					<td name="${domain.skuAndBoxMap[skuKey][0].url }">
						<img src="${domain.skuAndBoxMap[skuKey][0].url }" width="50px" height="50px;" onclick="enlarge(this)" />
					</td>
					<td name="${domain.skuAndBoxMap[skuKey][0].remark }">${domain.skuAndBoxMap[skuKey][0].remark }</td>
					<#list domain.skuAndBoxMap[skuKey] as item>
						<#if item_has_next>
							<td data-index="${item_index+1}" class="box-line" name="box_${item_index+1}">
								<span>${(item.loadingQuantity)!0}</span>
								<input readonly="readonly" class="form-control" type="hidden" name="loadNum" value="${item.loadingQuantity}">
							</td>
						<#else >
							<td name="total-and-quantity">
								<span name="total-loadNum">${item.loadingQuantity}</span>/
								<span name="total-pickNum">${item.pickQuantity}</span>
							</td>
						</#if>
					</#list>
				</tr>
			</#list>
			<tr>
				<td class="no-wrap-style">尺寸（CM）</td>
				<td></td>
				<td></td>
				<#list domain.boxList as box>
					<#if (domain.boxMap[box])??>
						<td data-index="${box}" class="box-line" name="box_${box}">
							<span name="size">${domain.boxMap[box].productLength}*${domain.boxMap[box].productWidth}*${domain.boxMap[box].productHeight}</span>
							<div style="float:left;">
								<input readonly="readonly" style="display:inline;width: 50px;" placeholder="长" class="form-control" type="hidden" name="length" value="${domain.boxMap[box].productLength}" min="0">
							</div>
							<div style="float:left;margin-left: 5px;">
								<input readonly="readonly" style="display:inline;width: 50px;" placeholder="宽" class="form-control" type="hidden" name="width" value="${domain.boxMap[box].productWidth}" min="0">
							</div>
							<div style="float:left;margin-left: 5px;">
								<input readonly="readonly" style="display:inline;width: 50px;" placeholder="高" class="form-control" type="hidden" name="height" value="${domain.boxMap[box].productHeight}" min="0">
							</div>
						</td>
					</#if>
				</#list>
				<td></td>
			</tr>
			<tr>
				<td class="no-wrap-style">重量（KG）</td>
				<td></td>
				<td></td>
				<#list domain.boxList as box>
					<#if (domain.boxMap[box])??>
						<td data-index="${box}" class="box-line" name="box_${box}">
							<span>${domain.boxMap[box].productWeight}</span>
							<input readonly="readonly" class="form-control input box-weight" type="hidden" name="weight" value="${domain.boxMap[box].productWeight}">
						</td>
					</#if>
				</#list>

				<td></td>
			</tr>
			</tbody>
		</table>
	</div>
</body>
<!-- END BODY -->
<script>
	// 产品图放大
	function enlarge(obj){
		debugger;
		var url = $(obj).attr("src");
		var content = `<img style='width:400px;height:400px;' src='`+url+`'/>`
		layer.open({
			type: 1,
			title: false,
			closeBtn:0,
			area: ['400px','400px'],
			offset:'auto',
			fix: true,
			maxmin: false,
			shade:0.4,
			shadeClose:true,
			content: content
		});
	}
</script>
</html>