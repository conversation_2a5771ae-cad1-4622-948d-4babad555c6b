<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<link rel="stylesheet" href="${CONTEXT_PATH }js/ztree/zTreeStyle.css" type="text/css">
	<style type="text/css">
	
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
ul.ztree {width:350px;min-height:500px;overflow-y:auto;overflow-x:auto;}
</style>
</head>
<body>
	<@header method="header" active="14040000"><#include "/ftl/header.ftl"></@header>
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<h2>用户</h2>
				<ul class="page-breadcrumb breadcrumb">
					<li><a href="#">用户</a> <i class="icon-angle-right"></i></li>
					<li><a href="#">添加用户</a> <i class="icon-angle-right"></i></li>
				</ul>
			</div>
		</div>
		<#assign query = domain.query/>
	
			<div class="row">
				<div class="col-md-12">
					<form target="" id="user-form" name="mainForm" method="post" action="#"> 
						<table class="table table-bordered table-condensed table-hover" id="user-create">
							<thead>
								<colgroup>
									<col width="200px;"/>
									<col />
								</colgroup>
							</thead>
							<tbody>
								<tr>
									<td class="form-label">
										<label class="control-label" >用户名称<span class="required">*</span></label>
									</td>
									<td>
										 <input name="user.username" placeholder="输入用户名称" id="username"/>
									</td>
								</tr>
								
								<tr>
									<td class="form-label">
										<label class="control-label" >用户密码<span class="required">*</span></label>
									</td>
									<td>
										 <input name="user.password" placeholder="输入用户密码" id="password"/>
									</td>
								</tr>
									
							</tbody>
						</table>
						
						<div class="form-actions fluid">
                           <div class="col-md-offset-3 col-md-9">
                           		<button type="submit" class="btn green">
									<i class="icon-save"></i> 提交
								</button>
								
								<a class="btn btn-default" href='${CONTEXT_PATH}system/roles'>
									<i class="m-icon-swapleft"></i> 返回
								</a>
                           </div>
                        </div>
						
					</form>
				</div>
			</div>
		</div>
		<#include "/common/footer.html">
	</div>
	
	<script type="text/javascript" src="${CONTEXT_PATH }js/ztree/jquery.ztree.min.js"></script>

	<script type="text/javascript">
	
		jQuery(document).ready(function() {
			
			var setting = {
					check: {
						enable: true,
						chkDisabledInherit: true
					},
					data : {
						simpleData : {
							enable : true
						}
					}
				};
				
				// 权限树
				$.getJSON(CONTEXT_PATH + 'system/roles/ptree?roleId=${domain.role.roleId}', function(data) {  
			    	$.fn.zTree.init($("#permission-tree"), setting, data);
			    	
					var globalZTree = $.fn.zTree.getZTreeObj("permission-tree");
			    	
			    	var rootNodes = globalZTree.getNodes();
			    	for(var i = 0; i < rootNodes.length; i++) {
			    		/* if(rootNodes[i].checked){
				    		globalZTree.checkNode(rootNodes[i], true, true);
			    		} */
			    	}
			    });
				
				
				// 表单验证
				var userForm = $('#user-form');
				userForm.validate({
					rules: {
		                "user.username": {
		                	required: true,
		                	minlength: 2,
	                        remote: {
	                            url: "${CONTEXT_PATH}system/roles/check/rolename",
	                            dataType: "json",
	                            data: {
	                            	username:function() {
	                                	return $("#username").val().trim();
	                                }
	                            },
	                            dataFilter: function (data) {
	                            	var jsonData = eval("("+data+")");
	                                if (jsonData.status == 200) {
	                                    return true;
	                                }
	                                else {
	                                    return false;
	                                }
	                            }
	                        }
		                }
		            },
		            messages: {
		            	"user.username": {
		            		required:"必填",
		                    minlength: jQuery.validator.format("用户名称最少2位"),
		                    remote: "用户名称已经存在",
		                }
		            },
	                submitHandler: function (form) { // 说明：当表单通过验证，提交表单
	                	createUser();
	                }
	            });
			
		});
		
		
		// 修改角色
		function createUser() {
			
			var requestParam = $("#role-form").serialize();
			
			var treeObj = $.fn.zTree.getZTreeObj("permission-tree");
			var nodes = treeObj.getCheckedNodes(true);
			
			//requestParam.permissions = [];
			
			if (nodes.length > 0) {
				
				for(var i = 0; i < nodes.length; i++) {
					
					var permissionCode = nodes[i].id;
					
					// 数据权限的唯一id是value
					/* if(nodes[i].type == "DATA") {
						permissionCode = nodes[i].value;
					} */
					
					var per = "&role.permissions["+ i +"].permissionCode=" + permissionCode;
					
					requestParam = requestParam + per;
					
					//requestParam.permissions.push({"permissionCode":permissionCode, "parentId":nodes[i].pId});
				}
			}
			
			$.post(CONTEXT_PATH + "system/roles/create", requestParam, function(data){
				var msg = "操作成功";
				
				toastr.options = {
			            closeButton: true,
			            debug: false,
			            timeOut: "5000",
			            positionClass: 'toast-top-center'
			    };
				toastr['success'](msg, "创建角色");
				
				setTimeout(jump, 1000);
			});
			
			function jump(){
				location.href = CONTEXT_PATH + "system/roles";
			}
			
		}
		
	</script>
</body>
</html>