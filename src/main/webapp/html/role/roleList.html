<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
	
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
</style>
</head>
<body>
	<@header method="header" active="14040000" ><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">系统配置</a></li>
					<li class="active">角色</li>
				</ul>
			</div>
		</div>
		<div class="container-fluid" style="background-color: white;border: none">
		<#assign query = domain.query/>
		<div class="row">
			<div class="portlet-body form" style="display: block;">
				<form action="${CONTEXT_PATH}system/roles/search"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="roleForm" id ="domain">
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-1">角色</label>
							<div class="col-md-3">
								<input name="query.likeRoleName" class="form-control" value="${query.likeRoleName}">
							</div>	
							
							<label class="control-label col-md-1">每页数目</label>
							<div class="col-md-3">
								<input id="page-size" name="page.pageSize" class="form-control" value="${domain.page.pageSize}">
							</div>
						</div>	
					</div>
					<div>
                        <@header method="auth" authCode="USER_ROLE_DEFINE_ADD_ROLE">
						<div class="pull-left" style="margin-bottom: 10px;margin-left: 10px">
							<div id="top_tool_bar">
								<div class="btn-group">
									<button type="button" class="btn blue" onclick="addRole()">
										<i class="icon-plus"></i> 添加角色
									</button>
								</div>
							</div>
						</div>
                        </@header>
						<div class="col-md-offset-10" style="text-align: right;margin-right: 10px">
							<button type="submit" class="btn blue">
								<i class="icon-search"></i> 查询
							</button>
							<button type="button" class="btn btn-default">
								<i class="icon-refresh"></i> 重置
							</button>
						</div>
					</div>
				</form>
			</div>
			<div class="row">
				<div id="fixedDiv" class="col-md-12">
					<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
						<colgroup>
							<col width="2%" />
							<col width="5%" />
							<col width="10%" />
							<col width="8%" />
						</colgroup>
						<thead>
						<tr>
							<th>
								<label class="checkbox-inline">
									全选/反选
								</label>
							</th>
							<th>ID</th>
							<th>角色名</th>
							<th>操作</th>
						</tr>
						</thead>
					</table>
				</div>
				<div class="col-md-12" id="task-list-warp">
					<form target="" id="main-form" name="mainForm" method="post" action="">
					<table class="table table-bordered table-condensed table-hover" id="task-list">
						<colgroup>
							<col width="2%" />
							<col width="5%" />
							<col width="10%" />
							<col width="8%" />
						</colgroup>
						<thead>
							<tr>
								<th>
									<label class="checkbox-inline">
                                    	<input type="checkbox" name="checkAll"> 全选/反选
                                    </label>
								</th>
								<th>ID</th>
								<th>角色名</th>
								<th>操作</th>
							</tr>
						</thead>
						<tbody>
							<#list domain.roles as role>
								<tr class="active" id="tr-role-${role.roleId}">
									<td>
										<label class="checkbox-inline"> 
										<input class="${loopVar.index}" name="ids" type="checkbox" value="${role.roleId}" >
										</label>
									</td>
									<td>${role.roleId}</td>
									<td>${role.roleName}</td>
									<td>
										<a class="btn btn-xs btn-default" target="_blank"
										href="${CONTEXT_PATH}system/roles/update?roleId=${role.roleId}">
											 修改 
										</a>
										<button type="button" class="btn btn-xs btn-default" onclick="viewLog(${role.roleId}, 'role')">日志</button>
									</td>
								</tr>
								
							</#list>
							<#if !(domain.roles)!>
								<tr class="tc">
									<td colspan="99" style="text-align: center;">没有记录</td>
								</tr>
							</#if>
							<thead>
								<tr id="top_tool_bars" style="display: none">
									<th>
									<label class="checkbox-inline">
                                    	<input type="checkbox" name="checkAll"> 全选/反选
                                    </label>
									</th>
									<th>ID</th>
									<th>角色名</th>
									<th>操作</th>
								</tr>
							</thead>
						</tbody>
					</table>
					</form>
				</div>
			</div>
		</div>
		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
	<script type="text/javascript">
		// 分页
		var total = "${domain.page.totalCount}";
		var pageNo = "${domain.page.pageNo}";
		var pageSize = "${domain.page.pageSize}";
		$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

		// 绑定滚动事件
		var top_tool_bar = $('#top_tool_bar');
		var ttb = top_tool_bar.offset();
		// 标题栏宽度
		var width=new Array();
	    
		$(window).scroll(function(e) {
			//若滚动条离顶部大于100元素
			if ($(window).scrollTop() > 100)
				$("#d-top").fadeIn(500);//以1秒的间隔渐显id=gotop的元素
			else
				$("#d-top").fadeOut(500);//以1秒的间隔渐隐id=gotop的元素

			if ($(window).scrollTop() > ttb.top) {
				
				// 固定工具栏
				top_tool_bar.addClass('top_tool_bar');
				top_tool_bar.width(top_tool_bar.parent().width());
				
				// 固定标题栏
				$('#top_tool_bars').addClass('top_bar');
	            
	            $('#top_tool_bars').show().css('width',$('#data-list').css('width'));
	            $('#task-list tr:eq(0)').find('th').each(function(i){
	        		width[i]=parseInt($(this).css('width'));
	        	 });
	            $('#top_tool_bars').find('th').each(function(i){
	                $(this).css('width',''+width[i]+'px');
	             });
			} else {
				// 工具栏
				top_tool_bar.removeClass('top_tool_bar');
				top_tool_bar.width("100%");
				
				// 标题栏
				$('#top_tool_bars').hide();
	            $('#top_tool_bars').removeClass('top_bar');
			}
		});
		
		
		// 全选
		var checkAll = $("#task-list").find("input[name='checkAll']");
		
		// 子选项
		var roleIds = $("#task-list").find("input[name='ids']");
		
		checkAll.change(
		  function () {
			  roleIds.prop("checked", $(this).prop("checked"));
			  
			  roleIds.each(function(){
				  var f = $(this).is(":checked");
					var checkClass = $(this).prop("class");
					
					$("." + checkClass).each(function(){
						$(this).prop("checked",f);
					})
			  })
		  }
		);
		
		roleIds.change(function(){
			var checkedLenght = roleIds.filter(":checked").length;
			var length = roleIds.length;
			checkAll.prop("checked", checkedLenght == length);
			
			// 改变背景色
			var $this = $(this);
			if($this.prop("checked")) {
				$this.closest("tr").find("td").css("background-color","#B1DB87");
			} else {
				$this.closest("tr").find("td").css("background-color","");
			}
		});
	
		
		$("input[name='page.pageSize']").select2({
			data : [{ id : '10', text : '10'}, { id : '50', text : '50'}
			, { id : '100', text : '100'}, { id : '300', text : '300'}
			, { id : '500', text : '500'}, { id : '1000', text : '1000'}],
			placeholder : "每页数目",
			allowClear : true
		});
		
		
		//添加角色
		function addRole(){
			window.open(CONTEXT_PATH + "system/roles/create","_blank");
		}
		
		// 删除角色
		function deleteRole(roleId){
			
			var r = confirm("确定要删除?")
			
			if(!r){
				return ;
			}
				
			$.getJSON("${CONTEXT_PATH}system/roles/delete", {roleId:roleId}, function(data) {
				
				if(data.status != 200){
					customizeLayer("服务器错误, " + data.message);
				} else {
					// 移除该行
					$("#tr-role-" + roleId).remove();
				}
			});
		}
		
	</script>
</body>
</html>