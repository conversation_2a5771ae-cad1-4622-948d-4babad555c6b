<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<link rel="stylesheet" href="${CONTEXT_PATH }js/ztree/zTreeStyle.css" type="text/css">
	<style type="text/css">
	
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}

ul.ztree {width:350px;min-height:500px;overflow-y:auto;overflow-x:auto;}
</style>
</head>
<body>
	<@header method="header" active="14040000" ><#include "/ftl/header.ftl"></@header>

	<div id="page">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">系统配置</a></li>
					<li class="active">角色</li>
					<li class="active">编辑</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<#assign query = domain.query/>
	
			<div class="row">
				<div class="col-md-12">
					<form target="" id="role-form" name="mainForm" method="post" action="#"> 
						<input name="role.roleId" type="hidden"  id="role-id" value="${domain.role.roleId }"/>
						<table class="table table-bordered table-condensed table-hover" id="role-update">
							<thead>
								<colgroup>
									<col width="200px;"/>
									<col />
								</colgroup>
							</thead>
							<tbody>
								<tr>
									<td class="form-label">
										<label class="control-label" >角色名称<span class="required">*</span></label>
									</td>
									<td>
										 <input name="role.roleName" placeholder="输入角色名称" id="rolename" value="${domain.role.roleName }"/>
									</td>
								</tr>
								
								<tr>
									<td class="form-label">
										<label class="control-label">权限</label>
									</td>
									<td>
										<div class="col-xs-3">
											<ul id="permission-tree" class="ztree form-control"></ul>
			                           		<span class="help-info"></span>
										</div>
										<div class="col-xs-9" id="authButton" style="display: none;">
											<div style="font-weight：bold;font-family：“SimHei”;font-size: 30px;margin-bottom: 20px;">按钮权限</div>
											<table class="table table-bordered table-condensed table-hover" id="auth-list">
												<thead>
													<tr>
														<th>
															<label class="checkbox-inline">
						                                    	<input type="checkbox" name="checkAll" id="selectAll"/> 全选/反选
						                                    </label>
														</th>
														<th>ID</th>
														<th>权限名称</th>
														<th>权限code</th>
													</tr>
												</thead>
												<tbody id="authPermissionTbody">
													
												</tbody>
											</table>
											
											<div style="text-align: center;margin-top: 30px;">
												<div class="btn-group">
													<button type="button" class="btn blue" onclick="confirmAuthPermission()">
														<i class="icon-save"></i> 确认提交
													</button>
												</div>
											</div>
										</div>
									</td>
								</tr>
									
							</tbody>
						</table>
						
						<div class="form-actions fluid">
                           <div class="col-md-offset-3 col-md-9">
                           		<button type="submit" class="btn green">
									<i class="icon-save"></i> 提交
								</button>
								
								<a class="btn btn-default" href='${CONTEXT_PATH}system/roles'>
									<i class="m-icon-swapleft"></i> 返回
								</a>
                           </div>
                        </div>
						
					</form>
				</div>
			</div>
		</div>
		<#include "/common/footer.html">
	</div>
	
	<script type="text/javascript" src="${CONTEXT_PATH }js/ztree/jquery.ztree.min.js"></script>

	<script type="text/javascript">
	
		function confirmAuthPermission() {
			var permissionCodes = $("#authPermissionTbody").find("input[name='permissionCodes']").serialize();
			var roleId = $("#role-id").val();
			var parentCode = $("#authPermissionTbody").find("input[name='parentCode']").val();
			$.post(CONTEXT_PATH + "system/roles/updateAuth?" + permissionCodes + "&roleId=" + roleId + "&parentCode=" + parentCode, function(data){
				if (data.status == '200') {
					layer.alert(data.message);
				} else {
					customizeLayer(data.message, 'error');
				}
			});
		}	
	
		jQuery(document).ready(function() {
			
			var setting = {
				check: {
					enable: true,
					chkDisabledInherit: true
				},
				data : {
					simpleData : {
						enable : true
					}
				},
				callback : {
					onClick : zTreeOnClick
				}
			};
			
			function zTreeOnClick(event, treeId, treeNode) {
				$("#authPermissionTbody").html('');
				if (treeNode.level == 2 && treeNode.value.length != 6) {//PDA权限Code为6位
					$.getJSON(CONTEXT_PATH + 'system/permissions/queryAuthPermissions?parentCode=' + treeNode.value + "&roleId=" + $("#role-id").val(), function(data) {
						var html = "";
						if (data.length > 0) {
							html += '<input name="parentCode" type="hidden" value="' + data[0].parentId + '" />';
							for (var i = 0; i < data.length; i++) {
								html +='<tr class="active" id="tr-auth-"' + data[i].permissionId + '>';
								html +=	'<td>';
								html +=		'<label class="checkbox-inline">';
								var isChoice = data[i].isChoice;
								if (isChoice == true) {
									html +=			'<input name="permissionCodes" type="checkbox" checked="checked" value="' + data[i].permissionCode + '" />';
								} else {
									html +=			'<input name="permissionCodes" type="checkbox" value="' + data[i].permissionCode + '" />';
								}
								html +=		'</label>';
								html +='</td>';
								
								html +=	'<td>' + data[i].permissionId + '</td>';
								html +=	'<td>' + data[i].permissionName + '</td>';
								html +=	'<td>' + data[i].permissionCode + '</td>';
								html +='</tr>';
							}
							
						} else {
							html +='<tr class="tc">';
							html +=	'<td colspan="99" style="text-align: center;">没有记录</td>';
							html +='</tr>';
						}
						$("#authPermissionTbody").html(html);
					});
					$("#authButton").show();
				} else {
					$("#authButton").hide();
				}
				
			}
			
			// 权限树
			$.getJSON(CONTEXT_PATH + 'system/roles/ptree?roleId=${domain.role.roleId}', function(data) {  
		    	$.fn.zTree.init($("#permission-tree"), setting, data);
		    	
				var globalZTree = $.fn.zTree.getZTreeObj("permission-tree");
		    	
		    	var rootNodes = globalZTree.getNodes();
		    	for(var i = 0; i < rootNodes.length; i++) {
		    		/* if(rootNodes[i].checked){
			    		globalZTree.checkNode(rootNodes[i], true, true);
		    		} */
		    	}
		    });
			
			
			// 表单验证
			var roleForm = $('#role-form');
			roleForm.validate({
                rules: {
                	roleName: {
                        minlength: 3,
                        required: true
                    }
                },
                submitHandler: function (form) { // 说明：当表单通过验证，提交表单
                	updateRole();
                }
            });

		});
		
		// 修改角色
		function updateRole() {
			
			var requestParam = $("#role-form").serialize();
			
			var treeObj = $.fn.zTree.getZTreeObj("permission-tree");
			var nodes = treeObj.getCheckedNodes(true);
			
			//requestParam.permissions = [];
			
			if (nodes.length > 0) {
				
				for(var i = 0; i < nodes.length; i++) {
					
					var permissionCode = nodes[i].id;
					
					// 数据权限的唯一id是value
					/* if(nodes[i].type == "DATA") {
						permissionCode = nodes[i].value;
					} */
					
					var per = "&role.permissions["+ i +"].permissionCode=" + permissionCode;
					
					requestParam = requestParam + per;
					
					//requestParam.permissions.push({"permissionCode":permissionCode, "parentId":nodes[i].pId});
				}
			}
			
			$.post(CONTEXT_PATH + "system/roles/update", requestParam, function(data){
				var msg = "操作成功";
				
				toastr.options = {
			            closeButton: true,
			            debug: false,
			            timeOut: "5000",
			            positionClass: 'toast-top-center'
			    };
				toastr['success'](msg, "修改角色");
				
				setTimeout(jump, 1000);
				
			});
			
			function jump(){
				location.href = CONTEXT_PATH + "system/roles?query.roleId=" + $("#role-id").val();
			}
			
		}

		// 全选/反选复选框点击事件
		$("#selectAll").on("click",function () {
		    var checkResult = $(this).prop("checked");
		    $("#authPermissionTbody input[name = 'permissionCodes']").each(function (index,item) {
		        $(item).removeAttr("checked");
		        if (checkResult){
                    $(item).prop("checked","checked");
                }
            })
        })
		
	</script>
</body>
</html>