<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <#include "/common/include.html">
    <link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
    <style type="text/css">
        .modal-body input {
            width: 240px;
        }
        .top_bar{
            position:fixed;top:0px;
        }
        #task-list td {
            vertical-align:middle;
        }
    </style>
</head>
<body>
<@header method="header" active="13010000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li>
                    <a href="#">仓库报表</a>
                </li>
                <li class="active">虾皮揽收报表</li>
            </ul>
        </div>
    </div>
    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">
        <#assign query=domain.query>
        <!-- END PAGE HEADER-->
        <div class="row">
            <div class="col-md-12">
                <form action="${CONTEXT_PATH}collectCode/shopeeCollectCode/statistics/search"
                      class="form-horizontal form-bordered form-row-stripped" enctype="multipart/form-data"
                      method="post" modelAttribute="domain" name="shopeeCollectCodeListForm" id="domain">
                    <!-- 分页信息 -->
                    <input id="page-no" type="hidden" name="page.pageNo" value="1">
                    <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
                    <div class="form-body">
                        <div class="form-group">
                            <label class="control-label col-md-1">装车时间</label>
                            <div class="input-group col-md-3">
                                <input class="form-control Wdate" name="statisticsQuery.deliverDateBegin" type="text" value="${domain.statisticsQuery.deliverDateBegin}" readonly="readonly" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd'})">
                                <span class="input-group-addon">到</span>
                                <input class="form-control Wdate" name="statisticsQuery.deliverDateEnd" type="text" value="${domain.statisticsQuery.deliverDateEnd}" readonly="readonly" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd'})">
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="col-md-offset-10" style="text-align: right;margin-right: 10px">
                            <button type="button" class="btn btn-default" onclick="formReset(this)">
                                <i class="icon-refresh"></i> 重置
                            </button>
                            <button type="submit" class="btn blue">
                                <i class="icon-search"></i> 查询
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <br/>
        </div>

        <div class="row">
            <div class="col-md-12" id="task-list-warp">
                <table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                    <thead>
                    <tr>
                        <th>装车日期</th>
                        <th>装车总单数</th>
                        <th>装车成功单数</th>
                        <th>装车失败单数</th>
                        <th>装车成功率</th>
                        <th>生成揽收码数</th>
                        <th>打印揽收码数</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list domain.statisticList as statistic>
                    <tr>
                        <td>${statistic.deliverDate}</td>
                        <td>${statistic.totalSum}</td>
                        <td>${statistic.successNumberSum}</td>
                        <td>${statistic.failNumberSum}</td>
                        <#if statistic.successRatio??>
                            <td>${statistic.successRatio*100} %</td>
                        <#else>
                            <td></td>
                        </#if>
                        <td>${statistic.collectCount}</td>
                        <td>${statistic.printCollectCount}</td>
                    </tr>
                    </#list>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="fixed-bottom">
            <div id="pager"></div>
        </div>
    </div>
    <#include "/common/footer.html">
</div>

<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript">
    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";
    $("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

    var heights = $("body").height();
    if(heights>910){
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }

    function reloadPage(){
        location.reload()
    }
</script>
</body>
</html>