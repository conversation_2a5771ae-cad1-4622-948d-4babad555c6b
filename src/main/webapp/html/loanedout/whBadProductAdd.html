<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
	<@header method="header" active="11030200"><#include "/ftl/header.ftl"></@header>
	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">库存管理</a></li>
					<li class="active">不良品管理</li>
					<li class="active">新增不良品</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="col-md-9">
				<form action=""
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="checkInForm" id="domain">
					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-1" style="width:280px">不良品SKU数量请提前线下确认好再提交</label>
						</div>
						<div class="form-group" style="margin-top: 40px;">
							<div class="col-md-5">SKU</div>
						</div>
						<div class="form-group">
							<div class="col-md-5">
								<div id="input_sku">
									<input style="height:35px;border-radius: 8px !important;" onkeypress="if(event.keyCode==13) { getSkuStock(); return false;}"
                                           class="form-control" placeholder="请输入SKU，多个SKU请以英文,分开" id="skus" name="skus" type="text">
								</div>
							</div>
                            <div class="col-md-2">
								<div style="border-radius: 8px !important;width: 100%;" type="button" class="btn blue" onclick="getSkuStock();">
									<i class="icon-plus"></i> 添加SKU
								</div>
                            </div>
						</div>

						<div class="form-group" style="margin-top: 20px;">
							<div class="col-md-7">
								<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
									<thead>
										<tr>
											<th>序号</th>
											<th>SKU</th>
											<th>报废批次|到期时间|剩余库存</th>
											<th>库位</th>
											<th>总可用库存</th>
											<th>外借数量</th>
											<th>操作</th>
										</tr>
									</thead>
									<tbody id="tbody">
									</tbody>
								</table>
							</div>
						</div>

						<div class="form-group">
							<label class="control-label col-md-1" style="width:80px"><span class="required">*</span>备注：</label>
							<div class="col-md-2" style="width:100px">
								<textarea cols="59"  rows="3" id="remark" name="query.remark" style="margin-top: 10px;"></textarea>
							</div>
						</div>
					</div>
				</form>
				<div class="col-md-9" style="margin-bottom: 20px;">
					<button class="btn blue" id="confirm">
						<i class="icon-search"></i> 提交不良品单
					</button>
				</div>
			</div>
			<br/>
		</div>
	</div>
	
		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript">
		var stockIdMap = new WebStorageCache();

        function getSkuStock() {
        	var outLocationType = 0;
            var skus = $('#skus').val();


            if(skus == ''){
                layer.alert('sku不能为空！','error');
                return;
			}
            $.post(CONTEXT_PATH + "scrap/queryStocks", {skus:skus,outLocationType:outLocationType}, function(data) {
                if (data.status == 200) {
                    var sku_array=new Array();
                    $("[id=data-sku]").each(function(){
                        sku_array.push($(this).text());
                    });
					// 保质期sku
					var exp_sku_array=new Array();
					if (data.body.expSkuList.length > 0){
						for (var i = 0; i<data.body.expSkuList.length;i++) {
							exp_sku_array.push(data.body.expSkuList[i])
						}
					}
                    var errorSkuStr = '';
                    var index = sku_array.length;
					if(Object.keys(data.body.stockMap).length > 0){
						var html = "";
						$.each(data.body.stockMap, function(sku, stockList) {
							var number=$.inArray(sku, sku_array);
							var expNumber=$.inArray(sku, exp_sku_array);
							//移除stockList中quantity为0的数据
							for(var i = 0; i < stockList.length; i++){
								if(stockList[i].quantity <= 0){
									stockList.splice(i, 1);
									i--;
								}
							}
							if(stockList.length == 0){
								errorSkuStr = sku + "," + errorSkuStr;
							}else {
								if(number == -1) {
									var locationHtml = "";
									for (var s = 0; s<stockList.length;s++) {
										locationHtml += "<option value='"+stockList[s].id+"'>" +stockList[s].locationNumber + "</option>" ;
										stockIdMap.set(stockList[s].id, {
											quantity: stockList[s].quantity,
											expManageMap: data.body.expManageMap[stockList[s].id],
											notBatchStockMap: data.body.notBatchStockMap[stockList[s].id]
										}, {exp: 5 * 60 * 60});
									}

									if(expNumber == -1) {
										html += "<tr id='" + sku + "'>" +
												"<td id='data-index'>" + parseInt(index + 1) + "</td>" +
												"<td id='data-sku'>" + sku + "</td>" +
												"<td id='data-batch'></td>" +
												"<td id='data-location'>" +
												"<select id='locationNumber' name ='locationNumber' onchange='changeLocation(this)' style='width:120px;'>" + locationHtml+ "</select>" +
												"</td>" +
												"<td id='data-quantity' class='" + sku + "'>" + stockList[0].quantity + "</td>" +
												"<td><input type='text' id='data-newQuantity' style='width:60px;' data-canRemoveQuantity='" + stockList[0].quantity + "' value='1' onchange='changeNum(this)'></td>" +
												"<td><button type='button' class='btn btn-xs btn-info' onclick='onRemove(this)'>删除</button></td></tr>";
									}else{
										var key = stockList[0].id;
										var selectHtml = "";
										if (data.body.expManageMap[key] != null && data.body.expManageMap[key].length>0){
											selectHtml = "<option disabled='disabled'>批次 &nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp| 到期时间 &nbsp&nbsp&nbsp| 剩余库存 </option>";
											for (var j = 0; j<data.body.expManageMap[key].length;j++) {
												selectHtml += "<option value='"+data.body.expManageMap[key][j].batchSelect+"'>" +data.body.expManageMap[key][j].batchSelect + "</option>" ;
											}
										}
										if (data.body.notBatchStockMap[key] != null
												&& data.body.notBatchStockMap[key] != ""
												&& data.body.notBatchStockMap[key] != undefined){
											selectHtml += "<option value='notBatch'>非保质期批次&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp|&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp|" + data.body.notBatchStockMap[key] + "</option>" ;
										}

										html += "<tr id='" + sku + "'>" +
												"<td id='data-index'>" + parseInt(index + 1) + "</td>" +
												"<td id='data-sku'>" + sku + "</td>" +
												"<td id='data-batch'> <select id='batchNo'  name ='batchNo' style='width:260px;'>" + selectHtml+ "</select></td>" +
												"<td id='data-location'>" +
												"<select id='locationNumber' name ='locationNumber' onchange='changeLocation(this)' style='width:120px;'>" + locationHtml+ "</select>" +
												"</td>" +
												"<td id='data-quantity'class='" + sku + "'>" + stockList[0].quantity + "</td>" +
												"<td><input type='text' id='data-newQuantity' style='width:60px;' data-canRemoveQuantity='" + stockList[0].quantity + "' value='1' onchange='changeNum(this)'></td>" +
												"<td><button type='button' class='btn btn-xs btn-info' onclick='onAdd(this)'>增加批次</button>&nbsp;" +
												"<button type='button' class='btn btn-xs btn-info' onclick='onRemove(this)'>删除</button></td>" +
												"</tr>";
									}
									index = index+1;
								}else {
									var skuNum = parseInt($('.'+sku).text());
									var number=1+parseInt($('.'+sku).next().find("input").val());
									if(number > skuNum){
										layer.alert(sku + "确认不良品数量不能大于可用库存！", "error");
										return false;
									}else {
										$('.'+sku).next().find("input").val(number);
									}
								}
							}
						});
						if(errorSkuStr != ''){
						    layer.alert(errorSkuStr + " 可用库存为0",'error');
						}
						$("#tbody").append(html);
					}
                } else {
                    customizeLayer(data.message, "error");
                }
                $('#skus').val('');
            });
        }

        function onRemove(obj){
            if(confirm("是否删除该条SKU?")){
				$(obj).parent().parent().remove();
				// 重新编排
				refreshIndex();
            }
        }

		function refreshIndex(){
			$("#tbody").find('tr').each(function (indexValue) {
				indexValue++;
				$(this).find('td[id="data-index"]').text(indexValue);
			});
		}


		function onAdd(obj) {
			var html = $(obj).parent().parent().html();
			$("#tbody").append("<tr>"+html+"</tr>");
			refreshIndex();

		}

		function changeNum(obj){
			var numQty = $(obj).val();
			if(!(/(^[1-9]\d*$)/.test(numQty))){
				layer.alert("数量不是正整数!!!","error");
				$(obj).val('');
				return ;
			}
		}

		$('#confirm').on("click",function () {
			var whLend = {};
            var whLendItems = [];
            var skus = '';

            var result = false;
            $('#tbody').find('tr').each(function (index) {
                var whLendItem = {};
                var dataIndex = $(this).find('td[id="data-index"]').text();
                var sku = $(this).find('td[id="data-sku"]').text();
				var dataBatch = $(this).find('td[id="data-batch"]').find('select[id="batchNo"]').val();
                var canOutQuantity = $(this).find('td[id="data-quantity"]').text();
                var quantity = $(this).find('td>input[id="data-newQuantity"]').val();
				var stockId = $(this).find('td[id="data-location"]').find('select[id="locationNumber"]').val();
                if(quantity == ''){
					layer.alert('第'+dataIndex+'个sku的确认不良品数量为空！','error');
					result = false;
					return false;
				}
                var r = /^\d+$/;
                if(!r.test(quantity)){
                    layer.alert("请填写正整数！", "error");
                    result = false;
                    return false;
                }
                if(quantity == 0){
                    layer.alert("第"+dataIndex+"个sku 不能填写0-不需要请删除！", "error");
                    result = false;
                    return false;
                }
				if(parseInt(quantity) > parseInt(canOutQuantity)){
                    layer.alert('第'+dataIndex+'个sku的确认不良品数量大于可移动库存！','error');
                    result = false;
                    return false;
				}
				var batchNoJson = dataBatch;
				var expDate='';
				if (dataBatch != '' &&  dataBatch != null && dataBatch != undefined){
					var split = dataBatch.split('|');
					if(split[0].trim() != 'notBatch'){
						if (parseInt(quantity) > parseInt(split[2].trim())){
							layer.alert('第'+dataIndex+'个sku的外借数量大于批次剩余数量！','error');
							result = false;
							return false;
						}
					}
					if (split[1] != '' &&  split[1] != null && split[1] != undefined){
						expDate = split[1].trim();
					}
					batchNoJson = split[0].trim();
				}
                skus += "," + sku;
				whLendItem.sku = sku;
				whLendItem.stockId = stockId;
				whLendItem.quantity = quantity;
				whLendItem.batchNoJson = batchNoJson;
				whLendItem.expDate = expDate;
				whLendItems.push(whLendItem);
                result = true;
			});

			var remark = $('#remark').val().replace(/\s/g,'');
			if(!remark){
			    layer.alert('备注不能为空！','error');
			    return false;
			}


			var remarkStr = $('#remark').val();
			whLend.remark = remarkStr;
			whLend.items = whLendItems;

            if(result && remark != ''){
				var param = {whBadProduct:JSON.stringify(whLend)};

				$.post(CONTEXT_PATH + "whBadProduct/create", param, function(data) {
					if (data.status == 200) {
						alert("成功！");
						setTimeout(function() {
							location.href = CONTEXT_PATH + "whBadProduct";
						}, 1500);
					} else {
						customizeLayer(data.message, "error");
					}
				});
			}
        });

	</script>
</body>
</html>