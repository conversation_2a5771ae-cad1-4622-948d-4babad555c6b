<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		#task-list td {
			vertical-align:middle;
		}
	</style>
</head>
<body>
<@header method="header" active="12020000" ><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li>库存管理</li>
				<li class="active">外借拣货单</li>
			</ul>
		</div>
	</div>

	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
	<div class="row">
		<div class="col-md-12">
		<#assign query = domain.query>
			<form action="${CONTEXT_PATH}LendPicking/search"
					   class="form-horizontal form-bordered form-row-stripped"
					   method="post" modelAttribute="domain" name="LendPickingForm" id ="domain">
				<!-- 分页信息 -->
				<input id="page-no" type="hidden" name="page.pageNo" value="1">
				<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
				<div class="form-body">
					<div class="form-group">
						<label class="control-label col-md-1">SKU</label>
						<div class="col-md-3">
							<input class="form-control" type="text" name="query.skuSplit" placeholder="请输入SKU,多个sku请用英文逗号隔开" value="${query.skuSplit }">
						</div>


						<label class="control-label col-md-1">外借单号</label>
						<div class="col-md-3">
							<input class="form-control" type="text" name="query.lendCheckOutCodeSplit" placeholder="请输入外借单号，多个外借单号请用英文逗号隔开" value="${query.lendCheckOutCodeSplit }">
						</div>
						<label class="control-label col-md-1">任务号</label>
						<div class="col-md-3">
							<input class="form-control" type="text" name="query.taskNoSplit" placeholder="请输入任务号，多个任务号请用英文逗号隔开" value="${query.taskNoSplit }" >
						</div>

						<label class="control-label col-md-1">任务状态</label>
						<div class="col-md-3">
							<input class="form-control" name="query.taskStatus" type="text" value="${query.taskStatus}">
						</div>
						<label class="control-label col-md-1">打印状态</label>
						<div class="col-md-3">
							<select class="form-control" name="query.isPrinting" type="text" value="${query.isPrinting}">
								<option value=""></option>
								<option value="0" <#if query.isPrinting == 0>selected="selected"</#if> >未打印</option>
							<option value="1" <#if query.isPrinting == 1>selected="selected"</#if> >已打印</option>
						</select>
						</div>
						<label class="control-label col-md-1">领取人</label>
						<div class="col-md-3">
							<input class="form-control" type="text" name="query.receivePerson" placeholder="请输入任务ID" value="${query.receivePerson}">
						</div>
						<label class="control-label col-md-1">创建时间</label>
						<div class="col-md-3">
							<div class="input-group">
								<input class="form-control Wdate" type="text" name="query.fromCreatedDate" placeholder="" readonly="readonly" value="${query.fromCreatedDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								<span class="input-group-addon">到</span>
								<input class="form-control Wdate" type="text" name="query.toCreatedDate" placeholder="" readonly="readonly" value="${query.toCreatedDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
							</div>
						</div>
						<label class="control-label col-md-1">完成时间</label>
						<div class="col-md-3">
							<div class="input-group">
								<input class="form-control Wdate" type="text" name="query.fromPickingEndDate" placeholder="" readonly="readonly" value="${query.fromPickingEndDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								<span class="input-group-addon">到</span>
								<input class="form-control Wdate" type="text" name="query.toPickingEndDate" placeholder="" readonly="readonly" value="${query.toPickingEndDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
							</div>
						</div>
						<label class="control-label col-md-1">库存ID:</label>
						<div class="col-md-3">
							<input class="form-control" type="text" name="query.stockIdStr" placeholder="多个逗号分开" value="${query.stockIdStr }">
						</div>
					</div>
					<div class="form-group">

					</div>
					<div style="margin-top: 20px;">
						<div class="pull-left" style="margin-left: 10px;" >
                                <@header method="auth" authCode="OUT_LEND_PICKING_TASK_PRINT_TASK_ORDER">
								<button type="button" class="btn  btn-default" onclick="batchaPrintQRCode()">
									<i class="icon-pickingTask"></i> 打印任务号
								</button>
                                </@header>
								<@header method="auth" authCode="OUT_LEND_PICKING_TASK_DISCARD_ORDER">
									<button type="button" class="btn  btn-default" onclick="discardedTask()">
										<i class="icon-pickingTask"></i> 废除任务
									</button>
								</@header>
						</div>
						<div class="col-md-offset-12" style="text-align: right">
							<button type="button" class="btn btn-default" onclick="formReset(this)">
								<i class="icon-refresh"></i> 重置
							</button>
							<button type="submit" class="btn blue">
								<i class="icon-search"></i> 查询
							</button>
                            <@header method="auth" authCode="OUT_LEND_PICKING_TASK_DOWNLOAD">
							<button type="button" class="btn btn-default" onclick="downloadPickingTask()">
								<i class="icon-download"></i> 导出
							</button>
                            </@header>
						</div>
					</div>
				</div>
			</form>
		</div>
	</div>

	<div class="row">
		<div id="fixedDiv" class="col-md-12">
			<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
				<thead>
				<tr>
					<th><label class="checkbox-inline">ID</label></th>
					<th>任务号</th>
					<th>外借单</th>
					<th>SKU</th>
					<th>PCS</th>
					<th>拣货数量</th>
					<th>拣货差异</th>
					<th>任务状态</th>
					<th>打印状态</th>
					<th>创建人/创建时间</th>
					<th>领取人/领取时间</th>
					<th>完成时间时间</th>
					<th>操作</th>
				</tr>
				</thead>
			</table>
		</div>
		<div class="col-md-12" id="task-list-warp">
			<!--内容 -->
			<table class="table table-striped table-bordered table-hover table-condensed"  id="task-list">

				<colgroup>
					<col width="6%"/>
					<col width="10%"/>
					<col width="5%"/>
					<col width="5%"/>
					<col width="5%"/>
					<col width="5%"/>
					<col width="5%"/>
					<col width="5%"/>
					<col width="5%"/>
					<col width="14%"/>
					<col width="14%"/>
					<col width="12%"/>
					<col width="5%"/>
				</colgroup>

				<thead>
				<tr>
					<th><label class="checkbox-inline"><input type="checkbox"  id="check-all" onclick="checkAll(this);">编码</label></th>
					<th>任务号</th>
					<th>外借单</th>
					<th>SKU</th>
					<th>PCS</th>
					<th>拣货数量</th>
					<th>拣货差异</th>
					<th>任务状态</th>
					<th>打印状态</th>
					<th>创建人/创建时间</th>
					<th>领取人/领取时间</th>
					<th>完成时间</th>
					<th>操作</th>
				</tr>
				</thead>
				<tbody>
				<#list domain.whLendPickingTasks as whLendPickingTask>
				<tr>
					<td>
						<label class="checkbox-inline">
							<input type="checkbox" name="taskIds" value="${whLendPickingTask.id}"/> ${whLendPickingTask.id}
						</label>
					</td>
					<td>${whLendPickingTask.taskNo}</td>

					<td>${whLendPickingTask.whLendQuantity}</td>

					<td>${whLendPickingTask.skuLendQuantity}</td>


					<td>${whLendPickingTask.pcsQuantity}</td>

					<td>${whLendPickingTask.pickingQuantity}</td>

					<td>${whLendPickingTask.differenceQuantity}</td>

					<td id="${whLendPickingTask.id}_taskStatus">
						${util('enumName', "com.estone.loanedout.enums.LendStatus", whLendPickingTask.taskStatus)}
					</td>

					<td>${(whLendPickingTask.isPrinting == 0) ? string("未打印", "已打印")}</td>

					<td>${util('name', whLendPickingTask.createdBy)} / ${whLendPickingTask.createdDate}</td>

					<td>
						<#if (whLendPickingTask.receivePerson)!>
						${util('name', whLendPickingTask.receivePerson)} / ${whLendPickingTask.receiveDate}
						</#if>
					</td>
					<td>
						${whLendPickingTask.pickingEndDate}
					</td>
					<td>
						<a class="btn btn-xs btn-default" href="${CONTEXT_PATH}LendPicking/whLendDetail?pickingTaskId=${whLendPickingTask.id }">
							<span class="icon-edit"></span>  查看详情
						</a>
						<button type="button" class="btn btn-xs btn-info" onclick="viewLog(${whLendPickingTask.id}, 'WhLendPickingTask')">日志</button>
					</td>
				</tr>
				</#list>
				</tbody>
			</table>
			<!--内容end -->
		</div>
	</div>
	</div>
		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
</div>

	<!-- 打印弹窗 -->
   	<div class="modal fade ui-popup" id="print_modal" tabindex="-1" role="dialog" aria-labelledby="organization-add-label" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content"></div>
		</div>
	</div>

<#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript">
    // 分页
    var total = "${domain.page.totalCount}";
	var pageNo = "${domain.page.pageNo}";
	var pageSize = "${domain.page.pageSize}";
	$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});




	$.getJSON(CONTEXT_PATH + "system/saleusers/userByPermissionCode?permissionCode=240000", function(json){
		if (json) {
			$("input[name='query.receivePerson']").select2({
				data : json,
				placeholder : "领取人",
				allowClear : true
			});
		} else {
			$("input[name='query.receivePerson']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
		}
	});



	//全选
	function checkAll(obj) {
		$("input[name='taskIds']").prop("checked", $(obj).is(':checked'));
	}

    var taskStatusArray = ${domain.query.taskStatusJSON};
    $("input[name='query.taskStatus']").select2({
        data : taskStatusArray,
        placeholder : "任务状态",
        allowClear : true
    });

	
	// 获取选中的任务
	function getCheckedTaskIds() {
		var checkedTaskIds = $("input[name='taskIds']:checked");
		return checkedTaskIds;
	}


	//打印任务号
	function batchaPrintQRCode() {
		var checkedDatas = getCheckedTaskIds();
		debugger;
		if(checkedDatas.length == 0) {
			layer.alert("请选择要操作的任务",'error');
			return;
		}else if (checkedDatas.length > 100) {
			layer.alert("批量打印不能超过100个任务!",'error');
			return;
		}
		var trueArray = new Array();
		var falseArray = new Array();
		for (var i = 0; i < checkedDatas.length; i++) {
			var checked = checkedDatas[i];
			var status = $("#"+checked.value+"_taskStatus").text();
			debugger;
			if (status.indexOf('待拣货') != -1) {
				trueArray.push(checked.value);
			}else {
				falseArray.push(checked.value);
			}
		}
		
		if (falseArray.length>0) {
			layer.alert("选择了的:"+falseArray.length+"条不符合的任务将不会打印", 'error');
		}
	
		
		var taskIds = "";
		for (var i = 0; i < trueArray.length; i++) {
			taskIds += "taskIds=" + trueArray[i] + "&";
		}
	
		if (trueArray.length>0) {
			$("#print_modal").removeData("bs.modal");
			$("#print_modal").modal({
				remote: CONTEXT_PATH + "LendPicking/qRCodePrint?" + taskIds
			});
		}
	}
	
	//导出
	// 下载
	function downloadPickingTask(downloadFor){
		var checkedDatas = getCheckedTaskIds();
		
		var diglog = dialog({
			title: '导出',
			width: 350,
			height:100,
			url: CONTEXT_PATH + "LendPicking/downloadmode",
			okValue: '确定',
		    ok: function () {
		    	
		    	var exportWindow = $(this.iframeNode.contentWindow.document.body);
		    	
		    	var submitForm = exportWindow.find("#submit-form");
		    	
		    	var exportType = submitForm.find("input[name='exportType']:checked").val();
		    	
		    	var submitFormParam = submitForm.serialize();
		    	
		    	// 导出当前选择
		    	if(exportType == 3) {
					if(checkedDatas.length == 0) {
						layer.alert("请选择要操作的数据!");
						return false;
					} else if (checkedDatas.length > 300) {
						layer.alert("选择数量不能超过300!");
						return false;
					}
					
					submitFormParam = submitFormParam + "&" +checkedDatas.serialize();
			    	
		    	}
		    	
		    	//还原分页
		    	$("#page-no").val("${domain.page.pageNo}");
		    	
		    	
		    	var action = document.LendPickingForm.action;
		    	var target = document.LendPickingForm.target;
		    	var method = document.LendPickingForm.method;
		    	document.LendPickingForm.action= CONTEXT_PATH + "LendPicking/download?" + submitFormParam;
		    	document.LendPickingForm.target="_blank";
		    	document.LendPickingForm.method="POST";
		    	document.LendPickingForm.submit();
		    	document.LendPickingForm.target=target;
		    	document.LendPickingForm.action=action;
		    	document.LendPickingForm.method=method;
		    	
		    	$("#page-no").val("1");
		    	
		    	setTimeout(function () {
		    		diglog.close().remove();
		    	}, 100);
		    	
		    	return true;
		    },
		    cancelValue: '取消',
		    cancel: function () {}
		});
		diglog.show();
	}
	
	//废除任务
	function discardedTask(){
		var checkedDatas = getCheckedTaskIds();
		
		if(checkedDatas.length == 0) {
			layer.alert("请选择要操作的任务",'error');
			return;
		}
		
		var taskIds = checkedDatas.serialize();
		
		var r = confirm("确定是否废除任务？");
    	if (r) {
    		$.get(CONTEXT_PATH + "LendPicking/discardedTask?"+taskIds, function(data){
    			if (data.status == 200) {
					layer.confirm(data.message,{
						icon: 1,
						btn: ['确定']
					},function () {
						window.location.reload();
					})
    			} else {
    				customizeLayer(data.message);
    			}
    		});
    	}	
	}
	

    
</script>
</body>
</html>