<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<#include "/common/include.html">
		<#include "/common/webSocket.html">
		<link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
		<style type="text/css">
			.modal-body input {
				width: 240px;
			}

			.return-model {
				padding: 12px;
			}

			#return-info-btn {
				color: #ffff;
				background-color: #5bc0de;
				padding: 3px;
			}
		</style>
	</head>

	<body>
		<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>

		<div id="page" style="background-color: rgb(231, 237, 248)">
			<div class="row">
				<div class="col-md-12" style="padding: 0">
					<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
						<li>
							<a href="#">库存</a>
						</li>
						<li class="active"> 报废单管理</li>
					</ul>
				</div>
			</div>

			<!-- 内容 -->
			<div class="container-fluid" style="background-color: white;border: none">
				<div class="row">
					<div class="col-md-12">
						<#assign query = domain.query>
						<form action="${CONTEXT_PATH}scrap/search" class="form-horizontal form-bordered form-row-stripped" method="post" modelAttribute="domain" name="scrapForm" id="domain">
							<!-- 分页信息 -->
							<input id="page-no" type="hidden" name="page.pageNo" value="1">
							<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">

							<div class="form-body">
								<div class="form-group">
									<label class="control-label col-md-1" style="width:90px">SKU:</label>
									<div class="col-md-2" style="width:200px">
										<input class="form-control" type="text" name="query.sku" placeholder="多个逗号分开" value="${query.sku }">
									</div>
									<label class="control-label col-md-1" style="width:90px">状态:</label>
									<div class="col-md-2" style="width:150px">
										<input class="form-control" type="text" name="query.status" value="${query.status}">
									</div>
									<label class="control-label col-md-1" style="width:85px">报废单号:</label>
									<div class="col-md-2" style="width:300px">
										<input class="form-control" type="text" name="query.scrapNumber" placeholder="多个逗号分开" value="${query.scrapNumber }">
									</div>
									<label class="control-label col-md-1" style="width:85px">创建时间:</label>
									<div class="col-md-2" style="width:160px">
										<input class="form-control Wdate" type="text" name="query.fromCreationDate" placeholder="" readonly="readonly" value="${query.fromCreationDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
									<label class="control-label col-md-1" style="width:25px">到</label>
									<div class="col-md-2" style="width:160px">
										<input class="form-control Wdate" type="text" name="query.toCreationDate" placeholder="" readonly="readonly" value="${query.toCreationDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
									<label class="control-label col-md-1" style="width:90px">审核时间:</label>
									<div class="col-md-2" style="width:160px">
										<input class="form-control Wdate" type="text" name="query.fromVerifyTime" placeholder="" readonly="readonly" value="${query.fromVerifyTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
									<label class="control-label col-md-1" style="width:25px">到</label>
									<div class="col-md-2" style="width:160px">
										<input class="form-control Wdate" type="text" name="query.toVerifyTime" placeholder="" readonly="readonly" value="${query.toVerifyTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>

								</div>
								<div class="form-group">
									<label class="control-label col-md-1" style="width:90px">报废类型:</label>
									<div class="col-md-2" style="width:200px">
										<select class="form-control" name="query.isExpSku" value="${query.isExpSku}">
											<option ></option>
											<option <#if query.isExpSku == 0>selected</#if> value="0">普通</option>
											<option <#if query.isExpSku == 1>selected</#if> value="1">保质期</option>
										</select>
									</div>
									<label class="control-label col-md-1" style="width:90px">库存ID:</label>
									<div class="col-md-2" style="width:200px">
										<input class="form-control" type="text" name="query.stockIdStr" placeholder="多个逗号分开" value="${query.stockIdStr }">
									</div>
								</div>
							</div>
							<div>
								<div class="pull-left" style="margin-bottom: 10px;">
                                    <@header method="auth" authCode="DISCARD_ORDER_MANAGE_CREATE_DISCARD_ORDER">
									<a class="btn btn-default" href="${CONTEXT_PATH}scrap/createScrap">
										<span class="icon-s"></span> 新建报废单
									</a>
                                    </@header>
                                    <@header method="auth" authCode="DISCARD_ORDER_MANAGE_DISCARD_DISCARD_ORDER">
									<button type="button" class="btn btn-default" onclick="scrapList()">
										<i class="icon-"></i>废弃报废单
									</button>
                                    </@header>
                                    <@header method="auth" authCode="BATCH_SCRAP_AUDIT">
                                        <button type="button" id="batchAudit" class="btn blue">审批</button>
                                    </@header>



								</div>
								<div class="col-md-offset-8" style="text-align: right">
                                    <@header method="auth" authCode="DISCARD_ORDER_MANAGE_DOWNLOAD_DETAIL">
									<button type="button" class="btn btn-default" onclick="downloadDetails()">
										<i class="icon-download"></i> 导出明细
									</button>
                                    </@header>
									<button type="button" class="btn btn-default" onclick="formReset(this)">
										<i class="icon-refresh"></i>重置
									</button>
									<button type="submit" class="btn blue">
										<i class="icon-search"></i>查询
									</button>
								</div>
							</div>
						</form>
					</div>
					<br/>
				</div>

				<div class="row">
					<div class="col-md-12">
						<!-- 内容 -->
						<table class="table table-bordered table-hover table-condensed" id="inventoryTaskItem-list">
							<colgroup>
								<col width="5%" />
								<col width="7%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="7%" />
								<col width="7%" />
								<col width="7%" />
								<col width="7%" />
								<col width="8%" />
								<col width="8%" />
								<col width="8%" />
								<col width="10%" />
								<col width="10%" />
							</colgroup>
							<thead>
								<tr>
									<th><input type="checkbox" id="check-all" name="checkAll">全选</th>
									<th>报废单号</th>
									<th>含保质期sku</th>
									<th>SKU数量</th>
									<th>件数</th>
									<th>报废金额</th>
									<th>回收金额</th>
									<th>状态</th>
									<th>创建人</th>
									<th>创建时间</th>
									<th>审核人</th>
									<th>审核时间</th>
									<th>备注</th>
									<th>操作</th>
								</tr>
							</thead>
							<tbody>
									<#list domain.whScraps as scrap>
										<td>
											<input type="checkbox" class="ids" value="${scrap.id}" name="ids"> ${scrap.id}
										</td>
										<td>${scrap.scrapNumber}</td>
										<td>${(scrap.isExpSku==1)?string('是','否')}</td>
										<td>${scrap.skuNumber }</td>
										<td>${scrap.number }</td>
										<td>${scrap.scrapAmount }</td>
										<td>${scrap.recoveryAmount }</td>
										<td>${util('enumName', 'com.estone.loanedout.enums.ScrapStatus', scrap.status)}</td>
										<td>${util('name',scrap.creationUser)}</td>
										<td>${scrap.creationDate }</td>
									    <td>${scrap.reviewName}</td>
									    <td>${scrap.reviewDate }</td>
									    <td>${scrap.remark }</td>
										<td>
											<!--<#if scrap.status == 6>
												<a class="btn btn-xs btn-info" href="${CONTEXT_PATH}scrap/edit/${scrap.id}">
													编辑
												</a>
											<#else>-->
												<a class="btn btn-xs btn-default" href="${CONTEXT_PATH}scrap/whScrapItems/${scrap.id}">详情</a>
<!--											</#if>-->
											<a class="btn btn-xs btn-default" onclick="viewLog(${scrap.id}, 'whScrap')">
												日志
											</a>
										</td>
									</tr>
								</#list>
							</tbody>
						</table>
						<!-- 内容end -->
					</div>
				</div>
				<div id="fixed-bottom">
					<div id="pager"></div>
				</div>
			</div>
			<#include "/common/footer.html">
		</div>
		<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
		<script type="text/javascript">
			// 分页
			var total = "${domain.page.totalCount}";
			var pageNo = "${domain.page.pageNo}";
			var pageSize = "${domain.page.pageSize}";
			$("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });

			var heights = $("body").height();
			if(heights > 910) {
				$("#fixed-bottom").addClass("fixed-bottom-height")
			}
			// 状态
			var statuses =  ${domain.statuses};
			$("input[name='query.status']").select2({
				data : statuses,
				placeholder : "状态",
				multiple: true,
				allowClear : true
			});


			// 全选
			var checkAll = $("input[name='checkAll']");

			// 子选项
			var itemIds = $("input[name='ids']");
			checkAll.change(
				function() {
					itemIds.prop("checked", $(this).prop("checked"));
					itemIds.each(function() {
						var f = $(this).is(":checked");
						var checkClass = $(this).prop("class");
						$("." + checkClass).each(function() {
							$(this).prop("checked", f);
						})
					})
				}
			);
			// 获取选中的数据
			function getCheckedIds() {
				var checkedIds = $("input[name='ids']:checked");
				return checkedIds;
			}

			//导出
			function downloadDetails(){

				let uuid = getFileQueueUUID();

				var checkedIds = getCheckedIds();
				var diglog = dialog({
					title: '导出',
					width: 350,
					height:100,
					url: CONTEXT_PATH + "scrap/downloadmode?type=2&uuid=" + uuid,
					okValue: '确定',
					ok: function () {
						var exportWindow = $(this.iframeNode.contentWindow.document.body);

						$("#page-no").val("${domain.page.pageNo}");
						var submitForm = exportWindow.find("#submit-form");

						var exportType = submitForm.find("input[name='exportType']:checked").val();

						var submitFormParam = $('#domain').serialize();

						// 导出当前选择
						if(exportType == 3) {
							if(checkedIds.length == 0) {
								layer.alert("请选择要操作的数据");
								return false;
							}else{
								submitFormParam = submitFormParam + "&" +checkedIds.serialize();
							}
						}

						submitFormParam = submitFormParam + "&exportType=" + exportType + "&uuid=" + uuid;

						//还原分页
						// $("#page-no").val("${domain.page.pageNo}");

						downloadByPostForm(submitFormParam, CONTEXT_PATH + "scrap/download");

						$("#page-no").val("1");

						beginExport(uuid, '导出装车发货明细');
						setTimeout(function () {
							diglog.close().remove();
						}, 100);

						return true;
					},
					cancelValue: '取消',
					cancel: function () {}
				});
				diglog.show();
			}


			//废弃报废单
			function scrapList() {
				var checkedDatas = getCheckedIds();
				if(checkedDatas.length == 0) {
					layer.alert("请先勾选需要废弃的报废单", "error");
					return;
				}else {
					var ids = "";
					for(var i = 0; i < checkedDatas.length; i++) {
						var check = checkedDatas[i];
						var id = $(check).val();
						var status = $("#"+id+"_status").val();
						if (status == '3' || status =='7') {
							layer.alert("勾选的报废单已有已通过或已废弃，请重新选择", "error");
							return;
						}
						ids += id;
						if(i != checkedDatas.length - 1) {
							ids += ",";
						}
					}
					var diglog = dialog({
						title: '',
						content: '确定废弃已选中的报废单吗？',
						onshow: function() {},
						okValue: '确定',
						ok: function() {
							$.ajax({
								url:CONTEXT_PATH +"scrap/scrapList",
								type:"POST",
								data:{
									"ids":ids
								},
								success : function(response){
									var message = '';
									if (response.message != null) {
										message = response.message
									}
									if (response.status == '500') {
										customizeLayer('废弃失败：'+message, 'error');
										return;
									} else if (response.status == '200') {
										layer.confirm('废弃成功：'+message,{
											icon: 1,
											btn: ['确定']
										},function () {
											window.location.reload();
										})
										// setTimeout(function() {
										// 	window.location.reload();
										// }, 1500);
									}
								},
								error:function(){
									layer.alert('操作失败!', 'error');
								}
							});
							setTimeout(function() {
								diglog.close().remove();
							}, 100);
							return false;
						},
						cancelValue: '取消操作',
						cancel: function() {}
					}).showModal();
				}
			}

			//批量审核
			$("#batchAudit").click(function () {
				let checkedIds = getCheckedIds();

				if(checkedIds.length == 0) {
					layer.alert("请勾选要操作的报废单",'warning');
					return;
				}

				let ids = [];
				checkedIds.each(function() {
					if ($(this).is(":checked")) {
						var s= $(this).val();
						ids.push(s);
						ableMany=true;
					}
				});

				var diglog = dialog({
					title:"审批报废单据",
					width: 200,
					height: 30,
					url: CONTEXT_PATH + "scrap/batchAudit",
					okValue: '确定',
					ok: function () {

						var batchCommentWindow = $(this.iframeNode.contentWindow.document.body);
						var submitForm = batchCommentWindow.find("#submit-form");
						var auditValue = submitForm.find("input[name='auditValue']:checked").val();
						if(submitForm) {
							$.ajax({
								url: CONTEXT_PATH + "scrap/batchAudit",
								type: "POST",
								data: {ids: ids.toString(),auditValue:auditValue},
								success: function(data){
									if (data.status == 200) {
										alert(data.message);
										window.location.reload();
									} else {
										customizeLayer(data.message);
									}
								}
							});
						}
						setTimeout(function() {
							diglog.close().remove();
						}, 100);
						return false;
					},
					cancelValue:'取消',
					cancel: function () {}

				});
				diglog.show();
			})
		</script>
	</body>

</html>
