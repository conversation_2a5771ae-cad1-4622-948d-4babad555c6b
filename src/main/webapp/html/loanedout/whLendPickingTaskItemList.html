<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		.top_bar{
			position:fixed;top:0px;
		}
		#task-list td {
			vertical-align:middle;
		}
        .red {
            color: red;
            font-weight: bold;
        }
	</style>
</head>
<body>
<@header method="header" active="12070000" ><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">外借拣货单</a></li>
				<li class="active">外借拣货单详情</li>
			</ul>
		</div>
	</div>

	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
	<h2 class="header-title">外借拣货单详情</h2>
	<div class="row">
		<div class="col-md-8">
			<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
				<colgroup>
					<col width="10%" />
					<col width="10%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
				</colgroup>
				<thead>
					<tr>
						<th>外借单号</th>
						<th>sku</th>
						<th>库存ID</th>
						<th>数量</th>
						<th>已拣数量</th>
					</tr>
				</thead>
				<tbody>
					<#list domain.itemList as itemList>
						<tr>
							<td>${itemList.lendCheckOutCode }</td>
						</tr>
						<#list itemList.whLendItemList as lendItem>
							<tr>
								<td></td>
								<td>${lendItem.sku }</td>
								<td>${lendItem.stockId }</td>
								<td>${lendItem.quantity }</td>
								<td>${lendItem.pickQuantity!0 }</td>
							</tr>
						</#list>
					</#list>
				</tbody>
			</table>
		</div>
		<div class="col-md-4">
			<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
				<colgroup>
					<col width="10%" />
					<col width="10%" />
					<col width="5%" />
					<col width="10%" />
				</colgroup>
				<thead>
					<tr>
						<th>sku</th>
						<th>库存ID</th>
						<th>需拣</th>
						<th>已拣</th>
					</tr>
				</thead>
				<tbody>
					<#list domain.whLendItemList as itemList>
						<tr>
							<td>${itemList.sku }</td>
							<td>${itemList.stockId }</td>
							<td>${itemList.quantity }</td>
							<td>${itemList.pickQuantity!0 }</td>
						</tr>
					</#list>
				</tbody>
			</table>
		</div>
	</div>

</div>
<div id="fixed-bottom">
	<div id="pager"></div>
</div>
<#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript">
    var heights = $("body").height();
    if(heights>910){
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }

    $("#apvNo-id").val("${domain.query.apvNo}");

</script>
</body>
</html>