<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<link href="${CONTEXT_PATH}js/assets/plugins/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css"/>
	<link href="${CONTEXT_PATH}js/assets/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
	<link href="${CONTEXT_PATH}js/assets/css/style.css" rel="stylesheet" type="text/css"/>
	<link href="${CONTEXT_PATH}js/assets/css/style-non-responsive.css" rel="stylesheet" type="text/css"/>
	<link href="${CONTEXT_PATH}js/assets/css/themes/default.css" rel="stylesheet" type="text/css"/>
	<link href="${CONTEXT_PATH}js/dist/bootstrap-table/bootstrap-table.min.css" rel="stylesheet" media="screen"/>
	<link href="${CONTEXT_PATH}js/dist/jquery-ui-1.12.1/jquery-ui.min.css" rel="stylesheet" media="screen"/>
	<link href="${CONTEXT_PATH}js/dist/layer/skin/layer.css" rel="stylesheet" media="screen"/>
	<link href="${CONTEXT_PATH}js/assets/css/custom.css" rel="stylesheet" type="text/css"/>
	<link href="${CONTEXT_PATH}css/common.css?v=${.now?datetime}" rel="stylesheet" media="screen"/>
	
	<script src="${CONTEXT_PATH}js/dist/jquery.11.3.min.js"></script>
	<script src="${CONTEXT_PATH}js/dist/bootstrap/js/bootstrap.js"></script>
	<script src="${CONTEXT_PATH}js/dist/bootstrap-table/bootstrap-table.min.js"></script>
	<script src="${CONTEXT_PATH}js/dist/bootstrap-table/locale/bootstrap-table-zh-CN.js"></script>
	<script src="${CONTEXT_PATH}js/dist/chosen.jquery.js"></script>
	<script src="${CONTEXT_PATH}js/dist/jquery-ui-1.12.1/jquery-ui.min.js"></script>
	<script src="${CONTEXT_PATH}js/dist/layer/layer.js"></script>
	<script src="${CONTEXT_PATH}js/assets/plugins/jquery-validation/dist/jquery.validate.min.js" type="text/javascript" ></script>
	<script src="${CONTEXT_PATH}js/assets/plugins/jquery-validation/dist/additional-methods.min.js" type="text/javascript" ></script>
	<script src="${CONTEXT_PATH}js/assets/plugins/jquery-validation/localization/messages_zh.js" type="text/javascript" ></script>
	<script src="${CONTEXT_PATH}js/assets/plugins/jquery.blockui.min.js" type="text/javascript"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/dist/chosen.jquery.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/common.js?v=${.now?datetime}"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/app.js?v=${.now?datetime}"></script>
    
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
	<@header method="header" active="14050000"><#include "/ftl/header.ftl"></@header>

	 <div id="page" style="background-color: rgb(231, 237, 248)">
		 <div class="row">
			 <div class="col-md-12" style="padding: 0">
				 <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					 <li><a href="#">系统配置</a></li>
					 <li class="active">定时任务</li>
				 </ul>
			 </div>
		 </div>
		 <!-- 内容 -->
		 <div class="container-fluid" style="background-color: white;border: none">
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="col-md-12">
				<div class="span9 content" id="content">
	                <div class="row-fluid">
	                    <div class="block" style="min-height: 450px;">
	                        <div class="block-content collapse in">
	                            <div id="scheduler-toolbar">
	                                <div class="pull-right" style="margin-left: 10px;">
	                                    <button class="btn blue" id="search_btn"><i class="icon-search"></i>查询</button>
                                        <@header method="auth" authCode="SYSTEM_CONFIGURATION_SCHEDULE_ADD">
	                                    <button class="btn blue" id="add_btn"><i class="icon-plus"></i>添加</button>
	                                    </@header>
                                        <@header method="auth" authCode="SYSTEM_CONFIGURATION_SCHEDULE_EDIT">
                                        <button class="btn blue" id="edit_btn"><i class="icon-edit"></i>编辑</button>
	                                    </@header>
                                        <@header method="auth" authCode="SYSTEM_CONFIGURATION_SCHEDULE_DELETE">
                                        <button class="btn blue" id="delete_btn"><i class="icon-remove"></i>删除</button>
                                        </@header>
	                                </div>
	                            </div>
	                            <table id="scheduler-job-table"></table>
	                        </div>
	                    </div>
	                </div>
	            </div>
			</div>
		</div>

	</div>
	<#include "/common/footer.html">
	 </div>
	<div id="add-scheduler-dialog" style="display:none;">
		<div class="col-xs-12">
	        <form class="form-horizontal">
	            <div class="form-group">
					<label class="col-md-4 control-label" for="jobName">任务名</span>：
					</label>
					<div class="col-md-5">
						<input type="text" class="form-control" id="jobName">
					</div>
				</div>
	            <div class="form-group">
	                <label class="col-md-4 control-label" for="jobClass" >任务类</label>
	                <div class="col-md-5">
	                  <input type="text" class="form-control" id="jobClass">
	                </div>
	            </div>
	            <div class="form-group">
	                <label class="col-md-4 control-label" for="description" >任务描述</label>
	                <div class="col-md-5">
	                  <input type="text" class="form-control" id="description">
	                </div>
	            </div>
	            <div class="form-group">
	                <label class="col-md-4 control-label" for="cronExpression" >cron表达式</label>
	                <div class="col-md-5">
	                  <input type="text" class="form-control" id="cronExpression">
	                </div>
	            </div>
	            <div class="form-group">
	                <label class="col-md-4 control-label" for="enable" >是否启用</label>
	                <div class="col-md-5">
	                   <select class="form-control" style="width: 140px;margin-bottom: 3px;" id="enable" >
	                          <option value="">请选择</option>
	                          <option value="0">禁用</option>
	                          <option value="1">启用</option>
	                   </select>
	                </div>
	            </div>
	        </form>
        </div>
    </div>
    
    <div id="edit-scheduler-dialog" style="display:none;">
    </div>
    <script type="text/javascript" src="${CONTEXT_PATH}js/schedulerJob.js?v=${.now?datetime}"></script>
</body>
</html>