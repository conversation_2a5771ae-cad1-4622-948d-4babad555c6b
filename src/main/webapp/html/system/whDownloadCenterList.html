<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<#include "/common/include.html">
		<style type="text/css">
			.modal-body input {
				width: 240px;
			}
			
			.return-model {
				padding: 12px;
			}
			
			#return-info-btn {
				color: #ffff;
				background-color: #5bc0de;
				padding: 3px;
			}
		</style>
	</head>

	<body>
		<@header method="header" active="12070000"><#include "/ftl/header.ftl"></@header>

		<div id="page" style="background-color: rgb(231, 237, 248)">
			<div class="row">
				<div class="col-md-12" style="padding: 0">
					<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
						<li>
							<a href="#">下载中心</a>
						</li>
						<li class="active">下载中心</li>
					</ul>
				</div>
			</div>

			<!-- 内容 -->
			<div class="container-fluid" style="background-color: white;border: none">
				<div class="row">
					<div class="col-md-12">
						<#assign query = domain.query>
						<form action="${CONTEXT_PATH}system/whDownloadCenter/search" class="form-horizontal form-bordered form-row-stripped" method="post" modelAttribute="domain" name="packCarRecordForm" id="domain">
							<!-- 分页信息 -->
							<input id="page-no" type="hidden" name="page.pageNo" value="1">
							<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">

							<div class="form-body">
								<div class="form-group">
									<label class="control-label col-md-1">下载内容</label>
									<div class="col-md-3">
										<input class="form-control" id="queryContent" name="query.content" type="text" value="${query.content}">
									</div>
									<label class="control-label col-md-1">下载时间</label>
									<div class="col-md-3">
										<div class="input-group">
											<input class="form-control Wdate" type="text" name="query.fromLoadDate" placeholder="" readonly="readonly" value="${query.fromLoadDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
											<span class="input-group-addon">到</span>
											<input class="form-control Wdate" type="text" name="query.toLoadDate" placeholder="" readonly="readonly" value="${query.toLoadDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
										</div>
									</div>
									<label class="control-label col-md-1">下载人</label>
									<div class="col-md-3">
										<input class="form-control" name="query.createdBy" type="text" value="${query.createdBy}">
									</div>
								</div>
							</div>
							<div>
								<div class="col-md-offset-10" style="text-align: right">
									<button type="button" class="btn btn-default" onclick="formReset(this)">
										<i class="icon-refresh"></i> 重置
									</button>
									<button type="submit" class="btn blue">
										<i class="icon-search"></i> 查询
									</button>
								</div>
							</div>
						</form>
					</div>
					<br/>
				</div>

				<div class="row">
					<div class="col-md-12">
						<!-- 内容  -->
						<table class="table table-bordered table-hover table-condensed" id="packCarRecord-list">
							<colgroup>
								<col width="3%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="6%" />
								<col width="18%" />
								<col width="5%" />
								<col width="8%" />
								<col width="5%" />
							</colgroup>
							<thead>
								<tr>
									<th>ID</th>
									<th>下载内容</th>
									<th>类型</th>
									<th>状态</th>
									<th>备注</th>
									<th>文件名</th>
									<th>导出人</th>
									<th>导出时间</th>
									<th>操作</th>
								</tr>
							</thead>
							<tbody>
								<#list domain.whDownloadCenters as whDownloadCenter>
									<tr>
										<td>${whDownloadCenter.id}</td>
										<td>${util('enumName', 'com.estone.system.downloadcenter.enums.WhDownloadContentEnum', whDownloadCenter.content)}</td>
										<td>
											<#if whDownloadCenter.type == 1>
												全部导出
											<#else>
												条件导出
											</#if>
										</td>
										<td>${util('enumName', 'com.estone.system.downloadcenter.enums.WhDownloadStatusEnum', whDownloadCenter.status)}</td>
										<td>${whDownloadCenter.remark}</td>
										<td>
											<#if whDownloadCenter.url??>
												<#assign url = whDownloadCenter.url>
												<#assign index = url?lastIndexOf("/")>
												${url?substring(index+1)}
											</#if>
										</td>
										<td>${util('name',whDownloadCenter.createdBy)}</td>
										<td>${whDownloadCenter.createdDate}</td>
										<td>
											<#if whDownloadCenter.url??>
												<a class="btn btn-info btn-xs" href="${whDownloadCenter.url}">下载</a>
											</#if>
										</td>
									</tr>
								</#list>
							</tbody>
						</table>
						<!-- 内容end -->
					</div>
				</div>
				<div id="fixed-bottom">
					<div id="pager"></div>
				</div>
			</div>
			<#include "/common/footer.html">
		</div>

		<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
		<script type="text/javascript">
			// 分页
			var total = "${domain.page.totalCount}";
			var pageNo = "${domain.page.pageNo}";
			var pageSize = "${domain.page.pageSize}";
			$("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });

			var heights = $("body").height();
			if(heights > 910) {
				$("#fixed-bottom").addClass("fixed-bottom-height")
			}

			// 下载中间
			var contentList = ${domain.contentList};
			$("input[name='query.content']").select2({
				data: contentList,
				placeholder : "下载内容",
				allowClear: true
			});

			$.getJSON(CONTEXT_PATH + "system/saleusers/userByRole", function(json){
				if (json) {
					$("input[name='query.createdBy']").select2({
						data : json,
						placeholder : "下载人",
						allowClear : true
					});
				} else {
					$("input[name='query.createdBy']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
				}
			});

			function downloadPacDetail(id) {
				if (id) {
					window.open(CONTEXT_PATH + "scan/whPackCarRecord/downloadPac?id=" + id);
				}
			}

		</script>
	</body>

</html>