<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
	
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
</style>
</head>
<body>
<@header method="header" active="14050000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">系统配置</a></li>
					<li class="active">日志</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">

		<#assign query=domain.query>
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="portlet-body form" style="display: block;">
				<form action="${CONTEXT_PATH}system/logs/search"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="logForm" id ="domain">
					<!-- 分页信息 -->
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
					<div class="form-body">
						<div class="form-group">
							<#-- <label class="control-label col-md-1">每页数目</label>
							<div class="col-md-3">
								<input id="page-size" name="page.pageSize" class="form-control" value="${domain.page.pageSize}">
							</div> -->
						</div>
					</div>
					<div>
						<br />
						<div class="col-md-offset-10" style="text-align: right">
							<button type="submit" class="btn blue">
								<i class="icon-search"></i> 查询
							</button>
							<button type="button" class="btn btn-default">
								<i class="icon-refresh"></i> 重置
							</button>
						</div>
					</div>
				</form>
			</div>
			<br />
			<div class="row">
				<div id="fixedDiv" class="col-md-12">
					<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
						<colgroup>
							<col width="7%" />
							<col width="10%" />
							<col width="10%" />
							<col width="10%" />
							<col width="30%" />
						</colgroup>
						<thead>
						<tr>
							<th>时间</th>
							<th>创建人</th>
							<th>模块</th>
							<th>关联ID</th>
							<th>内容</th>
						</tr>
						</thead>
					</table>
				</div>
				<div class="col-md-12" id="task-list-warp">
					<form target="" id="main-form" name="mainForm" method="post" action="">
					<table class="table table-bordered table-condensed table-hover" id="task-list">
						<colgroup>
							<col width="7%" />
							<col width="10%" />
							<col width="10%" />
							<col width="10%" />
							<col width="30%" />
						</colgroup>
						<thead>
							<tr>
								<th>时间</th>
								<th>创建人</th>
								<th>模块</th>
								<th>关联ID</th>
								<th>内容</th>
							</tr>
						</thead>
						<tbody>
							<#list domain.whSystemLogs as log>
								<tr class="active">
									<td>${log.creationDate}</td>
									<td>${util('name',log.createBy)}</td>
									<td>${log.module}</td>
									<td>${log.relevanceId}</td>
									<td>${log.content}</td>
								</tr>
							</#list>
							<#if !(domain.whSystemLogs)??>
								<tr class="tc">
									<td colspan="99" style="text-align: center;">没有记录</td>
								</tr>
							</#if>
							<thead>
								<tr id="top_tool_bars" style="display: none">
									<th>
									<label class="checkbox-inline">
                                    	<input type="checkbox" name="checkAll"> 全选/反选
                                    </label>
									</th>
									<th>员工编号</th>
									<th>姓名</th>
									<th>职位</th>
									<th>操作</th>
								</tr>
							</thead>
						</tbody>
					</table>
					</form>
				</div>
			</div>
		</div>
			<div id="fixed-bottom">
				<div id="pager"></div>
			</div>
	</div>
	

	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
	<script type="text/javascript">
		// 分页
		var total = "${domain.page.totalCount}";
		var pageNo = "${domain.page.pageNo}";
		var pageSize = "${domain.page.pageSize}";
		$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});
	</script>
</body>
</html>