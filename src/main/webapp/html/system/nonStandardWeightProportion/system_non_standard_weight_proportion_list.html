<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
<@header method="header" active="14030000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">系统配置</a></li>
					<li class="active">交运重量拦截设置(不规则)</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<div class="row" style="padding-top: 80px;">
			<div class="col-md-12">
				<form action="${CONTEXT_PATH}system/nonStandardWeightProportion/update"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="standardWeightProportionForm">
					<div class="form-body" style="width: 1700px;margin: 0 auto;">
						<div style="width: 850px;margin: 0 auto;" class="col-md-6">
							<div style="">
								<div style="margin-bottom: 10px">
                                    <@header method="auth" authCode="FREIGHT_WEIGHT_INTERCEPT_CONFIGURATION_EDIT">
									<a class="btn btn-default" href='${CONTEXT_PATH}system/nonStandardWeightProportion/update?type=1'>
										<i class="icon-edit"></i> 编辑
									</a>
                                    </@header>
                                    <@header method="auth" authCode="FREIGHT_WEIGHT_INTERCEPT_CONFIGURATION_DELETE">
									<a class="btn btn-default" href='${CONTEXT_PATH}system/nonStandardWeightProportion/delete?type=1'>
										<i class="icon-remove"></i> 删除
									</a>
                                    </@header>
								</div>
								<br/>
								<#if domain.standardWeightDiffProportion.diffProportion == true >
									<span style="font-weight: bolder;font-size: large;color: red;">
									负误差 : 正误差 = ${domain.standardWeightDiffProportion.left } : ${domain.standardWeightDiffProportion.right}
									</span>
									<br/>
									<span style="font-size: 15px;color: grey;">
									说明：负标准重量差=正标准重量差*（负误差：正误差）
									</span>
								</#if>
								<#if domain.standardWeightDiffProportion.diffProportion == false >
									<span style="font-weight: bolder;font-size: large;color: red;">
									正负误差配置 : 独立配置
									</span>
								</#if>
							</div>
							<br/>
							<br/>
							<span style="font-weight: bolder;font-size: 16px;">标准重量计算拦截<span style="color: red;">(单品单件)</span></span>
							<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list">
								<colgroup>
									<col width="10%" />
									<col width="20%" />
									<col width="5%" />
									<col width="20%" />
									<col width="10%" />
									<col width="10%" />
									<col width="10%" />
									<col width="15%" />
								</colgroup>
								<thead>
								<tr>
									<th>负标准重量差</th>
									<th>标准重量下限g(≧)</th>
									<th></th>
									<th>标准重量上限g(＜)</th>
									<th>正标准重量差</th>
									<th>误差比例 (%)</th>
									<th>误差重量 (g)</th>
									<th>类型</th>
								</tr>
								</thead>
								<tbody>
								<#list domain.standardWeightProportions as item>
									<tr id="tr-item-${item_index}">
										<td>${item.leftDiff }</td>
										<td>
											${item.left }
										</td>
										<td>
											——
										</td>
										<td>
											${item.right }
										</td>
										<td>${item.rightDiff }</td>
										<td>
											${item.percent}
										</td>
										<td>
											${item.weightDiff}
										</td>
										<td>
											<#if item.isDiff == true>按重量 (g)</#if>
											<#if item.isDiff == false >按百分比 (%)</#if>
										</td>
									</tr>
								</#list>
								</tbody>
							</table>
						</div>
						<div style="width: 750px;margin-left: 100px;" class="col-md-6">
							<div style="">
								<div style="margin-bottom: 10px">
                                    <@header method="auth" authCode="FREIGHT_GROSS_WEIGHT_INTERCEPT_CONFIGURATION_EDIT">
									<a class="btn btn-default" href='${CONTEXT_PATH}system/nonStandardWeightProportion/update?type=2'>
										<i class="icon-edit"></i> 编辑
									</a>
                                    </@header>
                                    <@header method="auth" authCode="FREIGHT_GROSS_WEIGHT_INTERCEPT_CONFIGURATION_DELETE">
									<a class="btn btn-default" href='${CONTEXT_PATH}system/nonStandardWeightProportion/delete?type=2'>
										<i class="icon-remove"></i> 删除
									</a>
                                    </@header>
								</div>
								<br/>
								<#if domain.skuWeightDiffProportion.diffProportion == true>
									<span style="font-weight: bolder;font-size: large;color: red;">
									负误差 : 正误差 = ${domain.skuWeightDiffProportion.left } : ${domain.skuWeightDiffProportion.right}
									</span>
									<br/>
									<span style="font-size: 15px;color: grey;">
									说明：负净重差=正净重差*（负误差：正误差）
									</span>
								</#if>
								<#if domain.skuWeightDiffProportion.diffProportion == false >
									<span style="font-weight: bolder;font-size: large;color: red;">
									正负误差配置 : 独立配置
									</span>
								</#if>
							</div>
							<br/>
							<br/>
							<span style="font-weight: bolder;font-size: 16px;">净重计算拦截<span style="color: red;">(单品单件)</span></span>
							<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list">
								<colgroup>
									<col width="10%" />
									<col width="20%" />
									<col width="5%" />
									<col width="20%" />
									<col width="10%" />
									<col width="10%" />
									<col width="10%" />
									<col width="15%" />
								</colgroup>
								<thead>
								<tr>
									<th>负净重差</th>
									<th>净重下限g(≧)</th>
									<th></th>
									<th>净重上限g(＜)</th>
									<th>正净重差</th>
									<th>误差比例 (%)</th>
									<th>误差重量 (g)</th>
									<th>类型</th>
								</tr>
								</thead>
								<tbody>
								<#list domain.skuWeightProportions as item>
									<tr id="tr-item-${item_index}">
										<td>${item.leftDiff }</td>
										<td>
											${item.left }
										</td>
										<td>
											——
										</td>
										<td>
											${item.right }
										</td>
										<td>${item.rightDiff }</td>
										<td>
											${item.percent}
										</td>
										<td>
											${item.weightDiff}
										</td>
										<td>
											<#if item.isDiff == true>按重量 (g)</#if>
											<#if item.isDiff == false >按百分比 (%)</#if>
										</td>
									</tr>
								</#list>
								</tbody>
							</table>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>
			<div class="row" style="padding-top: 80px;">
				<div class="col-md-12">
					<div class="" style="width: 1700px;margin: 0 auto;">
						<div style="width: 850px;margin: 0 auto;" class="col-md-6">
							<div style="">
								<div style="margin-bottom: 10px">
                                    <@header method="auth" authCode="FREIGHT_GROSS_WEIGHT_INTERCEPT_CONFIGURATION_EDIT_TO_SINGLE_AND_MULTIPLY">
									<a class="btn btn-default" href='${CONTEXT_PATH}system/nonStandardWeightProportion/update?type=3'>
										<i class="icon-edit"></i> 编辑
									</a>
                                    </@header>
                                    <@header method="auth" authCode="FREIGHT_GROSS_WEIGHT_INTERCEPT_CONFIGURATION_DELETE_TO_SINGLE_AND_MULTIPLY">
									<a class="btn btn-default" href='${CONTEXT_PATH}system/nonStandardWeightProportion/delete?type=3'>
										<i class="icon-remove"></i> 删除
									</a>
                                    </@header>
								</div>
								<br/>
								<#if domain.smOrMmWeightDiffProportion.diffProportion == true >
									<span style="font-weight: bolder;font-size: large;color: red;">
									负误差 : 正误差 = ${domain.smOrMmWeightDiffProportion.left } : ${domain.smOrMmWeightDiffProportion.right}
									</span>
									<br/>
									<span style="font-size: 15px;color: grey;">
									说明：负净重差=正净重差*（负误差：正误差）
									</span>
								</#if>
								<#if domain.smOrMmWeightDiffProportion.diffProportion == false >
									<span style="font-weight: bolder;font-size: large;color: red;">
									正负误差配置 : 独立配置
									</span>
								</#if>
							</div>
							<br/>
							<br/>
							<span style="font-weight: bolder;font-size: 16px;">净重计算拦截<span style="color: red;">(单品/多品多件)</span></span>
							<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list">
								<colgroup>
									<col width="10%" />
									<col width="20%" />
									<col width="5%" />
									<col width="20%" />
									<col width="10%" />
									<col width="10%" />
									<col width="10%" />
									<col width="15%" />
								</colgroup>
								<thead>
								<tr>
									<th>负净重差</th>
									<th>净重下限g(≧)</th>
									<th></th>
									<th>净重上限g(＜)</th>
									<th>正净重差</th>
									<th>误差比例 (%)</th>
									<th>误差重量 (g)</th>
									<th>类型</th>
								</tr>
								</thead>
								<tbody>
								<#list domain.smOrMmWeightProportions as item>
									<tr id="tr-item-${item_index}">
										<td>${item.leftDiff }</td>
										<td>
											${item.left }
										</td>
										<td>
											——
										</td>
										<td>
											${item.right }
										</td>
										<td>${item.rightDiff }</td>
										<td>
											${item.percent}
										</td>
										<td>
											${item.weightDiff}
										</td>
										<td>
											<#if item.isDiff == true>按重量 (g)</#if>
											<#if item.isDiff == false >按百分比 (%)</#if>
										</td>
									</tr>
								</#list>
								</tbody>
							</table>
						</div>
						<div style="width: 750px;margin-left: 100px;" class="col-md-6">

						</div>
					</div>
				</div>
			</div>
		</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.region.js"></script>
	<script type="text/javascript">
	
	</script>
</body>
</html>