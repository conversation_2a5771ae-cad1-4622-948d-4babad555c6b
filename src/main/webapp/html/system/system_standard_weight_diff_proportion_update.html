<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
<@header method="header" active="14030000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">系统配置</a></li>
					<li class="active">标准重量偏差排除配置</li>
					<li class="active">编辑</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<!-- END PAGE HEADER-->
		<div class="row" style="padding-top: 80px;height: 760px">
			<div class="col-md-12">
				<form id="submit-form" action="${CONTEXT_PATH}system/standardWeightDiffProportion/update"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="standardWeightDiffProportionForm">
					<div class="form-body" style="width: 800px;margin: 0 auto">
						<input id="sku_weight_type" class=""name="type" value="1" type="hidden">
						<div style="margin-bottom: 10px">
							<button type="submit" class="btn blue">
								<i class="icon-save"></i> 保存
							</button>
							<a class="btn btn-default" href='${CONTEXT_PATH}system/standardWeightDiffProportion'>
								<i class="m-icon-swapleft"></i> 返回
							</a>
						</div>
						<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list">
							<colgroup>
								<col width="15%" />
								<col width="9%" />
								<col width="15%" />
								<col width="9%" />
								<col width="9%" />
								<col width="15%" />
								<col width="28%" />
							</colgroup>
							<thead>
								<tr>
									<th>标准重量下限g(≧)</th>
									<th></th>
									<th>标准重量上限g(＜)</th>
									<th>误差比例 (%)</th>
									<th>误差重量 (g)</th>
									<th>类型<br />
										<label class="radio-inline" title="按重量  (g)">
											<input id="" class="" autocomplete="off" name="isDiff" onclick="checkIsDiff(true)" value="true" type="radio">按重量  (g)
										</label>
										<label class="radio-inline" title="按百分比 (%)">
											<input id="" class="" autocomplete="off" name="isDiff" onclick="checkIsDiff(false)" value="false" type="radio">按百分比 (%)
										</label>
									</th>
									<th>操作</th>
								</tr>
							</thead>
							<tbody>
								<#list domain.standardWeightProportions as item>
									<tr id="tr-item-${item_index}">
										<td>
											<#if (item.isMin)?? && item.isMin == true >
												<input class="form-control input-xsmall item-left" readonly="true" placeholder="≧" name="standardWeightProportions[${item_index }].left" value="${item.left }"/>
											<#else >
												<input class="form-control input-xsmall item-left" onblur="checkNumber(this)" placeholder="≧" name="standardWeightProportions[${item_index }].left" value="${item.left }"/>
											</#if>
										</td>
										<td>
											<input type="hidden" name="standardWeightProportions[${item_index }].isMin" value="${item.isMin }" />
											<input type="hidden" name="standardWeightProportions[${item_index }].isMax" value="${item.isMax }" />
											——
										</td>
										<td>
											<#if (item.isMax)?? && item.isMax == true >
												<input class="form-control input-xsmall item-right" readonly="true" placeholder="＜" name="standardWeightProportions[${item_index }].right" value="${item.right }" />
											<#else >
												<input class="form-control input-xsmall item-right" onblur="checkNumber(this)" placeholder="＜" name="standardWeightProportions[${item_index }].right" value="${item.right }" />
											</#if>
										</td>
										<td>
											<input class="form-control input-xsmall item-percent" onblur="checkPercent(this)" placeholder="%" name="standardWeightProportions[${item_index }].percent" value="${item.percent}"/>
										</td>
										<td>
											<input class="form-control input-xsmall item-percent" onblur="checkNumber(this)" placeholder="g" name="standardWeightProportions[${item_index }].weightDiff" value="${item.weightDiff}"/>
										</td>
										<td>
											<#if item.isDiff == true>
												<label class="radio-inline" title="按重量  (g)">
													<input id="" class="isDiffTrue" autocomplete="off" name="standardWeightProportions[${item_index }].isDiff" value="true" checked="checked" type="radio">按重量  (g)
												</label>
												<label class="radio-inline" title="按百分比 (%)">
													<input id="" class="isDiffFalse" autocomplete="off" name="standardWeightProportions[${item_index }].isDiff" value="false" type="radio">按百分比 (%)
												</label>
											<#else >
												<label class="radio-inline" title="按重量  (g)">
													<input id="" class="isDiffTrue" autocomplete="off" name="standardWeightProportions[${item_index }].isDiff" value="true" type="radio">按重量  (g)
												</label>
												<label class="radio-inline" title="按百分比 (%)">
													<input id="" class="isDiffFalse" autocomplete="off" name="standardWeightProportions[${item_index}].isDiff" value="false" checked="checked" type="radio">按百分比 (%)
												</label>
											</#if>
										</td>
										<td>
											<#if (!(item.isMin)?? || item.isMin == false) && (!(item.isMax)?? || item.isMax == false)>
												<buttton type="button" class="btn btn-xs btn-default" onclick="removeTr(${item_index})">
												<span class="glyphicon glyphicon-remove"></span>  删除
												</buttton>
											</#if>
											<#if !(item.isMax)?? || item.isMax == false >
												<buttton type="button" class="btn btn-xs btn-default" onclick="addTr(${item_index})">
												<span class="glyphicon glyphicon-add"></span>  添加
												</buttton>
											</#if>
										</td>
									</tr>
								</#list>
							</tbody>
						</table>
						<br/>
						<br/>
						<br/>
						<table class="table table-striped table-bordered table-hover table-condensed mt10" id="">
							<colgroup>
								<col width="33%" />
								<col width="33%" />
								<col width="34%" />
							</colgroup>
							<tbody>
							<tr>
								<th style="background-color: #ccc;text-align: center">正负误差比设置<br/>(负误差 : 正误差)：</th>
								<td>
									<input class="form-control input-xsmall item-right" onblur="checkNumber(this)" name="standardWeightDiffProportion.left" value="${domain.standardWeightDiffProportion.left}" />
								</td>
								<td>
									<input class="form-control input-xsmall item-right" onblur="checkNumber(this)" name="standardWeightDiffProportion.right" value="${domain.standardWeightDiffProportion.right}" />
								</td>
							</tr>
							</tbody>
						</table>
					</div>
				</form>
			</div>
			<br/>
		</div>

	</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.region.js"></script>
	<script type="text/javascript">
	
	$(document).ready(function(){
  		var submitForm = $('#submit-form');
  		// 表单校验
  		submitForm.validate({
  			rules : {
  				
  			},
  			messages : {
  				
  			},
  			invalidHandler : function(event, validator) {
  				App.scrollTo(submitForm.find(".has-success:eq(0)"), -50);
  			},
  			submitHandler : function(form) {
  				
  				var length = $("#item-list tbody tr").length;
  				for (var i = 0; i < length; i++) {
  					var weightDiff = $("input[name='standardWeightProportions["+i+"].weightDiff']").val();
  					var percent = $("input[name='standardWeightProportions["+i+"].percent']").val();
  					var left = $("input[name='standardWeightProportions["+i+"].left']").val();
  					var right = $("input[name='standardWeightProportions["+i+"].right']").val();
  					
  					var isDiff = $("input[name='standardWeightProportions["+i+"].isDiff']:checked").val();
  						
  					if(isDiff == null || isDiff == ''){
  	  			  		layer.alert("类型不能为空!", 'error');
  	  			  		return false;
  	  			  	}
  					if(percent == '' && isDiff == 'false'){
  	  			  		layer.alert("误差比例 (%)不能为空!", 'error');
  	  			  		return false;
  	  			  	}
  					if(weightDiff == '' && isDiff == 'true'){
  	  			  		layer.alert("误差重量 (g)不能为空!", 'error');
  	  			  		return false;
  	  			  	}
  					if(left == ''){
  			  			layer.alert("区间上限不能为空!", 'error');
  			  			return false;
  			  		}
  					//if(right == '' && i < (length-1)){
  					if(right == ''){
  			  			layer.alert("区间下限不能为空!", 'error');
  			  			return false;
  			  		}
  					var percent = parseInt(percent);
  					var left = parseInt(left);
  					var right = parseInt(right);
  					if(left >= right && i < (length-1)){
  						layer.alert("区间上限不能大于或等于区间下限!", 'error');
  			  			return false;
  					}
  					if (i>0) { 
  						var lastRight = $("input[name='standardWeightProportions["+(i-1)+"].right']").val();
  						var lastRight = parseInt(lastRight);
  						if(left < lastRight){
  	  						layer.alert("区间上限不能小于上一条的区间下限!", 'error');
  	  			  			return false;
  	  					}else if (left > lastRight) {
  	  						layer.alert("区间未封闭，请补充!", 'error');
	  			  			return false;
						}
					}
				}

                var left = $("input[name='standardWeightDiffProportion.left']").val();
                var right = $("input[name='standardWeightDiffProportion.right']").val();
                if(left == null || left == '' || right == null || right == ''){
                    layer.alert("正负误差比不能为空!", 'error');
                    return false;
                }
                var right = parseInt(right);
                if (right==0){
                    layer.alert("正负误差比错误!", 'error');
                    return false;
				}
  				App.blockUI();
  				form.submit();
  				return false;
  			},
  			/* 重写错误显示消息方法,以layer.alert方式弹出错误消息 */
  			showErrors : function(errorMap, errorList) {
  				var msg = "";  
  				$.each(errorList, function(i, v) {  
  					msg += (v.message + "\r\n");  
  				});  
  				if (msg != "") layer.alert(msg,'error');
  			},
  			/* 失去焦点时不验证 */  
  			onfocusout : false 
  		});
		
	}); // end ready
	
	function removeTr(index){
		// 删除本行
		$("#tr-item-"+index).remove();
			
		// 重新编排
		$("#item-list tbody tr").each(function(i){
			refreshIndex(i, this);
		});
	}
	
	function addTr(index){
		var currentTr = $("#tr-item-"+index);
		var length = $("#item-list tbody tr").length;
		
		var appendTr = '<tr id="tr-item-'+length+'">'
				+'<td>'
					+'<input class="form-control input-xsmall item-left" onblur="checkNumber(this)" placeholder="≧" name="standardWeightProportions['+length+'].left" value=""/></td>'
				+'<td>'
					+'<input type="hidden" name="standardWeightProportions['+length+'].isMin" value="false" />'
					+'<input type="hidden" name="standardWeightProportions['+length+'].isMax" value="false" />'
					+'——'
				+'</td>'
				+'<td>'
					+'<input class="form-control input-xsmall item-right" onblur="checkNumber(this)" placeholder="＜" name="standardWeightProportions['+length+'].right" value="" />'
				+'</td>'
				+'<td>'
					+'<input class="form-control input-xsmall item-percent" onblur="checkPercent(this)" placeholder="%" name="standardWeightProportions['+length+'].percent" value=""/>'
				+'</td>'
				+'<td>'
					+'<input class="form-control input-xsmall item-percent" onblur="checkNumber(this)" placeholder="g" name="standardWeightProportions['+length+'].weightDiff" value=""/>'
				+'</td>'
				+'<td>'
					+'<label class="radio-inline" title="按重量  (g)">'
						+'<input id="" class="isDiffTrue" autocomplete="off" name="standardWeightProportions['+length+'].isDiff" value="true" checked="checked" type="radio">按重量  (g)'
					+'</label>'
					+'<label class="radio-inline" title="按百分比 (%)">'
						+'<input id="" class="isDiffFalse" autocomplete="off" name="standardWeightProportions['+length+'].isDiff" value="false" type="radio">按百分比 (%)'
					+'</label>'
				+'</td>'
				+'<td>'
					+'<buttton type="button" class="btn btn-xs btn-default" onclick="removeTr('+length+')">'
						+'<span class="glyphicon glyphicon-remove"></span>删除'
					+'</buttton>'
					+'<buttton type="button" class="btn btn-xs btn-default" onclick="addTr('+length+')">'
						+'<span class="glyphicon glyphicon-add"></span>添加'
					+'</buttton>'
				+'</td>'
				+'</tr>';
				
		currentTr.after(appendTr);
		
		// 重新编排
		$("#item-list tbody tr").each(function(i){
			refreshIndex(i, this);
		});
	}
	
	function checkPercent(obj){
  		var $this = $(obj);
  		if($this.val() == ''){
  			$this.val("");
  			return;
  		}
  		//var reg = /^\+?[1-9][0-9]*$/;
  		var reg = /^(\+)?\d+(\.\d+)?$/;
  		if (!reg.test($this.val())) {
  			//layer.alert("请输入正确的正整数", 'error');
  			layer.alert("只能输入正实数", 'error');
  			$this.val("");
  			return;
  		}
  		if ($this.val() > 100) {
  			layer.alert("百分比不能超过100", 'error');
  			$this.val("");
  			return;
  		}
	}
	
	function checkNumber(obj){
  		var $this = $(obj);
  		if($this.val() == ''){
  			$this.val("");
  			return;
  		}
  		var reg = /^\+?[1-9][0-9]*$/;
  		if (!reg.test($this.val())) {
  			layer.alert("请输入正确的正整数", 'error');
  			$this.val("");
  			return;
  		}
	}
	
	function checkIsDiff(flag) {
		var isDiffTrue = $(".isDiffTrue");
		var isDiffFalse = $(".isDiffFalse");
		if (flag) {
			for (var i = 0; i < isDiffTrue.length; i++) {
				var isDiff = isDiffTrue[i];
				$(isDiff).attr("checked",'checked'); 
			}
		}else{
			for (var i = 0; i < isDiffFalse.length; i++) {
				var isDiff = isDiffFalse[i];
				$(isDiff).attr("checked",'checked'); 
			}
		}
	}

	</script>
</body>
</html>