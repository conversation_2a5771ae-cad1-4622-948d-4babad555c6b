<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
	</style>
</head>
<body>
<@header method="header" active="14010000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">系统配置</a></li>
					<li class="active">入库异常类型配置</li>
					<li class="active">编辑</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
			<div class="row" style="padding-top: 20px;">
				<div class="col-md-12">
					<form action="${CONTEXT_PATH}system/exceptionTypeConfig"
						class="form-horizontal form-bordered form-row-stripped"
						method="post" modelAttribute="domain" name="standardWeightProportionForm">
						<div class="form-body">
							<div style="margin: 0 auto;height:700px;overflow-y: scroll;" class="col-md-6" id="list">
								<span style="font-weight: bolder;font-size: 16px;color: red;">异常类型配置</span>
								<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list">
									<colgroup>
										<col width="20%" />
										<col width="20%" />
										<col width="20%" />
										<col width="20%" />
										<col width="20%" />
									</colgroup>
									<thead>
									<tr>
										<th>异常类型</th>
										<th>异常编码</th>
										<th>添加人</th>
										<th>状态</th>
										<th>操作</th>
									</tr>
									</thead>
									<tbody>
										<#list domain.configs as item>
											<tr id="tr-item-${item_index}">
												<td><input class="form-control" name="name" value="${item.name }" disabled/></td>
												<td><input class="form-control" name="code" value="${item.code }" disabled/></td>
												<td class="user">
													${util('name', item.createdBy)}
													<input class="form-control hidden" name="createdBy" value="${item.createdBy }" disabled/>
												</td>
												<td>
													<select class="form-control" name="enable" disabled>
														<option value="true" <#if item.enable!true == true>selected</#if>>启用</option>
														<option value="false" <#if item.enable?? && item.enable == false>selected</#if>>禁用</option>
													</select>
												</td>
												<td>
													<@header method="auth" authCode="EXCEPTION_TYPE_CONFIG_EDIT">
													<button type="button" class="btn btn-xs btn-info" name="toEdit" onclick="edit('${item_index}')">
														<span class="icon-edit">编辑</span>
													</button>
													<button type="button" class="btn btn-xs btn-info" name="saveData" onclick="save('${item_index}')" disabled>
														<span class="icon-edit">保存</span>
													</button>
													</@header>
												</td>
											</tr>
										</#list>
										<@header method="auth" authCode="EXCEPTION_TYPE_CONFIG_ADD">
										<tr>
											<td colspan="5" onclick="addTr()">
												<button type="button" class="btn btn-xs btn-defaul">
													<span class="icon-plus" style="font-size: 25px;margin-top:8px;"></span>
												</button>
											</td>
										</tr>
										</@header>
									</tbody>
								</table>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.region.js"></script>
	<script type="text/javascript">

		function edit(index){
			var tr = $("#tr-item-" + index);
			// $(tr).find("input[name='name']").removeAttr("disabled");
			// $(tr).find("input[name='code']").removeAttr("disabled");
			$(tr).find("select[name='enable']").removeAttr("disabled");
			$(tr).find("button[name='toEdit']").attr("disabled", "disabled");
			$(tr).find("button[name='saveData']").removeAttr("disabled");
		}

		function save(index){
			var tr = $("#tr-item-" + index);
			let name = $(tr).find("input[name='name']").val();
			let code = $(tr).find("input[name='code']").val();
			let createdBy = $(tr).find("input[name='createdBy']").val();
			let enable = $(tr).find("select[name='enable']").val();

			if (name == undefined || name == ""){
				layer.alert("异常类型不能为空!", 'error');
				return false;
			}
			if (code == undefined || code == ""){
				layer.alert("异常编码不能为空!", 'error');
				return false;
			}
			if (enable == undefined || enable == ""){
				layer.alert("请选择启用或禁用!", 'error');
				return false;
			}
			let item = {
				"name": name,
				"code": code,
				"enable": enable,
				"createdBy": createdBy
			}
			$.ajax({
				type: "POST",
				url: CONTEXT_PATH + "system/exceptionTypeConfig/update",
				data: JSON.stringify(item),
				contentType: "application/json;charset=utf-8",
				beforeSend : function() {
					App.blockUI();
				},
				success: function (response) {
					App.unblockUI();
					if(response.status == "200"){
						layer.alert("保存成功");
						let body = response.body;
						if (body) {
							let userId = body.userId;
							let userName = body.userName;
							var html = "<input class=\"form-control hidden\" name=\"createdBy\" value=\""+userId+"\" disabled/>";
							$(tr).find(".user").html(userName + html);
							$(tr).find("input[name='name']").attr("disabled", "disabled");
							$(tr).find("input[name='code']").attr("disabled", "disabled");
							$(tr).find("select[name='enable']").attr("disabled", "disabled");
							$(tr).find("button[name='saveData']").attr("disabled", "disabled");
							$(tr).find("button[name='toEdit']").removeAttr("disabled");
						} else {
							window.location.reload();
						}
					}else {
						layer.alert("保存失败：" + response.message);
					}
				},
				error: function () {
					App.unblockUI();
					layer.alert('保存失败');
				}
			})
		}

		function addTr(){
			var index = $("#item-list tbody tr").size() - 1;
			var html = '<tr id="tr-item-' + index + '"> ' +
					' <td><input class="form-control" name="name" value=""/></td> ' +
					' <td><input class="form-control" name="code" value=""/></td> ' +
					' <td class="user"> ' +
					' </td> ' +
					' <td> ' +
					' <select class="form-control" name="enable"> ' +
					' <option value="true" selected>启用</option> ' +
					' <option value="false">禁用</option> ' +
					' </select> ' +
					' </td> ' +
					' <td> ' +
					' <button type="button" class="btn btn-xs btn-info" name="toEdit" onclick="edit(\'' + index + '\')" disabled> ' +
					' <span class="icon-edit">编辑</span> ' +
					' </button> ' +
					' <button type="button" class="btn btn-xs btn-info" name="saveData" onclick="save(\'' + index + '\')" > ' +
					' <span class="icon-edit">保存</span> ' +
					' </button> ' +
					' </td> ' +
					'</tr>';
			$("#item-list tbody tr:last").before(html);
			// 调用滚动到最底部的函数
			scrollToBottom('list');
		}

		// 定义一个函数，用于将滚动条滚动到指定容器的底部
		function scrollToBottom(containerId) {
			var container = document.getElementById(containerId);
			if (container) {
				// 将滚动容器的 scrollTop 属性设置为其 scrollHeight，实现滚动到最底部
				container.scrollTop = container.scrollHeight;
			}
		}

	</script>
</body>
</html>