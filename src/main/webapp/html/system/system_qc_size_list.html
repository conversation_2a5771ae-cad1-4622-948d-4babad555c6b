<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
<@header method="header" active="14030000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">系统配置</a></li>
					<li class="active">尺寸偏差配置</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<div class="row" style="padding-top: 80px;">
			<div class="col-md-12">
				<form action="${CONTEXT_PATH}system/skuQcSize/update"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="skuQcSizeForm">
					<div class="form-body" style="width: 1000px;margin: 0 auto;">
						<div style="">
							<div style="margin-bottom: 10px">
								<@header method="auth" authCode="SKU_QC_SIZE_CONFIG_EDIT">
								<a class="btn btn-default" href='${CONTEXT_PATH}system/skuQcSize/update/0'>
									<i class="icon-edit"></i> 编辑
								</a>
								</@header>
								<@header method="auth" authCode="SKU_QC_SIZE_CONFIG_DELETE">
								<a class="btn btn-default" href='${CONTEXT_PATH}system/skuQcSize/delete/0'>
									<i class="icon-remove"></i> 删除
								</a>
								</@header>
								单位：CM
							</div>
						</div>
						<br/>
						<br/>
						<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list">
							<colgroup>
								<col width="10%" />
								<col width="10%" />
								<col width="10%" />
							</colgroup>
							<thead>
							<tr>
								<th>长</th>
								<th>宽</th>
								<th>高</th>
							</tr>
							</thead>
							<tbody>
							<#list domain.skuQcSizes as item>
								<tr id="tr-item-${item_index}">

									<td>
										${item.length }
									</td>
									<td>
										${item.width }
									</td>
									<td>
										${item.height }
									</td>
								</tr>
							</#list>
							</tbody>
						</table>
						<br/>
						<br/>
						<br/>
						<br/>
						<div style="">
							<div style="margin-bottom: 10px">
								<@header method="auth" authCode="SKU_QC_SIZE_CONFIG_EDIT">
								<a class="btn btn-default" href='${CONTEXT_PATH}system/skuQcSize/update/1'>
									<i class="icon-edit"></i> 编辑
								</a>
								</@header>
								<@header method="auth" authCode="SKU_QC_SIZE_CONFIG_DELETE">
								<a class="btn btn-default" href='${CONTEXT_PATH}system/skuQcSize/delete/1'>
									<i class="icon-remove"></i> 删除
								</a>
								</@header>
								单位：CM³
							</div>
						</div>
					<br/>
					<br/>
						<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list">
							<colgroup>
								<col width="10%" />
								<col width="20%" />
								<col width="20%" />
								<col width="10%" />
								<col width="15%" />
							</colgroup>
							<thead>
							<tr>
								<th>SKU体积负误差</th>
								<th>SKU体积范围下限(≧)</th>
								<th>SKU体积范围上限(＜)</th>
								<th>误差百分比</th>
								<th>SKU体积正误差</th>

							</tr>
							</thead>
							<tbody>
								<#list domain.skuQcVolumeSizes as item>
								<tr id="tr-item-${item_index}">
									<td>
										${item.leftDiff }
									</td>
									<td>
										${item.left }
									</td>
									<td>
										${item.right }
									</td>
									<td>
										${item.rightProportion }
									</td>
									<td>
										${item.rightDiff }
									</td>
								</tr>
								</#list>
							</tbody>
						</table>
					</div>
				</form>
			</div>
			<br/>
		</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.region.js"></script>
	<script type="text/javascript">
	
	</script>
</body>
</html>