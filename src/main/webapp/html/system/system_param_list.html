<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<#include "/common/include.html">
<style type="text/css">
.modal-body input {
	width: 240px;
}

.top_bar {
	position: fixed;
	top: 0px;
}
#addModal{
	margin-top: 58px;
}
</style>
<link href="${CONTEXT_PATH}js/assets/plugins/bootstrap-switch/css/bootstrap-switch.min.css" rel="stylesheet">
</head>
<body>
	<@header method="header" active="14050000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">系统配置</a></li>
					<li class="active">参数</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">

		<br/>
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="col-md-12">
				<div class="tabbable tabbable-custom boxless">
					<ul class="nav nav-stacked col-md-1">
						<#if (domain.systemParams)?exists>
							<#list (domain.systemParams)?keys as key>
								<li class="<#if key_index == 0>active</#if>"><a href="#tab_${key_index}" data-toggle="tab">${key}</a></li>
							</#list>
						</#if>
					</ul>
					<div class="tab-content col-md-11">
						<#if (domain.systemParams)?exists>
							<#list (domain.systemParams)?keys as mapKey>
								<div class="tab-pane <#if mapKey_index == 0>active</#if>" id="tab_${mapKey_index}">
									<!-- BEGIN FILTER -->
									<h3 style="float:left;">${mapKey}</h3>&nbsp;&nbsp;&nbsp;&nbsp;
									<#list (domain.systemParams[mapKey])?keys as systemParamMapKey>
										<#if systemParamMapKey_index == 0>
                                            <@header method="auth" authCode="SYSTEM_CONFIGURATION_SET_CONFIG_ADD">
											<button type="button" class="btn blue" onclick="addModelShow('${domain.systemParams[mapKey][systemParamMapKey].paramCode}', '${domain.systemParams[mapKey][systemParamMapKey].paramName}')">
												<i class="icon-plus"></i> 新增
											</button>
											</@header>
											<#break >
										</#if>
									</#list>
									<hr/>
									<#list (domain.systemParams[mapKey])?keys as systemParamMapKey>
										<#assign systemParam = domain.systemParams[mapKey][systemParamMapKey]>
										<div class="col-md-4">
											<!-- BEGIN SAMPLE TABLE PORTLET-->
											<div class="portlet box green">
												<div class="portlet-title">
													<div class="caption"><i class="icon-certificate"></i>${systemParam.paramKey}</div>
													<div class="actions">
														<button class="btn default btn-sm" onclick="viewLog(${systemParam.paramId}, 'systemParam')"><span>日志</span></button>
<#--                                                        <@header method="auth" authCode="SYSTEM_CONFIGURATION_SET_CONFIG_FORBID">-->
														<a href="#" class="btn default btn-sm content-enabled"><span>${systemParam.paramEnabled ? string('禁用','启用')}</span></a>
<#--														</@header>-->
<#--                                                        <@header method="auth" authCode="SYSTEM_CONFIGURATION_SET_CONFIG_HIDDEN">-->
														<a href="#" class="btn default btn-sm content-display"><span>隐藏</span></a>
<#--														</@header>-->
<#--                                                        <@header method="auth" authCode="SYSTEM_CONFIGURATION_SET_CONFIG_DELETE">-->
														<a href="#" class="btn default btn-sm content-remove"><i class="icon-trash" style="color:#000 !important;"></i> </a>
<#--														</@header>-->
<#--														<@header method="auth" authCode="SYSTEM_CONFIGURATION_SET_CONFIG_EDIT">-->
														<a href="#" class="btn default btn-sm content-save"><i class="icon-pencil" style="color:#000 !important;"></i> <span>编辑</span></a>
<#--														</@header>-->
													</div>
												</div>
												<div class="portlet-body" style="display: block;">
													<div>
														<h5 style="float:left;">参数名称：</h5>
														<div>
															<input class="form-control param-key" value="${systemParam.paramKey}" disabled="disabled" />
															<input type="hidden" class="old-param-key" value="${systemParam.paramKey}" />
															<input type="hidden" class="old-param-value" value="${systemParam.paramValue}" />
															<input type="hidden" class="param-id" value="${systemParam.paramId}" />
														</div>
													</div>
													<br/>
													<div>
														<h5>参数值：</h5>
														<#if systemParam.paramType == 1>
															<textarea class="form-control param-value" rows="5" disabled="disabled">${systemParam.paramValue}</textarea>
														<#else >
															<input class="form-control param-value" disabled="disabled" value='${systemParam.paramValue}' />
														</#if>
													</div>
												</div>
											</div>
											<!-- END SAMPLE TABLE PORTLET-->
										</div>
									</#list>
									<!-- END FILTER -->
								</div>
							</#list>

						</#if>

					</div>
				</div>
			</div>
		</div>

	</div>
	<#include "/common/footer.html">
	</div>
	<div class="modal fade" tabindex="-1" role="dialog" id="addModal">
		<div class="modal-dialog" role="document">
		    <div class="modal-content">
		    	<div class="modal-header">
			        <button type="button" class="close" data-dismiss="modal" aria-label="Close"></button>
			        <h4 class="modal-title">新增参数</h4>
		    	</div>
		    	<div class="modal-body">
		    		<div class="row">
						<div class="form-group">
	                    	<label class="col-md-3 control-label">参数模块名称</label>
	                    	<div class="col-md-6">
		                    	<input type="text" class="form-control add-param-name" placeholder="参数模块名称">
	                       	</div>
	                       	<div class="col-md-3">
	                       		<input type="checkbox" class="switch add-param-enabled" data-on-text="启用" data-off-text="禁用">
	                       	</div>
	                    </div>
	                    <br/><br/>
	                    <div class="form-group">
	                    	<label class="col-md-3 control-label">参数模块CODE</label>
	                    	<div class="col-md-6">
                           		<input type="text" class="form-control add-param-code" placeholder="参数模块CODE">
	                       	</div>
	                       	<div class="col-md-3">
	                       		<input type="checkbox" class="switch add-param-display" data-on-text="显示" data-off-text="隐藏">
	                       	</div>
	                    </div>
	                    <br/><br/>
	                    <div class="form-group">
	                    	<label class="col-md-3 control-label">参数名称</label>
	                    	<div class="col-md-9">
		                    	<input type="text" class="form-control add-param-key" placeholder="参数名称">
	                       	</div>
	                    </div>
	                    <br/><br/>
	                    <div class="form-group">
	                    	<label class="col-md-3 control-label">参数值</label>
	                    	<div class="col-md-6">
		                    	<input type="text" class="form-control text-yes add-param-value" placeholder="参数值">
	                       		<textarea class="form-control text-no add-param-value" rows="5" style="display:none;" placeholder="参数值"></textarea>
	                       	</div>
	                       	<div class="col-md-3">
	                       		<input type="checkbox" class="switch add-param-type" data-on-text="单行" data-off-text="多行">
	                       	</div>
	                    </div>
		      	</div>
		      	<div class="modal-footer">
			        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
			        <button type="button" onclick="saveParam()" class="btn btn-primary">保存</button>
		      	</div>
		    </div><!-- /.modal-content -->
		</div><!-- /.modal-dialog -->
	</div><!-- /.modal -->

	<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/bootstrap-switch/js/bootstrap-switch.min.js"></script>
	<script type="text/javascript">
		$(".content-save").click(function() {
			var $this = $(this);
			var span = $this.find("span").text();
			var div = $this.parent().parent().parent();
			if (span == "编辑") {
				$this.find("span").text("保存");
				$this.find("i").removeClass("icon-pencil").addClass("icon-save");
				$(div).find("input").attr("disabled", false);
				$(div).find("textarea").attr("disabled", false);
			} else {
				var param = {};
				param['systemParam.paramId'] = $(div).find(".param-id").val();
				param['systemParam.paramValue'] = $(div).find(".param-value").val();
				param['systemParam.paramKey'] = $(div).find(".param-key").val();
				param['systemParam.oldParamKey'] = $(div).find(".old-param-key").val();
				param['systemParam.oldParamValue'] = $(div).find(".old-param-value").val();
				$.post(CONTEXT_PATH + "system/params/update", param, function(data) {
					if (data.status == 200) {
						$this.find("span").text("编辑");
						$this.find("i").removeClass("icon-save").addClass("icon-pencil");
						$(div).find("input").attr("disabled", true);
						$(div).find("textarea").attr("disabled", true);
						layer.alert("修改成功！");
					} else {
						customizeLayer(data.message, "error");
					}
				});
			}
		});

		// Handles Bootstrap switches
	    var handleBootstrapSwitch = function() {
	        if (!$().bootstrapSwitch) {
	            return;
	        }
	        $('.switch').bootstrapSwitch();
	    };
	    
	    handleBootstrapSwitch();
	    
	    $('.add-param-type').on('switchChange.bootstrapSwitch', function (element, arguments) {  
            if(arguments){
            	$('.text-yes').hide();
            	$('.text-no').show();
            } else {
            	$('.text-yes').show();
            	$('.text-no').hide();
            }
        });
	    
	    function addModelShow(code, name){
			$(".add-param-code").val(code);
			$(".add-param-name").val(name);
			$("#addModal").modal('show');
		}
	    
	    function saveParam(){
	    	var param = {};
			param['systemParam.paramName'] = $(".add-param-name").val();
			param['systemParam.paramCode'] = $(".add-param-code").val();
			param['systemParam.paramKey'] = $(".add-param-key").val();
			param['systemParam.paramValue'] = $(".add-param-value:visible").val();
			param['systemParam.paramEnabled'] = $(".add-param-enabled").val();
			param['systemParam.paramDisplay'] = $(".add-param-display").val();
			param['systemParam.paramType'] = $(".add-param-type:checked").length ? 1 : 5; // 5单行 1多行
	    	$.post(CONTEXT_PATH + "system/params/create", param, function(data) { 
				if (data.status == 200) {
					layer.alert("添加成功！");
					$("#addModal").modal('hide');
					setTimeout("location.reload()",500);
				} else {
					customizeLayer(data.message, "error");
				}
			});
	    }
	</script>
</body>
</html>