<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
<@header method="header" active="14030000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">系统配置</a></li>
					<li class="active">尺寸偏差配置</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<!-- END PAGE HEADER-->
		<div class="row" style="padding-top: 80px;">
			<div class="col-md-12">
				<form id="submit-form" action="${CONTEXT_PATH}system/skuQcSize/update/0"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="skuQcSizeForm">
					<div class="form-body" style="width: 500px;margin: 0 auto">
						<input id="sku_weight_type" class=""name="type" value="2" type="hidden">
						<div style="margin-bottom: 10px">
							<button type="submit" class="btn blue">
								<i class="icon-save"></i> 保存
							</button>
							<a class="btn btn-default" href='${CONTEXT_PATH}system/skuQcSize'>
								<i class="m-icon-swapleft"></i> 返回
							</a>
						</div>
						<br/>
						<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list">
							<colgroup>
								<col width="20%" />
								<col width="20%" />
								<col width="20%" />
								<col width="40%" />
							</colgroup>
							<thead>
							<tr>
								<th>长</th>
								<th>宽</th>
								<th>高</th>
								<th>操作</th>
							</tr>
							</thead>
							<tbody>
								<#list domain.skuQcSizes as item>
									<tr id="tr-item-${item_index}">
										<td>
											<input class="form-control input-xsmall item-percent" onblur="checkNumber(this)" placeholder="cm" name="skuQcSizes[${item_index }].length" value="${item.length}"/>
										</td>
										<td>
											<input class="form-control input-xsmall item-percent" onblur="checkNumber(this)" placeholder="cm" name="skuQcSizes[${item_index }].width" value="${item.width}"/>
										</td>
										<td>
											<input class="form-control input-xsmall item-percent" onblur="checkNumber(this)" placeholder="cm" name="skuQcSizes[${item_index }].height" value="${item.height}"/>
										</td>


										<td>
												<buttton type="button" class="btn btn-xs btn-default" onclick="removeTr(${item_index})">
													<span class="glyphicon glyphicon-remove"></span>  删除
												</buttton>

												<buttton type="button" class="btn btn-xs btn-default" onclick="addTr(${item_index})">
													<span class="glyphicon glyphicon-add"></span>  添加
												</buttton>
										</td>
									</tr>
								</#list>
							</tbody>
						</table>
					</div>
				</form>
			</div>
			<br/>
		</div>

	</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.region.js"></script>
	<script type="text/javascript">

	$('#submit-form').submit(function(form) {
		var length = $("#item-list tbody tr").length;
		for (var i = 0; i < length; i++) {
			debugger;
			var lengthVal = $("input[name='skuQcSizes["+i+"].length']").val();
			var width = $("input[name='skuQcSizes["+i+"].width']").val();
			var height = $("input[name='skuQcSizes["+i+"].height']").val();

			if(lengthVal == undefined || lengthVal == ''){
				layer.alert("长不能为空!", 'error');
				return false;
			}
			if(width == undefined || width == ''){
				layer.alert("宽不能为空!", 'error');
				return false;
			}
			if(height == undefined || height == ''){
				layer.alert("高不能为空!", 'error');
				return false;
			}
		}
		App.blockUI();
		form.submit();
		return true;
	});

	function removeTr(index){
		// 删除本行
		$("#tr-item-"+index).remove();
			
		// 重新编排
		$("#item-list tbody tr").each(function(i){
			refreshIndex(i, this);
		});
	}
	
	function addTr(index){
		var currentTr = $("#tr-item-"+index);
		var length = $("#item-list tbody tr").length;
		
		var appendTr = '<tr id="tr-item-'+length+'">'
				+'<td >'
					+'<input class="form-control input-xsmall item-percent" onblur="checkNumber(this)" placeholder="cm" name="skuQcSizes['+length+'].length" value=""/>'
				+'</td>'
				+'<td >'
				+'<input class="form-control input-xsmall item-percent" onblur="checkNumber(this)" placeholder="cm" name="skuQcSizes['+length+'].width" value=""/>'
				+'</td>'
				+'<td >'
				+'<input class="form-control input-xsmall item-percent" onblur="checkNumber(this)" placeholder="cm" name="skuQcSizes['+length+'].height" value=""/>'
				+'</td>'
				+'<td>'
				+'<buttton type="button" class="btn btn-xs btn-default" onclick="removeTr('+length+')">'
				+'<span class="glyphicon glyphicon-remove"></span>删除'
				+'</buttton>'
				+'<buttton type="button" class="btn btn-xs btn-default" onclick="addTr('+length+')">'
				+'<span class="glyphicon glyphicon-add"></span>添加'
				+'</buttton>'
				+'</td>'
				+'</tr>';
				
		currentTr.after(appendTr);
		
		// 重新编排
		$("#item-list tbody tr").each(function(i){
			refreshIndex(i, this);
		});
	}
	
	function checkNumber(obj){
  		var $this = $(obj);
  		if($this.val() == ''){
  			$this.val("");
  			return;
  		}
  		var reg = /^\+?[1-9][0-9]*$/;
  		if (!reg.test($this.val())) {
  			layer.alert("请输入正确的正整数", 'error');
  			$this.val("");
  			return;
  		}
	}

	</script>
</body>
</html>