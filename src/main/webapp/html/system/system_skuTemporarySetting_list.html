<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <#include "/common/include.html">
    <style type="text/css">
        .modal-body input {
            width: 240px;
        }
        .top_bar{
            position:fixed;top:0px;
        }

        #item-list tbody {
            display:block;
            height:300px;
            overflow-y:scroll;
            text-align: center;
        }

        #item-list thead, tbody tr {
            display: table;
            width: 800px;
            table-layout: fixed;
        }
        #task-list td {
            vertical-align:middle;
        }
    </style>
</head>
<body>
<@header method="header" active="14010000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li><a href="#">系统配置</a></li>
                <li class="active">SKU临期配置</li>
            </ul>
        </div>
    </div>
    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">
        <div class="row" style="padding-top: 80px;height: 760px">
            <div class="col-md-12">
                <form action="${CONTEXT_PATH}system/skuTemporarySetting/update"
                      class="form-horizontal form-bordered form-row-stripped"
                      method="post" modelAttribute="domain" name="skuTemporarySettingForm">
                    <div class="form-body" style="width: 800px;margin: 0 auto;">
                        <div style="margin-bottom: 10px">
                            <@header method="auth" authCode="SKU_TEMP_CONFIGURATION_EDIT">
                            <a class="btn btn-default" href='${CONTEXT_PATH}system/skuTemporarySetting/update'>
                                <i class="icon-edit"></i> 编辑
                            </a>
                            </@header>
                            <@header method="auth" authCode="SKU_TEMP_CONFIGURATION_DELETE">
                            <a class="btn btn-default" href='${CONTEXT_PATH}system/skuTemporarySetting/delete'>
                                <i class="icon-remove"></i> 删除
                            </a>
                            </@header>
                        </div>

                        <div>
                            <table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list">
                                <colgroup>
                                    <col width="40%" />
                                    <col width="20%" />
                                    <col width="20%" />
                                    <col width="20%" />
                                </colgroup>
                                <thead>
                                <tr>
                                    <th>保质期天数范围</th>
                                    <th>促销系数</th>
                                    <th>临期系数</th>
                                    <th>限制入库系数</th>
                                </tr>
                                </thead>
                                <tbody>
                                <#list domain.skuTemporarySettings as item>
                                    <tr id="tr-item-${item_index}">
                                        <#if (item_index == domain.skuTemporarySettings?size-1) && !(item.right)??>
                                            <td>${item.left }以上</td>
                                        <#else >
                                            <td>${item.left } ～ ${item.right }</td>
                                        </#if>
                                        <td>${item.promotionFactor}%</td>
                                        <td>${item.expiryFactor }%</td>
                                        <td>${item.restrictedStorageFactor }%</td>
                                    </tr>
                                </#list>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </form>
            </div>

        </div>

    </div>
    <#include "/common/footer.html">
</div>
</body>
</html>