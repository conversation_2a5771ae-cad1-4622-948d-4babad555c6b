<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<#include "/common/include.html">
<style type="text/css">

	.div_data td {
		vertical-align:middle;
	}

	.div_data input{
		width: 50px;
		float: left;
	}

	.div_data{
		width: 380px;
		text-align: center;
		display: inline-block;
		vertical-align: top;
	}

	#item-list-yc thead th{
		background-color: #b6d2fe;
	}
	#item-list-jk thead th{
		background-color: #03b8cf;
	}
	#item-list-xw thead th{
		background-color: #38b03f;
	}
	#item-list-yw thead th{
		background-color: #f1a325;
	}
	#item-list-xbw thead th{
		background-color: #ea644a;
	}
	#item-list-xc thead th{
		background-color: #a9d494;
	}
	#item-list-kc thead th{
		background-color: #a29bfe;
	}
</style>
</head>
<body>
<@header method="header" active="14010000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">系统配置</a></li>
					<li class="active">服装差异配置</li>
					<li class="active">编辑</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<div class="row" style="padding-top: 30px;height:780px;overflow-y: auto;white-space: nowrap;overflow-x: scroll;width:100%;vertical-align: top;">
			<div class="col-md-12">
				<form id="submit-form" action="${CONTEXT_PATH}system/skuClothingSizeDiff/update"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="skuClothingSizeDiffForm">
					<div class="form-body" style="margin: 0 auto;">
						<div style="margin-left: 30px;margin-bottom: 30px;">
							<a class="btn btn-primary" onclick="save()">
								<i class="icon-save"></i> 保存
							</a>&nbsp;&nbsp;&nbsp;&nbsp;
							<a class="btn btn-default" href='${CONTEXT_PATH}system/skuClothingSizeDiff'>
								<i class="m-icon-swapleft"></i> 返回
							</a>
						</div>
						<div class="div_data" id="YC">
							<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list-yc">
								<colgroup>
									<col width="20%" />
									<col width="20%" />
									<col width="20%" />
									<col width="20%" />
									<col width="20%" />
								</colgroup>
								<thead>
								<tr>
									<th>负误差</th>
									<th colspan="2">衣长(CM)</th>
									<th>正误差</th>
									<th>操作</th>
								</tr>
								</thead>
								<tbody>
								<#if domain.clothingSizeSizeMap['YC']??>
									<#list domain.clothingSizeSizeMap['YC'] as item>
										<tr id="tr-item-yc-${item_index}">
											<td>
												<input class="form-control" onblur="checkNumber(this)" name="leftDiff" value="${item.leftDiff }" />
											</td>
											<td>
												<input hidden readonly name="clothingAttrCode" value="YC"/>
												<#if (item.isMin)?? && item.isMin == true>
													<input class="form-control" readonly="true" placeholder="≧" name="left" value="${item.left }"/>
													<input hidden readonly name="isMin" value="true"/>
												<#else >
													<input class="form-control" onblur="checkNumber(this)" placeholder="≧" name="left" value="${item.left }"/>
												</#if>
											</td>
											<td>
												<input class="form-control"  onblur="checkNumber(this)" placeholder="＜" name="right" value="${item.right }" />
											</td>
											<td>
												<input class="form-control" onblur="checkNumber(this)" name="rightDiff" value="${item.rightDiff }" />
											</td>
											<td>
<!--												<#if (!(item.isMin)?? || item.isMin == false)>-->
													<buttton type="button" class="btn btn-xs btn-default" onclick="removeTr('yc', ${item_index})">
														删除
													</buttton>
<!--												</#if>-->
												<buttton type="button" class="btn btn-xs btn-default" onclick="addTr('yc', ${item_index})">
													添加
												</buttton>
											</td>
										</tr>
									</#list>
								</#if>
								<tr>
									<td colspan="5" onclick="addTr2('yc', this)">
										<buttton type="button" class="btn btn-xs btn-defaul" >
											<span class="icon-plus" style="font-size: 25px;margin-top:8px;"></span>
										</buttton>
									</td>
								</tr>
								</tbody>
							</table>
						</div>
						<div class="div_data" id="JK">
							<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list-jk">
								<colgroup>
									<col width="20%" />
									<col width="20%" />
									<col width="20%" />
									<col width="20%" />
									<col width="20%" />
								</colgroup>
								<thead>
								<tr>
									<th>负误差</th>
									<th colspan="2">肩宽(CM)</th>
									<th>正误差</th>
									<th>操作</th>
								</tr>
								</thead>
								<tbody>
								<#if domain.clothingSizeSizeMap['JK']??>
									<#list domain.clothingSizeSizeMap['JK'] as item>
									<tr id="tr-item-jk-${item_index}">
										<td>
											<input class="form-control" onblur="checkNumber(this)" name="leftDiff" value="${item.leftDiff }" />
										</td>
										<td>
											<input hidden readonly name="clothingAttrCode" value="JK"/>
											<#if (item.isMin)?? && item.isMin == true>
											<input class="form-control" readonly="true" placeholder="≧" name="left" value="${item.left }"/>
											<input hidden readonly name="isMin" value="true"/>
											<#else >
											<input class="form-control" onblur="checkNumber(this)" placeholder="≧" name="left" value="${item.left }"/>
											</#if>
										</td>
										<td>
											<input class="form-control"  onblur="checkNumber(this)" placeholder="＜" name="right" value="${item.right }" />
										</td>
										<td>
											<input class="form-control" onblur="checkNumber(this)" name="rightDiff" value="${item.rightDiff }" />
										</td>
										<td>
											<#if (!(item.isMin)?? || item.isMin == false)>
											<buttton type="button" class="btn btn-xs btn-default" onclick="removeTr('jk', ${item_index})">
												删除
											</buttton>
											</#if>
										<buttton type="button" class="btn btn-xs btn-default" onclick="addTr('jk', ${item_index})">
											添加
										</buttton>
										</td>
									</tr>
									</#list>
								</#if>
								<tr>
									<td colspan="5" onclick="addTr2('jk', this)">
										<buttton type="button" class="btn btn-xs btn-defaul" >
											<span class="icon-plus" style="font-size: 25px;margin-top:8px;"></span>
										</buttton>
									</td>
								</tr>
								</tbody>
							</table>
						</div>
						<div class="div_data" id="XW">
							<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list-xw">
								<colgroup>
									<col width="20%" />
									<col width="20%" />
									<col width="20%" />
									<col width="20%" />
									<col width="20%" />
								</colgroup>
								<thead>
								<tr>
									<th>负误差</th>
									<th colspan="2">胸围(CM)</th>
									<th>正误差</th>
									<th>操作</th>
								</tr>
								</thead>
								<tbody>
								<#if domain.clothingSizeSizeMap['XW']??>
									<#list domain.clothingSizeSizeMap['XW'] as item>
									<tr id="tr-item-xw-${item_index}">
										<td>
											<input class="form-control" onblur="checkNumber(this)" name="leftDiff" value="${item.leftDiff }" />
										</td>
										<td>
											<input hidden readonly name="clothingAttrCode" value="XW"/>
											<#if (item.isMin)?? && item.isMin == true>
											<input class="form-control" readonly="true" placeholder="≧" name="left" value="${item.left }"/>
											<input hidden readonly name="isMin" value="true"/>
											<#else >
											<input class="form-control" onblur="checkNumber(this)" placeholder="≧" name="left" value="${item.left }"/>
											</#if>
										</td>
										<td>
											<input class="form-control"  onblur="checkNumber(this)" placeholder="＜" name="right" value="${item.right }" />
										</td>
										<td>
											<input class="form-control" onblur="checkNumber(this)" name="rightDiff" value="${item.rightDiff }" />
										</td>
										<td>
											<#if (!(item.isMin)?? || item.isMin == false)>
											<buttton type="button" class="btn btn-xs btn-default" onclick="removeTr('xw', ${item_index})">
												删除
											</buttton>
											</#if>
										<buttton type="button" class="btn btn-xs btn-default" onclick="addTr('xw', ${item_index})">
											添加
										</buttton>
										</td>
									</tr>
									</#list>
								</#if>
								<tr>
									<td colspan="5" onclick="addTr2('xw', this)">
										<buttton type="button" class="btn btn-xs btn-defaul" >
											<span class="icon-plus" style="font-size: 25px;margin-top:8px;"></span>
										</buttton>
									</td>
								</tr>
								</tbody>
							</table>
						</div>
						<div class="div_data" id="YW">
							<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list-yw">
								<colgroup>
									<col width="20%" />
									<col width="20%" />
									<col width="20%" />
									<col width="20%" />
									<col width="20%" />
								</colgroup>
								<thead>
								<tr>
									<th>负误差</th>
									<th colspan="2">腰围(CM)</th>
									<th>正误差</th>
									<th>操作</th>
								</tr>
								</thead>
								<tbody>
								<#if domain.clothingSizeSizeMap['YW']??>
									<#list domain.clothingSizeSizeMap['YW'] as item>
									<tr id="tr-item-yw-${item_index}">
										<td>
											<input class="form-control" onblur="checkNumber(this)" name="leftDiff" value="${item.leftDiff }" />
										</td>
										<td>
											<input hidden readonly name="clothingAttrCode" value="YW"/>
											<#if (item.isMin)?? && item.isMin == true>
											<input class="form-control" readonly="true" placeholder="≧" name="left" value="${item.left }"/>
											<input hidden readonly name="isMin" value="true"/>
											<#else >
											<input class="form-control" onblur="checkNumber(this)" placeholder="≧" name="left" value="${item.left }"/>
											</#if>
										</td>
										<td>
											<input class="form-control"  onblur="checkNumber(this)" placeholder="＜" name="right" value="${item.right }" />
										</td>
										<td>
											<input class="form-control" onblur="checkNumber(this)" name="rightDiff" value="${item.rightDiff }" />
										</td>
										<td>
											<#if (!(item.isMin)?? || item.isMin == false)>
											<buttton type="button" class="btn btn-xs btn-default" onclick="removeTr('yw', ${item_index})">
												删除
											</buttton>
											</#if>
										<buttton type="button" class="btn btn-xs btn-default" onclick="addTr('yw', ${item_index})">
											添加
										</buttton>
										</td>
									</tr>
									</#list>
								</#if>
								<tr>
									<td colspan="5" onclick="addTr2('yw', this)">
										<buttton type="button" class="btn btn-xs btn-defaul" >
											<span class="icon-plus" style="font-size: 25px;margin-top:8px;"></span>
										</buttton>
									</td>
								</tr>
								</tbody>
							</table>
						</div>
						<div class="div_data" id="XBW">
							<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list-xbw">
								<colgroup>
									<col width="20%" />
									<col width="20%" />
									<col width="20%" />
									<col width="20%" />
									<col width="20%" />
								</colgroup>
								<thead>
								<tr>
									<th>负误差</th>
									<th colspan="2">下摆(CM)</th>
									<th>正误差</th>
									<th>操作</th>
								</tr>
								</thead>
								<tbody>
								<#if domain.clothingSizeSizeMap['XBW']??>
									<#list domain.clothingSizeSizeMap['XBW'] as item>
									<tr id="tr-item-xbw-${item_index}">
										<td>
											<input class="form-control" onblur="checkNumber(this)" name="leftDiff" value="${item.leftDiff }" />
										</td>
										<td>
											<input hidden readonly name="clothingAttrCode" value="XBW"/>
											<#if (item.isMin)?? && item.isMin == true>
											<input class="form-control" readonly="true" placeholder="≧" name="left" value="${item.left }"/>
											<input hidden readonly name="isMin" value="true"/>
											<#else >
											<input class="form-control" onblur="checkNumber(this)" placeholder="≧" name="left" value="${item.left }"/>
											</#if>
										</td>
										<td>
											<input class="form-control"  onblur="checkNumber(this)" placeholder="＜" name="right" value="${item.right }" />
										</td>
										<td>
											<input class="form-control" onblur="checkNumber(this)" name="rightDiff" value="${item.rightDiff }" />
										</td>
										<td>
											<#if (!(item.isMin)?? || item.isMin == false)>
											<buttton type="button" class="btn btn-xs btn-default" onclick="removeTr('xbw', ${item_index})">
												删除
											</buttton>
											</#if>
										<buttton type="button" class="btn btn-xs btn-default" onclick="addTr('xbw', ${item_index})">
											添加
										</buttton>
										</td>
									</tr>
									</#list>
								</#if>
								<tr>
									<td colspan="5" onclick="addTr2('xbw', this)">
										<buttton type="button" class="btn btn-xs btn-defaul" >
											<span class="icon-plus" style="font-size: 25px;margin-top:8px;"></span>
										</buttton>
									</td>
								</tr>
								</tbody>
							</table>
						</div>
						<div class="div_data" id="XC">
							<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list-xc">
								<colgroup>
									<col width="20%" />
									<col width="20%" />
									<col width="20%" />
									<col width="20%" />
									<col width="20%" />
								</colgroup>
								<thead>
								<tr>
									<th>负误差</th>
									<th colspan="2">袖长(CM)</th>
									<th>正误差</th>
									<th>操作</th>
								</tr>
								</thead>
								<tbody>
								<#if domain.clothingSizeSizeMap['XC']??>
									<#list domain.clothingSizeSizeMap['XC'] as item>
									<tr id="tr-item-xc-${item_index}">
										<td>
											<input class="form-control" onblur="checkNumber(this)" name="leftDiff" value="${item.leftDiff }" />
										</td>
										<td>
											<input hidden readonly name="clothingAttrCode" value="XC"/>
											<#if (item.isMin)?? && item.isMin == true>
											<input class="form-control" readonly="true" placeholder="≧" name="left" value="${item.left }"/>
											<input hidden readonly name="isMin" value="true"/>
											<#else >
											<input class="form-control" onblur="checkNumber(this)" placeholder="≧" name="left" value="${item.left }"/>
											</#if>
										</td>
										<td>
											<input class="form-control"  onblur="checkNumber(this)" placeholder="＜" name="right" value="${item.right }" />
										</td>
										<td>
											<input class="form-control" onblur="checkNumber(this)" name="rightDiff" value="${item.rightDiff }" />
										</td>
										<td>
											<#if (!(item.isMin)?? || item.isMin == false)>
											<buttton type="button" class="btn btn-xs btn-default" onclick="removeTr('xc', ${item_index})">
												删除
											</buttton>
											</#if>
										<buttton type="button" class="btn btn-xs btn-default" onclick="addTr('xc', ${item_index})">
											添加
										</buttton>
										</td>
									</tr>
									</#list>
								</#if>
								<tr>
									<td colspan="5" onclick="addTr2('xc', this)">
										<buttton type="button" class="btn btn-xs btn-defaul" >
											<span class="icon-plus" style="font-size: 25px;margin-top:8px;"></span>
										</buttton>
									</td>
								</tr>
								</tbody>
							</table>
						</div>
						<div class="div_data" id="KC">
							<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list-kc">
								<colgroup>
									<col width="20%" />
									<col width="20%" />
									<col width="20%" />
									<col width="20%" />
									<col width="20%" />
								</colgroup>
								<thead>
								<tr>
									<th>负误差</th>
									<th colspan="2">裤长(CM)</th>
									<th>正误差</th>
									<th>操作</th>
								</tr>
								</thead>
								<tbody>
								<#if domain.clothingSizeSizeMap['KC']??>
									<#list domain.clothingSizeSizeMap['KC'] as item>
									<tr id="tr-item-kc-${item_index}">
										<td>
											<input class="form-control" onblur="checkNumber(this)" name="leftDiff" value="${item.leftDiff }" />
										</td>
										<td>
											<input hidden readonly name="clothingAttrCode" value="KC"/>
											<#if (item.isMin)?? && item.isMin == true>
											<input class="form-control" readonly="true" placeholder="≧" name="left" value="${item.left }"/>
											<input hidden readonly name="isMin" value="true"/>
											<#else >
											<input class="form-control" onblur="checkNumber(this)" placeholder="≧" name="left" value="${item.left }"/>
											</#if>
										</td>
										<td>
											<input class="form-control"  onblur="checkNumber(this)" placeholder="＜" name="right" value="${item.right }" />
										</td>
										<td>
											<input class="form-control" onblur="checkNumber(this)" name="rightDiff" value="${item.rightDiff }" />
										</td>
										<td>
											<#if (!(item.isMin)?? || item.isMin == false)>
											<buttton type="button" class="btn btn-xs btn-default" onclick="removeTr('kc', ${item_index})">
												删除
											</buttton>
											</#if>
										<buttton type="button" class="btn btn-xs btn-default" onclick="addTr('kc', ${item_index})">
											添加
										</buttton>
										</td>
									</tr>
									</#list>
								</#if>
								<tr>
									<td colspan="5" onclick="addTr2('kc', this)">
										<buttton type="button" class="btn btn-xs btn-defaul" >
											<span class="icon-plus" style="font-size: 25px;margin-top:8px;"></span>
										</buttton>
									</td>
								</tr>
								</tbody>
							</table>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>

	</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.region.js"></script>
	<script type="text/javascript">

	var clothingAttrJson = ${domain.clothingAttrJson };

	function deleteBatch(){

	}

	function save() {
		var params = [];
		var tables = $(".div_data");
		// 遍历table
		for(let i = 0; i < tables.length; i++){
			var tb = tables[i];
			var id = tb.id;
			var trArr = $(tb).find("tbody tr")
			var trSize = trArr.length;
			var attrName = clothingAttrJson[id];
			// 遍历tr
			for(let j = 0; j < trSize; j++){
				var tr = trArr[j];
				var size = $(tr).children().size();
				if(size <= 1){
					continue;
				}
				var leftDiff = $(tr).find("input[name='leftDiff']").val();
				var rightDiff = $(tr).find("input[name='rightDiff']").val();
				var left = $(tr).find("input[name='left']").val();
				var right = $(tr).find("input[name='right']").val();
				var clothingAttrCode = $(tr).find("input[name='clothingAttrCode']").val();
				var isMin = $(tr).find("input[name='isMin']").val();
				if(leftDiff == undefined || leftDiff == ''){
					layer.alert(attrName + "：负误差不能为空!", 'error');
					return false;
				}
				if(rightDiff == undefined || rightDiff == ''){
					layer.alert(attrName + "：正误差不能为空!", 'error');
					return false;
				}
				if(left == undefined || left == ''){
					layer.alert(attrName + "：区间下限不能为空!", 'error');
					return false;
				}
				if(right == undefined || right == ''){
					layer.alert(attrName + "：区间上限不能为空!", 'error');
					return false;
				}
				left = parseInt(left);
				right = parseInt(right);
				if(left >= right){
					layer.alert(attrName + "：区间下限不能大于或等于区间上限!" + left + ">" + right, 'error');
					return false;
				}
				if(j > 0){
					var lastRight = $(trArr[j-1]).find("input[name='right']").val();
					lastRight = parseInt(lastRight);
					if(left < lastRight){
						layer.alert(attrName + "：区间下限不能小于上一条的区间上限!", 'error');
						return false;
					}/*else if (left > lastRight) {
						layer.alert(attrName + "：区间未封闭，请补充!", 'error');
						return false;
					}*/
				}
				var item = {
					leftDiff: parseInt(leftDiff),
					rightDiff: parseInt(rightDiff),
					left: left,
					right: right,
					clothingAttrCode: clothingAttrCode,
					isMin: isMin
				}
				params.push(item);
			}
		}
		$.ajax({
			url : ${CONTEXT_PATH} + "system/skuClothingSizeDiff/update",
			type: "POST",
			dataType: "json",
			contentType: "application/json;charset=utf-8",
			data : JSON.stringify(params),
			success : function(response){
				if(response.status == '200'){
					location.href = CONTEXT_PATH + "system/skuClothingSizeDiff";
				} else {
					layer.alert('保存服装差异配置失败');
				}
			},
			error:function(){
				layer.alert('保存服装差异配置失败');
			}
		});
	}// end ready
	
	function removeTr(clothingAttrCode, index){
		if (index == 0){
			// 删除首行 校验其它行是否已删除
			var trSize = $("#item-list-"+ clothingAttrCode +" tbody tr").size();
			if(trSize > 2){
				layer.alert('当前存在其它范围配置，无法删除首行配置');
				return;
			}
		}
		// 删除本行
		$("#tr-item-" + clothingAttrCode + "-" +index).remove();
	}

	function addTr2(clothingAttrCode, trDom){
		var length = $("#item-list-"+ clothingAttrCode +" tbody tr").length;
		length = length - 1;
		var upCode = clothingAttrCode.toUpperCase();
		var defaultLeft = 'value=""';
		if(length == 0){
			defaultLeft = 'readonly value="0" disabled="disabled"';
		}
		var appendTr = '<tr id="tr-item-'+ clothingAttrCode +'-'+ length +'">' +
				'<td>' +
				'<input class="form-control" onblur="checkNumber(this)" name="leftDiff" value="" />' +
				'</td>' +
				'<td>' +
				'<input hidden readonly name="clothingAttrCode" value="'+ upCode +'"/>' +
				'<input class="form-control" onblur="checkNumber(this)" placeholder="≧" name="left" '+defaultLeft+'/>' +
				'</td>' +
				'<td>' +
				'<input class="form-control"  onblur="checkNumber(this)" placeholder="＜" name="right" value="" />' +
				'</td>' +
				'<td>' +
				'<input class="form-control" onblur="checkNumber(this)" name="rightDiff" value="" />' +
				'</td>' +
				'<td>' +
				'<buttton type="button" class="btn btn-xs btn-default" onclick="removeTr(\''+clothingAttrCode+'\', '+ length +')">删除</buttton>' +
				'<buttton type="button" class="btn btn-xs btn-default" onclick="addTr(\''+clothingAttrCode+'\', '+ length +')">添加</buttton>' +
				'</td>' +
				'</tr>';
		$(trDom).parent().before(appendTr);
	}
	
	function addTr(clothingAttrCode, index){
		var currentTr = $("#tr-item-" + clothingAttrCode + "-" +index);
		var length = $("#item-list-"+ clothingAttrCode +" tbody tr").length;
		length = length - 1;
		var upCode = clothingAttrCode.toUpperCase();
		var appendTr = '<tr id="tr-item-'+ clothingAttrCode +'-'+ length +'">' +
				'<td>' +
				'<input class="form-control" onblur="checkNumber(this)" name="leftDiff" value="" />' +
				'</td>' +
				'<td>' +
				'<input hidden readonly name="clothingAttrCode" value="'+ upCode +'"/>' +
				'<input class="form-control" onblur="checkNumber(this)" placeholder="≧" name="left" value=""/>' +
				'</td>' +
				'<td>' +
				'<input class="form-control"  onblur="checkNumber(this)" placeholder="＜" name="right" value="" />' +
				'</td>' +
				'<td>' +
				'<input class="form-control" onblur="checkNumber(this)" name="rightDiff" value="" />' +
				'</td>' +
				'<td>' +
				'<buttton type="button" class="btn btn-xs btn-default" onclick="removeTr(\''+clothingAttrCode+'\', '+ length +')">删除</buttton>' +
				'<buttton type="button" class="btn btn-xs btn-default" onclick="addTr(\''+clothingAttrCode+'\', '+ length +')">添加</buttton>' +
				'</td>' +
				'</tr>';
		currentTr.after(appendTr);
	}
	
	function checkNumber(obj){
  		var $this = $(obj);
  		if($this.val() == ''){
  			$this.val("");
  			return;
  		}
  		var reg = /^\+?[1-9][0-9]*$/;
  		if (!reg.test($this.val())) {
  			layer.alert("请输入正确的正整数", 'error');
  			$this.val("");
  			return;
  		}
	}

	</script>
</body>
</html>