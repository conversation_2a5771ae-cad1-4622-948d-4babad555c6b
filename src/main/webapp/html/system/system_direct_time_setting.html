<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">

#item-list thead, tbody tr {
	display: table;
	width: 600px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
<@header method="header" active="14010000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">系统配置</a></li>
					<li class="active">入库直发时间配置</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<!-- END PAGE HEADER-->
		<div class="row" style="padding-top: 80px;height: 760px">
			<div class="col-md-12">
				<form id="submit-form" action="${CONTEXT_PATH}system/directTimeSetting/update"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain">
					<div class="form-body" style="width: 600px;margin: 0 auto">
						<!--<div style="; height:600px; overflow:scroll;">-->
						<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list">
							<colgroup>
								<col width="20%" />
								<col width="20%" />
								<col width="10%" />
							</colgroup>
							<thead>
								<tr>
									<th>直发开始时间</th>
									<th>直发结束时间</th>
									<th>操作</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>
										<input class="form-control Wdate" type="text" id="beginTime" name="beginTime" placeholder="" readonly="readonly" value="${domain.beginTime }"
											   onfocus="WdatePicker({startDate:'%H:%m:%s',dateFmt:'HH:mm:ss',maxDate:'#F{$dp.$D(\'endTime\')}'})">
									</td>
									<td>
										<input class="form-control Wdate" type="text" id="endTime" name="endTime" placeholder="" readonly="readonly" value="${domain.endTime }"
											   onfocus="WdatePicker({startDate:'%H:%m:%s',dateFmt:'HH:mm:ss',minDate:'#F{$dp.$D(\'beginTime\')}'})">
									</td>
									<td>
										<button type="submit" class="btn btn-default">
											保存
										</button>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</form>
			</div>
			<br/>
		</div>

	</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.region.js"></script>
	<script type="text/javascript">

	</script>
</body>
</html>