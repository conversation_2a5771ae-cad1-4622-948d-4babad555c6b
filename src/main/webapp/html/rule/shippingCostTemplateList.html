<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
        .table-top{
            margin-bottom: 0px;
            margin-top: 20px;
        }
        #item-list{
            margin-bottom: 80px;
        }
		.pick-content-button{
			margin-top: 20px;
		}
        .rule-pick-label input{
            width: 40px;
        }
        .red-flag{
            color: red;
        }
	</style>
</head>
<body>
<@header method="header" active="14020000" ><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">系统配置</a></li>
				<li class="active">退货单运费模板</li>
			</ul>
		</div>
	</div>

	<div class="container-fluid" style="background-color: white;border: none">
	<div class="row">
		<div class="col-md-12">
		<#assign query = domain.query />
			<form action="${CONTEXT_PATH}shippingCostTemplate/search"
					   class="form-horizontal form-bordered form-row-stripped"
					   method="post" modelAttribute="domain" name="costTemplateForm" id ="domain">
                <!-- 分页信息 -->
                <input id="page-no" type="hidden" name="page.pageNo" value="1">
                <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
                <@header method="auth" authCode="REFUND_CONFIGURATION_ADD_FREIGHT_PARADIGM">
				<div class="pick-content-button">
					<div class="pull-left" style="margin-left: 10px;">
                        <button type="button" class="btn blue" onclick="setCostTemplate()">
                            <i class="icon-plus"></i> 添加运费模板
                        </button>
					</div>
				</div>
                </@header>
			</form>
		</div>
		<br/>
	</div>
	<div class="row">
        <#list domain.whShippingCostTemplateSettings as whShippingCostTemplateSetting>
            <div class="col-md-12" id="by-list-warp">
                <table class="table table-top">
                    <thead>
                        <tr>
                            <th><h2>${whShippingCostTemplateSetting.templateName }快递价目表</h2></th>
                            <th>对应快递方式：<span class="red-flag">${util('enumName', 'com.estone.checkin.enums.ShippingCompanyEnum', whShippingCostTemplateSetting.shippingType)}</span></th>
                            <th>运费规则：${util('enumName', 'com.estone.rule.enums.ShippingCostRuleEnum', whShippingCostTemplateSetting.shippingCostRule)}</th>
                            <th>添加时间：${util('name',whShippingCostTemplateSetting.createBy)}&nbsp;&nbsp;&nbsp;&nbsp;${whShippingCostTemplateSetting.createdDate }</th>
                            <th>最后编辑时间：${util('name',whShippingCostTemplateSetting.lastUpdateBy)}&nbsp;&nbsp;&nbsp;&nbsp;${whShippingCostTemplateSetting.lastUpdateDate }</th>
                            <th>
<#--                                <@header method="auth" authCode="REFUND_CONFIGURATION_DELETE">-->
                                <button class="btn dark" onclick="deleteTemplate(${whShippingCostTemplateSetting.id})">删除</button>
<#--                                </@header>-->

                                <button class="btn blue" onclick="viewLog(${whShippingCostTemplateSetting.id}, 'costSetting')">日志</button>
<#--                                <@header method="auth" authCode="REFUND_CONFIGURATION_MODIFY">-->
                                <button class="btn yellow" onclick="setCostTemplate(${whShippingCostTemplateSetting.id})">修改</button>
<#--                                </@header>-->
                            </th>
                        </tr>
                        <tr>
                            <th>体积重规则：
                                <#if whShippingCostTemplateSetting.length??>
                                    长${whShippingCostTemplateSetting.length}宽${whShippingCostTemplateSetting.width}高${whShippingCostTemplateSetting.height}${whShippingCostTemplateSetting.weight}</th>
                                 </#if>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                        </tr>
                    </thead>
                </table>
                <table class="table table-striped table-bordered table-hover table-condensed" id="item-list">
                    <#list (whShippingCostTemplateSetting.groupItems)?keys as key>
                        <thead>
                        <th colspan="7" style="text-align:left"><span class="red-flag">重量区间：≥${whShippingCostTemplateSetting.groupItems[key][0].minWeight}且≤${whShippingCostTemplateSetting.groupItems[key][0].maxWeight}</span></th>
                        <thead>
                        <colgroup>
                            <col width="5%" />
                            <col width="45%" />
                            <col width="15%" />
                            <col width="10%" />
                            <col width="10%" />
                            <col width="10%" />
                            <col width="10%" />
                        </colgroup>
                        <thead>
                            <tr>
                                <th>明细编号</th>
                                <th>运送到</th>
                                <th>首重（KG）</th>
                                <th>首重运费（元）</th>
                                <th>续重（KG）</th>
                                <th>续重运费（元）</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <#list whShippingCostTemplateSetting.groupItems[key] as whShippingCostTemplateItemSetting>
                                <tr>
                                    <td>${whShippingCostTemplateItemSetting.id }</td>
                                    <td>${whShippingCostTemplateItemSetting.provinceName }</td>
                                    <td><#if whShippingCostTemplateItemSetting.firstWeight??>${whShippingCostTemplateItemSetting.firstWeight?if_exists?string.number }</#if></td>
                                    <td><#if whShippingCostTemplateItemSetting.firstWeightCost??>${whShippingCostTemplateItemSetting.firstWeightCost?if_exists?string.number }</#if></td>
                                    <td><#if whShippingCostTemplateItemSetting.continuedWeight??>${whShippingCostTemplateItemSetting.continuedWeight?if_exists?string.number }</#if></td>
                                    <td><#if whShippingCostTemplateItemSetting.continuedWeightCost??>${whShippingCostTemplateItemSetting.continuedWeightCost?if_exists?string.number }</#if></td>
                                    <td></td>
                                </tr>
                            </#list>
                        </tbody>
                    </#list>
                </table>
            </div>
        </#list>
	</div>
    <div id="fixed-bottom">
        <div id="pager"></div>
    </div>
</div>
<#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript">
    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";
    $("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

    function setCostTemplate(id) {
        if(id){
            window.location.href = CONTEXT_PATH + "shippingCostTemplate/edit?id=" + id;
        }else {
            window.location.href = CONTEXT_PATH + "shippingCostTemplate/edit";
        }
    }

    function deleteTemplate(id) {
        $.getJSON('${CONTEXT_PATH}shippingCostTemplate/delete?id='+ id, function(data) {
            if(data.status == 200){
                window.location.reload();
            }else {
                customizeLayer(data.message,'error');
            }
        });
    }
</script>
</body>
</html>