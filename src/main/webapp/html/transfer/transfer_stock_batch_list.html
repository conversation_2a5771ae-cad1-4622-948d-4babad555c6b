<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		table{
			max-width: none;
		}
		th,td{
			width: 97px;
		}
		.tab-container{
			width: 1855px;
			overflow-y: scroll;
			height: 500px !important;
			overflow-x: hidden;
		}
		.fixed-table-header{
			overflow-x: auto;
			width: auto;
		}
		#fixedTab{
			width: 1855px;
			margin-bottom: 0px;
		}

		#task-list {
			width: 1855px;
		}

		#task-list td {
			vertical-align:middle;
		}
		#fixedTab thead>tr>th {
			vertical-align: middle;
		}
	</style>
</head>
<body>
<@header method="header" active="13050000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">海外仓报表</a></li>
				<li class="active">中转仓库存明细批次</li>
			</ul>
		</div>
	</div>

	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">

		<!-- BEGIN PAGE HEADER-->
		<#assign query=domain.query>
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="col-md-12">
				<form action="${CONTEXT_PATH}transfer/stock/batch/search"
					  class="form-horizontal form-bordered form-row-stripped"
					  method="post" modelAttribute="domain" name="transferStockBatchForm" id="domain">
					<!-- 分页信息 -->
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-1">单据生成时间</label>
							<div class="col-md-2">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.startReportTime" placeholder="" readonly="readonly" value="${query.startReportTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.endReportTime" placeholder="" readonly="readonly" value="${query.endReportTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>
							<label class="control-label col-md-1">批次号</label>
							<div class="col-md-2">
								<input class="form-control" name="query.batNoStr" type="text" value="${query.batNoStr}"  placeholder="多个批次号以英文逗号分割">
							</div>
							<label class="control-label col-md-1">关联批次号</label>
							<div class="col-md-2">
								<input class="form-control" name="query.relationBatNoStr" type="text" value="${query.relationBatNoStr}"  placeholder="多个关联批次号以英文逗号分割">
							</div>
							<label class="control-label col-md-1">SKU</label>
							<div class="col-md-2">
								<input class="form-control" name="query.skuStr" type="text" value="${query.skuStr}"  placeholder="多个SKU以英文逗号分割">
							</div>
						</div>

						<div class="form-group">
							<label class="control-label col-md-1">出入库类型</label>
							<div class="col-md-2">
								<input class="form-control" name="query.quantityType" type="text" value="${query.quantityType}"/>
							</div>
							<label class="control-label col-md-1">库存类型</label>
							<div class="col-md-2">
								<input class="form-control" name="query.checkInType" type="text" value="${query.checkInType}"/>
							</div>
							<label class="control-label col-md-1">单据类型</label>
							<div class="col-md-2">
								<input class="form-control" name="query.orderType" type="text" value="${query.orderType}"/>
							</div>
							<label class="control-label col-md-1">相关单号</label>
							<div class="col-md-2">
								<input class="form-control" name="query.orderNoStr" type="text" value="${query.orderNoStr}"  placeholder="多个单号以英文逗号分割">
							</div>
						</div>
					</div>
					<div>

						<div class="col-md-offset-12" style="text-align: right;margin-right: 10px">
							<button type="button" class="btn btn-default" onclick="formReset(this)">
								<i class="icon-refresh"></i> 重置
							</button>
							<button type="submit" class="btn blue">
								<i class="icon-search"></i> 查询
							</button>
							<@header method="auth" authCode="TRANSFER_STOCK_BATCH_DETAIL_DOWNLOAD">
								<button type="button" class="btn btn-default" onclick="downloadItems()">
									<i class="icon-download"></i> 导出
								</button>
							</@header>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>
		<div class="row">

			<div class="col-md-12 fixed-table-header">
				<!--此处722px，是由于border有1px，加上两边的总共有722px，这样保证内容720px，才能使两个表格对齐-->
				<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
					<thead>
					<tr>
						<th style="width: 50px !important;"><input type="checkbox" id="check-all" name="checkAll"></th>
						<th>时间</th>
						<th>批次号</th>
						<th>关联批次号</th>
						<th>店铺</th>
						<th>出入库类型</th>
						<th>库存类型</th>
						<th>单据类型</th>
						<th>相关单号</th>
						<th>SKU</th>
						<th>数量</th>
						<th style="width: 2px !important;"></th>
					</tr>
					</thead>
				</table>
				<div class="tab-container">
					<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
						<tbody>
						<#list domain.transferStockBatchDetails as item>
							<tr>
								<td style="width: 50px !important;">
									<input type="checkbox" value="${item.id}" name="ids">
								</td>
								<td>${item.reportTime}</td>
								<td>
									${item.batchNo}
								</td>
								<td>${item.relationBatchNo}</td>
								<td>${item.storeCode}</td>
								<td>${util('enumName', 'com.estone.transfer.enums.QuantityType', item.quantityType)}</td>
								<td>${util('enumName', 'com.estone.transfer.enums.TransitBatchStockType', item.checkInType)}</td>
								<td>${util('enumName', 'com.estone.transfer.enums.TransitBatchOrderType', item.orderType)}</td>
								<td>${item.orderNo}</td>
								<td>${item.sku}</td>
								<td>${item.quantity}</td>
								<td style="width: 2px !important;"></td>
							</tr>
						</#list>
						</tbody>
					</table>
				</div>

			</div>
		</div>

		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	</div>
	<#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript">
	// 分页
	var total = "${domain.page.totalCount}";
	var pageNo = "${domain.page.pageNo}";
	var pageSize = "${domain.page.pageSize}";
	$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

	var heights = $("body").height();
	if(heights>910){
		$("#fixed-bottom").addClass("fixed-bottom-height")
	}

	var quantityTypeArray =  ${domain.quantityTypes};
	$("input[name='query.quantityType']").select2({
		data : quantityTypeArray,
		placeholder : "出入库类型",
		allowClear : true
	});

	var checkInTypeArray =  ${domain.checkInTypes};
	$("input[name='query.checkInType']").select2({
		data : checkInTypeArray,
		placeholder : "库存类型",
		allowClear : true
	});

	var orderTypeArray =  ${domain.orderTypes};
	$("input[name='query.orderType']").select2({
		data : orderTypeArray,
		placeholder : "单据类型",
		allowClear : true
	});


	var checkAll = $("input[name='checkAll']");

	// 子选项
	var itemIds = $("input[name='ids']");
	checkAll.change(
			function() {
				itemIds.prop("checked", $(this).prop("checked"));
			}
	);


	function getCheckedIds(){
		var outIds = $("#task-list").find("input[name='ids']:checked");
		var checkIds = "";
		for (var i = 0; i < outIds.length; i++) {
			var outId = outIds[i].value;
			if (i == 0) {
				checkIds += outId;
			} else {
				checkIds += "," + outId;
			}
		}
		return checkIds;
	}


	// 导出
	function downloadItems() {
		var params = $('#domain').serialize();
		var ids = getCheckedIds();
		if (ids.length == 100000) {

		} else {
			params = params + "&query.ids=" + ids;
		}
		$.post(CONTEXT_PATH + "transfer/stock/batch/download", params, function(data){
			if (data.status == 200) {
				if (data.message==null || data.message==''){
					layer.alert('成功',function (index) {
						layer.close(index);
						diglog.close().remove();
						location.reload();
					});
				}else{
					customizeLayer(data.message);
				}
			} else {
				customizeLayer(data.message);
			}
		});
	}
</script>
</body>
</html>