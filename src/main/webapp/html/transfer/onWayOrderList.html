<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <#include "/common/include.html">
    <style type="text/css">
        .modal-body input {
            width: 240px;
        }

        #task-list td {
            vertical-align: middle;
        }
        .group-inline {
            color: red;
        }
    </style>
</head>

<body>
<@header method="header" active="15040100"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li>
                    <a href="#">中转仓</a>
                </li>
                <li class="active">头程在途管理</li>
            </ul>
        </div>
    </div>
    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">
        <div class="row">
            <div class="col-md-12">
                <#assign query=domain.query>
                <form action="${CONTEXT_PATH}onWayOrder/search" class="form-horizontal form-bordered form-row-stripped"
                      method="post" modelAttribute="domain" name="onWayOrderForm" id="domain">
                    <!-- 分页信息 -->
                    <input id="page-no" type="hidden" name="page.pageNo" value="1">
                    <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
                    <div class="form-body">
                        <div class="form-group">
                            <label class="control-label col-md-1">平台</label>
                            <div class="col-md-2">
                                <select class="form-control" name="query.platform">
                                    <option></option>
                                    <option <#if query.platform == 'Amazon'>selected</#if> value="Amazon">Amazon</option>
                                    <#list domain.saleChannelList as saleChannel>
                                        <#if query.platform == saleChannel>
                                            <option selected="selected" value="${saleChannel}">${saleChannel}</option>
                                        <#else>
                                            <option value="${saleChannel}">${saleChannel}</option>
                                        </#if>
                                    </#list>
                                </select>
                            </div>
                            <label class="control-label col-md-1">发货单号:</label>
                            <div class="col-md-2">
                                <input class="form-control" type="text" name="query.fbaNo" placeholder="多个逗号分开" value="${query.fbaNo }">
                            </div>
                            <label class="control-label col-md-1">货件编号:</label>
                            <div class="col-md-2">
                                <input class="form-control" name="query.shipmentId" type="text" placeholder="多个逗号分开" value="${query.shipmentId }">
                            </div>
                            <label class="control-label col-md-1">跟踪单号:</label>
                            <div class="col-md-2">
                                <input class="form-control" type="text" name="query.trackingNumber" placeholder="多个逗号分开" value="${query.trackingNumber }">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-md-1">状态:</label>
                            <div class="col-md-2">
                                <input class="form-control" type="text" name="query.status" value="${query.status}">
                            </div>
                            <label class="control-label col-md-1">发货仓:</label>
                            <div class="col-md-2">
                                <input class="form-control" type="text" name="query.shippingWhLikeStr" placeholder="支持模糊查询" value="${query.shippingWhLikeStr }">
                            </div>
                            <label class="control-label col-md-1">SKU:</label>
                            <div class="col-md-2">
                                <input class="form-control" type="text" name="query.sku" placeholder="多个逗号分开" value="${query.sku }">
                            </div>
                            <label class="control-label col-md-1">在途预警:</label>
                            <div class="col-md-2">
                                <select name="query.onWayWarning" class="form-control">
                                    <option></option>
                                    <option value="false" <#if query.onWayWarning == 'false'>selected="selected"</#if> >无</option>
                                    <option value="true" <#if query.onWayWarning == 'true'>selected="selected"</#if> >有</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-md-1">时间:</label>
                            <div class="col-md-2" style="width: 150px;">
                                <select name="query.timeType" class="form-control">
                                    <option value="LT" <#if query.timeType == 'LT'>selected="selected"</#if> >装车时间</option>
                                    <option value="EAT" <#if query.timeType == 'EAT'>selected="selected"</#if> >预计到仓时间</option>
                                    <option value="RT" <#if query.timeType == 'RT'>selected="selected"</#if> >签收时间</option>
                                    <option value="OT" <#if query.timeType == 'OT'>selected="selected"</#if> >海外仓上架时间</option>
                                </select>
                            </div>
                            <div class="col-md-1">
                                <input class="form-control Wdate" type="text" name="query.fromTime" placeholder="" readonly="readonly" value="${query.fromTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                            </div>
                            <label class="control-label col-md-1" style="width:20px">到</label>
                            <div class="col-md-1">
                                <input class="form-control Wdate" type="text" name="query.toTime" placeholder="" readonly="readonly" value="${query.toTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                            </div>
                        </div>
                    </div>

                    <div>
                        <div class="pull-left" >
                            <div class="btn-group">
                                <@header method="auth" authCode="BATCH_UPDATE_ORDER_STATUS_AND_UP_TIME">
                                    <button type="button" class="btn btn-default" onclick="batchSyncStatus()">
                                        更新海外上架数量
                                    </button>
                                </@header>
                            </div>
                        </div>
                        <div class="col-md-offset-10" style="text-align: right">
                            <@header method="auth" authCode="ON_WAY_ORDER_MANAGE_DOWNLOAD">
                                <button type="button" class="btn btn-default" onclick="downloadOnWayOrder()">
                                    <i class="icon-download"></i> 导出
                                </button>
                            </@header>
                            <button type="button" class="btn default" onclick="formReset(this)">
                                <i class="icon-refresh"></i> 重置
                            </button>
                            <button type="submit" class="btn blue">
                                <i class="icon-search"></i> 查询
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <br/>
        </div>

        <div class="row">
            <div id="fixedDiv" class="col-md-12">
                <table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
                    <colgroup>
                        <col width="5%" />
                        <col width="10%" />
                        <col width="5%" />
                        <col width="15%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="10%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="10%" />
                        <col width="15%" />
                        <col width="5%" />
                    </colgroup>
                    <thead>
                    <tr>
                        <th>编号</th>
                        <th>SKU</th>
                        <th>店铺</th>
                        <th>单号</th>
                        <th>单据状态</th>
                        <th>平台</th>
                        <th>仓库信息</th>
                        <th>装箱数量</th>
                        <th>海外仓上架数量</th>
                        <th>差异</th>
                        <th>在途预警</th>
                        <th>时间</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="col-md-12" id="task-list-warp">
                <table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                    <colgroup>
                        <col width="5%" />
                        <col width="10%" />
                        <col width="5%" />
                        <col width="15%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="10%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="10%" />
                        <col width="15%" />
                        <col width="5%" />
                    </colgroup>
                    <thead>
                    <tr>
                        <th><label class="checkbox-inline"><input type="checkbox" id="check-all" name="checkAll"> 编号</label></th>
                        <th>SKU</th>
                        <th>店铺</th>
                        <th>单号</th>
                        <th>单据状态</th>
                        <th>平台</th>
                        <th>仓库信息</th>
                        <th>装箱数量</th>
                        <th>海外仓上架数量</th>
                        <th>差异</th>
                        <th>在途预警</th>
                        <th>时间</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list domain.onWayOrderItemList as item>
                        <#assign order=item.onWayOrder>
                        <#assign asnExtra=item.whAsnExtra>
                        <#assign allocation=item.allocation>
                        <tr>
                            <td>
                                <label class="checkbox-inline"><input type="checkbox" value="${item.id}" name="ids">${item.id}</label>
                                <input type="hidden" value="${order.id}" name="orderIds">
                            </td>
                            <td style="text-align: left;">
                                <p class="text">${item.sku}</p>
                                <#if order.platform == 'Amazon'>
                                    <p class="text"><strong>FnSku：</strong>${item.fnSku}</p>
                                    <p class="text"><strong>SellerSku：</strong>${item.sellSku}</p>
                                </#if>
                            </td>
                            <td> ${allocation.accountNumber} </td>
                            <td style="text-align: left;">
                                <p class="text"><strong>货件编号：</strong>${allocation.shipmentId}</p>
                                <p class="text"><strong>发货单号：</strong>${order.fbaNo}</p>
                                <p class="text"><strong>物流商：</strong>${allocation.shippingCompany}</p>
                                <p class="text"><strong>物流单号：</strong>${allocation.shippingOrderNo}</p>
                                <p class="text"><strong>跟踪单号：</strong>${allocation.trackingNumberByTms}</p>
                            </td>
                            <td>
                                ${util('enumName', 'com.estone.transfer.enums.OnWayStatus', order.status)}
                            </td>
                            <td>${order.platform}</td>
                            <td style="text-align: left;">
                                <p class="text"><strong>发货仓：</strong>${order.shippingWarehouse}</p>
                                <p class="text"><strong>仓库ID：</strong>${order.purposeWarehouse}</p>
                                <p class="text"><strong>国家：</strong>${asnExtra.receiptCountry}</p>
                            </td>
                            <td>${item.loadingQuantity}</td>
                            <td>${item.overseasUpQuantity}</td>
                            <td><span <#if item.upDiff?? && item.upDiff !=0>style="color: red" </#if>>${item.upDiff}</span></td>
                            <td>
                                <#if item.onWayWarning?? && item.onWayWarning gt 0>
                                    <span style="color: red">${item.onWayWarning}天</span>
                                <#else >无
                                </#if>
                            </td>
                            <td style="text-align: left;">
                                <p class="text"><strong>装车时间：</strong>${order.loadTime}</p>
                                <p class="text">
                                    <strong>预计到仓时间：</strong>
                                    ${order.startExpectArriveTime}
                                    <#if order.endExpectArriveTime??>
                                       ～ ${order.endExpectArriveTime}
                                    </#if>
                                </p>
                                <p class="text"><strong>签收时间：</strong>${order.receiveTime}</p>
                                <p class="text"><strong>海外仓上架：</strong>${item.overseasUpTime}</p>
                            </td>
                            <td>
                                <button type="button" class="btn btn-xs btn-info" onclick="viewLog(${order.id}, 'onWayOrder')">日志</button>
                            </td>
                        </tr>

                    </#list>
                    </tbody>
                </table>
            </div>
        </div>
        <div id="fixed-bottom">
            <div id="pager"></div>
        </div>
    </div>
    <#include "/common/footer.html">
    <img id="enlarge" style='position:fixed;width:800px;height:800px;top:15%;left:15%;display:none;'/>
</div>



<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript">
    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";

    $("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });
    var heights = $("body").height();
    if(heights > 910) {
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }

    // 全选
    var checkAll = $("input[name='checkAll']");
    // 子选项
    var itemIds = $("input[name='ids']");
    checkAll.change(
        function() {
            itemIds.prop("checked", $(this).prop("checked"));
            itemIds.each(function() {
                var f = $(this).is(":checked");
                var checkClass = $(this).prop("class");
                $("." + checkClass).each(function() {
                    $(this).prop("checked", f);
                })
            })
        }
    );
    // 获取选中的记录
    function getCheckedIds() {
        var checkedIds = $("input[name='ids']:checked");
        return checkedIds;
    }

    function getCheckedOrderIds() {
        var orderIds = new Set();
        $("input[name='ids']:checked").each(function () {
            let orderId = $(this).parent().parent().find("input[name='orderIds']").val();
            orderIds.add(orderId);
        });
        return orderIds;
    }

    // 状态
    var orderStatus = ${domain.orderStatus};
    $("input[name='query.status']").select2({
        data : orderStatus,
        placeholder : "状态",
        allowClear : true
    });

    function downloadOnWayOrder() {
        var checkedIds = getCheckedIds();
        var diglog = dialog({
            title: '导出',
            width: 800,
            height:300,
            url: CONTEXT_PATH + "apvs/downloadmode?type=1",
            okValue: '确定',
            ok: function () {
                var exportWindow = $(this.iframeNode.contentWindow.document.body);

                var submitForm = exportWindow.find("#submit-form");

                var exportType = submitForm.find("input[name='exportType']:checked").val();

                var submitFormParam = submitForm.serialize();

                // 导出当前选择
                if(exportType == 3) {
                    if(checkedIds.length == 0) {
                        layer.alert("请选择要操作的数据");
                        return false;
                    }else if (checkedIds.length > 300) {
                        layer.alert("选择数量不能超过300!");
                        return false;
                    }

                    submitFormParam = submitFormParam + "&" +checkedIds.serialize();
                }

                //还原分页
                $("#page-no").val("${domain.page.pageNo}");

                var action = document.onWayOrderForm.action;
                var target = document.onWayOrderForm.target;
                var method = document.onWayOrderForm.method;
                document.onWayOrderForm.action= CONTEXT_PATH + "onWayOrder/download?" + submitFormParam;
                document.onWayOrderForm.target="_blank";
                document.onWayOrderForm.method="POST";
                document.onWayOrderForm.submit();
                document.onWayOrderForm.target=target;
                document.onWayOrderForm.action=action;
                document.onWayOrderForm.method=method;

                $("#page-no").val("1");

                setTimeout(function () {
                    diglog.close().remove();
                }, 100);

                return true;
            },
            cancelValue: '取消',
            cancel: function () {}
        });
        diglog.show();
    }

    function batchSyncStatus() {
        var url = CONTEXT_PATH + "onWayOrder/syncStatus";

        var searchUrl = $("#domain").attr("action");

        var checkedIds = getCheckedOrderIds();
        if (checkedIds.size > 0) {
            let orderIdStr = Array.from(checkedIds).join(",");
            url = url + "?ids=" + orderIdStr;
        }
        $("#domain").attr("action", url);

        $("#domain").ajaxSubmit(function(data) {
            if (data.status == 200) {
                alert("成功！");
            } else {
                customizeLayer(data.message, "error");
            }

            $("#domain").attr("action", searchUrl);
        });

        $("#domain").attr("action", searchUrl);

        setTimeout(function () {
           location.reload();
        }, 2000);
    }

</script>
</body>

</html>