<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <#include "/common/webSocket.html">
		<#include "/common/include.html">
		<style type="text/css">
			.modal-body input {
				width: 240px;
			}

			#task-list td {
				vertical-align: middle;
			}
			#task-list thead>tr>th {
				vertical-align: middle;
			}
			#fixedDiv thead>tr>th {
				vertical-align: middle;
			}

		</style>
	</head>

	<body>
		<@header method="header" active="15040100"><#include "/ftl/header.ftl"></@header>
		<div id="page" style="background-color: rgb(231, 237, 248)">
			<div class="row">
				<div class="col-md-12" style="padding: 0">
					<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
						<li>
							<a href="#">中转仓</a>
						</li>
						<li class="active">待返架sku</li>
					</ul>
				</div>
			</div>
			<!-- 内容 -->
			<div class="container-fluid" style="background-color: white;border: none">
				<div class="row">
					<div class="col-md-12">
						<#assign query=domain.query>
						<form action="${CONTEXT_PATH}fba/change/search" class="form-horizontal form-bordered form-row-stripped"
							  method="post" modelAttribute="domain" name="fbaChangeForm" id="domain">
							<!-- 分页信息 -->
							<input id="page-no" type="hidden" name="page.pageNo" value="1">
							<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
							<div class="form-body" >
								<div class="form-group">
									<label class="control-label col-md-1" style="width:80px">创建时间:</label>
									<div class="col-md-2" style="width:150px">
                                        <input class="form-control Wdate" type="text" name="query.startChangeTime" placeholder="" readonly="readonly" value="${query.startChangeTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
                                    <label class="control-label col-md-1" style="width:20px">到</label>
                                    <div class="col-md-2" style="width:200px">
                                        <input class="form-control Wdate" type="text" name="query.endChangeTime" placeholder="" readonly="readonly" value="${query.endChangeTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    </div>
                                    <label class="control-label col-md-1" style="width:80px">单据类型:</label>
                                    <div class="col-md-2" style="width:250px">
                                        <input class="form-control" type="text" name="query.deliveryTypes" placeholder="单据类型" value="<#if (query.deliveryTypes)!>${query.deliveryTypes?join(',')}<#else ></#if>">
                                    </div>
									<label class="control-label col-md-1" style="width:80px">发货单号:</label>
									<div class="col-md-2" style="width:250px">
										<input class="form-control" type="text" name="query.fbaNoList" placeholder="多个逗号分开" value="${query.fbaNoList }">
									</div>
									<label class="control-label col-md-1" style="width:80px">SKU:</label>
                                    <div class="col-md-2" style="width:250px">
                                        <input class="form-control" type="text" name="query.skuSplit" placeholder="多个逗号分开" value="${query.skuSplit }">
                                    </div>
                                    <label class="control-label col-md-1" style="width:80px">返架状态</label>
                                    <div class="col-md-2" style="width:250px">
                                        <input class="form-control" type="text" name="query.orderItemStatus" placeholder="返架状态" value="${query.orderItemStatus }">
                                    </div>
								</div>
                                <div class="form-group">
                                    <label class="control-label col-md-1" style="width:80px">取消类型:</label>
                                    <div class="col-md-2" style="width:370px">
                                        <input class="form-control" type="text" name="query.cancelType" placeholder="取消类型" value="${query.cancelType }">
                                    </div>
                                    <label class="control-label col-md-1" style="width:100px">取消前状态:</label>
                                    <div class="col-md-2" style="width:250px">
                                        <input class="form-control" type="text" name="query.changeStatus" placeholder="取消前状态" value="${query.changeStatus }">
                                    </div>
                                </div>
                                <div>
                                    <div class="pull-left" style="margin: 10px;">
                                        <@header method="auth" authCode="FBA_CHANGE_GENERATER_RETURN_ORDER">
                                        <button type="button" class="btn default" onclick="generateReturnOrder()">
                                            <i class="icon-plus"></i> 生成返架任务
                                        </button>
                                        </@header>
                                        <@header method="auth" authCode="TEMP_WAREHOUSE_GENERATE_WAIT">
                                            <button type="button" class="btn default" onclick="generateWaitOrder()">
                                                <i class="icon-plus"></i> 确认已拿货
                                            </button>
                                        </@header>
                                    </div>
                                </div>
                                <div class="col-md-offset-10" style="margin: 10px;text-align: right">
                                    <button type="button" class="btn default" onclick="download()">
                                        <i class="icon-download"></i> 导出数据
                                    </button>
									<button type="button" class="btn default" onclick="formReset(this)">
										<i class="icon-refresh"></i> 重置
									</button>
									<button type="submit" class="btn blue">
										<i class="icon-search"></i> 查询
									</button>
								</div>
							</div>
						</form>
					</div>
				</div>

				<div class="row">
                    <div id="fixedDiv" class="col-md-12">
                        <table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
                            <colgroup>
                                <col width="6%" />
                                <col width="8%" />
                                <col width="5%" />
                                <col width="5%" />
                                <col width="5%" />
                                <col width="8%" />
                                <col width="8%" />
                                <col width="8%" />
                                <col width="8%" />
                                <col width="8%" />
                                <col width="8%" />
                                <col width="8%" />
                                <col width="6%" />
                                <col width="10%" />
                            </colgroup>
                            <thead>
                            <tr>
                                <th><input name="checkAll" type="checkbox"/> 编号</th>
                                <th>中转仓发货单号</th>
                                <th>变更前状态</th>
                                <th>发货类型</th>
                                <th>取消类型</th>
                                <th>箱号</th>
                                <th>FNSKU</th>
                                <th>SKU</th>
                                <th>库位</th>
                                <th>应返架数量</th>
                                <th>已返架数量</th>
                                <th>关联返架单</th>
                                <th>状态</th>
                                <th>创建时间</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
					<div class="col-md-12" id="task-list-warp">
						<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                            <colgroup>
                                <col width="6%" />
                                <col width="8%" />
                                <col width="5%" />
                                <col width="5%" />
                                <col width="5%" />
                                <col width="8%" />
                                <col width="8%" />
                                <col width="8%" />
                                <col width="8%" />
                                <col width="8%" />
                                <col width="8%" />
                                <col width="8%" />
                                <col width="6%" />
                                <col width="10%" />
                            </colgroup>
							<thead>
                                <tr>
                                    <th><input name="checkAll" type="checkbox"/> 编号</th>
                                    <th>中转仓发货单号</th>
                                    <th>变更前状态</th>
                                    <th>发货类型</th>
                                    <th>取消类型</th>
                                    <th>箱号</th>
                                    <th>FNSKU</th>
                                    <th>SKU</th>
                                    <th>库位</th>
                                    <th>应返架数量</th>
                                    <th>已返架数量</th>
                                    <th>关联返架单</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                </tr>
							</thead>
							<tbody>
							<#list domain.whFbaChanges as change>
							<tr>
                                <td>
                                    <label class="checkbox-inline">
                                        <input class="${change.id}" name="ids" type="checkbox" value="${change.id}">${change.id}
                                    </label>
                                </td>
                                <td>${change.fbaNo}</td>
                                <td>${util('enumName', 'com.estone.transfer.enums.AsnPrepareStatus', change.status)}</td>
                                <td>${change.deliveryTypeStr}</td>
                                <td>${util('enumName', 'com.estone.temu.enums.CancelTypeEnum', change.cancelType)}</td>
                                <td>${change.boxNo}</td>
                                <td>${change.fnSku}</td>
                                <td>${change.sku}</td>
                                <td>${change.locationNumber}</td>
                                <td>${change.returnQuantity}</td>
                                <td>${change.accomplishmentQuantity}</td>
                                <td>${change.returnOrderNo}</td>
                                <td>${change.orderItemStatusStr}</td>
                                <td>${change.changeTime}</td>
							</tr>
							</#list>

							</tbody>
						</table>
					</div>
				</div>
				<div id="fixed-bottom" >
					<div id="pager"><ur class="pages"><li class="select"><select><option>10</option></select></ur></div>
				</div>
			</div>

			<#include "/common/footer.html">
			<img id="enlarge" style='position:absolute;width:800px;height:800px;top:10%;left:15%;display:none;'/>
		</div>

		<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
        <script type="text/javascript" src="${CONTEXT_PATH}js/prompt-message.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
		<script type="text/javascript">
			// 分页
			var total = "${domain.page.totalCount}";
			var pageNo = "${domain.page.pageNo}";
			var pageSize = "${domain.page.pageSize}";

			$("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });
			var heights = $("body").height();
			if(heights > 910) {
				$("#fixed-bottom").addClass("fixed-bottom-height")
			}

			// 全选
			var checkAll = $("input[name='checkAll']");
			// 子选项
			var itemIds = $("input[name='ids']");
			checkAll.change(
				function() {
					itemIds.prop("checked", $(this).prop("checked"));
					itemIds.each(function() {
						var f = $(this).is(":checked");
						var checkClass = $(this).prop("class");
						$("." + checkClass).each(function() {
							$(this).prop("checked", f);
						})
					})
				}
			);
			// 获取选中的记录
			function getCheckedIds() {
				var checkedIds = $("input[name='ids']:checked");
				return checkedIds;
			}

			// 状态
            // 单据类型
            var deliveryTypeJson = ${domain.deliveryTypeJson};
            $("input[name='query.deliveryTypes']").select2({
                data : deliveryTypeJson,
                placeholder : "单据类型",
				allowClear : true,
				multiple: true
			});


            // 返架状态
            var orderItemStatusJson = ${domain.orderItemStatusJson};
            $("input[name='query.orderItemStatus']").select2({
                data : orderItemStatusJson,
                placeholder : "单据类型",
                allowClear : true
                //multiple: true
            });

            var cancelTypeJson = ${domain.cancelTypeJson};
            $("input[name='query.cancelType']").select2({
                data : cancelTypeJson,
                placeholder : "取消类型",
                allowClear : true
                //multiple: true
            });

            var changeStatusJson = ${domain.changeStatusJson};
            $("input[name='query.changeStatus']").select2({
                data : changeStatusJson,
                placeholder : "取消前状态",
                allowClear : true
                //multiple: true
            });


            function download(){
                var diglog = dialog({
                    title:"待返架SKU导出",
                    width: 500,
                    height: 80,
                    url: CONTEXT_PATH + "fba/change/downloadmode",
                    okValue: '确定',
                    ok: function () {
                        var exportWindow = $(this.iframeNode.contentWindow.document.body);

                        var submitForm = exportWindow.find("#submit-form");

                        var exportType = submitForm.find("input[name='exportType']:checked").val();

                        var ids = getCheckedIds();
                        var uuid = getFileQueueUUID();
                        $("#page-no").val("${domain.page.pageNo}");
                        var params = $('#domain').serialize();
                        if(exportType == 3) {// 导出当前选择
                            if(ids.length == 0) {
                                layer.alert("请选择要操作的数据");
                                return false;
                            }
                            params = params + "&" + ids.serialize();
                        }
                        params = params + "&exportType=" + exportType +"&uuid="+uuid;
                        //还原分页
                        downloadByPostForm(params, "download?");
                        $("#page-no").val("1");
                        setTimeout(function () {
                            diglog.close().remove();
                        }, 100);
                        return true;
                    },
                    cancelValue:'取消',
                    cancel: function () {}
                });
                diglog.show();
            }

            function generateReturnOrder(){
                var ids = getCheckedIds();
                var idArray = [];
                $.each(ids,function(index,item){
                    var val = $(item).val();
                    idArray.push(val);
                });
                var idStr = "";
                for (var i = 0; i < idArray.length; i++) {
                    var id = idArray[i];
                    idStr += id;
                    if (i != idArray.length - 1) {
                        idStr += ",";
                    }
                }
                $.ajax({
                    url : CONTEXT_PATH + "fba/change/generateReturnOrder",
                    data : {ids:idStr},
                    type: "POST",
                    success : function(json){
                        if (json.status == '500') {
                            getErrorInfoAlert(json.message + '');
                        } else if (json.status == '200') {
                            layer.alert("成功!",'success');
                            setTimeout(function () {
                                window.location.reload();
                            }, 500);
                        }
                    },
                    error:function(){
                        getErrorInfoAlert('生成失败!');
                    }
                });
            }

            function generateWaitOrder(){
                var ids = getCheckedIds();

                if(ids.length == 0) {
                    layer.alert("请选择要操作的数据!");
                    return false;
                }

                var idArray = [];
                $.each(ids,function(index,item){
                    var val = $(item).val();
                    idArray.push(val);
                });
                var idStr = "";
                for (var i = 0; i < idArray.length; i++) {
                    var id = idArray[i];
                    idStr += id;
                    if (i != idArray.length - 1) {
                        idStr += ",";
                    }
                }
                $.ajax({
                    url : CONTEXT_PATH + "fba/change/generateWaitOrder",
                    data : {ids:idStr},
                    type: "POST",
                    success : function(json){
                        if (json.status == '500') {
                            getErrorInfoAlert(json.message + '');
                        } else if (json.status == '200') {
                            layer.alert(json.message,'success');
                            layer.alert(json.message, {closeBtn: 0}, function (index) {
                                layer.close(index);
                                window.location.reload();
                            });
                        }
                    },
                    error:function(){
                        getErrorInfoAlert('生成失败!');
                    }
                });

            }

		</script>
	</body>

</html>