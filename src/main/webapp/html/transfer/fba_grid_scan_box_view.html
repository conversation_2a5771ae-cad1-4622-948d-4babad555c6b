<#if (domain.whPickingTaskSkuList)!>
    <div class="row" style="line-height: 60px;">
        <div style="font-size: x-large;color: blue;">
            <input type="hidden" value="${domain.boxNo}" id="bindBoxNo"/>
            <span style="margin-left: 10px;">已拣总数：${domain.pickQuantity}</span>
            <span style="margin-left: 50px;">已播总数：<span id="grid_amount">${domain.gridQuantity}</span></span>
            <span style="margin-left: 50px;">差异数：<span id="grid_amount_diff">${domain.pickQuantity - domain.gridQuantity}</span></span>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12" id="task-list-warp">
            <table class="table" style="background-color: #f2f2f2;text-align: center;" id="task-list">
                <thead>
                <tr>
                    <th>SKU</th>
                    <th>分配数量</th>
                    <th>拣货数量</th>
                    <th>播种数量</th>
                </tr>
                </thead>
                <tbody>

                    <#list domain.whPickingTaskSkuList as fbaItem>
                        <tr>
                            <td>
                                ${fbaItem.sku}
                            </td>
                            <td>${fbaItem.quantity}</td>
                            <td>${fbaItem.pickQuantity}</td>
                            <#if fbaItem.sowDifferQuantity??>
                                <td id="${fbaItem.sku}">${fbaItem.quantity - fbaItem.sowDifferQuantity}</td>
                            <#else>
                                <td id="${fbaItem.sku}">0</td>
                            </#if>
                        </tr>
                    </#list>

                </tbody>
            </table>
        </div>
    </div>
    <div class="row">
        <button type="button" class="btn btn-info btn-modal" onclick="taskFinish();">
            拣货任务播种完成
        </button>
    </div>
<#elseif (domain.errorMsg)!>
    <div id="scan-box-error" style="font-size:15px ;font-weight:bold" class="error scan_error">${domain.errorMsg}</div>
</#if>