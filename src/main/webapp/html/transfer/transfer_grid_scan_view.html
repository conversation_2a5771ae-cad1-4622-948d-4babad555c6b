<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
.scan-a {
    width: 100px;
    height: 100px;
    line-height: 85px;
    text-align: center;
    margin-bottom: 50px;
}
</style>
</head>
<body>
 	<div>
   <div id="scan_datas">
	  	<div class="row">
            <span style="color: red;font-size:20px;margin-left: 100px;">播种差异格子：${domain.lessGridNoStr }</span><br/>
            <span style="color: red;font-size:20px;margin-left: 100px;">少拣货的格子：${domain.lessPickNoStr }</span><br/>
            <span style="color: red;font-size:20px;margin-left: 100px;">取消/待分配/已交运...的格子：${domain.cancelGridNoStr }</span><br/>

	  		<input type="hidden" name="scannumber" value="${domain.whApvGrid.number }" id = "scan-number"/>
	  		<input type="hidden" name="lessGridNoStr" value="${domain.lessGridNoStr }" id = "lessGridNoStr"/>
	  		<input type="hidden" name="lessPickNoStr" value="${domain.lessPickNoStr }" id = "lessPickNoStr"/>
	  		<input type="hidden" name="cancelGridNoStr" value="${domain.cancelGridNoStr }" id = "cancelGridNoStr"/>
	  		<input type="hidden" name="taskType" value="${domain.taskType }" id = "taskType"/>
	  		<input type="hidden" name="isAsn" value="${domain.isAsn }" id = "isAsn"/>
	  		<input type="hidden" name="scanapvid" value="${domain.whApvGrid.apvId }" id = "scan-apvId"/>
	  		
	  		<input type="hidden" name="scanapvno" value="${domain.whApvGrid.whApv.apvNo }" id = "scan-apvNo"/>
	  		
	  		<input type="hidden" name="scanstatus" value="${domain.whApvGrid.status}" id = "scan-status"/>
	  		
	  		<input type="hidden" name="scannum" value="${domain.apvCount}" id = "scan-num"/>
	  		
	  		<input type="hidden" name="scansuccess" value="${domain.successApvCount}" id = "scan-success-num"/> 
	  		
	  		<input type="hidden" name="scanwarehousetype" value="${domain.warehouseType}" id = "scan-warehouseType"/>
	  		
	  		<input type="hidden" name="scanpickingquantity" value="${domain.pickingQuantity}" id = "scan-pickingQuantity"/>
	  		
	  		<input type="hidden" name="scansowingquantity" value="${domain.sowingQuantity}" id = "scan-sowingQuantity"/>
            <input type="hidden" name="serialNumber" value="${domain.serialNumber}" id="serial-number"/>

	  		<input type="hidden" value="${domain.errorMsg }" id = "scan-error"/>
            <#if domain.gridNum??>
 				<#list 1 .. domain.gridNum  as grid>
                    <#assign whApvGrid = domain.whApvGridMap[grid?string]>
                    <div class="col-lg-1 col-md-1 " style="width: 10%;padding-left: 2px;padding-right: 2px">
                            <#if (domain.whApvGridMap[grid?string])! && domain.whApvGridMap[grid?string].status == 2>
                                <div class="panel panel-default my-success" style="height: 113px;line-height:113px;margin-bottom: 4px;text-align: center;cursor: pointer;" id="apv-grid-${grid }"
                                     data-toggle="popover" data-placement="${(grid<=20)? string('bottom','right') }" data-title="${domain.whApvGridMap[grid?string].whApv.apvNo }" data-trigger="hover" data-html="true"
                                     data-content='<table class="table table-bordered table-condensed">
                                          <thead>
                                             <tr class="">
                                                <th><div class="" style="width:90px;">SKU</div></th>
                                                <th><div class="">已播</div></th>
                                                <th><div class="">应播</div></th>
                                                <th><div class="">已拣</div></th>
                                                <th><div class="">差异值</div></th>
                                             </tr>
                                          </thead>
                                          <tbody>
                                            <#assign viewCount = 0 />
                                            <#list whApvGrid.gridItems as gridItem>
                                                <#assign viewCount = (viewCount + 1) />
                                                 <tr>
                                                    <td>${gridItem.itemSku }</td>
                                                    <td>${gridItem.gridQuantity }</td>
                                                    <td>${gridItem.itemQuantity }</td>
                                                    <td>${gridItem.pickQuantity }</td>
                                                    <td>${gridItem.itemQuantity - gridItem.gridQuantity }</td>
                                                 </tr>
                                             </#list>
                                          </tbody>
                                       </table>' >
                                    <span style="font-size: 60px;color: white">${grid}</span>
                                </div>
                            <#elseif (domain.whApvGridMap[grid?string])! && domain.whApvGridMap[grid?string].status == 1 && domain.whApvGridMap[grid?string].hasGrig== 'yes'>
                                <div class="panel panel-default my-warning" style="height: 113px;line-height:113px;margin-bottom: 4px;text-align: center;cursor: pointer;" id="apv-grid-${grid }"
                                data-toggle="popover" data-placement="${(grid<=20)? string('bottom','right') }" data-title="${domain.whApvGridMap[grid?string].whApv.apvNo }" data-trigger="hover" data-html="true"
                                     data-content='<table class="table table-bordered table-condensed">
                                          <thead>
                                             <tr class="">
                                                <th><div class="" style="width:90px;">SKU</div></th>
                                                <th><div class="">已播</div></th>
                                                <th><div class="">应播</div></th>
                                                <th><div class="">已拣</div></th>
                                                <th><div class="">差异值</div></th>
                                             </tr>
                                          </thead>
                                          <tbody>
                                            <#assign viewCount = 0 />
                                            <#list whApvGrid.gridItems as gridItem>
                                                <#assign viewCount = (viewCount + 1) />
                                                 <tr>
                                                    <td>${gridItem.itemSku }</td>
                                                    <td>${gridItem.gridQuantity }</td>
                                                    <td>${gridItem.itemQuantity }</td>
                                                    <td>${gridItem.pickQuantity }</td>
                                                    <td>${gridItem.itemQuantity - gridItem.gridQuantity }</td>
                                                 </tr>
                                             </#list>
                                          </tbody>
                                       </table>' >
                                    <span style="font-size: 60px;color: white">${grid}</span>
                                </div>
                            <#else>
                                <div class="panel panel-default my-disabled" style="height: 113px;line-height:113px;margin-bottom: 4px;text-align: center;cursor: pointer;" id="apv-grid-${grid }">
                                    <span style="font-size: 60px;color: white">${grid}</span>
                                </div>
                            </#if>
                    </div>
                </#list>
            </#if>
        </div>
	  </div>
  </div>
  </body>
</html>