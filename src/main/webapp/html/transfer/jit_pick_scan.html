<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		#task-list td {
			vertical-align:middle;
		}
		.form-control{
			height:42px;
		}
		.my-success {
			background-color: #19be6b;
		}
		.my-warning {
			background-color: #ff9900;
		}
		.my-disabled {
			background-color: #c5c8ce;
		}
	</style>
</head>
<body>
	<@header method="header" active="15060000"><#include "/ftl/header.ftl"></@header>
	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">中转仓管理</a></li>
					<li class="active">中转仓分拣</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<div class="row">
			<div class="form-body">
				<div class="form-group">
					<label class="control-label col-md-1" style="text-align: right; line-height: 42px">面单号</label>
					<div class="col-md-2">
						<input type="text" class="form-control input-medium" name="orderid" id="orderid" onkeypress="if(event.keyCode==13) { inputnext(this); return false;}"tabindex="4">
					</div>
				</div>
			</div>
			<div style="margin: 35px 0 15px;border-bottom: 1px solid #ccc"></div>

			<div id="check-area">
				<div style="height: 441px;">
				  <div id="scan_datas">
					<#if domain.jitPickBoxs??>
						<div class="row" style="width: 85%;float: left;">
							<#list 2 .. 30  as boxNumber>
								<#assign grid = domain.jitPickBoxMap[boxNumber?string]>
								<div class="col-lg-1 col-md-1" style="width: 10%;padding-left: 5px;padding-right: 5px">
									<#if (domain.jitPickBoxMap[boxNumber?string])! && grid.status == 2>
										<div class="panel panel-default my-success" style="height: 113px;line-height:113px;margin-bottom: 4px;text-align: center;cursor: pointer;" id="apv-grid-${grid.number }">
											<span style="font-size: 60px;color: white">${grid.number}</span>
										</div>
									<#elseif (domain.jitPickBoxMap[boxNumber?string])! && grid.status == 1>
										<div class="panel panel-default my-warning" style="height: 113px;line-height:113px;margin-bottom: 4px;text-align: center;cursor: pointer;" id="apv-grid-${grid.number }" >
											<span style="font-size: 60px;color: white">${grid.number}</span>
										</div>
									<#else>
										<div class="panel panel-default my-disabled" style="height: 113px;line-height:113px;margin-bottom: 4px;text-align: center;cursor: pointer;" id="apv-grid-${boxNumber }">
											<span style="font-size: 60px;color: white">${boxNumber}</span>
										</div>
									</#if>
								</div>
							</#list>
						</div>
						<div>
							<div class="col-lg-1 col-md-1 " style="width: 12%;padding-left: 5px;padding-right: 5px">
								<#if (domain.jitPickBoxMap[1?string])! && domain.jitPickBoxMap[1?string].status == 2>
									<div class="panel panel-default my-success"
										 style="height: 113px;line-height:113px;margin-bottom: 4px;text-align: center;cursor: pointer;"
										 id="apv-grid-1">
										<span style="font-size: 60px;color: white">单</span>
									</div>
								<#elseif (domain.jitPickBoxMap[1?string])! && domain.jitPickBoxMap[1?string].status == 1 && domain.jitPickBoxMap[1?string].hasGrig== 'yes'>
									<div class="panel panel-default my-warning" style="height: 113px;line-height:113px;margin-bottom: 4px;text-align: center;cursor: pointer;" id="apv-grid-1" >
										<span style="font-size: 60px;color: white">单</span>
									</div>
								<#else>
									<div class="panel panel-default my-disabled" style="height: 113px;line-height:113px;margin-bottom: 4px;text-align: center;cursor: pointer;" id="apv-grid-1">
										<span style="font-size: 60px;color: white">单</span>
									</div>
								</#if>
							</div>
						</div>

					</#if>
				  </div>
				</div>
			</div>
			
		</div><!-- end row -->
		<div id="info" onclick="{$('#info').hide();}" style='position:absolute;font-size:120px;text-align:center;width:500px;height:500px;line-height:500px;top:180px;left:50%;background-color:#7bbbd6;display:none;'></div>
	</div>
	<#include "/common/footer.html">
	</div>
	<!-- 	打印插件 -->
   	<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
   	<script type="text/javascript" src="${CONTEXT_PATH }js/jquery.sound.js?v=${.now?datetime}"></script>

	
	<script type="text/javascript">
        $(function () {
            $('[data-toggle="popover"]').popover()
        })

        
		$(document).ready(function(){
			$('#orderid').val('');
			$('#orderid').focus();
		});

		// 输入单号
		function inputnext(obj){
			$("#info").hide();
			if(!obj.value || obj.value.replace(/\s/g,'') == ''){
				layer.alert("请输入面单号!");
				return ;
			}

			var r= $.ajax({

				url : CONTEXT_PATH + "jitPick/scan/orderNo",
				data : {orderNo : $.trim($("#orderid").val())},
				timeout : 100000,
				beforeSend : function() {
					App.blockUI(null, null, 10000);
				},
				success : function(response) {
					App.unblockUI();
					var responseHtml = $(response).find("#scan_datas").html();
					var errorMsg = $(response).find("#scan_datas").find("#scan-error").val();
					if(errorMsg){
						audioPlay("error");
						layer.open({
							content: errorMsg,
							yes: function (index) {
								$('#orderid').val("");
								$('#orderid').focus();
								layer.close(index);
							},
							cancel: function(){
								$('#orderid').val("");
								$('#orderid').focus();
							}
						});
						return;
					}

					var number = $(response).find("#scan_datas").find("#scan-number").val();
					var status = $(response).find("#scan_datas").find("#scan-status").val();

					$("#scan_datas").html(responseHtml);
					$('[data-toggle="popover"]').popover();

					if(number) {
						if (status == 2) {
							// 延迟播放
							setTimeout(sayOk, 700);
						}
						if (number == "1" || number == 1){
							number = "单";
						}
						$("#info").html(number).show();
						$('#orderid').val("");
						$('#orderid').focus();
						if (number == "单") {
							audioPlay("dan");
						} else {
							audioPlay(number);
						}

					} else {
						audioPlay("error");
						layer.open({
							content: "单号不存在或者单据不是当天合单",
							yes: function (index) {
								$('#orderid').val("");
								$('#orderid').focus();
								layer.close(index);
							},
							cancel: function(){
								$('#orderid').val("");
								$('#orderid').focus();
							}
						});

					}
				},
				error : function() {
					App.unblockUI();
					layer.alert('扫描失败，请重新扫描');
				}
			});
		}


	
		function sayOk() {
			audioPlay("success");
		}

	
	</script>
</body>
</html>