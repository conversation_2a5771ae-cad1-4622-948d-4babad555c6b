<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<#include "/common/include.html">
	<style type="text/css">
		#boxSku-body td{
			/*width: 25%;*/
			text-align: center;
		}
		#boxSku-body a{
			color:#169BD5;
			margin-left: 5px;
		}
		.no-wrap-style{
			background-color: #e4e4e4 !important;
			font-size: 14px;
			font-weight: 600;
		}
	</style>
</head>
<body class="page-header-fixed page-full-width">
	<div id="order-log-area" style="max-height: 410px;overflow-x: auto">
		<div class="form-group">
			<div style="display:inline-block;width: 50%;">
				<#if domain.orderType?? && domain.orderType == '21'>
					<h4>出库单号：<span id="orderNo">${domain.whFbaAllocation.fbaNo}</span></h4>
					<#else >
					<h4>FBA调拨单号：<span id="orderNo">${domain.whFbaAllocation.fbaNo}</span></h4>
				</#if>
			</div>
			<div style="display:inline-block;width: 48%;margin-left: 10px;">
				<input type="hidden" id="fbaBoxNum" value="${(domain.boxList?size)-1}"/>
				<button style="float: right;" type="button" class="btn btn-primary" onclick="addBox()">
					增加箱数
				</button>
			</div>
		</div>
		<table class="table table-condensed table-bordered  table-striped">
			<thead>
				<tr>
					<#if domain.whFbaAllocation?? && domain.whFbaAllocation.isAsn?? && domain.whFbaAllocation.isAsn==true>
						<th>套装SKU</th>
						<th>SKU</th>
					<#elseif domain.orderType?? && domain.orderType == '21'>
						<th>SKU</th>
					<#else >
						<th>FNSKU</th>
					</#if>
					<#list domain.boxList as box>
						<#if box_has_next>
							<th class="box-line" name="box_${box}">箱号${box}</th>
						<#else >
							<th>${box}</th>
						</#if>
					</#list>
				</tr>
			</thead>
			<tbody id="boxSku-body">
				<#list (domain.skuAndBoxMap)?keys as skuKey>
					<tr name="sku-tr">
						<#if domain.whFbaAllocation?? && domain.whFbaAllocation.isAsn?? && domain.whFbaAllocation.isAsn==true && domain.fnSkuMap?? && domain.fnSkuMap[skuKey]??>
							<td name="${skuKey}">
								<#if (domain.fnSkuMap[skuKey][0].suitFlag)! && domain.fnSkuMap[skuKey][0].suitFlag==1>
									${skuKey}<span style="color: red;">（${domain.fnSkuMap[skuKey][0].skuSuitNum}套）</span>
								<#else >
									${domain.fnSkuMap[skuKey][0].productSku}
								</#if>
							</td>
							<td>
								<#list domain.fnSkuMap[skuKey] as item>
									<dd>${item.productSku}*<span style="color: red;">${item.quantity}</span></dd>
								</#list>
							</td>
						<#else >
							<td name="${skuKey}">${skuKey}</td>
						</#if>
						<#list domain.skuAndBoxMap[skuKey] as item>
							<#if item_has_next>
								<td data-index="${item_index+1}" class="box-line" name="box_${item_index+1}">
									<span>${(item.loadNum)!0}</span>
									<input onblur="check(this,3)" class="form-control" type="hidden" name="loadNum" value="${item.loadNum}">
								</td>
							<#else >
								<td name="total-and-quantity">
									<span name="total-loadNum">${item.loadNum}</span>/
									<span name="total-pickNum">${item.pickQuantity}</span>/
									<span name="total-num">${item.quantity}</span>
								</td>
							</#if>
						</#list>
					</tr>
				</#list>
				<tr>
					<td class="no-wrap-style">尺寸（CM）</td>
					<#if domain.whFbaAllocation?? && domain.whFbaAllocation.isAsn?? && domain.whFbaAllocation.isAsn==true>
						<td></td>
					</#if>
					<#list domain.boxList as box>
						<#if (domain.boxMap[box])??>
							<td data-index="${box}" class="box-line" name="box_${box}">
								<span name="size">${domain.boxMap[box].productLength}*${domain.boxMap[box].productWidth}*${domain.boxMap[box].productHeight}</span>
								<div style="float:left;">
									<input onblur="check(this,1)" style="display:inline;width: 50px;" placeholder="长" class="form-control" type="hidden" name="length" value="${domain.boxMap[box].productLength}" min="0">
								</div>
								<div style="float:left;margin-left: 5px;">
									<input onblur="check(this,1)" style="display:inline;width: 50px;" placeholder="宽" class="form-control" type="hidden" name="width" value="${domain.boxMap[box].productWidth}" min="0">
								</div>
								<div style="float:left;margin-left: 5px;">
									<input onblur="check(this,1)" style="display:inline;width: 50px;" placeholder="高" class="form-control" type="hidden" name="height" value="${domain.boxMap[box].productHeight}" min="0">
								</div>
							</td>
						</#if>
					</#list>
					<td></td>
				</tr>
				<tr>
					<td class="no-wrap-style">重量（KG）</td>
					<#if domain.whFbaAllocation?? && domain.whFbaAllocation.isAsn?? && domain.whFbaAllocation.isAsn==true>
						<td></td>
					</#if>
					<#list domain.boxList as box>
						<#if (domain.boxMap[box])??>
							<td data-index="${box}" class="box-line" name="box_${box}">
								<span>${domain.boxMap[box].productWeight}</span>
								<input onblur="check(this,2)" class="form-control input box-weight" type="hidden" name="weight" value="${domain.boxMap[box].productWeight}">
							</td>
						</#if>
					</#list>

					<td></td>
				</tr>
				<tr name="handle-tr">
					<td class="no-wrap-style">操作</td>
					<#if domain.whFbaAllocation?? && domain.whFbaAllocation.isAsn?? && domain.whFbaAllocation.isAsn==true>
						<td></td>
					</#if>
					<#list domain.boxList as box>
						<#if box_has_next >
							<td data-index="${box}" class="box-line" name="box_${box}">
								<#if box_index != 0>
									<a style="color:#D9001B" data-action="deleteTemplate">删除</a>
								</#if>
								<a data-action="editTemplate">编辑</a>
								<a data-action="printBoxNo">打印箱号</a>
							</td>
							<#else >
							<td></td>
						</#if>
					</#list>
				</tr>
			</tbody>
		</table>
	</div>
</body>
<!-- END BODY -->
<script>

	$('#boxSku-body').on('click', "a[data-action]", function () {
		var action = $(this).data("action");
		var num = $(this).closest('td').attr('data-index');
		if (action == "deleteTemplate") {
			if (confirm("确认删除?")) {
				$("table").find("td[name='box_" + num + "']").remove();
				$("table").find("th[name='box_" + num + "']").remove();
				refreshBoxIndex();
				$('#boxSku-body').find("tr[name='sku-tr']").each(function () {
					$(this).find('input').each(function () {
						var $this2 = $(this);
						refreshTotalNum($this2);
					});
				});
				var boxNum = $("thead").find("th[class='box-line']").length;
				$('#fbaBoxNum').val(boxNum);
			}
		} else if (action == "editTemplate"){
			var tdEle = $(this).closest('td');
			$(tdEle).find('a').remove();
			$(tdEle).append("<a data-action='saveTemplate'>保存</a>");

			$("table").find("td[name='box_" + num + "']").each(function () {
				$(this).find('span').css("display", "none");
				$(this).find('input').attr("type", "number");
			});
		} else if (action == "saveTemplate") {
			var flag = true;
			var haveNum = false;
			var findSize = $("table").find("td[name='box_" + num + "']").length;
			var index = findSize - 4;
			$("table").find("td[name='box_" + num + "']").each(function (i) {

				$(this).find('input').each(function () {
					if (i <= index && $(this).val() != '' && $(this).val() > 0){
						haveNum = true;
					}else if (i <= index && ($(this).val() == '' || $(this).val() == 0)){
						$(this).val(0);
					}
					if (i > index && haveNum && ($(this).val() == '' || $(this).val() == 0)){
						$(this).val(0);
						layer.alert("箱号"+num+"重量、尺寸输入框请输入正确的数字!",'error');
						flag = false;
						return false;
					}
				});
				if (!flag){
					return false;
				}
			});
			if (!haveNum){
				layer.alert("箱号"+num+"SKU装箱数量输入框不能全部为空，或输入不正确!",'error');
				flag = false;
			}
			if (flag){
				doSaveTemplate(this,num);
			}
		} else if (action == "printBoxNo") {
			var fbaNo = $("#orderNo").text();
			var boxNum = $('#fbaBoxNum').val();
			window.open(CONTEXT_PATH + "fba/allocation/toPrintBox?fbaNo=" + fbaNo + "&boxNo=" + num + "&boxNum=" + boxNum,'_blank');
		}
	});

	function doSaveTemplate(obj,num) {
		var tdEle = $(obj).closest('td');
		$(tdEle).find('a').remove();
		var deleteHtml = "<a style='color:#D9001B' data-action='deleteTemplate'>删除</a>";
		var editHtml = "<a data-action='editTemplate'>编辑</a>";
		var printHtml =	"<a data-action='printBoxNo'>打印箱号</a>";
		if (num == 1) {
			$(tdEle).append(editHtml).append(printHtml);
		} else {
			$(tdEle).append(deleteHtml).append(editHtml).append(printHtml);

		}
		$("table").find("td[name='box_" + num + "']").each(function () {
			$(this).find('span').css("display", "block");
			$(this).find('input').attr("type", "hidden");
			$(this).find('span').text($(this).find('input').val());
			if ($(this).find('span').attr('name') == 'size'){
				var sizeStr = $(this).find("input[name='length']").val()+"*"+$(this).find("input[name='width']").val()+"*"+$(this).find("input[name='height']").val();
				$(this).find('span').text(sizeStr);
			}
		});
		var boxNum = $("thead").find("th[class='box-line']").length;
		$('#fbaBoxNum').val(boxNum);
	}

	
	function refreshBoxIndex() {
		$('table>thead').find('.box-line').each(function (boxNum) {
			boxNum++;
			$(this).text("箱号"+boxNum);
			$(this).attr("name","box_"+boxNum);
		});
		$('#boxSku-body').find('tr').each(function () {
			$(this).find('.box-line').each(function (num) {
				num++;
				$(this).attr("name","box_"+num);
				$(this).attr("data-index",num);
			});
		});

	}

	function check(obj, type) {
		var sku = $(obj).parent().parent().children()[0].innerText;

		var mes = "尺寸";
		if (type == 2) {
			mes = "重量";
		} else if (type == 3) {
			mes = "装箱数量";
		}
		var value = $(obj).val();
		if (value != "") {
			var reg3 = /^\+?[1-9][0-9]*$/;
			if (type == 3 && !reg3.test(value)) {
				layer.alert("FNSKU：" + sku + mes + "输入错误，请输入正确的正整数", 'error');
				$(obj).val(0);
				return;
			} else {
				var reg = /^\+?[1-9][0-9]*$|^[+]{0,1}(\d+\.\d{1,2})$/;
				if (!reg.test(value)) {
					layer.alert("请输入正确的" + mes + "数据，支持小数点后两位", 'error');
					$(obj).val(0);
				}
			}
		} else {
			layer.alert(mes + "不能为空", "error");
			$(obj).val(0);
			return;
		}

		refreshTotalNum(obj);

	}

	function refreshTotalNum(obj) {
		var loadingQuantity = 0;
		var skuTr = $(obj).parent().parent();
		var sku = $(skuTr).children()[0].innerText;
		$(skuTr).find("input[name='loadNum']").each(function () {
			var loadStr = $(this).val();
			loadingQuantity = loadingQuantity + parseInt(loadStr == '' ? 0 : loadStr);
		});
		var pickNum = parseInt($(skuTr).find("span[name='total-pickNum']").text());
		var totalNum= parseInt($(skuTr).find("span[name='total-num']").text());
		var value = $(obj).val() == null ? 0 : $(obj).val();
		//待发：SKU需要发货的数量 保存时校验合计<已捡
		if (pickNum < loadingQuantity  || totalNum<loadingQuantity) {
		//	layer.alert("FNSKU：" + sku + " 装箱数量合计 或 待发数量 需小于等于已捡", "error");
			$(skuTr).find("span[name='total-loadNum']").text(loadingQuantity - value);
			$(obj).val("");
		} else {
			$(skuTr).find("span[name='total-loadNum']").text(loadingQuantity);
		}
	}

	function addBox() {
		var boxNum = $('table>thead').find('.box-line').length + 1;
		var boxHtml = "<th class='box-line' name='box_"+boxNum+"'>箱号"+boxNum+"</th>";
		$('table>thead').find('th').last().before(boxHtml);

		var loadNumHtml = "<td data-index='"+boxNum+"' class='box-line' name='box_"+boxNum+"'><span style='display: none'>0</span>" +
				"<input onblur='check(this,3)' class='form-control' type='number' name='loadNum' value='0'></td>";
		$('#boxSku-body').find("td[name='total-and-quantity']").before(loadNumHtml);

		var sizeHtml = "<td data-index='"+boxNum+"' class=\"box-line\" name='box_"+boxNum+"'>\n" +
						"<span name=\"size\" style='display: none'>--</span>\n" +
						"<div style=\"float:left;\">\n" +
						"<input onblur=\"check(this,1)\" style=\"display:inline;width: 50px;\" placeholder=\"长\" class=\"form-control\" type=\"number\" name=\"length\" value='0' min=\"0\">\n" +
						"</div>\n" +
						"<div style=\"float:left;margin-left: 5px;\">\n" +
						"<input onblur=\"check(this,1)\" style=\"display:inline;width: 50px;\" placeholder=\"宽\" class=\"form-control\" type=\"number\" name=\"width\" value='0' min=\"0\">\n" +
						"</div>\n" +
						"<div style=\"float:left;margin-left: 5px;\">\n" +
						"<input onblur=\"check(this,1)\" style=\"display:inline;width: 50px;\" placeholder=\"高\" class=\"form-control\" type=\"number\" name=\"height\" value='0' min=\"0\">\n" +
						"</div>\n" +
						"</td>";

		$('#boxSku-body').find("input[name='length']").parent().parent().last().after(sizeHtml);

		var weightHtml = "<td data-index='"+boxNum+"' class=\"box-line\" name='box_"+boxNum+"'><span style='display: none'>--</span>\n" +
				"<input onblur=\"check(this,2)\" class=\"form-control input box-weight\" type=\"number\" name=\"weight\" value=\"0\">\n" +
				"</td>";
		$('#boxSku-body').find("input[name='weight']").parent().last().after(weightHtml);

		var handleHtml = "<td data-index='"+boxNum+"' data-index='"+boxNum+"' class='box-line' name='box_"+boxNum+"'>" +
				"<a style='color:#D9001B' data-action='deleteTemplate'>删除</a><a data-action='saveTemplate'>保存</a></td>";
		$('#boxSku-body').find("tr[name='handle-tr']").find('td').last().before(handleHtml);

	}

</script>
</html>