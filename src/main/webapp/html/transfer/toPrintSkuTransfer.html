<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<#include "/common/include.html">
</head>
<body class="page-header-fixed page-full-width">
	<div id="order-log-area" style="max-height: 405px;overflow: auto">
		<table class="table table-condensed table-bordered  table-striped">
			<colgroup>
				<col width="35%"/>
				<col width="35%"/>
				<col width="30%"/>
			</colgroup>
			<thead>
				<tr>
					<th>SKU</th>
					<th>打印数量</th>
					<th>操作</th>
				</tr>
			</thead>
			<tbody id="printSku-body">
				<#list domain.items as item>
					<tr>
						<td style="vertical-align: middle;">
							${item.productSku}
						</td>
						<td style="vertical-align: middle;">
							<input class="form-control input-small print-quantity" type="number" min="0" name="quantity" id="qty_${item.productSku}" value="0">
						</td>
						<td style="vertical-align: middle;">
							<button class="btn btn-primary" onclick="printTag('${domain.whFbaAllocation.id}','${item.productSku}')" style="height: 30px;">  打印</button>
						</td>
					</tr>
				</#list>
			</tbody>
		</table>
	</div>
</body>
<!-- END BODY -->
<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/packing.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/print.js?v=${.now?datetime}"></script>
<script>
	$(document).ready(function(){
		initPrinter();
	});
	function printTag(id, sku) {
		if (!jitPrinter || !jitPrinterTag || !jitPrinter75Tag) {
			layer.alert("请先配置打印机",'error');
			return
		}
		var copies = $("#qty_" + sku).val();
		if (copies <= 0) {
			layer.alert("请输入打印数量",'error');
			return
		}
		var url = CONTEXT_PATH + "fba/packs/printTag?id=" + id + "&sku=" + sku;
		$.ajax({
			url: url,
			type:"GET",
			data:{id:id},
			success : function(response){
				let res = $(response);
				var printUrl = res.find("input[name='printUrl']").val();
				var purposeHouse = res.find("input[name='purposeHouse']").val();
				if (printUrl) {
					printUrl = window.location.origin + printUrl;
					var pageLength = "60mm";
					var pageWidth = "60mm";
					printPdfCopies(printUrl, jitPrinter75Tag, pageLength, pageWidth,1);
				} else {
					var map = new Map();
					res.find("input[name='base64']").each(function () {
						var key = $(this).val();
						var num = map.get(key);
						if (num === undefined) {
							num = 0;
						}
						num++;
						map.put(key, num);
					})
					map.each(function (k, v) {
						if(purposeHouse && purposeHouse == 'Shein'){
							printWeirdPdfCopies(k, jitPrinter75Tag, "70mm", "60mm", "70mm", "30mm", copies);
						}else {
							printCopies(k, jitPrinterTag, null, copies, null);
						}
					});
					// 打印合并后sku标签
					var mergePdfList = [];
					res.find("input[name='base64Sku']").each(function () {
						var pdf = $(this).val();
						mergePdfList.push(pdf);
					});
					// var copies = res.find("input[name='printCopies']").val();
					printMergeCopies(mergePdfList,jitPrinter75Tag,copies);
				}
			}
		});
	}
</script>
</html>