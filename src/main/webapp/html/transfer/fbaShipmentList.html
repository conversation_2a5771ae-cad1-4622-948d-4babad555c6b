<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<#include "/common/include.html">
		<style type="text/css">
			.modal-body input {
				width: 100%;
			}
			.modal-body .box_size_td input {
				width: 31%;
				float:left;
				margin-left:2px;
			}
			.control-label {
				margin-top: 2px;
			}
			.form-bordered .control-label {
				padding-top: 14px;
			}
			.form-horizontal .control-label {
				text-align: right;
			}
			.col-md-1 {
				padding-left: 10px;
				padding-right: 0px;
				width: 5%;
				font-size: 12px;
			}
			.col-md-2 {
				padding-left: 10px;
				padding-right: 0px;
				width: 7.5%;
				font-size: 12px;
			}
			.form-control {
				height: 30px;
				padding: 0px 4px;
				border: 1px solid #ddd;
				color: #000;
				font-size: 10px;
				font-weight: normal;
			}
			.form-bordered .form-group > div {
				padding: 4px 10px 0;
			}
			/*启用table滚动条*/
			.my-div-table{
				overflow-x: auto;
				overflow-y: auto;
				height: 654px;
				width: auto;
				padding-bottom: 38px;
				/*width:1920px;*/
			}
			#box_info_table tr th{
				width:160px;
			}
			#task-list-warp td{
				vertical-align: middle;
			}
			#fixedDiv thead>tr>th {
				vertical-align: middle;
			}
			#task-list thead>tr>th {
				vertical-align: middle;
			}
		</style>
	</head>

	<body>
		<@header method="header" active="15040100"><#include "/ftl/header.ftl"></@header>

		<div id="page" style="background-color: rgb(231, 237, 248)">
			<div class="row">
				<div class="col-md-12" style="padding: 0">
					<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
						<li>
							<a href="#">中转仓</a>
						</li>
						<li class="active">FBA货件单管理</li>
					</ul>
				</div>
			</div>
			<!-- 内容 -->
			<div class="container-fluid" style="background-color: white;border: none">
				<div class="row">
					<div class="col-md-12">
						<form action="${CONTEXT_PATH}fba/shipment/search" class="form-horizontal form-bordered form-row-stripped"
							  method="post" modelAttribute="domain" name="fbaShipmentForm" id="domain">
							<!-- 分页信息 -->
							<input id="page-no" type="hidden" name="page.pageNo" value="1">
							<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
							<div class="form-body">
								<div class="form-group">
									<label class="control-label col-md-1">YSTN:</label>
									<div class="col-md-2">
										<input class="form-control" type="text" name="query.fbaNo" placeholder="多个逗号分开" value="${domain.query.fbaNo }">
									</div>
									<label class="control-label col-md-1">货件单号:</label>
									<div class="col-md-2">
										<input class="form-control" type="text" name="query.shipmentId" placeholder="多个逗号分开" value="${domain.query.shipmentId }">
									</div>
									<label class="control-label col-md-1">状态:</label>
									<div class="col-md-2">
										<input class="form-control" type="text" name="query.status" placeholder="状态" value="${domain.query.status}">
									</div>
									<label class="control-label col-md-1">FNSKU:</label>
									<div class="col-md-2">
										<input class="form-control" type="text" name="query.fnSku" placeholder="多个逗号分开" value="${domain.query.fnSku}">
									</div>
									<label class="control-label col-md-1">SKU:</label>
									<div class="col-md-2">
										<input class="form-control" type="text" name="query.sku" placeholder="多个逗号分开" value="${domain.query.sku}">
									</div>
									<label class="control-label col-md-1">确认发货时间：</label>
									<div class="col-md-2">
										<input class="form-control Wdate" type="text" name="query.fromConfirmTime" placeholder="" readonly="readonly" value="${domain.query.fromConfirmTime }"
											   onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
									<label class="control-label col-md-1" style="font-weight: bolder">到</label>
									<div class="col-md-2">
										<input class="form-control Wdate" type="text" name="query.toConfirmTime" placeholder="" readonly="readonly" value="${domain.query.toConfirmTime }"
											   onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
								</div>
                                <div class="form-group">
									<label class="control-label col-md-1">交运时间：</label>
									<div class="col-md-2">
										<input class="form-control Wdate" type="text" name="query.fromDeliverTime" placeholder="" readonly="readonly" value="${domain.query.fromDeliverTime }"
											   onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
									<label class="control-label col-md-1" style="font-weight: bolder">到</label>
									<div class="col-md-2">
										<input class="form-control Wdate" type="text" name="query.toDeliverTime" placeholder="" readonly="readonly" value="${domain.query.toDeliverTime }"
											   onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
									<label class="control-label col-md-1">装车时间：</label>
									<div class="col-md-2">
										<input class="form-control Wdate" type="text" name="query.fromLoadTime" placeholder="" readonly="readonly" value="${domain.query.fromLoadTime }"
											   onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
									<label class="control-label col-md-1" style="font-weight: bolder">到</label>
									<div class="col-md-2">
										<input class="form-control Wdate" type="text" name="query.toLoadTime" placeholder="" readonly="readonly" value="${domain.query.toLoadTime }"
											   onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
                                </div>
							</div>

							<div>
								<div class="pull-left" style="margin-left: 10px;">
                                  <!--  <@header method="auth" authCode="TEMU_ALLOT">
									<button type="button" id="preBtn" class="btn btn-default" onclick="allot()">
										 分配库存
									</button>
                                    </@header>-->
								</div>

								<div class="col-md-offset-10" style="text-align: right">
									<button type="button" class="btn btn-default" onclick="downloadRecord()">
										<i class="icon-download"></i> 导出
									</button>
									<button type="button" class="btn default" onclick="formReset(this)">
										<i class="icon-refresh"></i> 重置
									</button>
									<button type="submit" class="btn blue">
										<i class="icon-search"></i> 查询
									</button>
								</div>
							</div>
						</form>
					</div>
					<br/>
				</div>

				<div class="row">
					<div id="fixedDiv" class="col-md-12">
						<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
							<colgroup>
								<col width="5%" />
								<col width="10%" />
								<col width="10%" />
								<col width="8%" />
								<col width="8%" />
								<col width="5%" />
								<col width="5%" />
								<col width="14%" />
								<col width="10%" />
								<col width="10%" />
							</colgroup>
							<thead>
								<tr>
									<th> 编号</th>
									<th>货件单号</th>
									<th>YSTN</th>
									<th>箱号</th>
									<th>尺寸信息</th>
									<th>重量信息</th>
									<th>状态</th>
									<th>运输方式<br/>物流方式<br/>追踪号<br/>物流单号</th>
									<th>时间</th>
									<th>操作</th>
								</tr>
							</thead>
						</table>
					</div>
					<div class="col-md-12" id="task-list-warp">
						<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
							<colgroup>
								<col width="5%" />
								<col width="10%" />
								<col width="10%" />
								<col width="8%" />
								<col width="8%" />
								<col width="5%" />
								<col width="5%" />
								<col width="14%" />
								<col width="10%" />
								<col width="10%" />
							</colgroup>
							<thead>
							<tr>
								<th> 编号</th>
								<th>货件单号</th>
								<th>YSTN</th>
								<th>箱号</th>
								<th>尺寸信息</th>
								<th>重量信息</th>
								<th>状态</th>
								<th>运输方式<br/>物流方式<br/>追踪号<br/>物流单号</th>
								<th>时间</th>
								<th>操作</th>
							</tr>
							</thead>
							<tbody>
							<#list domain.whFbaShipments as fbaShipment>
								<#list fbaShipment.boxGroup?keys as mKey>
									<#assign item = fbaShipment.boxGroup[mKey] />
									<#assign rowspan = fbaShipment.boxGroup?size />
									<tr>
										<#if mKey_index == 0>
											<td rowspan="${rowspan }">
												<label class="checkbox-inline"><input type="checkbox" value="${fbaShipment.id}" name="ids">${fbaShipment.id}</label>
											</td>
											<td rowspan="${rowspan }">${fbaShipment.shipmentId}</td>
											<td rowspan="${rowspan }">${fbaShipment.fbaNo}</td>
										</#if>
										<td>${item.boxNo}</td>
										<td>
											<#if item.productLength?? || item.productWidth?? || item.productHeight??>
												${item.productLength} * ${item.productWidth} * ${item.productHeight}
											</#if>
										</td>
										<td>${item.productWeight}</td>
										<#if mKey_index == 0>
											<td rowspan="${rowspan }">${util('enumName', 'com.estone.transfer.enums.AsnPrepareStatus', fbaShipment.status)}</td>
											<td rowspan="${rowspan }">
												${util('enumName', 'com.estone.transfer.enums.SmCodeStatus', fbaShipment.smCode)}
												<#if fbaShipment.shippingMethodByTms??><br/> <span style="color: #3f8fb5">${fbaShipment.shippingMethodByTms}</span></#if>
												<#if fbaShipment.trackingNumberByTms??><br/> <span style="color: #673ab7">${fbaShipment.trackingNumberByTms}</span></#if>
												<#if fbaShipment.shippingOrderNo??><br/> <span>${fbaShipment.shippingOrderNo}</span></#if>
											</td>
											<td rowspan="${rowspan }">
												<#if fbaShipment.confirmTime??>确认: ${fbaShipment.confirmTime}<br/></#if>
												<#if fbaShipment.deliverTime??>交运: ${fbaShipment.deliverTime}<br/></#if>
												<#if fbaShipment.loadTime??>装车: ${fbaShipment.loadTime}</#if>
											</td>
											<td rowspan="${rowspan }">
												<#if fbaShipment.status?? && (fbaShipment.status==13 || fbaShipment.status==17 || fbaShipment.status==18)>
													<!--<#if fbaShipment.transitType != 1>
														<a type="button" class="btn btn-xs btn-info" onclick="printXiangmai('${fbaShipment.id}')" disabled="disabled">打印箱唛</a>
													<#else>
													</#if>-->
													<a type="button" class="btn btn-xs btn-info" onclick="printXiangmai('${fbaShipment.id}')">打印箱唛</a>
													<#if fbaShipment.pdfUrl??>
														<a type="button" class="btn btn-xs btn-info" onclick="checkOutPrint('${fbaShipment.id}')">打印面单</a>
													<#else >
														<a type="button" class="btn btn-xs btn-info" onclick="checkOutPrint('${fbaShipment.id}')" disabled="disabled">打印面单</a>
													</#if>
													<a type="button" class="btn btn-xs btn-info" onclick="heavyTag('${fbaShipment.fbaId}')">重货标签</a>
												</#if>
												<button type="button" class="btn btn-xs btn-info" onclick="viewLog(${fbaShipment.id}, 'fbaShipment')">日志</button>
											</td>
										</#if>
									</tr>
								</#list>
							</#list>
							</tbody>
						</table>
					</div>
				</div>
				<div id="fixed-bottom">
					<div id="pager"></div>
				</div>
			</div>
			<#include "/common/footer.html">
			<img id="enlarge" style='position:fixed;width:800px;height:800px;top:15%;left:15%;display:none;'/>
		</div>

		<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
		<script type="text/javascript">
			// 分页
			var total = "${domain.page.totalCount}";
			var pageNo = "${domain.page.pageNo}";
			var pageSize = "${domain.page.pageSize}";

			$("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });
			var heights = $("body").height();
			if(heights > 910) {
				$("#fixed-bottom").addClass("fixed-bottom-height")
			}

			// 全选
			var checkAll = $("input[name='checkAll']");
			// 子选项
			var itemIds = $("input[name='ids']");
			checkAll.change(
				function() {
					itemIds.prop("checked", $(this).prop("checked"));
					itemIds.each(function() {
						var f = $(this).is(":checked");
						var checkClass = $(this).prop("class");
						$("." + checkClass).each(function() {
							$(this).prop("checked", f);
						})
					})
				}
			);
			// 获取选中的记录
			function getCheckedIds() {
				var checkedIds = $("input[name='ids']:checked");
				return checkedIds;
			}

			// 状态
			var statusJson = ${domain.statusSelectJson};
			$("input[name='query.status']").select2({
				data : statusJson,
				placeholder : "状态",
				allowClear : true
			});

			// 导出
			function downloadRecord() {
				var param = $("#domain").serialize();
				var checkedDatas = getCheckedIds();
				if(checkedDatas.length > 0) {
					param = param + "&" +checkedDatas.serialize();
				}
				$.post(CONTEXT_PATH + "fba/shipment/download", param, function(data){
					if (data.status == 200) {
						if (data.message==null || data.message==''){
							layer.alert('成功',function (index) {
								layer.close(index);
								diglog.close().remove();
								location.reload();
							});
						}else{
							customizeLayer(data.message);
						}
					} else {
						customizeLayer(data.message);
					}
				});
			}

			function printXiangmai(id) {
				if (confirm("确定打印？")) {
					setTimeout(function() {
						window.open(CONTEXT_PATH + "fba/shipment/printXiangmai?id=" + id);
					}, 1000);
				}
			}

			function checkOutPrint(id) {
				if (confirm("确定打印？")) {
					setTimeout(function() {
						var newWindow = window.open(CONTEXT_PATH + "fba/shipment/printCheckOut?id=" + id);
					}, 1000);
				}
			}

			//重货标签
			function heavyTag(id) {
				window.open(CONTEXT_PATH + "fba/allocation/printHeavyTag?id=" + id);
			}

		</script>
	</body>

</html>