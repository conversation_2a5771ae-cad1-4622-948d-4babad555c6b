<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		#task-list td {
			vertical-align:middle;
		}
	</style>
</head>
<body>
<@header method="header" active="12010000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">发货单</a></li>
				<li class="active">发货单列表</li>
			</ul>
		</div>
	</div>
	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
	<#assign query=domain.query>
	<#assign saleMap=domain.saleChanneMap>
	<!-- END PAGE HEADER-->
	<div class="row">
		<div class="col-md-12">
			<form action="${CONTEXT_PATH}apvs/search"
					   class="form-horizontal form-bordered form-row-stripped"
					   method="post" modelAttribute="domain" name="apvForm" id="domain">
				<!-- 分页信息 -->
				<input id="page-no" type="hidden" name="page.pageNo" value="1">
				<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
				<div class="form-body">
					<div class="form-group">
						<label class="control-label col-md-1">发货单号</label>
						<div class="col-md-3">
							<input class="form-control" type="text" name="query.apvNo" placeholder="请输入发货单号" value="${query.apvNo}" id= "apvNo-id">
						</div>
						<label class="control-label col-md-1">状态</label>
						<div class="col-md-3">
							<input class="form-control" type="text" name="query.statusToStr"  value="${query.statusToStr}">
						</div> 
						<label class="control-label col-md-1">发货单类型</label>
						<div class="col-md-3">
							<input class="form-control" name="query.apvType" type="text" value="${query.apvType}">
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-md-1">编号</label>
						<div class="col-md-3">
							<input class="form-control" type="text" name="query.apvIdsString" placeholder="请输入数字编号,支持逗号分隔查询" value="${query.apvIdsString}">
						</div>
						<label class="control-label col-md-1">物流方式</label>
						<div class="col-md-3">
							<input class="form-control" type="text" name="query.logisticsCompany"  value="${query.logisticsCompany}">
						</div>
						<label class="control-label col-md-1">
                            <select name="query.numberType" class="form-control" style="width:80px;float:right;border:none;margin-top:-9px;margin-right:-5px;">
                                <option value="1" ${(query.numberType == 1)?string('selected', '')}>追踪单号</option>
                                <option value="2" ${(query.numberType == 2)?string('selected', '')}>货代单号</option>
                            </select>
                        </label>
						<div class="col-md-3">
							<input class="form-control" type="text" name="query.numberText"  value="${query.numberText}">
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-md-1">SKU</label>
						<div class="col-md-3">
							<input class="form-control" type="text" name="query.likeSku"  value="${query.likeSku}" id="uuidSku" onkeypress="if(event.keyCode==13) { inputSku(this); return false;}" >
						</div>
						
						<label class="control-label col-md-1">仓库</label>
						<div class="col-md-3">
							<input class="form-control" name="query.warehouseStr" type="text" value="${query.warehouseStr}" id="warehouseStr-id">
						</div> 
						
						<label class="control-label col-md-1">平台</label>
						<div class="col-md-3">
							<input class="form-control" type="text" name="query.saleChannel"  value="${query.saleChannel}">
						</div>
					</div>
					<div id="expand-area">
						<div class="form-group">
							<label class="control-label col-md-1">频次</label>
							<div class="col-md-3 input-group">
								<input type="number"  name="query.thenHz"  id="thenHz"  value="${query.thenHz }"  class="Wdate form-control"   digits = "true"/>
								<span class="input-group-addon">到</span>
								<input type="number"  name="query.lessHz"  id="lessHz"  value="${query.lessHz }"  class="Wdate form-control"  digits = "true"/>
							</div>

							<label class="control-label col-md-1">面单尺寸</label>
							<div class="col-md-3">
								<select name="query.waybillSize" class="form-control" id = "waybillSize-id">
									<option vlaue=""></option>
									<option value="1">100*100</option>
									<option value="2">100*150</option>
								</select>
							</div>

							<label class="control-label col-md-1">推单时间</label>
							<div class="col-md-3">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.fromCreateDate" placeholder="" readonly="readonly" value="${query.fromCreateDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.toCreateDate" placeholder="" readonly="readonly" value="${query.toCreateDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1">周转筐</label>
							<div class="col-md-3">
								<input class="form-control" type="text" name="query.boxNo"  value="${query.boxNo}">
							</div>

							<label class="control-label col-md-1">拣货任务号</label>
							<div class="col-md-3">
								<input class="form-control" type="text" name="query.taskNo"  value="${query.taskNo}">
							</div>

							<label class="control-label col-md-1">合单时间</label>
							<div class="col-md-3">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.fromTaskDate" placeholder="" readonly="readonly" value="${query.fromTaskDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.toTaskDate" placeholder="" readonly="readonly" value="${query.toTaskDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>

							<label class="control-label col-md-1">播种完成时间</label>
							<div class="col-md-3">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.fromSowDate" placeholder="" readonly="readonly" value="${query.fromSowDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.toSowDate" placeholder="" readonly="readonly" value="${query.toSowDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>

							<label class="control-label col-md-1">包装完成时间</label>
							<div class="col-md-3">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.fromPackDate" placeholder="" readonly="readonly" value="${query.fromPackDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.toPackDate" placeholder="" readonly="readonly" value="${query.toPackDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>

							<label class="control-label col-md-1">交运时间</label>
							<div class="col-md-3">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.fromDeliverDate" placeholder="" readonly="readonly" value="${query.fromDeliverDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.toDeliverDate" placeholder="" readonly="readonly" value="${query.toDeliverDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1">DD号</label>
							<div class="col-md-3">
								<input class="form-control" type="text" name="query.salesRecordNumbers"  value="${query.salesRecordNumbers}">
							</div>

							<label class="control-label col-md-1">锁状态</label>
							<div class="col-md-3">
								<select class="form-control" name="query.whApvLock.status" value="${query.whApvLock.status}" id="lock-status">
									<option value=""></option>
									<option value="2">没锁</option>
									<option value="1">有锁</option>
								</select>
							</div>

							<label class="control-label col-md-1">任务状态</label>
							<div class="col-md-3">
								<select class="form-control" name="query.signPayment" value="${query.signPayment}" id="signPayment-status">
									<option value=""></option>
									<option value="true">已生成</option>
									<option value="false">未生成</option>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1">付款时间</label>
							<div class="col-md-3">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.fromPaidDate" placeholder="" readonly="readonly" value="${query.fromPaidDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.toPaidDate" placeholder="" readonly="readonly" value="${query.toPaidDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>

							<label class="control-label col-md-1">是否查询历史数据</label>
							<div class="col-md-3">
								<select class="form-control" name="query.queryHistory" value="${query.queryHistory}" id="queryHistory">
									<option value="">否</option>
									<option value="2019">2019</option>
									<option value="2020">2020</option>
									<option value="2021">2021</option>
								</select>
							</div>
							
							<label class="control-label col-md-1">发货类型</label>
                            <div class="col-md-3">
                                <input class="form-control" type="text" name="query.shipStatusToStr" value="${query.shipStatusToStr}" id="shipStatus-id">
                            </div>
						</div>
						<div class="form-group">
                            <label class="control-label col-md-1">新YST</label>
                            <div class="col-md-3">
                                <input class="form-control" type="text" name="query.paymentStatus" placeholder="请输入发货单号" value="${query.paymentStatus}">
                            </div>
                            <label class="control-label col-md-1">
                                <select name="query.oldNumberType" class="form-control" style="width:80px;float:right;border:none;margin-top:-9px;margin-right:-5px;">
                                    <option value="1" ${(query.oldNumberType == 1)?string('selected', '')}>旧追踪单号</option>
                                    <option value="2" ${(query.oldNumberType == 2)?string('selected', '')}>旧货代单号</option>
                                </select>
                            </label>
                            <div class="col-md-3">
                                <input class="form-control" type="text" name="query.oldNumberText"  value="${query.oldNumberText}">
                            </div>
							<label class="control-label col-md-1">标签</label>
							<div class="col-md-3">
								<select class="form-control" name="query.buyerCheckout" value="${query.buyerCheckout}" id="apv-flag-id">
									<option value="">全部</option>
									<option value="EUR" ${(query.buyerCheckout == 'EUR')?string('selected', '')}>欧代</option>
									<option value="UK" ${(query.buyerCheckout == 'UK')?string('selected', '')}>英代</option>
                                    <option value="RFP" ${(query.buyerCheckout == 'RFP')?string('selected', '')}>加强包装</option>
									<option value="IC" ${(query.buyerCheckout == 'IC')?string('selected', '')}>拦截订单</option>
									<option value="QC_IC" ${(query.buyerCheckout == 'QC_IC')?string('selected', '')}>质控拦截</option>
									<option value="OVERSIZE" ${(query.buyerCheckout == 'OVERSIZE')?string('selected', '')}>超体积拦截</option>
									<option value="OVERWEIGHT" ${(query.buyerCheckout == 'OVERWEIGHT')?string('selected', '')}>超重拦截</option>
									<option value="GPSR" ${(query.buyerCheckout == 'GPSR')?string('selected', '')}>GPSR</option>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1">平台单号</label>
							<div class="col-md-3">
								<input class="form-control" type="text" name="query.splitPlatformOrderId" placeholder="请输入平台单号" value="${query.splitPlatformOrderId}">
							</div>
							<label class="control-label col-md-1">取消时间</label>
							<div class="col-md-3">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.fromCancelDate" placeholder="" readonly="readonly" value="${query.fromCancelDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.toCancelDate" placeholder="" readonly="readonly" value="${query.toCancelDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>
							<label class="control-label col-md-1">移动待分配时间</label>
							<div class="col-md-3">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.fromMoveStatusDate" placeholder="" readonly="readonly" value="${query.fromMoveStatusDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.toMoveStatusDate" placeholder="" readonly="readonly" value="${query.toMoveStatusDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>
						</div>

						<div class="form-group">
							<label class="control-label col-md-1">特殊订单</label>
							<div class="col-md-3">
								<select class="form-control" name="query.isNanjingShop" value="${query.isNanjingShop}" >
									<option value="false"></option>
									<option value="true" <#if query.isNanjingShop == true>selected</#if>>南宁仓店铺</option>
								</select>
							</div>
						</div>

					</div>
				</div>
				<div>
					<div class="pull-left" style="margin-bottom: 10px;">
						<@header method="auth" authCode="CHECKOUT_SEARCH_ALLOCATION_MOVE_SOLO_ORDER">
						<button type="button" class="btn btn-default" onclick="moveGoodsPrints()">
							<i class="icon-goodsPrints"></i> 已分配移动单件合单
						</button>
						</@header>
						<@header method="auth" authCode="CHECKOUT_SEARCH_ALLOCATION_MOVE_MULTIPLY_ORDER_MERGE">
						<button type="button" class="btn btn-default" onclick="moveBasketPrints()">
							<i class="icon-basketPrints"></i> 已分配移动多件合单
						</button>
						</@header>
						<@header method="auth" authCode="CHECKOUT_SEARCH_ALLOCATION_MOVE_MULTIPLY_ORDER_MERGE">
						<button type="button" class="btn btn-default" onclick="moveSingleMoreProduct()">
							<i class="icon-singleMoreProduct"></i> 已分配移动多品合单
						</button>
						</@header>
						<@header method="auth" authCode="CHECKOUT_SEARCH_MOVE_PICK_SHORT_TO_ALLOCATION">
						<div class="btn-group">
							<#if util('localWarehouseId') == 2>
								<button type="button" class="btn btn-default" onclick="moveOutOfStock('2')">移动拣货缺货到已分配 </button>
							<#else>
								<button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
									移动拣货缺货到已分配 <i class="icon-angle-down"></i>
								</button>
								<ul class="dropdown-menu">
									<li><a href="#" onclick="moveOutOfStock('1')">汉海达仓</a></li>
									<li><a href="#" onclick="moveOutOfStock('n')">跨仓</a></li>
								</ul>
							</#if>
						</div>
						</@header>
						<div class="btn-group">
							<button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
								批量匹配库存 <i class="icon-angle-down"></i>
							</button>
							<ul class="dropdown-menu">
                                <@header method="auth" authCode="CHECKOUT_SEARCH_BATCH_MATCH_INVENTOTY_LOCAL">
                                <li><a href="#" onclick="allotStock('local')">本仓</a></li>
                                </@header>
<!--                                <li><a href="#" onclick="allotStock('optimal')">优选仓</a></li>-->
<!--                                <li><a href="#" onclick="allotStock('span')">跨仓</a></li>-->
                                <@header method="auth" authCode="CHECKOUT_SEARCH_BATCH_MATCH_INVENTOTY_VIRTUAL">
								<li><a href="#" onclick="allotStock('virtual')">匹配虚拟海外仓</a></li>
                                </@header>
								<@header method="auth" authCode="CHECKOUT_SEARCH_BATCH_MATCH_INVENTROY_SCREEN">
								<li><a href="#" onclick="allotStock('filter')">当前筛选结果</a></li>
								</@header>
								<#--<li><a href="#" onclick="allotStock('stockOut')">匹配缺货直发</a></li>-->
							</ul>
						</div>
						<@header method="auth" authCode="CHECKOUT_SEARCH_MOVE_TO_PENDING">
						<div class="btn-group">
							<button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" onclick="moveStatus(1, '待分配')">
								移动到待分配 <!-- <i class="icon-angle-down"></i> -->
							</button>
							<!-- <ul class="dropdown-menu">
								<li><a href="#" onclick="moveStatus(1, '待分配')">待分配</a></li>
								<li><a href="#" onclick="moveStatus(6, '已分配')">已分配</a></li>
							</ul> -->
						</div>
						</@header>
						<@header method="auth" authCode="CHECKOUT_SEARCH_MATCH_SECURITY_CHECK_RETREVE">
						<div class="btn-group">
							<button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" onclick="checkReturn()">
								匹配安检退件 <!-- <i class="icon-angle-down"></i> -->
							</button>
						</div>
						</@header>
					</div>
					<div class="col-md-offset-12" style="text-align: right">
						<button  type="button" class="btn red" onclick="openPrintDialog()">打印机配置</button>
						<button type="button" id="expandFn" class="btn blue">收起</button>
						<button type="button" class="btn default" onclick="formReset(this)">
							<i class="icon-refresh"></i> 重置
						</button>
						<button type="submit" class="btn blue" onclick="return submitTest()">
							<i class="icon-search"></i> 查询
						</button>
						<@header method="auth" authCode="CHECKOUT_SEARCH_DOWNLOAD">
							<button type="button" class="btn btn-default" onclick="downloadOrder()">
								<i class="icon-download"></i> 导出
							</button>
						</@header>
					</div>
				</div>
			</form>
		</div>
		<br/>
	</div>

	<div class="row">

		<div id="fixedDiv" class="col-md-12">
			<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
				<colgroup>
					<col width="6%" />
					<col width="10%" />
					<col width="3%" />
					<col width="5%" />
					<col width="7%" />
					<col width="10%" />
					<col width="10%" />
					<col width="8%" />
					<col width="3%" />
					<col width="5%" />
					<col width="3%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="10%" />
					<col width="5%" />
					<col width="5%" />
				</colgroup>
				<thead>
				<tr>
					<th>
						<label class="checkbox-inline">编号</label>
					</th>
					<th>单号</th>
					<th>频次</th>
					<th>状态</th>
					<th>平台单号</th>
					<th>DD号</th>
					<th>SKU/时间</th>
					<th>库位</th>
					<th>数量</th>
					<th>称重重量(g)</th>
					<th>发货类型</th>
					<th>标签</th>
					<th>订单来源</th>
					<th>平台</th>
					<th>物流方式</th>
					<th>推单次数</th>
					<th>日志</th>
				</tr>
				</thead>
			</table>
		</div>
		<div class="col-md-12" id="task-list-warp">
			<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
				<colgroup>
					<col width="6%" />
					<col width="10%" />
					<col width="3%" />
					<col width="5%" />
					<col width="7%" />
					<col width="10%" />
					<col width="10%" />
					<col width="8%" />
					<col width="3%" />
					<col width="5%" />
					<col width="3%" />
					<col width="5%" />
                    <col width="5%" />
                    <col width="5%" />
					<col width="10%" />
					<col width="5%" />
					<col width="5%" />
				</colgroup>
				<thead>
					<tr>
						<th>
							<label class="checkbox-inline"><input type="checkbox"  id="check-all" onclick="checkAll(this);">编号</label>
						</th>
						<th>单号</th>
						<th>频次</th>
						<th>状态</th>
						<th>平台单号</th>
						<th>DD号</th>
						<th>SKU/时间</th>
						<th>库位</th>
						<th>数量</th>
						<th>称重重量(g)</th>
						<th>发货类型</th>
						<th>标签</th>
						<th>订单来源</th>
						<th>平台</th>
						<th>物流方式</th>
						<th>推单次数</th>
						<th>日志</th>
					</tr>
				</thead>
				<tbody>
					<#list domain.whApvs as apv>
						<tr id="tr-apv-${apv.id}" class="${(apv.whApvLock.status== 0)?string('bg','danger')} tr-apv-${apv.id}">
							<td>
								<label class="checkbox-inline">
									<#if apv.whApvLock.status == 0 && query.queryHistory??>
										<input type="checkbox" name="apvIds" value="${apv.id}" /> ${apv.id}
									<#elseif (query.queryHistory)!>
										${apv.id} <br/> (历史订单不能操作)
									<#else>
										${apv.id} <br/> (锁定订单不能操作)
									</#if> 
								</label>
							</td>
							<td>
								${apv.apvNo }<br/>
                                <#if apv.paymentStatus??><span style="color: red">新: ${apv.paymentStatus}<br/></span></#if>
								<#if apv.taskId != '0'>拣货任务ID：${apv.taskId}</#if>
								<#if (query.queryHistory)!><span style="color: red">历史数据不能操作</span></#if>
							</td>
							<td>
								${apv.originalOrderId}
							</td>
							<td>
								<span class='label label-sm label-info'>
									${util('enumName', 'com.estone.apv.common.ApvStatus', apv.status)}
								</span>
							</td>
							<td>${apv.platformOrderId }</td>
							<td>
								${apv.salesRecordNumber}
							</td>
							<td>
								付款：${apv.paidDate}<br/>
								推单：${apv.creationDate}
							</td>
							<td><#if apv.number gt 0> 篮筐号：${apv.number} </#if></td>
							<td> </td>
							<td>${apv.actualWeight}</td>
							<td>
								<span class='label label-sm label-info'>
									${util('enumName', 'com.estone.apv.common.ApvOrderType', apv.shipStatus)}
								</span>
							</td>
							<td><span style="color: #008000">
											${util('enumName', 'com.estone.apv.enums.ApvTaxTypeEnum', apv.buyerCheckout)}
											</span>
							</td>
							<td>
								<#if (apv.shipService)!>
									${apv.shipService}
								<#else>
									PMS
								</#if>
							</td>
							<td>
								${saleMap[apv.platform?string]}
							</td>
							<td>
								<dl>
									<dd>${apv.logisticsCompany }</dd>
									<#if (apv.trackingNumber)! && (apv.trackingNumber) != ''>
										<dd>${apv.trackingNumber }</dd>
									<#else>
										<dd>${apv.serviceProviderNo }</dd>
									</#if>
								</dl>
							</td>
							<td>${apv.apvPushNumber}</td>
							<td>
								<#if apv.shipStatus == 22 || apv.shipStatus == 23>
									<button type="button" class="btn btn-xs btn-info" onclick="printLocalJitTag('${apv.id}','${apv.apvNo}')">打印SKU标签</button>
									<button type="button" class="btn btn-xs btn-info" style="margin-top: 2px;margin-bottom: 2px;" onclick="printLocalPdf('${apv.apvNo}')">打印面单</button>
								</#if>
								<button type="button" class="btn btn-xs btn-info" onclick="viewLog(${apv.id}, 'whapv')">日志</button>
							</td>
						</tr>
						<#list apv.whApvItems as apvItem>
							<tr class="tr-order-${apv.id}" id="tr-order-item-${apvItem.id }">
								<td></td>
								<td></td>
								<td colspan="4" <#if '4' == apvItem.itemDesc && '4' == apv.status>style="color: red;"</#if>>${apvItem.whSku.name }</td>
								<td>
									${apvItem.sku }（汉海达仓）
                                    <span style="color: red">
										<#if (apv.shipStatus == 8 || apv.shipStatus == 11 || apv.shipStatus == 14) && apvItem.whSku.shearSign?? && apvItem.whSku.shearSign == 1>
											<br>剪标
										</#if>
										<#if apvItem.whSku.noStockUp?? && apvItem.whSku.noStockUp == 1>
											，不备货
										</#if>
									</span>
								</td>
								<td>${apvItem.allotLocationsBr }</td>
								<td>${apvItem.saleQuantity }</td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
								<td></td>
                                <td></td>
                                <td></td>
							</tr>
						</#list>
					</#list>
				</tbody>
			</table>
		</div>
		<#include "/common/pack_bgcolor_selector.html">
		<div id="print_tag" style="display: none">
		</div>
	</div>
	<div id="fixed-bottom">
		<div id="pager"></div>
	</div>
</div>

<#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH }js/LodopFuncs.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/pages/pms.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/packing.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/print.js?v=${.now?datetime}"></script>

<script type="text/javascript">
	window.onload = function () {
		getPrinterList();
	};

$('#expandFn').on('click', function (event) {
	if ($(this).hasClass('expand')) {
	    $(this).text('收起');
        $('#expand-area').slideDown(200);
	} else {
        $(this).text('展开');
        $('#expand-area').slideUp(200);
    }
    $(this).toggleClass('expand');
	event.preventDefault();
});
jQuery(document).ready(function() {
	initPrinter();
	var domainForm = $('#domain');
		domainForm.validate({
	   	});
	});

    // 分页
    var total = "${domain.page.totalCount}";
	var pageNo = "${domain.page.pageNo}";
	var pageSize = "${domain.page.pageSize}";
	$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});
	
	$("input[name='query.apvType']").select2({
		data : [{id : "SS", text : "单品单件"}, {id : "SM", text : "单品多件"}, {id : "MM", text : "多品多件"}],
		placeholder : "发货单类型",
		allowClear : true
	});
	
	var warehouseList = ${domain.warehouseList};
	$("input[name='query.warehouseStr']").select2({
	    data : warehouseList,
	    placeholder : "仓库",
	    allowClear : true
	});

	var apvOrderTypes = ${domain.apvOrderTypes};
	$("input[name='query.shipStatusToStr']").select2({
		data : apvOrderTypes,
		placeholder : "发货类型",
        multiple: true,
		allowClear : true
	});

	
	// $("#shipStatus-id").val("${domain.query.shipStatus}");
	$("#shipService-id").val("${domain.query.shipService}");
	$("#signPayment-status").val("${domain.query.signPayment}");
	
	$("#lock-status").val("${domain.query.whApvLock.status}");
	
	$("#warehouseStr-id").val("${domain.query.warehouseStr}");
	$("#waybillSize-id").val("${domain.query.waybillSize}");
	$("#queryHistory").val("${domain.query.queryHistory}");
	
	
	// 正则表达式校验
	function submitTest(){
		var apvIds = $("input[name='query.apvIdsString']").val();
		if (!apvIds) { // 为空不判断
			return true;
		}
		
		var re = new RegExp(/^[\d+,*\d+]+$/);
		if (re.test(apvIds)) {
			return true;
		} else {
			layer.alert("请输入正确的编号");
			return false;
		}
	}
    
    $("#apvNo-id").val("${domain.query.apvNo}");
    
  	//全选
	function checkAll(obj) {
		$("#task-list").find("input[name='apvIds']").prop("checked", $(obj).is(':checked'));
	}

    var heights = $("body").height();
    if(heights>910){
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }
    
 	// 获取选中的订单
	function getCheckedApvs() {
		var checkedApvs = $("#task-list").find("input[name='apvIds']:checked");
		return checkedApvs;
	}
 	
    //移动单件合单
    function moveGoodsPrints(){
    	
    	var checkedApvs = getCheckedApvs();
		
		var diglog = dialog({
			title: '移动单件合单',
			width: 400,
			height:100,
			url: CONTEXT_PATH + "commons/checkModel",
			okValue: '确定',
		    ok: function () {
		    	debugger;
		    	var exportWindow = $(this.iframeNode.contentWindow.document.body);
		    	
		    	var submitForm = exportWindow.find("#submit-form");
		    	
		    	var exportType = submitForm.find("input[name='exportType']:checked").val();
		    	
		    	var submitFormParam = submitForm.serialize();
		    	
		    	submitFormParam =  submitFormParam + "&" + $("#domain").serialize();
		    	
		    	// 导出当前选择
		    	if(exportType == 3) {
					if(checkedApvs.length == 0) {
						layer.alert("请选择要操作的数据!");
						return false;
					} else if (checkedApvs.length > 300) {
						layer.alert("选择数量不能超过300!");
						return false;
					}
					
					submitFormParam = submitFormParam + "&" +checkedApvs.serialize();
			    	
		    	}
		    	
		    	$.get(CONTEXT_PATH + "apvs/moveGoodsPrints", submitFormParam, function(data){
					//customizeLayer(data.message);
					layer.confirm(data.message,{
						icon: 1,
						btn: ['确定']
					},function () {
						window.location.reload();
					})
				});
		    	
		    	setTimeout(function () {
		    		diglog.close().remove();
		    	}, 100);
		    	
		    	return true;
		    },
		    cancelValue: '取消',
		    cancel: function () {}
		});
		diglog.show();
		setTimeout(function () {
			$(diglog.iframeNode.contentWindow.document.body).find("#allName").html("当前条件单件已分配订单");
    	}, 500);
    }
    
    //移动多件合单
	function moveBasketPrints(){
    	
		var checkedApvs = getCheckedApvs();
		
		var diglog = dialog({
			title: '移动多件合单',
			width: 400,
			height:100,
			url: CONTEXT_PATH + "commons/checkModel",
			okValue: '确定',
		    ok: function () {
		    	
		    	var exportWindow = $(this.iframeNode.contentWindow.document.body);
		    	
		    	var submitForm = exportWindow.find("#submit-form");
		    	
		    	var exportType = submitForm.find("input[name='exportType']:checked").val();
		    	
		    	var submitFormParam = submitForm.serialize();
		    	
		    	submitFormParam =  submitFormParam + "&" + $("#domain").serialize();
		    	
		    	// 导出当前选择
		    	if(exportType == 3) {
		    		if(checkedApvs.length == 0) {
						layer.alert("请选择要操作的数据!");
						return false;
					} else if (checkedApvs.length > 300) {
						layer.alert("选择数量不能超过300!");
						return false;
					}
					
					submitFormParam = submitFormParam + "&" +checkedApvs.serialize();
		    	}
		    	
		    	$.get(CONTEXT_PATH + "apvs/moveBasketPrints", submitFormParam, function(data){
					//customizeLayer(data.message);
					layer.confirm(data.message,{
						icon: 1,
						btn: ['确定']
					},function () {
						window.location.reload();
					})
				});
		    	
		    	setTimeout(function () {
		    		diglog.close().remove();
		    	}, 100);
		    	
		    	return true;
		    },
		    cancelValue: '取消',
		    cancel: function () {}
		});
		diglog.show();
		setTimeout(function () {
			$(diglog.iframeNode.contentWindow.document.body).find("#allName").html("当前条件多件已分配订单");
    	}, 500);
    }
    
	//移动多品合单
	function moveSingleMoreProduct(){
		
		var checkedApvs = getCheckedApvs();
		
		var diglog = dialog({
			title: '移动多品合单',
			width: 400,
			height:100,
			url: CONTEXT_PATH + "commons/checkModel",
			okValue: '确定',
		    ok: function () {
		    	
		    	var exportWindow = $(this.iframeNode.contentWindow.document.body);
		    	
		    	var submitForm = exportWindow.find("#submit-form");
		    	
		    	var exportType = submitForm.find("input[name='exportType']:checked").val();
		    	
		    	var submitFormParam = submitForm.serialize();
		    	
		    	submitFormParam =  submitFormParam + "&" + $("#domain").serialize();
		    	
		    	// 导出当前选择
		    	if(exportType == 3) {
		    		if(checkedApvs.length == 0) {
						layer.alert("请选择要操作的数据!");
						return false;
					} else if (checkedApvs.length > 300) {
						layer.alert("选择数量不能超过300!");
						return false;
					}
					
					submitFormParam = submitFormParam + "&" +checkedApvs.serialize();
		    	}
		    	
		    	$.get(CONTEXT_PATH + "apvs/moveSingleMoreProduct", submitFormParam, function(data){
					//customizeLayer(data.message);
					layer.confirm(data.message,{
						icon: 1,
						btn: ['确定']
					},function () {
						window.location.reload();
					})
				});
		    	
		    	setTimeout(function () {
		    		diglog.close().remove();
		    	}, 100);
		    	
		    	return true;
		    },
		    cancelValue: '取消',
		    cancel: function () {}
		});
		diglog.show();
		setTimeout(function () {
			$(diglog.iframeNode.contentWindow.document.body).find("#allName").html("当前条件多品已分配订单");
    	}, 500);
	}
	
	// 移动状态
	function moveStatus(status, name){
		var checkedApvs = getCheckedApvs();
		
		if(checkedApvs.length == 0) {
			layer.alert("请选择要操作的数据!");
			return false;
		} else if (checkedApvs.length > 1000) {
			layer.alert("选择数量不能超过1000!");
			return false;
		}

        var remark = prompt("确定移动状态到" + name+"?原因(可不填写):");
        if (remark != null){
            remark = remark.replace(/\s*/g,'');
            var params = checkedApvs.serialize() + "&status=" + status;
            if (remark != ''){
                params += "&remark="+remark;
            }
			$.post(CONTEXT_PATH + "apvs/moveStatus", params, function(data){
				//customizeLayer(data.message);
				layer.confirm(data.message,{
					icon: 1,
					btn: ['确定']
				},function () {
					window.location.reload();
				})
			});
		}
	}
	
	// 移动拣货缺货到待分配
	function moveOutOfStock(warehouse){
		if (confirm("确定移动拣货缺货状态到已分配？")) {
			$.get(CONTEXT_PATH + "apvs/moveOutOfStock?warehouseStr=" + warehouse, function(data){
				//customizeLayer(data.message);
				layer.confirm(data.message,{
					icon: 1,
					btn: ['确定']
				},function () {
					window.location.reload();
				})
			});
		}
	}
	
	// 移动拣货缺货到待分配
	function allotStock(warehouse){
		if (confirm("确定匹配库存？")) {
			var params = "type=" + warehouse;
			if(warehouse == "filter"){
			    // 加入筛选结果
                params = params + "&" + $("#domain").serialize();
            }
			$.get(CONTEXT_PATH + "apvs/allotStock?" + params, function(data){
				//customizeLayer(data.message);
				layer.confirm(data.message,{
					icon: 1,
					btn: ['确定']
				},function () {
					window.location.reload();
				})
			});
		}
	}
	
	
	//匹配安检退件
	function checkReturn(){
		if (confirm("确定匹配安检退件？")) {
			$.get(CONTEXT_PATH + "apvs/checkReturn", function(data){
				//customizeLayer(data.message);
				layer.confirm(data.message,{
					icon: 1,
					btn: ['确定']
				},function () {
					window.location.reload();
				})
			});
		}
	}
	
	//导出
//导出
function downloadOrder(){

	var uuid = getFileQueueUUID();

	var checkedApvs = getCheckedApvs();

	var diglog = dialog({
		title: '导出',
		width: 350,
		height:100,
		url: CONTEXT_PATH + "apvs/downloadmode?type=1",
		okValue: '确定',
		ok: function () {

			var exportWindow = $(this.iframeNode.contentWindow.document.body);

			var submitForm = exportWindow.find("#submit-form");

			var exportType = submitForm.find("input[name='exportType']:checked").val();

			var headers =  submitForm.find("input[name='selectHeaders']:checked");

			var submitFormParam = $('#domain').serialize();

			// 导出当前选择
			if(exportType == 3) {
				if(checkedApvs.length == 0) {
					layer.alert("请选择要操作的数据");
					return false;
				}

				submitFormParam = submitFormParam + "&" +checkedApvs.serialize();
			}
			submitFormParam = submitFormParam + "&exportType=" + exportType + "&uuid=" + uuid;

			//还原分页
			$("#page-no").val("${domain.page.pageNo}");

			// downloadByPostForm(submitFormParam, CONTEXT_PATH + "apvs/download");

			$.post(CONTEXT_PATH + "apvs/download", submitFormParam, function(data){
				if (data.status == 200) {
					if (data.message==null || data.message==''){
						layer.alert('成功',function (index) {
							layer.close(index);
							diglog.close().remove();
							location.reload();
						});
					}else{
						customizeLayer(data.message);
					}
				} else {
					customizeLayer(data.message);
				}
			});
			$("#page-no").val("1");
			return true;
		},
		cancelValue: '取消',
		cancel: function () {}
	});
	diglog.show();
}
	
	var statusSelectJson = ${domain.statusSelectJson};
	$("input[name='query.statusToStr']").select2({
		data : statusSelectJson,
		placeholder : "状态",
		multiple: true,
		allowClear : true
	});

function printLocalJitTag(apvId,apvNo){
	var diglog = dialog({
		title: '打印标签',
		width: 800,
		url: CONTEXT_PATH + "apvs/toLocalJitPrint?apvId=" + apvId,
		okValue: '确定',
		ok: function () {
			var printWindow = $(this.iframeNode.contentWindow.document.body);
			if (!jitPrinter75Tag || !jitPrinterTag) {
				layer.alert("请先配置打印机", "error");
				result = false;
				return false;
			}
			var body = printWindow.find("#printSku-body");

			var items = body.find(".print-content");
			if(items == null || items.length == 0){
				layer.alert("没有要打印的SKU", "error");
				return false;
			}

			var result = false;
			items.each(function () {
				var skuBarcode = $(this).find('input[name="skuBarcode"]').val();
				var productSku = $(this).find('input[name="productSku"]').val();
				var quantity = $(this).find('input[name="quantity"]').val();
				if(quantity == ""){
					layer.alert("打印数量不能为空", "error");
					result = false;
					return false;
				}
				if (quantity > 0){
					setTimeout(function () {
						//打印JIT货品标签
						printJitTag(apvNo, productSku, quantity, skuBarcode, true);
					}, 300);
				}

			});
		},
		cancelValue: '取消',
		cancel: function () {}
	});
	diglog.show();
}


function printLocalPdf(apvNo) {
	var r= $.ajax({
		url: CONTEXT_PATH + "fba/packs/localPrintXiangmai?apvNo=" + apvNo,
		timeout : 100000,
		async: false,
		success : function(response){
			if (response.status == '200') {
				var jitPdfUrl = response.body.jitPdfUrl;
				if (!jitPdfUrl) {
					jitPdfUrl = window.location.origin + CONTEXT_PATH + response.message;
				}
				var jitBoxNumber = response.location;
				printCopies(jitPdfUrl, null, jitPrinter, 1, jitBoxNumber);
			} else {
				layer.alert(response.message, {closeBtn: 0}, function (index) {
					layer.close(index);
				});
			}
		},
		error:function(){
			layer.alert('扫描失败，请重新扫描');
		}
	});

}

</script>
</body>
</html>