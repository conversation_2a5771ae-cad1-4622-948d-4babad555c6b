<!DOCTYPE html>
<!--[if IE 8]> <html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en" class="no-js">
<!--<![endif]-->
<!-- BEGIN HEAD -->
<head>
<meta charset="utf-8" />
<title>ERP</title>

<style type="text/css">
	.radio-inline {
		display: inline-block;
		padding-left: 20px;
		margin-bottom: 0;
		font-weight: normal;
		vertical-align: middle;
		cursor: pointer;
	}
</style>

</head>
<!-- END HEAD -->
<!-- BEGIN BODY -->
<body>

	<!-- BEGIN CONTAINER -->
	<div class="container-fluid" style="min-width: 900px;">

		<div class="row">
			<div class="col-md-12">
			
				<form class="form-horizontal" id="submit-form" name="submitForm" action="${CONTEXT_PATH}apvs/download" method="post">
				
					<div class="form-body">
						
						<div class="form-group">
							<div class="">
								<label class="radio-inline">
									<input type="radio" name="exportType" value="1">
									所有
								</label>
								<label class="radio-inline">
									<input type="radio" checked="checked" name="exportType" value="2">
									当前页
								</label>
								<label class="radio-inline">
									<input type="radio" name="exportType" value="3">
									当前选择
								</label>
							</div>
							<#if (domain.downloadHeaders)??>
								<div class="">
									<h4 class="modal-title" id="selectDownloadHeadersModalLabel">选择要导出的表头字段
                                    <label class="radio-inline" style="width:150px;"><input type="checkbox" checked="checked" onclick="tableHeaderAll(this)"> 全选/反选</label></h4>
									<div id="table-header" style="width:750px;">
										<#list domain.downloadHeaders as downloadHeader>
											<label class="radio-inline" style="width:150px;">
												<input type="checkbox" checked="checked" name="selectHeaders" value="${downloadHeader}">
												${downloadHeader}
											</label>
										</#list>
									</div>
								</div>
							</#if>
						</div>
					</div>
				</form>
			</div>
			<!-- end col -->
		</div>
		<!-- end row -->
	</div>
	<!-- END CONTAINER -->

    <script type="text/javascript" src="${CONTEXT_PATH}js/dist/jquery.11.3.min.js"></script>
	<script type="text/javascript">
        //全选
        function tableHeaderAll(obj) {
            $("#table-header").find("input[name='selectHeaders']").prop("checked", $(obj).is(':checked'));
        }
	</script>

	<!-- END JAVASCRIPTS -->
</body>
<!-- END BODY -->
</html>