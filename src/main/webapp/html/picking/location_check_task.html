<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
  <title>易世通达仓库管理系统</title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <#include "/common/include.html">
  <style type="text/css">
    .modal-body input {
      width: 240px;
    }
    .top_bar{
      position:fixed;top:0px;
    }
    #task-list td {
      vertical-align:middle;
    }
  </style>
</head>
<body>
<@header method="header" active="11030000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
  <div class="row">
    <div class="col-md-12" style="padding: 0">
      <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
        <li><a href="#">库位管理</a></li>
        <li class="active">库位校验任务</li>
      </ul>
    </div>
  </div>

  <!-- 内容 -->
  <div class="container-fluid" style="background-color: white;border: none">
    <div class="row">
      <div class="col-md-12">
        <#assign query = domain.query>
        <form action="${CONTEXT_PATH}location/check/search"
              class="form-horizontal form-bordered form-row-stripped"
              method="post" modelAttribute="domain" name="locationCheckForm" id ="domain">
          <!-- 分页信息 -->
          <input id="page-no" type="hidden" name="page.pageNo" value="1">
          <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
          <div class="form-body">
            <div class="form-group">
              <label class="control-label col-md-1">任务编号</label>
              <div class="col-md-2">
                <input class="form-control" type="text" name="query.taskNo" placeholder="多个使用,分隔查询" value="${query.taskNo}">
              </div>
              <label class="control-label col-md-1">库区</label>
              <div class="col-md-2">
                <input class="form-control" name="query.region" type="text" value="${query.region}" onchange="getLocationRegion(this)">
              </div>
              <label class="control-label col-md-1">通道</label>
              <div class="col-md-2">
                <input class="form-control" name="query.aisle" type="text" value="${query.aisle}">
              </div>
              <label class="control-label col-md-1">库位</label>
              <div class="col-md-2">
                <input class="form-control" type="text" name="query.locationNumber" placeholder="多个使用,分隔查询"
                       value="${query.locationNumber }">
              </div>
              <label class="control-label col-md-1">任务状态</label>
              <div class="col-md-2">
                <input class="form-control" name="query.taskStatus" type="text" value="${query.taskStatus}">
              </div>
              <label class="control-label col-md-1">领取人</label>
              <div class="col-md-2">
                <input class="form-control" name="query.receiveBy" type="text" value="${query.receiveBy}">
              </div>
              <label class="control-label col-md-1">创建人</label>
              <div class="col-md-2">
                <input class="form-control" name="query.createdBy" type="text" value="${query.createdBy}">
              </div>

              <label class="control-label col-md-1">创建时间</label>
              <div class="col-md-2">
                <div class="input-group">
                  <input class="form-control Wdate" type="text" name="query.fromCreatedDate" placeholder=""
                         readonly="readonly" value="${query.fromCreatedDate }"
                         onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                  <span class="input-group-addon">到</span>
                  <input class="form-control Wdate" type="text" name="query.toCreatedDate" placeholder=""
                         readonly="readonly" value="${query.toCreatedDate }"
                         onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                </div>
              </div>

              <label class="control-label col-md-1">领取时间</label>
              <div class="col-md-2">
                <div class="input-group">
                  <input class="form-control Wdate" type="text" name="query.fromReceiveDate" placeholder=""
                         readonly="readonly" value="${query.fromReceiveDate }"
                         onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                  <span class="input-group-addon">到</span>
                  <input class="form-control Wdate" type="text" name="query.toReceiveDate" placeholder=""
                         readonly="readonly" value="${query.toReceiveDate }"
                         onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                </div>
              </div>
              <label class="control-label col-md-1">完成时间</label>
              <div class="col-md-2">
                <div class="input-group">
                  <input class="form-control Wdate" type="text" name="query.fromFinishDate" placeholder=""
                         readonly="readonly" value="${query.fromFinishDate }"
                         onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                  <span class="input-group-addon">到</span>
                  <input class="form-control Wdate" type="text" name="query.toFinishDate" placeholder=""
                         readonly="readonly" value="${query.toFinishDate }"
                         onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                </div>
              </div>
            </div>
          </div>
          <div>
            <div class="pull-left" style="margin-bottom: 10px;">
              <@header method="auth" authCode="CREATE_LOCATION_CHECK_TASK">
                <a class="btn btn-default" href="${CONTEXT_PATH}location/check/create">
                    <i class="icon-plus"></i>创建任务
                </a>
              </@header>
              <#--<@header method="auth" authCode="PRINT_LOCATION_CHECK_TASK_NUMBER">
              </@header>
                <button type="button" class="btn  btn-default" onclick="printTaskQRCode()">
                  <i class="icon-print"></i> 打印任务号
                </button>-->
              <@header method="auth" authCode="DISCARD_LOCATION_CHECK_TASK">
                <button type="button" class="btn  btn-default" onclick="discardLocationTask()">
                  <i class="icon-edit"></i> 废除任务
                </button>
              </@header>
                <@header method="auth" authCode="LOCATION_CHECK_ALLOCATION_ORDER">
                    <button type="button" class="btn btn-default" onclick="batchAllocation()">
                        <i class="icon-allocation"></i>分配任务
                    </button>
                </@header>
            </div>

            <div class="col-md-offset-12" style="text-align: right">
              <button type="button" class="btn btn-default" onclick="formReset(this)">
                <i class="icon-refresh"></i> 重置
              </button>
              <button type="submit" class="btn blue">
                <i class="icon-search"></i> 查询
              </button>
              <@header method="auth" authCode="DOWNLOAD_LOCATION_CHECK_TASK">
                <button type="button" class="btn btn-default" onclick="downloadTask()">
                  <i class="icon-download"></i> 导出
                </button>
              </@header>

            </div>
          </div>
        </form>
      </div>
    </div>

    <div class="row">
      <div id="fixedDiv" class="col-md-12">
        <table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
          <colgroup>
            <col width="10%">
            <col width="10%">
            <col width="10%">
            <col width="10%">
            <col width="10%">
            <col width="10%">
            <col width="10%">
            <col width="10%">
            <col width="10%">
            <col width="10%">
          </colgroup>
          <thead>
          <tr>
            <th>任务ID</th>
            <th>任务号</th>
            <th>库位数</th>
            <th>SKU数</th>
            <th>无差异SKU数</th>
            <th>创建人/创建时间</th>
            <th>领取人/领取时间</th>
            <th>完成人/完成时间</th>
            <th>单据状态</th>
            <th>操作</th>
          </tr>
          </thead>
        </table>
      </div>
      <div class="col-md-12" id="task-list-warp">
        <table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
          <colgroup>
            <col width="10%">
            <col width="10%">
            <col width="10%">
            <col width="10%">
            <col width="10%">
            <col width="10%">
            <col width="10%">
            <col width="10%">
            <col width="10%">
            <col width="10%">
          </colgroup>
          <thead>
          <tr>
            <th><label class="checkbox-inline"><input type="checkbox" name="checkAll"> 任务ID</label></th>
            <th>任务号</th>
            <th>库位数</th>
            <th>SKU数</th>
            <th>无差异SKU数</th>
            <th>创建人/创建时间</th>
            <th>领取人/领取时间</th>
            <th>完成人/完成时间</th>
            <th>单据状态</th>
            <th>操作</th>
          </tr>
          </thead>
          <tbody>
          <#list domain.locationCheckTasks as task >
            <tr>
              <td><label class="checkbox-inline"><input type="checkbox" name="taskIds" value="${task.id}" /> ${task.id }</label></td>
              <td>${task.taskNo }</td>
              <td>${task.locationQty } </td>
              <td>${task.skuQty } </td>
              <td>${task.noDiffQty } </td>
              <td>${util('name',task.createdBy)} <br/>${task.createdDate } </td>
              <td>${util('name',task.receiveBy)} <br/>${task.receiveDate } </td>
              <td>${util('name',task.finishBy)} <br/>${task.finishDate } </td>

              <td id="${task.id}_taskStatus">
                ${util('enumName',"com.estone.picking.enums.LocationCheckStatus", task.taskStatus)}
              </td>

              <td>
                <button type="button" class="btn btn-info btn-xs" onclick="getLocationTaskDetail(${task.id})">详情</button>
                <button type="button" class="btn btn-xs btn-info" onclick="viewLog(${task.id}, 'locationCheckTask')">日志</button>
              </td>
            </tr>
          </#list>
          </tbody>
        </table>
      </div>
    </div>
    <div id="fixed-bottom">
      <div id="pager"></div>
    </div>
  </div>

  <!-- 打印弹窗 -->
  <div class="modal fade ui-popup" id="print_modal" tabindex="-1" role="dialog" aria-labelledby="organization-add-label" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content"></div>
    </div>
  </div>

  <#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript">
  // 分页
  var total = "${domain.page.totalCount}";
  var pageNo = "${domain.page.pageNo}";
  var pageSize = "${domain.page.pageSize}";
  $("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

  var heights = $("body").height();
  if(heights>910){
    $("#fixed-bottom").addClass("fixed-bottom-height")
  }

  $.getJSON(CONTEXT_PATH + "system/saleusers/userByPermissionCode?permissionCode=310000", function(json){
    if (json) {
      $("input[name='query.receiveBy']").select2({
        data : json,
        placeholder : "领取人",
        allowClear : true
      });
    } else {
      $("input[name='query.receiveBy']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
    }
  });

  $.getJSON(CONTEXT_PATH + "system/saleusers/userByPermissionCode?permissionCode=11030500", function(json){
    if (json) {
      $("input[name='query.createdBy']").select2({
        data : json,
        placeholder : "创建人",
        allowClear : true
      });
    } else {
      $("input[name='query.createdBy']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
    }
  });


  var taskStatusArray = ${domain.locationCheckStatusStr};
  $("input[name='query.taskStatus']").select2({
    data : taskStatusArray,
    placeholder : "任务状态",
    allowClear : true
  });

  var locationRegionArray =  ${domain.locationRegionList};
  $("input[name='query.region']").select2({
    data : locationRegionArray,
    placeholder : "库区",
    allowClear : true
  });

  var locationAisleArray =  ${domain.locationAisleList};
  $("input[name='query.aisle']").select2({
    data : locationAisleArray,
    placeholder : "通道",
    allowClear : true
  });

  function getLocationRegion(obj) {
    var warehouseId = 1;
    var regionId = $(obj).val();
    if (warehouseId && regionId) {
      $.post(CONTEXT_PATH + "warehouse/locations/seleteAisle", {
        warehouseId: warehouseId,
        regionId: regionId
      }, function (data) {
        if (data.status == 200) {
          var dataJson = data.body[regionId];
          $("input[name='query.aisle']").val("").select2({
            data: JSON.parse(dataJson),
            placeholder: "通道",
            multiple: true,
            allowClear: true
          });
        } else {
          customizeLayer(data.message);
        }
      });
    } else {
      $("input[name='query.aisle']").val("").select2({
        data: [],
        placeholder: "通道",
        multiple: true,
        allowClear: true
      });
    }
  }

  /*明细*/
  function getLocationTaskDetail(id) {
    window.open(CONTEXT_PATH + "location/check/item/getDetail/" + id);
  }

  $("#task-list").find("input[name='checkAll']").change(
      function () {
        var checkBox = $("#task-list").find(":checkbox");
        checkBox.prop("checked", $(this).prop("checked"));
        checkBox.each(function(){
          var f = $(this).is(":checked");
          var checkClass = $(this).prop("class");
          $("." + checkClass).each(function(){
            $(this).prop("checked",f);
          })
        })
      }
  );

  // 获取选中的任务
  function getCheckedTaskIds() {
    var checkedTaskIds = $("input[name='taskIds']:checked");
    return checkedTaskIds;
  }

  //打印任务号
  function printTaskQRCode() {
    var checkedDatas = getCheckedTaskIds();

    if(checkedDatas.length == 0) {
      layer.alert("请选择要操作的任务",'error');
      return;
    }else if (checkedDatas.length > 100) {
      layer.alert("批量打印不能超过100个任务!",'error');
      return;
    }
    var trueArray = new Array();
    var falseArray = new Array();
    for (var i = 0; i < checkedDatas.length; i++) {
      var checked = checkedDatas[i];
      var status = $("#"+checked.value+"_taskStatus").text();
      if (status.indexOf('待领取') != -1) {
        trueArray.push(checked.value);
      }else {
        falseArray.push(checked.value);
      }
    }

    if (falseArray.length>0) {
      layer.alert("选择了的:"+falseArray.length+"条不符合的任务将不会打印", 'error');
    }


    var taskIds = "";
    for (var i = 0; i < trueArray.length; i++) {
      taskIds += "taskIds=" + trueArray[i] + "&";
    }

    if (trueArray.length>0) {
      $("#print_modal").removeData("bs.modal");
      $("#print_modal").modal({
        remote: CONTEXT_PATH + "location/check/qRCodePrint?" + taskIds
      });
    }
  }

  //导出
  // 下载
  function downloadTask(){
    var diglog = dialog({
      title: '导出',
      width: 350,
      height:100,
      url: CONTEXT_PATH + "picking/tasks/downloadmode",
      okValue: '确定',
      ok: function () {

        var exportWindow = $(this.iframeNode.contentWindow.document.body);

        var submitForm = exportWindow.find("#submit-form");

        var exportType = submitForm.find("input[name='exportType']:checked").val();

        var params = $('#domain').serialize();

        // 导出当前选择
        if(exportType == 3) {
          var ids = getCheckedTaskIds();
          if(ids.length == 0) {
            layer.alert("请选择要操作的数据!");
            return false;
          } else if (ids.length > 300) {
            layer.alert("选择数量不能超过300!");
            return false;
          }
          params = params + "&" +ids.serialize();
        }
        params = params + "&exportType=" + exportType
        $.post(CONTEXT_PATH + "location/check/download", params, function (data) {
          if (data.status == 200) {
            if (data.message == null || data.message == '') {
              layer.alert('成功', function (index) {
                layer.close(index);
                diglog.close().remove();
                location.reload();
              });
            } else {
              customizeLayer(data.message);
            }
          } else {
            customizeLayer(data.message);
          }
        });
        $("#page-no").val("1");
        return true;
        return true;
      },
      cancelValue: '取消',
      cancel: function () {}
    });
    diglog.show();
  }

  //废除任务
  function discardLocationTask(){
    var checkedDatas = getCheckedTaskIds();

    if(checkedDatas.length == 0) {
      layer.alert("请选择要操作的任务",'error');
      return;
    }

    var taskIds = checkedDatas.serialize();

    var r = confirm("确定是否废除任务？");
    if (r) {
      $.post(CONTEXT_PATH + "location/check/discardedTask?"+taskIds, function(data){
        if (data.status == 200) {
          var message = !data.message ? "操作成功" : data.message;
          layer.confirm(message,{
            icon: 1,
            btn: ['确定']
          },function () {
            window.location.reload();
          })
        } else {
          customizeLayer(data.message);
        }
      });
    }
  }

  // 分配
  function batchAllocation(){
      var checkedDatas = getCheckedTaskIds();
      if(checkedDatas.length == 0) {
          layer.alert("请先勾选需要分配的库位校验任务", "error");
          return;
      } else {
          var ids = "";
          for(var i = 0; i < checkedDatas.length; i++) {
              var check = checkedDatas[i];
              var id = $(check).val();
              var status = $("#"+id+"_taskStatus").text().trim();
              if (status != '待领取') {
                  layer.alert("勾选的任务不为待领取状态，请重新选择", "error");
                  return;
              }
              ids += id;
              if(i != checkedDatas.length - 1) {
                  ids += ",";
              }
          }
          var diglog = dialog({
              title: '分配任务',
              width: 350,
              height: 300,
              url: CONTEXT_PATH + "location/check/taskListAllocation",
              onshow: function() {},
              okValue: '确定',
              ok: function() {
                  var exportWindow = $(this.iframeNode.contentWindow.document.body);
                  var submitForm = exportWindow.find("#submit-form");
                  var allocationUser = submitForm.find("#allocationUser").val();
                  // 导出当前选择
                  if(allocationUser) {
                      $.ajax({
                          url: CONTEXT_PATH + "location/check/batchAllocation",
                          type: "POST",
                          data: {ids: ids,allocationUser:allocationUser},
                          success: function(data){
                              if (data.status == 200) {
                                  alert(data.message);
                                  window.location.reload();
                              } else {
                                  customizeLayer(data.message);
                              }
                          }
                      });
                  }
                  setTimeout(function() {
                      diglog.close().remove();
                      //window.location.reload();

                  }, 100);
                  return false;
              },
              cancelValue: '取消操作',
              cancel: function() {}
          }).showModal();
      }
  }

</script>
</body>
</html>