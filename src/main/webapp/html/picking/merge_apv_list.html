<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal{
			margin-top: 100px;
		}
		#myModal .input-group-addon:not(.black) {
			background-color: transparent;
			border: none;
		}
</style>
</head>

<body>
<@header method="header"  active="12070000" ><#include "/ftl/header.ftl"></@header>
<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">波次管理</a></li>
				<li class="active">合成拣货任务单</li>
			</ul>
		</div>
	</div>
	<div class="container-fluid" style="background-color: white;border: none">
		<#assign whMergeApvQueryCondition = domain.whMergeApvQueryCondition/>
		<div class="row">
			<div class="portlet-body form" style="display: block;">
				<form action="${CONTEXT_PATH}picking/mergeApv/search"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="searchForm" id ="domain">
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
					
					<div class="form-body">					
						<div class="form-group">
							<label class="control-label col-md-1">合单类型</label>
								<div class="col-md-3">
									<input class="form-control" name="whMergeApvQueryCondition.mergeApvType" type="text" value="${whMergeApvQueryCondition.mergeApvType}">
								</div>								
							<label class="control-label col-md-1">仓库</label>
								<div class="col-md-3">
									<input class="form-control" name="whMergeApvQueryCondition.warehouseType" type="text" value="${whMergeApvQueryCondition.warehouseType}">
								</div>  
							<label class="control-label col-md-1">面单尺寸</label>
								<div class="col-md-3">
									<input class="form-control" name="whMergeApvQueryCondition.waybillType" type="text" value="${whMergeApvQueryCondition.waybillType}">
								</div>	
							<label class="control-label col-md-1">锁状态</label>
								<div class="col-md-3">
									<select class="form-control" name="whMergeApvQueryCondition.whApvLock.status" value="${whMergeApvQueryCondition.whApvLock.status}" id="lock-status">
										<option value=""></option>
										<option value="2">没锁</option>
										<option value="1">有锁</option>
									</select>
								</div>
							<label class="control-label col-md-1">任务状态</label>
								<div class="col-md-3">
									<select class="form-control" name="whMergeApvQueryCondition.signPayment" value="${whMergeApvQueryCondition.signPayment}" id="signPayment-status">
										<option value=""></option>
										<option <#if whMergeApvQueryCondition.signPayment == true>selected</#if> value="true">已生成</option>
										<option <#if whMergeApvQueryCondition.signPayment?? && whMergeApvQueryCondition.signPayment ==false>selected</#if> value="false">未生成</option>
									</select>
								</div> 
							<label class="control-label col-md-1">发货类型</label>
								<div class="col-md-3">
                                    <input class="form-control" name="whMergeApvQueryCondition.shipStatusStr" value="${whMergeApvQueryCondition.shipStatusStr}" id="shipStatus-id"  type="text">
								</div>
							<label class="control-label col-md-1">发货单号</label>
								<div class="col-md-3">
									<input class="form-control" type="text" name="whMergeApvQueryCondition.apvNo" placeholder="请输入发货单号" value="${whMergeApvQueryCondition.apvNo}" id= "apvNo-id">
								</div>
							
							<label class="control-label col-md-1">物流方式</label>
								<div class="col-md-3">
									<input class="form-control" type="text" name="whMergeApvQueryCondition.logisticsCompany"  value="${whMergeApvQueryCondition.logisticsCompany}">
								</div>
							
							<label class="control-label col-md-1">平台</label>
								<div class="col-md-3">
									<input class="form-control" type="text" name="whMergeApvQueryCondition.saleChannel"  value="${whMergeApvQueryCondition.saleChannel}">
								</div>
						
							<label class="control-label col-md-1">创建时间</label>
								<div class="col-md-3">
									<div class="input-group">
										<input class="form-control Wdate" type="text" name="whMergeApvQueryCondition.fromCreateDate" placeholder="" readonly="readonly" value="${whMergeApvQueryCondition.fromCreateDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
										<span class="input-group-addon">到</span>
										<input class="form-control Wdate" type="text" name="whMergeApvQueryCondition.toCreateDate" placeholder="" readonly="readonly" value="${whMergeApvQueryCondition.toCreateDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
								</div>
							
							<label class="control-label col-md-1">跟踪号</label>
								<div class="col-md-3">
									<select class="form-control" name="whMergeApvQueryCondition.isExistTrackingNumber" value="${whMergeApvQueryCondition.isExistTrackingNumber}" id="exist-trackingnumber">
										<option value=""></option>
										<option value="false">没有跟踪号</option>
										<option value="true">有跟踪号</option>
									</select>
								</div>
								
							<label class="control-label col-md-1">频次</label>
								<div class="col-md-3 input-group">
									<input type="number"  name="whMergeApvQueryCondition.startFrequency"  id="startFrequency"  value="${whMergeApvQueryCondition.startFrequency }"  class="Wdate form-control"   digits = "true"/>
									<span class="input-group-addon">到</span>
									<input type="number"  name="whMergeApvQueryCondition.endFrequency"  id="endFrequency"  value="${whMergeApvQueryCondition.endFrequency }"  class="Wdate form-control"  digits = "true"/>
								</div>
						
							<label class="control-label col-md-1">周转筐</label>
								<div class="col-md-3">
									<input class="form-control" type="text" name="whMergeApvQueryCondition.boxNo"  value="${whMergeApvQueryCondition.boxNo}">
								</div>
								
							<label class="control-label col-md-1">订单来源</label>
							<div class="col-md-3">
								<select class="form-control" name="whMergeApvQueryCondition.shipService" value="${whMergeApvQueryCondition.shipService}" id="shipService-id">
									<option value="">全部</option>
									<option value="PMS">PMS</option>
									<option value="OMS">OMS</option>
								</select>
							</div>	
							
							<label class="control-label col-md-1">区域</label>
							<div class="col-md-3">
								<input class="form-control" name="whMergeApvQueryCondition.area" type="text" value="${whMergeApvQueryCondition.area}">
							</div>
							
							<label class="control-label col-md-1">通道</label>
							<div class="col-md-3" id="location_access_id">
								<input class="form-control" name="whMergeApvQueryCondition.access" type="text" value="${whMergeApvQueryCondition.access}">
							</div>

                            <label class="control-label col-md-1">SKU</label>
                            <div class="col-md-3">
                                <input class="form-control" name="whMergeApvQueryCondition.sku" type="text" value="${whMergeApvQueryCondition.sku}">
                            </div>
							<label class="control-label col-md-1">标签</label>
							<div class="col-md-3">
								<select class="form-control" name="whMergeApvQueryCondition.buyerCheckout" value="${whMergeApvQueryCondition.buyerCheckout}" id="apv-flag-id">
									<option value="">全部</option>
									<option value="EUR" ${(whMergeApvQueryCondition.buyerCheckout == 'EUR')?string('selected', '')}>欧代</option>
									<option value="UK" ${(whMergeApvQueryCondition.buyerCheckout == 'UK')?string('selected', '')}>英代</option>
									<option value="RFP" ${(whMergeApvQueryCondition.buyerCheckout == 'RFP')?string('selected', '')}>加强包装</option>
									<option value="IC" ${(whMergeApvQueryCondition.buyerCheckout == 'IC')?string('selected', '')}>拦截订单</option>
									<option value="QC_IC" ${(whMergeApvQueryCondition.buyerCheckout == 'QC_IC')?string('selected', '')}>质控拦截</option>
									<option value="OVERSIZE" ${(whMergeApvQueryCondition.buyerCheckout == 'OVERSIZE')?string('selected', '')}>超体积拦截</option>
									<option value="OVERWEIGHT" ${(whMergeApvQueryCondition.buyerCheckout == 'OVERWEIGHT')?string('selected', '')}>超重拦截</option>
									<option value="GPSR" ${(whMergeApvQueryCondition.buyerCheckout == 'GPSR')?string('selected', '')}>GPSR</option>
								</select>
							</div>
                            <label class="control-label col-md-1">SMT是否超时:</label>
                            <div class="col-md-3">
                                <input class="form-control" type="text" name="whMergeApvQueryCondition.overTime" value="<#if whMergeApvQueryCondition.overTime??>${whMergeApvQueryCondition.overTime?string }</#if>">
                            </div>
                            <label class="control-label col-md-1">SMT剩余时间(小时):</label>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <input class="form-control Wdate" type="number" name="whMergeApvQueryCondition.fromRemainTime" id="whMergeApvQueryCondition.fromRemainTime" placeholder="" value="${whMergeApvQueryCondition.fromRemainTime }" step="1">
                                    <span class="input-group-addon">到</span>
                                    <input class="form-control Wdate" type="number" name="whMergeApvQueryCondition.toRemainTime" id="whMergeApvQueryCondition.toRemainTime" placeholder="" value="${whMergeApvQueryCondition.toRemainTime }" step="1">
                                </div>
                            </div>

							<label class="control-label col-md-1">特殊订单</label>
							<div class="col-md-3">
								<select class="form-control" name="whMergeApvQueryCondition.isNanjingShop" value="${whMergeApvQueryCondition.isNanjingShop}" >
									<option value="false"></option>
									<option value="true" <#if whMergeApvQueryCondition.isNanjingShop == true>selected</#if>>南宁仓店铺</option>
								</select>
							</div>
						</div>
					</div>
					<div>
						<div class="pull-left" style="margin-left: 10px;">
							<button type="button" class="btn  btn-default" onclick="checkNegative();">
								反选
							</button>
							<button type="button" class="btn  btn-default" onclick="checkAll();">
								全选
							</button>
                            <@header method="auth" authCode="DELIVERY_ORDER_MANAGE_LOCK_DELIVERY_ORDER">
							<button type="button" class="btn  btn-default" onclick="lockApv()">
								<i class="icon-lock"></i> 锁定发货单
							</button>
                            </@header>
                            <@header method="auth" authCode="DELIVERY_ORDER_MANAGE_UNLOCK_DELIVERY_ORDER">
							<button type="button" class="btn  btn-default" onclick="unlockApv()">
								<i class="icon-unlock"></i> 解锁发货单
							</button>
                            </@header>
                            <@header method="auth" authCode="DELIVERY_ORDER_MANAGE_GENERATE_PICKING_TASK">
							<button type="button" class="btn  btn-default" onclick="createPickingTask()">
								<i class="icon-pickingTask"></i> 生成拣货任务
							</button>
							</@header>
                            <@header method="auth" authCode="DELIVERY_ORDER_MANAGE_GENERATE_HOT_PICKING_TASK">
							<button type="button" class="btn  btn-default" onclick="createGoodsPickingTask()">
								<i class="icon-pickingTask"></i> 生成热销拣货任务
							</button>
							</@header>
                            <@header method="auth" authCode="DELIVERY_ORDER_MANAGE_GENERATE_DELIVERY_WORK_PICKING_TASK">
							<button type="button" class="btn  btn-default" onclick="createExpressPickingTask()">
								<i class="icon-pickingTask"></i> 生成快递业务拣货任务
							</button>
							</@header>
                            <@header method="auth" authCode="DELIVERY_ORDER_MANAGE_GENERATE_FBA_PICKING_TASK">
							<button type="button" class="btn  btn-default" onclick="createFBAPickingTask()">
								<i class="icon-pickingTask"></i> 生成FBA拣货任务
							</button>
                            </@header>
							<@header method="auth" authCode="DELIVERY_ORDER_MANAGE_GENERATE_PICKING_ZONE_PICK_TASK">
								<button type="button" class="btn  btn-default" onclick="createAislePickingTask()">
									<i class="icon-pickingTask"></i> 生成拣货区拣货任务
								</button>
							</@header>
                            <@header method="auth" authCode="DELIVERY_ORDER_MANAGE_GENERATE_SOLO_PICK_TASK">
							<button type="button" class="btn  btn-default" onclick="createSingleVarietyPickingTask()">
								<i class="icon-pickingTask"></i> 生成单品拣货任务
							</button>
                            </@header>
							<@header method="auth" authCode="MEAGER_APV_LOGISTICS_CONFIG">
							<button type="button" class="btn  btn-default"  data-toggle="modal" data-target="#myModal">
								<i class="icon-meagerApvLogisticsConfig"></i> 合单加急渠道
							</button>
							</@header>
						</div>
	
						<div class="col-md-offset-10" style="text-align: right;margin-right: 10px;">
							<button type="button" id="expandFn" class="btn blue">收起</button>
							<button type="button" class="btn btn-default" onclick="formReset(this)">
								<i class="icon-refresh"></i> 重置
							</button>
							<button type="submit" class="btn blue">
								<i class="icon-search"></i> 查询
							</button>
						</div>	
					</div>
				</form>
			</div>
			<br />
			<div class="row">
				<div id="fixedDiv" class="col-md-12">
					<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
						<colgroup>
	                           <col width="8%" />
	                           <col width="5%" />
	                           <col width="5%"/>
	                           <col width="5%"/>
	                           <col width="5%"/>
	                           <col width="8%"/>
	                           <col width="8%" />
	                           <col width="5%" />
	                           <col width="5%" />
	                           <col width="10%" />
	                           <col width="7%" />
	                           <col width="5%" />
	                           <col width="5%" />
	                           <col width="5%" />
	                           <col width="5%" />
	                           <col width="10%" />
						</colgroup>
						<thead>
						<tr>
						   <th>发货单号</th>
						   <th>频次</th>
						   <th>状态</th>
						   <th>任务状态</th>
						   <th>时效</th>
						   <th>平台单号</th>
						   <th>客户ID</th>
						   <th>国家</th>
						   <th>发货单来源</th>
						   <th>SKU</th>
						   <th>库位</th>
						   <th>数量</th>
						   <th>发货类型</th>
						   <th>标签</th>
						   <th>库存区域</th>
						   <th>物流方式</th>
						</tr>
						</thead>
					</table>
				</div>
				<div class="col-md-12" id="task-list-warp">
					<form target="" id="main-form" name="mainForm" method="post" action="">
					<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
						<colgroup>
							<col width="8%" />
							<col width="5%" />
							<col width="5%"/>
							<col width="5%"/>
							<col width="5%"/>
							<col width="8%"/>
							<col width="8%" />
							<col width="5%" />
							<col width="5%" />
							<col width="10%" />
							<col width="7%" />
							<col width="5%" />
							<col width="5%" />
							<col width="5%" />
							<col width="5%" />
							<col width="10%" />
						</colgroup>
						<thead>
							<tr>
								<th>发货单号</th>
								<th>频次</th>
								<th>状态</th>
								<th>任务状态</th>
								<th>时效</th>
								<th>平台单号</th>
								<th>客户ID</th>
								<th>国家</th>
								<th>发货单来源</th>
								<th>SKU</th>
								<th>库位</th>
								<th>数量</th>
								<th>发货类型</th>
								<th>标签</th>
								<th>库存区域</th>
								<th>物流方式</th>
							</tr>
						</thead>
						<tbody>
                            <#assign saleMap=domain.saleChannelMap>
							<#list domain.whApvs as apv>
								<tr id="tr-apv-${apv.id}" class="${(apv.whApvLock.status== 0) ? string('bg' , 'danger')} tr-apv-${apv.id}">
									<td>
										<label class="checkbox-inline"> <input type="checkbox" name="apvIds" value="${apv.id}" /> ${apv.id}
										</label> <span class="help-block">${apv.apvNo }</span>
									</td>
									<td>
										${apv.originalOrderId}
									</td>
									<td>
										<span class="label label-sm label-warning">
											${apv.statusCn }
										</span>
										<#if apv.whApvLock.status != 0 >
											<span class="label label-sm label-danger">
											<i class="icon-lock"></i> 
											</span>
										</#if>
									</td>
									<td>${(apv.signPayment == true) ? string("已生成" , "未生成")}</td>
                                    <#if apv.shipStatus??
                                        && (apv.shipStatus != 16 && apv.shipStatus != 17)
                                        && (apv.platform?? && saleMap[apv.platform?string] == '速卖通')>
                                        <td name="remainTime_${apv.countDownFlag?string}">
                                            <!-- 不需要进行倒计时时，其显示差距时间，需要进行倒计时时，其取值截止时间，以便用于进行倒计时操作 -->
                                            <#if apv.countDownFlag>
                                                ${apv.stopTime}
                                            <#else>
                                                ${apv.intervalTime}
                                            </#if>
                                        </td>
                                    <#else>
                                        <td></td>
                                    </#if>
									<td>${apv.platformOrderId }</td>
									<td>
										<dl>
											<dt>${apv.buyerId }</dt>
											<dd>${apv.buyerName }</dd>
											<dd>${apv.buyerEmail }</dd>
										</dl>
									</td>
									<td nowrap="nowrap">
										${apv.buyerCountry }
									</td>
									<td>
										<#if apv.shipService == 'OMS'>
											OMS
										<#else>
											PMS
										</#if>
									</td>
									<td> </td>
									<td> </td>
									<td> </td>
									<td>  
										<span class="label label-sm label-info">
											${util('enumName',"com.estone.apv.common.ApvOrderType", apv.shipStatus)}
										</span>
									</td>
									<td><span style="color: #008000">
										${util('enumName', 'com.estone.apv.enums.ApvTaxTypeEnum', apv.buyerCheckout)}
										</span>
									</td>
									<td>${apv.apvDesc } </td>
									<td>
										<dl>
											<dd>${apv.logisticsCompany }</dd>
											<dd>${apv.trackingNumber }</dd>
										</dl>
									</td>
								</tr>
								<#list apv.whApvItems as apvItem>
									<tr class="tr-apv-${apv.id}" id="tr-apv-item-${apvItem.id }">
										<td></td>
										<td></td>
										<td></td>
										<td colspan="6">${apvItem.whSku.name }</td>
										<td>${apvItem.sku }（${util('wh',apvItem.whSku.warehouseId)}）</td>
										<td>${apvItem.allotLocationsBr }</td>
										<td>${apvItem.saleQuantity }</td>
										<td></td>
										<td></td>
										<td></td>
										<td></td>
									</tr>
								</#list>	
							</#list>
						</tbody>
					</table>
					</form>
				</div>
			</div>
		</div>
		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	</div>
	<#include "/common/footer.html">
</div>
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
	<div class="modal-dialog" style="width:40%">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
				<h4 class="modal-title" id="myModalLabel">渠道集合管理</h4>
			</div>
			<div class="modal-body">

				<form id="attributeSettingForm">
					<div class="form-group">
					<button type="button"  class="btn  btn-default" id="toScanCode">
						添加
					</button>
					</div>
					<table class="table table-bordered">
						<colgroup>
							<col width="10%"/>
							<col width="75%"/>
							<col width="15%"/>
						</colgroup>
						<thead>
						<tr>
							<th>渠道集合名称</th>
							<th>包含物流方式</th>
							<th>操作</th>
						</tr>
						</thead>
						<tbody id="myTemplate" style="text-align: center">
						<#if (domain.meagerApvLogisticsConfigList)?? && domain.meagerApvLogisticsConfigList?size gt 0>
							<#list domain.meagerApvLogisticsConfigList as setting>
								<tr data-index="${setting.id}" data-level="${setting.id}">
									<td>${setting.logisticsListName}</td>
									<td code = "${setting.shippingMethodCode}">${setting.shippingMethodBr}</td>
									<td align="center"><a id="editLogisticsConfig-${setting.id}" onclick="editLogisticsConfig(this)">编辑</a>&nbsp&nbsp&nbsp <a data-action="deleteLogisticsConfig-${setting.id}" onclick="deleteLogisticsConfig(this)">删除</a></td>
								</tr>
							</#list>
						</#if>
				</tbody>
				</table>
				</form>
			</div>

		</div><!-- /.modal-content -->
	</div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<div class="modal fade" id="addModal" tabindex="-1" role="dialog" aria-labelledby="addModalLabel" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
				<h4 class="modal-title" id="addModalLabel">添加渠道集合</h4>
			</div>
			<div class="modal-body" style="height: 500px">
				<form>
					<input  id="addLogisticsCompanyNameId" type="hidden" value="">
					<div class="form-body">
						<div class="form-group" style="margin-bottom: 20px">
							<label class="control-label col-md-2">集合名称：</label>
							<div class="col-md-9">
								<input class="form-control" id="addLogisticsCompanyName" type="text" value="">
							</div>
						</div>
						<br/>
						<div class="form-group">
							<label class="control-label col-md-2">物流方式：</label>
							<div class="col-md-9">
								<input class="form-control" type="text" id="addLogisticsCompany" name = "addLogisticsCompany"  value="">
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal" id="cancelLogisticsCompany">取消</button>
				<button type="button" class="btn btn-primary" id = "saveLogisticsCompany">确认</button>
			</div>
		</div>
	</div>
</div>


<script type="text/javascript">var initSelect2 = true;</script>
<script type="text/javascript" src="${CONTEXT_PATH}js/pages/pms.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/countdown.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript">
	// 渠道集合
	initShippingMethod('addLogisticsCompany');

	$('#toScanCode').on('click',function () {
		$("#addModal").modal('show');
		$('#addLogisticsCompanyNameId').val('');
		$('#addLogisticsCompanyName').val('');
		$('#addLogisticsCompany').val('');
		$('#addLogisticsCompany').trigger("change.select2");
	});


	$('#saveLogisticsCompany').on('click',function () {
		//3.设置提交按钮失效，以实现防止按钮重复点击
		//$('#saveLogisticsCompany').attr("disabled", true);
		var id = $('#addLogisticsCompanyNameId').val();
		var addLogisticsCompanyName = $('#addLogisticsCompanyName').val();
		if(addLogisticsCompanyName == ''){
			layer.alert('集合名称不能为空！','error');
			$('#saveLogisticsCompany').removeAttr('disabled');
			return false;
		}
		var shippingMethod = $('#addLogisticsCompany').val();
		if(addLogisticsCompany == ''){
			layer.alert('物流方式不能为空！','error');
			$('#saveLogisticsCompany').removeAttr('disabled');
			return false;
		}

		$.ajax({
			url: CONTEXT_PATH + 'meagerApvLogisticsConfig/create',
			type: "POST",
			data: {id: id,logisticsListName:addLogisticsCompanyName,shippingMethod:shippingMethod},
			success: function(response) {
				if (response.status == '500') {
					customizeLayer(response.message, 'error');
				} else{
					layer.alert('修改成功！');
					setTimeout(function() {
						//提交完成后按钮重新设置有效
						//$('#saveLogisticsCompany').removeAttr('disabled');
						//$("#addModal").modal('hide');
						location.reload();
						//$("#addModal").modal('show');
					}, 1500);
				}
			}
		});

	});
	// 编辑
	function editLogisticsConfig(obj){
		debugger;
		var id = $(obj).parent().parent().attr("data-index");
		var logisticsListName = $(obj).parent().parent().find("td").get(0).innerText;
		let newVar = $(obj).parent().parent().find("td").get(1);
		var shippingMethodCode = $(newVar).attr("code");
		$("#addModal").modal('show');
		$('#addLogisticsCompanyNameId').val(id);
		$('#addLogisticsCompanyName').val(logisticsListName);
		$('#addLogisticsCompany').val(shippingMethodCode);
		$('#addLogisticsCompany').trigger("change.select2");
	}
	// 删除
	function deleteLogisticsConfig(obj){
		if(confirm("确认删除?")){
			var id = $(obj).parent().parent().attr("data-index");
			$.getJSON('${CONTEXT_PATH}meagerApvLogisticsConfig/delete?meagerApvLogisticsConfigId='+ id, function(data) {
				if(data.status != 200){
					customizeLayer(data.message,'error');
				}else{
					layer.alert('删除成功！');
					setTimeout(function() {
						location.reload();
					}, 1500);
				}
			});
		}
	}

	var mergeApvTypeArray = ${domain.mergeApvTypes};
	$("input[name='whMergeApvQueryCondition.mergeApvType']").select2({
	    data : mergeApvTypeArray,
	    placeholder : "合单类型",
	    allowClear : true
	});
	
	var waybillTypeArray = ${domain.waybillTypes};
	$("input[name='whMergeApvQueryCondition.waybillType']").select2({
	    data : waybillTypeArray,
	    placeholder : "面单尺寸",
	    allowClear : true
	});

    var apvOrderTypes = ${domain.apvOrderTypes};
    $("input[name='whMergeApvQueryCondition.shipStatusStr']").select2({
        data : apvOrderTypes,
        placeholder : "发货类型",
		multiple: true,
		allowClear : true
    });

	var warehouseTypeArray = ${domain.warehouseTypes};
	$("input[name='whMergeApvQueryCondition.warehouseType']").select2({
	    data : warehouseTypeArray,
	    placeholder : "仓库",
	    allowClear : true
	});

        $('#expandFn').on('click', function (event) {
            if ($(this).hasClass('expand')) {
                $(this).text('收起');
                $('#expand-area').slideDown(200);
            } else {
                $(this).text('展开');
                $('#expand-area').slideUp(200);
            }
            $(this).toggleClass('expand');
            event.preventDefault();
        });
        
        // 下拉框
	initShippingMethodList('whMergeApvQueryCondition.logisticsCompany');

		/*$(document).ready(function() {
			$.getJSON(CONTEXT_PATH + "apvs/getShippingMethod", function (json) {
				if (json) {
					$('#my-select').select2({
						// 自定义选择器
						data: json,
						placeholder: "运输方式",
						// multiple: true,
						allowClear: true,
						//tags: true,
						//tokenSeparators: [',', ' ']
					});
					jsonArr = json;
				} else {
					$("input[name='" + name + "']").attr("placeholder", "没有权限数据").attr("readonly", true);
				}
			});
		});*/

	//监听选择框键入逗号的事件
	$(document).on('keyup', '.select2-input', function (e) {
		if (e.keyCode == 188) { //判断是否为逗号键
			var vals = $(this).val().trim().split(",");
		    var old = $("input[name='whMergeApvQueryCondition.logisticsCompany']").val();
			var oldArr = [];
			if (old) {
				oldArr = old.trim().split(",");
			}
		    oldArr = oldArr.concat(vals);
			$.ajax({
				url: CONTEXT_PATH + "apvs/getShippingMethodByCode",
				type: "post",
				dataType: "json",
				contentType: "application/json;charset=utf-8",
				data: JSON.stringify(oldArr),
				success: function(json) {
					if (json) {
						$("input[name='whMergeApvQueryCondition.logisticsCompany']").val(json);
						$("input[name='whMergeApvQueryCondition.logisticsCompany']").trigger("change.select2");
					} else {
						$("input[name='" + name + "']").attr("placeholder", "没有权限数据").attr("readonly", true);
					}
				}
			});
		}
	});

	// 监听 select2:select 事件
	$(document).on('change', '[name="whMergeApvQueryCondition.logisticsCompany"]', function() {
		var vals = $(this).val().trim().split(",");
		var old = $("input[name='whMergeApvQueryCondition.logisticsCompany']").val();
		var oldArr = [];
		if (old) {
			oldArr = old.trim().split(",");
		}
		oldArr = oldArr.concat(vals);
		$.ajax({
			url: CONTEXT_PATH + "apvs/getShippingMethodByCode",
			type: "post",
			dataType: "json",
			contentType: "application/json;charset=utf-8",
			data: JSON.stringify(oldArr),
			success: function(json) {
				if (json) {
					$("input[name='whMergeApvQueryCondition.logisticsCompany']").val(json);
					$("input[name='whMergeApvQueryCondition.logisticsCompany']").trigger("change.select2");
				} else {
					$("input[name='" + name + "']").attr("placeholder", "没有权限数据").attr("readonly", true);
				}
			}
		});
	});


	function initShippingMethod(name){
		$.getJSON(CONTEXT_PATH + "apvs/getShippingMethod", function(json){
			if (json) {
				$("input[name='" + name + "']").select2({
					data : json,
					placeholder : "运输方式",
					multiple: true,
					allowClear : true,
					tags: true,
					tokenSeparators: [',', ' ']
				});
			} else {
				$("input[name='" + name + "']").attr("placeholder", "没有权限数据").attr("readonly", true);
			}
		});
	}

	function initShippingMethodList(name){
		$.getJSON(CONTEXT_PATH + "picking/mergeApv/getShippingMethod", function(json){
			if (json) {
				$("input[name='" + name + "']").select2({
					data : json,
					placeholder : "运输方式",
					multiple: true,
					allowClear : true,
					tags: true,
					tokenSeparators: [',', ' ']
				});
			} else {
				$("input[name='" + name + "']").attr("placeholder", "没有权限数据").attr("readonly", true);
			}
		});
	}
    	initSaleChannel('whMergeApvQueryCondition.saleChannel');

		// 分页
		var total = "${domain.page.totalCount}";
		var pageNo = "${domain.page.pageNo}";
		var pageSize = "${domain.page.pageSize}";
		$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

        var heights = $("body").height();
        if(heights>910){
            $("#fixed-bottom").addClass("fixed-bottom-height")
        }
       
        $("#shipService-id").val("${domain.whMergeApvQueryCondition.shipService}");
        $("#shipStatus-id").val("${domain.whMergeApvQueryCondition.shipStatusStr}");
		$("#lock-status").val("${domain.whMergeApvQueryCondition.whApvLock.status}");
		$("#exist-trackingnumber").val("${domain.whMergeApvQueryCondition.isExistTrackingNumber}");
		
		
		//全选
		function checkAll() {
			var checkBox = $("#task-list").find(":checkbox");
			checkBox.prop("checked", !checkBox.prop("checked"));
		}

		// 反选
		function checkNegative() {
			var checkBox = $("#task-list").find(":checkbox");
			checkBox.each(function() {
				if($(this).attr('checked')){
		            $(this).removeAttr('checked');
		        }else{
		            $(this).attr('checked','checked');
		        }
			});
		}
		
		// 获取选中的订单
		function getCheckedApvs() {
			var checkedApvs = $("#task-list").find("input[name='apvIds']:checked");
			return checkedApvs;
		}
		
		// 锁定发货单
		function lockApv() {
			var checkedApvs = getCheckedApvs();
			
			var diglog = dialog({
				title: '锁定发货单',
				width: 350,
				height:100,
				url: CONTEXT_PATH + "picking/mergeApv/mergeApvLock",
				okValue: '确定',
			    ok: function () {
			    	
			    	var exportWindow = $(this.iframeNode.contentWindow.document.body);
			    	
			    	var submitForm = exportWindow.find("#submit-form");
			    	
			    	var exportType = submitForm.find("input[name='exportType']:checked").val();
			    	
			    	var submitFormParam = submitForm.serialize();
			    	
			    	//还原分页
			    	$("#page-no").val("${domain.page.pageNo}");
			    	
			    	submitFormParam =  submitFormParam + "&" + $("#domain").serialize();
			    	
			    	// 导出当前选择
			    	if(exportType == 3) {
						if(checkedApvs.length == 0) {
							layer.alert("请选择要操作的数据","error");
							return false;
						}
						
						submitFormParam = submitFormParam + "&" +checkedApvs.serialize();
				    	
			    	} 
			    	
			    	$.post(CONTEXT_PATH + "picking/mergeApv/lock", submitFormParam, function(data){
			    		if (data.status == 200) {
							layer.confirm(data.message,{
								icon: 1,
								btn: ['确定']
							},function () {
								window.location.reload();
							})
						} else {
							customizeLayer(data.message ,"error");
						}
					});
			    	
			    	//还原分页
			    	$("#page-no").val("1");
			    	
			    	setTimeout(function () {
			    		diglog.close().remove();
			    	}, 100);
			    	
			    	return true;
			    },
			    cancelValue: '取消',
			    cancel: function () {}
			});
			diglog.show();
		}
		
		// 解锁发货单
		function unlockApv(){
			var checkedApvs = getCheckedApvs();
			
			var diglog = dialog({
				title: '解锁订单',
				width: 350,
				height:100,
				url: CONTEXT_PATH + "picking/mergeApv/mergeApvUnLock",
				okValue: '确定',
			    ok: function () {
			    	
			    	var exportWindow = $(this.iframeNode.contentWindow.document.body);
			    	
			    	var submitForm = exportWindow.find("#submit-form");
			    	
			    	var exportType = submitForm.find("input[name='exportType']:checked").val();
			    	
			    	var submitFormParam = submitForm.serialize();
			    	
			    	//还原分页
			    	$("#page-no").val("${domain.page.pageNo}");
			    	
			    	submitFormParam =  submitFormParam + "&" + $("#domain").serialize();
			    	
			    	// 导出当前选择
			    	if(exportType == 3) {
						if(checkedApvs.length == 0) {
							layer.alert("请选择要操作的数据","error");
							return false;
						}
						
						submitFormParam = submitFormParam + "&" +checkedApvs.serialize();
			    	} 
			    	
			    	$.post(CONTEXT_PATH + "picking/mergeApv/unlock", submitFormParam, function(data){
			    		if (data.status == 200) {
							layer.confirm(data.message,{
								icon: 1,
								btn: ['确定']
							},function () {
								window.location.reload();
							})
						} else {
							customizeLayer(data.message ,"error");
						}
					});
			    	
			    	//还原分页
			    	$("#page-no").val("1");
			    	
			    	setTimeout(function () {
			    		diglog.close().remove();
			    	}, 100);
			    	
			    	return true;
			    },
			    cancelValue: '取消',
			    cancel: function () {}
			});
			diglog.show();
		}
		
		
		// 生成拣货任务
		function createPickingTask() {
			
			var checkedApvs = getCheckedApvs();

			var mergeApvType = $('input[name="whMergeApvQueryCondition.mergeApvType"]').val();
			var height = 100;
			if (mergeApvType != 2) {
                height = 150;
            }
			var diglog = dialog({
				title: '生成拣货任务',
				width: 350,
				height: height,
				url: CONTEXT_PATH + "picking/mergeApv/pickingTask?mergeApvType=" + mergeApvType,
				okValue: '确定',
			    ok: function () {
			    	
			    	var exportWindow = $(this.iframeNode.contentWindow.document.body);
			    	
			    	var submitForm = exportWindow.find("#submit-form");
			    	
			    	
					var exportType = submitForm.find("input[name='exportType']:checked").val();
			    	
			    	var submitFormParam = submitForm.serialize();
			    	
			    	//还原分页
			    	$("#page-no").val("${domain.page.pageNo}");
			    	
			    	submitFormParam =  submitFormParam + "&" + $("#domain").serialize();
			    	
			    	
					var locationCount = $(submitForm).find("#task-id").val();

			    	if((!locationCount || locationCount == '') && mergeApvType !=2){
			    		layer.alert("请填写库位数!");
			    		return false;
			    	}
			    	
			    	
			    	// 导出当前选择
			    	if(exportType == 3) {
						if(checkedApvs.length == 0) {
							layer.alert("请选择要操作的数据");
							return false;
						}
						
						submitFormParam = submitFormParam + "&" +checkedApvs.serialize();
				    	
			    	} 
			    	
			    	$.post(CONTEXT_PATH + "picking/mergeApv/createPickingTask", submitFormParam, function(data){
			    		if (data.status == 200) {
							layer.confirm(data.message,{
								icon: 1,
								btn: ['确定']
							},function () {
								window.location.reload();
							})
						} else {
							customizeLayer(data.message);
						}
					});
			    	
			    	//还原分页
			    	$("#page-no").val("1");
			    				    	
			    	setTimeout(function () {
			    		diglog.close().remove();
			    	}, 100);
			    	
			    	return true;
			    },
			    cancelValue: '取消',
			    cancel: function () {}
			});
			diglog.show();
		}
		
		
		//生成热销拣货任务
		function createGoodsPickingTask(){
			var checkedApvs = getCheckedApvs();
			
			var diglog = dialog({
				title: '生成热销拣货任务',
				width: 350,
				height:100,
				url: CONTEXT_PATH + "picking/mergeApv/sellwellPickingTask",
				okValue: '确定',
			    ok: function () {
			    	
					var exportWindow = $(this.iframeNode.contentWindow.document.body);
			    	
			    	var submitForm = exportWindow.find("#submit-form");
			    	
					var exportType = submitForm.find("input[name='exportType']:checked").val();
			    	
			    	var submitFormParam = submitForm.serialize();
			    	
			    	//还原分页
			    	$("#page-no").val("${domain.page.pageNo}");
			    	
			    	submitFormParam =  submitFormParam + "&" + $("#domain").serialize();
			    	
			    	var startSalesQuatity = $(submitForm).find("#sku_sales").val();
			    	
			    	if(!startSalesQuatity || startSalesQuatity == ''){
			    		layer.alert("请输入最小SKU销量!");
			    		return false;
			    	}
			    	
			    	if(parseInt(startSalesQuatity) < 20){
			    		layer.alert("最小SKU销量不能小于20!");
			    		return false;
			    	}
			    	
			    	// 导出当前选择
			    	if(exportType == 3) {
						if(checkedApvs.length == 0) {
							layer.alert("请选择要操作的数据");
							return false;
						}
						submitFormParam = submitFormParam + "&" +checkedApvs.serialize();
			    	}
			    	
			    	$.post(CONTEXT_PATH + "picking/mergeApv/createSellwellPickingTask", submitFormParam, function(data){
			    		if (data.status == 200) {
							layer.confirm(data.message,{
								icon: 1,
								btn: ['确定']
							},function () {
								window.location.reload();
							})
						} else {
							customizeLayer(data.message);
						}
					});	
			    	
			    	//还原分页
			    	$("#page-no").val("1");
			    				    	
			    	setTimeout(function () {
			    		diglog.close().remove();
			    	}, 100);
			    	
			    	return true;
			    },
			    cancelValue: '取消',
			    cancel: function () {}
			});
			diglog.show();		
		}
		
		
		
		//生成快递业务拣货任务
		function createExpressPickingTask(){
			var checkedApvs = getCheckedApvs();
			var mergeApvType = $('input[name="whMergeApvQueryCondition.mergeApvType"]').val();
			var diglog = dialog({
				title: '生成快递业务拣货任务',
				width: 350,
				height:100,
				url: CONTEXT_PATH + "picking/mergeApv/expressPickingTask?mergeApvType=" + mergeApvType,
				okValue: '确定',
			    ok: function () {
			    	
					var exportWindow = $(this.iframeNode.contentWindow.document.body);
			    	
			    	var submitForm = exportWindow.find("#submit-form");
			    	
					var exportType = submitForm.find("input[name='exportType']:checked").val();
			    	
			    	var submitFormParam = submitForm.serialize();
			    	
			    	//还原分页
			    	$("#page-no").val("${domain.page.pageNo}");
			    	
			    	submitFormParam =  submitFormParam + "&" + $("#domain").serialize();
			    	
					var locationCount = $(submitForm).find("#task-id").val();
			    	
			    	if((!locationCount || locationCount == '') && mergeApvType !=2){
			    		layer.alert("请填写库位数!");
			    		return false;
			    	}
			    	
			    	// 导出当前选择
			    	if(exportType == 3) {
						if(checkedApvs.length == 0) {
							layer.alert("请选择要操作的数据");
							return false;
						}
						submitFormParam = submitFormParam + "&" +checkedApvs.serialize();
			    	}
			    	
			    	
			    		
			    	$.post(CONTEXT_PATH + "picking/mergeApv/createExpressPickingTask", submitFormParam, function(data){
			    		if (data.status == 200) {
							layer.confirm(data.message,{
								icon: 1,
								btn: ['确定']
							},function () {
								window.location.reload();
							})
						} else {
							customizeLayer(data.message);
						}
					});	
			    	
			    	//还原分页
			    	$("#page-no").val("1");
			    				    	
			    	setTimeout(function () {
			    		diglog.close().remove();
			    	}, 100);
			    	
			    	return true;
			    },
			    cancelValue: '取消',
			    cancel: function () {}
			});
			diglog.show();		
		}
		
		
		//生成FBA拣货任务
		function createFBAPickingTask(){
			var checkedApvs = getCheckedApvs();
			var mergeApvType = $('input[name="whMergeApvQueryCondition.mergeApvType"]').val();
			var diglog = dialog({
				title: '生成FBA拣货任务',
				width: 350,
				height:100,
				url: CONTEXT_PATH + "picking/mergeApv/fbaPickingTask?mergeApvType=" + mergeApvType,
				okValue: '确定',
			    ok: function () {
			    	
					var exportWindow = $(this.iframeNode.contentWindow.document.body);
			    	
			    	var submitForm = exportWindow.find("#submit-form");
			    	
					var exportType = submitForm.find("input[name='exportType']:checked").val();
			    	
			    	var submitFormParam = submitForm.serialize();
			    	
			    	//还原分页
			    	$("#page-no").val("${domain.page.pageNo}");
			    	
			    	submitFormParam =  submitFormParam + "&" + $("#domain").serialize();
			    	
					var taskNum = $(submitForm).find("#task-id").val();
			    	
					var locationCount = $(submitForm).find("#task-id").val();
			    	
			    	if((!locationCount || locationCount == '') && mergeApvType !=2){
			    		layer.alert("请填写库位数!");
			    		return false;
			    	}
			    	
			    	// 导出当前选择
			    	if(exportType == 3) {
						if(checkedApvs.length == 0) {
							layer.alert("请选择要操作的数据");
							return false;
						}
						submitFormParam = submitFormParam + "&" +checkedApvs.serialize();
			    	} 
			    	
			    	$.post(CONTEXT_PATH + "picking/mergeApv/createFBAPickingTask", submitFormParam, function(data){
			    		if (data.status == 200) {
							layer.confirm(data.message,{
								icon: 1,
								btn: ['确定']
							},function () {
								window.location.reload();
							})
						} else {
							customizeLayer(data.message);
						}
					});	
			    	
			    	//还原分页
			    	$("#page-no").val("1");
			    				    	
			    	setTimeout(function () {
			    		diglog.close().remove();
			    	}, 100);
			    	
			    	return true;
			    },
			    cancelValue: '取消',
			    cancel: function () {}
			});
			diglog.show();		
		}
		
		//生成拣货区拣货任务
		function createAislePickingTask(){
			var checkedApvs = getCheckedApvs();
			
			var diglog = dialog({
				title: '生成拣货区拣货任务',
				width: 350,
				height:100,
				url: CONTEXT_PATH + "picking/mergeApv/aislePickingTask",
				okValue: '确定',
			    ok: function () {
			    	
					var exportWindow = $(this.iframeNode.contentWindow.document.body);
			    	
			    	var submitForm = exportWindow.find("#submit-form");
			    	
					var exportType = submitForm.find("input[name='exportType']:checked").val();
			    	
			    	var submitFormParam = submitForm.serialize();
			    	
			    	//还原分页
			    	$("#page-no").val("${domain.page.pageNo}");
			    	
			    	submitFormParam =  submitFormParam + "&" + $("#domain").serialize();
			    	
					var taskNum = $(submitForm).find("#task-id").val();
			    	
					var locationCount = $(submitForm).find("#task-id").val();
			    	
			    	// 导出当前选择
			    	if(exportType == 3) {
						if(checkedApvs.length == 0) {
							layer.alert("请选择要操作的数据");
							return false;
						}
						submitFormParam = submitFormParam + "&" +checkedApvs.serialize();
			    	} 
			    	
			    	$.post(CONTEXT_PATH + "picking/mergeApv/createAislePickingTask", submitFormParam, function(data){
			    		if (data.status == 200) {
							layer.confirm(data.message,{
								icon: 1,
								btn: ['确定']
							},function () {
								window.location.reload();
							})
						} else {
							customizeLayer(data.message);
						}
					});	
			    	
			    	//还原分页
			    	$("#page-no").val("1");
			    				    	
			    	setTimeout(function () {
			    		diglog.close().remove();
			    	}, 100);
			    	
			    	return true;
			    },
			    cancelValue: '取消',
			    cancel: function () {}
			});
			diglog.show();		
		}
		
		// 生成单品拣货任务
		function createSingleVarietyPickingTask() {
			
			var checkedApvs = getCheckedApvs();
			
			var diglog = dialog({
				title: '生成单品拣货任务',
				width: 350,
				height:100,
				url: CONTEXT_PATH + "picking/mergeApv/singleVarietyPickingTask",
				okValue: '确定',
			    ok: function () {
			    	
			    	var exportWindow = $(this.iframeNode.contentWindow.document.body);
			    	
			    	var submitForm = exportWindow.find("#submit-form");
			    	
			    	
					var exportType = submitForm.find("input[name='exportType']:checked").val();
			    	
			    	var submitFormParam = submitForm.serialize();
			    	
			    	//还原分页
			    	$("#page-no").val("${domain.page.pageNo}");
			    	
			    	submitFormParam =  submitFormParam + "&" + $("#domain").serialize();
			    	
			    	
					var locationCount = $(submitForm).find("#task-id").val();
			    	
			    	if(!locationCount || locationCount == ''){
			    		layer.alert("请填写库位数!");
			    		return false;
			    	}
			    	
			    	
			    	// 导出当前选择
			    	if(exportType == 3) {
						if(checkedApvs.length == 0) {
							layer.alert("请选择要操作的数据");
							return false;
						}
						
						submitFormParam = submitFormParam + "&" +checkedApvs.serialize();
				    	
			    	} 
			    	
			    	$.post(CONTEXT_PATH + "picking/mergeApv/createSingleVarietyPickingTask", submitFormParam, function(data){
			    		if (data.status == 200) {
							layer.confirm(data.message,{
								icon: 1,
								btn: ['确定']
							},function () {
								window.location.reload();
							})
						} else {
							customizeLayer(data.message);
						}
					});
			    	
			    	//还原分页
			    	$("#page-no").val("1");
			    				    	
			    	setTimeout(function () {
			    		diglog.close().remove();
			    	}, 100);
			    	
			    	return true;
			    },
			    cancelValue: '取消',
			    cancel: function () {}
			});
			diglog.show();
		}
		
		// 所有通道
        var locationAllAisleList =  ${domain.locationAllAisleList};
		
		var locationRegionList =  ${domain.locationRegionList};
        $("input[name='whMergeApvQueryCondition.area']").select2({
			data : locationRegionList,
			placeholder : "区域",
			multiple: true,
			allowClear : true
		});
    $("input[name='whMergeApvQueryCondition.overTime']").select2({
        data : [{id : "true", text : "是"}, {id : "false", text : "否"}],
        placeholder : "是否超时",
        allowClear : true
        //multiple: true
    });
    // 用于确保输入的剩余时间值为整数
    $("input[id='whMergeApvQueryCondition.fromRemainTime']").on('blur', function() {
        var val = this.value;
        if(!val || val.trim() == '' || isNaN(val)){
            this.value = '';
            return;
        }
        this.value = parseInt(this.value);
    });

    $("input[id='whMergeApvQueryCondition.toRemainTime']").on('blur', function() {
        var val = this.value;
        if(!val || val.trim() == '' || isNaN(val)){
            this.value = '';
            return;
        }
        this.value = parseInt(this.value);
    });

        // 合单类型修改
    $("input[name='whMergeApvQueryCondition.mergeApvType']").off().on("change",function() {
        var data = $(this).val();
        if (!data){
			$("input[name='whMergeApvQueryCondition.area']").select2("val","");
			changeArea(data);
        }
    });
        
        var selectArea = $("input[name='whMergeApvQueryCondition.area']").val();
        changeArea(selectArea);
        
        // 区域、通道联动
        $("input[name='whMergeApvQueryCondition.area']").off().on("change",function() {
			var data = $(this).val();
			if (data){
                var type = $("input[name='whMergeApvQueryCondition.mergeApvType']").val();
			    if (type==null || type==''){
                    $("input[name='whMergeApvQueryCondition.area']").select2("val","");
                    layer.alert("请先选合单类型", 'error');
                    return false;
				}
			}
			changeArea(data);
		});
        
        function changeArea(data) {
        	if (data) {
				var selectAccessArray = new Array();
				var selectAreaList = data.split(",");
				var selectAccessList = $("input[name='whMergeApvQueryCondition.access']").val();
				if (selectAccessList) {
					selectAccessList = selectAccessList.split(",");
					for (var i = 0; i < selectAreaList.length; i++) {
						var selectArea = selectAreaList[i];
						for (var j = 0; j < selectAccessList.length; j++) {
							var selectAccess = selectAccessList[j];
							if (selectAccess.substring(0, 1).indexOf(selectArea) != -1) {
								selectAccessArray.push(selectAccess);
							}
						}
					}
				}
        		
				var dataSplit = data.split(",");
				var array = new Array();
				for (var i = 0; i < dataSplit.length; i++) {
					var locationRegion = dataSplit[i];
					for (var j = 0; j < locationAllAisleList.length; j++) {
						var locationAisle = locationAllAisleList[j];
						if (locationAisle.substring(0, 1).indexOf(locationRegion) != -1) {
							var obj = {
								"id": locationAisle,
								"text": locationAisle
							};
							array.push(obj);
						}
					}
				}
				$("#location_access_id").html(null);
				var html = '<input class="form-control" name="whMergeApvQueryCondition.access" type="text" value="' + selectAccessArray.join(",") + '">';
				$("#location_access_id").html(html);
				$("input[name='whMergeApvQueryCondition.access']").select2({
					data : array,
					placeholder : "通道",
					multiple: true,
					allowClear : true
				});
			} else {
				$("#location_access_id").html(null);
				var html = '<input class="form-control" name="whMergeApvQueryCondition.access" type="text" value="">';
				$("#location_access_id").html(html);
				$("input[name='whMergeApvQueryCondition.access']").select2({
					data : new Array(),
					placeholder : "通道",
					multiple: true,
					allowClear : true
				});
				$("input[name='whMergeApvQueryCondition.access']").val(null);
			}
        }


        $(document).ready(function(){
            // 执行倒计时功能
            startCountDown();
        });

        // 用于执行开始倒计时
        function startCountDown(){
            $("td[name='remainTime_true']").each(function (index,element) {
                var stopDate = $(element).html();
                if (!stopDate || stopDate.trim() == ''){
                    return;
                }
                countdown.resetLabels();
                countdown.setLabels(
                    ' 毫秒| 秒| 分| 小时| 天| 星期| 月| 年| 十年| 世纪| 千年',
                    ' 毫秒| 秒| 分| 小时| 天| 星期| 月| 年| 十年| 世纪| 千年',
                    ' ',
                    ' ',
                    '',
                    function(n){ return n.toString(); });
                new countdown(new Date(stopDate),function (time) {
                    var htmlContent = time.toString();
                    if (time.value > 0){
                        htmlContent = `<span style="color:red;">超出 </span>` + htmlContent;
                    }
                    $(element).html(htmlContent);
                }, countdown.HOURS|countdown.MINUTES|countdown.SECONDS);
            });
        }
	</script>
</body>
</html>