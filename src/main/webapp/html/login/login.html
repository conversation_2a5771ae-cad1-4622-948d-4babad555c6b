<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>易世通达仓库管理系统</title>
<link rel="shortcut icon" href="images/favicon.ico" />
<link rel="stylesheet" type="text/css" href="css/login/ui/css/screen.css">
<link rel="stylesheet" type="text/css" href="css/login/ui/css/base.css">
<link rel="stylesheet" type="text/css" href="css/login/passport/css/login.css">
<style type="text/css">
	.logina-main h3{
		color:#fff;
		text-align: center;
		letter-spacing: 2px;
		font-size: 40px;
		font-weight: bolder;
		font-family: cursive;
	}
	.login-title{
		padding: 10px 0 10px 30px;
		text-align: left;
		font-size: 25px;
		font-weight: bold;
		color: #FFA54F;
	}
	.login-title span{
		font-size: 15px;
		color: #ADADAD;
		padding: 10px;
	}
	.main .tab-con th{
		width: 20px;
    	padding-right: 20px;
	}
	.main .tab-con td{
		width: 100%;
    	padding-right: 20px;
    	padding: 12px 30px 12px 30px;
	}
	.main .tab-con input {
	    border-radius: 3px;
	    width: 100%;
	    height: 50px;
	    box-sizing: border-box;
    }
</style>
</head>
<body style="background: #EEEEEE url(css/login/passport/images/bg_new.jpg) no-repeat;background-size: 100% 100%;position: absolute;width: 100%;height: 100%;">
	<div class="logina-logo"></div>
	<div class="logina-main main clearfix" style="background: transparent; border: none; width: 100%;">
		<#assign whName = whName>
		<div class="tab-con" style="background: transparent;float: none; width: 100%; margin:0 auto;min-height: 50px;">
			<h3>易世通达仓储管理系统（${whName}）</h3>
		</div>
		<div class="tab-con" style="float: none;width: 500px;margin:0 auto;min-height: 200px;padding: 10px 0 50px 0;">
			<form id="form-login" method="post" action="login/check">
				<div class="login-title">用户登录<span>UserLogin</span></div>
				<table border="0" cellspacing="0" cellpadding="0" style="width:100%;">
					<tbody>
						<tr>
							<th></th>
							<td><input id="name" type="text" value=""
							    name="name" placeholder="请输入账号" autocomplete="off" spellcheck="false" autofocus="autofocus"/></td>
							<td></td>
						</tr>
						<tr>
							<th></th>
							<td><input id="password" type="password" value=""
								name="password" placeholder="请输入密码" autocomplete="off" spellcheck="false" onkeypress="if(event.keyCode==13) {toLogin(this);return false;}"/>
							</td>
							<td></td>
						</tr>
						<tr>
							<th></th>
							<td>
								<button class="confirm" type="button"
										style="background: #2d8cf0;border-color: #2d8cf0;width: 100%;" onclick="toLogin()">登 录
								</button>
								<br/><span style="color:red;">${message}</span></td>
							<td></td>
						</tr>
					</tbody>
				</table>
			</form>
		</div>
	</div>
	<div id="footer">
		<div class="copyright">Copyright © 2018 estone 版权所有</div>
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/dist/jquery.11.3.min.js"></script>
	<script type="text/javascript">
		function clearCookie() {
			var keys = document.cookie.match(/[^ =;]+(?=\=)/g);
			if (keys) {
				for (var i = keys.length; i--;) {
					document.cookie = keys[i] + '=0;path=/;expires=' + new Date(0).toUTCString();
				}
			}
		}
		clearCookie()
		function toLogin(obj) {
			$("#form-login").submit();
		}
	</script>
</body>
</html>