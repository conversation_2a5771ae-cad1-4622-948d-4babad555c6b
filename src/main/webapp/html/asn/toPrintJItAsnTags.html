<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <#include "/common/include.html">
</head>
<body class="page-header-fixed page-full-width">
<div>
    <td style="width: 30%;">选择打印机</td>
    <td style="width: 60%;">
        <select class="input-medium" id="jitAsnPrinterTag"></select>
    </td>
</div>
</br>
<div id="order-log-area" style="max-height: 405px;overflow: auto">
    <table class="table table-condensed table-bordered  table-striped">
        <colgroup>
            <col width="20%"/>
            <col width="20%"/>
            <col width="20%"/>
            <col width="20%"/>
            <col width="20%"/>
        </colgroup>
        <thead>
        <tr>
            <th>组合/套装</th>
            <th>SKU条码</th>
            <th>SKU</th>
            <th>数量</th>
            <th>打印数量</th>
        </tr>
        </thead>
        <tbody id="printSku-body">
            <#if domain.fnSkuMap??>
                <#list domain.fnSkuMap?keys as suitSku>
                    <#assign items = domain.fnSkuMap[suitSku]>
                    <#list items as suitItem>
                        <tr class="show-red">
                            <#if suitItem_index ==0>
                                <td rowspan="${items ? size}" align="center" id="check_task_suit_sku_${suitSku}">${suitSku}</td>
                            </#if>
                            <td align="center">${suitItem.skuBarcode}</td>
                            <td align="center">${suitItem.productSku} <#if suitItem.quantity ??><span style="color: red;">*${suitItem.quantity}</span></#if></td>
                            <#if (suitItem.suitFlag)! && suitItem.suitFlag == 1>
                                <#if suitItem_index ==0>
                                    <td rowspan="${items ? size}" align="center">${suitItem.skuSuitNum}</td>
                                    <td rowspan="${items ? size}" align="center" class="print-content">
                                        <input type="hidden" id="${suitItem.skuBarcode}_quantity" value="${suitItem.skuSuitNum}"/>
                                        <input class="form-control input-small" type="hidden" name="skuBarcode" value="${suitItem.skuBarcode}">
                                        <input class="form-control input-small" type="hidden" name="productSku" value="${suitItem.productSku}">
                                        <input class="form-control input-small print-quantity" type="number"  min="0" onblur="checkQuantity('${suitItem.skuBarcode}',this)" name="quantity" value="0">
                                    </td>
                                </#if>
                            <#else >
                                <td align="center">${suitItem.skuQuantity}</td>
                                <td align="center" class="print-content">
                                    <input type="hidden" id="${suitItem.skuBarcode}_quantity" value="${suitItem.skuQuantity}"/>
                                    <input class="form-control input-small" type="hidden" name="skuBarcode" value="${suitItem.skuBarcode}">
                                    <input class="form-control input-small" type="hidden" name="productSku" value="${suitItem.productSku}">
                                    <input class="form-control input-small print-quantity" type="number"  min="0" onblur="checkQuantity('${suitItem.skuBarcode}',this)" name="quantity" value="0">
                                </td>
                            </#if>
                        </tr>
                    </#list>
                </#list>
            </#if>
        </tbody>
    </table>
</div>
</body>
<!-- END BODY -->
<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/print.js?v=${.now?datetime}"></script>
<script>
    window.onload = function () {
        var LODOP = getLodop();
        let printeList=LODOP.Printers.list;
        $("#jitAsnPrinterTag").html("");

        $("#jitAsnPrinterTag").append("<option selected='selected'></option>");

        for (var i = 0; i < printeList.length; i++) {
            if (jitAsnPrinterTag && jitAsnPrinterTag==printeList[i].name){
                $("#jitAsnPrinterTag").append("<option selected='selected' value='" + printeList[i].name + "'>" + printeList[i].name + "</option>");
            }else{
                $("#jitAsnPrinterTag").append("<option  value='" + printeList[i].name + "'>" + printeList[i].name + "</option>");
            }
        }
    };
    var jitAsnPrinterTag;
    $(document).ready(function(){
        var storage = new WebStorageCache();
        if (storage.get("jit_asn_printerTag")) {
            jitAsnPrinterTag = storage.get("jit_asn_printerTag");
        }
    });
    function checkQuantity(sku, obj) {
        var quantity = $("#" + sku + "_quantity").val();
        var num = $(obj).val();
        if(num != ""){
            num = parseInt(num);
            if (num > quantity){
                layer.alert("打印数量不能大于" + quantity, "error");
                $(obj).val("");
            }else if (num < 0){
                layer.alert("打印数量不能小于0", "error");
                $(obj).val("");
            }
        }

    }
</script>
</html>