<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<meta content="text/html; charset=utf-8" http-equiv="Content-type">
	<title>Print</title>
	<style>
		@media print {
			.printbtn {
				display: none;
			}
		}
	</style>
</head>

<body style="padding: 0px; margin: 20px">

	<div class="printbtn">
		<button onclick="myPreview();">打印预览</button>
		&nbsp;
		<button onclick="myPagePrint2(${domain.whAsn.items ? size});">打印</button>
		<input type="hidden" id="print-size" value="${domain.whAsn.items ? size}">
		&nbsp;
		<button onclick="myPrintDesign();">打印设计</button>
		&nbsp;
	</div>
	<h5>
		选择打印机&nbsp;&nbsp;
		<select id="printer"></select>
	</h5>


<form id="print_content">
    <#if (domain.whAsn.items)?? && (domain.whAsn.items)?size gt 0>
	<div id="print-item-0">
		<#list domain.whAsn.items as item>

		<!-- 70*30mm -->
		<div style="width:68mm; height:29mm;text-align: center;margin-top: 0mm;">
			<div style="width: 68mm;text-align:left;">
				<div style="font-size: 18px;font-weight: bolder;margin-left: 2mm;">
					Packing&nbsp;List
				</div>
			</div>
			<div style="width: 68mm;text-align: left;">
				<img style="margin-left: 2mm;" src="${CONTEXT_PATH}servlet/barcode?keycode=${domain.whAsn.receivingCode}-${item.boxNo}&width=50&height=40">
			</div>
            <div style="width: 68mm;">
                <div style="font-size: 13px;text-align: left;font-weight: bolder;margin-left: 2mm;">
					${domain.whAsn.receivingCode }-${item.boxNo}
                </div>
            </div>
			<div style="width: 68mm;text-align:right;">
				<div style="font-size: 14px;font-weight: bolder;margin-right: 1mm;">
					<span style="text-decoration:underline;">${item.boxNo}</span><br/>${domain.whAsn.boxTotal}
				</div>
			</div>
		</div>
		<!-- 单排 -->
		<p style="page-break-after: always; margin: 0; padding: 0; line-height: 1px; font-size: 1px;clear: both;">&nbsp;</p>
	</div><div id="print-item-${item_index }">
	</#list>
</div>
    <#else >
        <h4 style="color: red;"><strong>数据不存在！</strong></h4>
    </#if>
</form>

<object width="0" height="0" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" id="LODOP_OB">
	<embed width="0" height="0" type="application/x-print-lodop" id="LODOP_EM">
</object>

<!-- 打印插件 -->
<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
<script src="${CONTEXT_PATH }js/assets/plugins/jquery-1.10.2.min.js" type="text/javascript" ></script>
	<script src="${CONTEXT_PATH }js/print.js" type="text/javascript" ></script>
	<script language="javascript">
        pageLength = "70mm";//纸张长
        pageWidth = "30mm";//纸张宽
        window.onload = function () {
            printerList();
            //var size = $("#print-size").val();
            // 自动打印
            //setTimeout(myPagePrint2(size), 100);
        };

</script>
</body>
</html>