<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<#include "/common/include.html">
		<style type="text/css">
			.modal-body input {
				width: 240px;
			}

			.top_bar {
				position: fixed;
				top: 0px;
			}

			#task-list td {
				vertical-align: middle;
			}

			#add_modal{
			    margin-top:50px;overflow:hidden;
			}
		</style>
	</head>

	<body>
		<@header method="header" active="11030200"><#include "/ftl/header.ftl"></@header>

		<div id="page" style="background-color: rgb(231, 237, 248)">
			<div class="row">
				<div class="col-md-12" style="padding: 0">
					<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
						<li>
							<a href="#">库存管理</a>
						</li>
						<li class="active">海外仓拣货任务</li>
					</ul>
				</div>
			</div>
			<!-- 内容 -->
			<div class="container-fluid" style="background-color: white;border: none">
				<div class="row">
					<div class="col-md-12 col-new-wms-8">
						<#assign query=domain.query>
						<form action="${CONTEXT_PATH}whAsnPickingTask/search" class="form-horizontal form-bordered form-row-stripped" method="post" modelAttribute="domain" name="whAsnForm" id="domain">
							<!-- 分页信息 -->
							<input id="page-no" type="hidden" name="page.pageNo" value="1">
							<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
							<div class="form-body">
								<div class="form-group">
									<label class="control-label col-md-1" style="width:65px">创建时间:</label>
									<div class="col-md-2" style="width:150px">
                                        <input class="form-control Wdate" type="text" name="query.fromCreationDate" placeholder="" readonly="readonly" value="${query.fromCreationDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
                                    <label class="control-label col-md-1" style="width:25px">到</label>
                                    <div class="col-md-2" style="width:150px">
                                        <input class="form-control Wdate" type="text" name="query.toCreationDate" placeholder="" readonly="readonly" value="${query.toCreationDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    </div>
									<label class="control-label col-md-1" style="width:65px">出库单号:</label>
									<div class="col-md-2" style="width:300px">
										<input class="form-control" type="text" name="query.receivingCode" placeholder="多个逗号分开" value="${query.receivingCode }">
									</div>
									<label class="control-label col-md-1" style="width:50px">任务号:</label>
									<div class="col-md-2" style="width:250px">
										<input class="form-control" type="text" name="query.taskNo" placeholder="多个逗号分开" value="${query.taskNo }">
									</div>
									<label class="control-label col-md-1" style="width:50px">调入仓:</label>
									<div class="col-md-2">
										<input class="form-control" type="text" name="query.warehouseCode" value="${query.warehouseCode }">
									</div>
									<label class="control-label col-md-1" style="width:50px">状态:</label>
									<div class="col-md-2">
										<input class="form-control" type="text" name="query.taskStatus" value="${query.taskStatus}">
									</div>
									<label class="control-label col-md-1" style="width:65px">打印状态:</label>
									<div class="col-md-2">
										<input class="form-control" type="text" name="query.isPrinting" value="${query.isPrinting}">
									</div>
									<label class="control-label col-md-1" style="width:50px">领取人:</label>
									<div class="col-md-2">
										<input class="form-control" type="text" name="query.receivePerson" value="${query.receivePerson}">
									</div>
								</div>
                                <div class="form-group">
									<label class="control-label col-md-1" style="width:65px">完成时间:</label>
									<div class="col-md-2" style="width:150px">
										<input class="form-control Wdate" type="text" name="query.fromPickingEndDate" placeholder="" readonly="readonly" value="${query.fromPickingEndDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
									<label class="control-label col-md-1" style="width:25px">到</label>
									<div class="col-md-2" style="width:150px">
										<input class="form-control Wdate" type="text" name="query.toPickingEndDate" placeholder="" readonly="readonly" value="${query.toPickingEndDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
									<label class="control-label col-md-1" style="width:65px">SKU:</label>
									<div class="col-md-2" style="width:300px">
										<input class="form-control" type="text" name="query.sku" placeholder="多个逗号分开" value="${query.sku }">
									</div>
                                </div>
							</div>

							<div>
								<div class="pull-left" style="margin-left: 10px;">
									<button type="button" id="syncBtn" class="btn btn-default" onclick="batchaPrintQRCode()">
										 打印任务号
									</button>
								</div>

								<div class="col-md-offset-10" style="text-align: right">
									<button type="button" class="btn btn-default" onclick="downloadRecord()">
										<i class="icon-download"></i> 导出
									</button>
									<button type="button" class="btn default" onclick="formReset(this)">
										<i class="icon-refresh"></i> 重置
									</button>
									<button type="submit" class="btn blue">
										<i class="icon-search"></i> 查询
									</button>
								</div>
							</div>
						</form>
					</div>
					<br/>
				</div>

				<div class="row">
					<div id="fixedDiv" class="col-md-12">
						<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
							<colgroup>
								<col width="5%" />
								<col width="10%" />
								<col width="10%" />
								<col width="5%" />
								<col width="10%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="10%" />
								<col width="10%" />
								<col width="10%" />
								<col width="5%" />
							</colgroup>
							<thead>
								<tr>
									<th>编号</th>
									<th>出库单号</th>
									<th>任务号</th>
									<th>调出仓</th>
									<th>调入仓</th>
									<th>sku种类</th>
									<th>件数</th>
									<th>任务状态</th>
									<th>打印状态</th>
									<th>领取人/领取时间</th>
									<th>完成人/完成时间</th>
									<th>创建人/创建时间</th>
									<th>日志</th>
								</tr>
							</thead>
						</table>
					</div>
					<div class="col-md-12" id="task-list-warp">
						<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
							<colgroup>
								<col width="5%" />
								<col width="10%" />
								<col width="10%" />
								<col width="5%" />
								<col width="10%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="10%" />
								<col width="10%" />
								<col width="10%" />
								<col width="5%" />
							</colgroup>
							<thead>
								<tr>
									<th><label class="checkbox-inline"><input type="checkbox" id="check-all" name="checkAll"> 编号</label></th>
									<th>出库单号</th>
									<th>任务号</th>
									<th>调出仓</th>
									<th>调入仓</th>
									<th>sku种类</th>
									<th>件数</th>
									<th>任务状态</th>
									<th>打印状态</th>
									<th>领取人/领取时间</th>
									<th>完成人/完成时间</th>
									<th>创建人/创建时间</th>
									<th>日志</th>
								</tr>
							</thead>
							<tbody>
								<#list domain.whAsnPickingTasks as whAsnPickingTask>
										<tr>
											<td>
												<label class="checkbox-inline"><input type="checkbox" value="${whAsnPickingTask.id}" name="ids"> ${whAsnPickingTask.id}</label>
											</td>
											<td>${whAsnPickingTask.receivingCode}</td>
											<td>${whAsnPickingTask.taskNo}</td>
											<td>${util('enumName', 'com.estone.warehouse.enums.LocationWarehouseType', whAsnPickingTask.warehouseCodeDeliver)}</td>
											<td>${whAsnPickingTask.warehouseCode}-${util('enumName', 'com.estone.asn.enums.AsnWarehouseStatus', whAsnPickingTask.warehouseCode)}</td>
											<td>${whAsnPickingTask.skuSpecies}</td>
											<td>${whAsnPickingTask.skuTotal}</td>
											<td id="${whAsnPickingTask.id}_taskStatus">${util('enumName', 'com.estone.asn.enums.AsnPickTaskStatus', whAsnPickingTask.taskStatus)}</td>
											<td>${util('enumName', 'com.estone.picking.enums.PickingTaskIsPrinting', whAsnPickingTask.isPrinting)}</td>
											<td>${util('name',whAsnPickingTask.receivePerson)} <br/> ${whAsnPickingTask.receiveDate}</td>
											<td>
												<#if whAsnPickingTask.pickingEndDate??>
													${util('name',whAsnPickingTask.receivePerson)} <br/> ${whAsnPickingTask.pickingEndDate}
												</#if>
											</td>
											<td>${util('name',whAsnPickingTask.createdBy)} <br/> ${whAsnPickingTask.createdDate}</td>
											<td>
												<button type="button" class="btn btn-xs btn-info" onclick="viewLog(${whAsnPickingTask.id}, 'whAsnPickTask')">日志</button>
											</td>
										</tr>
								</#list>
							</tbody>
						</table>
					</div>
				</div>
				<div id="fixed-bottom">
					<div id="pager"></div>
				</div>
			</div>
			<!-- 打印弹窗 -->
			<div class="modal fade ui-popup" id="print_modal" tabindex="-1" role="dialog" aria-labelledby="organization-add-label" aria-hidden="true">
				<div class="modal-dialog">
					<div class="modal-content"></div>
				</div>
			</div>
			<#include "/common/footer.html">
		</div>

		<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
		<script type="text/javascript">
			// 分页
			var total = "${domain.page.totalCount}";
			var pageNo = "${domain.page.pageNo}";
			var pageSize = "${domain.page.pageSize}";

			$("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });
			var heights = $("body").height();
			if(heights > 910) {
				$("#fixed-bottom").addClass("fixed-bottom-height")
			}

			// 全选
			var checkAll = $("input[name='checkAll']");
			// 子选项
			var itemIds = $("input[name='ids']");
			checkAll.change(
				function() {
					itemIds.prop("checked", $(this).prop("checked"));
					itemIds.each(function() {
						var f = $(this).is(":checked");
						var checkClass = $(this).prop("class");
						$("." + checkClass).each(function() {
							$(this).prop("checked", f);
						})
					})
				}
			);
			// 获取选中的记录
			function getCheckedIds() {
				var checkedIds = $("input[name='ids']:checked");
				return checkedIds;
			}

			// 状态
			var resultJson = ${domain.asnPickTaskJson};
			$("input[name='query.taskStatus']").select2({
				data : resultJson,
				placeholder : "状态",
				allowClear : true
				//multiple: true
			});

			// 目的仓
			var asnWhJson = ${domain.asnWhJson};
			$("input[name='query.warehouseCode']").select2({
				data : asnWhJson,
				placeholder : "目的仓",
				allowClear : true
				//multiple: true
			});

			//打印
			var isPrintingArray = ${domain.isPrintings};
			$("input[name='query.isPrinting']").select2({
				data : isPrintingArray,
				placeholder : "打印状态",
				allowClear : true
			});

			// 领取人
			$.getJSON(CONTEXT_PATH + "system/saleusers/userByRole", function(json){
				if (json) {
					$("input[name='query.receivePerson']").select2({
						data : json,
						placeholder : "领取人",
						allowClear : true
					});
				} else {
					$("input[name='query.receivePerson']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
				}
			});

			// 导出
			function downloadRecord() {
				var checkedDatas = getCheckedIds();
				if(checkedDatas.length == 0) {
					var param = $("#domain").serialize();

					window.open(CONTEXT_PATH + "whBatchReturn/download?" + param);
				} else {
					var ids = checkedDatas.serialize();
					window.open(CONTEXT_PATH + "whBatchReturn/download?" + ids);
				}
			}

			//打印任务号
			function batchaPrintQRCode() {
				debugger;
				var checkedDatas = getCheckedIds();

				if(checkedDatas.length == 0) {
					layer.alert("请选择要操作的任务",'error');
					return;
				}else if (checkedDatas.length > 100) {
					layer.alert("批量打印不能超过100个任务!",'error');
					return;
				}
				var trueArray = new Array();
				var falseArray = new Array();
				for (var i = 0; i < checkedDatas.length; i++) {
					var checked = checkedDatas[i];
					var status = $("#"+checked.value+"_taskStatus").text();
					if (status.indexOf('待领取') != -1) {
						trueArray.push(checked.value);
					}else {
						falseArray.push(checked.value);
					}
				}

				if (falseArray.length>0) {
					layer.alert("选择了的:"+falseArray.length+"条不符合的任务将不会打印", 'error');
				}

				var taskIds = "";
				for (var i = 0; i < trueArray.length; i++) {
					taskIds += "taskIds=" + trueArray[i] + "&";
				}

				if (trueArray.length>0) {
					$("#print_modal").removeData("bs.modal");
					$("#print_modal").modal({
						remote: CONTEXT_PATH + "whAsnPickingTask/qRCodePrint?" + taskIds
					});
				}
			}


		</script>
	</body>

</html>