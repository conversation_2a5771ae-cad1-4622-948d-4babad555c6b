<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<div class="modal-dialog enterprise-modal">
	<div class="modal-content">
		<div class="modal-header">
			<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
			<h4 class="modal-title" id="expert-modal-label">打拣货任务号</h4>
		</div>
		<div class="modal-body form-horizontal portlet">
			<div class="printbtn">
			    <button onclick="myPreview();">打印预览</button> &nbsp;
			    <button onclick="myPrint();">打印</button>  &nbsp;
				<button onclick="myPrintDesign();">打印设计</button>  &nbsp;
			</div>
			
			<div id="print_content">
				<#list domain.whAsnPickingTasks as task>
					<!-- 100*100 -->
					<div style="width: 95mm; height: 49mm; font-weight: 300; margin:0 auto">
						<div style="width: 95mm; height: 5mm; margin:0 auto; text-align:center;"/>
						<div style="width: 95mm; height: 40mm; margin:0 auto; text-align:center;">
							<table style="margin:auto;">
								<thead>
									<tr >
										<td style="text-align: center; border: 1px solid black; color: #000;" rowspan="5" >
											<img src="${CONTEXT_PATH}servlet/qrcode?keycode=${task.taskNo}&size=120">
										</td>
									</tr>
									<tr>
										<td style="text-align: center; border: 1px solid black; color: #000;">
											海外仓出库
										</td>
									</tr>
									<tr>
										<td style="text-align: center; border: 1px solid black; color: #000;">
											100*150
										</td>
									</tr>
									<tr>
										
										<td style="text-align: center; border: 1px solid black; color: #000;">
											${.now?string["yyyy-MM-dd HH:mm:ss"]}
										</td>
									</tr>
									<tr>	
										<td style="text-align: center; border: 1px solid black; color: #000;">
											${ task.taskNo }
										</td>
									</tr>
								</thead>
							</table>
						</div>
					</div>
					<input type="hidden" name="print-taskIds" value="${task.id }" />
					<p style="page-break-after: always; margin: 0; padding: 0; line-height: 1px; font-size: 1px;clear: both;">&nbsp;</p>
				</#list>
			</div>
		
			<object width="0" height="0" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" id="LODOP_OB"> 
			    <embed width="0" height="0" type="application/x-print-lodop" id="LODOP_EM">
			</object>
		</div>
	</div>
</div>
	
<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
<script language="javascript">
	var LODOP; //声明为全局变量 
	function CheckIsInstall() {
		try {
			var LODOP = getLodop(document.getElementById('LODOP_OB'), document.getElementById('LODOP_EM'));
			if ((LODOP != null) && (typeof (LODOP.VERSION) != "undefined")) {
				return LODOP.VERSION;
			}	
		} catch (err) {
			//layer.alert("Error:本机未安装或需要升级!");
		}
		return false;
	}
	function myPrint() {
		
		if ($("input[name='print-taskIds']") == '') {
			layer.alert("没有要打印的号码", "error");
			return false;
		}
		CreatePrintPage();
		LODOP.PRINT();
		
		$("input[name='print-taskIds']").each(function (index) {
			var taskId = $(this).val();
			$.ajax({
				url : CONTEXT_PATH + "whAsnPickingTask/updatePrint",
				data : {taskId : taskId, type: 1}
			});
		});
		
	};
	function myPreview() {
		CreatePrintPage();
		LODOP.PREVIEW();
	};
	function myPrintDesign() {
		CreatePrintPage();
		LODOP.PRINT_DESIGN();
	};
	function CreatePrintPage() {
		LODOP = getLodop(document.getElementById('LODOP_OB'), document.getElementById('LODOP_EM'));
		LODOP.PRINT_INIT("打印");
		try {
			if (typeof (eval(CreatePrintPageWithImage)) == 'function') {
				return CreatePrintPageWithImage();
			}
		} catch (e) {
		}
	    
		LODOP.ADD_PRINT_HTM(0, 0, "100mm", "100mm", document.getElementById('print_content').innerHTML);
	};
</script>