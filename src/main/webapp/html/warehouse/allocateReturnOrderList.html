<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <#include "/common/include.html">
        <style type="text/css">
            .modal-body input {
                width: 240px;
            }
            .control-label {
                margin-top: 2px;
            }
            .form-bordered .control-label {
                padding-top: 14px;
            }
            .form-horizontal .control-label {
                text-align: right;
            }
            .col-md-1 {
                padding-left: 10px;
                padding-right: 0px;
                width: 5%;
                font-size: 12px;
            }
            .col-md-2 {
                padding-left: 10px;
                padding-right: 0px;
                width: 7.5%;
                font-size: 12px;
            }
            .form-control {
                height: 30px;
                padding: 0px 4px;
                border: 1px solid #ddd;
                color: #000;
                font-size: 10px;
                font-weight: normal;
            }
            .form-bordered .form-group > div {
                padding: 4px 10px 0;
            }
            /*启用table滚动条*/
            .my-div-table{
                overflow-x: auto;
                overflow-y: auto;
                height: 654px;
                width: auto;
                padding-bottom: 38px;
                /*width:1920px;*/
            }
            /*禁用body滚动条*/
            body{
                overflow-x: hidden;
                overflow-y: hidden;
            }
        </style>
        </head>
<body>
<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li><a href="#">仓库管理</a></li>
                <li class="active">调拨返架列表</li>
            </ul>
        </div>
    </div>

    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">
        <div class="row">
            <div class="col-md-12">
                <#assign query = domain.query>
                    <form action="${CONTEXT_PATH}warehouse/allocateReturnOrder/search" class="form-horizontal form-bordered form-row-stripped"
                          method="post" modelAttribute="domain" name="allocateReturnOrderForm" id="domain">
                        <!-- 分页信息 -->
                        <input id="page-no" type="hidden" style="display:none" name="page.pageNo" value="1">
                        <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">

                        <div class="form-body">
                            <div class="form-group">
                                <label class="control-label col-md-1">返架列表号：</label>
                                <div class="col-md-2">
                                    <input class="form-control" type="text" name="query.orderNo" placeholder="" value="${query.orderNo}">
                                </div>

                                <label class="control-label col-md-1">SKU：</label>
                                <div class="col-md-2">
                                    <input class="form-control" type="text" name="query.sku" placeholder="多个查询逗号分开" value="${query.sku }">
                                </div>

                                <label class="control-label col-md-1">状态：</label>
                                <div class="col-md-2">
                                    <input class="form-control" name="query.status" type="text" value="${query.status}">
                                </div>

                                <label class="control-label col-md-1">库存匹配差异：</label>
                                <div class="col-md-2">
                                    <select name="query.mateDiff" class="form-control">
                                        <option vlaue=""></option>
                                        <#list domain.selects as status>
                                            <#if query.mateDiff?? && query.mateDiff?string ("true","false") == status[0]>
                                                <option selected="selected" value="${status[0]}">${status[1]}
                                                </option>
                                                <#else>
                                                    <option value="${status[0]}">${status[1]}</option>
                                            </#if>
                                        </#list>
                                    </select>
                                </div>

                                <label class="control-label col-md-1">创建时间：</label>
                                <div class="col-md-2">
                                    <input class="form-control Wdate" type="text" name="query.fromCreateDate" placeholder="" readonly="readonly" value="${query.fromCreateDate }"
                                           onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>

                                <label class="control-label col-md-1">到</label>
                                <div class="col-md-2">
                                    <input class="form-control Wdate" type="text" name="query.toCreateDate" placeholder="" readonly="readonly" value="${query.toCreateDate }"
                                           onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>

                                <label class="control-label col-md-1">打印时间：</label>
                                <div class="col-md-2">
                                    <input class="form-control Wdate" type="text" name="query.fromPrintDate" placeholder="" readonly="readonly" value="${query.fromPrintDate }"
                                           onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>

                                <label class="control-label col-md-1">到</label>
                                <div class="col-md-2">
                                    <input class="form-control Wdate" type="text" name="query.toPrintDate" placeholder="" readonly="readonly" value="${query.toPrintDate }"
                                           onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-md-1">装车时间：</label>
                                <div class="col-md-2">
                                    <input class="form-control Wdate" type="text" name="query.fromLoadDate" placeholder="" readonly="readonly" value="${query.fromLoadDate }"
                                           onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>

                                <label class="control-label col-md-1">到</label>
                                <div class="col-md-2">
                                    <input class="form-control Wdate" type="text" name="query.toLoadDate" placeholder="" readonly="readonly" value="${query.toLoadDate }"
                                           onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>

                        </div>
                        <div>
                            <div class="pull-left">
                                <div class="btn-group">
                                    <@header method="auth" authCode="RETURN_SHELF_LIST_ADD">
                                    <a class="btn btn-default" href="${CONTEXT_PATH}warehouse/allocateReturnOrder/create">
                                        <span class="icon-plus"></span> 新增
                                    </a>
                                    </@header>
                                    <@header method="auth" authCode="RETURN_SHELF_LIST_PRINT_SHELF_LIST">
                                    <button type="button" class="btn btn-default" onclick="batchPrint()">
                                        打印返架列表号
                                    </button>
                                    </@header>
                                </div>
                            </div>
                            <div class="col-md-offset-12" style="text-align: right">
                                <@header method="auth" authCode="RETURN_SHELF_DOWNLOAD_DETAIL">
                                <button type="button" class="btn btn-default" onclick="downloadCheck()">
                                    <i class="icon-download"></i> 导出明细
                                </button>
                                </@header>
                                <button type="button" onclick="formReset(this)" class="btn btn-default">
                                    <i class="icon-refresh"></i> 重置
                                </button>
                                <button type="submit" class="btn blue">
                                    <i class="icon-search"></i> 查询
                                </button>
                            </div>
                        </div>
                    </form>
            </div>
        </div>
        <br/>
    </div>

    <div class="row">
        <div id="myFixedDiv" class="col-md-12">
            <table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
                <colgroup>
                    <col width="5%"/>
                    <col width="10%"/>
                    <col width="5%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                </colgroup>
                <thead>
                <tr>
                    <th><input type="checkbox" id="check-all" name="checkAll">编号</th>
                    <th>返架列表号</th>
                    <th>SKU种类</th>
                    <th>商品件数</th>
                    <th>匹配调拨返架数量</th>
                    <th>状态</th>
                    <th>创建日期</th>
                    <th>编辑日期</th>
                    <th>打印日期</th>
                    <th>装车日期</th>
                    <th>操作</th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="col-md-12 my-div-table" id="task-list-warp">
            <!-- 内容 -->
            <table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                <colgroup>
                    <col width="5%"/>
                    <col width="10%"/>
                    <col width="5%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                </colgroup>
                <!--<thead>
                <tr>
                    <th><input type="checkbox" id="check-all" name="checkAll">编号</th>
                    <th>返架列表号</th>
                    <th>SKU种类</th>
                    <th>商品件数</th>
                    <th>匹配调拨返架数量</th>
                    <th>状态</th>
                    <th>创建日期</th>
                    <th>编辑日期</th>
                    <th>打印日期</th>
                    <th>装车日期</th>
                    <th>操作</th>
                </tr>
                </thead>-->
                <tbody>
                <#list domain.allocateReturnOrders as order>
                    <tr>
                        <td>
                            <input type="checkbox" value="${order.id}" name="ids">
                            ${order.id}
                        </td>
                        <td>${order.orderNo }</td>
                        <td>${order.skuCount }</td>
                        <td>${order.quantityCount }</td>
                        <td>${order.mateQuantityCount }</td>
                        <td id="${order.id}_status">${order.statusName }</td>
                        <td>
                            ${util('name',order.createBy)}<br/>
                            ${order.creationDate }
                        </td>
                        <td>
                            ${util('name',order.lastUpdatedBy)}<br/>
                            ${order.lastUpdateDate }
                        </td>
                        <td>
                            ${util('name',order.printBy)}<br/>
                            ${order.printDate }
                        </td>
                        <td>
                            ${util('name',order.loadBy)}<br/>
                            ${order.loadDate }
                        </td>
                        <td>
                            <#if order.status == 0>
                                <a class="btn btn-xs btn-default" target="_blank" href="${CONTEXT_PATH}warehouse/allocateReturnOrder/update?id=${order.id }">
                                    <i class="icon-edit"></i>编辑
                                </a>
                            </#if>
                            <a class="btn btn-xs btn-default" target="_blank" href="${CONTEXT_PATH}warehouse/allocateReturnOrder/detail?id=${order.id }">
                                <i class="icon-eye-open"></i>查看
                            </a>
                            <button type="button" class="btn btn-info btn-xs"
                                    onclick="viewLog(${order.id}, 'allocateReturnOrder')">
                                日志
                            </button>
                        </td>
                    </tr>
                </#list>
                </tbody>
            </table>
            <!-- 内容end -->
        </div>
    </div>
    <div id="fixed-bottom">
        <div id="pager"></div>
    </div>

    <!-- 打印弹窗 -->
    <div class="modal fade ui-popup" id="print_modal" tabindex="-1" role="dialog" aria-labelledby="organization-add-label" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content"></div>
        </div>
    </div>
</div>


<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript">
    document.getElementById("fixedTab").style.width = $('#task-list').css('width');
    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";
    $("#pager").initPager({total: total, pageNo: pageNo, pageSize: pageSize});

    var heights = $("body").height();
    if (heights > 910) {
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }

    var statusArray =  ${domain.statuses2};
    $("input[name='query.status']").select2({
        data: statusArray,
        placeholder: "状态",
        //multiple: true,
        allowClear: true
    });

    // 全选
    var checkAll = $("input[name='checkAll']");

    // 子选项
    var itemIds = $("input[name='ids']");
    checkAll.change(
        function () {
            itemIds.prop("checked", $(this).prop("checked"));
            itemIds.each(function () {
                var f = $(this).is(":checked");
                var checkClass = $(this).prop("class");
                $("." + checkClass).each(function () {
                    $(this).prop("checked", f);
                })
            })
        }
    );

    // 获取选中的入库单
    function getCheckedIds() {
        var checkedIds = $("input[name='ids']:checked");
        return checkedIds;
    }

    //打印返架列表号
    function batchPrint() {
        var checkedDatas = getCheckedIds();

        if(checkedDatas.length == 0) {
            layer.alert("请选择要操作的数据",'error');
            return;
        }else if (checkedDatas.length > 10) {
            layer.alert("批量打印不能超过10个!",'error');
            return;
        }

        var trueArray = new Array();
        var falseArray = new Array();
        for (var i = 0; i < checkedDatas.length; i++) {
            var checked = checkedDatas[i];
            var status = $("#"+checked.value+"_status").text();
            if (status.indexOf('待打印') != -1 || status.indexOf('待装车') != -1) {
                trueArray.push(checked.value);
            }else {
                falseArray.push(checked.value);
            }
        }

        if (falseArray.length>0) {
            layer.alert("选择了的:"+falseArray.length+"条不符合的列表将不会打印", 'error');
        }

        var ids = "";
        for (var i = 0; i < trueArray.length; i++) {
            ids += "ids=" + trueArray[i] + "&";
        }

        if (trueArray.length>0) {
            $("#print_modal").removeData("bs.modal");
            $("#print_modal").modal({
                remote: CONTEXT_PATH + "warehouse/allocateReturnOrder/printOrderNo?" + ids
            });
        }
    }

    // 下载
    function downloadCheck() {
        var checkedDatas = getCheckedIds();
        if (checkedDatas.length == 0) {
            var param = $("#domain").serialize();
            if (total > 100000) {
                layer.alert("导出数据不能超过100000条！", "error");
                return;
            }
            window.open(CONTEXT_PATH + "warehouse/allocateReturnOrder/download?" + param);
        } else {
            downloadCheckByPost();
        }
    }


    // 超过500条不能用GET请求
    function downloadCheckByPost() {
        var checkedDatas = getCheckedIds();
        var ids = "";
        for (var i = 0; i < checkedDatas.length; i++) {
            var check = checkedDatas[i];
            var id = $(check).val();
            ids += id;
            if (i != checkedDatas.length - 1) {
                ids += ",";
            }
        }
        var url = CONTEXT_PATH + "warehouse/allocateReturnOrder/download";
        var tempForm = document.createElement("form");
        tempForm.id = "tempForm";
        tempForm.method = "post";
        tempForm.action = url;
        tempForm.target = "blank";
        var hideInput = document.createElement("input");
        hideInput.type = "hidden";
        hideInput.name = "ids";
        hideInput.value = ids;
        tempForm.appendChild(hideInput);
        if (tempForm.attachEvent) {  // IE
            tempForm.attachEvent("onsubmit", function () {
                window.open('about:blank', 'blank');
            });
        } else if (tempForm.addEventListener) {  // DOM Level 2 standard
            tempForm.addEventListener("onsubmit", function () {
                window.open('about:blank', 'blank');
            });
        }
        document.body.appendChild(tempForm);
        if (document.createEvent) { // DOM Level 2 standard
            evt = document.createEvent("MouseEvents");
            evt.initMouseEvent("submit", true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
            tempForm.dispatchEvent(evt);
        } else if (tempForm.fireEvent) { // IE
            tempForm.fireEvent('onsubmit');
        }
        //必须手动的触发
        tempForm.submit();
        document.body.removeChild(tempForm);
    }


</script>
</body>
</html>