<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
    <#include "/common/webSocket.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
#add_modal{
    margin-top:50px;overflow:hidden;
}
.portlet{
    height: 50px;
    margin-top: 10px;
}
.group-inline {
	color: red;
}
#task-list thead>tr>th {
	vertical-align: middle;
}
</style>
</head>
<body>
<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">仓库管理</a></li>
					<li class="active">库存汇总查询</li>
				</ul>
			</div>
		</div>

		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">

		<!-- BEGIN PAGE HEADER-->
		<#assign query=domain.query>
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="col-md-12">
				<form action="${CONTEXT_PATH}warehouse/stocks/full/search"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="checkInForm" id="domain">
					<!-- 分页信息 -->
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-1">SKU</label>
							<div class="col-md-3">
								<input class="form-control" name="query.skuSplit" type="text" value="${query.skuSplit}"  id="uuidSku" onkeypress="if(event.keyCode==13) { inputSku(this); return false;}">
							</div>
							<label class="control-label col-md-1">货位号</label>
							<div class="col-md-3">
								<input class="form-control" name="query.locationNoSplit" type="text" value="${query.locationNoSplit}">
							</div>
							<label class="control-label col-md-1">区域</label>
							<div class="col-md-3">
								<input class="form-control" id="query.area" name="query.area" type="text" value="${query.area}">
							</div>
						</div>
						<div class="form-group">
							
							<label class="control-label col-md-1">通道</label>
							<div class="col-md-3">
								<input class="form-control" id="query.access" name="query.access" type="text" value="${query.access}">
							</div>
							<label class="control-label col-md-1">是否统计汇总</label>
							<div class="col-md-3">
								<select class="form-control" name="query.isTotal" value="${query.isTotal}">
									<option value="false">否</option>
									<option <#if query.isTotal == true>selected</#if> value="true">是</option>
								</select>
							</div>
                            <label class="control-label col-md-1">库存类型</label>
                            <div class="col-md-3">
                                <input class="form-control" name="query.type" type="text" value="${query.type}">
                            </div>
						</div>
                        <div class="form-group">
                            <div class="col-md-8 type-label"></div>
                            <label class="control-label col-md-1 type-label">过滤库存为0</label>
                            <div class="col-md-1 type-label">
                                <input style="margin-top: 10px;" name="query.isFilterZero" type="checkbox" value="true" <#if query.isFilterZero == true> checked </#if>>
                            </div>
                        </div>
					</div>
					<div>
						<div class="col-md-offset-10" style="text-align: right">
							<button type="button" class="btn btn-default" onclick="formReset(this)">
								<i class="icon-refresh"></i> 重置
							</button>
							<button type="submit" class="btn blue">
								<i class="icon-search"></i> 查询
							</button>
                            <@header method="auth" authCode="INVENTORY_ALLOCATION_SEARCH_DOWNLOAD">
							<button type="button" class="btn btn-default" onclick="downloadRecords()">
								<i class="icon-download"></i> 导出
							</button>
                            </@header>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>
		
		<div class="row">
			<div id="fixedDiv" class="col-md-12">
				<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
					<colgroup>
						<col width="5%" />
						<col width="5%" />
						<col width="10%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="6%" />
						<col width="3%" />
                        <col width="3%" />
                        <col width="3%" />
                        <col width="3%" />
                        <col width="3%" />
                        <col width="3%" />
                        <col width="3%" />
                        <col width="3%" />
                        <col width="3%" />
                        <col width="3%" />
                        <col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
					</colgroup>
					<thead>
						<tr>
							<th style="text-align: center; border-right: 1px solid #A9A9A9;" colspan="8"></th>
                            <th style="text-align: center; border-right: 1px solid #A9A9A9;" colspan="6">库存总计</th>
							<th style="text-align: center; border-right: 1px solid #A9A9A9;" colspan="16">本地仓</th>
							<th style="text-align: center; border-right: 1px solid #A9A9A9;" colspan="4">中转仓</th>
						</tr>
						<tr>
							<th><label class="checkbox-inline">编号</label></th>
							<th>SKU</th>
							<th>商品名称</th>
							<th>单品<br/>状态</th>
							<th>仓库销<br/>售属性</th>
							<th>销售频次<br/>（30天）</th>
							<th>动销频次<br/>（30天）</th>
							<th style="border-right: 1px solid #A9A9A9;">库位</th>
                            <th>总库位库存</th>
                            <th>已分配</th>
                            <th>已拣</th>
                            <th>已拣返架</th>
                            <th>拣货缺货</th>
                            <th>取消</th>
                            <th>冻结<br/>库存</th>
                            <th>退货<br/>在途</th>
                            <th>外借<br/>在途</th>
							<th>在途</th>
							<th>仓库</th>
							<th>待QC</th>
							<th>待上架</th>
							<th>上架中</th>
							<th>库位</th>
							<th>可用</th>
							<th>已分配</th>
							<th>已拣</th>
							<th>已拣<br/>返架</th>
							<th>取消</th>
							<th>订单<br/>调拨</th>
							<th style="border-right: 1px solid #A9A9A9;">库存<br/>调拨</th>
							<th>可用库存</th>
							<th>已分配<br/>库存</th>
							<th>已拣库存</th>
							<th>已拣返架<br/>库存</th>
						</tr>
					</thead>
				</table>
			</div>
			<div class="col-md-12" id="task-list-warp">
				<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
					<colgroup>
						<col width="5%" />
						<col width="5%" />
						<col width="10%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="6%" />
                        <col width="3%" />
                        <col width="3%" />
                        <col width="3%" />
                        <col width="3%" />
                        <col width="3%" />
                        <col width="3%" />
						<col width="3%" />
                        <col width="3%" />
                        <col width="3%" />
                        <col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
					</colgroup>
					<thead>
						<tr>
							<td style="text-align: center; border-right: 1px solid #A9A9A9;" colspan="8"></td>
							<td style="text-align: center; border-right: 1px solid #A9A9A9;" colspan="6">库存总计</td>
							<td style="text-align: center; border-right: 1px solid #A9A9A9;" colspan="16">本地仓</td>
							<td style="text-align: center; border-right: 1px solid #A9A9A9;" colspan="4">中转仓</td>
						</tr>
						<tr>
							<th><label class="checkbox-inline"><input type="checkbox" name="checkAll"> 编号</label></th>
							<th>SKU</th>
							<th>商品名称</th>
							<th>单品<br/>状态</th>
							<th>仓库销<br/>售属性</th>
							<th>销售频次<br/>（30天）</th>
							<th>动销频次<br/>（30天）</th>
							<th style="border-right: 1px solid #A9A9A9;">库位</th>
                            <th>总库位库存</th>
                            <th>已分配</th>
                            <th>已拣</th>
                            <th>已拣返架</th>
                            <th>拣货缺货</th>
                            <th>取消</th>
                            <th>冻结<br/>库存</th>
                            <th>退货<br/>在途</th>
                            <th>外借<br/>在途</th>
							<th>在途</th>
							<th>仓库</th>
							<th>待QC</th>
							<th>待上架</th>
							<th>上架中</th>
							<th>库位</th>
							<th>可用</th>
							<th>已分配</th>
							<th>已拣</th>
							<th>已拣<br/>返架</th>
							<th>取消</th>
							<th>订单<br/>调拨</th>
							<th style="border-right: 1px solid #A9A9A9;">库存<br/>调拨</th>
							<th>可用库存</th>
							<th>已分配<br/>库存</th>
							<th>已拣库存</th>
							<th>已拣返架<br/>库存</th>
						</tr>
					</thead>
					<tbody>
						<#list domain.stockFullList as stockFull>
							<tr <#if stockFull.warehouseId != util('localWarehouseId')>style="color: red;"</#if>>
								<td><label class="checkbox-inline"><input class="${stockFull.id}" name="query.ids" type="checkbox" value="${stockFull.id}"> ${stockFull.id} </label></td>
								<td>${stockFull.sku}</td>
								<td>${stockFull.name}</td>
								<td>${stockFull.statusName}</td>
								<td>
									<#if stockFull.saleAttributeSettingStr == ''>
										长呆滞
										<#else>
										${stockFull.saleAttributeSettingStr}
									</#if>
								</td>
								<td>${stockFull.thirtyDaysSalesOrders}</td>
								<td>${stockFull.thirtyDaysSalesDays}</td>
								<td style="border-right: 1px solid #A9A9A9;">${stockFull.locationNumber}</td>
                                <td>${stockFull.totalLocationQuantity}</td>
                                <td>${stockFull.totalAllotQuantity}</td>
                                <td>${stockFull.totalPickQuantity}</td>
                                <td>${stockFull.totalPickReturnQuantity}</td>
                                <td>${stockFull.totalPickNotQuantity}</td>
                                <td>${stockFull.totalCancelQuantity}</td>
                                <td>${stockFull.frozenQuantity}</td>
                                <td>${stockFull.batchReturnQuantity}</td>
                                <td>${stockFull.lendOnwayQuantity}</td>
								<td>${stockFull.onWayQuantity}</td>
								<td>${stockFull.quantity}</td>
								<td>${stockFull.qcQuantity}</td>
								<td>${stockFull.waitingUpQuantity}</td>
								<td>${stockFull.upQuantity}</td>
								<td>${stockFull.locationQuantity}</td>
								<td>${stockFull.surplusQuantity}</td>
								<td>${stockFull.allotQuantity}</td>
								<td>${stockFull.pickQuantity}</td>
								<td>${stockFull.pickReturnQuantity}</td>
								<td>${stockFull.cancelQuantity}</td>
								<td>${stockFull.orderAllocationQuantity}</td>
								<td style="border-right: 1px solid #A9A9A9;">${stockFull.allocationQuantity}</td>
								<td>${stockFull.transferSurplusQuantity}</td>
								<td>${stockFull.transferAllotQuantity}</td>
								<td>${stockFull.transferPickQuantity}</td>
								<td>${stockFull.transferPickReturnQuantity}</td>
							</tr>
						</#list>
						<tr>
							<td style="border-right: 1px solid #A9A9A9;" colspan="8"><span class="group-inline">汇总：</span></td>
                            <td><span class="group-inline">${domain.stockFullGroup.totalLocationQuantityGroup}</span></td>
                            <td><span class="group-inline">${domain.stockFullGroup.totalAllotQuantityGroup}</span></td>
                            <td><span class="group-inline">${domain.stockFullGroup.totalPickQuantityGroup}</span></td>
                            <td><span class="group-inline">${domain.stockFullGroup.totalPickReturnQuantityGroup}</span></td>
                            <td><span class="group-inline">${domain.stockFullGroup.totalPickNotQuantityGroup}</span></td>
                            <td><span class="group-inline">${domain.stockFullGroup.totalCancelQuantityGroup}</span></td>
                            <td><span class="group-inline">${domain.stockFullGroup.frozenQuantityGroup}</span></td>
                            <td><span class="group-inline">${domain.stockFullGroup.batchReturnQuantityGroup}</span></td>
                            <td><span class="group-inline">${domain.stockFullGroup.lendOnwayQuantityGroup}</span></td>
							<td><span class="group-inline">${domain.stockFullGroup.onWayQuantityGroup }</span></td>
							<td><span class="group-inline">${domain.stockFullGroup.quantityGroup }</span></td>
							<td><span class="group-inline">${domain.stockFullGroup.qcQuantityGroup}</span></td>
							<td><span class="group-inline">${domain.stockFullGroup.waitingUpQuantityGroup }</span></td>
							<td><span class="group-inline">${domain.stockFullGroup.upQuantityGroup }</span></td>
							<td><span class="group-inline">${domain.stockFullGroup.locationQuantityGroup }</span></td>
							<td><span class="group-inline">${domain.stockFullGroup.surplusQuantityGroup}</span></td>
							<td><span class="group-inline">${domain.stockFullGroup.allotQuantityGroup}</span></td>
							<td><span class="group-inline">${domain.stockFullGroup.pickQuantityGroup }</span></td>
							<td><span class="group-inline">${domain.stockFullGroup.pickReturnQuantityGroup}</span></td>
							<td><span class="group-inline">${domain.stockFullGroup.cancelQuantityGroup}</span></td>
							<td><span class="group-inline">${domain.stockFullGroup.orderAllocationQuantityGroup }</span></td>
							<td style="border-right: 1px solid #A9A9A9;"><span class="group-inline">${domain.stockFullGroup.allocationQuantityGroup }</span></td>
							<td><span class="group-inline">${domain.stockFullGroup.transferSurplusQuantityGroup }</span></td>
							<td><span class="group-inline">${domain.stockFullGroup.transferAllotQuantityGroup }</span></td>
							<td><span class="group-inline">${domain.stockFullGroup.transferPickQuantityGroup }</span></td>
							<td><span class="group-inline">${domain.stockFullGroup.transferPickReturnQuantityGroup }</span></td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
		

		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	</div>

	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/access.js?v=${.now?datetime}"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery-ui/jquery-ui-1.10.2.custom.min.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
	<script type="text/javascript">

        if ($("input[name='query.type']").val()) {
            $(".type-label").show();
        } else {
            $(".type-label").hide();
        }

        $("input[name='query.type']").change(function() {
            if ($(this).val()) {
                $(".type-label").show();
            } else {
                $(".type-label").hide();
            }
        })

        $("#sku-noStockUp").val("${domain.query.noStockUp}");
		// 全选
		var checkAll = $("#task-list").find("input[name='checkAll']");
		// 子选项
		var recordIds = $("#task-list").find("input[name='query.ids']");
		
		checkAll.change(
		  function () {
			  recordIds.prop("checked", $(this).prop("checked"));
			  
			  recordIds.each(function(){
				  var f = $(this).is(":checked");
					var checkClass = $(this).prop("class");
					$("." + checkClass).each(function(){
						$(this).prop("checked",f);
					})
			  })
		  }
		);
		
		recordIds.change(function(){
			var checkedLenght = recordIds.filter(":checked").length;
			var length = recordIds.length;
			checkAll.prop("checked", checkedLenght == length);
		});
	
	
		// 分页
		var total = "${domain.page.totalCount}";
		var pageNo = "${domain.page.pageNo}";
		var pageSize = "${domain.page.pageSize}";
		$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

        var heights = $("body").height();
        if(heights>910){
            $("#fixed-bottom").addClass("fixed-bottom-height")
        }
        
        function downloadRecords() {
            let uuid = getFileQueueUUID();

            var diglog = dialog({
                title: '导出库存',
                width: 800,
                height:320,
                url: CONTEXT_PATH + "warehouse/stocks/full/downloadmode?type=2&uuid=" + uuid,
                okValue: '确定',
                ok: function () {
                    var exportWindow = $(this.iframeNode.contentWindow.document.body);

                    var submitForm = exportWindow.find("#submit-form");

                    var exportType = submitForm.find("input[name='exportType']:checked").val();

                    var headers =  submitForm.find("input[name='selectHeaders']:checked");

                    var submitFormParam = $('#domain').serialize();
                    // 导出当前选择
                    if(exportType == 3) {
                        var recordIds = $("#task-list").find("input[name='query.ids']:checked");
                        if(recordIds.length == 0) {
                            layer.alert("请选择要操作的数据");
                            return false;
                        }else if (recordIds.length > 300) {
                            layer.alert("选择数量不能超过300!");
                            return false;
                        }
                        submitFormParam = submitFormParam + "&" +recordIds.serialize();
                    }

                    if(headers.length == 0) {
                        layer.alert("请选择要导出的表头字段");
                        return false;
                    }
                    var headersStr = headersJoin(headers);
                    submitFormParam = submitFormParam +"&selectHeaders="+headersStr+"&exportType="+exportType;
                    //还原分页
                    $("#page-no").val("${domain.page.pageNo}");

                    postExcelFile(submitFormParam, CONTEXT_PATH + "warehouse/stocks/full/download?uuid="+uuid);

                    $("#page-no").val("1");

                    beginExport(uuid, '导出库存记录');

                    setTimeout(function () {
                        diglog.close().remove();
                    }, 100);

                    return true;
                },
                cancelValue: '取消',
                cancel: function () {}
            });
            diglog.show();
        }

        function headersJoin(headers) {
            var checkHeaders = "";
            for (var i = 0; i < headers.length; i++) {
                var header = headers[i].value;
                if (i == 0) {
                    checkHeaders += header;
                } else {
                    checkHeaders += "," + header;
                }
            }
            return checkHeaders;
        }
		function postExcelFile(params, url) {
            downloadByPostForm(params, url);
			/*var form = document.createElement("form");
			form.style.display = 'none';
			form.action = url + params;
			form.method = "post";
			document.body.appendChild(form);
			form.submit();
			form.remove();*/
		}

        $("input[name='query.type']").change(function() {
            if ($(this).val()) {
                $(".type-label").show();
            } else {
                $(".type-label").hide();
            }
        })

		var locationRegionList =  ${domain.locationRegionList};
        $("input[name='query.area']").select2({
			data : locationRegionList,
			placeholder : "区域",
			multiple: true,
			allowClear : true
		});
        
        var locationAisleList =  ${domain.locationAisleList};
        $("input[name='query.access']").select2({
			data : locationAisleList,
			placeholder : "通道",
			multiple: true,
			allowClear : true
		});

        // 类型
        var typeArray = ${domain.typeList};
        $("input[name='query.type']").select2({
            data: typeArray,
            placeholder: "库位",
            multiple: false,
            allowClear: true
        });

	</script>
</body>
</html>