<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
    <#include "/common/include.html">
    <style type="text/css">
        #task-list td {
            vertical-align:middle;
        }
        .modal{
            margin-top: 60px;
        }
    </style>
</head>
<body>
<@header method="header" active="11030200"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li><a href="#">库存管理</a></li>
                <li class="active">库存移库</li>
                <li class="active">库存移库明细</li>
            </ul>
        </div>
    </div>
    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">
        <!-- END PAGE HEADER-->
        <div class="row">
            </br>
        </div>

        <div class="row">
            <div class="col-md-12">
                <table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                    <colgroup>
                        <col width="5%"/>
                        <col width="20%" />
                        <col width="15%" />
                        <col width="15%" />
                        <col width="15%" />
                        <col width="15%" />
                    </colgroup>
                    <thead>
                    <tr>
                        <th>序号</th>
                        <th>SKU</th>
                        <th>库位</th>
                        <th>移出库位</th>
                        <th>移入库位</th>
                        <th>库存移库数量</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list domain.whStockMoveItems as item>
                        <tr>
                            <td>${item.id }</td>
                            <td>${item.sku }</td>
                            <td>${item.locationNumber }</td>
                            <td>${util('enumName', 'com.estone.warehouse.enums.StockLogType', item.outLocation)}</td>
                            <td>${util('enumName', 'com.estone.warehouse.enums.StockLogType', item.inLocation)}</td>
                            <td>${item.quantity }</td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript">
</script>
</body>
</html>