<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
.region-style{
    width: 30%;
    float: left;
}
</style>
</head>
<body>
	<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">仓库管理</a></li>
					<li class="active">调拨返架列表</li>
					<li class="active">新增调拨返架</li>
				</ul>
			</div>
		</div>
		<div class="container-fluid" style="background-color: white;border: none">
		<#assign whReturn=domain.whReturn>
		<div class="row">
			<div class="col-md-9">
				<form action="${CONTEXT_PATH}warehouse/allocateReturnOrder2/create"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="checkInForm" id="domain">
					<input type="hidden" name="whReturn.type" value="2">
					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-1" ></label>
							<div class="col-md-3"></div>
							<label class="control-label col-md-1" >调拨列表号</label>
							<div class="col-md-5">
								<div>
								   <input id="returnOrderNo" type="text" name="whReturn.returnOrderNo" readonly class="form-control"  value="${whReturn.returnOrderNo}">
								</div>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1" ></label>
							<div class="col-md-3"></div>
							<label class="control-label col-md-1" >返架单号</label>
							<div class="col-md-5">
								<div>
								   <input type="text" name="whReturn.returnNo" readonly class="form-control"  value="${whReturn.returnNo}">
								</div>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1" ></label>
							<div class="col-md-3"></div>
							<label class="control-label col-md-1" >周转筐</label>
							<div class="col-md-5">
								<div><input type="text" name="whReturn.boxNo" id="boxNo" class="form-control" value="${whReturn.boxNo}" onfocus="isScan = false;" onblur="if(isScan) {return;} $(this).val('');layer.alert('请扫描周转框！', 'error');" onkeypress="if(event.keyCode==13) { getBoxNo(this); return false;}" /></div>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1" ></label>
							<div class="col-md-3"></div>
							<label class="control-label col-md-1" >返架区域</label>
							<div class="col-md-5">
								<label class="radio-inline">
									<input type="radio" checked="checked" name="regionType" id="return_area_false" value="1">
									否
								</label>
								<label class="radio-inline">
									<input type="radio" id="return_area_true" name="regionType" value="2">
									是
								</label>
							</div>
							<div class="col-md-5 return_add_region_selector" style="display:none;">
								<div style="padding-top: 20px;max-height: 200px;overflow-y: scroll;">
									<#list domain.whRuleReturnAreaList as ruleReturnArea>
										<div name="location_region_div">
											<label class="region-style"><input type="checkbox" onclick="regionChange(this)" name="aisles" value="${ruleReturnArea.aisle}">${ruleReturnArea.area}</label>
										</div>
									</#list>
								</div>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1" ></label>
							<div class="col-md-3"></div>
							<label class="control-label col-md-1" >扫描SKU</label>
							<div class="col-md-5">
								<div id="input_sku">
									<input class="form-control" type="text" onblur="$(this).val('');layer.alert('请扫描SKU！', 'error');" onkeypress="if(event.keyCode==13) { inputnext(this); return false;}" id="sku" name="sku">
									<input type="hidden" name="notSku">
								</div>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1" ></label>
							<div class="col-md-3"></div>
							<label class="control-label col-md-1" ></label>
							<div class="col-md-5">
								调拨返架SKU
								<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
									<thead>
										<tr>
											<th>SKU</th>
											<th>标题</th>
											<th>待返架数量</th>
											<th>数量</th>
											<th>删除</th>
										</tr>
									</thead>
									<tbody id="tskubody">
									</tbody>
								</table>
							</div>
						</div>
					</div>
					<div class="col-md-offset-1" style="text-align: center">
						<button type="submit" class="btn blue" id="save_allocation_return">
							<i class="icon-search"></i> 保存
						</button>
					</div>
				</form>
			</div>
			
			<div style="padding-top: 4%;">
				<div class="col-md-3" style="margin-left: -190px;">
					<h2>SKU扫描个数：<span id="sku-quantity" style="font-weight:bold;color:red;"></span></h2>
					<h2>当前扫描SKU：<span id="sku-history" style="font-weight:bold;color:red;"></span></h2>
				</div>
			</div>
			<br/>
		</div>
	</div>
	
	<div id="fixed-bottom">
		<div id="pager"></div>
	</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/packing.js?v=${.now?datetime}"></script>
	<script type="text/javascript">
        var uuIdCacheKey = 'CHECK_RETURN_FROM_ORDER_FOR_UUID_CACHE_KEY_ALLOCAION';
		var apvNo = "";
		var orderOrigin = "";
	
		var isScan = false;
		var skuQuantity = 0;
		var apvQuantity = 0;
		var skuHistory = "";

        $(document).ready(function(){

            initSkuUuIdStorageCache(uuIdCacheKey);
        }); // end ready

		var userForm = $('#domain');
        var url = $("#domain").attr("action");
		userForm.validate({
			rules: {
				"whReturn.returnNo": {
	            	required: true
	            },
				"whReturn.returnOrderNo": {
	            	required: true
	            },
	            "whReturn.boxNo": {
	            	required: true
	            },
	            "notSku": {
	            	required: true
	            }
	        },
	        submitHandler: function (form) {
                if($('#tskubody').find('tr').length == 0){
                    layer.alert('没有要保存的sku','error');
                    return false;
                }
                $('#save_allocation_return').attr("disabled", true);
	        	App.blockUI();

                $.post(url, $('#domain').serialize(), function(data){
                    if(data.status == 500){
                       customizeLayer(data.message,'error');
                        setTimeout(jump, 5000);
                    }else {
                        alert(data.message);
                        setTimeout(jump, 1500);
					}
                });
                function jump(){
                    location.href = CONTEXT_PATH + "warehouse/allocateReturnOrder2";
                }
	        }
	    });
		
		function getBoxNo(obj){
	
			var boxNo = $.trim($(obj).val());
		
			$.getJSON(CONTEXT_PATH + "warehouse/boxs/boxNo?type=4&boxNo=" + boxNo, function(data){
	    		if (data.status == 200) {
					alert("成功！");
					$('#boxNo').val(boxNo);
					isScan = true;
					$("#sku").focus();
				} else {
					$(obj).val("");
					layer.alert("该周转框不存在或者已使用！", "error");
				}
	    	});
		}
		
		function inputnext(obj) {
			var sku_array=new Array();
			$("[id=skuArticlenumber]").each(function(){
				sku_array.push($(this).text());
			  });
			
			// 检查是否还有输入框未输入值
			var visible_inputs = $('#input_scan').find("input:visible").length;
			for (i = 0; i < visible_inputs; i++) {
				var e = $('#input_scan').find("input:visible").get(i);
				if ($(e).val() == '') {
					$(e).focus();
					return false;
				}
			}
			
			var val = $('#sku').val();
            var uuid = val.trim();
			
			if(!val || val.trim() == ''){
				layer.alert("请输入有效sku!");
				return ;
			}

            if(!(val.indexOf("=") == -1)){
                var realSku = val.split('=')[0];
                $('#sku').val(realSku);
                val = realSku;
            }

			// 前端缓存校验是否重复扫描
            if(!checkRetrunUuIdStorageCache(uuIdCacheKey, uuid)){
                layer.alert("唯一码重复扫描！", 'error');
                $('#sku').val("");
                $('#sku').focus();
                return;
            }

			$.ajax({
				url:CONTEXT_PATH+"warehouse/allocateReturnOrder2/skuDetail",
				type:"POST",
				data:{
					sku:val.trim(),
                    returnOrderNo:$('#returnOrderNo').val().trim(),
                    uuid:uuid,
                    step:6
				},
				success:function(result){
				    if(result.status == "200"){

						if(result.message && result.message != ''){
                            var orderItem = JSON.parse(result.message);
                            var whSku = orderItem.whSku;
							// 扫描成功唯一码加入缓存
							addUuIdStorageCache(uuIdCacheKey, uuid);
                            $("#sku-quantity").text(++skuQuantity);
                            $("#sku-history").text(whSku.sku);

							var skuLocation = whSku.locationNumber;
                            if('2' == $('input[type=radio][name=regionType]:checked').val() && skuLocation == null){
                                addWhUniqueSkuLog(uuid, '');
                                customizeLayer("SKU:["+val+"]库位不存在",'error');
                                return;
                            }
							if(isLocationMatch(skuLocation)){
                                var i=sku_array.length;
                                var number=$.inArray(whSku.sku, sku_array);
                                var waitQuantity = orderItem.quantity-orderItem.scanCount;
								if(number==-1){
									var skuoption="<tr><td id='skuArticlenumber'>"+whSku.sku+"<input type='text' name='whReturn.whReturnItems["+i+"].sku' style='width:60px;display:none' value='"+whSku.sku+"'></td>"+
												"<td>"+whSku.name+"</td>"+
												"<td>"+waitQuantity+"</td>"+
												"<td><input type='text' name='whReturn.whReturnItems["+i+"].quantity' style='width:60px;' value='1' onblur='isMinus(this)'></td>"+
												"<td onclick='onRemove(this)'><i class='icon-remove'></i></td></tr>";
									$("#tskubody").prepend(skuoption);
								}else{
									$("[id=skuArticlenumber]").each(function(){
										if(whSku.sku==$(this).text()){
											var number=1+parseInt($(this).next().next().next().find("input").val());
											$(this).next().next().next().find("input").val(number);
										}
									 });
								}
								$("input[name='notSku']").val("1");
								var returnNo = $("input[name='whReturn.returnNo']").val().trim();
								addWhUniqueSkuLog(uuid, returnNo);
							}else{
								audioPlay('error');
								addWhUniqueSkuLog(uuid, '');
								customizeLayer("SKU:["+val+"]库位不匹配",'error');
								return;
							}
							audioPlay('success');
                            //找不到对应的值
						} else{
							audioPlay('error');
                            addWhUniqueSkuLog(uuid, '');
							customizeLayer("SKU:["+val+"]不存在",'error');
						}
					}else{
						audioPlay('error');
                        addWhUniqueSkuLog(uuid, '');
						customizeLayer(result.message,'error');
					}
				},
				error : function() {
					audioPlay('error');
                    customizeLayer("SKU:["+val+"]不存在",'error');
				}
			});
			$('#sku').val("");
			
		}

        // 添加唯一码调拨返架日志
        function addWhUniqueSkuLog(uuid, returnNo) {
            var r = $.ajax({
                type : "get",
                url :CONTEXT_PATH+"warehouse/returns/addWhUniqueSkuLog" ,
                data : {uuid : uuid, returnNo: returnNo, step: 6},
                timeout : 100000,
                beforeSend : function() {
                },
                success : function(responese) {

                },
                error : function() {
                }
            });
        }

		function isMinus(obj){
			var r = /^\d+$/;
			if(!r.test($(obj).val())){
				$(obj).val("1");
				layer.alert("请填写正整数！", "error");
			}
			if($(obj).val() == 0){
				$(obj).val("1");
				layer.alert("不能填写0-不需要请删除！", "error");
			}
		}
		
		function onRemove(obj){
			if(confirm("是否删除该条SKU?")){
				$(obj).parent().remove();
				// 重新编排
				$("#tskubody tr").each(function(i){
					refreshIndex(i, this);
				});
			}
		}
		
		var isFocus = false;
	    function focusSku(){
	    	if (isFocus) {
		    	setTimeout(focusSku, 1000);
			}
	    }
		
		function regionChange(obj){
			$(obj).parent().parent().find("input").prop("checked", $(obj).is(':checked'));
		}
		
		function isLocationMatch(skuLocation){
			var flag = true;
			if('2' == $('input[type=radio][name=regionType]:checked').val()){
				var skuAisle = skuLocation.substring(skuLocation.length - 12, skuLocation.length - 9);
                var checkedAisles = '';
                $("input[name='aisles']:checked").each(function () {
                    checkedAisles += $(this)[0].value + ",";
                });
				if(!(checkedAisles.indexOf(skuAisle) != -1)){
					flag = false;
				}
			}
			return flag;
		}
		
		$('input[type=radio][name=regionType]').change(function() {
			if (this.value == '2') {
				$(".return_add_region_selector").show();
			} else {
				$(".return_add_region_selector").hide();
			}
		});
	</script>
</body>
</html>