<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <#include "/common/include.html">
    <style type="text/css">

    </style>
</head>

<body>
<@header method="header" active="13040000"><#include "/ftl/header.ftl"></@header>
<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li>
                    <a href="#">仓库报表</a>
                <li class="active">订单取消报表明细</li>
            </ul>
        </div>
    </div>
    <div class="container-fluid" style="background-color: white; border: none">
        <#assign query=domain.query>
        <!-- END PAGE HEADER-->
        <div class="row">
            <div class="col-md-12">
                <form action="${CONTEXT_PATH}warehouse/orderCancelItem/search" class="form-horizontal form-bordered form-row-stripped" method="post" modelAttribute="domain"  id="domain" name="orderCancelItemFrom">
                    <!-- 分页信息 -->
                    <input id="page-no" type="hidden" name="page.pageNo" value="1">
                    <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
                    <div class="form-body">
                        <div class="form-group">
                            <label class="control-label col-md-1">取消类型</label>
                            <div class="col-md-3">
                                <input class="form-control" name="query.cancelType" type="text" value="${query.cancelType}">
                            </div>

                            <label class="control-label col-md-1">取消时间</label>
                            <div class="col-md-3">
                                <div class="input-group">

                                    <input class="form-control Wdate" type="text" name="query.startCancelDate" placeholder="" readonly="readonly" value="${query.startCancelDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    <span class="input-group-addon">到</span>
                                    <input class="form-control Wdate" type="text" name="query.endCancelDate" placeholder="" readonly="readonly" value="${query.endCancelDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>

                            <label class="control-label col-md-1">发货单号</label>
                            <div class="col-md-3">
                                <input class="form-control" type="text" name="query.apvNo" placeholder="请输入发货单号" value="${query.apvNo}" id= "apvNo-id">
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="col-md-offset-10" style="text-align: right ">
                            <@header method="auth" authCode="DOWNLOAD_ORDER_CANCEL_ITEM">
                            <button type="button" class="btn btn-default" onclick="downloadOrderCancelItem()">
                                <i class="icon-download"></i> 导出数据
                            </button>
                        </@header>
                            <button type="button" onclick="formReset(this)" class="btn btn-default">
                                <i class="icon-refresh"></i> 重置
                            </button>
                            <button type="submit" class="btn blue">
                                <i class="icon-search"></i> 查询
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <br />
        </div>

        <div class="row">
            <div class="col-md-12" id="task-list-warp">
                <div id="left-table">
                    <table class="table table-striped table-bordered table-hover table-condensed" id="center-table-list">
                        <colgroup>
                            <col width="15%" />
                            <col width="17%" />
                            <col width="18%" />
                            <col width="18%" />
                            <col width="15%" />
                            <col width="17%" />
                        </colgroup>
                        <thead>
                            <th><label class="checkbox-inline"><input type="checkbox"  id="check-all" onclick="checkAll(this);">编号</label></th>
                            <th>发货单号</th>
                            <th>推单日期</th>
                            <th>取消日期</th>
                            <th>取消操作人</th>
                            <th>取消发货类型</th>
                        </thead>
                        <tbody id="table-body">
                        <#list domain.whOrderCancelItems as whOrderCancelItem>
                        <tr>
                            <td>
                                <label class="checkbox-inline"><input type="checkbox" value="${whOrderCancelItem.id}" name="ids" /> ${whOrderCancelItem.id}</label>
                            </td>
                            <td>${whOrderCancelItem.apvNo}</td>
                            <td>${whOrderCancelItem.pushDate}</td>
                            <td>${whOrderCancelItem.cancelDate}</td>
                            <td>${util('name',whOrderCancelItem.cancelBy)}</td>
                            <td>${util('enumName','com.estone.warehouse.enums.OrderCancelType',whOrderCancelItem.cancelType+'')}</td>
                        </tr>
                        </#list>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div id="fixed-bottom">
            <div id="pager"></div>
        </div>
    </div>
    <#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript">
    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";
    $("#pager").initPager({
        total: total,
        pageNo: pageNo,
        pageSize: pageSize
    });

    function getCheckedIds() {
        var checkedRerutnIds = $("input[name='ids']:checked");
        return checkedRerutnIds;
    }

    var cancelTypes = ${domain.cancelTypes};
    $("input[name='query.cancelType']").select2({
        data : cancelTypes,
        placeholder : "类型",
        allowClear : true
    });

    // 导出
    function downloadOrderCancelItem() {
        var checkedDatas = getCheckedIds();

        var diglog = dialog({
            title: '导出',
            width: 350,
            height: 100,
            url: CONTEXT_PATH + "warehouse/locations/downloadmode",
            okValue: '确定',
            ok: function () {

                var exportWindow = $(this.iframeNode.contentWindow.document.body);
                var submitForm = exportWindow.find("#submit-form");
                var exportType = submitForm.find("input[name='exportType']:checked").val();
                var submitFormParam = submitForm.serialize();
                // 导出当前选择
                if (exportType == 3) {
                    if (checkedDatas.length == 0) {
                        layer.alert("请选择要操作的数据!");
                        return false;
                    } else if (checkedDatas.length > 1000) {
                        layer.alert("选择数量不能超过1000!");
                        return false;
                    }
                    submitFormParam = submitFormParam + "&" + checkedDatas.serialize();
                }

                //还原分页
                $("#page-no").val("${domain.page.pageNo}");


                var action = document.orderCancelItemFrom.action;
                var target = document.orderCancelItemFrom.target;
                var method = document.orderCancelItemFrom.method;
                document.orderCancelItemFrom.action = CONTEXT_PATH + "warehouse/orderCancelItem/download?" + submitFormParam;
                document.orderCancelItemFrom.target = "_blank";
                document.orderCancelItemFrom.method = "POST";
                document.orderCancelItemFrom.submit();
                document.orderCancelItemFrom.target = target;
                document.orderCancelItemFrom.action = action;
                document.orderCancelItemFrom.method = method;

                $("#page-no").val("1");

                setTimeout(function () {
                    diglog.close().remove();
                }, 100);

                return true;
            },
            cancelValue: '取消',
            cancel: function () {
            }
        });
        diglog.show();
    }
</script>
</body>

</html>