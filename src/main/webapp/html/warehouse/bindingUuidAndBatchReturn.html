<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
	<@header method="header" active="17020000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">库存管理</a></li>
					<li class="active">退货批次补标</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<#assign whBatchReturn=domain.whBatchReturn>
		<div class="row" style="padding-top: 80px;height: 760px">
			<div class="col-md-10">
				<form action="${CONTEXT_PATH}whBatchReturn/bindingUuid"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="whBatchReturnForm">
					<div class="form-body" style="width: 800px;margin: 0 auto;">
						<div class="form-group">
							<label class="control-label col-md-3">退货批次号：</label>
							<div class="col-md-8">
								<input type="text" id="orderNo" readonly class="form-control" value="${whBatchReturn.orderNo}">
								<input type="hidden" id="whBatchReturnId" readonly class="form-control" value="${whBatchReturn.id}">
							</div>
							<label class="control-label col-md-3">扫码打码：</label>
							<div class="col-md-8">
								<input class="form-control" type="text" id="scanCode" name="scanCode" onkeypress="if(event.keyCode==13) { scanSku(this); return false;}">
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-3"></label>
							<div class="col-md-8" style="color: grey;">
								说明：补标后的唯一码会直接与退货批次关联，如果提示该SKU不属于本退货批次，可进行SKU补标并走异常返架流程
							</div>
							<div id='exp-setting-contain-div' style="display: none;">
								<div id="exp-setting-contain" style="margin: 20px;">
									<#include "/checkin/expSettingContain.html">
								</div>
							</div>
						</div>
						<br/>
						<br/>
						<br/>
						<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list">
							<colgroup>
								<col width="10%" />
								<col width="25%" />
								<col width="25%" />
								<col width="25%" />
							</colgroup>
							<thead>
								<tr>
									<th>序号</th>
									<th>SKU</th>
									<th>标签</th>
									<th>唯一码</th>
								</tr>
							</thead>
							<tbody id="check_scan_datas">
							</tbody>
						</table>
					</div>
				</form>
			</div>
			<!-- 打印区 -->
			<div class="col-md-2" style="border-left: 1px solid #eee">
				<div class="easyui-panel panel-body" data-options="iconCls:'icon-print'" title="">
					<div id="print_info"></div>

					<iframe style="width:220px;height:400px;border: 0 none;margin-top: 2px;" name="printHtml" id="printHtml"></iframe>

					<div id="idContainer"></div>

					<form action="" method="post" target="_self" id="print_form"></form>

					<!-- 加载打印内容 -->
					<div id="print" style="overflow: auto;max-height: 500px;">

					</div>
				</div>
			</div>
		</div>

	</div>
	<#include "/common/footer.html">
		<img id="enlarge" style='position:absolute;width:80%;height:90%;top:10%;left:15%;display:none;'/>
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.region.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/sku/expSettingCommon.js"></script>
	<script type="text/javascript">
		var num = 0;
        jQuery(document).ready(function() {
            pageInit();
        });

        // 初始化
        function pageInit() {
            $('#scanCode').val('');
            $('#scanCode').focus();
            $('#check_scan_datas').html('');
        }

		function scanSku(obj) {
			var scanCode = $('#scanCode').val();
			if (!scanCode || scanCode.trim() == '') {
				layer.alert("请输入有效SKU!", 'error');
				$('#scanCode').val('');
				$('#scanCode').focus();
				return false;
			}

			if (scanCode.indexOf("=") >= 0) {
				layer.alert("不能扫描唯一码，唯一码请到SKU列表补标!", 'error');
				$('#scanCode').val('');
				$('#scanCode').focus();
				return false;
			}

			$.getJSON(CONTEXT_PATH + "expManage/checkExpSku?scanSku=" + scanCode, function (result) {
				if (result) {
					var html = $('#exp-setting-contain-div').html();
					var index = layer.open({
						type: 1,
						skin: 'layui-layer-rim', //加上边框
						title: '保质期信息',
						area: ['auto', '200px'], //宽高
						shadeClose: false, //开启遮罩关闭
						btn: ['确定'],
						content: html,
						yes: function (index, layero) {
							var days = layero.find("input[name='days']").val();
							var expDate = layero.find("input[name='expDate']").val();
							var proDate = layero.find("input[name='proDate']").val();
							if (days == null || days == '' || expDate == null || expDate == '' || proDate == null || proDate == '') {
								layer.alert("保质期信息不能为空！");
								return false;
							}
							bindingUuid(scanCode, days, proDate, expDate);
							layer.close(index);
						},
						cancel: function () {
							$('#scanCode').val("");
							$('#scanCode').focus();
						}
					});
				} else {
					bindingUuid(scanCode);
				}
			});

		}

		function bindingUuid(scanCode, days, proDate, expDate) {
			var orderNo = $("#orderNo").val();
			if (!orderNo || orderNo.trim() == '') {
				layer.alert("退货批次号不存在", 'error');
				return false;
			}
			var whBatchReturnId = $("#whBatchReturnId").val();

			$.ajax({
				url:CONTEXT_PATH+"whBatchReturn/bindingUuid",
				type:"POST",
				data:{
					days:days,
					proDate:proDate,
					expDate:expDate,
					whBatchReturnId:whBatchReturnId,
					orderNo:orderNo.trim(),
					scanCode:scanCode.trim()
				},
				success : function(response){
					if (response.status == '500') {
						customizeLayer(response.message, 'error');
						$('#scanCode').val('');
						$('#scanCode').focus();
						return;
					} else if (response.status == '200') {
						num += 1;
						var whSku = JSON.parse(response.message);
						var uuid = response.location;
						var html = "<tr style='text-align: center' id='tr_"+ num +"'>";
						html +="<td>" + num + "</td>";
						html +="<td>" + whSku.sku + "</td>";
						html +="<td><div style='width: 13mm;margin-left: 18mm;'>";
						html +="<img src='${CONTEXT_PATH}servlet/qrcode?size=50&keycode=" + uuid + "'>";
						html +="</td>";
						html +="<td>" + uuid + "</td>";
						html +="</tr>" + + "</td>";
						//$("#check_scan_datas").html(html);
						$("#check_scan_datas").append(html);

						// iframe跳转打印页面
						var printPageUrl = '${CONTEXT_PATH}skus/printSkuQRCodeByUuidSku?';
						$('#printHtml').attr('src', printPageUrl + "&" + "scanCode="+uuid);
						//自动打印
						setTimeout(IframeOnloadPrint, 100);
					}
					$('#scanCode').val('');
					$('#scanCode').focus();
				},
				error:function(){
					$('#scanCode').val("");
					$('#scanCode').focus();
					layer.alert('扫描失败，请重新扫描', 'error');
				}
			});
		}

        // 这里Go
        var printed = false;
        function IframeOnloadPrint(){
            var iframe=document.getElementById("printHtml");
            // 加载完iframe后执行
            if (iframe.attachEvent){
                iframe.attachEvent("onload", function(){
                    printed = true;
                    myPrint();
                });
            } else {
                iframe.onload = function(){
                    printed = true;
                    setTimeout(myPrint, 500);/*延时0.5秒打印*/
                    return;
                };
            }

            printed = false;
        }

        /** 打印 **/
        var LODOP; //声明为全局变量
        function myPrint() {
            App.unblockUI();

            //先判断 内页中是否有 打印 方法 有的话直接调用
            try{
                if(typeof(eval(window.frames["printHtml"].myPrint))=='function'){
                    return window.frames["printHtml"].myPrint();
                }
            }catch(e){
            }

            try{
                CreatePrintPage();
                LODOP.PRINT();
            }
            catch(e){

            }
        };

	</script>
</body>
</html>