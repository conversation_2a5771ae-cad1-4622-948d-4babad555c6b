<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		.top_bar{
		    position:fixed;top:0px;
		}
		#task-list td {
			vertical-align:middle;
		}
	</style>
</head>
<body>
<@header method="header" active="10020000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12">
			<ul class="page-breadcrumb breadcrumb">
				<li><a href="#">仓库管理</a> </li>
				<li class="active">海外退件</li>
				<li class="active">新增海外退件</li>
			</ul>
		</div>
	</div>
	<div class="container-fluid">
		<h2 class="header-title">海外退件</h2>
		<#assign whAbroadReturn=domain.whAbroadReturn>
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="col-md-12">
				<form action="${CONTEXT_PATH}warehouse/abroadReturns/create"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="abroadReturnForm" id="domain">
					<div class="form-body">
						<div class="form-group">
							<div class="col-md-3"></div>
							<label class="control-label col-md-1" style="width:130px;">海外退件单号</label>
							<div class="col-md-4">
								<div>
								   <input type="text" name="whAbroadReturn.returnNo" readonly class="form-control"  value="${whAbroadReturn.returnNo}">
								</div>
							</div>
						</div>
						<div class="form-group">
							<div class="col-md-3"></div>
							<label class="control-label col-md-1" style="width:130px;">周转筐</label>
							<div class="col-md-4">
								<div><input id="box_no" type="text" name="whAbroadReturn.boxNo" class="form-control" value="${whAbroadReturn.boxNo}" onkeypress="if(event.keyCode==13) { getBoxNo(this); return false;}" /></div>
							</div>
						</div>
						<div class="form-group">
							<div class="col-md-3"></div>
							<label class="control-label col-md-1" style="width:130px;">扫描唯一码</label>
							<div class="col-md-4">
								<div id="input_sku">
									<input class="form-control" type="text" onkeypress="if(event.keyCode==13) { inputnext(this); return false;}" id="sku" name="sku">
									<input type="hidden" name="notSku">
								</div>
							</div>
						</div>
						<div class="form-group">
							<div class="col-md-3"></div>
							<label class="control-label col-md-1" style="width:130px;"></label>
							<div class="col-md-4">
								<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
									<thead>
										<tr>
											<th>SKU</th>
											<th>库位</th>
											<th>销售属性</th>
											<th>标题</th>
											<th>数量</th>
											<th>删除</th>
										</tr>
									</thead>
									<tbody id="tbody">
									</tbody>
								</table>
							</div>
						</div>
						<div class="form-group">
							<div class="col-md-3"></div>
							<label class="control-label col-md-1" style="width:130px;">备注</label>
							<div class="col-md-4">
                                <div>
                                    <textarea rows="5" wrap="off" class="form-control" name="whAbroadReturn.remark">${whAbroadReturn.remark}</textarea>
                                </div>
							</div>
						</div>
					</div>
					<div class="col-md-offset-1" style="text-align: center">
							<button type="submit" class="btn blue" id="save_abroad_return">
								<i class="icon-search"></i> 保存
							</button>
						</div>
				</form>
			</div>
			<br/>
		</div>
	</div>

	<div id="fixed-bottom">
		<div id="pager"></div>
	</div>
	<#include "/common/footer.html">
</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/packing.js?v=${.now?datetime}"></script>
	<script type="text/javascript">
		var isScan = false;
        var uuIdCacheKey = 'CHECK_RETURN_FOR_UUID_CACHE_KEY_ABROAD';
        $(document).ready(function(){

            initSkuUuIdStorageCache(uuIdCacheKey);
        }); // end ready

		var userForm = $('#domain');
		var url = $("#domain").attr("action");
		userForm.validate({
			rules: {
				"whAbroadReturn.returnNo": {
	            	required: true
	            },
	            "whAbroadReturn.boxNo": {
	            	required: true
	            },
	            "notSku": {
	            	required: true
	            }
	        },
	        submitHandler: function (form) {
			    if($('#tbody').find('tr').length == 0){
					layer.alert('没有要保存的sku','error');
			        return false;
				}
                $('#save_abroad_return').attr("disabled", true);
	        	App.blockUI();

                $.post(url, $('#domain').serialize(), function(data){
                    if(data.status == 500){
						customizeLayer(data.message,'error');
                    }else {
                        alert(data.message);
                    }
                    setTimeout(jump, 2000);
                });
                function jump(){
                    location.href = CONTEXT_PATH + "warehouse/abroadReturns";
                }
	        }
	    });

		function getBoxNo(obj){
			var boxNo = $.trim($(obj).val());
			$.getJSON(CONTEXT_PATH + "warehouse/boxs/boxNo?type=5&boxNo=" + boxNo, function(data){
	    		if (data.status == 200) {
					isScan = true;
					$('#box_no').blur();
					if(confirm("是否使用该周转筐，确认后周转筐将被标记为已使用状态。")){
						$.get(CONTEXT_PATH + "warehouse/boxs/bindingPre/" + boxNo, function(data){
							if(data.status == 200){
								$('#box_no').val(boxNo);
								alert("绑定周转筐成功！");
							}else{
								layer.alert("该周转筐绑定失败", "error");
								$('#box_no').val('');
							}
						});
					}else{
						$('#boxNo').val('');
					}
				} else {
					$(obj).val("");
					layer.alert("该周转框不存在或者已使用！", "error");
				}
	    	});
		}

		function inputnext(obj) {
			var sku_array=new Array();
			$("[id=articlenumber]").each(function(){
				sku_array.push($(this).text());
			  });
			// 检查是否还有输入框未输入值
			var visible_inputs = $('#input_scan').find("input:visible").length;
			for (i = 0; i < visible_inputs; i++) {
				var e = $('#input_scan').find("input:visible").get(i);
				if ($(e).val() == '') {
					$(e).focus();
					return false;
				}
			}

			var val = $('#sku').val();
            var uuid = val.trim();
			if(val.indexOf("=") == -1){
				layer.alert("请输入有效的唯一码!");
				return ;
			}

            if(!(val.indexOf("=") == -1)) {
                val = val.split('=')[0];
            }

			if(!val || val.trim() == ''){
				layer.alert("请输入有效的唯一码!");
				return ;
			}

            // 前端缓存校验是否重复扫描
            if(!checkRetrunUuIdStorageCache(uuIdCacheKey, uuid)){
				layer.alert("唯一码重复扫描！", 'error');
                $('#sku').val("");
                $('#sku').focus();
                return;
            }

			$.ajax({
				url:CONTEXT_PATH+"warehouse/abroadReturns/skuDetail",
				type:"POST",
				data:{
					sku:val.trim(),
                    uuid:uuid
				},
				success:function(result){
					if(result.status == '200'){
                        // 扫描成功唯一码加入缓存
                        addUuIdStorageCache(uuIdCacheKey, uuid);
						var i=sku_array.length;
						var quantity=1;
						var number=$.inArray(result.body.sku.sku, sku_array);
						if(number==-1){
							var option="<tr><td id='articlenumber'>"+result.body.sku.sku+"<input type='text' name='whAbroadReturn.whAbroadReturnItems["+i+"].sku' style='width:60px;display:none' value='"+result.body.sku.sku+"'></td>"+
                                "<td>"+(result.body.sku.locationNumber == null ? "":result.body.sku.locationNumber)+"</td>"+
                                        "<td>"+result.body.sku.saleAttributeSettingStr+"<input type='text' name='whAbroadReturn.whAbroadReturnItems["+i+"].saleAttribute' style='width:60px;display:none' value='"+result.body.sku.saleAttributeSettingStr+"'></td>"+
										"<td>"+result.body.sku.name+"</td>"+
										"<td><input type='text' name='whAbroadReturn.whAbroadReturnItems["+i+"].quantity' style='width:60px;' value='"+quantity+"' onblur='isMinus(this)'></td>"+
										"<td onclick='onRemove(this)'><i class='icon-remove'></i></td>" +
										"<td style='display:none '><input type='text'  name='whAbroadReturn.whAbroadReturnItems["+i+"].uniqueKey' value='"+result.body.uuid+"'></td></tr>";
							$("#tbody").append(option);
						}else{
							$("[id=articlenumber]").each(function(){
								if(result.body.sku.sku==$(this).text()){
									var number=1+parseInt($(this).next().next().next().next().find("input").val());
									$(this).next().next().next().next().find("input").val(number);

									var uuidStr=$(this).next().next().next().next().next().next().find("input").val();
									debugger;
									var unique=uuidStr+","+uuid
									$(this).next().next().next().next().next().next().find("input").val(unique);
								}
							 });
						}
						$("input[name='notSku']").val("1");

                    	var returnNo = $("input[name='whAbroadReturn.returnNo']").val().trim();
                        addWhUniqueSkuLog(uuid, returnNo);

					}else{
                        addWhUniqueSkuLog(uuid, '');
						layer.alert(result.message,'error');
					}
				}
			});
			$('#sku').val("");

		}

        // 添加唯一码海外退件返架日志
        function addWhUniqueSkuLog(uuid, returnNo) {
            var r = $.ajax({
                type : "get",
                url :CONTEXT_PATH+"warehouse/returns/addWhUniqueSkuLog" ,
                data : {uuid : uuid, returnNo: returnNo, step: null},
                timeout : 100000,
                beforeSend : function() {
                },
                success : function(responese) {

                },
                error : function() {
                }
            });
        }

		function isMinus(obj){
			var r = /^\d+$/;
			if(!r.test($(obj).val())){
				$(obj).val("1");
				layer.alert("请填写正整数！", "error");
			}
			if($(obj).val() == 0){
				$(obj).val("1");
				layer.alert("不能填写0-不需要请删除！", "error");
			}
		}

		function onRemove(obj){
			if(confirm("是否删除该条SKU?")){
				$(obj).parent().remove();
				// 重新编排
				$("#tbody tr").each(function(i){
					refreshIndex(i, this);
				});
			}
		}
	</script>
</body>
</html>