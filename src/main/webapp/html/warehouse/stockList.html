<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
#add_modal{
    margin-top:50px;overflow:hidden;
}
.portlet{
    height: 50px;
    margin-top: 10px;
}
.group-inline {
	color: red;
}
#task-list thead>tr>th {
	vertical-align: middle;
}
#fixedDiv thead>tr>th {
	vertical-align: middle;
}
</style>
</head>
<body>
<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">仓库管理</a></li>
					<li class="active">库存记录</li>
				</ul>
			</div>
		</div>

		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">

		<!-- BEGIN PAGE HEADER-->
		<#assign query=domain.query>
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="col-md-12">
				<form action="${CONTEXT_PATH}warehouse/stocks/search"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="checkInForm" id="domain">
					<!-- 分页信息 -->
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-1">SKU</label>
							<div class="col-md-3">
								<input class="form-control" name="query.skuSplit" type="text" value="${query.skuSplit}"  id="uuidSku" onkeypress="if(event.keyCode==13) { inputSku(this); return false;}">
							</div>
							<label class="control-label col-md-1">货位号</label>
							<div class="col-md-3">
								<input class="form-control" name="query.locationNoSplit" type="text" value="${query.locationNoSplit}">
							</div>
							<label class="control-label col-md-1">区域</label>
							<div class="col-md-3">
								<input class="form-control" id="query.area" name="query.area" type="text" value="${query.area}">
							</div>
						</div>
						<div class="form-group">
							
							<label class="control-label col-md-1">通道</label>
							<div class="col-md-3">
								<input class="form-control" id="query.access" name="query.access" type="text" value="${query.access}">
							</div>
							<label class="control-label col-md-1">名称</label>
							<div class="col-md-3">
								<input class="form-control" name="query.skuName" type="text" value="${query.skuName}">
							</div>
                            <label class="control-label col-md-1">是否备货</label>
                            <div class="col-md-3 input-group">
                                <!--是否备货，1表示不备货，0表示备货-->
                                <select name="query.noStockUp" class="form-control" value="${domain.query.noStockUp}" id="sku-noStockUp">
                                    <option value=""></option>
                                    <option value="0">备货</option>
                                    <option value="1">不备货</option>
                                </select>
                            </div>
						</div>
						<div class="form-group">
                            <label class="control-label col-md-1">最新上架日期</label>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <input class="form-control Wdate" type="text" name="query.fromUpTime" placeholder="" readonly="readonly" value="${query.fromUpTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    <span class="input-group-addon">到</span>
                                    <input class="form-control Wdate" type="text" name="query.toUpTime" placeholder="" readonly="readonly" value="${query.toUpTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>
                            <label class="control-label col-md-1">最新移库日期</label>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <input class="form-control Wdate" type="text" name="query.fromMoveLastUpdateTime" placeholder="" readonly="readonly" value="${query.fromMoveLastUpdateTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    <span class="input-group-addon">到</span>
                                    <input class="form-control Wdate" type="text" name="query.toMoveLastUpdateTime" placeholder="" readonly="readonly" value="${query.toMoveLastUpdateTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>
							<label class="control-label col-md-1">是否统计汇总</label>
							<div class="col-md-3">
								<select class="form-control" name="query.isTotal" value="${query.isTotal}">
									<option value="false">否</option>
									<option <#if query.isTotal == true>selected</#if> value="true">是</option>
								</select>
							</div>
						</div>

						<div class="form-group">
                            <label class="control-label col-md-1">库存类型</label>
                            <div class="col-md-3">
                                <input class="form-control" name="query.type" type="text" value="${query.type}">
                            </div>
							<label class="control-label col-md-1">单品状态</label>
							<div class="col-md-3">
								<input class="form-control" type="text" name="query.statusList" placeholder="单品状态" value="${query.statusListStr}">
							</div>
                            <label class="control-label col-md-1 type-label">库存范围</label>
                            <div class="col-md-3 input-group type-label">
                                <input type="number" name="query.thenQuantity" value="${query.thenQuantity }" class="Wdate form-control" digits="true"/>
                                <span class="input-group-addon">到</span>
                                <input type="number" name="query.lessQuantity" value="${query.lessQuantity }" class="Wdate form-control" digits="true"/>
                            </div>
                            <label class="control-label col-md-1 type-label">过滤库存为0</label>
                            <div class="col-md-3 type-label">
                                <input style="margin-top: 10px;" name="query.isFilterZero" type="checkbox" onchange="filterZero(this)" value="${query.isFilterZero}">
								<label style="color: red;">此条件不能和库存范围混合使用！！！</label>
							</div>
							<label class="control-label col-md-1">展示上架/移库日期</label>
							<div class="col-md-3">
								<select class="form-control" name="query.isShowDate" value="${query.isShowDate}">
									<option value="false">否</option>
									<option <#if query.isShowDate == true>selected</#if> value="true">是</option>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1">贵重物品</label>
							<div class="col-md-3 input-group">
								<select class="form-control" name="query.valuables" type="text" value="${query.valuables}">
									<option value=""></option>
                                    <option value="3" <#if query.valuables == 3>selected="selected"</#if> >贵重物品(wms)</option>
									<option value="1" <#if query.valuables == 1>selected="selected"</#if> >贵重物品(产品)</option>
                                    <option value="0" <#if query.valuables == 0>selected="selected"</#if> >否</option>
								</select>
							</div>
							<label class="control-label col-md-1">可用库存范围</label>
							<div class="col-md-3 input-group">
								<input type="number" name="query.surplusThenQuantity" value="${query.surplusThenQuantity }"
									   class="Wdate form-control" digits="true" placeholder="此条件会影响速率,请谨慎使用"/>
								<span class="input-group-addon">到</span>
								<input type="number" name="query.surplusLessQuantity" value="${query.surplusLessQuantity }"
									   class="Wdate form-control" digits="true" placeholder=""/>
							</div>
                            <label class="control-label col-md-1">最近调拨日期</label>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <input class="form-control Wdate" type="text" name="query.fromOutLastTime" placeholder="" readonly="readonly" value="${query.fromOutLastTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    <span class="input-group-addon">到</span>
                                    <input class="form-control Wdate" type="text" name="query.toOutLastTime" placeholder="" readonly="readonly" value="${query.toOutLastTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1">库存id</label>
							<div class="col-md-3">
								<input class="form-control" name="query.stockIdStr" type="text" value="${ query.stockIdStr }"/>
							</div>
							<label class="control-label col-md-1">是否一品多位</label>
							<div class="col-md-3">
								<select name="query.skuMoreLocation" placeholder="是表示一品多位" class="form-control" type="text" value="${query.skuMoreLocation}" >
									<option value=""></option>
									<option value="true" <#if query.skuMoreLocation == 'true'>selected="selected"</#if> >是</option>
								<option value="false" <#if query.skuMoreLocation?? && query.skuMoreLocation == 'false'>selected="selected"</#if> >否</option>
							</select>
							</div>
							<label class="control-label col-md-1">最近入库单上架日期</label>
							<div class="col-md-3">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.fromCheckInUpTime" placeholder="" readonly="readonly" value="${query.fromCheckInUpTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.toCheckInUpTime" placeholder="" readonly="readonly" value="${query.toCheckInUpTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1">库位属性</label>
							<div class="col-md-3">
								<input class="form-control" type="text" name="query.locationTagList" placeholder="库位属性" value="${query.locationTagListStr}">
							</div>
							<label class="control-label col-md-1">组合SKU状态</label>
							<div class="col-md-3">
								<input class="form-control" type="text" name="query.combineSkuStatusList" placeholder="组合SKU状态" value="${query.combineSkuStatusListStr}">
							</div>
							<label class="control-label col-md-1">保质期到期时间</label>
							<div class="col-md-3">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.fromMinExpDate" placeholder="" readonly="readonly" value="${query.fromMinExpDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.toMinExpDate" placeholder="" readonly="readonly" value="${query.toMinExpDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1">货主</label>
							<div class="col-md-3 input-group">
								<select class="form-control" name="query.virtualShippers" type="text" value="${query.virtualShippers}">
									<option value=""></option>
									<option value="1" <#if query.virtualShippers== 1>selected="selected"</#if> >香港易顺达有限公司</option>
									<option value="2" <#if query.virtualShippers== 2>selected="selected"</#if> >香港易通达有限公司</option>
								</select>
							</div>
							<label class="control-label col-md-1">库位数量</label>
							<div class="col-md-3">
								<div class="input-group">
									<input class="form-control number"  type="number" step="1" min="1" name="query.locationCountMin" placeholder="最小库位数量" value="${query.locationCountMin}" onblur="locationCountCheck()">
									<span class="input-group-addon">到</span>
									<input class="form-control number"  type="number" step="1" min="1" name="query.locationCountMax" placeholder="最大库位数量" value="${query.locationCountMax}" onblur="locationCountCheck()">
								</div>
							</div>
						</div>
					</div>
					<div>
						<div class="pull-left" style="margin-bottom: 10px;">
							<@header method="auth" authCode="NEW_INVENTORY_SEARCH_ADD">
							<button type="button" class="btn btn-default" onclick="addBox()">
								添加
							</button>
                            </@header>
							<@header method="auth" authCode="OTHER_SKU_GENERATE_CHECK_NEED">
								<button id="batchGenerateDemand" type="button" class="btn btn-default">
									<span class="icon-plus"></span> 生成盘点需求
								</button>
							</@header>
						</div>
						<div class="col-md-offset-10" style="text-align: right">
							<button type="button" class="btn btn-default" onclick="formReset(this)">
								<i class="icon-refresh"></i> 重置
							</button>
							<button type="submit" class="btn blue">
								<i class="icon-search"></i> 查询
							</button>
                            <@header method="auth" authCode="NEW_INVENTORY_SEARCH_DOWNLOAD">
							<button type="button" class="btn btn-default" onclick="downloadRecords()">
								<i class="icon-download"></i> 导出
							</button>
                            </@header>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>
		
		<div class="row">
			<div id="fixedDiv" class="col-md-12">
				<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
                    <colgroup>
						<col width="4%" />
						<col width="4%" />
						<col width="5%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="3%" />
						<col width="5%" />
						<col width="5%" />
						<col width="2%" />
						<col width="2%" />
						<col width="2%" />
						<col width="2%" />
						<col width="2%" />
						<col width="2%" />
						<col width="3%" />
						<col width="3%" />
						<col width="2%" />
						<col width="2%" />
						<col width="2%" />
						<col width="2%" />
						<col width="2%" />
						<col width="2%" />
						<col width="2%" />
						<col width="2%" />
						<col width="2%" />
						<col width="2%" />
						<col width="2%" />
						<col width="2%" />
						<col width="2%" />
						<col width="2%" />
						<col width="2%" />
						<col width="2%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
                    </colgroup>
					<thead>
					<tr>
						<th>
							<label class="checkbox-inline">
                                编<br/>号
							</label>
						</th>
						<th>SKU</th>
						<th>商品<br/>名称</th>
						<th>单品<br/>状态</th>
						<th>仓库<br/>销售<br/>属性</th>
						<th>销售<br/>频次<br/>（30天）</th>
						<th>动销<br/>频次<br/>（30天）</th>
						<th>货位号</th>
						<th>库位属性<br/>/到期时间</th>
						<th>退货<br/>在途</th>
						<th>在途</th>
						<th>仓库</th>
						<th>待<br/>QC</th>
						<th>待<br/>上架</th>
						<th>上架<br/>中</th>
						<th>总库位<br/>库存</th>
						<th>本地仓<br/>库位</th>
						<th>可用</th>
						<th>已<br/>分配</th>
						<th>外借<br/>占用<br/>分配</th>
						<th>冻结</th>
						<th>已拣</th>
						<th>已拣<br/>返架</th>
						<th>拣货<br/>缺货</th>
						<th>取消</th>
						<th>库存<br/>调拨</th>
						<th>调拨<br/>在途</th>
						<th>已<br/>交运</th>
						<th>已<br/>退货</th>
						<th>外借<br/>在途</th>
						<th>外借<br/>出库</th>
						<th>报废</th>
						<th>不<br/>良品</th>
						<th>最近<br/>调拨时间<br/>/入库<br/>上架时间</th>
						<th>最近<br/>上架人/时间</th>
						<th>最新<br/>移库日期</th>
                    </tr>
					</thead>
				</table>
			</div>
			<div class="col-md-12" id="task-list-warp">
				<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
						<colgroup>
							<col width="4%" />
							<col width="4%" />
							<col width="5%" />
							<col width="3%" />
							<col width="3%" />
							<col width="3%" />
							<col width="3%" />
							<col width="5%" />
							<col width="5%" />
							<col width="2%" />
							<col width="2%" />
							<col width="2%" />
							<col width="2%" />
							<col width="2%" />
							<col width="2%" />
							<col width="3%" />
							<col width="3%" />
							<col width="2%" />
							<col width="2%" />
							<col width="2%" />
							<col width="2%" />
							<col width="2%" />
							<col width="2%" />
							<col width="2%" />
							<col width="2%" />
							<col width="2%" />
							<col width="2%" />
							<col width="2%" />
							<col width="2%" />
							<col width="2%" />
							<col width="2%" />
							<col width="2%" />
							<col width="2%" />
							<col width="5%" />
							<col width="5%" />
							<col width="5%" />
						</colgroup>
						<thead>
							<tr>
                                <th><label class="checkbox-inline"><input type="checkbox" name="checkAll"> 编<br/>号</label></th>
								<th>SKU</th>
								<th>商品<br/>名称</th>
								<th>单品<br/>状态</th>
								<th>仓库<br/>销售<br/>属性</th>
								<th>销售<br/>频次<br/>（30天）</th>
								<th>动销<br/>频次<br/>（30天）</th>
								<th>货位号</th>
								<th>库位属性<br/>/到期时间</th>
								<th>退货<br/>在途</th>
								<th>在途</th>
								<th>仓库</th>
								<th>待<br/>QC</th>
								<th>待<br/>上架</th>
								<th>上架<br/>中</th>
								<th>总库位<br/>库存</th>
								<th>本地仓<br/>库位</th>
								<th>可用</th>
								<th>已<br/>分配</th>
								<th>外借<br/>占用<br/>分配</th>
								<th>冻结</th>
								<th>已拣</th>
								<th>已拣<br/>返架</th>
								<th>拣货<br/>缺货</th>
								<th>取消</th>
								<th>库存<br/>调拨</th>
								<th>调拨<br/>在途</th>
								<th>已<br/>交运</th>
								<th>已<br/>退货</th>
								<th>外借<br/>在途</th>
								<th>外借<br/>出库</th>
								<th>报废</th>
								<th>不<br/>良品</th>
								<th>最近<br/>调拨时间<br/>/入库<br/>上架时间</th>
								<th>最近<br/>上架人/时间</th>
								<th>最新<br/>移库日期</th>
							</tr>
						</thead>
						<tbody>
							<#list domain.whStocks as whStock>
								<tr>
									<td><label class="checkbox-inline"><input class="${whStock.id}" name="query.ids" type="checkbox" value="${whStock.id}"> ${whStock.id} </label></td>
									<td>${whStock.sku}</td>
									<td>${whStock.whSku.name}</td>
									<td>${whStock.whSku.statusName}</td>
									<td>
										<#if whStock.whSku.saleAttributeSettingStr1 == ''>
											长呆滞
											<#else>
											${whStock.whSku.saleAttributeSettingStr1}
										</#if>
									</td>
									<td>${whStock.whSku.thirtyDaysSalesOrders}</td>
									<td>${whStock.whSku.thirtyDaysSalesDays}</td>
									<td>${whStock.locationNumber}</td>
									<td>
										<#if (whStock.locationTag??) && (whStock.locationTag) != ''>
											<span class="label label-sm label-warning">
												${util('enumName', 'com.estone.warehouse.enums.LocationTagEnum', whStock.locationTag)}
											</span>
										</#if>
										<#if (whStock.frozenStock??) && whStock.frozenStock.minExpDate??>
											<br/>
											/${whStock.frozenStock.minExpDate}
										</#if>
									</td>
									<td>${whStock.batchReturnQuantity}</td>
									<td>${whStock.onWayQuantity}</td>
									<td>${whStock.quantity}</td>
									<td>${whStock.qcQuantity}</td>
									<td>${whStock.waitingUpQuantity}</td>
									<td>${whStock.upQuantity}</td>
									<td>${whStock.totalLocationQuantity}</td>
									<td>${whStock.locationQuantity}</td>
									<td>${whStock.surplusQuantity}</td>
									<td>${whStock.allotQuantity}</td>
									<td>${whStock.lendAllotQuantity}</td>
									<td>
										<#if whStock.frozenQuantity??>
											${whStock.frozenQuantity}
											<#else>
												0
										</#if>
									</td>
									<td>${whStock.pickQuantity}</td>
									<td>${whStock.pickReturnQuantity}</td>
									<td>${whStock.pickNotQuantity}</td>
									<td>${whStock.cancelQuantity}</td>
									<td>${whStock.allocationQuantity}</td>
									<td>${whStock.allocationOnWayQuantity}</td>
									<td>${whStock.deliverQuantity}</td>
									<td>${whStock.returnQuantity}</td>
									<td>${whStock.lendOnwayQuantity}</td>
									<td>${whStock.lendQuantity}</td>
									<td>${whStock.scrapQuantity}</td>
									<td>${whStock.badProductQuantity}</td>
									<td>${whStock.whAllocateRecord.creationDate}<br/>/${whStock.frozenStock.checkInUpTime }</td>
									<td>${util('name',whStock.frozenStock.lastUpUser)}<br/>${whStock.frozenStock.lastUpTime }</td>
									<td>${util('name',whStock.locationMoveInfo.userId)}<br/>${whStock.locationMoveInfo.lastUpdateDate }</td>
								</tr>
							</#list>
							<tr>
								<td colspan="9"><span class="group-inline">汇总：</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupBatchReturnQuantity }</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupOnWayQuantity }</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupQuantity}</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupWaitQcQuantity }</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupWaitUpQuantity }</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupUpingQuantity }</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupTotalLocationQuantity}</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupLocationQuantity}</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupSurplusQuantity }</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupAllotQuantity}</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupLendAllotQuantity}</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupFrozenQuantity}</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupPickQuantity }</span></td>

								<td><span class="group-inline">${domain.resultStockGroup.groupPickReturnQuantity }</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupPickNotQuantity }</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupCancelQuantity }</span></td>

								<td><span class="group-inline">${domain.resultStockGroup.groupAllocationQuantity }</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupAllocationOnWayQuantity }</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupDeliverQuantity }</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupReturnQuantity }</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupLendOnwayQuantity }</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupLendQuantity }</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupScrapQuantity }</span></td>
								<td><span class="group-inline">${domain.resultStockGroup.groupBadProductQuantity }</span></td>
								<td></td>
								<td></td>
								<td></td>
							</tr>
						</tbody>
					</table>
			</div>
		</div>
		

		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	</div>

	<!-- 弹窗 -->
	<div class="modal fade" id="add_modal" tabindex="-1" role="dialog" aria-labelledby="organization-add-label" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
					<h4 class="modal-title" id="expert-modal-label">新增</h4>
				</div>
				<div class="modal-body form-horizontal portlet">
					<div class="form-group">
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12">
							<div class="line-height-label">
								<div class="col-md-3">
									<label class="control-label col-md-11">sku<span class="required">*</span></label>
								</div>
								<div class="col-md-7">
									<input id="add_sku_input" type="text" placeholder="仅支持单个SKU添加" value=""/>
									<#--<textarea class="form-control description" id="add_sku_input" rows="10" placeholder="SKU支持逗号拼接"></textarea>-->
								</div>
								<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
							</div>
						</div>
					</div>
					<div class="form-group">
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12">
							<div class="line-height-label">
								<div class="col-md-3">
									<label class="control-label col-md-11">库位<span class="required">*</span></label>
								</div>
								<div class="col-md-7">
									<input id="add_location" type="text"  value=""/>
								</div>
								<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
							</div>
						</div>
						<div class="col-md-11 br-div"></div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" onclick="saveBox()">保存</button>
					<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
				</div>
			</div>
		</div>
	</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/access.js?v=${.now?datetime}"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery-ui/jquery-ui-1.10.2.custom.min.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
	<script type="text/javascript">

        // 类型
        var typeArray = ${domain.typeList};
        $("input[name='query.type']").select2({
            data: typeArray,
            placeholder: "库位",
            multiple: false,
            allowClear: true
        });

        if ($("input[name='query.type']").val()) {
            $(".type-label").show();
        } else {
            $(".type-label").hide();
        }

        $("input[name='query.type']").change(function() {
            if ($(this).val()) {
                $(".type-label").show();
                if ($("input[name='query.type']").val()=='19'){

				}
            } else {
                $(".type-label").hide();
            }
        })

        $("#sku-noStockUp").val("${domain.query.noStockUp}");
		// 全选
		var checkAll = $("#task-list").find("input[name='checkAll']");
		// 子选项
		var recordIds = $("#task-list").find("input[name='query.ids']");
		
		checkAll.change(
		  function () {
			  recordIds.prop("checked", $(this).prop("checked"));
			  
			  recordIds.each(function(){
				  var f = $(this).is(":checked");
					var checkClass = $(this).prop("class");
					$("." + checkClass).each(function(){
						$(this).prop("checked",f);
					})
			  })
		  }
		);
		
		recordIds.change(function(){
			var checkedLenght = recordIds.filter(":checked").length;
			var length = recordIds.length;
			checkAll.prop("checked", checkedLenght == length);
		});
	
	
		// 分页
		var total = "${domain.page.totalCount}";
		var pageNo = "${domain.page.pageNo}";
		var pageSize = "${domain.page.pageSize}";
		$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

        var heights = $("body").height();
        if(heights>910){
            $("#fixed-bottom").addClass("fixed-bottom-height")
        }

		function downloadRecords() {
			let uuid = getFileQueueUUID();

			var diglog = dialog({
				title: '导出库存',
				width: 800,
				height:400,
				url: CONTEXT_PATH + "warehouse/stocks/downloadmode?type=2&uuid=" + uuid,
				okValue: '确定',
				ok: function () {
					var exportWindow = $(this.iframeNode.contentWindow.document.body);

					var submitForm = exportWindow.find("#submit-form");

					var exportType = submitForm.find("input[name='exportType']:checked").val();

					var headers =  submitForm.find("input[name='selectHeaders']:checked");

					var submitFormParam = $('#domain').serialize();
					// 导出当前选择
					if(exportType == 3) {
						var recordIds = $("#task-list").find("input[name='query.ids']:checked");
						if(recordIds.length == 0) {
							layer.alert("请选择要操作的数据");
							return false;
						}else if (recordIds.length > 300) {
							layer.alert("选择数量不能超过300!");
							return false;
						}
						submitFormParam = submitFormParam + "&" +recordIds.serialize();
					}

					if(headers.length == 0) {
						layer.alert("请选择要导出的表头字段");
						return false;
					}
					var headersStr = headersJoin(headers);
					submitFormParam = submitFormParam +"&selectHeaders="+headersStr+"&exportType="+exportType;
					//还原分页
					$("#page-no").val("${domain.page.pageNo}");

					$.post(CONTEXT_PATH + "warehouse/stocks/download", submitFormParam, function(data){
						if (data.status == 200) {
							if (data.message==null || data.message==''){
								layer.alert('成功',function (index) {
									layer.close(index);
									diglog.close().remove();
									location.reload();
								});
							}else{
								customizeLayer(data.message);
							}
						} else {
							customizeLayer(data.message);
						}
					});

					$("#page-no").val("1");

					return true;
				},
				cancelValue: '取消',
				cancel: function () {}
			});
			diglog.show();
		}

        function headersJoin(headers) {
            var checkHeaders = "";
            for (var i = 0; i < headers.length; i++) {
                var header = headers[i].value;
                if (i == 0) {
                    checkHeaders += header;
                } else {
                    checkHeaders += "," + header;
                }
            }
            return checkHeaders;
        }
		function postExcelFile(params, url) {
            downloadByPostForm(params, url);
			/*var form = document.createElement("form");
			form.style.display = 'none';
			form.action = url + params;
			form.method = "post";
			document.body.appendChild(form);
			form.submit();
			form.remove();*/
		}

		var locationRegionList =  ${domain.locationRegionList};
        $("input[name='query.area']").select2({
			data : locationRegionList,
			placeholder : "区域",
			multiple: true,
			allowClear : true
		});
        
        var locationAisleList =  ${domain.locationAisleList};
        $("input[name='query.access']").select2({
			data : locationAisleList,
			placeholder : "通道",
			multiple: true,
			allowClear : true
		});

        // 单品状态
		var statusArray =  ${domain.skuStatuses};
		$("input[name='query.statusList']").select2({
			data : statusArray,
			placeholder : "单品状态",
			multiple: true,
			allowClear : true
		});

		// 组合SKU状态
		var combineSkuStatusArray =  ${domain.combineSkuStatuses};
		$("input[name='query.combineSkuStatusList']").select2({
			data : combineSkuStatusArray,
			placeholder : "组合SKU状态",
			multiple: true,
			allowClear : true
		});

		// 库位属性
		var locationTagArray =  ${domain.locationTags};
		$("input[name='query.locationTagList']").select2({
			data : locationTagArray,
			placeholder : "库位属性",
			multiple: true,
			allowClear : true
		});

        function addBox(){
            $("#add_modal").modal('show');
            setTimeout( function(){
                $("#add_modal").draggable({
                    handle: ".modal-header"
                });
            }, 200 );
        }

        function saveBox(){

			var sku = $.trim($("#add_sku_input").val());
			var location = $.trim($("#add_location").val());
            if(!sku){
                layer.alert('请输入SKU！','error');
                return false;
			}
			if(sku.indexOf(",") != -1 || sku.indexOf("，") != -1){
                layer.alert('只支持添加单个SKU,请输入正确的SKU！','error');
                return false;
			}
			if(!location){
				layer.alert('请输入库位！','error');
				return false;
			}
			if(location.indexOf(",") != -1 || location.indexOf("，") != -1){
				layer.alert('只支持添加单个库位！','error');
				return false;
			}
            $.getJSON(CONTEXT_PATH + "warehouse/stocks/add?sku=" + sku+"&location="+ location, function(data) {
                if(data.status == "200"){
					$("#add_sku_input").val('');
					$("#add_location").val('');
					$("#add_modal").modal('hide');
					alert("添加成功！");
				}else {
                    customizeLayer(data.message,'error');
                    $("#add_sku_input").val('');
					$("#add_location").val('');
                    $("#add_sku_input").focus();
				}
                setTimeout(function() {
                    location.reload();
                }, 1000);
            });
		}
        
        var isFilterZero = "${domain.query.isFilterZero}";
        if (isFilterZero == "true") {
        	$("input[name='query.isFilterZero']").attr('checked', true);
        }
        function filterZero(obj) {
        	if ($(obj).is(':checked')) {
        		$(obj).val(true);
        	} else {
        		$(obj).val(false);
        	}
        }

		$('#batchGenerateDemand').on('click',function () {
			//3.设置提交按钮失效，以实现防止按钮重复点击
			$('#batchGenerateDemand').attr("disabled", true);

			var outIds = $("#task-list").find("input[name='query.ids']:checked");
			if(outIds.length == 0) {
				layer.alert("请先勾选需要生成盘点需求的条目", "error");
				$('#batchGenerateDemand').removeAttr('disabled');
				return;
			}
			if(outIds.length > 100) {
				layer.alert("勾选的条目不能超过100个", "error");
				$('#batchGenerateDemand').removeAttr('disabled');
				return;
			}

			var checkIds = "";
			for (var i = 0; i < outIds.length; i++) {
				var outId = outIds[i].value;
				if (i == 0) {
					checkIds += outId;
				} else {
					checkIds += "," + outId;
				}
			}

			$.ajax({
				url:CONTEXT_PATH +"warehouse/pickInventoryDemands/batchGenerateDemand",
				type:"POST",
				data:{
					"ids":checkIds
				},
				success: function(response) {
					if (response.status == '500') {
						customizeLayer(response.message, 'error');
					} else{
						layer.alert('操作成功！');
						setTimeout(function() {
							window.location.reload();
						}, 2000);
					}
				}
			});

			setTimeout(function() {
				//提交完成后按钮重新设置有效
				$('#batchGenerateDemand').removeAttr('disabled');
			}, 1500);
		});

		function locationCountCheck(){
			let min = $("input[name='query.locationCountMin']").val();
			let max = $("input[name='query.locationCountMax']").val();
			if (min != undefined && min != '') {
				let intMin = parseInt(min);
				if (intMin <= 0) {
					layer.alert("最小库位数量不能为负数!");
					$("input[name='query.locationCountMin']").val("")
					return;
				}
			}
			if (max != undefined && max != '') {
				let intMax = parseInt(max);
				if (intMax <= 0) {
					layer.alert("最大库位数量不能为负数!");
					$("input[name='query.locationCountMax']").val("")
					return;
				}
			}
			if (min != undefined && min != '' && max != undefined && max != '') {
				let intMin = parseInt(min);
				let intMax = parseInt(max);
				if (intMin > intMax) {
					layer.alert("最小库位数量不能大于最大库位数量!");
					$("input[name='query.locationCountMin']").val("")
					$("input[name='query.locationCountMax']").val("")
					return;
				}
			}
		}
	</script>
</body>
</html>