<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
    <head>
        <meta charset="UTF-8">
        <title>易世通达仓库管理系统</title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <#include "/common/include.html">
        <link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet"
              type="text/css"/>
        <style type="text/css">
            .modal-body input {
                width: 240px;
            }

            .table-top {
                margin-bottom: 0px;
                margin-top: 20px;
            }

            #item-list {
                margin-bottom: 80px;
            }

            .pick-content-button {
                margin-top: 20px;
            }

            .rule-pick-label input {
                width: 40px;
            }

            .red-flag {
                color: red;
            }
        </style>
    </head>
    <body>
        <@header method="header" active="14020000" ><#include "/ftl/header.ftl"></@header>
        <div id="page" style="background-color: rgb(231, 237, 248)">
            <div class="row">
                <div class="col-md-12" style="padding: 0">
                    <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                        <li><a href="#">存货管理</a></li>
                        <li class="active">存货迁移规则</li>
                    </ul>
                </div>
            </div>
            <!-- 内容 -->
            <div class="container-fluid" style="background-color: white;border: none">
                <div class="row">
                    <form class="form-horizontal form-bordered form-row-stripped" id="domain"
                          action="${CONTEXT_PATH}prestorageMigration/rule/create" method="post">
                        <#assign item=domain.migrationRuleVO>
                        <input type="hidden" value="${item.id}" id="id"/>
                        <div style="width: 28%;height: 650px;margin: 0 auto;">
                            <div class="col-md-12">
                                <div class="form-group" style="width:550px ">
                                    <label class="control-label col-md-3">
                                        <strong style="float:left;">仓库:</strong>
                                    </label>
                                    <div class="col-md-1" style="width:250px ">
                                        <#if item.ruleType != 2>
                                            <select class="form-control" id="rule.warehouseType" name="rule.warehouseType">
                                                <option value="1" <#if item.warehouseType == 1>selected="selected" </#if>>
                                                    本地仓
                                                </option>
                                                <option value="2" <#if item.warehouseType == 2>selected="selected" </#if>>
                                                    中转仓
                                                </option>
                                            </select>
                                        <!-- 当为全部sku时，不进可以进行选择 -->
                                        <#else>
                                            <select class="form-control" id="rule.warehouseType" name="rule.warehouseType">
                                                <#if item.warehouseType == 1>
                                                <option value="1" selected="selected">
                                                    本地仓
                                                </option>
                                                </#if>
                                                <#if item.warehouseType == 2>
                                                <option value="2" selected="selected">
                                                    中转仓
                                                </option>
                                                </#if>
                                            </select>
                                        </#if>
                                    </div>
                                </div>
                                <div class="form-group" style="width:550px ">
                                    <label class="control-label col-md-3">
                                        <strong style="float:left;">规则类型</strong>
                                    </label>
                                    <div class="col-md-1" style="width:250px ">
                                        <#if item.ruleType != 2>
                                            <select class="form-control" onchange="selectRuleType(this);"
                                                    id="rule.ruleType" name="rule.ruleType">
                                                <option value="1" <#if item.ruleType == 1> selected="selected"</#if>>
                                                    根据条件判断SKU
                                                </option>
                                                <option value="0" <#if item.ruleType == 0> selected="selected"</#if>>指定sku
                                                </option>
                                            </select>
                                        <!-- 当为全部sku时，不进可以进行选择 -->
                                        <#else>
                                            <select class="form-control" id="rule.ruleType" name="rule.ruleType">
                                                <option value="2"  selected="selected">
                                                    全部sku
                                                </option>
                                            </select>
                                        </#if>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12" id="rule-content">
                                <#if item.ruleType == 0>
                                    <div class="form-group" style="width:550px ">
                                        <div class="col-md-8" style="margin-top: 5px">
                                            <input type="text" id="skuStrs" placeholder="请输入SKU，多个SKU请以英文,号隔开"
                                                   class="form-control input-large input-inline">
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn blue" onclick="searchSkus();return false;">添加调拨sku
                                            </button>
                                        </div>
                                    </div>
                                    <div class="form-group" style="width:550px ">
                                        <div class="col-md-3">
                                       <span class="btn btn-primary fileinput-button">
                                            <span class="icon-upload"> 导入Sku</span>
                                            <input type="file" name="file" onchange="importSkus(this);"/>
                                       </span>
                                        </div>
                                        <div class="col-md-3" style="margin-top: 15px">
                                            <a class="blue"
                                               href="${CONTEXT_PATH}file/execl/prestorage_migration_rule_import_sku.xlsx">下载导入模板EXCEL</a>
                                        </div>
                                    </div>
                                    <div class="form-group" style="width:550px ">
                                        <table class="table table-striped table-bordered table-hover table-condensed mt10">
                                            <colgroup>
                                                <col width="10%"/>
                                                <col width="25%"/>
                                                <col width="25%"/>
                                                <col width="20%"/>
                                                <col width="20%"/>
                                                <col width="20%"/>
                                                <col width="20%"/>
                                                <col width="10%"/>
                                            </colgroup>
                                            <thead>
                                            <tr>
                                                <th>SKU</th>
                                                <th>名称</th>
                                                <th>销售属性</th>
                                                <th>单品状态</th>
                                                <th>7天销量</th>
                                                <th>30天销量</th>
                                                <th>90天销量</th>
                                                <th>操作</th>
                                            </tr>
                                            </thead>
                                            <tbody id="skus">
                                            <#list domain.whSkus as whSku>
                                                <tr id="${whSku.sku}">
                                                    <td name="sku">${whSku.sku}</td>
                                                    <td>${whSku.name}</td>
                                                    <#if whSku.saleAttributeSettingStr??>
                                                        <td>长呆滞</td>
                                                    <#else>
                                                        <td>${whSku.saleAttributeSettingStr}</td>
                                                    </#if>
                                                    <td>${whSku.statusName}</td>
                                                    <td>${whSku.sevenDaysSaleQuantity}</td>
                                                    <td>${whSku.thirtyDaysSaleQuantity}</td>
                                                    <td>${whSku.ninetyDaysSaleQuantity}</td>
                                                    <td>
                                                        <button class="btn blue"
                                                                onclick="removeSku('${whSku.sku}');return false;">删除
                                                        </button>
                                                    </td>
                                                </tr>;
                                            </#list>
                                            </tbody>
                                        </table>
                                    </div>
                                <#else>
                                    <#assign index=0>
                                    <#--销售属性-->
                                    <#if item.saleAttribute.saleAttributeStr??>
                                        <div class="form-group" style="width:550px " id="item-list_${index}">
                                            <label class="control-label col-md-3">
                                                <strong style="float:left;">条件${index + 1}：</strong>
                                            </label>
                                            <div class="col-md-1" style="width:250px ">
                                                <select class="form-control" name="condition"
                                                        onchange="selectCondition(this,'item-list_${index}')">
                                                    <option value="销售属性" selected="selected">销售属性</option>
                                                    <option value="销量">销量</option>
                                                    <option value="库区">库区</option>
                                                </select>
                                            </div>
                                            <div class="col-md-1">
                                                <#if index == 0>
                                                    <button class="btn blue" onclick="addConditionRule();return false;">
                                                        添加条件
                                                    </button>
                                                <#else>
                                                    <button class="btn blue"
                                                            onclick="removeConditionRule('item-list_${index}');return false;">
                                                        删除条件
                                                    </button>
                                                </#if>
                                            </div>
                                            <div class="form-group" name="selectContent">
                                                <label class="control-label col-md-3">销售属性包含：</label>
                                                <div class="col-md-7" style="margin-top: 10px">
                                                    <input type="text" name="saleAttribute"
                                                           value="${item.saleAttribute.saleAttributeStr}"
                                                           class="form-control input-large input-inline">
                                                </div>
                                            </div>
                                        </div>
                                        <#assign index = index+1>
                                    </#if>
                                    <#--库区-->
                                    <#if item.region.regionStr??>
                                        <div class="form-group" style="width:550px " id="item-list_${index}">
                                            <label class="control-label col-md-3">
                                                <strong style="float:left;">条件${index + 1}：</strong>
                                            </label>
                                            <div class="col-md-1" style="width:250px ">
                                                <select class="form-control" name="condition"
                                                        onchange="selectCondition(this,'item-list_${index}')">
                                                    <option value="销售属性">销售属性</option>
                                                    <option value="销量">销量</option>
                                                    <option value="库区" selected="selected">库区</option>
                                                </select>
                                            </div>
                                            <div class="col-md-1">
                                                <#if index == 0>
                                                    <button class="btn blue" onclick="addConditionRule();return false;">
                                                        添加条件
                                                    </button>
                                                <#else>
                                                    <button class="btn blue"
                                                            onclick="removeConditionRule('item-list_${index}');return false;">
                                                        删除条件
                                                    </button>
                                                </#if>
                                            </div>
                                            <div class="form-group" name="selectContent">
                                                <label class="control-label col-md-3">销售库区包含：</label>
                                                <div class="col-md-7" style="margin-top: 10px">
                                                    <input type="text" name="saleRegion"
                                                           value="${item.region.regionStr}"
                                                           class="form-control input-large input-inline">
                                                </div>
                                            </div>
                                        </div>
                                        <#assign index = index+1>
                                    </#if>
                                    <#--销量-->
                                    <#if item.skuSaleAmounts??>
                                        <div class="form-group" style="width:550px " id="item-list_${index}">
                                            <label class="control-label col-md-3">
                                                <strong style="float:left;">条件${index + 1}：</strong>
                                            </label>
                                            <div class="col-md-1" style="width:250px ">
                                                <select class="form-control" name="condition"
                                                        onchange="selectCondition(this,'item-list_${index}')">
                                                    <option value="销售属性">销售属性</option>
                                                    <option value="销量" selected="selected">销量</option>
                                                    <option value="库区">库区</option>
                                                </select>
                                            </div>
                                            <div class="col-md-1">
                                                <#if index == 0>
                                                    <button class="btn blue" onclick="addConditionRule();return false;">
                                                        添加条件
                                                    </button>
                                                <#else>
                                                    <button class="btn blue"
                                                            onclick="removeConditionRule('item-list_${index}');return false;">
                                                        删除条件
                                                    </button>
                                                </#if>
                                            </div>
                                            <div class="form-group" name="selectContent">
                                                <#assign amountIndex = 0>
                                                <#list item.skuSaleAmounts as skuSaleAmount>
                                                    <div id="saleAmount_${amountIndex}" class="col-md-10">
                                                        <label class="control-label col-md-3">销量</label>
                                                        <div class="col-md-1" style="width:75px ">
                                                            <select class="form-control" name="saleDays">
                                                                <option value="7"
                                                                        <#if skuSaleAmount.saleDays == 7>selected="selected"</#if>>
                                                                    7天
                                                                </option>
                                                                <option value="30"
                                                                        <#if skuSaleAmount.saleDays == 30>selected="selected"</#if>>
                                                                    30天
                                                                </option>
                                                                <option value="90"
                                                                        <#if skuSaleAmount.saleDays == 90>selected="selected"</#if>>
                                                                    90天
                                                                </option>
                                                            </select>
                                                        </div>
                                                        <div class="col-md-1" style="width:75px ">
                                                            <select class="form-control" name="compareSignal">
                                                                <option value=">" <#if skuSaleAmount.compareSignal == '>'> selected="selected"</#if>>
                                                                    >
                                                                </option>
                                                                <option value="<" <#if skuSaleAmount.compareSignal == '<'> selected="selected"</#if>>
                                                                    <
                                                                </option>
                                                                <option value=">=" <#if skuSaleAmount.compareSignal == '>='> selected="selected"</#if>>
                                                                    >=
                                                                </option>
                                                                <option value="<=" <#if skuSaleAmount.compareSignal == '<='> selected="selected"</#if>>
                                                                    <=
                                                                </option>
                                                            </select>
                                                        </div>
                                                        <div class="col-md-2" style="width:75px ">
                                                            <input type="number" min="1" step="1"
                                                                   value="${skuSaleAmount.amount}"
                                                                   onkeypress="return (/\d/.test(String.fromCharCode(event.keyCode)));"
                                                                   name="amount" class="form-control input-inline">
                                                        </div>
                                                        <div class="col-md-1">
                                                            <#if amountIndex == 0>
                                                                <button class="btn blue"
                                                                        onclick="addSaleCondition('item-list_${index}');return false;">
                                                                    +
                                                                </button>
                                                            <#else>
                                                                <button class="btn blue"
                                                                        onclick="removeSaleCondition('item-list_${index}','saleAmount_${amountIndex}');return false;">
                                                                    -
                                                                </button>
                                                            </#if>
                                                        </div>
                                                    </div>
                                                    <#assign amountIndex = amountIndex+1>
                                                </#list>
                                            </div>
                                        </div>
                                    </#if>
                                </#if>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group" style="width:550px ">
                                    <label class="control-label col-md-3">
                                        <strong style="float:left;">规则优先级：</strong>
                                    </label>
                                    <div class="col-md-5" style="margin-top: 10px">
                                        <input type="number" min="1" step="1" value="${item.priority}"
                                               onkeypress="return (/\d/.test(String.fromCharCode(event.keyCode)));"
                                               id="rule.priority" name="rule.priority"
                                               class="form-control input-large input-inline">
                                    </div>
                                </div>
                                <div class="form-group" style="width:550px ">
                                    <label class="control-label col-md-3">
                                        <strong style="float:left;">拣货库存水位值：</strong>
                                    </label>
                                    <div class="col-md-5" style="margin-top: 10px">
                                        <input type="number" min="1" step="1" value="${item.pickingStockThreshold}"
                                               onkeypress="return (/\d/.test(String.fromCharCode(event.keyCode)));"
                                               id="rule.pickingStockThreshold" name="rule.pickingStockThreshold"
                                               class="form-control input-large input-inline">
                                    </div>
                                </div>
                                <div class="form-group" style="width: 75px;margin: 0 auto;">
                                    <div class="col-md-2">
                                        <button class="btn blue" onclick="submitData();return false;">提交</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <#include "/common/footer.html">
        <script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
        <script type="text/javascript" src="${CONTEXT_PATH}js/jquery.region.js"></script>
        <script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
        <script type="text/javascript" src="${CONTEXT_PATH}js/prompt-message.js"></script>
        <script type="text/javascript">
            // 初始化操作
            $(document).ready(function () {
                initSaleAttribute();
                initSaleRegion();
            });

            // 用于变更规则类型
            function selectRuleType(obj) {
                var ruleType = $(obj).find("option:selected").val();
                var content = ``;
                // 根据条件判断sku
                if (ruleType == "1") {
                    content = `<div class="form-group" style="width:550px " id="item-list_0">
                                    <label class="control-label col-md-3">
                                        <strong style="float:left;">条件1：</strong>
                                    </label>
                                    <div class="col-md-1" style="width:250px ">
                                        <select class="form-control" name="condition" onchange="selectCondition(this,'item-list_0')">
                                            <option value="销售属性" selected="selected">销售属性</option>
                                            <option value="销量">销量</option>
                                            <option value="库区">库区</option>
                                        </select>
                                    </div>
                                    <div class="col-md-1">
                                        <button class="btn blue" onclick="addConditionRule();return false;">添加条件
                                        </button>
                                    </div>
                                    <div class="form-group" name="selectContent">
                                        <label class="control-label col-md-3">销售属性包含：</label>
                                        <div class="col-md-5" style="margin-top: 10px">
                                            <input type="text" name="saleAttribute"
                                                   class="form-control input-large input-inline">
                                        </div>
                                    </div>
                                </div>`;
                    $("#rule-content").html(content);
                    initSaleAttribute();
                }
                // 指定sku
                if (ruleType == "0") {
                    content = `<div class="form-group" style="width:550px ">
                                    <div class="col-md-8" style="margin-top: 5px">
                                        <input type="text" id="skuStrs" placeholder="请输入SKU，多个SKU请以英文,号隔开"
                                                class="form-control input-large input-inline">
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn blue" onclick="searchSkus();return false;">添加调拨sku</button>
                                    </div>
                               </div>
                               <div class="form-group" style="width:550px ">
                                    <div class="col-md-3">
                                       <span class="btn btn-primary fileinput-button">
                                            <span class="icon-upload"> 导入Sku</span>
                                            <input type="file" name="file" onchange="importSkus(this);" />
                                       </span>
                                    </div>
                                    <div class="col-md-3" style="margin-top: 15px">
                                        <a class="blue" href="`+${CONTEXT_PATH}+
                    `file/execl/prestorage_migration_rule_import_sku.xlsx">下载导入模板EXCEL</a>
                                    </div>
                               </div>
                               <div class="form-group" style="width:550px ">
                                    <table class="table table-striped table-bordered table-hover table-condensed mt10">
                                        <colgroup>
                                            <col width="10%" />
                                            <col width="25%" />
                                            <col width="25%" />
                                            <col width="20%" />
                                            <col width="20%" />
                                            <col width="20%" />
                                            <col width="20%" />
                                            <col width="10%" />
                                        </colgroup>
                                        <thead>
                                            <tr>
                                                <th>SKU</th>
                                                <th>名称</th>
                                                <th>销售属性</th>
                                                <th>单品状态</th>
                                                <th>7天销量</th>
                                                <th>30天销量</th>
                                                <th>90天销量</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="skus">
                                        </tbody>
                                    </table>
                               </div>
                               `;
                    $("#rule-content").html(content);
                }
            }

            // 用于在根据条件判断sku的情况下添加条件
            function addConditionRule() {
                var lastChildId = $("#rule-content").children(":last-child").attr("id");
                var lastIndex = lastChildId.substr(lastChildId.indexOf("_") + 1);
                var index = parseInt(lastIndex) + 1;
                var content = `<div class="form-group" style="width:550px " id="item-list_` + index + `">
                                    <label class="control-label col-md-3">
                                        <strong style="float:left;">条件` + (index + 1) + `：</strong>
                                    </label>
                                    <div class="col-md-1" style="width:250px ">
                                        <select class="form-control" name="condition" onchange="selectCondition(this,'item-list_` + index + `')">
                                            <option value="销售属性" selected="selected">销售属性</option>
                                            <option value="销量">销量</option>
                                            <option value="库区">库区</option>
                                        </select>
                                    </div>
                                    <div class="col-md-1">
                                        <button class="btn blue" onclick="removeConditionRule('item-list_` + index + `');return false;">删除条件</button>
                                    </div>
                                    <div class="form-group" name="selectContent">
                                        <label class="control-label col-md-3">销售属性包含：</label>
                                        <div class="col-md-7" style="margin-top: 10px">
                                            <input type="text" name="saleAttribute"
                                                   class="form-control input-large input-inline">
                                        </div>
                                    </div>
                                </div>`;

                $("#rule-content").append(content);
                initSaleAttribute();
            }

            // 删除当前所添加的条件
            function removeConditionRule(obj) {
                $("#" + obj).remove();
            }

            // 用于变更条件的筛选情况
            function selectCondition(obj, itemId) {
                var condition = $(obj).find("option:selected").val();
                var content = ``;
                if (condition == '销售属性') {
                    var content = `<label class="control-label col-md-3">销售属性包含：</label>
                                        <div class="col-md-7" style="margin-top: 10px">
                                            <input type="text" name="saleAttribute"
                                                   class="form-control input-large input-inline">
                                        </div>`;
                    $("#" + itemId + " > div[name='selectContent']").html(content);
                    initSaleAttribute();
                }
                if (condition == '销量') {
                    var content = `<div id="saleAmount_0" class="col-md-10">
                                        <label class="control-label col-md-3">销量</label>
                                        <div class="col-md-1" style="width:75px ">
                                            <select class="form-control" name="saleDays">
                                                <option value="7" selected="selected">7天</option>
                                                <option value="30">30天</option>
                                                <option value="90">90天</option>
                                            </select>
                                        </div>
                                        <div class="col-md-1" style="width:75px ">
                                            <select class="form-control" name="compareSignal">
                                                <option value="\>" selected="selected">\></option>
                                                <option value="\<">\<</option>
                                                <option value="\>=">\>=</option>
                                                <option value="\<=">\<=</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2" style="width:75px ">
                                            <input type="number" min="1" step="1"
                                                   onkeypress="return (/\\d/.test(String.fromCharCode(event.keyCode)));"
                                                   name="amount" class="form-control input-inline">
                                        </div>
                                        <div class="col-md-1">
                                            <button class="btn blue" onclick="addSaleCondition('` + itemId + `');return false;">+
                                            </button>
                                        </div>
                                    </div>`;
                    $("#" + itemId + " > div[name='selectContent']").html(content);

                }
                if (condition == '库区') {
                    var content = `<label class="control-label col-md-3">销售库区包含：</label>
                                        <div class="col-md-7" style="margin-top: 10px">
                                            <input type="text" name="saleRegion"
                                                   class="form-control input-large input-inline">
                                        </div>`;
                    $("#" + itemId + " > div[name='selectContent']").html(content);
                    initSaleRegion();
                }
            }


            // 用于添加销量数据
            function addSaleCondition(itemId) {
                var div = $("#" + itemId + " > div[name='selectContent']");
                var lastChildId = div.children(":last-child").attr("id");
                var lastIndex = lastChildId.substr(lastChildId.indexOf("_") + 1);
                var index = parseInt(lastIndex) + 1;

                var content = `<div id="saleAmount_` + index + `" class="col-md-10">
                                    <label class="control-label col-md-3">销量</label>
                                    <div class="col-md-1" style="width:75px ">
                                        <select class="form-control" name="saleDays">
                                            <option value="7" selected="selected">7天</option>
                                            <option value="30">30天</option>
                                            <option value="90">90天</option>
                                        </select>
                                    </div>
                                    <div class="col-md-1" style="width:75px ">
                                        <select class="form-control" name="compareSignal">
                                            <option value="\>" selected="selected">\></option>
                                            <option value="\<">\<</option>
                                            <option value="\>=">\>=</option>
                                            <option value="\<=">\<=</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2" style="width:75px ">
                                        <input type="number" min="1" step="1"
                                               onkeypress="return (/\\d/.test(String.fromCharCode(event.keyCode)));"
                                               name="amount" class="form-control input-inline">
                                    </div>
                                    <div class="col-md-1">
                                        <button class="btn blue" onclick="removeSaleCondition('` + itemId + `','saleAmount_` + index + `');return false;">-
                                        </button>
                                    </div>
                                </div>`;
                div.append(content);
            }

            // 删除销量条件
            function removeSaleCondition(itemId, obj) {
                var div = $("#" + itemId + " > div[name='selectContent'] > #" + obj);
                div.remove();
            }

            // 初始化销售库区
            function initSaleRegion() {
                var data = ${domain.locationRegionSelect};
                $("input[name='saleRegion']").select2({
                    data: data,
                    placeholder: "销售库区",
                    multiple: true,
                    allowClear: true
                });
            }

            // 初始化销售属性
            function initSaleAttribute() {
                var data = ${domain.salesProperties};
                $("input[name='saleAttribute']").select2({
                    data: data,
                    placeholder: "销售属性",
                    multiple: true,
                    allowClear: true
                });
            }

            // 获取已查找的sku集合
            function getAlreadySearch() {
                var searchSkus = new Set();
                $.each($("td[name='sku']"), function (index, item) {
                    searchSkus.add($(item).text());
                });
                return searchSkus;
            }

            // 添加调拨sku
            function searchSkus() {
                var skuStrs = $("#skuStrs").val();
                if (!skuStrs || skuStrs == '') {
                    layer.alert("请输入sku", "error");
                    return false;
                }
                $.ajax({
                    url: CONTEXT_PATH + "prestorageMigration/rule/searchSkus",
                    type: 'POST',
                    data: {"skuStrs": skuStrs},
                    success: function (data) {
                        addSkuList(data);
                    },
                    error: function (data) {
                        layer.alert(data, 'error');
                    }
                });
            }

            // 用于移除sku
            function removeSku(skuId) {
                $("#" + skuId).remove();
            }

            // 导入sku
            function importSkus(target) {
                var filename = target.value;
                var ext, idx;
                if (filename == '') {
                    $("#submit-upload").attr("disabled", true);
                    layer.alert("请选择需要上传的文件!");
                    return;
                } else {
                    idx = filename.lastIndexOf(".");
                    if (idx != -1) {
                        ext = filename.substr(idx + 1).toUpperCase();
                        ext = ext.toLowerCase();
                        if (ext != 'xls' && ext != 'xlsx') {
                            layer.alert("只能上传Excel类型的文件!");
                            return;
                        }
                    } else {
                        layer.alert("只能上传Excel类型的文件!");
                        return;
                    }
                }

                var r = confirm("确定上传" + filename + "?");

                if (!r) {
                    return false;
                }

                var uploadUrl = CONTEXT_PATH + "prestorageMigration/rule//upload";

                var originalUrl = $("#domain").attr("action");

                $("#domain").attr("action", uploadUrl);

                $("#domain").ajaxSubmit(function (data) {
                    addSkuList(data);
                });

                // 还原url
                $(target).val(null);
                $("#domain").attr("action", originalUrl);
            }

            // 用于添加sku在列表中的显示
            function addSkuList(data) {
                if (!data) {
                    layer.alert("无对应的sku")
                    return;
                }
                $.each(data, function (index, item) {
                    var set = getAlreadySearch();
                    if (set.has(item.sku)) {
                        return true;
                    }
                    var content = `<tr id="` + item.sku + `">
                                                <td name="sku">` + item.sku + `</td>
                                                <td>` + item.name + `</td>`;
                    if (item.saleAttributeSettingStr == null) {
                        content += `<td>长呆滞</td>`;
                    } else {
                        content += `<td>` + item.saleAttributeSettingStr + `</td>`;
                    }

                    content += `<td>` + item.statusName + `</td>
                                        <td>` + item.sevenDaysSaleQuantity + `</td>
                                        <td>` + item.thirtyDaysSaleQuantity + `</td>
                                        <td>` + item.ninetyDaysSaleQuantity + `</td>
                                        <td>
                                            <button class="btn blue" onclick="removeSku('` + item.sku + `');return false;">删除</button>
                                        </td>
                                        </tr>`;
                    $("#skus").append(content);
                });
            }

            // 用于提交数据
            function submitData() {
                var warehouseType = $("select[name='rule.warehouseType']").find("option:selected").val();
                var priority = $("input[name='rule.priority']").val();
                var pickingStockThreshold = $("input[name='rule.pickingStockThreshold']").val();
                var id = $("input[id='id']").val();

                if (!warehouseType || !priority || !pickingStockThreshold || !id) {
                    layer.alert("数据不能为空!");
                    return false;
                }

                var obj = {
                    "id": parseInt(id),
                    "warehouseType": parseInt(warehouseType),
                    "pickingStockThreshold": parseInt(pickingStockThreshold),
                    "priority": parseInt(priority)
                };
                var ruleType = $("select[name='rule.ruleType']").find("option:selected").val();
                // 指定sku
                if (ruleType == '0') {
                    var skuSet = getAlreadySearch();
                    if (!skuSet || skuSet.size == 0) {
                        layer.alert("sku列表不允许为空", "error");
                        return false;
                    }
                    var skuObj = {};
                    var skus = [];
                    skuSet.forEach(function (index, item) {
                        skus.push(item.trim());
                    });
                    skus.sort(function(a,b){
                        return a.localeCompare(b);
                    });
                    skuObj['skus'] = skus;
                    obj['skuRuleJsonData'] = JSON.stringify(skuObj);
                    obj['ruleType'] = 0;
                }
                // 用于校验数据是否通过，true为通过
                var canSend = true;
                if (ruleType == '1') {
                    var saleAttributeList = [];
                    var saleAmountList = [];
                    var saleAmountSet = new Set();
                    var regionList = [];
                    var contentObj = {};
                    var contents = $("#rule-content").children();
                    $.each(contents, function (index, item) {
                        var condition = $(item).find("select[name='condition']").val();
                        if (condition == "销售属性") {
                            var saleAttributes = $(item).find("input[name='saleAttribute']").val().split(",");
                            if (saleAttributes == '') {
                                canSend = false;
                                return false;
                            }
                            $.each(saleAttributes, function (index, saleAttribute) {
                                if (saleAttributeList.hasOwnProperty(saleAttribute.trim())) {
                                    return true;
                                }
                                if (saleAttribute.trim() != '') {
                                    saleAttributeList.push(saleAttribute.trim());
                                }
                            });

                        } else if (condition == "销量") {
                            var saleAmounts = $(item).find("div[id^='saleAmount_']");
                            $.each(saleAmounts, function (index, saleAmount) {
                                var saleDays = $(saleAmount).find("select[name='saleDays']").val();
                                var compareSignal = $(saleAmount).find("select[name='compareSignal']").val();
                                var amount = $(saleAmount).find("input[name='amount']").val().trim();
                                if (amount == '') {
                                    canSend = false;
                                    return false;
                                }
                                // 用于校验是否有重复的判断条件
                                var key = saleDays + "_" + compareSignal + "_" + amount + "_";
                                if (saleAmountSet.has(key)) {
                                    return true;
                                }
                                saleAmountSet.add(key);

                                var amountObj = {};
                                amountObj['saleDays'] = saleDays;
                                amountObj['compareSignal'] = compareSignal;
                                amountObj['amount'] = amount;
                                saleAmountList.push(amountObj);
                                saleAmountSet.add(key);
                            });

                            // 校验不通过提前结束
                            if (!canSend) {
                                return false;
                            }
                        } else if (condition == "库区") {
                            var regions = $(item).find("input[name='saleRegion']").val().split(",");
                            if (regions == '') {
                                canSend = false;
                                return false;
                            }
                            $.each(regions, function (index, region) {
                                if (regionList.hasOwnProperty(region.trim())) {
                                    return true;
                                }
                                if (region.trim() != '') {
                                    regionList.push(region);
                                }
                            });
                        }
                    });
                    if (!canSend) {
                        layer.alert("数据不能为空!", "error");
                        return false;
                    }
                    if (saleAttributeList.length != 0) {
                        // 按照自然序排列
                        saleAttributeList.sort(function(a,b){
                            return a.localeCompare(b);
                        });
                        contentObj["saleAttribute"] = saleAttributeList;
                    }
                    if (saleAmountList.length != 0) {
                        saleAmountList.sort(function(a,b){
                            return (a.saleDays - b.saleDays)
                                || (a.compareSignal.localeCompare(b.compareSignal))
                                || (a.amount - b.amount);
                        });
                        contentObj['saleAmount'] = saleAmountList;
                    }
                    if (regionList.length != 0) {
                        // 按照自然序排列
                        regionList.sort(function (a,b) {
                            return a.localeCompare(b);
                        })
                        contentObj['region'] = regionList;
                    }
                    obj['skuRuleJsonData'] = JSON.stringify(contentObj);
                    obj['ruleType'] = 1;
                }
                var objArray = [];
                objArray.push(obj);
                $.ajax({
                    url: CONTEXT_PATH + "prestorageMigration/rule/update",
                    contentType: "application/json;charset=utf-8",
                    data: JSON.stringify(objArray),
                    type: 'POST',
                    dataType: "json",
                    success: function (data) {
                        if (data.status == 200) {
                            layer.confirm('成功！:)', {
                                icon: 1,
                                btn: ['确定']
                            }, function () {
                                location.href = CONTEXT_PATH + "prestorageMigration/rule/init";
                            });
                        } else {
                            customizeLayer(data.message, "error");
                        }
                    },
                    error: function (data) {
                        customizeLayer(data.message, "error");
                    }
                });
            }
        </script>
    </body>
</html>
