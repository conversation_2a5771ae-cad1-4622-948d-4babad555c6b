<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
    <head>
        <meta charset="UTF-8">
        <title>易世通达仓库管理系统</title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <#include "/common/include.html">
        <#include "/common/webSocket.html">
        <link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
        <style type="text/css">
            .modal-body input {
                width: 240px;
            }

            .table-top {
                margin-bottom: 0px;
                margin-top: 20px;
            }

            #item-list {
                margin-bottom: 80px;
            }

            .pick-content-button {
                margin-top: 20px;
            }

            .rule-pick-label input {
                width: 40px;
            }

            .red-flag {
                color: red;
            }
        </style>
    </head>
    <body>
        <@header method="header" active="14020000" ><#include "/ftl/header.ftl"></@header>
        <div id="page" style="background-color: rgb(231, 237, 248)">
            <div class="row">
                <div class="col-md-12" style="padding: 0">
                    <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                        <li><a href="#">仓库管理</a></li>
                        <li class="active">存货查询</li>
                    </ul>
                </div>
            </div>


            <!-- 内容 -->
            <div class="container-fluid" style="background-color: white;border: none">
                <div class="row">
                    <div class="col-md-12">
                        <#assign query = domain.query>
                        <form action="${CONTEXT_PATH}prestorageStock/search"
                              class="form-horizontal form-bordered form-row-stripped" method="post"
                              modelAttribute="domain" name="prestoreStockList" id="domain">
                            <!-- 分页信息 -->
                            <input id="page-no" type="hidden" name="page.pageNo" value="1">
                            <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
                            <div class="form-body">
                                <div class="form-group">
                                    <label class="control-label col-md-1">SKU</label>
                                    <div class="col-md-3">
                                        <input class="form-control" type="text" name="query.skuStrs"
                                               placeholder="多个使用,分割查询" value="${query.skuStrs }">
                                    </div>

                                    <label class="control-label col-md-1">货位号</label>
                                    <div class="col-md-3">
                                        <input class="form-control" type="text" name="query.locationStrs"
                                               placeholder="多个使用,分割查询" value="${query.locationStrs }">
                                    </div>

                                    <label class="control-label col-md-1">区域</label>
                                    <div class="col-md-3">
                                        <input class="form-control" type="text" name="query.regionStrs"
                                               placeholder="请选择" value="${query.regionStrs }">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="control-label col-md-1">通道</label>
                                    <div class="col-md-3">
                                        <input class="form-control" type="text" name="query.passageStrs"
                                               placeholder="请选择" value="${query.passageStrs }">
                                    </div>

                                    <label class="control-label col-md-1">销售属性</label>
                                    <div class="col-md-3">
                                        <input class="form-control" name="query.saleAttributeStrs"
                                               placeholder="多个使用,分割查询" type="text" value="${query.saleAttributeStrs }">
                                    </div>

                                    <label class="control-label col-md-1">是否统计汇总</label>
                                    <div class="col-md-3">
                                        <input class="form-control" name="query.statistics" placeholder="请选择"
                                               type="text"
                                               value="<#if query.statistics??>${query.statistics?string}</#if>">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-md-1">单品状态</label>
                                    <div class="col-md-3">
                                        <input class="form-control" type="text" name="query.singularStatusStrs"
                                               placeholder="请选择" value="${query.singularStatusStrs }">
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="pull-left">
                                </div>
                                <div class="col-md-offset-10" style="text-align: right">
                                    <button type="button" class="btn btn-default" onclick="formReset(this)">
                                        <i class="icon-refresh"></i> 重置
                                    </button>
                                    <button type="submit" class="btn blue">
                                        <i class="icon-search"></i> 查询
                                    </button>
                                    <@header method="auth" authCode="PRESTORAGE_STOCK_DOWNLOAD">
                                    <button type="button" class="btn btn-default" onclick="download()">
                                        <i class="icon-download"></i> 导出
                                    </button>
                                    </@header>
                                </div>
                            </div>
                        </form>
                    </div>
                    <br/>
                </div>

                <div class="row">
                    <div id="fixedDiv" class="col-md-12">
                        <table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
                            <colgroup>
                                <col width="5%"/>
                                <col width="8%"/>
                                <col width="8%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="8%"/>
                                <col width="5%"/>
                                <col width="8%"/>
                            </colgroup>
                            <thead>
                            <tr>
                                <th><input type="checkbox" id="check-all" name="checkAll" onclick="checkAll(this)">全选
                                </th>
                                <th>SKU</th>
                                <th>商品名称</th>
                                <th>单品状态</th>
                                <th>仓库销售属性</th>
                                <th>销售频次</th>
                                <th>动销频次</th>
                                <th>总库位库存</th>
                                <th>仓库</th>
                                <th>存货库存</th>
                                <th colspan="2">存货明细</th>
                                <th>拣货库位库存</th>
                                <th colspan="2">拣货库存明细</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="col-md-12" id="task-list-warp">
                        <!-- 内容  -->
                        <table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                            <colgroup>
                                <col width="5%"/>
                                <col width="8%"/>
                                <col width="8%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="8%"/>
                                <col width="5%"/>
                                <col width="8%"/>
                            </colgroup>
                            <thead>
                            <tr>
                                <th><input type="checkbox" id="check-all" name="checkAll" onclick="checkAll(this)">全选
                                </th>
                                <th>SKU</th>
                                <th>商品名称</th>
                                <th>单品状态</th>
                                <th>仓库销售属性</th>
                                <th>销售频次</th>
                                <th>动销频次</th>
                                <th>总库位库存</th>
                                <th>仓库</th>
                                <th>存货库存</th>
                                <th colspan="2">存货明细</th>
                                <th>拣货库位库存</th>
                                <th colspan="2">拣货库存明细</th>
                            </tr>
                            </thead>
                            <tbody>
                            <#list domain.prestorageStockVOs as item>
                                <#assign localWarehouseMap = item.localWarehouseMap>
                                <#assign transferWarehouseMap = item.transferWarehouseMap>
                                <#assign localWarehouseAmountMap = item.localWarehouseAmountMap>
                                <#assign transferWarehouseAmountMap = item.transferWarehouseAmountMap>
                                <#list 0..((item.rowSize?number) - 1)!0 as index>
                                    <tr>
                                        <#if index == 0>
                                            <td rowspan="${item.rowSize}">
                                                <label class="checkbox-inline">
                                                    <input name="ids" type="checkbox" value="${item.skuId}">
                                                    ${item.skuId}
                                                </label>
                                            </td>
                                            <td rowspan="${item.rowSize}">${item.sku}</td>
                                            <td rowspan="${item.rowSize}">${item.name}</td>
                                            <td rowspan="${item.rowSize}">${item.singularStatusStr}</td>
                                            <td rowspan="${item.rowSize}">${item.saleAttribute}</td>
                                            <td rowspan="${item.rowSize}">${item.saleOrders}</td>
                                            <td rowspan="${item.rowSize}">${item.saleDays}</td>
                                            <td rowspan="${item.rowSize}">${item.sumTotalStockAmount}</td>
                                        </#if>
                                        <#-- 本地仓 ,key = 5 为存货库存。等于1则为拣货库存 -->
                                        <#if index < item.localWarehouseRowSize>
                                            <#list ["5","1"] as key>
                                                <#if localWarehouseMap?? && localWarehouseMap[key]??>
                                                    <#if index == 0>
                                                        <#if key == "5">
                                                        <td rowspan="${item.localWarehouseRowSize}">
                                                            ${localWarehouseMap[key][0].warehouseType}
                                                        </td>
                                                        </#if>
                                                        <td rowspan="${item.localWarehouseRowSize}">
                                                            ${localWarehouseAmountMap[key]!'-'}
                                                        </td>
                                                    </#if>
                                                    <#if index < localWarehouseMap[key]?size - 1>
                                                        <td>
                                                            ${localWarehouseMap[key][index].location!'-'}
                                                        </td>
                                                        <td>
                                                            ${localWarehouseMap[key][index].stockAmount!'-'}
                                                        </td>
                                                    <#elseif index == localWarehouseMap[key]?size - 1>
                                                        <td rowspan="${item.localWarehouseRowSize - localWarehouseMap[key]?size + 1}">
                                                            ${localWarehouseMap[key][index].location!'-'}
                                                        </td>
                                                        <td rowspan="${item.localWarehouseRowSize - localWarehouseMap[key]?size + 1}">
                                                            ${localWarehouseMap[key][index].stockAmount!'-'}
                                                        </td>
                                                    </#if>
                                                <#else>
                                                    <#if index == 0>
                                                        <#if key == "5">
                                                        <td rowspan="${item.localWarehouseRowSize}">本地仓</td>
                                                        </#if>
                                                        <td rowspan="${item.localWarehouseRowSize}">-</td>
                                                        <td rowspan="${item.localWarehouseRowSize}">-</td>
                                                        <td rowspan="${item.localWarehouseRowSize}">-</td>
                                                    </#if>
                                                </#if>
                                            </#list>
                                        <#else>
                                        <#--中转仓-->
                                            <#list ["5","1"] as key>
                                                <#if transferWarehouseMap?? && transferWarehouseMap[key]??>
                                                    <#if index - item.localWarehouseRowSize == 0>
                                                        <#if key == "5">
                                                        <td rowspan="${item.transferWarehouseRowSize}">
                                                            ${transferWarehouseMap[key][0].warehouseType}
                                                        </td>
                                                        </#if>
                                                        <td rowspan="${item.transferWarehouseRowSize}">
                                                            ${transferWarehouseAmountMap[key]!'-'}
                                                        </td>
                                                    </#if>
                                                    <#if index - item.localWarehouseRowSize < transferWarehouseMap[key]?size - 1>
                                                        <td>
                                                            ${transferWarehouseMap[key][index - item.localWarehouseRowSize].location!'-'}
                                                        </td>
                                                        <td>
                                                            ${transferWarehouseMap[key][index - item.localWarehouseRowSize].stockAmount!'-'}
                                                        </td>
                                                    <#elseif index - item.localWarehouseRowSize == transferWarehouseMap[key]?size - 1>
                                                        <td rowspan="${item.transferWarehouseRowSize - transferWarehouseMap[key]?size + 1}">
                                                            ${transferWarehouseMap[key][index - item.localWarehouseRowSize].location!'-'}
                                                        </td>
                                                        <td rowspan="${item.transferWarehouseRowSize - transferWarehouseMap[key]?size + 1}">
                                                            ${transferWarehouseMap[key][index - item.localWarehouseRowSize].stockAmount!'-'}
                                                        </td>
                                                    </#if>
                                                <#else>
                                                    <#if index - item.localWarehouseRowSize == 0>
                                                        <#if key == "5">
                                                            <td rowspan="${item.transferWarehouseRowSize}">中转仓</td>
                                                        </#if>
                                                        <td rowspan="${item.transferWarehouseRowSize}">-</td>
                                                        <td rowspan="${item.transferWarehouseRowSize}">-</td>
                                                        <td rowspan="${item.transferWarehouseRowSize}">-</td>
                                                    </#if>
                                                </#if>
                                            </#list>
                                        </#if>
                                    </tr>
                                </#list>
                            </#list>
                            <tr>
                                <td colspan="5"><span class="group-inline" style="color: red;">汇总:</span></td>
                                <td><span class="group-inline" style="color: red;">
                                        <#if domain.statisticsStockVo?? && domain.statisticsStockVo.saleOrders??>
                                            ${domain.statisticsStockVo.saleOrders}
                                        </#if>
                                    </span>
                                </td>
                                <td><span class="group-inline" style="color: red;">
                                        <#if domain.statisticsStockVo?? && domain.statisticsStockVo.saleDays??>
                                            ${domain.statisticsStockVo.saleDays}
                                        </#if>
                                    </span>
                                </td>
                                <td><span class="group-inline" style="color: red;">
                                        <#if domain.statisticsStockVo?? && domain.statisticsStockVo.totalStock??>
                                            ${domain.statisticsStockVo.totalStock}
                                        </#if>
                                    </span>
                                </td>
                                <td><span class="group-inline" style="color: red;">-</span></td>
                                <td><span class="group-inline" style="color: red;">
                                        <#if domain.statisticsStockVo?? && domain.statisticsStockVo.prestorageTotalStock??>
                                            ${domain.statisticsStockVo.prestorageTotalStock}
                                        </#if>
                                    </span>
                                </td>
                                <td><span class="group-inline" style="color: red;">-</span></td>
                                <td><span class="group-inline" style="color: red;">
                                        <#if domain.statisticsStockVo?? && domain.statisticsStockVo.prestorageTotalStock??>
                                            ${domain.statisticsStockVo.prestorageTotalStock}
                                        </#if>
                                    </span>
                                </td>
                                <td><span class="group-inline" style="color: red;">
                                        <#if domain.statisticsStockVo?? && domain.statisticsStockVo.pickingTotalStock??>
                                            ${domain.statisticsStockVo.pickingTotalStock}
                                        </#if>
                                    </span>
                                </td>
                                <td><span class="group-inline" style="color: red;">-</span></td>
                                <td><span class="group-inline" style="color: red;">
                                        <#if domain.statisticsStockVo?? && domain.statisticsStockVo.pickingTotalStock??>
                                            ${domain.statisticsStockVo.pickingTotalStock}
                                        </#if>
                                    </span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <!-- 内容end  -->
                    </div>
                </div>
                <div id="fixed-bottom">
                    <div id="pager"></div>
                </div>
            </div>

        </div>
        <#include "/common/footer.html">
    </body>
    <script type="text/javascript">

        // 分页
        var total = "${domain.page.totalCount}";
        var pageNo = "${domain.page.pageNo}";
        var pageSize = "${domain.page.pageSize}";
        $("#pager").initPager({total: total, pageNo: pageNo, pageSize: pageSize});

        // 初始化选择项
        $("input[name='query.statistics']").select2({
            data: [{id: "true", text: "是"}, {id: "false", text: "否"}],
            placeholder: "请选择",
            allowClear: true,
            multiple: false
        });

        var regionSelectData = ${domain.regionSelectData};
        $("input[name='query.regionStrs']").select2({
            data: regionSelectData,
            placeholder: "请选择",
            allowClear: true,
            multiple: true
        });

        var passageSelectData = ${domain.passageSelectData};
        $("input[name='query.passageStrs']").select2({
            data: passageSelectData,
            placeholder: "请选择",
            allowClear: true,
            multiple: true
        });

        var saleAttributeSelectData = ${domain.saleAttributeSelectData};
        $("input[name='query.saleAttributeStrs']").select2({
            data: saleAttributeSelectData,
            placeholder: "请选择",
            allowClear: true,
            multiple: true
        });

        var singularStatusSelectData = ${domain.singularStatusSelectData};
        $("input[name='query.singularStatusStrs']").select2({
            data: singularStatusSelectData,
            placeholder: "请选择",
            allowClear: true,
            multiple: true
        });

        //全选
        function checkAll(obj) {
            $("#task-list").find("input[name='ids']").prop("checked", $(obj).is(':checked'));
        }


        // 获取选中的记录
        function getCheckedIds() {
            var checkedIds = $("input[name='ids']:checked");
            return checkedIds;
        }

        function download() {
            var ids = getCheckedIds();
            var uuid = getFileQueueUUID();
            var diglog = dialog({
                title: "存货查询导出",
                width: 500,
                height: 80,
                url: CONTEXT_PATH + "prestorageStock/downloadmode",
                okValue: '确定',
                ok: function () {
                    var exportWindow = $(this.iframeNode.contentWindow.document.body);

                    var submitForm = exportWindow.find("#submit-form");

                    var exportType = submitForm.find("input[name='exportType']:checked").val();

                    var submitFormParam = $('#domain').serialize();

                    // 导出当前选择
                    if(exportType == 3) {
                        if(ids.length == 0) {
                            layer.alert("请选择要操作的数据");
                            return false;
                        }
                        submitFormParam = submitFormParam + "&" +ids.serialize();
                    }
                    submitFormParam = submitFormParam + "&exportType=" + exportType + "&uuid=" + uuid;

                    //还原分页
                    $("#page-no").val("${domain.page.pageNo}");

                    $.post(CONTEXT_PATH + "prestorageStock/download", submitFormParam, function(data){
                        if (data.status == 200) {
                            if (data.message==null || data.message==''){
                                layer.alert('成功',function (index) {
                                    layer.close(index);
                                    diglog.close().remove();
                                    location.reload();
                                });
                            }else{
                                customizeLayer(data.message);
                            }
                        } else {
                            customizeLayer(data.message);
                        }
                    });
                    $("#page-no").val("1");
                    return true;
                },
                cancelValue: '取消',
                cancel: function () {}
            });
            diglog.show();
        }

    </script>
</html>