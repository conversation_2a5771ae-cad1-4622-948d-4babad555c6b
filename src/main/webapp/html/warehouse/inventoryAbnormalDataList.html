<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/html">

	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<#include "/common/include.html">
		<link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
		<style type="text/css">
			.modal-body input {
				width: 240px;
			}
		</style>
	</head>

	<body>
		<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>

		<div id="page" style="background-color: rgb(231, 237, 248)">
			<div class="row">
				<div class="col-md-12" style="padding: 0">
					<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
						<li>
							<a href="#">盘点</a>
						</li>
						<li class="active"> 库区盘点异常数据</li>
					</ul>
				</div>
			</div>

			<!-- 内容 -->
			<div class="container-fluid" style="background-color: white;border: none">
				<div class="row">
					<div class="col-md-12">
						<#assign query = domain.query>
						<form action="${CONTEXT_PATH}abnormalData/search" class="form-horizontal form-bordered form-row-stripped" method="post" modelAttribute="domain" name="inventoryAbnormalDataForm" id="domain">
							<!-- 分页信息 -->
							<input id="page-no" type="hidden" name="page.pageNo" value="1">
							<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">

							<div class="form-body">
								<div class="form-group">
									<label class="control-label col-md-1">SKU</label>
									<div class="col-md-3">
										<input class="form-control" id="" name="query.sku" type="text" placeholder="" value="${query.sku}">
									</div>

									<label class="control-label col-md-1">盘点任务号</label>
									<div class="col-md-3">
										<input class="form-control" name="query.taskNumber" type="text" value="${query.taskNumber}">
									</div>

									<label class="control-label col-md-1">盘点人</label>
									<div class="col-md-3">
										<input class="form-control" name="query.checker" type="text" value="${query.checker}">
									</div>
								</div>

								<div class="form-group">
									<label class="control-label col-md-1">盘点时间</label>
									<div class="col-md-3">
										<div class="input-group">
											<input class="form-control Wdate" type="text" name="query.fromInventoryDate" placeholder="" readonly="readonly" value="${query.fromInventoryDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
											<span class="input-group-addon">到</span>
											<input class="form-control Wdate" type="text" name="query.endInventoryDate" placeholder="" readonly="readonly" value="${query.endInventoryDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
										</div>
									</div>
								</div>
							</div>
							<div>
								<div class="col-md-offset-8" style="text-align: right">
									<button type="button" class="btn btn-default" onclick="formReset(this)">
										<i class="icon-refresh"></i>重置
									</button>
									<button type="submit" class="btn blue">
										<i class="icon-search"></i>查询
									</button>
                                    <@header method="auth" authCode="ABNORMAL_DOWNLOAD">
									<button type="button" class="btn btn-default" onclick="download()">
										<i class="icon-download"></i>导出
									</button>
                                    </@header>
								</div>
							</div>
						</form>
					</div>
					<br/>
				</div>
    
				<div class="row">
					<div class="col-md-12">
						<!-- 内容 -->
						<table class="table table-bordered table-hover table-condensed" id="inventoryTaskItem-list">
							<colgroup>
								<col width="4%" />
								<col width="10%" />
								<col width="10%" />
								<col width="10%" />
								<col width="10%" />
								<col width="10%" />
								<col width="10%" />
								<col width="10%" />
								<col width="10%" />
								<col width="8%" />
								<col width="8%" />
							</colgroup>
							<thead>
								<tr>
									<th><input type="checkbox" id="check-all" name="checkAll">全选</th>
									<th>盘点任务号</th>
									<th>盘点异常周转筐</th>
									<th>盘点人</th>
									<th>盘点确认时间</th>
									<th>SKU</th>
									<th>盘点库位</th>
									<th>盘点时正常拣货库位</th>
									<th>选择操作</th>
									<th>状态</th>
									<th>实物转移库位</th>
								</tr>
							</thead>
							<tbody>
								<#list domain.inventoryAbnormalDatas as item>
                                    <tr>
										<td>
											<input type="checkbox" class="ids" value="${item.id}" name="ids"> ${item.id}
										</td>
										<td>${item.taskNumber}</td>
										<td>${item.abnormalTurnoverBasket }</td>
										<td>${util('name',item.checker)}</td>
										<td>${item.confirmDate }</td>
										<td>${item.sku}</td>
										<td>${item.shelfPosition}</td>
										<td>${item.normalShelfPosition}</td>
										<td>${util('enumName', 'com.estone.warehouse.enums.OperationType', item.operationType)}</td>
										<td>${util('enumName', 'com.estone.warehouse.enums.InventoryAbnormalStatus', item.status)}</td>
										<td>${item.physicalTransferPosition }</td>
									</tr>
								</#list>
							</tbody>
						</table>
						<!-- 内容end -->
					</div>
				</div>
				<div id="fixed-bottom">
					<div id="pager"></div>
				</div>
			</div>
			<#include "/common/footer.html">
		</div>
		<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
		<script type="text/javascript">
			// 分页
			var total = "${domain.page.totalCount}";
			var pageNo = "${domain.page.pageNo}";
			var pageSize = "${domain.page.pageSize}";
			$("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });

			var heights = $("body").height();
			if(heights > 910) {
				$("#fixed-bottom").addClass("fixed-bottom-height")
			}

			// 领取人 PDA盘点任意界面的权限的人员
			$.getJSON(CONTEXT_PATH + "system/saleusers/userByPermissionCode?permissionCode=030100,030200,030300,030400", function(json) {
				if(json) {
					$("input[name='query.checker']").select2({
						data: json,
						placeholder: "领取",
						allowClear: true
					});
				} else {
					$("input[name='query.checker']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
				}
			});
			
			// 全选
			var checkAll = $("input[name='checkAll']");

			// 子选项
			var itemIds = $("input[name='ids']");
			checkAll.change(
				function() {
					itemIds.prop("checked", $(this).prop("checked"));
					itemIds.each(function() {
						var f = $(this).is(":checked");
						var checkClass = $(this).prop("class");
						$("." + checkClass).each(function() {
							$(this).prop("checked", f);
						})
					})
				}
			);
			// 获取选中的数据
			function getCheckedIds() {
				var checkedIds = $("input[name='ids']:checked");
				return checkedIds;
			}

			// 导出
			function download() {
				var param = $("#domain").serialize();
				var checkedDatas = getCheckedIds();
				if(checkedDatas.length > 0) {
					param = param + "&" +checkedDatas.serialize();
				}
				$.post(CONTEXT_PATH + "abnormalData/download", param, function(data){
					if (data.status == 200) {
						if (data.message==null || data.message==''){
							layer.alert('成功',function (index) {
								layer.close(index);
								diglog.close().remove();
								location.reload();
							});
						}else{
							customizeLayer(data.message);
						}
					} else {
						customizeLayer(data.message);
					}
				});
			}
		</script>
	</body>

</html>