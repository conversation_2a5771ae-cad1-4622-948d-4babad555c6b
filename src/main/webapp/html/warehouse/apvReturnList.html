<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<#include "/common/include.html">
		<style type="text/css">
			.modal-body input {
				width: 240px;
			}

			.top_bar {
				position: fixed;
				top: 0px;
			}

			#task-list td {
				vertical-align: middle;
			}

			#add_modal{
			    margin-top:50px;overflow:hidden;
			}
			.col-md-7{
				margin-left: 60px;
			}
		</style>
	</head>

	<body>
		<@header method="header" active="17010000"><#include "/ftl/header.ftl"></@header>

		<div id="page" style="background-color: rgb(231, 237, 248)">
			<div class="row">
				<div class="col-md-12" style="padding: 0">
					<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
						<li>
							<a href="#">库存管理</a>
						</li>
						<li class="active">退件录入</li>
					</ul>
				</div>
			</div>
			<!-- 内容 -->
			<div class="container-fluid" style="background-color: white;border: none">
				<div class="row">
					<div class="col-md-12 col-new-wms-8">
						<#assign query=domain.query>
						<form action="${CONTEXT_PATH}warehouse/apvReturns/search" class="form-horizontal form-bordered form-row-stripped" method="post" modelAttribute="domain" name="whApvReturnForm" id="domain">
							<!-- 分页信息 -->
							<input id="page-no" type="hidden" name="page.pageNo" value="1">
							<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
							<div class="form-body">
								<div class="form-group">
									<label class="control-label col-md-1" style="margin-left: 20px">安检创建时间:</label>
									<div class="col-md-3 input-group type-label">
                                        <input class="form-control Wdate" type="text" name="query.fromCreationDate" placeholder="" readonly="readonly" value="${query.fromCreationDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
										<label class="input-group-addon"style="width:25px ">到</label>
                                        <input class="form-control Wdate" type="text" name="query.toCreationDate" placeholder="" readonly="readonly" value="${query.toCreationDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    </div>
									<label class="control-label col-md-1" style="margin-left: 20px">退回追踪号:</label>
									<div class="col-md-2">
										<input class="form-control" type="text" name="query.trackingNumber" placeholder="请输入退回追踪号" value="${query.trackingNumber }">
									</div>
									<label class="control-label col-md-1" style="margin-left: 20px">退回YST:</label>
									<div class="col-md-2">
										<input class="form-control" type="text" name="query.apvNo" placeholder="请输入退回YST" value="${query.apvNo }">
									</div>
									<label class="control-label col-md-1" style="margin-left: 20px">创建人:</label>
									<div class="col-md-2">
										<input class="form-control" type="text" name="query.createdBy" value="${query.createdBy}">
									</div>
									<label class="control-label col-md-1" style="margin-left: 20px">反馈结果:</label>
									<div class="col-md-2">
										<input class="form-control" type="text" name="query.omsResult" value="${query.omsResult}">
									</div>
									<label class="control-label col-md-1" style="margin-left: 20px">重发单据状态:</label>
									<div class="col-md-2">
										<input class="form-control" type="text" name="query.apvStatus" value="${query.apvStatus}">
									</div>
								</div>
                                <div class="form-group">
									<label class="control-label col-md-1" style="margin-left: 20px">交运时间:</label>
									<div class="col-md-3 input-group type-label">
										<input class="form-control Wdate" type="text" name="query.fromDeliverDate" placeholder="" readonly="readonly" value="${query.fromDeliverDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
										<label class="input-group-addon">到</label>
										<input class="form-control Wdate" type="text" name="query.toDeliverDate" placeholder="" readonly="readonly" value="${query.toDeliverDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
									<label class="control-label col-md-1" style="margin-left: 20px">重发YST:</label>
									<div class="col-md-2">
										<input class="form-control" type="text" name="query.againApvNo" placeholder="请输入重发YST" value="${query.againApvNo }">
									</div>
                                    <label class="control-label col-md-1" style="margin-left: 20px">操作结果:</label>
                                    <div class="col-md-2">
                                        <input class="form-control" type="text" name="query.result" value="${query.result}">
                                    </div>
                                    <label class="control-label col-md-1" style="margin-left: 20px">操作人:</label>
                                    <div class="col-md-2">
                                        <input class="form-control" type="text" name="query.returnUser" value="${query.returnUser}">
                                    </div>

                                </div>
							</div>

							<div>
								<div class="pull-left" style="margin-left: 10px;">
                                    <@header method="auth" authCode="RETREVE_INPUT_ADD">
									<button type="button" class="btn btn-default" onclick="location.href='${CONTEXT_PATH}warehouse/apvReturns/addApvReturn'">
										<span class="icon-plus"></span> 新增
									</button>
                                    </@header>
                                    <@header method="auth" authCode="RETREVE_INPUT_BATCH_RETURN_COMPLETE">
									<button type="button" class="btn btn-default" onclick="batchReturn()">
										批量退仓完成
									</button>
                                    </@header>
								</div>
								<div class="pull-left" style="margin-left: 30px; margin-top: 10px;font-size: 18px;color: red">
									${domain.timedTaskApvReturn}
								</div>
								<div class="col-md-offset-10" style="text-align: right">
									<button type="button" class="btn default" onclick="formReset(this)">
										<i class="icon-refresh"></i> 重置
									</button>
									<button type="submit" class="btn blue">
										<i class="icon-search"></i> 查询
									</button>
                                    <@header method="auth" authCode="RETREVE_INPUT_DOWNLOAD">
									<button type="button" class="btn btn-default" onclick="downloadRecord()">
										<i class="icon-download"></i> 导出
									</button>
                                    </@header>
								</div>
							</div>
						</form>
					</div>
					<br/>
				</div>

				<div class="row">
					<div id="fixedDiv" class="col-md-12">
						<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
							<colgroup>
								<col width="4%" />
								<col width="12%" />
								<col width="12%" />
								<col width="9%" />
								<col width="9%" />
								<col width="5%" />
								<col width="5%" />
								<col width="12%" />
								<col width="9%" />
								<col width="5%" />
								<col width="10%" />
								<col width="8%" />
							</colgroup>
							<thead>
								<tr>
									<th>编号</th>
									<th>退回跟踪号</th>
									<th>退回YST</th>
									<th>退回时间</th>
									<th>创建人</th>
									<th>反馈结果 </th>
									<th>仓库操作结果</th>
									<th>重发YST</th>
                                    <th>操作人</th>
									<th>重发单据状态</th>
									<th>原YST交运时间</th>
									<th>日志</th>
								</tr>
							</thead>
						</table>
					</div>
					<div class="col-md-12" id="task-list-warp">
						<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
							<colgroup>
								<col width="5%" />
								<col width="12%" />
								<col width="12%" />
								<col width="9%" />
								<col width="9%" />
								<col width="5%" />
								<col width="5%" />
								<col width="11%" />
								<col width="9%" />
								<col width="5%" />
								<col width="10%" />
								<col width="8%" />
							</colgroup>
							<thead>
								<tr>
									<th><label class="checkbox-inline"><input type="checkbox" id="check-all" name="checkAll"> 编号</label></th>
									<th>退回跟踪号</th>
									<th>退回YST</th>
									<th>退回时间</th>
									<th>创建人</th>
									<th>反馈结果 </th>
									<th>仓库操作结果</th>
									<th>重发YST</th>
                                    <th>操作人</th>
									<th>重发单据状态</th>
									<th>原YST交运时间</th>
									<th>日志</th>
								</tr>
							</thead>
							<tbody>
								<#list domain.whApvReturns as whApvReturn>
									<tr>
										<td>
											<label class="checkbox-inline"><input type="checkbox" value="${whApvReturn.id}" name="ids"> ${whApvReturn.id}</label>
										</td>
										<td>${whApvReturn.trackingNumber}</td>
										<td>${whApvReturn.apvNo}
											<#if whApvReturn.returnNewApvNo??>
												<br/><span style="color: red;">新YST：${whApvReturn.returnNewApvNo}</span>
											</#if>
										</td>
										<td>${whApvReturn.creationDate}</td>
										<td>${util('name', whApvReturn.createdBy)}</td>
										<td>${util('enumName', 'com.estone.warehouse.enums.WhApvReturnOmsResult', whApvReturn.omsResult)}</td>
										<td>${util('enumName', 'com.estone.warehouse.enums.WhApvReturnResult', whApvReturn.result)}</td>
										<td>${whApvReturn.againApvNo}
											<#if whApvReturn.reSendNewApvNo??>
												<br/><span style="color: red;">新YST：${whApvReturn.reSendNewApvNo}</span>
											</#if>
										</td>
										<td>${util('name', whApvReturn.returnUser)}</td>
										<td>${util('enumName', 'com.estone.apv.common.ApvStatus', whApvReturn.apvStatus)}</td>
										<td>${whApvReturn.deliverTime}</td>
										<td>
											<button type="button" class="btn btn-xs btn-info" onclick="viewLog(${whApvReturn.id}, 'whApvReturn')">日志</button>
											<#if whApvReturn.omsResult?? && whApvReturn.omsResult==0>
												<button type="button" class="btn btn-xs btn-info" onclick="pushToOms(${whApvReturn.id})">推送</button>
											</#if>
										</td>
									</tr>
								</#list>
							</tbody>
						</table>
					</div>
				</div>
				<div id="fixed-bottom">
					<div id="pager"></div>
				</div>
			</div>
			<#include "/common/footer.html">
		</div>

		<!-- 弹窗 -->
		<div class="modal fade" id="add_modal" tabindex="-1" role="dialog" aria-labelledby="organization-add-label" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
						<h4 class="modal-title" id="expert-modal-label">新增</h4>
					</div>
					<div class="modal-body form-horizontal portlet">
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12" style="height:120px;">
							<div class="line-height-label">
								<div class="col-md-2">
									<label class="control-label col-md-11">追踪号</label>
								</div>
								<div class="col-md-10">
                                    <textarea id="add_input" class="form-control" cols="50" rows="5" type="text" placeholder="请输入退回包裹的追踪号" value=""></textarea>
								</div>
								<div class="col-md-7">
									<button style="float: right;margin-top: 10px; margin-left: 60px" type="button" class="btn btn-default blue" onclick="refresh()">关闭</button>
									<button style="float: right;margin-top: 10px;" type="button" class="btn btn-default blue" onclick="addApvReturn()">添加</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
		<script type="text/javascript">
			// 分页
			var total = "${domain.page.totalCount}";
			var pageNo = "${domain.page.pageNo}";
			var pageSize = "${domain.page.pageSize}";

			$("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });
			var heights = $("body").height();
			if(heights > 910) {
				$("#fixed-bottom").addClass("fixed-bottom-height")
			}

			// 全选
			var checkAll = $("input[name='checkAll']");
			// 子选项
			var itemIds = $("input[name='ids']");
			checkAll.change(
				function() {
					itemIds.prop("checked", $(this).prop("checked"));
					itemIds.each(function() {
						var f = $(this).is(":checked");
						var checkClass = $(this).prop("class");
						$("." + checkClass).each(function() {
							$(this).prop("checked", f);
						})
					})
				}
			);
			// 获取选中的记录
			function getCheckedIds() {
				var checkedIds = $("input[name='ids']:checked");
				return checkedIds;
			}
			
			// 反馈结果
			var omsResultJson = ${domain.omsResultJson};
			$("input[name='query.omsResult']").select2({
				data : omsResultJson,
				placeholder : "反馈结果",
				allowClear : true
			});
			
			// 操作结果
			var resultJson = ${domain.resultJson};
			$("input[name='query.result']").select2({
				data : resultJson,
				placeholder : "操作结果",
				allowClear : true
			});

            // 创建人
            $.getJSON(CONTEXT_PATH + "system/saleusers/userByRole", function(json){
                if (json) {
                    $("input[name='query.createdBy']").select2({
                        data : json,
                        placeholder : "创建人",
                        allowClear : true
                    });
                    // 操作人
                    $("input[name='query.returnUser']").select2({
                        data : json,
                        placeholder : "操作人",
                        allowClear : true
                    });
                } else {
                    $("input[name='query.createdBy']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
                    $("input[name='query.returnUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
                }
            });

			var apvStatusJson = ${domain.apvStatusJson};
			$("input[name='query.apvStatus']").select2({
				data : apvStatusJson,
				placeholder : "状态",
				multiple: true,
				allowClear : true
			});
			
			// 添加退回单
			function add(){
	            $("#add_modal").modal('show');
	            setTimeout( function(){
	                /*$("#add_modal").draggable({
	                    handle: ".modal-header"
	                });*/
	            }, 200 );
	        }
			
			// 添加退回单
			function addApvReturn(){
	            var trackingNumber = $("#add_input").val();
	            if(!trackingNumber) {
	            	layer.alert("追踪号不能为空!", "error");
	            	return false;
	            }
	            $.getJSON(CONTEXT_PATH + "warehouse/apvReturns/create?trackingNumber=" + trackingNumber, function(result){
					if(result.status == 200) {
						layer.alert(result.message);
                        // setTimeout( function(){
                        //     window.location.reload();
                        // }, 200 );
					} else {
						customizeLayer(result.message, "error");
					}
				});
	        }

	        function refresh(){
				setTimeout( function(){
					   window.location.reload();
				}, 200 );
			}

			//批量退回
			function batchReturn() {
				var checkedDatas = getCheckedIds();
				if(checkedDatas.length == 0) {
					layer.alert("请选择要操作的退回单", 'error');
					return;
				}
				var diglog = dialog({
					title: '批量退仓',
				    content: "确认退仓？",
				    width: 200,
                    height: 30,
				    onshow: function () {
				    },
				    okValue: '确定',
			        ok: function () {
			        	
						var ids = checkedDatas.serialize();
						//window.location.href = CONTEXT_PATH + "warehouse/apvReturns/batchReturn?" + ids;
                        $.getJSON(CONTEXT_PATH + "warehouse/apvReturns/batchReturn?" + ids, function(result){
                            if(result.status == 200) {
								layer.alert("操作成功!");
                                setTimeout( function(){
                                    window.location.reload();
                                }, 200 );
                            } else {
                                customizeLayer(result.message, "error");
                            }
                        });
			        	setTimeout(function () {
				    		diglog.close().remove();
				    	}, 100);
				    	return false;
			        },
			        cancelValue: '取消',
			       	cancel: function () {}
				}).showModal();
				
			}

			// 导出
			function downloadRecord() {
				var checkedDatas = getCheckedIds();
				if(checkedDatas.length == 0) {
					var param = $("#domain").serialize();

					window.open(CONTEXT_PATH + "warehouse/apvReturns/download?" + param);
				} else {
					var ids = checkedDatas.serialize();
					window.open(CONTEXT_PATH + "warehouse/apvReturns/download?" + ids);
				}
			}

			function pushToOms(id) {
                $.getJSON(CONTEXT_PATH + "warehouse/apvReturns/pushToOms?id=" + id, function(result){
                    if(result.status == 200) {
						layer.alert("推送成功!");
                        setTimeout( function(){
                            window.location.reload();
                        }, 200 );
                    } else {
                        layer.alert(result.message, "error");
                    }
                });
            }
		</script>
	</body>

</html>