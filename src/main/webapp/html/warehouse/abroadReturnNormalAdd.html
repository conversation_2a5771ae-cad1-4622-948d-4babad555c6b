<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

	<div>
		<div>
				<div class="row"  style="height: 500px;">
					<div id="check-area">
						<div class="panel-header" style="overflow: hidden;">
						</div>
						<div class="form-group">
							<#if (domain.areaMap?? && (domain.areaMap)!) || (domain.virtualLocationMap?? && (domain.virtualLocationMap)!)>
								<div class="col-md-4" style="margin-left: 250px">
<!--									<label class="control-label col-md-6" style="text-align: right; line-height: 42px">-->
<!--									</label>-->
									<div class="col-lg-1 col-md-1" style="width: 80%;padding-left: 2px;padding-right: 2px">
										<div class="panel panel-default my-disabled" style="height: 480px;max-height: 480px;overflow: auto;line-height:50px;margin-bottom: 4px;text-align: center;cursor: pointer;background-color: skyblue" id="apv-grid" >
												<div class="col-md-3" style="text-align: left">
													<b  style="color:black">
														<input type="checkbox"  id="check-alls" name="checkAll" style="width:20px" onchange="checkChange(this)"/>返架区
													</b>
												</div>
												<div class="col-md-9" style="text-align: left">
													<#assign x=0 />
													<#list domain.areaMap?keys as key>
														<#if key != '汉海达' && key != '美景仓' && key != '南宁仓'>
															<span style="font-size: 15px;color: white">
																<b  style="color:black">
																	<input type="checkbox"  id="check-alls-1" name="check-alls-1" style="width:20px" value="${key}"/>${key}
																</b>
															</span>
															<#assign x=x+1 />
															<span style="font-size: 15px;color: white;margin-left: 8px;" ><b id="skuQty-${key_index}" style="color:black">SKU:${domain.areaMap[key]?size}</b></span>
															<span style="font-size: 15px;color: white;margin-left: 8px;"><b id="skuPcsQty-${key_index}" style="color:black">PCS:${domain.qtyMap[key]}</b></span>
															<table	class="table table-bordered table-condensed">
																<colgroup>
																	<col width="80" />
																	<col width="140" />
																	<col width="50"/>
																</colgroup>
																<thead>
																	<tr class="">
																		<th>SKU</th>
																		<th>库位</th>
																		<th>待反架数量</th>
																	</tr>
																</thead>
																<tbody>
																	<#list domain.areaMap[key] as whBatchReturnSku>
																		<tr class="sku-tr" id="sku-tr-${key_index}-${whBatchReturnSku_index}">
																			<td style="text-align: center;font-size: 15px;">
																				<!--  sku -->
																				<span class="sku-td" id="sku-td-${key_index}-${whBatchReturnSku_index}">${whBatchReturnSku.sku}</span>
																			</td>
																			<td style="text-align: center;font-size: 15px;">
																				<!--  库位 -->
																				<span class="sku-td" id="location-td-${key_index}-${whBatchReturnSku_index}">${whBatchReturnSku.locationNumber}</span>
																			</td>
																			<td style="text-align: center;font-size: 15px;">
																				<!--  待返架数量）-->
																				<span class="sku-td" id="qty-td-${key_index}-${whBatchReturnSku_index}">${whBatchReturnSku.waitUpQty}</span>
																			</td>
																		</tr>
																	</#list>
																</tbody>
															</table>
														</#if>
													</#list>
												</div>
										</div>
									</div>
								</div>
								<div class="col-md-3" >
									<!--<label class="control-label col-md-3" style="text-align: right; line-height: 42px">
									</label>-->
									<div class="col-lg-1 col-md-1" style="width: 90%;padding-left: 2px;padding-right: 2px">
										<div class="panel panel-default my-disabled" style="height: 480px;max-height: 480px;overflow: auto;line-height:50px;margin-bottom: 4px;text-align: center;cursor: pointer;background-color: skyblue" id="apv-grid2">
											<#if domain.qtyMap['汉海达']?? || domain.qtyMap['美景仓']?? || domain.qtyMap['南宁仓']??>
											<div class="col-md-3" style="text-align: left">
												<b  style="color:black">
													<input type="checkbox"  id="check-alls-2" name="check-alls-2" style="width:20px" onchange="checkChange2(this)"/>通道
												</b>
											</div>
											<div class="col-md-9" style="text-align: left">
												<#assign y=0 />
												<#list domain.areaMap?keys as key>
														<#if key == '汉海达' || key == '美景仓' || key == '南宁仓'>
															<!--<span style="font-size: 15px;color: white">
																<b  style="color:black">
																	<input type="checkbox"  id="2check-alls-${key_index}" name="checkAll" style="width:20px"/>${key}
																</b>
															</span>-->
															<#assign y=y+1 />
															<span style="font-size: 15px;color: white;margin-left: 8px;" ><b id="2skuQty-${key_index}" style="color:black">SKU:${domain.areaMap[key]?size}</b></span>
															<span style="font-size: 15px;color: white;margin-left: 8px;"><b id="2skuPcsQty-${key_index}" style="color:black">PCS:${domain.qtyMap[key]}</b></span>
															<table	class="table table-bordered table-condensed">
																<colgroup>
																	<col width="80" />
																	<col width="140" />
																	<col width="50"/>
																</colgroup>
																<thead>
																<tr class="">
																	<th>SKU</th>
																	<th>库位</th>
																	<th>待反架数量</th>
																</tr>
																</thead>
																<tbody>
																<#list domain.areaMap[key] as whBatchReturnSku>
																	<tr class="sku-tr" id="2sku-tr-${key_index}-${whBatchReturnSku_index}">
																		<td style="text-align: center;font-size: 15px;">
																			<!--  sku -->
																			<span class="sku-td" id="2sku-td-${key_index}-${whBatchReturnSku_index}">${whBatchReturnSku.sku}</span>
																		</td>
																		<td style="text-align: center;font-size: 15px;">
																			<!--  库位 -->
																			<span class="sku-td" id="2location-td-${key_index}-${whBatchReturnSku_index}">${whBatchReturnSku.locationNumber}</span>
																		</td>
																		<td style="text-align: center;font-size: 15px;">
																			<!--  待返架数量）-->
																			<span class="sku-td" id="2qty-td-${key_index}-${whBatchReturnSku_index}">${whBatchReturnSku.waitUpQty}</span>
																		</td>
																	</tr>
																</#list>
																</tbody>
															</table>
														</#if>
												</#list>
												</div>
											<#else>
												<span style="font-size: 25px;color: white"><b id="hint-msg2" style="color:black">无通道SKU</b></span>
											</#if>
										</div>
									</div>
								</div>

								<div class="col-md-3">
									<#if domain.virtualLocation=true>
									<div class="col-lg-1 col-md-1" style="width: 90%;padding-left: 2px;padding-right: 2px;padding-top:10px; background-color: skyblue;height:480px " >
										<div class="col-md-3" style="text-align: left">
											<b  style="color:black">
												<#if domain.allMatchPicking=false>
												<input type="checkbox"  id="check-alls-4" name="check-alls-3" style="width:20px;" disabled="disabled"/>虚拟库位
												<#else>
												<input type="checkbox"  id="check-alls-3" name="check-alls-3" style="width:20px" onchange="checkChange3(this)"/>虚拟库位
												</#if>
											</b>
										</div>
										<div class="col-md-9" style="text-align: left ;height: 300px" >
											<#assign y=0 />
											<#list domain.virtualLocationMap?keys as key>
											<!--<span style="font-size: 15px;color: white">
                                                <b  style="color:black">
                                                    <input type="checkbox"  id="2check-alls-${key_index}" name="checkAll" style="width:20px"/>${key}
                                                </b>
                                            </span>-->
											<#assign y=y+1 />
											<span style="font-size: 15px;color: white;margin-left: 8px;" ><b id="3skuQty-${key_index}" style="color:black">SKU:${domain.virtualLocationMap[key]?size}</b></span>
											<span style="font-size: 15px;color: white;margin-left: 8px;"><b id="3skuPcsQty-${key_index}" style="color:black">PCS:${domain.virtualQtyMap[key]}</b></span>
											<table	class="table table-bordered table-condensed" style="margin-top: 20px">
												<thead>
												<tr class="">
													<th>SKU</th>
													<th>库位</th>
													<th>库位类型</th>
													<th>待反架数量</th>
												</tr>
												</thead>
												<tbody>
												<#list domain.virtualLocationMap[key] as whBatchReturnSku>
												<tr class="sku-tr" id="3sku-tr-${key_index}-${whBatchReturnSku_index}">
													<td style="text-align: center;font-size: 15px;">
														<!--  sku -->
														<span class="sku-td" id="3sku-td-${key_index}-${whBatchReturnSku_index}">${whBatchReturnSku.sku}</span>
													</td>
													<td style="text-align: center;font-size: 15px;">
														<!--  库位 -->
														<span class="sku-td" id="3location-td-${key_index}-${whBatchReturnSku_index}">${whBatchReturnSku.locationNumber}</span>
													</td>
													<td style="text-align: center;font-size: 15px;">
														<!--  待返架数量）-->
														<span class="sku-td" id="3type-td-${key_index}-${whBatchReturnSku_index}">${util('enumName', 'com.estone.warehouse.enums.LocationType', whBatchReturnSku.existingLocationType)}</span>
													</td>
													<td style="text-align: center;font-size: 15px;">
														<!--  待返架数量）-->
														<span class="sku-td" id="3qty-td-${key_index}-${whBatchReturnSku_index}">${whBatchReturnSku.waitUpQty}</span>
													</td>
												</tr>
												</#list>
												</tbody>
											</table>
											</#list>
										</div>
									</div>
									</#if>
								</div>
							<#else>
								<div class="col-md-7">
								</div>
								<div class="col-md-9">
									<label class="control-label col-md-6" style="text-align: right; line-height: 42px">
									</label>
									<div class="col-lg-1 col-md-1" style="width: 50%;padding-left: 2px;padding-right: 2px">
										<!--<div class="panel panel-default my-disabled" style="height: 480px;line-height:350px;margin-bottom: 4px;text-align: center;cursor: pointer;" id="apv-grid}">-->
											<span style="font-size: 25px;color: white"><b id="error-msg" style="color:red">${domain.errorMsg}</b></span>
										<!--</div>-->
									</div>
								</div>
							</#if>
						</div>
					</div>
				</div>
		</div>
	</body>

</html>