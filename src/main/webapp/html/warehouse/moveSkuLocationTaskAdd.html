<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
	<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>
	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">库存</a></li>
					<li class="active">SKU移库</li>
					<li class="active">新建库位移库</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="col-md-9">
				<form action="${CONTEXT_PATH}moveLocationTask/create"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="checkInForm" id="domain">
					<div class="form-body">
						<input id="taskId" type="hidden" name="taskId" value="${domain.moveLocationTask.id}">
						<input id="taskNo" type="hidden" name="taskNo" value="${domain.moveLocationTask.taskNo}">
						<div class="form-group" style="margin-top: 50px;">
							<div class="col-md-5">待转移库位</div>
						</div>
						<div class="form-group">
							<div class="col-md-5">
								<div id="input_sku">
									<input style="height:35px;border-radius: 8px !important;" onkeypress="if(event.keyCode==13) { getSKU(); return false;}"
                                           class="form-control" placeholder="请输入SKU，多个SKU请以英文,号隔开" id="skus" name="skus" type="text">
								</div>
							</div>
                            <div class="col-md-2">
								<div style="border-radius: 8px !important;width: 100%;" type="button" class="btn blue" onclick="getSKU();">
									<i class="icon-plus"></i> 添加SKU
								</div>
                            </div>
						</div>

						<div class="form-group" style="margin-top: 20px;">
							<div class="col-md-7">
								<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
									<thead>
										<tr>
											<th>序号</th>
											<th>SKU</th>
											<th>库位</th>
											<th>迁移库存</th>
											<th>目标库位</th>
											<th>操作</th>
										</tr>
									</thead>
									<tbody id="tbody">
										<#list domain.moveLocationTask.items as item>
											<tr>
												<td id="data-index">${item_index +1}</td>
												<td id="data-sku">${item.sku}</td>
												<td id="data-location">${item.location}</td>
												<td id="data-quantity">${item.quantity}</td>
												<td id="data-new-location"><input name="new_location" value="${item.newLocation}"/></td>
												<td onclick="onRemove(this)"><a>删除</a></td>
											</tr>
										</#list>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</form>
				<div class="col-md-9" style="margin-bottom: 20px;">
					<button class="btn blue" id="confirmMove">
						<i class="icon-search"></i> 提交移库单
					</button>
				</div>
			</div>
			<br/>
		</div>
	</div>
	
		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript">
        var del_array=new Array();
        function getSKU() {
            var taskNo = $('#taskNo').val();
            var skus = $('#skus').val();
            if(skus == ''){
                layer.alert('SKU不能为空！','error');
                return;
			}
            $.post(CONTEXT_PATH + "moveSkuLocationTask/querySkus", {skus:skus,taskNo:taskNo}, function(data) {
                if (data.status == 200) {
                    var sku_array=new Array();
                    $("[id=data-sku]").each(function(){
                        sku_array.push($(this).text());
                    });
                    var errorSkuStr = '';
                    var index = sku_array.length;
                    if(data.body){
						var html = "";
                        $.each(data.body,function(sku,moveData){
							var item = moveData;
							var sku = item.sku;
							var number=$.inArray(sku, sku_array);
							if(number == -1){
								var taskId = $('#taskId').val();
								var del_number=$.inArray(sku, del_array);
								if(item.location == '' || item.location == null){
                                    layer.alert(sku + "没有库位，不能操作！", "error");
                                    return;
								}
								if(taskId!=null && taskId!='' && del_number != -1){
									layer.alert(sku + "待删除不能添加，如不想删除请刷新后重新操作！", "error");
									return;
								}else {
									html += "<tr>" +
											"<td id='data-index'>"+parseInt(index+1)+"</td>"+
											"<td id='data-sku'>"+sku+"</td>"+
											"<td id='data-location'>"+item.location+"</td>"+
											"<td id='data-quantity'>"+item.quantity+"</td>"+
											"<td id='data-new-location'><input name='new_location' value=''/></td>"+
											"<td onclick='onRemove(this)'><a>删除</a></td></tr>";
									index = index+1;
								}

							}else {
								layer.alert(sku + "记录已存在！", "error");
							}
                        });
						$("#tbody").append(html);
					}
                } else {
                    customizeLayer(data.message, "error");
                }
                $('#skus').val('');
            });
        }

        function onRemove(obj){
            if(confirm("是否删除该条库位?")){
                var taskId = $('#taskId').val();
                if(taskId!=null && taskId!=''){
					var delSku = $(this).find('td[id="data-sku"]').text();
					del_array.push(delSku);
				}
                $(obj).parent().remove();
                // 重新编排
                $("#tbody").find('tr').each(function (indexValue) {
					indexValue++;
					$(this).find('td[id="data-index"]').text(indexValue);
                });
            }
        }

		$('#confirmMove').on("click",function () {
            var skus = '';
            var result = false;
            $('#tbody').find('tr').each(function (index) {
                var sku = $(this).find('td[id="data-sku"]').text();
                var new_location = $(this).find('input[name="new_location"]').val();
                if(new_location!=''){
                    sku += "=" + new_location;
                }
                if(skus!=''){
                    skus += ",";
				}
                skus += sku;
                result = true;
			});

            if(result){
				var url = "moveSkuLocationTask/create";
                var taskId = $('#taskId').val();
                if(taskId!=null && taskId!=''){
                    url= "moveSkuLocationTask/update";
                }
				var param = {
                    skus:skus,
                    taskId:taskId
				};

				$.post(CONTEXT_PATH + url, param, function(data) {
					if (data.status == 200) {
						layer.confirm('成功！',{
							icon: 1,
							btn: ['确定']
						},function () {
							location.href = CONTEXT_PATH + "moveSkuLocationTask";
						})
					} else {
						customizeLayer(data.message, "error");
					}
				});
			}
        });

	</script>
</body>
</html>