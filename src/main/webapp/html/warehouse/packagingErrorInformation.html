<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
    <head>
        <title>易世通达仓库管理系统</title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <#include "/common/webSocket.html">
        <#include "/common/include.html">
        <style type="text/css">

            .modal-body input {
                width: 240px;
            }
            .top_bar{
                position:fixed;top:0px;
            }
        </style>
    </head>

    <body>
        <@header method="header"  active="12070000" ><#include "/ftl/header.ftl"></@header>
        <div id="page" style="background-color: rgb(231, 237, 248)">
            <div class="row">
                <div class="col-md-12" style="padding: 0">
                    <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                        <li><a href="#">包装</a></li>
                        <li class="active">包装异常信息查询</li>
                    </ul>
                </div>
            </div>
            <div class="container-fluid" style="background-color: white;border: none">
                <#assign query = domain.query/>
                <div class="row">
                    <div class="portlet-body form" style="display: block;">
                        <form action="${CONTEXT_PATH}error/information/search"
                              class="form-horizontal form-bordered form-row-stripped"
                              method="post" modelAttribute="domain" name="searchForm" id ="domain">
                            <input id="page-no" type="hidden" name="page.pageNo" value="1">
                            <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">

                            <div class="form-body">
                                <div class="form-group">
                                    <label class="control-label col-md-1">报告人</label>
                                    <div class="col-md-3">
                                        <input class="form-control" type="text" name="query.reportPersonId"  value="${query.reportPersonId}">
                                    </div>
                                    <label class="control-label col-md-1">报告时间</label>
                                    <div class="col-md-3">
                                        <div class="input-group">
                                            <input class="form-control Wdate" type="text" name="query.startReportDate" placeholder="" readonly="readonly" value="${query.startReportDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                            <span class="input-group-addon">到</span>
                                            <input class="form-control Wdate" type="text" name="query.endReportDate" placeholder="" readonly="readonly" value="${query.endReportDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                        </div>
                                    </div>

                                    <label class="control-label col-md-1">sku</label>
                                    <div class="col-md-3">
                                        <input class="form-control" type="text" name="query.skuStr"  value="${query.skuStr}">
                                    </div>

                                    <label class="control-label col-md-1">处理状态</label>
                                    <div class="col-md-3">
                                        <input class="form-control" name="query.status" type="text" value="${query.status}">
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="pull-left" style="margin-left: 10px;">
                                    <@header method="auth" authCode="PACKAGING_ERROR_INFORMATION_ACCOMPLISH">
                                        <button type="button" class="btn btn-default" onclick="accomplish()">
                                            <i class="icon-lock"></i> 标记完成
                                        </button>
                                    </@header>
                                </div>

                                <div class="col-md-offset-10" style="text-align: right;margin-right: 10px;">
                                    <@header method="auth" authCode="PACKAGING_ERROR_INFORMATION_DOWNLOAD_DETAIL">
                                        <button type="button" class="btn btn-default" onclick="downloadDetails()">
                                            <i class="icon-download"></i> 导出数据
                                        </button>
                                    </@header>
                                    <button type="button" class="btn btn-default" onclick="formReset(this)">
                                        <i class="icon-refresh"></i> 重置
                                    </button>
                                    <button type="submit" class="btn blue">
                                        <i class="icon-search"></i> 查询
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <br />
                    <div class="row">
                        <div id="fixedDiv" class="col-md-12">
                            <table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
                                <colgroup>
                                    <col width="5%" />
                                    <col width="5%" />
                                    <col width="5%"/>
                                    <col width="5%"/>
                                    <col width="5%"/>
                                    <col width="5%" />
                                    <col width="5%" />
                                </colgroup>
                                <thead>
                                <tr>
                                    <th>sku</th>
                                    <th>问题字段</th>
                                    <th>报告人</th>
                                    <th>报告时间</th>
                                    <th>状态</th>
                                    <th>处理人</th>
                                    <th>处理完成时间</th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="col-md-12" id="task-list-warp">
                            <table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                                <colgroup>
                                    <col width="5%" />
                                    <col width="5%" />
                                    <col width="5%"/>
                                    <col width="5%"/>
                                    <col width="5%"/>
                                    <col width="5%" />
                                    <col width="5%" />
                                </colgroup>
                                <thead>
                                <tr>
                                    <th>sku</th>
                                    <th>问题字段</th>
                                    <th>报告人</th>
                                    <th>报告时间</th>
                                    <th>状态</th>
                                    <th>处理人</th>
                                    <th>处理完成时间</th>
                                </tr>
                                </thead>
                                <tbody>
                                    <#list domain.informationList as item>
                                        <tr>
                                            <td>
                                                <input type="checkbox" value="${item.id}" name="ids"/>
                                                ${item.sku}
                                            </td>
                                            <td>${item.questionFieldNameStrs}</td>
                                            <td>${item.reporter}</td>
                                            <td>${item.reportDate}</td>
                                            <td>${item.statusStr}</td>
                                            <td>${item.handler}</td>
                                            <td>${item.handleAccomplishmentDate}</td>
                                        </tr>
                                    </#list>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div id="fixed-bottom">
                    <div id="pager"></div>
                </div>
            </div>
            <#include "/common/footer.html">
        </div>
        <script type="text/javascript" src="${CONTEXT_PATH}js/pages/pms.js?v=${.now?datetime}"></script>
        <script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
        <script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
        <script type="text/javascript">

            var statusJson = ${domain.statusJson};
            $("input[name='query.status']").select2({
                data : statusJson,
                placeholder : "处理状态",
                multiple:false,
                allowClear : true
            });

            $.getJSON(CONTEXT_PATH + "system/saleusers/userByRole", function(json){
                if (json) {
                    $("input[name='query.reportPersonId']").select2({
                        data : json,
                        placeholder : "报告人",
                        multiple:false,
                        allowClear : true
                    });
                } else {
                    $("input[name='query.reportPersonId']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
                }
            });

            // 分页
            var total = "${domain.page.totalCount}";
            var pageNo = "${domain.page.pageNo}";
            var pageSize = "${domain.page.pageSize}";
            $("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});


            // 获取选中的订单
            function getCheckedIds() {
                var checkedIds = $("input[name='ids']:checked");
                return checkedIds;
            }

            function accomplish(){
                var checkedIds = getCheckedIds();
                if (!checkedIds || checkedIds.length == 0){
                    layer.alert("请勾选标记完成条目",'error');
                    return;
                }
                var ids = [];
                $.each(checkedIds,function (index,item) {
                    var val = $(item).val();
                    ids.push(val);
                })
                var idsStr = "";
                for (var i = 0; i < ids.length; i++) {
                    var id = ids[i];
                    idsStr += id;
                    if (i != ids.length - 1) {
                        idsStr += ",";
                    }
                }
                $.ajax({
                    url: CONTEXT_PATH + "/error/information/accomplish",
                    type: "post",
                    data: {ids:idsStr},
                    success:function(response){
                        if (response.status == '500') {
                            customizeLayer(response.message, 'error');
                        } else{
                            alert('标记成功！');
                            window.location.reload();
                        }
                    },
                    error:function () {
                        layer.alert("系统异常，操作失败!",'error');
                    }
                });
            }



            //导出
            function downloadDetails(){

                let uuid = getFileQueueUUID();

                var checkedIds = getCheckedIds();
                var diglog = dialog({
                    title: '导出',
                    width: 350,
                    height:100,
                    url: CONTEXT_PATH + "error/information/downloadmode?type=1&uuid=" + uuid,
                    okValue: '确定',
                    ok: function () {
                        var exportWindow = $(this.iframeNode.contentWindow.document.body);

                        $("#page-no").val("${domain.page.pageNo}");
                        var submitForm = exportWindow.find("#submit-form");

                        var exportType = submitForm.find("input[name='exportType']:checked").val();

                        var submitFormParam = $('#domain').serialize();

                        // 导出当前选择
                        if(exportType == 3) {
                            if(checkedIds.length == 0) {
                                layer.alert("请选择要操作的数据");
                                return false;
                            }else{
                                submitFormParam = submitFormParam + "&" +checkedIds.serialize();
                            }
                        }

                        submitFormParam = submitFormParam + "&exportType=" + exportType + "&uuid=" + uuid;

                        //还原分页
                        // $("#page-no").val("${domain.page.pageNo}");

                        downloadByPostForm(submitFormParam, CONTEXT_PATH + "error/information/download");

                        $("#page-no").val("1");

                        beginExport(uuid, '包装信息异常');
                        setTimeout(function () {
                            diglog.close().remove();
                        }, 100);

                        return true;
                    },
                    cancelValue: '取消',
                    cancel: function () {}
                });
                diglog.show();
            }
        </script>
    </body>
</html>