<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}

		#task-list td {
			vertical-align: middle;
		}
		.group-inline {
			color: red;
		}

	</style>
</head>
<body>
<@header method="header" active="18020000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li>
					<a href="#">库存管理</a>
				</li>
				<li class="active">耗材库存</li>
			</ul>
		</div>
	</div>
	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">

		<!-- BEGIN PAGE HEADER-->
		<#assign query=domain.query>
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="col-md-12">
				<form action="${CONTEXT_PATH}packagingMaterialStock/search"
					  class="form-horizontal form-bordered form-row-stripped"
					  method="post" modelAttribute="domain" name="materialStockDoForm" id="domain">
					<!-- 分页信息 -->
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-1">耗材型号</label>
							<div class="col-md-3">
								<input class="form-control" type="text" name="query.materialArticleNumberStr" placeholder="多个查询请以英文逗号分开"  value="${query.materialArticleNumberStr}">
							</div>
							<label class="control-label col-md-1">耗材名称</label>
							<div class="col-md-3">
								<input class="form-control" type="text" name="query.name" placeholder="多个查询请以英文逗号分开"  value="${query.name }">
							</div>

							<label class="control-label col-md-1">是否统计汇总</label>
							<div class="col-md-3">
								<select class="form-control" name="query.isTotal" value="${query.isTotal}">
									<option value="false">否</option>
									<option <#if query.isTotal == true>selected</#if> value="true">是</option>
								</select>
							</div>

						</div>
						<div class="form-group">

							<label class="control-label col-md-1">库存类型</label>
							<div class="col-md-3">
								<input class="form-control" name="query.type" type="text" value="${query.type}">
							</div>
							<label class="control-label col-md-1 type-label">库存范围</label>
							<div class="col-md-3 input-group type-label">
								<input type="number" name="query.thanQuantity" value="${query.thanQuantity }" class="Wdate form-control" digits="true"/>
								<span class="input-group-addon">到</span>
								<input type="number" name="query.lessQuantity" value="${query.lessQuantity }" class="Wdate form-control" digits="true"/>
							</div>

							<label class="control-label col-md-1 type-label">过滤库存为0</label>
							<div class="col-md-3 type-label">
								<input style="margin-top: 10px;" name="query.isFilterZero" type="checkbox" onchange="filterZero(this)" value="${query.isFilterZero}">
							</div>
						</div>
                        <div class="form-group">
                            <label class="control-label col-md-1 type-label">可用天数范围</label>
                            <div class="col-md-3 input-group type-label">
                                <input type="number" name="query.thanCanUseDays" value="${query.thanCanUseDays }" class="Wdate form-control" digits="true"/>
                                <span class="input-group-addon">到</span>
                                <input type="number" name="query.lessCanUseDays" value="${query.lessCanUseDays }" class="Wdate form-control" digits="true"/>
                            </div>
							<label class="control-label col-md-1">使用环节</label>
							<div class="col-md-1">
								<select name="query.useLink" class="form-control" value="${query.useLink}" id="useLink-id">
									<option value="">全部</option>
									<option value="入库">入库</option>
									<option value="出库">出库</option>
								</select>
							</div>
							<label class="control-label col-md-1">耗材状态</label>
							<div class="col-md-1">
								<select class="form-control" name="query.status" value="${query.status}">
									<option value="">全部</option>
									<option <#if query.status?? && query.status == 1>selected</#if> value="1">启用</option>
									<option <#if query.status?? && query.status == 2>selected</#if> value="2">停用</option>
								</select>
							</div>
                        </div>
					</div>
					<div>
						<div class="col-md-offset-12" style="text-align: right">
                            <@header method="auth" authCode="MATERIAL_STOCK_DOWNLOAD_INVENTORY">
							<button type="button" class="btn btn-default" onclick="downloadStocks()">
								<i class="icon-download"></i> 导出库存
							</button>
                            </@header>
							<button type="button" class="btn btn-default" onclick="formReset(this)">
								<i class="icon-refresh"></i> 重置
							</button>
							<button type="submit" class="btn blue">
								<i class="icon-search"></i> 查询
							</button>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>

		<div class="row">
			<div class="col-md-12" id="task-list-warp">
				<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
					<thead>
					<tr>
						<th><label class="checkbox-inline"><input type="checkbox" name="checkAll"> 全选</label></th>
						<th>耗材型号</th>
						<th>耗材名称</th>
                        <th>可用天数</th>
                        <th>在途</th>
						<th>入库中</th>
						<th>库存数量</th>
						<th>上月日平均用量</th>
						<th>上月出库数量</th>
						<th>最近修改人</th>
						<th>最近入库时间</th>
					</tr>
					</thead>
					<tbody>
					<#list domain.whPackagingMaterialStocks as stock>
					<tr>
						<td>
							<label class="checkbox-inline">
								<input class="${stock.id}" name="ids" type="checkbox" value="${stock.id}">
								${stock.id}
							</label>
						</td>
						<td>${stock.materialArticleNumber}</td>
						<td>${stock.name}</td>
                        <td>${stock.canUseDays}</td>
						<td>${stock.onWayQuantity}</td>
						<td>${stock.upQuantity}</td>
						<td>${stock.surplusQuantity}</td>
						<td>${stock.articleDosage}</td>
						<td>${stock.lastMonthCheckoutQty}</td>
						<td>${util('name',stock.lastUpdatedBy)}</td>
						<td>${stock.lastUpdateDate}</td>
					</tr>
				</#list>
					<tr>
						<td colspan="4"><span class="group-inline">汇总：</span></td>
						<td><span class="group-inline">${domain.stockCount.onWayQuantityCount}</span></td>
						<td><span class="group-inline">${domain.stockCount.upQuantityCount}</span></td>
						<td><span class="group-inline">${domain.stockCount.surplusQuantityCount}</span></td>
						<td></td>
						<td></td>
					</tr>
				</tbody>
				</table>
			</div>
		</div>

		<div id="fixed-bottom" >
			<div id="pager"><ur class="pages"><li class="select"><select><option>10</option></select></ur></div>
		</div>
	</div>

	<#include "/common/footer.html">
	<img id="enlarge" style='position:absolute;width:800px;height:800px;top:10%;left:15%;display:none;'/>
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/access.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
<script type="text/javascript">
	// 分页
	var total = "${domain.page.totalCount}";
	var pageNo = "${domain.page.pageNo}";
	var pageSize = "${domain.page.pageSize}";
	$("#useLink-id").val("${domain.query.useLink}");
	$("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });
	var heights = $("body").height();
	if(heights > 910) {
		$("#fixed-bottom").addClass("fixed-bottom-height")
	}
	// 分页
	var total = "${domain.page.totalCount}";
	var pageNo = "${domain.page.pageNo}";
	var pageSize = "${domain.page.pageSize}";
	$("#pager").initPager({total: total, pageNo: pageNo, pageSize: pageSize});

	var heights = $("body").height();
	if (heights > 910) {
		$("#fixed-bottom").addClass("fixed-bottom-height")
	}
	var isFilterZero = "${domain.query.isFilterZero}";
	if (isFilterZero == "true") {
		$("input[name='query.isFilterZero']").attr('checked', true);
	}
	function filterZero(obj) {
		if ($(obj).is(':checked')) {
			$(obj).val(true);
		} else {
			$(obj).val(false);
		}
	}
	// 类型
	var typeArray = ${domain.typeList};
	$("input[name='query.type']").select2({
		data: typeArray,
		placeholder: "库存类型",
		multiple: false,
		allowClear: true
	});

	// 全选
	var checkAll = $("input[name='checkAll']");
	// 子选项
	var itemIds = $("input[name='ids']");
	checkAll.change(
			function() {
				itemIds.prop("checked", $(this).prop("checked"));
				itemIds.each(function() {
					var f = $(this).is(":checked");
					var checkClass = $(this).prop("class");
					$("." + checkClass).each(function() {
						$(this).prop("checked", f);
					})
				})
			}
	);
	// 获取选中的记录
	function getCheckedIds() {
		var checkedIds = $("input[name='ids']:checked");
		return checkedIds;
	}


	// 导出
	function downloadStocks() {
		var checkedDatas = getCheckedIds();

		var diglog = dialog({
			title: '导出',
			width: 350,
			height:100,
			url: CONTEXT_PATH + "transit/return/downloadmode",
			okValue: '确定',
			ok: function () {

				var exportWindow = $(this.iframeNode.contentWindow.document.body);

				var submitForm = exportWindow.find("#submit-form");

				var exportType = submitForm.find("input[name='exportType']:checked").val();

				var submitFormParam = submitForm.serialize();

				// 导出当前选择
				if(exportType == 3) {
					if(checkedDatas.length == 0) {
						layer.alert("请选择要操作的数据!");
						return false;
					} else if (checkedDatas.length > 300) {
						layer.alert("选择数量不能超过300!");
						return false;
					}

					submitFormParam = submitFormParam + "&" +checkedDatas.serialize();

				}

				//还原分页
				$("#page-no").val("${domain.page.pageNo}");


				var action = document.materialStockDoForm.action;
				var target = document.materialStockDoForm.target;
				var method = document.materialStockDoForm.method;
				document.materialStockDoForm.action= CONTEXT_PATH + "packagingMaterialStock/download?" + submitFormParam;
				document.materialStockDoForm.target="_blank";
				document.materialStockDoForm.method="POST";
				document.materialStockDoForm.submit();
				document.materialStockDoForm.target=target;
				document.materialStockDoForm.action=action;
				document.materialStockDoForm.method=method;

				$("#page-no").val("1");

				setTimeout(function () {
					diglog.close().remove();
				}, 100);

				return true;
			},
			cancelValue: '取消',
			cancel: function () {}
		});
		diglog.show();
	}
	function downAscpStocks(){
		var checkedDatas = getCheckedIds();
		if(checkedDatas.length == 0) {
			var param = $("#domain").serialize();

			window.open(CONTEXT_PATH + "pacStoct/downloadASCP?" + param);
		} else {
			var ids = checkedDatas.serialize();
			window.open(CONTEXT_PATH + "pacStoct/downloadASCP?" + ids);
		}
	}

</script>
</body>
</html>