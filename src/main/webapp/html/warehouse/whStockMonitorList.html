<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#record-list td {
	vertical-align:middle;
}
.group-inline {
	color: red;
	font-size: 20px;
}
#record-list th {
	border:1px solid #FFFFFF;
}
.td-color{
	color: red;
	font-size: 20px;
}
</style>
</head>
<body>
<@header method="header" active="13060000"><#include "/ftl/header.ftl"></@header>
	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">库存</a></li>
					<li class="active">库存监控表</li>
				</ul>
			</div>
		</div>
		<div class="container-fluid" style="background-color: white;border: none">
			<#assign query=domain.query>
			<!-- END PAGE HEADER-->
			<div class="row">
				<div class="col-md-12">
					<form action="${CONTEXT_PATH}whStockMonitors/search"
						class="form-horizontal form-bordered form-row-stripped"
						method="post" modelAttribute="domain" name="whStockMonitorsForm" id="domain">
						<!-- 分页信息 -->
						<input id="page-no" type="hidden" name="page.pageNo" value="1">
						<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
						<input id="page-total" type="hidden" name="page.totalCount" value="${domain.page.totalCount}">
						<div class="form-body">
							<div class="form-group">
								<label class="control-label col-md-1">SKU</label>
								<div class="col-md-3">
									<input id="skuStr" name="query.skuStr" class="form-control" type="text" value="${query.skuStr}" placeholder="多个sku以英文逗号分割">
								</div>
                                <label class="control-label col-md-1">是否统计汇总</label>
                                <div class="col-md-3">
                                    <select class="form-control" name="query.queryTotal" value="${query.queryTotal}">
                                        <option <#if (query.queryTotal)?? && query.queryTotal == false>selected</#if> value="false">否</option>
                                        <option <#if query.queryTotal == true>selected</#if> value="true">是</option>
                                    </select>
                                </div>
                                <label class="control-label col-md-1">是否差异</label>
                                <div class="col-md-3">
                                    <select class="form-control" name="query.different" value="${query.different}">
                                        <option value="" <#if (query.different)??>selected</#if>>所有</option>
                                        <option <#if query.different == true>selected</#if> value="true">是</option>
                                        <option <#if (query.different)?? && query.different == false>selected</#if> value="false">否</option>
                                    </select>
                                </div>
							</div>
						</div>
						<div>
							<div class="col-md-offset-10" style="text-align: right">
                                <button type="button" class="btn btn-default" onclick="formReset(this)">
                                    <i class="icon-refresh"></i> 重置
                                </button>
								<button type="submit" class="btn blue">
									<i class="icon-search"></i> 查询
								</button>
                                <@header method="auth" authCode="INVETORY_CHART_DOWNLOAD">
								<button type="button" class="btn btn-default" onclick="downloadMonitors()">
									<i class="icon-download"></i> 导出
								</button>
                                </@header>
							</div>
						</div>
					</form>
				</div>
				<br/>
			</div>
		
			<div class="row">
                <div id="fixedDiv" class="col-md-12">
                    <table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
                        <colgroup>
                            <col width="3%" />
                            <col width="5%" />
                            <col width="11%" />
                            <col width="7%" />
                            <col width="5%" />
                            <col width="5%" />
                            <col width="5%" />
                            <col width="3%" />
                            <col width="3%" />
                            <col width="3%" />
                            <col width="3%" />
                            <col width="3%" />
                            <col width="3%" />
                            <col width="3%" />
                            <col width="5%" />
                            <col width="5%" />
                            <col width="5%" />
                            <col width="5%" />
                            <col width="5%" />
                            <col width="5%" />
                            <col width="5%" />
                        </colgroup>
						<thead>
                        <tr>
                            <th>选择</th>
                            <th>SKU</th>
                            <th>商品名称</th>
                            <th>库位</th>
                            <th>初始库存</th>
                            <th>总入库</th>
                            <th>总出库</th>
                            <th>结余</th>
                            <th>系统</th>
                            <th>差异</th>
                            <th>采购入库</th>
                            <th>海外退件</th>
                            <th>调拨入库</th>
                            <th>外借入库</th>
                            <th>盘增</th>
                            <th>盘减</th>
                            <th>调拨出库</th>
                            <th>海外仓出库</th>
                            <th>交运出库</th>
                            <th>外借出库</th>
                            <th>报废出库</th>
                            <th>不良品出库</th>
                            <th>滞销品<br>退换货出库</th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="col-md-12" id="task-list-warp">
                    <table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                        <colgroup>
                            <col width="3%" />
                            <col width="5%" />
                            <col width="11%" />
                            <col width="7%" />
                            <col width="5%" />
                            <col width="5%" />
                            <col width="5%" />
                            <col width="3%" />
                            <col width="3%" />
                            <col width="3%" />
                            <col width="3%" />
                            <col width="3%" />
                            <col width="3%" />
                            <col width="3%" />
                            <col width="5%" />
                            <col width="5%" />
                            <col width="5%" />
                            <col width="5%" />
                            <col width="5%" />
                            <col width="5%" />
                            <col width="5%" />
                        </colgroup>
                        <thead>
                        <tr>
                            <th>选择</th>
                            <th>SKU</th>
                            <th>商品名称</th>
                            <th>库位</th>
                            <th>初始库存</th>
                            <th>总入库</th>
                            <th>总出库</th>
                            <th>结余</th>
                            <th>系统</th>
                            <th>差异</th>
                            <th>采购入库</th>
                            <th>海外退件</th>
                            <th>调拨入库</th>
                            <th>外借入库</th>
                            <th>盘增</th>
                            <th>盘减</th>
                            <th>调拨出库</th>
                            <th>海外仓出库</th>
                            <th>交运出库</th>
                            <th>外借出库</th>
                            <th>报废出库</th>
                            <th>不良品出库</th>
                            <th>滞销品<br>退换货出库</th>
                        </tr>
                        </thead>
                        <tbody>
						<tbody>
							<#list domain.whStockMonitorss as stockMonitors>
								<tr>
                                    <td>
                                        <label class="checkbox-inline"> <input name="skuList" id="skuList" value="${stockMonitors.sku}" text-align="center" type="checkbox" />
                                        </label>
                                    </td>
									<td>${stockMonitors.sku}</td>
									<td>${stockMonitors.skuName}</td>
									<td>${stockMonitors.location}</td>
									<td>${stockMonitors.originalSurplusQuantity}</td>
									<td>${stockMonitors.checkInTotal}</td>
									<td>${stockMonitors.checkOutTotal}</td>
									<td>${stockMonitors.balanceQuantity}</td>
									<td>${stockMonitors.systemQuantity}</td>
									<td>${stockMonitors.diffQuantity}</td>
									<td>${stockMonitors.checkInUpQuantity}</td>
									<td>${stockMonitors.abroadReturnUpQuantity}</td>
									<td>${stockMonitors.allocationCheckInUpQuantity}</td>
									<td>${stockMonitors.lendInQuantity}</td>
									<td>${stockMonitors.increaseQuantity}</td>
									<td>${stockMonitors.decreaseQuantity}</td>
									<td>${stockMonitors.allocationOutQuantity}</td>
									<td>${stockMonitors.asnQuantity}</td>
									<td>${stockMonitors.deliverQuantity}</td>
                                    <td>${stockMonitors.lendQuantity}</td>
                                    <td>${stockMonitors.scrapQuantity}</td>
                                    <td>${stockMonitors.badProductQuantity}</td>
                                    <td>${stockMonitors.rfoQuantity}</td>
								</tr>
							</#list>
                            <#if query.queryTotal == true>
                                <tr class="td-color">
                                    <td colspan="4">汇总</td>
                                    <td>${domain.whStockMonitors.originalSurplusQuantity}</td>
                                    <td>${domain.whStockMonitors.checkInTotal}</td>
                                    <td>${domain.whStockMonitors.checkOutTotal}</td>
                                    <td>${domain.whStockMonitors.balanceQuantity}</td>
                                    <td>${domain.whStockMonitors.systemQuantity}</td>
                                    <td>${domain.whStockMonitors.diffQuantity}</td>
                                    <td>${domain.whStockMonitors.checkInUpQuantity}</td>
                                    <td>${domain.whStockMonitors.abroadReturnUpQuantity}</td>
                                    <td>${domain.whStockMonitors.allocationCheckInUpQuantity}</td>
                                    <td>${domain.whStockMonitors.lendInQuantity}</td>
                                    <td>${domain.whStockMonitors.increaseQuantity}</td>
                                    <td>${domain.whStockMonitors.decreaseQuantity}</td>
                                    <td>${domain.whStockMonitors.allocationOutQuantity}</td>
                                    <td>${domain.whStockMonitors.asnQuantity}</td>
                                    <td>${domain.whStockMonitors.deliverQuantity}</td>
                                    <td>${domain.whStockMonitors.lendQuantity}</td>
                                    <td>${domain.whStockMonitors.scrapQuantity}</td>
                                    <td>${domain.whStockMonitors.badProductQuantity}</td>
                                    <td>${domain.whStockMonitors.rfoQuantity}</td>
                                </tr>
                            </#if>
						</tbody>
					</table>
				</div>
			</div>
		</div>	

		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
	<script type="text/javascript">
		// 分页
		var total = "${domain.page.totalCount}";
		var pageNo = "${domain.page.pageNo}";
		var pageSize = "${domain.page.pageSize}";
		$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

        var heights = $("body").height();
        if(heights>910){
            $("#fixed-bottom").addClass("fixed-bottom-height")
        }

        function getCheckedSkus() {

            var skuList = $("#task-list").find("input[name='skuList']:checked");
            var checkSkus = "";
            for (var i = 0; i < skuList.length; i++) {
                var sku = skuList[i].value;
                if (i == 0) {
                    checkSkus += sku;
                } else {
                    checkSkus += "," + sku;
                }
            }
            return checkSkus;
        }

        function downloadMonitors() {
            var diglog = dialog({
                title: '导出',
                width: 350,
                height:100,
                url: CONTEXT_PATH + "whStockMonitors/downloadmode",
                okValue: '确定',
                ok: function () {
                    var submitFormParam = $('#domain').serialize();
                    var exportWindow = $(this.iframeNode.contentWindow.document.body);

                    var submitForm = exportWindow.find("#submit-form");

                    var exportType = submitForm.find("input[name='exportType']:checked").val();

                    submitFormParam = submitFormParam  + "&exportType=" + exportType

                    // 导出当前选择
                    // 导出当前选择
                    if(exportType == 3) {
                        if(getCheckedSkus().length == 0) {
                            layer.alert("请选择要操作的数据!");
                            return false;
                        }
                        $('#skuStr').val(getCheckedSkus());
                        $('#skuStr').val('');
                    } else {
                        //还原分页
                        $("#page-no").val("${domain.page.pageNo}");
                        $("#page-no").val("${domain.page.pageSize}");
                    }
                    $.post(CONTEXT_PATH + "whStockMonitors/download", submitFormParam, function(data){
                        if (data.status == 200) {
                            if (data.message==null || data.message==''){
                                layer.alert('成功',function (index) {
                                    layer.close(index);
                                    diglog.close().remove();
                                    location.reload();
                                });
                            }else{
                                customizeLayer(data.message);
                            }
                        } else {
                            customizeLayer(data.message);
                        }
                    });
                    return true;
                },
                cancelValue: '取消',
                cancel: function () {}
            });
            diglog.show();
        }
	</script>
</body>
</html>