<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<link rel="stylesheet" href="${CONTEXT_PATH}css/bootstrap.min.css">
		</link>
		<#include "/common/include.html">
		<style type="text/css">
			.modal-body input {
				width: 240px;
			}
			#creationType{
				margin-left: 100px;
				margin-top: 20px;
			}
			#creationType  input{
				margin-left: 30px;
				width: 30px;
				height: 14px;
			}
			#creationContent{
				text-align: center;
				width: 90%;
				height: 110px;
				background-color:rgba(242, 242, 242, 1);
				border: 1px solid #dcdcdc;
				margin-left: 100px;
				margin-top: 20px;
			}
			#creationContent .form-group{
				margin-top: 26px;
			}


			#inventoryLocationContent{
				margin-top: 30px;
				margin-left: 100px;
				width: 90%;
				height: 366px;
				background-color:rgba(242, 242, 242, 1);
				border: 1px solid #dcdcdc;
				overflow: auto;
				position: sticky;
				top: 0;
			}
			#inventoryLocation-list thead{
				position: sticky;
				top: 0;
			}




		</style>
	</head>

	<body>
	<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>
		<div id="page" style="background-color: rgb(231, 237, 248)">
			<div class="row">
				<div class="col-md-12" style="padding: 0">
					<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
						<li>
							<a href="#">盘点</a>
						</li>
						<li class="active">创建盘点库位</li>
					</ul>
				</div>
			</div>

			<input id="warehouseType-id" type="hidden" name="locationWarehouseType" value="${domain.locationWarehouseType}">
			<!-- 内容 -->
			<div class="container-fluid" style="background-color: white;border: none">
				<div class="row">
					<div class="col-md-12">
						<div id="creationType" class="form-body">
							<div class="form-group">
								<span>创建方式：</span>
								<label>
									<input type="radio" checked="checked" name="creationType" value="area" onclick="toggleCreationType('area')"> 按照库区创建
								</label>
								<label>
									<input type="radio" name="creationType" value="pickingLocation" onclick="toggleCreationType('pickingLocation')"> 按照拣货库位创建
								</label>
								<label>
									<input type="radio" name="creationType" value="specifiedLocation" onclick="toggleCreationType('specifiedLocation')"> 指定库位
								</label>

								<span style="margin-left: 200px">盘点次数：</span>
								<label>
									<select class="form-control" id="inventoryNumber" name="inventoryNumber" style="width: 200px">
										<option value="1">1</option>
										<option value="2">2</option>
										<option value="3">3</option>
										<option value="4" selected>4</option>
									</select>
								</label>
							</div>
						</div>
						<div id="creationContent" class="form-body">
							<div class="form-group" id="areaContent">
								<label class="control-label col-md-1">区域</label>
								<div class="col-md-2" >
									<select class="form-control" id = "locationRegion-id" onchange="locationRegionFunction(this)">
										<option vlaue=""></option>
										<#list domain.locationRegionList as locationRegion>
											<#if query.locationRegion == locationRegion>
												<option selected="selected" value="${locationRegion}">${locationRegion}</option>
											<#else>
												<option value="${locationRegion}">${locationRegion}</option>
											</#if>
										</#list>
									</select>
								</div>

								<label class="control-label col-md-1">开始通道</label>
								<div class="col-md-2">
									<input class="form-control"  type="text" name="query.locationAisle" id="fromLocationAisle" value="${query.locationAisle}">
								</div>
								<label class="control-label col-md-1">结束通道</label>
								<div class="col-md-2">
									<input class="form-control"  type="text" name="query.locationAisle" id="toLocationAisle" value="${query.locationAisle}">
								</div>

								<button  type="button" class="btn btn-default" onclick="addLocation(1)">
									按条件增加盘点库位
								</button>
								<button type="button" class="btn btn-default" onclick="updateLocation(1)">
									按条件更新盘点库位
								</button>
							</div>

							<div id="pickingLocationContent" class="form-group" style="display: none;">

								<label class="control-label col-md-1">开始库位</label>
								<div class="col-md-2">
									<input class="form-control"  type="text" id="fromLocation">
								</div>

								<label class="control-label col-md-1">结束库位</label>
								<div class="col-md-2">
									<input class="form-control"  type="text" id="toLocation">
								</div>

								<button  type="button" class="btn btn-default" onclick="addLocation(2)">
									按条件增加盘点库位
								</button>
								<h5 style="text-align: left; margin-left: 40px; color: #169BD5;"> 说明：请按照拣货库区配置开始-结束库位，拣货库区排序
									[ Y, D, E, F, G, H, J, K, L, M ]
								</h5>


							</div>

							<div id="specifiedLocationContent"  class="form-group" style="display: none;">

								<label class="control-label col-md-1"></label>
								<div class="col-md-2">
									<input style="width: 500px; height: 40px; border: 1px solid #dcdcdc; margin-left: 100px" type="text" id="locationLabel"  placeholder="请输入库位,多个请用英文逗号分隔">
								</div>

								<button  type="button" class="btn btn-default" onclick="addLocation(3)">
									按条件增加盘点库位
								</button>
							</div>
						</div>
						<div id="inventoryLocationContent">
							<table class="table table-bordered table-hover table-condensed" id="inventoryLocation-list">
								<thead>
								<tr>
									<th>库位</th>
									<th>SKU数</th>
								</tr>
								</thead>
								<tbody>
								</tbody>
							</table>
						</div>
						<div class="form-group" style="margin-top: 30px">
							<div class="col-md-offset-10 col-md-2">
								<button type="button" class="btn btn-success" onclick="confirmAction()">确定</button>
								<a type="button" class="btn btn-danger" href="${CONTEXT_PATH}warehouse/inventoryTasks/search">取消</a>
							</div>
						</div>
					</div>
				</div>
			</div>
			<#include "/common/footer.html">
		</div>

		<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
		<script type="text/javascript">
			var radio;
			var oldFromLocationAisle;
			var oldToLocationAisle;
			var oldLocationLabel;

			// 默认显示按照库区创建的内容
			window.onload = function() {
				toggleCreationType('area');
				radio='area';
			}

			function toggleCreationType(type) {
				if (radio!=type) {
					$("#inventoryLocation-list tbody").empty();
				}
				document.getElementById('areaContent').style.display = (type === 'area') ? 'block' : 'none';
				document.getElementById('pickingLocationContent').style.display = (type === 'pickingLocation') ? 'block' : 'none';
				document.getElementById('specifiedLocationContent').style.display = (type === 'specifiedLocation') ? 'block' : 'none';
				radio=type;
				oldFromLocationAisle='';
				oldToLocationAisle='';
				oldLocationLabel='';
			}

			function locationRegionFunction (obj){
				var html = "<option vlaue=''></option>";
				$("#locationAisle-id").html(html);
				var warehouseId = $("#warehouseType-id").val();
				var regionId = $(obj).val();
				if (warehouseId && regionId) {
					$.post(CONTEXT_PATH + "warehouse/locations/seleteAisle", {warehouseId: warehouseId,regionId: regionId}, function(data) {
						if (data.status == 200) {
							var dataJson = data.body[regionId];
							$("input[name='query.locationAisle']").val("").select2({
								data : JSON.parse(dataJson),
								placeholder : "通道",
								allowClear : true
							});
						} else {
							customizeLayer(data.message);
						}
					});
				}
				oldFromLocationAisle='';
				oldToLocationAisle='';
			}

			function addLocation(type){
				var locationRegionId = $("#locationRegion-id").val();
				var fromLocationAisle = $("#fromLocationAisle").val();
				var toLocationAisle = $("#toLocationAisle").val();
				if (type == 1 && (!locationRegionId || !fromLocationAisle || !toLocationAisle)){
					layer.alert("区域、开始通道、结束通道不能为空！");
					return;
				}
				if (type == 1  && (fromLocationAisle == oldFromLocationAisle || toLocationAisle == oldToLocationAisle)){
					layer.alert("该通道已经添加过盘点库位，请勿重复添加！");
					return;
				}

				var fromLocation = $("#fromLocation").val();
				var toLocation = $("#toLocation").val();
				if (type == 2 && (!fromLocation || !toLocation)){
					layer.alert("开始库位、结束库位不能为空！");
					return;
				}
				if (type == 2  && (fromLocation == oldFromLocationAisle || toLocation == oldToLocationAisle)){
					layer.alert("该库位已经添加过盘点库位，请勿重复添加！");
					return;
				}

				var locationLabel = $("#locationLabel").val();
				if (type == 3 && !locationLabel){
					layer.alert("请输入库位！");
					return;
				}
				if (type==3  && oldLocationLabel==locationLabel){
					layer.alert("该库位已经添加过盘点库位，请勿重复添加！");
					return;
				}
				if (type==2){
					fromLocationAisle=fromLocation;
					toLocationAisle=toLocation;
				}
				if (type==3){
					locationRegionId=locationLabel;
				}
				$.ajax({
					url: CONTEXT_PATH + "warehouse/pickInventoryDemands/queryFloorLocation",
					type: "POST",
					data: {type: type,locationRegionId:locationRegionId,
						fromLocationAisle:fromLocationAisle,toLocationAisle:toLocationAisle},
					success: function(data){
						debugger;
						if (!data.success){
							layer.alert(data.errorMsg, "error");
						}else{
							var dataJson = data.result;
							var fragment = document.createDocumentFragment();
							for (var i = 0; i < dataJson.length; i++) {
								var tr = document.createElement("tr");
								var td1 = document.createElement("td");
								td1.textContent = dataJson[i].location;
								var td2 = document.createElement("td");
								td2.textContent = dataJson[i].categoryActual;
								tr.appendChild(td1);
								tr.appendChild(td2);
								fragment.appendChild(tr);
							}
							$("#inventoryLocation-list tbody")[0].appendChild(fragment);

							if (fromLocationAisle) {
								oldFromLocationAisle=fromLocationAisle;
								oldToLocationAisle=toLocationAisle;
							}
							if (type==3 && locationLabel){
								oldLocationLabel=locationLabel;
							}
						}
					}
				});
			}

			//更新按钮
			function updateLocation(type){
				var locationRegionId = $("#locationRegion-id").val();
				var fromLocationAisle = $("#fromLocationAisle").val();
				var toLocationAisle = $("#toLocationAisle").val();
				if (type == 1 && (!locationRegionId || !fromLocationAisle || !toLocationAisle)){
					layer.alert("区域、开始通道、结束通道不能为空！");
					return;
				}

				var fromLocation = $("#fromLocation").val();
				var toLocation = $("#toLocation").val();
				if (type == 2 && (!fromLocation || !toLocation)){
					layer.alert("开始库位、结束库位不能为空！");
					return;
				}

				var locationLabel = $("#locationLabel").val();
				if (type == 3 && !locationLabel){
					layer.alert("请输入库位！");
					return;
				}
				if (type==2){
					fromLocationAisle=fromLocation;
					toLocationAisle=toLocation;
				}
				if (type==3){
					locationRegionId=locationLabel;
				}
				$.ajax({
					url: CONTEXT_PATH + "warehouse/pickInventoryDemands/queryFloorLocation",
					type: "POST",
					data: {type: type,locationRegionId:locationRegionId,
						fromLocationAisle:fromLocationAisle,toLocationAisle:toLocationAisle},
					success: function(data){
						if (!data.success){
							layer.alert(data.errorMsg, "error");
						}else{
							$("#inventoryLocation-list tbody").empty();
							var dataJson = data.result;
							var fragment = document.createDocumentFragment();
							for (var i = 0; i < dataJson.length; i++) {
								var tr = document.createElement("tr");
								var td1 = document.createElement("td");
								td1.textContent = dataJson[i].location;
								var td2 = document.createElement("td");
								td2.textContent = dataJson[i].categoryActual;
								tr.appendChild(td1);
								tr.appendChild(td2);
								fragment.appendChild(tr);
							}
							$("#inventoryLocation-list tbody")[0].appendChild(fragment);

							if (fromLocationAisle) {
								oldFromLocationAisle=fromLocationAisle;
								oldToLocationAisle=toLocationAisle;
							}
							if (type==3 && locationLabel){
								oldLocationLabel=locationLabel;
							}
						}
					}
					,error: function(){
						layer.alert("网络异常，请稍后再试！", "error");
					}
				});
			}

			//确认按钮
			function confirmAction(){
				debugger;
				var locationList = [];
				$("#inventoryLocation-list tbody tr").each(function(){
					var location = $(this).find("td").eq(0).text();
					locationList.push(location);
				});
				if (locationList.length == 0){
					layer.alert("内容为空不能提交！");
					return;
				}
				var inventoryNumber = $("#inventoryNumber").val();
				if (!inventoryNumber){
					layer.alert("请选择盘点次数！");
					return;
				}else{
					layer.confirm("当前盘点次数为"+inventoryNumber+"，请确认是否生成任务？", {
						btn: ['是','否']
					}, function(){
						createTask(locationList,inventoryNumber);
					});
				}
			}

			function createTask(locationList,inventoryNumber){
				locationList=JSON.stringify(locationList);
				$.ajax({
					url: CONTEXT_PATH + "warehouse/pickInventoryDemands/createInventoryTask",
					type: "POST",
					data: {locationList:locationList,inventoryNumber:inventoryNumber},
					success: function(data){
						if (!data.success){
							layer.alert(data.errorMsg, "error");
						}else{
							layer.alert('创建盘点任务成功',function (index) {
								layer.close(index);
								window.location.href = CONTEXT_PATH + "warehouse/inventoryTasks/search";
							});
						}
					}
					,error: function(){
						layer.alert("网络异常，请稍后再试！", "error");
					}
				});
			}
		</script>
	</body>
</html>