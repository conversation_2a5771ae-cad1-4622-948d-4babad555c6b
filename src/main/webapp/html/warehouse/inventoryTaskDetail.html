<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<#include "/common/include.html">
		<style type="text/css">
			.modal-body input {
				width: 240px;
			}
			
			.return-model {
				padding: 12px;
			}
			
			#return-info-btn {
				color: #ffff;
				background-color: #5bc0de;
				padding: 3px;
			}
		</style>
	</head>

	<body>
		<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>

		<div id="page" style="background-color: rgb(231, 237, 248)">
			<div class="row">
				<div class="col-md-12" style="padding: 0">
					<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
						<li>
							<a href="#">盘点</a>
						</li>
						<li class="active">盘点任务明细</li>
					</ul>
				</div>
			</div>

			<!-- 内容 -->
			<div class="container-fluid" style="background-color: white;border: none">
				<div class="row">
					<div class="col-md-12">
						<#assign task = domain.whInventoryTask>
						<form action="${CONTEXT_PATH}warehouse/inventoryTaskItems/search" class="form-horizontal form-bordered form-row-stripped" method="post" modelAttribute="domain" name="inventoryTaskItemForm" id="domain">
							<!-- 分页信息 -->
							<input id="page-no" type="hidden" name="page.pageNo" value="1">
							<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">

							<div class="form-body">
								<div class="form-group">
									<label class="pull-left control-label col-md-2" style="font-size: 20px;font-weight: bold;" >盘点任务【${task.taskNo}】</label>
									<label class="control-label col-md-1">盘点过程：${task.taskLevelName}</label>
									
									<label class="control-label col-md-1">任务状态：${task.statusName}</label>
									<label class="control-label col-md-1">审核人：${util('name',task.reviewUser)}</label>
									
									<label class="control-label col-md-1">确认SKU数量：${task.confirmQuantity}</label>
									<label class="control-label col-md-5">
										历史关联盘点任务：
										<#if task.firstTask?? && task.taskLevel != 1>
											初盘=${task.firstTask.taskNo};   
										</#if>
										<#if task.repeatTask?? && task.taskLevel != 3>
											复盘=${task.repeatTask.taskNo};
										</#if>
										<#if task.finallyTask?? && task.taskLevel != 5>
											终盘=${task.finallyTask.taskNo};
										</#if>
										<#if task.confirmTask?? && task.taskLevel != 7>
											确认盘=${task.confirmTask.taskNo};
										</#if>
									</label>
									
								</div>
								<div class="form-group">
									<label class="control-label col-md-1"><br /></label>
								</div>
								
							</div>
							<div> 
								<div class="pull-left" style="margin-bottom: 10px;">
									<button type="button" class="btn btn-default" onclick="batchDiscard()">
										<i class="icon-remove"></i>废弃盘点需求
									</button>
								</div>
							</div>
						</form>
					</div>
					<br/>
				</div>
    
				<div class="row">
					<div class="col-md-12">
						<!-- 内容 -->
						<table class="table table-bordered table-hover table-condensed" id="pickInventoryDemand-list">
							<colgroup>
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
							</colgroup>
							<thead>
								<tr>
									<th><input type="checkbox" id="check-all" name="checkAll">全选</th>
									<th>SKU</th>												
									<th>名称</th>
									<th>库存ID</th>
									<th>库位</th>
									<th>盘点类型</th>
									<th>初盘数量</th>
									<th>初盘差异</th>
									<th>初盘人/时间</th>
									<th>复盘数量</th>
									<th>复盘差异</th>
									<th>复盘人/时间</th>
									<th>终盘数量</th>
									<th>终盘差异</th>
									<th>终盘人/时间</th>
									<th>确认时<br/>库位库存</th>
									<th>确认数量</th>
									<th>确认人/时间</th>
									<th>结果</th>
									<th>操作</th>
								</tr>
							</thead>
							<tbody>
									<#list task.whInventoryTaskItems as item>
										<td>
											<input type="checkbox" class="ids" value="${item.id}" name="ids"> ${item.id}
											<input id="${item.id}_status" type="hidden" name="" value="${item.status}"/>
										</td>
										<td>${item.sku}</td>
										<td>${item.skuName}</td>
										<td>${item.stockId}</td>
										<td>${item.locationNumber}</td>
										<td>${task.taskTypeName}</td>
										<td>${item.inventoryQuantity}</td>
										<td>${item.diffQuantity}</td>
										<td>
											<#if task.taskLevel gte 1>
												${util('name',task.firstTask.inventoryUser)}
													<br />
												${task.firstTask.inventoryDate}
											</#if>
										</td>
										
										<td>${item.repeatQuantity}</td>
										<td>${item.repeatDiffQuantity}</td>
										<td>
											<#if task.taskLevel gte 3>
												${util('name',task.repeatTask.inventoryUser)}
													<br />
												${task.repeatTask.inventoryDate}
											</#if>
										</td>
										
										<td>${item.finallyQuantity}</td>
										<td>${item.finallyDiffQuantity}</td>
										<td>
											<#if task.taskLevel gte 5>
												${util('name',task.finallyTask.inventoryUser)}
													<br />
												${task.finallyTask.inventoryDate}
											</#if>
										</td>
										
										<td>
											<#if item.confirmQuantity?? >
												${item.confirmQuantity - item.confirmDiff}
											</#if>
										</td>
										<td>${item.confirmQuantity}</td>
										<td>
											<#if task.taskLevel gte 7>
												${util('name',task.confirmTask.inventoryUser)}
													<br />
												${task.confirmTask.inventoryDate}
											</#if>
										</td>
										<td>${item.confirmDiff}</td>
										<td>
											<button type="button" class="btn btn-info btn-xs" onclick="viewLog(${item.id}, 'whInventoryTaskItem')" style="margin: 5px">日志</button>
										</td>
									</tr>
								</#list>
							</tbody>
						</table>
						<!-- 内容end -->
					</div>
				</div>
				<div id="fixed-bottom">
					<div id="pager"></div>
				</div>
			</div>
			<#include "/common/footer.html">
		</div>
		<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
		<script type="text/javascript">
			var heights = $("body").height();
			if(heights > 910) {
				$("#fixed-bottom").addClass("fixed-bottom-height")
			}
			
			// 全选
			var checkAll = $("input[name='checkAll']");

			// 子选项
			var itemIds = $("input[name='ids']");
			checkAll.change(
				function() {
					itemIds.prop("checked", $(this).prop("checked"));
					itemIds.each(function() {
						var f = $(this).is(":checked");
						var checkClass = $(this).prop("class");
						$("." + checkClass).each(function() {
							$(this).prop("checked", f);
						})
					})
				}
			);
			// 获取选中的数据
			function getCheckedIds() {
				var checkedIds = $("input[name='ids']:checked");
				return checkedIds;
			}
			
			// 废弃盘点需求
			function batchDiscard(){
				var checkedDatas = getCheckedIds();
				if(checkedDatas.length == 0) {
					layer.alert("请先勾选需要废弃的盘点任务", "error");
					return;
				} else {
					var ids = "";
					for(var i = 0; i < checkedDatas.length; i++) {
						var check = checkedDatas[i];
						var id = $(check).val();
						var status = $("#"+id+"_status").val();
						if (status == '20') {
							layer.alert("勾选的盘点任务已有废弃，请重新选择", "error");
							return;
						}
						if (status == '7') {
							layer.alert("勾选的盘点任务已盘点完成，请重新选择", "error");
							return;
						}
						if (status != '3' && status != '1') {
							layer.alert("只能废弃未完成的任务，请重新选择", "error");
							return;
						}
						ids += id;
						if(i != checkedDatas.length - 1) {
							ids += ",";
						}
					}
					var diglog = dialog({
						title: '确定废弃盘点任务？',
						width: 350,
						height: 100,
						url: CONTEXT_PATH + "warehouse/pickInventoryDemands/discardInventoryDemandType",
						onshow: function() {},
						okValue: '确定',
						ok: function() {
							var exportWindow = $(this.iframeNode.contentWindow.document.body);
					    	var submitForm = exportWindow.find("#submit-form");
					    	var submitFormParam = submitForm.serialize();
					    	
					    	var type = submitForm.find("input[name='discardType']:checked").val();
							// type = "reset";
							// type = "discard";
							if(type == null || type ==''){
								layer.alert("请选择类型!", "error");
								return false;
							}
							$.ajax({
								url:CONTEXT_PATH +"warehouse/inventoryTaskItems/batchDiscard",
								type:"POST",
								data:{
									"ids":ids, "type":type
								},
								success : function(response){
									var message = '';
									if (response.message != null) {
										message = response.message
									}
									if (response.status == '500') {
										customizeLayer('废弃盘点任务失败：'+message, 'error');
										return;
									} else if (response.status == '200') {
										layer.confirm('已经成功废弃盘点任务！',{
											icon: 1,
											btn: ['确定']
										},function () {
											window.location.reload();
										})
									}
								},
					             error:function(){
					                 customizeLayer('操作失败!', 'error');
					             }
							});
							setTimeout(function() {
								diglog.close().remove();
							}, 100);
							return false;
						},
						cancelValue: '取消操作',
						cancel: function() {}
					}).showModal();
				}
			}
		</script>
	</body>

</html>