<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
    <head>
        <meta charset="UTF-8">
        <title>易世通达仓库管理系统</title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <#include "/common/include.html">
        <link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet"
              type="text/css"/>
        <style type="text/css">
            .modal-body input {
                width: 240px;
            }

            .table-top {
                margin-bottom: 0px;
                margin-top: 20px;
            }

            #item-list {
                margin-bottom: 80px;
            }

            .pick-content-button {
                margin-top: 20px;
            }

            .rule-pick-label input {
                width: 40px;
            }

            .red-flag {
                color: red;
            }
        </style>
    </head>
    <body>
        <@header method="header" active="14020000" ><#include "/ftl/header.ftl"></@header>
        <div id="page" style="background-color: rgb(231, 237, 248)">
            <div class="row">
                <div class="col-md-12" style="padding: 0">
                    <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                        <li><a href="#">存货管理</a></li>
                        <li class="active">存货迁移任务规则</li>
                    </ul>
                </div>
            </div>

            <!-- 内容 -->
            <div class="container-fluid" style="background-color: white;border: none">
                <div class="row">
                    <div class="col-md-12">
                        <form action="${CONTEXT_PATH}prestorageMigration/rule/init"
                              class="form-horizontal form-bordered form-row-stripped" method="get"
                              modelAttribute="domain" name="prestoreStockTransferList" id="domain">
                            <!-- 分页信息 -->
                            <input id="page-no" type="hidden" name="page.pageNo" value="1">
                            <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
                            <div>
                                <div class="pull-left">
                                    <@header method="auth" authCode="PRESTORAGE_STOCK_MIGRATION_RULE_CREATE">
                                    <button type="button" class="btn  btn-default" onclick="createRule()">
                                        <i class="icon-plus"></i>创建规则
                                    </button>
                                    </@header>
                                    <@header method="auth" authCode="PRESTORAGE_STOCK_MIGRATION_RULE_FORBID">
                                    <button type="button" class="btn  btn-default" onclick="forbidRule()">
                                        禁用规则
                                    </button>
                                    </@header>
                                    <@header method="auth" authCode="PRESTORAGE_STOCK_MIGRATION_RULE_START">
                                    <button type="button" class="btn  btn-default" onclick="startRule()">
                                        启用规则
                                    </button>
                                    </@header>
                                    <@header method="auth" authCode="PRESTORAGE_STOCK_MIGRATION_RULE_CALCULATE">
                                    <button type="button" class="btn  btn-default" <#if domain.hasCalculateTaskDoing>disabled="disabled"</#if> onclick="calculateTask(this)">
                                        计算存货迁移
                                    </button>
                                    </@header>
                                </div>
                            </div>
                        </form>
                    </div>
                    <br/>
                </div>

                <div class="row">
                    <div id="fixedDiv" class="col-md-12">
                        <table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
                            <colgroup>
                                <col width="3%"/>
                                <col width="3%"/>
                                <col width="8%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                            </colgroup>
                            <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="check-all" name="checkAll" onclick="checkAll(this)">ID
                                </th>
                                <th>仓库</th>
                                <th>SKU</th>
                                <th>拣货库存水位值</th>
                                <th>优先级</th>
                                <th>创建人</th>
                                <th>创建时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="col-md-12" id="task-list-warp">
                        <div class="col-md-12">
                            <!-- 内容  -->
                            <table class="table table-striped table-bordered table-hover table-condensed"
                                   id="task-list">
                                <colgroup>
                                    <col width="3%"/>
                                    <col width="3%"/>
                                    <col width="8%"/>
                                    <col width="5%"/>
                                    <col width="5%"/>
                                    <col width="5%"/>
                                    <col width="5%"/>
                                    <col width="5%"/>
                                    <col width="5%"/>
                                </colgroup>
                                <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="check-all" name="checkAll" onclick="checkAll(this)">ID
                                    </th>
                                    <th>仓库</th>
                                    <th>SKU</th>
                                    <th>拣货库存水位值</th>
                                    <th>优先级</th>
                                    <th>创建人</th>
                                    <th>创建时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <#list domain.migrationRuleVOS as item>
                                    <tr>
                                        <td>
                                            <label class="checkbox-inline">
                                                <input name="ids" type="checkbox" value="${item.id}">
                                                ${item.id}
                                            </label>
                                        </td>
                                        <td>${item.warehouseTypeStr}</td>
                                        <td>
                                            <#if item.ruleType == 2>
                                                全部sku
                                            <#elseif item.ruleType == 0>
                                                指定SKU:${item.skus.skuStr}
                                            <#else>
                                                <#if item.region.regionStr??>
                                                    库区：${item.region.regionStr}
                                                    <br/>
                                                </#if>
                                                <#if item.saleAttribute.saleAttributeStr??>
                                                    SKU销售属性：${item.saleAttribute.saleAttributeStr}
                                                    <br/>
                                                </#if>
                                                <#if item.skuSaleAmounts??>
                                                    SKU销量:
                                                    <#list item.skuSaleAmounts as skuSaleAmount>
                                                        ${skuSaleAmount.saleDays}天销量${skuSaleAmount.compareSignal}${skuSaleAmount.amount}
                                                        <br/>
                                                    </#list>
                                                </#if>
                                            </#if>
                                        </td>
                                        <td>${item.pickingStockThreshold}</td>
                                        <td>${item.priority}</td>
                                        <td>${item.createPerson}</td>
                                        <td>${item.createDate}</td>
                                        <td>
                                            <#if item.status>
                                                启用
                                            <#else>
                                                禁用
                                            </#if>
                                        </td>
                                        <td>
                                            <a class="btn btn-xs btn-default"
                                               href="${CONTEXT_PATH}prestorageMigration/rule/update/init?id=${item.id}">
                                                <span class="icon-edit"></span> 编辑
                                            </a>
                                            <a class="btn btn-xs btn-info" href="javascript:void(0);" onclick="viewLog(${item.id}, 'preStockMigrationRules')">
                                                <i class="icon-book">日志</i>
                                            </a>
                                        </td>
                                    </tr>
                                </#list>
                                </tbody>
                            </table>
                            <!-- 内容end  -->
                        </div>
                    </div>
                    <div id="fixed-bottom">
                        <div id="pager"></div>
                    </div>
                </div>
                <!-- 打印弹窗 -->
                <div class="modal fade ui-popup" id="print_modal" tabindex="-1" role="dialog" aria-labelledby="organization-add-label" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content"></div>
                    </div>
                </div>
            </div>
            <#include "/common/footer.html">
    </body>
    <script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
    <script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery-ui/jquery-ui-1.10.2.custom.min.js"></script>
    <script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
    <script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
    <script type="text/javascript">

        // 分页
        var total = "${domain.page.totalCount}";
        var pageNo = "${domain.page.pageNo}";
        var pageSize = "${domain.page.pageSize}";
        $("#pager").initPager({total: total, pageNo: pageNo, pageSize: pageSize});

        //全选
        function checkAll(obj) {
            $("#task-list").find("input[name='ids']").prop("checked", $(obj).is(':checked'));
        }


        // 获取选中的记录
        function getCheckedIds() {
            var checkedIds = $("input[name='ids']:checked");
            return checkedIds;
        }

        // 创建规则
        function createRule(){
            location.href = CONTEXT_PATH + "prestorageMigration/rule/create";
        }

        // 禁用规则
        function forbidRule() {
            var checkedIds = getCheckedIds();
            if (!checkedIds || checkedIds.length == 0) {
                layer.alert("请选择要操作的数据");
                return false;
            }
            var data = [];
            $.each(checkedIds,function(index,item){
                var id = $(item).val();
                data.push({"id":parseInt(id),"status":false});
            });

            $.ajax({
                url: CONTEXT_PATH + "prestorageMigration/rule/update",
                type: "POST",
                dataType : "json",
                contentType: "application/json;charset=utf-8",
                data: JSON.stringify(data),
                success: function (result) {
                    if (result.status == 200) {
                        layer.alert(result.message||'禁用成功!');
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    } else {
                        customizeLayer(result.message, "error");
                    }
                },
                error: function () {
                    layer.alert("系统异常，操作失败!", 'error');
                }
            });
        }

        // 启用规则
        function startRule() {
            var checkedIds = getCheckedIds();
            if (!checkedIds || checkedIds.length == 0) {
                layer.alert("请选择要操作的数据");
                return false;
            }
            var data = [];
            $.each(checkedIds,function(index,item){
                var id = $(item).val();
                data.push({"id":parseInt(id),"status":true});
            });

            $.ajax({
                url: CONTEXT_PATH + "prestorageMigration/rule/update",
                type: "POST",
                dataType : "json",
                contentType: "application/json;charset=utf-8",
                data: JSON.stringify(data),
                success: function (result) {
                    if (result.status == 200) {
                        layer.alert(result.message||'启用成功!');
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    } else {
                        customizeLayer(result.message, "error");
                    }
                },
                error: function () {
                    layer.alert("系统异常，操作失败!", 'error');
                }
            });
        }
        // 用于计算存货迁移
        function calculateTask(obj) {
            $.ajax({
                url: CONTEXT_PATH + "prestorageMigration/rule/calculate",
                type: "POST",
                contentType: "application/json;charset=utf-8",
                success: function (result) {
                    if (result.status == 200) {
                        var message = '执行计算任务成功';
                        if (result.message == 'false'){
                            message = '当前别的服务正在执行计算任务,请稍后...';
                        }
                        layer.alert(message);
                        $(obj).attr("disabled",'disabled');
                    } else {
                        customizeLayer(result.message, "error");
                    }
                },
                error: function () {
                    layer.alert("系统异常，操作失败!", 'error');
                }
            });
        }
    </script>
</html>