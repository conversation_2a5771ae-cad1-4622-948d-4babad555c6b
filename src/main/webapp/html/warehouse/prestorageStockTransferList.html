<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
    <head>
        <meta charset="UTF-8">
        <title>易世通达仓库管理系统</title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <#include "/common/include.html">
        <#include "/common/webSocket.html">
        <link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet"
              type="text/css"/>
        <style type="text/css">
            .modal-body input {
                width: 240px;
            }

            .table-top {
                margin-bottom: 0px;
                margin-top: 20px;
            }

            #item-list {
                margin-bottom: 80px;
            }

            .pick-content-button {
                margin-top: 20px;
            }

            .rule-pick-label input {
                width: 40px;
            }

            .red-flag {
                color: red;
            }
        </style>
    </head>
    <body>
        <@header method="header" active="14020000" ><#include "/ftl/header.ftl"></@header>
        <div id="page" style="background-color: rgb(231, 237, 248)">
            <div class="row">
                <div class="col-md-12" style="padding: 0">
                    <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                        <li><a href="#">存货管理</a></li>
                        <li class="active">存货迁移任务</li>
                    </ul>
                </div>
            </div>

            <!-- 内容 -->
            <div class="container-fluid" style="background-color: white;border: none">
                <div class="row">
                    <div class="col-md-12">
                        <#assign query = domain.query>
                        <form action="${CONTEXT_PATH}prestorageStockTransfer/search"
                              class="form-horizontal form-bordered form-row-stripped" method="post"
                              modelAttribute="domain" name="prestoreStockTransferList" id="domain">
                            <!-- 分页信息 -->
                            <input id="page-no" type="hidden" name="page.pageNo" value="1">
                            <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
                            <div class="form-body">
                                <div class="form-group">
                                    <label class="control-label col-md-1">创建时间</label>
                                    <div class="col-md-3">
                                        <div class="input-group">
                                            <input class="form-control Wdate" type="text" name="query.fromCreateDate"
                                                   placeholder="" readonly="readonly" value="${query.fromCreateDate }"
                                                   onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                            <span class="input-group-addon">到</span>
                                            <input class="form-control Wdate" type="text" name="query.toCreateDate"
                                                   placeholder="" readonly="readonly" value="${query.toCreateDate }"
                                                   onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                        </div>
                                    </div>
                                    <label class="control-label col-md-1">SKU</label>
                                    <div class="col-md-3">
                                        <input class="form-control" type="text" name="query.skuStrs"
                                               placeholder="多个使用,分割查询" value="${query.skuStrs }">
                                    </div>

                                    <label class="control-label col-md-1">任务号</label>
                                    <div class="col-md-3">
                                        <input class="form-control" type="text" name="query.orderNoStrs"
                                               placeholder="多个使用,分割查询" value="${query.orderNoStrs }">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="control-label col-md-1">状态</label>
                                    <div class="col-md-3">
                                        <input class="form-control" type="text" name="query.status"
                                               placeholder="请选择" value="${query.status }">
                                    </div>

                                    <label class="control-label col-md-1">打印状态</label>
                                    <div class="col-md-3">
                                        <input class="form-control" name="query.printStatus"
                                               placeholder="请选择" type="text" value="${query.printStatus }">
                                    </div>

                                    <label class="control-label col-md-1">领取人</label>
                                    <div class="col-md-3">
                                        <input class="form-control" name="query.acceptPersonId" placeholder="请选择"
                                               type="text"
                                               value="${query.acceptPersonId}"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-md-1">完成时间</label>
                                    <div class="col-md-3">
                                        <div class="input-group">
                                            <input class="form-control Wdate" type="text" name="query.fromCompletedDate"
                                                   placeholder="" readonly="readonly"
                                                   value="${query.fromCompletedDate }"
                                                   onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                            <span class="input-group-addon">到</span>
                                            <input class="form-control Wdate" type="text" name="query.toCompletedDate"
                                                   placeholder="" readonly="readonly" value="${query.toCompletedDate }"
                                                   onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                        </div>
                                    </div>

                                    <label class="control-label col-md-1">来源</label>
                                    <div class="col-md-3">
                                        <input class="form-control" type="text" name="query.source"
                                               placeholder="请选择" value="${query.source }">
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="pull-left">
                                    <@header method="auth" authCode="PRESTORAGE_STOCK_TRANSFER_PRINT_ORDER">
                                    <button type="button" class="btn  btn-default" onclick="printOrder()">
                                        <i class="icon-print"></i> 打印任务号
                                    </button>
                                    </@header>
                                    <@header method="auth" authCode="PRESTORAGE_STOCK_TRANSFER_ABORT_ORDER">
                                    <button type="button" class="btn  btn-default" onclick="abortOrder()">
                                        <i class="icon-eraser"></i> 废弃任务
                                    </button>
                                    </@header>
                                    <@header method="auth" authCode="PRESTORAGE_STOCK_TRANSFER_IMPORT_ORDER">
                                    <span class="btn btn-default fileinput-button">
                                        <span class="icon-plus"> 导入创建任务</span>
                                        <input type="file" name="file" onchange="importPrestorageStockTransferOrder(this)"/>
								    </span>
                                    </@header>
                                    <@header method="auth" authCode="PRESTORAGE_STOCK_TRANSFER_DOWNLOAD_MODEL">
                                    <a class="btn btn-default" href="${CONTEXT_PATH}file/execl/prestorage_migration_order_import_model.xlsx">
                                        <i class="icon-download"></i> 下载导入模板Excel
                                    </a>
                                    </@header>
                                </div>
                                <div class="col-md-offset-10" style="text-align: right">
                                    <button type="button" class="btn btn-default" onclick="formReset(this)">
                                        <i class="icon-refresh"></i> 重置
                                    </button>
                                    <button type="submit" class="btn blue">
                                        <i class="icon-search"></i> 查询
                                    </button>
                                    <@header method="auth" authCode="PRESTORAGE_STOCK_TRANSFER_DOWNLOAD">
                                    <button type="button" class="btn btn-default" onclick="download()">
                                        <i class="icon-download"></i> 导出明细
                                    </button>
                                    </@header>
                                </div>
                            </div>
                        </form>
                    </div>
                    <br/>
                </div>

                <div class="row">
                    <div id="fixedDiv" class="col-md-12">
                        <table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
                            <colgroup>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="8%"/>
                                <col width="8%"/>
                                <col width="8%"/>
                                <col width="3%"/>
                            </colgroup>
                            <thead>
                            <tr>
                                <th><input type="checkbox" id="check-all" name="checkAll" onclick="checkAll(this)">ID
                                </th>
                                <th>任务号</th>
                                <th>来源</th>
                                <th>仓库</th>
                                <th>类型</th>
                                <th>SKU</th>
                                <th>迁出库位</th>
                                <th>迁入库位</th>
                                <th>迁移数量</th>
                                <th>拣货数量</th>
                                <th>拣货差异</th>
                                <th>任务状态</th>
                                <th>打印状态</th>
                                <th>创建人<br/>创建时间</th>
                                <th>领取人<br/>领取时间</th>
                                <th>完成时间</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="col-md-12" id="task-list-warp">
                        <div class="col-md-12">
                            <!-- 内容  -->
                            <table class="table table-striped table-bordered table-hover table-condensed"
                                   id="task-list">
                                <colgroup>
                                    <col width="5%"/>
                                    <col width="5%"/>
                                    <col width="5%"/>
                                    <col width="5%"/>
                                    <col width="5%"/>
                                    <col width="5%"/>
                                    <col width="5%"/>
                                    <col width="5%"/>
                                    <col width="5%"/>
                                    <col width="5%"/>
                                    <col width="5%"/>
                                    <col width="5%"/>
                                    <col width="5%"/>
                                    <col width="8%"/>
                                    <col width="8%"/>
                                    <col width="8%"/>
                                    <col width="3%"/>
                                </colgroup>
                                <thead>
                                <tr>
                                    <th><input type="checkbox" id="check-all" name="checkAll" onclick="checkAll(this)">ID
                                    </th>
                                    <th>任务号</th>
                                    <th>来源</th>
                                    <th>仓库</th>
                                    <th>类型</th>
                                    <th>SKU</th>
                                    <th>迁出库位</th>
                                    <th>迁入库位</th>
                                    <th>迁移数量</th>
                                    <th>拣货数量</th>
                                    <th>拣货差异</th>
                                    <th>任务状态</th>
                                    <th>打印状态</th>
                                    <th>创建人<br/>创建时间</th>
                                    <th>领取人<br/>领取时间</th>
                                    <th>完成时间</th>
                                    <th>操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <#list domain.orders as item>
                                    <tr>
                                        <td>
                                            <label class="checkbox-inline">
                                                <input name="ids" type="checkbox" value="${item.id}">
                                                ${item.id}
                                            </label>
                                        </td>
                                        <td>${item.orderNo}</td>
                                        <td>${item.sourceStr}</td>
                                        <td>${item.wareHouseTypeStr}</td>
                                        <td>${item.typeStr}</td>
                                        <td>${item.skuTypeAmount}</td>
                                        <td>${item.emigrationLocationTypeAmount}</td>
                                        <td>${item.immigrationLocationTypeAmount}</td>
                                        <td>${item.migrationSkuAmount}</td>
                                        <td>${item.pickingSkuAmount!'0'}</td>
                                        <td>
                                            <#if item.pickingSkuAmount??>
                                                ${item.pickingDiscrepancy}
                                            </#if>
                                        </td>
                                        <td id="${item.id}_status">${item.statusStr}</td>
                                        <td id="${item.id}_printStatus">${item.printStatusStr}</td>
                                        <td>${util('name',item.createPerson)}<br/>${item.createDate}</td>
                                        <td>${util('name',item.acceptPerson)}<br/>${item.acceptDate}</td>
                                        <td>${item.completedDate}</td>
                                        <td>
                                            <a class="btn btn-xs btn-default"
                                               href="${CONTEXT_PATH}prestorageStockTransfer/orderDetail?orderId=${item.id}">
                                                <span class="icon-edit"></span> 查看详情
                                            </a>
                                        </td>
                                    </tr>
                                </#list>
                                </tbody>
                            </table>
                            <!-- 内容end  -->
                        </div>
                    </div>
                    <div id="fixed-bottom">
                        <div id="pager"></div>
                    </div>
                </div>
                <!-- 打印弹窗 -->
                <div class="modal fade ui-popup" id="print_modal" tabindex="-1" role="dialog" aria-labelledby="organization-add-label" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content"></div>
                    </div>
                </div>
            </div>
            <#include "/common/footer.html">
    </body>
    <script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
    <script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery-ui/jquery-ui-1.10.2.custom.min.js"></script>
    <script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
    <script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
    <script type="text/javascript">

        // 分页
        var total = "${domain.page.totalCount}";
        var pageNo = "${domain.page.pageNo}";
        var pageSize = "${domain.page.pageSize}";
        $("#pager").initPager({total: total, pageNo: pageNo, pageSize: pageSize});

        // 初始化选择项 PDA领取存货迁移任务的人员
        $.getJSON(CONTEXT_PATH + "system/saleusers/userByPermissionCode?permissionCode=290000", function (json) {
            if (json) {
                $("input[name='query.acceptPersonId']").select2({
                    data: json,
                    placeholder: "领取人",
                    multiple: false,
                    allowClear: true
                });
            } else {
                $("input[name='query.acceptPersonId']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
            }
        });

        var statusArray =  ${domain.statusSelectData};
        $("input[name='query.status']").select2({
            data: statusArray,
            placeholder: "状态",
            multiple: false,
            allowClear: true
        });


        var sourceArray =  ${domain.sourceSelectData};
        $("input[name='query.source']").select2({
            data: sourceArray,
            placeholder: "来源",
            multiple: false,
            allowClear: true
        });

        $("input[name='query.printStatus']").select2({
            data: [{id: 0, text: "未打印"}, {id: 1, text: "已打印"}],
            placeholder: "打印状态",
            multiple: false,
            allowClear: true
        });

        //全选
        function checkAll(obj) {
            $("#task-list").find("input[name='ids']").prop("checked", $(obj).is(':checked'));
        }


        // 获取选中的记录
        function getCheckedIds() {
            var checkedIds = $("input[name='ids']:checked");
            return checkedIds;
        }

        function download() {
            var uuid = getFileQueueUUID();

            var ids = getCheckedIds();

            var diglog = dialog({
                title: "存货迁移任务导出",
                width: 500,
                height: 80,
                url: CONTEXT_PATH + "prestorageStock/downloadmode",
                okValue: '确定',
                ok: function () {
                    debugger
                    var exportWindow = $(this.iframeNode.contentWindow.document.body);

                    var submitForm = exportWindow.find("#submit-form");

                    var exportType = submitForm.find("input[name='exportType']:checked").val();

                    var submitFormParam = $('#domain').serialize();

                    // 导出当前选择
                    if(exportType == 3) {
                        if(ids.length == 0) {
                            layer.alert("请选择要操作的数据");
                            return false;
                        }
                        submitFormParam = submitFormParam + "&" +ids.serialize();
                    }
                    submitFormParam = submitFormParam + "&exportType=" + exportType + "&uuid=" + uuid;

                    //还原分页
                    $("#page-no").val("${domain.page.pageNo}");

                    $.post(CONTEXT_PATH + "prestorageStockTransfer/download", submitFormParam, function(data){
                        if (data.status == 200) {
                            if (data.message==null || data.message==''){
                                layer.alert('成功',function (index) {
                                    layer.close(index);
                                    diglog.close().remove();
                                    location.reload();
                                });
                            }else{
                                customizeLayer(data.message);
                            }
                        } else {
                            customizeLayer(data.message);
                        }
                    });
                    $("#page-no").val("1");
                    return true;
                },
                cancelValue: '取消',
                cancel: function () {}
            });
            diglog.show();
        }

        function abortOrder() {
            var checkedIds = getCheckedIds();
            if (!checkedIds || checkedIds.length == 0) {
                layer.alert("请选择要操作的数据");
                return false;
            }
            var ids = checkedIds.serialize();
            $.ajax({
                url: CONTEXT_PATH + "prestorageStockTransfer/batchAbort",
                type: "POST",
                data: ids,
                success: function (result) {
                    if (result.status == 200) {
                        layer.alert(result.message||'废弃成功!');
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    } else {
                        customizeLayer(result.message, "error");
                    }
                },
                error: function () {
                    layer.alert("系统异常，操作失败!", 'error');
                }
            });
        }

        function importPrestorageStockTransferOrder(obj) {
            //检测上传文件的类型
            var filename = obj.value;

            var ext, idx;
            if (filename == '') {
                layer.alert("请选择需要上传的文件!");
                return;
            } else {
                idx = filename.lastIndexOf(".");
                if (idx != -1) {
                    ext = filename.substr(idx + 1).toUpperCase();
                    ext = ext.toLowerCase();

                    if (ext != 'xls' && ext != 'xlsx') {
                        layer.alert("只能上传.Excel类型的文件!");
                        return;
                    }
                } else {
                    layer.alert("只能上传.Excel类型的文件!");
                    return;
                }
            }

            var r = confirm("确定上传" + filename + "?");

            if (!r) {
                return;
            }

            var uploadUrl = CONTEXT_PATH + "prestorageStockTransfer/import";

            var searchUrl = $("#domain").attr("action");

            $("#domain").attr("action", uploadUrl);

            $("#domain").ajaxSubmit(function (data) {
                $("#domain").attr("action", searchUrl);
                if (data.status == 200) {
                    if (data.message) {
                        customizeLayer(data.message, 'warning');
                    } else {
                        alert("成功！");
                    }
                    setTimeout(function () {
                        reloadPage();
                    }, 2000);
                } else {
                    layer.confirm(data.message||'程序出错!导入不成功', {
                        icon: 2,
                        btn: ['确定'],
                        btn1:function () {
                            reloadPage();
                        },
                        end:function(){
                            reloadPage();
                        }
                    });
                }
            });
        }

        function reloadPage() {
            location.reload()
        }

        function printOrder() {
            var ids = getCheckedIds();

            if (!ids ||ids.length == 0){
                layer.alert("请选择要操作的数据");
                return false;
            }

            var trueArray = new Array();
            var falseArray = new Array();
            for (var i = 0; i < ids.length; i++) {
                var checked = ids[i];
                var status = $("#"+checked.value+"_status").text();
                var printStatus = $("#"+checked.value+"_printStatus").text();
                if (status.indexOf('已废弃') != -1 || status.indexOf('已完成') != -1
                    || printStatus.indexOf("已打印") != -1) {
                    falseArray.push(checked.value);
                }else {
                    trueArray.push(checked.value);
                }
            }

            if (falseArray.length>0) {
                layer.alert("选择了的:"+falseArray.length+"条不符合的列表将不会打印", 'error');
            }

            var ids = "";
            for (var i = 0; i < trueArray.length; i++) {
                ids += "ids=" + trueArray[i] + "&";
            }

            if (trueArray.length>0) {

                $('#print_modal').off('hide.bs.modal');
                $('#print_modal').on('hide.bs.modal', function () {
                    reloadPage();
                });
                $("#print_modal").removeData("bs.modal");
                $("#print_modal").modal({
                    remote: CONTEXT_PATH + "prestorageStockTransfer/printInit?" + ids
                });
            }
        }
    </script>
</html>