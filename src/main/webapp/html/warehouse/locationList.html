<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		.top_bar{
			position:fixed;top:0px;
		}
		#location-list td {
			vertical-align:middle;
			text-align:center;
		}
		
	</style>
</head>
<body>
<@header method="header" active="11030000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">库位管理</a></li>
				<li class="active">库位列表</li>
			</ul>
		</div>
	</div>

	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
	<div class="row">
		<div class="col-md-12">
		<#assign query=domain.query>
			<form action="${CONTEXT_PATH}warehouse/locations/search"
					   class="form-horizontal form-bordered form-row-stripped"
					   method="post" modelAttribute="domain" name="warehouseLocationForm" id ="domain">					
				<!-- 分页信息 -->
				<input id="page-no" type="hidden" name="page.pageNo" value="1">
				<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
				<input id="warehouseType-id" type="hidden" name="locationWarehouseType" value="${domain.locationWarehouseType}">
				<div class="form-body">
					<div class="form-group">
						<label class="control-label col-md-1">区域</label>
						<div class="col-md-3">
							<select name="query.locationRegion" class="form-control" id = "locationRegion-id" onchange="locationRegionFunction(this)">
								<option vlaue=""></option>
								<#list domain.locationRegionList as locationRegion>
									<#if query.locationRegion == locationRegion>
										<option selected="selected" value="${locationRegion}">${locationRegion}</option>
									<#else>
										<option value="${locationRegion}">${locationRegion}</option>
									</#if>
								</#list>
							</select>
						</div>
						<label class="control-label col-md-1">通道</label>
						<div class="col-md-3">
                            <input class="form-control" type="text" name="query.locationAisle" value="${query.locationAisle}">
						</div>
						<label class="control-label col-md-1">库位</label>
						<div class="col-md-3">
							<input class="form-control" type="text" name="query.locationStr" placeholder="支持逗号分隔查询" value="${query.locationStr }">
						</div>
						<label class="control-label col-md-1">库位属性</label>
						<div class="col-md-3">
							<select name="query.locationAttribute" class="form-control" id = "locationAttribute-id">
								<option vlaue=""></option>
								<#list domain.locationAttributeList as locationAttribute>
									<#if query.locationAttribute == locationAttribute.code>
										<option selected="selected" value="${locationAttribute.code}">${locationAttribute.getName()}</option>
									<#else>
										<option value="${locationAttribute.code}">${locationAttribute.getName()}</option>
									</#if>
								</#list>
							</select>
						</div>
						<label class="control-label col-md-1">库位状态</label>
						<div class="col-md-3">
							<select name="query.locationStatus" class="form-control" id = "locationStatus-id">
								<option vlaue=""></option>
								<#list domain.locationStatusList as locationStatus>
									<#if query.locationStatus == locationStatus.code>
										<option selected="selected" value="${locationStatus.code}">${locationStatus.getName()}</option>
									<#else>
										<option value="${locationStatus.code}">${locationStatus.getName()}</option>
									</#if>
								</#list>
							</select>
						</div>
						<label class="control-label col-md-1">品类差异</label>
						<div class="col-md-3">
							<select name="query.storageDiffType" class="form-control">
								<option vlaue=""></option>
								<option value="1" <#if query.storageDiffType == 1>selected="selected"</#if>>实际存储品类 > 品类上限</option>
								<option value="2" <#if query.storageDiffType == 2>selected="selected"</#if>>实际存储品类 = 品类上限</option>
								<option value="3" <#if query.storageDiffType == 3>selected="selected"</#if>>实际存储品类 < 品类上限</option>
							</select>
						</div>
						<label class="control-label col-md-1">品类上限</label>
						<div class="col-md-3">
							<input class="form-control" type="number" min=0 name="query.categoryLimit" placeholder="请输入品类上限" value="${query.categoryLimit }" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}">
						</div>
						<label class="control-label col-md-1">实际品类数</label>
						<div class="col-md-3">
							<input class="form-control" type="number" min=0 name="query.categoryActual" placeholder="请输入实际品类数" value="${query.categoryActual}" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}">
						</div>
						<label class="control-label col-md-1">库位类型</label>
						<div class="col-md-3">
							<select name="query.locationType" class="form-control">
								<option vlaue=""></option>
								<#list domain.locationTypeList as locationType>
									<#if query.locationType == locationType.code>
										<option selected="selected" value="${locationType.code}">${locationType.getName()}</option>
									<#else>
										<option value="${locationType.code}">${locationType.getName()}</option>
									</#if>
								</#list>
							</select>
						</div>
					</div>
				</div>
				<div>
					<div class="pull-left" style="margin-left: 10px;">
							<button type="button" class="btn  btn-default" onclick="checkNegative();">
								反选
							</button>
							<button type="button" class="btn  btn-default" onclick="checkAll();">
								全选
							</button>
                            <@header method="auth" authCode="SHELF_MANAGE_ADD_LOCATION">
							<button type="button" class="btn  btn-default" onclick="addLocation()">
								<i class="icon-pickingTask"></i> 添加库位
							</button>
                            </@header>
                            <@header method="auth" authCode="SHELF_MANAGE_LOCATION_STORE_ATTRIBUTE_SET">
							<button type="button" class="btn  btn-default" onclick="addLocaitonStorage()">
								<i class="icon-pickingTask"></i> 库位存储属性设置
							</button>
                            </@header>
                            <@header method="auth" authCode="SHELF_MANAGE_BATCH_MODIFY_LOCATION_ATTRIBUTE">
							<button type="button" class="btn  btn-default" onclick="bacthUpdateLocaitonAttribute()">
								<i class="icon-pickingTask"></i> 批量修改库位属性
							</button>
                            </@header>
                            <@header method="auth" authCode="SHELF_MANAGE_IMPORT_LOCATION">
							<span class="btn btn-default fileinput-button">
								<span class="icon-plus"> 导入库位</span>
								<input type="file" name="file" onchange="fileinputSubmit(this)" />
							</span>
                            </@header>
							<div class="btn-group">
								<button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
									导入库位打印 <i class="icon-angle-down"></i>
								</button>
								<ul class="dropdown-menu">
                                    <@header method="auth" authCode="SHELF_MANAGE_IMPORT_LOCATION_PRINT_DOWNLOAD_PARADIGM">
									<li>
										<a class="btn btn-default" href="${CONTEXT_PATH}file/execl/import_print_location.xlsx">下载模板</a>
									</li>
                                    </@header>
                                    <@header method="auth" authCode="SHELF_MANAGE_IMPORT_LOCATION_PRINT">
									<span class="btn btn-default fileinput-button" style="width: 100%;margin-top: 2px;">
										<span class="icon-upload">导入打印</span>
										<input type="file" name="file" onchange="importPrint(this)" />
									</span>
                                    </@header>
								</ul>
							</div>
                            <@header method="auth" authCode="SHELF_MANAGE_DOWNLOAD_IMPORT_SHELF_PARADIGM_EXCEL">
							<a class="btn btn-default" href="${CONTEXT_PATH}file/execl/location_import.xlsx">
								<i class="icon-download"></i> 下载导入库位模板Excel
							</a>
                            </@header>
                            <@header method="auth" authCode="SHELF_MANAGE_OPEN">
							<button type="button" class="btn  btn-default" onclick="batchModifyLocation(1)">
								<i class="icon-pickingTask"></i> 开启
							</button>
                            </@header>
                            <@header method="auth" authCode="SHELF_MANAGE_CLOSED">
							<button type="button" class="btn  btn-default" onclick="batchModifyLocation(2)">
								<i class="icon-pickingTask"></i> 关闭
							</button>
                            </@header>
							<@header method="auth" authCode="SHELF_MANAGE_DELETE">
							<button type="button" class="btn  btn-default" onclick="batchModifyLocation(3)">
								<i class="icon-pickingTask"></i> 删除
							</button>
							</@header>
					</div>		
					<div class="col-md-offset-10" style="text-align: right">
						<button type="button" class="btn btn-default" onclick="formReset(this)">
							<i class="icon-refresh"></i> 重置
						</button>
						<button type="submit" class="btn blue">
							<i class="icon-search"></i> 查询
						</button>
                        <@header method="auth" authCode="SHELF_MANAGE_DOWNLOAD">
						<button type="button" class="btn btn-default" onclick="downloadWarehouseLocation()">
							<i class="icon-download"></i> 导出
						</button>
                        </@header>
					</div>
				</div>
			</form>
		</div>
		<br/>
	</div>

	<div class="row">
		<div id="fixedDiv" class="col-md-12">
			<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
				<colgroup>
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="15%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="10%" />
					<col width="5%" />
					<col width="10%" />
				</colgroup>
				<thead>
					<tr>
						<th>选择</th>
						<th>区域</th>
						<th>通道</th>
						<th>库位</th>
						<th>品类上限</th>
						<th>实际品类</th>
						<th>有库存品类</th>
						<th>储存属性</th>
						<th>长(cm)</th>
						<th>宽(cm)</th>
						<th>高(cm)</th>
						<th>体积(m³)</th>
						<th>库位类型</th>
						<th>库位属性</th>
						<th>状态</th>
						<th>日志</th>
					</tr>
				</thead>
			</table>
		</div>
		<div class="col-md-12" id="lcoation-list-warp">
			<table class="table table-striped table-bordered table-hover table-condensed" id="lcoation-list">
				<colgroup>
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="15%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="10%" />
					<col width="5%" />
					<col width="10%" />
				</colgroup>
				<thead>
					<tr>
						<th>选择</th>
						<th>区域</th>
						<th>通道</th>
						<th>库位</th>
						<th>品类上限</th>
						<th>实际品类</th>
						<th>有库存品类</th>
						<th>储存属性</th>
						<th>长(cm)</th>
						<th>宽(cm)</th>
						<th>高(cm)</th>
						<th>体积(m³)</th>
						<th>库位类型</th>
						<th>库位属性</th>
						<th>状态</th>
						<th>日志</th>
					</tr>
				</thead>
				<tbody>
					<#list domain.whLocations as warehouseLocation>
						<tr>
							<td><input type="checkbox" name="locationIds" value="${warehouseLocation.id}" /></td>
							<td>${warehouseLocation.locationRegion}</td>
							<td>${warehouseLocation.locationAisle}</td>
							<td>${warehouseLocation.location}</td>
							<td>${warehouseLocation.categoryLimit}</td>
							<td>${warehouseLocation.categoryActual}</td>
							<td>${warehouseLocation.categoryInStock}</td>
							<td>${warehouseLocation.whLocationStorage.name}</td>
							<td>${warehouseLocation.locationLength}</td>
							<td>${warehouseLocation.locationWidth}</td>
							<td>${warehouseLocation.locationHeight}</td>
							<td>${warehouseLocation.locationVolume}</td>
							<td>
								<span class="btn btn-sm">
									${util('enumName',"com.estone.warehouse.enums.LocationType", warehouseLocation.locationType)}
								</span>
							</td>
							<td>
								<span class="btn btn-sm">
									${util('enumName',"com.estone.warehouse.enums.LocationAttribute", warehouseLocation.locationAttribute)}
								</span>
							</td>
							<td>
								<span class="btn btn-sm">
									${util('enumName',"com.estone.warehouse.enums.LocationStatus", warehouseLocation.locationStatus)}
								</span>
							</td>
							<td>
								<button type="button" class="btn btn-xs btn-info" onclick="viewLog(${warehouseLocation.id}, 'whLocation')">日志</button>
							</td>
						</tr>
					</#list>
				</tbody>
			</table>
		</div>
	</div>
		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
</div>

	<div class="modal fade ui-popup" id="add_modal" style="overflow:hidden;" tabindex="-1" role="dialog" aria-labelledby="organization-add-label" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
					<h4 class="modal-title" id="expert-modal-label">添加库位</h4>
				</div>
				<div class="modal-body form-horizontal portlet" style="height:400px;">
					<input type="hidden" id="warehouseType" name="warehouseType" value="${domain.locationWarehouseType}"/>
					<div class="form-group">
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12">
							<div class="line-height-label">
								<div class="col-md-3">
									<label class="control-label col-md-11"><span class="required">*</span>库位类型:</label>
								</div>
								<div class="col-md-7">
									<select name="query.locationType" class="form-control" id = "add_modal_locationType_id">
										<option vlaue=""></option>
										<#list domain.locationTypeList as locationType>
											<#if query.locationType == locationType.code>
												<option selected="selected" value="${locationType.code}">${locationType.getName()}</option>
											<#else>
												<option value="${locationType.code}">${locationType.getName()}</option>
											</#if>
										</#list>
									</select>
								</div>
								<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
							</div>
						</div>
					</div>	
					
					<div class="form-group">
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12">
							<div class="line-height-label">
								<div class="col-md-3">
									<label class="control-label col-md-11"><span class="required">*</span>库位属性:</label>
								</div>
								<div class="col-md-7">
									<select name="query.locationAttribute" class="form-control" id = "add_modal_locationAttribute_id">
										<option vlaue=""></option>
										<#list domain.locationAttributeList as locationAttribute>
											<#if query.locationAttribute == locationAttribute.code>
												<option selected="selected" value="${locationAttribute.code}">${locationAttribute.getName()}</option>
											<#else>
												<option value="${locationAttribute.code}">${locationAttribute.getName()}</option>
											</#if>
										</#list>
									</select>
								</div>
								<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
							</div>
						</div>
					</div>	
					
					
					<div class="form-group">
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12">
							<div class="line-height-label">
								<div class="col-md-3">
									<label class="control-label col-md-11"><span class="required">*</span>库位号:</label>
								</div>
								<div class="col-md-7">
									<input class="form-control input-medium" placeholder="库位号" name="query.location" type="text">
								</div>
								<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
							</div>
						</div>
					</div>	
					
					<div class="form-group">
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12">
							<div class="line-height-label">
								<div class="col-md-3">
									<label class="control-label col-md-11"><span class="required">*</span>品类上限:</label>
								</div>
								<div class="col-md-7">
									<input class="form-control input-medium" placeholder="品类上限" name="query.categoryLimit" type="number">
								</div>
								<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
							</div>
						</div>
					</div>
					
					<div class="form-group">
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12">
							<div class="line-height-label">
								<div class="col-md-3">
									<label class="control-label col-md-11"><span class="required">*</span>存储属性:</label>
								</div>
								<div class="col-md-7">
									<select name="query.storageAttribute" class="form-control" id = "add_modal_storageAttribute-id">
										<option vlaue=""></option>
										<#list domain.locationStorageList as storageAttribute>
											<#if test="${query.storageAttribute == storageAttribute}">
												<option selected="selected" value="${storageAttribute.id}">${storageAttribute.name}</option>
											<#else>
												<option value="${storageAttribute.id}">${storageAttribute.name}</option>
											</#if>
										</#list>
									</select>
								</div>
								<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
							</div>
						</div>
					</div>
					
					<div class="form-group">
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12">
							<div class="line-height-label">
								<div class="col-md-3">
									<label class="control-label col-md-11">长*宽*高(cm):</label>
								</div>
								<div class="col-md-8">
									<input class="form-control input-inline input-xsmall" placeholder="长cm" name="query.locationLength" aria-invalid="false" type="text">
									<input class="form-control input-inline input-xsmall" placeholder="宽cm" name="query.locationWidth" aria-invalid="false" type="text">
									<input class="form-control input-inline input-xsmall" placeholder="高cm" name="query.locationHeight" aria-invalid="false" type="text">
								</div>
								<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
							</div>
						</div>
					</div>	
					
					<div class="form-group">
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12">
							<div class="line-height-label">
								<div class="col-md-3">
									<label class="control-label col-md-11"><span class="required" aria-required="true">*</span>状态:</label>
								</div>
								<div class="col-md-8">
									<label class="radio-inline" title="启动">
										<input id="add_modal_locationStatus" class="input-xsmall" autocomplete="off" name="query.locationStatus" value="1" checked="checked" type="radio">启动
									</label>
									<label class="radio-inline" title="关闭">
										<input id="" class="input-xsmall" autocomplete="off" name="query.locationStatus" value="2" type="radio">关闭
									</label>
								</div>
							</div>
						</div>
					</div>
				</div>	
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" onclick="saveLocation()">保存</button>
					<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
				</div>
			</div>
		</div>
	</div>
	
	<div class="modal fade ui-popup" id="update_modal" style="overflow:hidden;" tabindex="-1" role="dialog" aria-labelledby="organization-update-label" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
					<h4 class="modal-title" id="expert-modal-label">批量修改库位属性</h4>
				</div>
				<div class="modal-body form-horizontal portlet" style="height:400px;">
					
					<div class="form-group">
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12">
							<div class="line-height-label">
								<div class="col-md-3">
									<label class="control-label col-md-11">说明:</label>
								</div>
								<div class="col-md-7">
									<label class="">当前选择了<span id="select-location-num" style="color: red">0</span>个库位</label>
								</div>
								<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
							</div>
						</div>
					</div>

					<div class="form-group">
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12">
							<div class="line-height-label">
								<div class="col-md-3">
									<label class="control-label col-md-11"><span class="required">*</span>库位类型:</label>
								</div>
								<div class="col-md-7">
									<select name="query.locationType" class="form-control" id = "update_modal_locationType_id">
										<option vlaue=""></option>
										<#list domain.locationTypeList as locationType>
											<#if query.locationType == locationType.code>
												<option selected="selected" value="${locationType.code}">${locationType.getName()}</option>
											<#else>
												<option value="${locationType.code}">${locationType.getName()}</option>
											</#if>
										</#list>
									</select>
								</div>
								<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
							</div>
						</div>
					</div>	
					
					<div class="form-group">
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12">
							<div class="line-height-label">
								<div class="col-md-3">
									<label class="control-label col-md-11"><span class="required">*</span>库位属性:</label>
								</div>
								<div class="col-md-7">
									<select name="query.locationAttribute" class="form-control" id = "update_modal_locationAttribute_id">
										<option vlaue=""></option>
										<#list domain.locationAttributeList as locationAttribute>
											<#if query.locationAttribute == locationAttribute.code>
												<option selected="selected" value="${locationAttribute.code}">${locationAttribute.getName()}</option>
											<#else>
												<option value="${locationAttribute.code}">${locationAttribute.getName()}</option>
											</#if>
										</#list>
									</select>
								</div>
								<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
							</div>
						</div>
					</div>	

					<div class="form-group">
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12">
							<div class="line-height-label">
								<div class="col-md-3">
									<label class="control-label col-md-11"><span class="required">*</span>品类上限:</label>
								</div>
								<div class="col-md-7">
									<input class="form-control input-medium" placeholder="品类上限" name="query.categoryLimit" type="number" >
								</div>
								<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
							</div>
						</div>
					</div>
					
					<div class="form-group">
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12">
							<div class="line-height-label">
								<div class="col-md-3">
									<label class="control-label col-md-11"><span class="required">*</span>存储属性:</label>
								</div>
								<div class="col-md-7">
									<select name="query.storageAttribute" class="form-control" id = "update_modal_storageAttribute-id">
										<option vlaue=""></option>
										<#list domain.locationStorageList as storageAttribute>
											<#if test="${query.storageAttribute == storageAttribute}">
												<option selected="selected" value="${storageAttribute.id}">${storageAttribute.name}</option>
											<#else>
												<option value="${storageAttribute.id}">${storageAttribute.name}</option>
											</#if>
										</#list>
									</select>
								</div>
								<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
							</div>
						</div>
					</div>
					
					<div class="form-group">
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12">
							<div class="line-height-label">
								<div class="col-md-3">
									<label class="control-label col-md-11">长*宽*高(cm):</label>
								</div>
								<div class="col-md-8">
									<input class="form-control input-inline input-xsmall" placeholder="长cm" name="query.locationLength" aria-invalid="false" type="text">
									<input class="form-control input-inline input-xsmall" placeholder="宽cm" name="query.locationWidth" aria-invalid="false" type="text">
									<input class="form-control input-inline input-xsmall" placeholder="高cm" name="query.locationHeight" aria-invalid="false" type="text">
								</div>
								<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
							</div>
						</div>
					</div>	
				</div>	
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" onclick="savebacthUpdateLocaitonAttribute()">保存</button>
					<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
				</div>
			</div>
		</div>
	</div>
	
	<div class="modal fade ui-popup" id="storage_modal" style="overflow:hidden;" tabindex="-1" role="dialog" aria-labelledby="organization-update-label" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
					<h4 class="modal-title" id="expert-modal-label">存储属性设置</h4>
				</div>
				<div class="modal-body form-horizontal portlet" style="height:400px;">
					<table class="table" id="location-list">
						<thead >
							<tr>
								<th width="50%">存储属性</th>
								<th width="50%">操作</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td><input type="text" readonly="readonly" value="普通"></input></td>
								<td>
									<button type="submit" class="btn blue" id="addRow">
										添加
									</button>
								</td>
							</tr>
							<#list domain.storageAttributeList as item>
							<tr id="row-${item.id}">
								<td><input type="text" readonly="readonly" value="${item.name}"></input></td>
								<td>
									<button type="button" class="btn red" onclick="removeRow2(${item.id})">
										删除
									</button>
								</td>
							</tr>
							</#list>
						</tbody>
					</table>															
				</div>	
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" onclick="saveLocaitonStorage()">保存</button>
					<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
				</div>
			</div>
		</div>
	</div>

<#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript">
	
	jQuery(document).ready(function() {
		
			var obj1 = $("#warehouseType-id");
			var selectValue1 = $("#warehouseType-id").find("option:selected").val();//选中的文本
			
			var obj2 = $("#locationRegion-id");
			var selectValue2 = $("#locationRegion-id").find("option:selected").val();//选中的文本
			
			if (selectValue1 !='') {
				//warehouseTypeFunction(obj1);
			}
			if (selectValue2 !='') {
				//locationRegionFunction(obj2);
			}
	 
	});
	var locationStorageIds="";
    // 分页
    var total = "${domain.page.totalCount}";
	var pageNo = "${domain.page.pageNo}";
	var pageSize = "${domain.page.pageSize}";
	$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

    var locationAisleSelectJson = ${domain.locationAisleSelectJson};
    $("input[name='query.locationAisle']").select2({
        data : locationAisleSelectJson,
        placeholder : "通道",
        multiple: true,
        allowClear : true
    });

    var heights = $("body").height();
    if(heights>910){
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }
    
  	//全选
	function checkAll() {
		var checkBox = $("#lcoation-list").find(":checkbox");
		checkBox.prop("checked", !checkBox.prop("checked"));
	}

	// 反选
	function checkNegative() {
		var checkBox = $("#lcoation-list").find(":checkbox");
		checkBox.each(function() {
			if($(this).attr('checked')){
	            $(this).removeAttr('checked');
	        }else{
	            $(this).attr('checked','checked');
	        }
		});
	}
	
	// 获取选中的任务
	function getCheckedLocationIds() {
		var checkedLocationIds = $("input[name='locationIds']:checked");
		return checkedLocationIds;
	}
	
	//打开添加库位页面
	function addLocation(){
		$("#add_modal").modal('show');
		$("#add_modal_locationStatus").click();
	}
	
	//添加库位方法
	function saveLocation(){
		var warehouseType = $("#warehouseType").val();//库位仓库类型
		var locationType = $("#add_modal_locationType_id option:selected").val();//库位类型
		var locationAttribute = $("#add_modal_locationAttribute_id option:selected").val();//库位属性
		var location = $("#add_modal input[name='query.location']").val();//库位
		var categoryLimit = $("#add_modal input[name='query.categoryLimit']").val();//库位品类上限
		var storageAttribute = $("#add_modal_storageAttribute-id option:selected").val();//库位存储属性
		var locationLength = $("#add_modal input[name='query.locationLength']").val();//库位长
		var locationWidth = $("#add_modal input[name='query.locationWidth']").val();//库位宽
		var locationHeight = $("#add_modal input[name='query.locationHeight']").val();//库位高
		var locationStatus = $("#add_modal input[name='query.locationStatus']:checked").val();//库位状态
		
		if (!warehouseType || !locationType || !locationAttribute || !location || (!categoryLimit && locationType==1)
				|| !storageAttribute || !locationStatus) {
			layer.alert("必填数据请填写完整！");
			return false;
		}

        var checkNewLocation = new RegExp("^[A-Za-z0-9]{3,4}(-[A-Za-z0-9]{2}){3}$");
        if (!checkNewLocation.test(location)) {
            layer.alert("库位不符合库位规则！");
            return false;
        }
//		var checkOldLocation = new RegExp("^([A-Z]{1})([0-9]{2})-([0-9]{2})-([0-9]{2})-([0-9]{2})$");
//		if (warehouseType == 1) {
//			if (!checkOldLocation.test(location)) {
//				layer.alert("库位不符合老仓库位规则！");
//				return false;
//		    }
//		}
//		var checkNewLocation = new RegExp("^([2-2]{1})([A-Z]{1})([0-9]{2})-([0-9]{2})-([0-9]{2})-([0-9]{2})$");
//		if (warehouseType == 2) {
//			if (!checkNewLocation.test(location)) {
//				layer.alert("库位不符合新仓库位规则！");
//				return false;
//		    }
//		}


		var checkInteger = new RegExp("^(0|[0-9]*[1-9][0-9]*)$");
		if (!checkInteger.test(categoryLimit) && locationType==1) {
			layer.alert("品类上限必须为正整数！");
			return false;
	    }
	    if (locationType==1 && categoryLimit<=0){
            layer.alert("品类上限必须为正整数！");
            return false;
		}
		
		//对值进行判断
		if(categoryLimit.length > 8){
			layer.alert("品类上限太大！");
			return false;
		}
		
		if( locationLength != "" ){
			if (!checkInteger.test(locationLength)) {
				layer.alert("库位长必须为正整数！");
				return false;
		    }
			if (locationWidth == "" || locationHeight == "") {
				layer.alert("库位长宽高必须同时填写或者同时置空！");
				return false;
			}
		}
		if( locationWidth != "" ){
			if (!checkInteger.test(locationWidth)) {
				layer.alert("库位宽必须为正整数！");
				return false;
		    }
			if (locationLength == "" || locationHeight == "") {
				layer.alert("库位长宽高必须同时填写或者同时置空！");
				return false;
			}
		}
		if( locationHeight != "" ){
			if (!checkInteger.test(locationHeight)) {
				layer.alert("库位高必须为正整数！");
				return false;
		    }
			if (locationWidth == "" || locationLength == "") {
				layer.alert("库位长宽高必须同时填写或者同时置空！");
				return false;
			}
		}
		
		//对值进行判断
		if(locationHeight.length > 8 || locationWidth.length > 8  || locationLength.length > 8 ){
			layer.alert("库位长宽高数值太大！");
			return false;
		}
		
		$.post(CONTEXT_PATH + "warehouse/locations/create", { "whLocation.warehouseType": warehouseType,
			"whLocation.locationType": locationType,
			"whLocation.locationAttribute": locationAttribute,
			"whLocation.location": location,
			"whLocation.categoryLimit": categoryLimit,
			"whLocation.storageAttribute": storageAttribute,
			"whLocation.locationLength": locationLength,
			"whLocation.locationWidth": locationWidth,
			"whLocation.locationHeight": locationHeight,
			"whLocation.locationStatus": locationStatus }, function(data) {
				$("#add_modal").modal('hide');
				if (data.status == 200) {
    				alert(data.message);
    				setTimeout(function() {
    					window.location.reload();
					}, 1000);
    			} else {
    				customizeLayer(data.message);
    			}
		});
	}
	
	
	//打开批量修改库位属性页面
	function bacthUpdateLocaitonAttribute(){
		var checkedDatas = getCheckedLocationIds();
		
		if(checkedDatas.length == 0) {
			layer.alert("请选择要操作的库位",'error');
			return;
		}
		
		$("#select-location-num").text(checkedDatas.length);
		$("#update_modal").modal('show');
	}
	
	//批量修改库位属性方法
	function savebacthUpdateLocaitonAttribute(){
		var checkedDatas = getCheckedLocationIds();
		
		if(checkedDatas.length == 0) {
			layer.alert("请选择要操作的库位",'error');
			return;
		}
		
		var locationType = $("#update_modal_locationType_id option:selected").val();//库位类型
		var locationAttribute = $("#update_modal_locationAttribute_id option:selected").val();//库位属性
		var categoryLimit = $("#update_modal input[name='query.categoryLimit']").val();//库位品类上限
		var storageAttribute = $("#update_modal_storageAttribute-id option:selected").val();//库位存储属性
		var locationLength = $("#update_modal input[name='query.locationLength']").val();//库位长
		var locationWidth = $("#update_modal input[name='query.locationWidth']").val();//库位宽
		var locationHeight = $("#update_modal input[name='query.locationHeight']").val();//库位高
		
		if ( !locationType || !locationAttribute || (!categoryLimit && locationType==1)
				|| !storageAttribute) {
			layer.alert("必填数据请填写完整！");
			return false;
		}

		var checkInteger = new RegExp("^(0|[0-9]*[1-9][0-9]*)$");
		if (!checkInteger.test(categoryLimit) && locationType==1) {
			layer.alert("品类上限必须为正整数！");
			return false;
	    }
        if (locationType==1 && categoryLimit<=0){
            layer.alert("品类上限必须为正整数！");
            return false;
        }
		
		//对值进行判断
		if(categoryLimit.length > 8){
			layer.alert("品类上限太大！");
			return false;
		}
		
		if( locationLength != "" ){
			if (!checkInteger.test(locationLength)) {
				layer.alert("库位长必须为正整数！");
				return false;
		    }
			if (locationWidth == "" || locationHeight == "") {
				layer.alert("库位长宽高必须同时填写或者同时置空！");
				return false;
			}
		}
		if( locationWidth != "" ){
			if (!checkInteger.test(locationWidth)) {
				layer.alert("库位宽必须为正整数！");
				return false;
		    }
			if (locationLength == "" || locationHeight == "") {
				layer.alert("库位长宽高必须同时填写或者同时置空！");
				return false;
			}
		}
		if( locationHeight != "" ){
			if (!checkInteger.test(locationHeight)) {
				layer.alert("库位高必须为正整数！");
				return false;
		    }
			if (locationWidth == "" || locationLength == "") {
				layer.alert("库位长宽高必须同时填写或者同时置空！");
				return false;
			}
		}
		
		//对值进行判断
		if(locationHeight.length > 8 || locationWidth.length > 8  || locationLength.length > 8 ){
			layer.alert("库位长宽高数值太大！");
			return false;
		}
		
		var locationIds = checkedDatas.serialize();
		
		$.post(CONTEXT_PATH + "warehouse/locations/batchUpdate?" + locationIds, { 
			"whLocation.locationType": locationType,
			"whLocation.locationAttribute": locationAttribute,
			"whLocation.categoryLimit": categoryLimit,
			"whLocation.storageAttribute": storageAttribute,
			"whLocation.locationLength": locationLength,
			"whLocation.locationWidth": locationWidth,
			"whLocation.locationHeight": locationHeight}, function(data) {
				$("#update_modal").modal('hide');
				if (data.status == 200) {
    				alert(data.message);
    				setTimeout(function() {
    					window.location.reload();
					}, 1000);
    			} else {
    				customizeLayer(data.message);
    			}
		});
		
	}
	
	//库位储存属性设置页面
	$('#addRow').on('click',function(){
		$('#location-list').find('tbody').append('<tr><td><input class="storage-add-row" type="text"/></td><td ><button type="button" onclick="removeRow(this)" class="btn red">删除</button></td></tr>')
	})
	function removeRow (dom){
		$(dom).parents('tr').remove()
	}
	function removeRow2 (id){
		$('#row-'+id).remove()
		locationStorageIds += id +',';
	}
	//打开库位储存属性设置页面
	function addLocaitonStorage(){
		$("#storage_modal").modal('show');
		setTimeout( function(){
			$("#storage_modal").draggable({
				handle: ".modal-header"
		    });
		}, 200 );
	}
	
	$('#storage_modal').on('hidden.bs.modal', function () {
		window.location.reload();
	})
	
	//新增库位存储属性 
	function saveLocaitonStorage(){
		var tempArray = new Array();
		var inputs = $(".storage-add-row");
		for (var i = 0; i < inputs.size(); i++) {
			var value = $(inputs[i]).val();
			//对值进行判断
			if(value.length > 8){
				layer.alert("属性值长度太长！");
				return false;
			}
			tempArray.push(value);
		}
		
		$.post(CONTEXT_PATH + "warehouse/location/storage/update", {
			storageList: JSON.stringify(tempArray), locationStorageIds:locationStorageIds }, function(data) {
				$("#storage_modal").modal('hide');
				if (data.status == 200) {
    				alert(data.message);
    				setTimeout(function() {
    					window.location.reload();
					}, 1000);
    			} else {
    				customizeLayer(data.message);
    			}
		});
		
	}
	
	//导出
	// 下载
	function downloadWarehouseLocation(){
		var checkedDatas = getCheckedLocationIds();
		
		var diglog = dialog({
			title: '导出',
			width: 350,
			height:100,
			url: CONTEXT_PATH + "warehouse/locations/downloadmode",
			okValue: '确定',
		    ok: function () {
		    	
		    	var exportWindow = $(this.iframeNode.contentWindow.document.body);
		    	
		    	var submitForm = exportWindow.find("#submit-form");
		    	
		    	var exportType = submitForm.find("input[name='exportType']:checked").val();
		    	
		    	var submitFormParam = submitForm.serialize();
		    	
		    	// 导出当前选择
		    	if(exportType == 3) {
					if(checkedDatas.length == 0) {
						layer.alert("请选择要操作的数据!");
						return false;
					}
					
					downloadByIds(exportType, checkedDatas);
		    	} else {
			    	//还原分页
			    	$("#page-no").val("${domain.page.pageNo}");
			    	
			    	var action = document.warehouseLocationForm.action;
			    	var target = document.warehouseLocationForm.target;
			    	var method = document.warehouseLocationForm.method;
			    	document.warehouseLocationForm.action= CONTEXT_PATH + "warehouse/locations/download?" + submitFormParam;
			    	document.warehouseLocationForm.target="_blank";
			    	document.warehouseLocationForm.method="POST";
			    	document.warehouseLocationForm.submit();
			    	document.warehouseLocationForm.target=target;
			    	document.warehouseLocationForm.action=action;
			    	document.warehouseLocationForm.method=method;
			    	
			    	$("#page-no").val("${domain.page.pageSize}");
		    	}
		    	
		    	setTimeout(function () {
		    		diglog.close().remove();
		    	}, 100);
		    	
		    	return true;
		    },
		    cancelValue: '取消',
		    cancel: function () {}
		});
		diglog.show();
	}
	
	function downloadByIds(exportType, checkedDatas) {
		var url= CONTEXT_PATH + "warehouse/locations/download?&exportType=" + exportType;
		var locationIdStr = "";
		for (var i = 0; i < checkedDatas.length; i++) {
			var checkedData = checkedDatas[i];
			var locationId = $(checkedData).val();
			locationIdStr += locationId;
			if(i != checkedDatas.length-1){
				locationIdStr += ",";
			}
		}
		
		var tempForm = document.createElement("form");       
		tempForm.id="tempForm";
		tempForm.method="post";     
		tempForm.action=url;
		tempForm.target="blank";
		
		var skuHideInput = document.createElement("input");      
		skuHideInput.type="hidden";   
		skuHideInput.name="query.locationIdStr";
		skuHideInput.value= locationIdStr;      
		tempForm.appendChild(skuHideInput);
		
		if (tempForm.attachEvent) { // IE 
			tempForm.attachEvent("onsubmit",function(){ window.open('about:blank','blank'); });  
		} else if (tempForm.addEventListener) {  // DOM Level 2 standard  
			tempForm.addEventListener("onsubmit",function(){ window.open('about:blank','blank'); });  
		}              
		document.body.appendChild(tempForm);   
		if (document.createEvent) { // DOM Level 2 standard  
			evt = document.createEvent("MouseEvents");  
			evt.initMouseEvent("submit", true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);  
			tempForm.dispatchEvent(evt);  
		} else if (tempForm.fireEvent) { // IE  
			tempForm.fireEvent('onsubmit');  
		} 
		//必须手动的触发        
		tempForm.submit();         
		document.body.removeChild(tempForm);
	}
	
	//导入 批量生成库位
	function fileinputSubmit(target) {
		
		//检测上传文件的类型 
		var filename = target.value;

		var ext, idx;
		if (filename == '') {
			$("#submit-upload").attr("disabled", true);
			layer.alert("请选择需要上传的文件!");
			return;
		} else {
			idx = filename.lastIndexOf(".");
			if (idx != -1) {
				ext = filename.substr(idx + 1).toUpperCase();
				ext = ext.toLowerCase();

				if (ext != 'xls' && ext != 'xlsx') {
					layer.alert("只能上传.Excel类型的文件!");
					return;
				}
			} else {
				layer.alert("只能上传.Excel类型的文件!");
				return;
			}
		}
		
		var r = confirm("确定上传" + filename + "?");
		
		if(!r) {
			return;
		}
		
		var uploadUrl = CONTEXT_PATH + "warehouse/locations/upload";
		
		var searchUrl = $("#domain").attr("action");
		
		$("#domain").attr("action", uploadUrl);
		
		$("#domain").ajaxSubmit(function(data) {
			debugger;
			if (data.status == 200) {
				alert(data.message);
				setTimeout(function() {
        			window.location.reload();
    			}, 1000);
			} else {
				$(target).val(null);
				customizeLayer(data.message, "error");
			}
			
			$("#domain").attr("action", searchUrl);
		});
		
		$("#domain").attr("action", searchUrl);
	}
	
	function warehouseTypeFunction(obj){
		var html = "<option vlaue=''></option>";
		$("#locationRegion-id").html("");
		$("#locationAisle-id").html("");
		var warehouseId = $(obj).val();
		if (warehouseId) {
			$.post(CONTEXT_PATH + "warehouse/locations/seleteRegion", {warehouseId: warehouseId}, function(data) {
				if (data.status == 200) {
					var dataList = data.body[warehouseId];
					if(dataList != null && dataList.length > 0){
						for (var i = 0; i < dataList.length; i++) {
							html += "<option value='" + dataList[i] + "'>" + dataList[i] + "</option>";
						}
	    				$("#locationRegion-id").append(html);
					}
    			} else {
    				customizeLayer(data.message);
    			}
			});
		}
	}
	
	function locationRegionFunction(obj){
		var html = "<option vlaue=''></option>";
		$("#locationAisle-id").html(html);
		var warehouseId = $("#warehouseType-id").val();
		var regionId = $(obj).val();
		if (warehouseId && regionId) {
			$.post(CONTEXT_PATH + "warehouse/locations/seleteAisle", {warehouseId: warehouseId,regionId: regionId}, function(data) {
				if (data.status == 200) {
					var dataJson = data.body[regionId];
                    $("input[name='query.locationAisle']").val("").select2({
                        data : JSON.parse(dataJson),
                        placeholder : "通道",
                        multiple: true,
                        allowClear : true
                    });
    			} else {
    				customizeLayer(data.message);
    			}
			});
		}
	}
	
	function batchModifyLocation(type) {
		var checkedDatas = getCheckedLocationIds();
		if (checkedDatas.length == 0) {
			layer.alert("请勾选要操作的库位", "error");
			return false;
		}
		
		var typeText = "";
		if (type == 1) {
			typeText = "开启";
		} else if (type == 2) {
			typeText = "关闭";
		} else {
			typeText = "删除";
		}
		
		var r = confirm("确定" + typeText + "库位？");
		
		if (r) {
			var locationIds = checkedDatas.serialize();
			$.post(CONTEXT_PATH + "warehouse/locations/batchModifyLocation?" + locationIds + "&type=" + type, null, function(data) {
				if (data.status == 200) {
	   				alert(data.message);
	   				setTimeout(function() {
	   					window.location.reload();
					}, 1000);
	   			} else {
	   				customizeLayer(data.message);
	   			}
			});
		}
		
	}

	function importPrint(target) {

		//检测上传文件的类型
		var filename = target.value;

		var ext, idx;
		if (filename == '') {
			$("#submit-upload").attr("disabled", true);
			layer.alert("请选择需要上传的文件!");
			return;
		} else {
			idx = filename.lastIndexOf(".");
			if (idx != -1) {
				ext = filename.substr(idx + 1).toUpperCase();
				ext = ext.toLowerCase();

				if (ext != 'xls' && ext != 'xlsx') {
					layer.alert("只能上传.Excel类型的文件!");
					return;
				}
			} else {
				layer.alert("只能上传.Excel类型的文件!");
				return;
			}
		}

		var r = confirm("确定上传" + filename + "?");

		if(!r) {
			return;
		}

		var uploadUrl = CONTEXT_PATH + "warehouse/locations/uploadPrint";

		var searchUrl = $("#domain").attr("action");

		$("#domain").attr("action", uploadUrl);

		$("#domain").ajaxSubmit(function(data) {

			var printSkuWindow=window.open();
			printSkuWindow.document.write(data);
			printSkuWindow.focus();
			window.location.reload();

			$("#domain").attr("action", searchUrl);

		});

		$("#domain").attr("action", searchUrl);
	}
</script>
</body>
</html>