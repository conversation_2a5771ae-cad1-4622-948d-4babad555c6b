<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.br-div {
	height:20px;
}
</style>
</head>
<body>
<@header method="header" active="11040000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">仓库管理</a><#-- <i class="icon-angle-right"></i>--></li>
					<li class="active">出库周转框</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<#-- <h2 class="header-title">周转框</h2> -->

		<#-- 角色
		<div class="row">
			<div class="col-md-12">
				<BEGIN PAGE TITLE & BREADCRUMB>
				<h2>周转框</h2>
				<ul class="page-breadcrumb breadcrumb">
					<li><a href="#">仓库</a> <i class="icon-angle-right"></i></li>
					<li><a href="#">周转框</a> <i class="icon-angle-right"></i></li>
				</ul>
				END PAGE TITLE & BREADCRUMB
			</div>
		</div>-->
		<#assign query=domain.query>
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="col-md-12">
				<form action="${CONTEXT_PATH}warehouse/boxs/search"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="checkInForm" id="domain">
					<!-- 分页信息 -->
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
					<input id="boxFrom" type="hidden" name="boxFrom" value="OUT">
					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-1">周转框编号</label>
							<div class="col-md-2">
								<input class="form-control" name="query.boxNo" type="text" value="${query.boxNo}">
							</div>
							<label class="control-label col-md-1">类型</label>
							<div class="col-md-2">
								<input class="form-control" name="query.type" type="text" value="${query.type}">
							</div>
							<label class="control-label col-md-1">周转框ID</label>
							<div class="col-md-2">
								<input class="form-control" name="query.id" type="text" value="${query.id}">
							</div>
							<label class="control-label col-md-1">关联ID</label>
							<div class="col-md-2">
								<input class="form-control" name="query.relationNo" type="text" value="${query.relationNo}">
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1">状态</label>
							<div class="col-md-2">
								<input class="form-control" name="query.status" type="text" value="${query.status}">
							</div>
							<label class="control-label col-md-1">创建人</label>
							<div class="col-md-2">
								<input class="form-control" name="query.createdBy" type="text" value="${query.createdBy}">
							</div>
							<label class="control-label col-md-1">创建时间</label>
							<div class="col-md-2">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.createStartTime" placeholder="" readonly="readonly" value="${query.createStartTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.createEndTime" placeholder="" readonly="readonly" value="${query.createEndTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>
						</div>
					</div>
					<div>
						<div class="pull-left" style="margin-bottom: 10px;">
                            <@header method="auth" authCode="TURNOVER_BASKET_ADD">
							<button type="button" class="btn btn-default" onclick="addBox()">
								 添加
							</button>
                            </@header>
                            <@header method="auth" authCode="TURNOVER_BASKET_PRINT">
							<button type="button" class="btn btn-default" onclick="printBox()">
								 打印
							</button>
                            </@header>
							<!-- <button type="button" class="btn btn-default" onclick="relationNoBinding()">
								 绑定周转框
							</button> -->
                            <@header method="auth" authCode="TURNOVER_BASKET_UNLOCK">
							<button type="button" class="btn btn-default" onclick="boxNoUnbind()">
								 解锁周转框
							</button>
                            </@header>
							<@header method="auth" authCode="TURNOVER_BASKET_BATCH_UNLOCK_IN_SCREN">
							<a class="btn btn-default" href="${CONTEXT_PATH}warehouse/boxs/batchUnbindPage">
								现场批量解锁
							</a>
							</@header>
							<@header method="auth" authCode="TURNOVER_BASKET_BATCH_UNLOCK">
							<button type="button" class="btn btn-default" onclick="boxNoBatchUnbind()">
								 批量解锁周转框
							</button>
							</@header>
						</div>
						<div class="col-md-offset-10" style="text-align: right">
							<button type="button" class="btn btn-default" onclick="formReset(this)">
								<i class="icon-refresh"></i> 重置
							</button>
							<button type="submit" class="btn blue">
								<i class="icon-search"></i> 查询
							</button>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>

		<div class="row">
			<div id="fixedDiv" class="col-md-12">
				<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
					<colgroup>
						<col width="7%" />
						<col width="7%" />
						<col width="7%" />
						<col width="10%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="14%" />
						<col width="10%" />
						<col width="10%" />
						<col width="12%" />
						<col width="12%" />
					</colgroup>
					<thead>
					<tr>
						<th><label class="checkbox-inline">编号</label></th>
						<th>周转框编号</th>
						<th>关联ID</th>
						<th>类型</th>
						<th>状态</th>
						<th>是否打印</th>
						<th>当前使用人</th>
						<th>描述</th>
						<th>创建人</th>
						<th>创建时间</th>
						<th>最近使用时间</th>
						<th>操作</th>
					</tr>
					</thead>
				</table>
			</div>
			<div class="col-md-12" id="task-list-warp">
				<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
					<colgroup>
						<col width="7%" />
						<col width="7%" />
						<col width="7%" />
						<col width="10%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="14%" />
						<col width="10%" />
						<col width="10%" />
						<col width="12%" />
						<col width="12%" />
					</colgroup>
					<thead>
						<tr>
							<th><label class="checkbox-inline"><input type="checkbox"  id="check-all" onclick="checkAll(this);">编号</label></th>
							<th>周转框编号</th>
							<th>关联ID</th>
							<th>类型</th>
							<th>状态</th>
							<th>是否打印</th>
							<th>当前使用人</th>
							<th>描述</th>
							<th>创建人</th>
							<th>创建时间</th>
							<th>最近使用时间</th>
							<th>操作</th>
						</tr>
					</thead>
					<tbody>
						<#list domain.whBoxs as whBox>
							<tr>
								<td>
									<label class="checkbox-inline">
										<input type="checkbox" name="ids" value="${whBox.id}" /> ${whBox.id}
									</label>
								</td>
								<td>${whBox.boxNo}</td>
								<td>${whBox.relationNo}</td>
								<td>${util('enumName','com.estone.warehouse.enums.BoxType', whBox.type)}</td>
								<td>${util('enumName','com.estone.warehouse.enums.BoxStatus', whBox.status)}</td>
								<td>
									<#if (whBox.isPrint)??>
										${(whBox.isPrint==true)?string ("是","否")}
									<#else>
										否
									</#if>
								</td>
								<td>${util('name',whBox.presentUser)}</td>
								<td>${whBox.description}</td>
								<td>${util('name',whBox.createdBy)}</td>
								<td>${whBox.creationDate}</td>
								<td>${whBox.lastUpdateDate}</td>
								<td><button type="button" class="btn btn-xs btn-info" onclick="viewLog(${whBox.id}, 'whBox')">日志</button></td>
							</tr>
						</#list>
					</tbody>
				</table>
				<#--内容end -->
			</div>
		</div>
		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	</div>
	<div class="modal fade ui-popup" id="add_modal" style="overflow:hidden;top:150px" tabindex="-1" role="dialog" aria-labelledby="organization-add-label" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
					<h4 class="modal-title" id="expert-modal-label">新增</h4>
				</div>
				<div class="modal-body form-horizontal portlet" style="height:230px;">
					<div class="col-md-11 br-div"></div>
					<div class="col-md-12">
						<div class="line-height-label">
							<div class="col-md-3">
								<label class="control-label col-md-11">类型<span class="required">*</span></label>
							</div>
							<div class="col-md-7">
								<input class="form-control" name="query.type" type="text">
							</div>
							<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
						</div>
					</div>
					<div class="col-md-11 br-div"></div>
					<div class="col-md-12">
						<div class="line-height-label">
							<div class="col-md-3">
								<label class="control-label col-md-11">生成数量<span class="required">*</span></label>
							</div>
							<div class="col-md-7">
								<input class="form-control" name="query.num" type="number" value="1">
							</div>
							<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
						</div>
					</div>
					<div class="col-md-11 br-div"></div>
					<div class="col-md-12">
						<div class="line-height-label">
							<div class="col-md-3">
								<label class="control-label col-md-11">描述</label>
							</div>
							<div class="col-md-7">
								<textarea class="form-control description" rows="3" placeholder="描述"></textarea>
							</div>
							<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
						</div>
					</div>
					<div class="col-md-11 br-div"></div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" onclick="saveBox()">保存</button>
					<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
				</div>
			</div>
		</div>
	</div>

	<!-- 弹窗 -->
   	<div class="modal fade" id="print_modal" tabindex="-1" role="dialog" aria-labelledby="organization-add-label" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
			</div>
		</div>
	</div>

	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery-ui/jquery-ui-1.10.2.custom.min.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
	<script type="text/javascript">
		// 分页
		var total = "${domain.page.totalCount}";
		var pageNo = "${domain.page.pageNo}";
		var pageSize = "${domain.page.pageSize}";
		$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

		var boxTypes = ${domain.boxTypes};
		$("input[name='query.type']").select2({
			data : boxTypes,
			placeholder : "类型",
			allowClear : true
		});

		$("input[name='query.status']").select2({
			data : [{id : "0", text : "未使用"}, {id : "1", text : "已使用"}],
			placeholder : "状态",
			allowClear : true
		});
		// 创建人
		$.getJSON(CONTEXT_PATH + "system/saleusers/userByRole", function(json) {
			if(json) {
				$("input[name='query.createdBy']").select2({
					data: json,
					placeholder: "创建人",
					allowClear: true
				});
			} else {
				$("input[name='query.createdBy']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
			}
		});

		//全选
		function checkAll(obj) {
			$("input[name='ids']").prop("checked", $(obj).is(':checked'));
		}

	    var heights = $("body").height();
	    if(heights>910){
	        $("#fixed-bottom").addClass("fixed-bottom-height")
	    }

	 	// 获取选中的订单
		function getCheckedApvs() {
			var checkedApvs = $("input[name='ids']:checked");
			return checkedApvs;
		}

		function addBox(){
			$("#add_modal").modal('show');
			setTimeout( function(){
				$("#add_modal").draggable({
					handle: ".modal-header"
			    });
			}, 200 );
		}

		function saveBox(){
			var type = $("#add_modal input[name='query.type']").val();
			var num = $("#add_modal input[name='query.num']").val();
			var description = $("#add_modal .description").val();
			if (!type || !num || num < 1) {
				layer.alert("必填数据请填写完整！");
			}
			$.post(CONTEXT_PATH + "warehouse/boxs/create", { "whBox.type": type, "whBox.description": description, "num": num }, function(data) {
			    if(data.status == 200){
					layer.alert(data.message);
				}else {
                    layer.alert("添加失败！",'error');
				}
				$("#add_modal").modal('hide');
			});
		}

		function printBox(ids){
			var checked = $("input[name='ids']:checked");
			if (checked.length == 0) {
				layer.alert("请选择周转框!");
				return;
			}
			if (!ids) {
				ids = getCheckedApvs().serialize();
			}
			layer.prompt({
				formType: 2,
				title: '请输入打印备注',
				yes: function (index, layero) {
					var value = layero.find(".layui-layer-input").val();
					var url = CONTEXT_PATH + "warehouse/boxs/print?" + ids;
					if (value) {
						url = url + "&remark=" + value;
					}
					$("#print_modal").removeData("bs.modal");
					$("#print_modal").modal({
						remote: url
					});
					layer.close(index);
				}
			});
		}

		// 解锁周转框
		function boxNoUnbind() {
			var checked = $("input[name='ids']:checked");
			if (checked.length == 0) {
				layer.alert("请选择周转框!");
				return;
			}
			if (checked.length > 1) {
				layer.alert("请选择单个周转框!");
				return;
			}

			if (confirm("确定解锁周转框？")) {
				$.getJSON(CONTEXT_PATH + "warehouse/boxs/unbind?" + checked.serialize(), function(data) {
					if(data.status == 200){
						layer.confirm("解绑成功！",{
							icon: 1,
							btn: ['确定']
						},function () {
							window.location.reload();
						})
					}else{
						layer.alert("解绑失败！");
					}
				});
			}
		}

		//批量解锁周转框
		function boxNoBatchUnbind() {
			var checked = $("input[name='ids']:checked");
			if (checked.length == 0) {
				layer.alert("请选择周转框!");
				return;
			}

			if (confirm("确定解锁周转框？")) {
				$.getJSON(CONTEXT_PATH + "warehouse/boxs/batchUnbind?" + checked.serialize(), function(data) {
					if(data.status == 200){
						layer.confirm("解绑成功！",{
							icon: 1,
							btn: ['确定']
						},function () {
							location.reload();
						})
					}else{
						layer.alert("解绑失败！");
					}
				});
			}
		}

		// 绑定周转框
		function relationNoBinding() {
			if(!$("input[name='query.relationNo']").val()){
				layer.alert("请输入关联ID!");
				return ;
			}

			if(!$("input[name='query.boxNo']").val()){
				layer.alert("请输入周转框编号!");
				return ;
			}

			if (confirm("确定绑定周转框？")) {
				$.post(CONTEXT_PATH + "warehouse/boxs/binding?query.boxNo="
						+ $("input[name='query.boxNo']").val() + "&query.relationNo="
						+ $("input[name='query.relationNo']").val(), function(data) {
					if(data.status == 200){
						layer.alert("绑定成功！");
					}else{
						layer.alert("绑定失败！");
					}
				});
			}
		}
	</script>
</body>
</html>