<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<#include "/common/include.html">
		<style type="text/css">
			.modal-body input {
				width: 240px;
			}

			.top_bar {
				position: fixed;
				top: 0px;
			}

			#task-list td {
				vertical-align: middle;
			}

			#add_modal{
			    margin-top:50px;overflow:hidden;
			}
		</style>
	</head>

	<body>
		<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>

		<div id="page" style="background-color: rgb(231, 237, 248)">
			<div class="row">
				<div class="col-md-12" style="padding: 0">
					<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
						<li>
							<a href="#">库存管理</a>
						</li>
						<li class="active">库内返架批次</li>
					</ul>
				</div>
			</div>
			<!-- 内容 -->
			<div class="container-fluid" style="background-color: white;border: none">
				<div class="row">
					<div class="col-md-12 col-new-wms-8">
						<#assign query=domain.query>
						<form action="${CONTEXT_PATH}whBatchReturn/search" class="form-horizontal form-bordered form-row-stripped" method="post" modelAttribute="domain" name="whBatchReturnForm" id="domain">
							<!-- 分页信息 -->
							<input id="page-no" type="hidden" name="page.pageNo" value="1">
							<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
							<input type="hidden" name="query.type" value="KNFJ">
							<div class="form-body">
								<div class="form-group">
									<label class="control-label col-md-1">创建时间:</label>
									<div class="col-md-2">
                                        <input class="form-control Wdate" type="text" name="query.fromCreationDate" placeholder="" readonly="readonly" value="${query.fromCreationDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
                                    <label class="control-label col-md-1" style="width: 1%;">到</label>
                                    <div class="col-md-2">
                                        <input class="form-control Wdate" type="text" name="query.toCreationDate" placeholder="" readonly="readonly" value="${query.toCreationDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    </div>
									<label class="control-label col-md-1">返架批次号:</label>
									<div class="col-md-2">
										<input class="form-control" type="text" name="query.orderNo" placeholder="返架批次号" value="${query.orderNo }">
									</div>
									<label class="control-label col-md-1">状态:</label>
									<div class="col-md-2">
										<input class="form-control" type="text" name="query.status" value="${query.status}">
									</div>
									<label class="control-label col-md-1">类型:</label>
									<div class="col-md-2">
										<input class="form-control" type="text" name="query.warehouseId" value="${query.warehouseId}">
									</div>
                                    <label class="control-label col-md-1">SKU:</label>
                                    <div class="col-md-2">
                                        <input class="form-control" type="text" name="query.sku" placeholder="请输入SKU" value="${query.sku }">
                                    </div>
									<label class="control-label col-md-1">唯一码:</label>
									<div class="col-md-2">
										<input class="form-control" type="text" name="query.uuid" placeholder="请输入唯一码" value="${query.uuid}">
									</div>
								</div>
                                <div class="form-group">

                                </div>
							</div>

							<div>
								<div class="pull-left" style="margin-left: 10px;">
                                    <@header method="auth" authCode="RETREVE_BATCH_MANAGE_PRINT_RETURN_BATCH_NUMBER_1">
									<button type="button" class="btn  btn-default" onclick="batchaPrintQRCode()">
										<i class="icon-print"></i> 打印退货批次号
									</button>
                                    </@header>
                                    <@header method="auth" authCode="RETREVE_BATCH_MANAGE_ADD_1">
									<button type="button" id="addBtn" class="btn btn-default" onclick="add()">
										<span class="icon-plus"></span> 新增
									</button>
                                    </@header>
                                      <@header method="auth" authCode="RETREVE_BATCH_MANAGE_EXCEPTION_COMPLETE_1">
									<button type="button" id="batchExceptionCompleteBtn" class="btn btn-default" onclick="batchExceptionComplete()">
										异常完成
									</button>
                                    </@header>
								</div>

								<div class="col-md-offset-10" style="text-align: right">
                                    <@header method="auth" authCode="RETREVE_BATCH_MANAGE_DOWNLOAD_DETAIL_1">
									<button type="button" class="btn btn-default" onclick="downloadRecord()">
										<i class="icon-download"></i> 导出明细
									</button>
                                    </@header>
									<button type="button" class="btn default" onclick="formReset(this)">
										<i class="icon-refresh"></i> 重置
									</button>
									<button type="submit" class="btn blue">
										<i class="icon-search"></i> 查询
									</button>
								</div>
							</div>
						</form>
					</div>
					<br/>
					<div id="errorMsg" style="display: none">${domain.errorMsg}</div>
				</div>

				<div class="row">
					<div id="fixedDiv" class="col-md-12">
						<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
							<colgroup>
								<col width="5%" />
								<col width="10%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="10%" />
								<col width="15%" />
								<col width="15%" />
								<col width="10%" />
								<col width="15%" />
							</colgroup>
							<thead>
								<tr>
									<th>编号</th>
									<th>返架批次号</th>
									<th>类型</th>
									<th>SKU数</th>
									<th>播种件数</th>
									<th>返架时<br/>库位件数</th>
									<th>返架件数</th>
									<th>创建日期</th>
                                    <th>编辑日期</th>
									<th>状态</th>
									<th>操作</th>
								</tr>
							</thead>
						</table>
					</div>
					<div class="col-md-12" id="task-list-warp">
						<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
							<colgroup>
								<col width="5%" />
								<col width="10%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="10%" />
								<col width="15%" />
								<col width="15%" />
								<col width="10%" />
								<col width="15%" />
							</colgroup>
							<thead>
								<tr>
									<th><label class="checkbox-inline"><input type="checkbox" id="check-all" name="checkAll"> 编号</label></th>
									<th>返架批次号</th>
									<th>类型</th>
									<th>SKU数</th>
									<th>播种件数</th>
									<th>返架时<br/>库位件数</th>
									<th>返架件数</th>
									<th>创建日期</th>
									<th>编辑日期</th>
									<th>状态</th>
									<th>操作</th>
								</tr>
							</thead>
							<tbody>
								<#list domain.whBatchReturns as whBatchReturn>
									<tr>
										<td>
											<label class="checkbox-inline"><input type="checkbox" value="${whBatchReturn.id}" name="ids"> ${whBatchReturn.id}</label>
										</td>
										<td>${whBatchReturn.orderNo}</td>
										<td>
											<#if whBatchReturn.warehouseId??>
												${util('enumName', 'com.estone.warehouse.enums.BatchReturnType', whBatchReturn.warehouseId)}
												<#else >
												普通
											</#if>
										</td>
										<td>${whBatchReturn.whBatchReturnSkus?size}</td>
										<td>${whBatchReturn.quantity}</td>
										<td>${whBatchReturn.zfQty}</td>
										<td>${whBatchReturn.completeQuantity}</td>
										<td>${whBatchReturn.creationDate}</td>
										<td>${whBatchReturn.lastUpdateDate}</td>
										<td id="${whBatchReturn.id}_status">${util('enumName', 'com.estone.warehouse.enums.WhBatchReturnSkuStatus', whBatchReturn.status)}</td>
										<td>
											<a type="button" class="btn btn-xs btn-info" href="${CONTEXT_PATH}whBackUp/detail?id=${whBatchReturn.id}">查看</a>
											<button type="button" class="btn btn-xs btn-info" onclick="viewLog(${whBatchReturn.id}, 'whBatchReturn')">日志</button>
										</td>
									</tr>
								</#list>
							</tbody>
						</table>
					</div>
				</div>
				<div id="fixed-bottom">
					<div id="pager"></div>
				</div>
			</div>
			<!-- 打印弹窗 -->
			<div class="modal fade ui-popup" id="print_modal" tabindex="-1" role="dialog" aria-labelledby="organization-add-label" aria-hidden="true">
				<div class="modal-dialog">
					<div class="modal-content"></div>
				</div>
			</div>
			<#include "/common/footer.html">
		</div>

		<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
		<script type="text/javascript">
			// 分页
			var total = "${domain.page.totalCount}";
			var pageNo = "${domain.page.pageNo}";
			var pageSize = "${domain.page.pageSize}";

			$("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });
			var heights = $("body").height();
			if(heights > 910) {
				$("#fixed-bottom").addClass("fixed-bottom-height")
			}

			// 全选
			var checkAll = $("input[name='checkAll']");
			// 子选项
			var itemIds = $("input[name='ids']");
			checkAll.change(
				function() {
					itemIds.prop("checked", $(this).prop("checked"));
					itemIds.each(function() {
						var f = $(this).is(":checked");
						var checkClass = $(this).prop("class");
						$("." + checkClass).each(function() {
							$(this).prop("checked", f);
						})
					})
				}
			);
			// 获取选中的记录
			function getCheckedIds() {
				var checkedIds = $("input[name='ids']:checked");
				return checkedIds;
			}

			// 状态
			var resultJson = ${domain.statusJson};
			$("input[name='query.status']").select2({
				data : resultJson,
				placeholder : "状态",
				allowClear : true
				//multiple: true
			});


			var batchTypeJson = ${domain.batchTypeJson};
			$("input[name='query.warehouseId']").select2({
				data : batchTypeJson,
				placeholder : "类型",
				allowClear : true
			});

			// 只有美景才有按钮
            var localWarehouse = "${util('local')}";
			if(localWarehouse == '美景仓'){
                $("#addBtn").hide();
                //$("#batchExceptionCompleteBtn").hide();
			}
			var errorMsg = $("#errorMsg").text();
			if(errorMsg != ''){
				customizeLayer(errorMsg, 'error');
			}

			//异常完成
			function batchExceptionComplete() {
				var checkedDatas = getCheckedIds();
				if(checkedDatas.length == 0) {
					layer.alert("请选择要操作的退货批次", 'error');
					return;
				}
				var diglog = dialog({
					title: '异常完成',
				    content: "确认异常完成？",
				    width: 200,
                    height: 30,
				    onshow: function () {
				    },
				    okValue: '确定',
			        ok: function () {

						var ids = checkedDatas.serialize();
                        $.getJSON(CONTEXT_PATH + "whBackUp/batchExceptionComplete?" + ids, function(result){
                            if(result.status == 200) {
								layer.alert("操作成功!");
                                setTimeout( function(){
                                    window.location.reload();
                                }, 200 );
                            } else {
                                customizeLayer(result.message, "error");
                            }
                        });
			        	setTimeout(function () {
				    		diglog.close().remove();
				    	}, 100);
				    	return false;
			        },
			        cancelValue: '取消',
			       	cancel: function () {}
				}).showModal();

			}

			// 导出
			function downloadRecord() {
				var checkedDatas = getCheckedIds();
				if(checkedDatas.length == 0) {
					var param = $("#domain").serialize();

					window.open(CONTEXT_PATH + "whBackUp/download?" + param);
				} else {
					var ids = checkedDatas.serialize();
					window.open(CONTEXT_PATH + "whBackUp/download?" + ids);
				}
			}

            //打印退货批次码
            function batchaPrintQRCode() {
                var checkedDatas = getCheckedIds();

                if(checkedDatas.length == 0) {
                    layer.alert("请选择要操作的批次",'error');
                    return;
                }else if (checkedDatas.length > 10) {
                    layer.alert("批量打印不能超过10个批次!",'error');
                    return;
                }
                var ids = new Array();
                var falseArray = new Array();
                checkedDatas.each(function(i) {
                    var id = $(this).val();
                    var status = $("#"+id+"_status").text();
                    if (status.indexOf('未生成') != -1) {
                        //falseArray.push(id);
                    } else {
                        //ids.push(id);
                    }
                    ids.push(id);
                });

                if (falseArray.length>0) {
                    layer.alert("选择了的:"+falseArray.length+"条不符合的批次将不会打印", 'error');
                    return;
                }

                if (ids.length>0) {
                    $("#print_modal").removeData("bs.modal");
                    $("#print_modal").modal({
                        remote: CONTEXT_PATH + "whBackUp/qRCodePrint?ids=" + ids
                    });
                }
            }

			// 新增
			function add() {
                window.location.href=CONTEXT_PATH + "whBackUp/create";
            }

		</script>
	</body>

</html>