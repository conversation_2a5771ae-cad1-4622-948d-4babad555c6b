<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
<!--	<#include "/common/webSocket.html">-->
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
#add_modal{
    margin-top:50px;overflow:hidden;
}
.portlet{
    height: 50px;
    margin-top: 10px;
}
.group-inline {
	color: red;
}
</style>
</head>
<body>
<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">仓库管理</a></li>
					<li class="active">库存记录</li>
				</ul>
			</div>
		</div>

		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">

		<!-- BEGIN PAGE HEADER-->
		<#assign query=domain.query>
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="col-md-12">
				<form action="${CONTEXT_PATH}warehouse/records/search"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="checkInForm" id="domain">
					<!-- 分页信息 -->
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-1">SKU</label>
							<div class="col-md-3">
								<input class="form-control" name="query.skuSplit" type="text" value="${query.skuSplit}"  id="uuidSku" onkeypress="if(event.keyCode==13) { inputSku(this); return false;}">
							</div>
							<label class="control-label col-md-1">货位号</label>
							<div class="col-md-3">
								<input class="form-control" name="query.locationNoSplit" type="text" value="${query.locationNoSplit}">
							</div>
							<label class="control-label col-md-1">区域</label>
							<div class="col-md-3">
								<input class="form-control" id="query.area" name="query.area" type="text" value="${query.area}">
							</div>
						</div>
						<div class="form-group">
							
							<label class="control-label col-md-1">通道</label>
							<div class="col-md-3">
								<input class="form-control" id="query.access" name="query.access" type="text" value="${query.access}">
							</div>
							<label class="control-label col-md-1">名称</label>
							<div class="col-md-3">
								<input class="form-control" name="query.skuName" type="text" value="${query.skuName}">
							</div>
							<label class="control-label col-md-1">仓库库存范围</label>
							<div class="col-md-3 input-group">
								<input type="number" name="query.thenQuantity" id="thenQuantity" value="${query.thenQuantity }" class="Wdate form-control" digits="true"/>
								<span class="input-group-addon">到</span>
								<input type="number" name="query.lessQuantity" id="lessQuantity" value="${query.lessQuantity }" class="Wdate form-control" digits="true"/>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1">可用库存范围</label>
							<div class="col-md-3 input-group">
								<input type="number" name="query.thenSurplusQuantity" id="thenSurplusQuantity" value="${query.thenSurplusQuantity }" class="Wdate form-control" digits="true"/>
								<span class="input-group-addon">到</span>
								<input type="number" name="query.lessSurplusQuantity" id="lessSurplusQuantity" value="${query.lessSurplusQuantity }" class="Wdate form-control" digits="true"/>
							</div>
							<label class="control-label col-md-1">拣货库存范围</label>
							<div class="col-md-3 input-group">
								<input type="number" name="query.thenPickQuantity" id="thenPickQuantity" value="${query.thenPickQuantity }" class="Wdate form-control" digits="true"/>
								<span class="input-group-addon">到</span>
								<input type="number" name="query.lessPickQuantity" id="lessPickQuantity" value="${query.lessPickQuantity }" class="Wdate form-control" digits="true"/>
							</div>
							<label class="control-label col-md-1">过滤库存为0</label>
							<div class="col-md-3">
								<input style="margin-top: 10px;" name="query.isFilterZero" type="checkbox" onchange="filterZero(this)" value="${query.isFilterZero}">
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1">是否统计汇总</label>
							<div class="col-md-3">
								<select class="form-control" name="query.isTotal" value="${query.isTotal}">
									<option value="false">否</option>
									<option <#if query.isTotal == true>selected</#if> value="true">是</option>
								</select>
							</div>
                            <label class="control-label col-md-1">最新移库日期</label>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <input class="form-control Wdate" type="text" name="query.fromMoveLastUpdateTime" placeholder="" readonly="readonly" value="${query.fromMoveLastUpdateTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    <span class="input-group-addon">到</span>
                                    <input class="form-control Wdate" type="text" name="query.toMoveLastUpdateTime" placeholder="" readonly="readonly" value="${query.toMoveLastUpdateTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>
                            <label class="control-label col-md-1">在途库存范围</label>
							<div class="col-md-3 input-group">
								<input type="number" name="query.thenOnPassageQuantity" id="thenOnPassageQuantity" value="${query.thenOnPassageQuantity }" class="Wdate form-control" digits="true"/>
								<span class="input-group-addon">到</span>
								<input type="number" name="query.lessOnPassageQuantity" id="lessOnPassageQuantity" value="${query.lessOnPassageQuantity }" class="Wdate form-control" digits="true"/>
							</div>
						</div>

						<div class="form-group">
							<label class="control-label col-md-1">是否备货</label>
							<div class="col-md-3 input-group">
								<!--是否备货，1表示不备货，0表示备货-->
								<select name="query.noStockUp" class="form-control" value="${domain.query.noStockUp}" id="sku-noStockUp">
									<option value=""></option>
									<option value="0">备货</option>
									<option value="1">不备货</option>
								</select>
							</div>

						</div>
					</div>
					<div>
						<div class="pull-left" style="margin-bottom: 10px;">
							<@header method="auth" authCode="WH_RECORD_ADD">
							<button type="button" class="btn btn-default" onclick="addBox()">
								添加
							</button></@header>
						</div>
						<div class="col-md-offset-10" style="text-align: right">
							<button type="button" class="btn btn-default" onclick="formReset(this)">
								<i class="icon-refresh"></i> 重置
							</button>
							<button type="submit" class="btn blue">
								<i class="icon-search"></i> 查询
							</button>
							<button type="button" class="btn btn-default" onclick="downloadRecords()">
								<i class="icon-download"></i> 导出
							</button>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>
		
		<div class="row">
			<div id="fixedDiv" class="col-md-12">
				<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
                    <colgroup>
                        <col width="6%" />
                        <col width="6%" />
                        <col width="8%" />
                        <col width="3%" />
                        <col width="3%" />
                        <col width="3%" />
                        <col width="3%" />
                        <col width="7%" />
                        <col width="5%" />
                        <col width="6%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="6%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="3%" />
                        <col width="7%" />
                        <col width="4%" />
                    </colgroup>
					<thead>
					<tr>
						<th>
							<label class="checkbox-inline">
                                编号
							</label>
						</th>
                        <th>SKU</th>
                        <th>商品名称</th>
                        <th>单品<br/>状态</th>
                        <th>仓库销<br/>售属性</th>
                        <th>销售频次<br/>（30天）</th>
                        <th>动销频次<br/>（30天）</th>
                        <th>货位号</th>
                        <th>在途库存</th>
                        <th>仓库库存</th>
                        <th>待QC</th>
                        <th>待上架</th>
                        <th>上架中</th>
                        <th>可用库存</th>
                        <th>已分配</th>
                        <th>已拣库存</th>
                        <th>库位库存</th>
                        <th>调拨库存</th>
                        <th>最近修改人/时间</th>
                        <th>最新移库日期</th>
                    </tr>
					</thead>
				</table>
			</div>
			<div class="col-md-12" id="task-list-warp">
				<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
						<colgroup>
							<col width="6%" />
							<col width="6%" />
							<col width="8%" />
							<col width="3%" />
							<col width="3%" />
							<col width="3%" />
							<col width="3%" />
							<col width="7%" />
							<col width="5%" />
							<col width="6%" />
							<col width="5%" />
							<col width="5%" />
							<col width="5%" />
							<col width="6%" />
							<col width="5%" />
							<col width="5%" />
							<col width="5%" />
							<col width="3%" />
							<col width="7%" />
							<col width="4%" />
						</colgroup>
						<thead>
							<tr>
                                <th><label class="checkbox-inline"><input type="checkbox" name="checkAll"> 编号</label></th>
                                <th>SKU</th>
                                <th>商品名称</th>
                                <th>单品<br/>状态</th>
                                <th>仓库销<br/>售属性</th>
                                <th>销售频次<br/>（30天）</th>
                                <th>动销频次<br/>（30天）</th>
                                <th>货位号</th>
                                <th>在途库存</th>
                                <th>仓库库存</th>
                                <th>待QC</th>
                                <th>待上架</th>
                                <th>上架中</th>
                                <th>可用库存</th>
                                <th>已分配</th>
                                <th>已拣库存</th>
                                <th>库位库存</th>
                                <th>调拨库存</th>
                                <th>最近修改人/时间</th>
                                <th>最新移库日期</th>
							</tr>
						</thead>
						<tbody>
							<#list domain.whRecords as whRecord>
								<tr>
									<td><label class="checkbox-inline"><input class="${whRecord.id}" name="query.ids" type="checkbox" value="${whRecord.id}"> ${whRecord.id} </label></td>
									<td>${whRecord.sku}</td>
									<td>${whRecord.whSku.name}</td>
									<td>${whRecord.whSku.statusName}</td>
									<td>${whRecord.whSku.saleAttributeSettingStr}</td>
									<td>${whRecord.whSku.thirtyDaysSalesOrders}</td>
									<td>${whRecord.whSku.thirtyDaysSalesDays}</td>
									<td>${whRecord.whSku.locationNumber}</td>
									<td>${whRecord.onWayQuantity}</td>
									<td>${whRecord.surplusQuantity + whRecord.allotQuantity}</td>
									<td>${whRecord.waitQcQuantity}</td>
									<td>${whRecord.waitUpQuantity}</td>
									<td>${whRecord.upingQuantity}</td>
									<td>${whRecord.surplusQuantity}</td>
									<td>${whRecord.allotQuantity - whRecord.pickSkuQuantity}</td>
									<td>${whRecord.pickSkuQuantity}</td>
									<td>${whRecord.surplusQuantity + whRecord.allotQuantity - whRecord.pickSkuQuantity}</td>
									<td>${whRecord.allocationQuantity}</td>
									<td>${util('name',whRecord.lastUpdatedBy)}<br/>${whRecord.lastUpdateDate}</td>
									<td>${whRecord.locationMoveInfo.lastUpdateDate }</td>
								</tr>
							</#list>
							<tr>
								<td colspan="8"><span class="group-inline">汇总：</span></td>
								<td><span class="group-inline">${domain.resultRecordGroup.groupOnWayQuantity }</span></td>
								<td><span class="group-inline">${domain.resultRecordGroup.groupSurplusQuantity + domain.resultRecordGroup.groupAllotQuantity}</span></td>
								<td><span class="group-inline">${domain.resultRecordGroup.groupWaitQcQuantity }</span></td>
								<td><span class="group-inline">${domain.resultRecordGroup.groupWaitUpQuantity }</span></td>
								<td><span class="group-inline">${domain.resultRecordGroup.groupUpingQuantity }</span></td>
								<td><span class="group-inline">${domain.resultRecordGroup.groupSurplusQuantity }</span></td>
								<td><span class="group-inline">${(domain.resultRecordGroup.groupAllotQuantity - domain.resultRecordGroup.groupPickSkuQuantity)!}</span></td>
								<td><span class="group-inline">${domain.resultRecordGroup.groupPickSkuQuantity }</span></td>
								<td><span class="group-inline">${domain.resultRecordGroup.groupSurplusQuantity + (domain.resultRecordGroup.groupAllotQuantity - domain.resultRecordGroup.groupPickSkuQuantity)!}</span></td>
								<td><span class="group-inline">${domain.resultRecordGroup.groupAllocationquantity }</span></td>
								<td></td>
								<td></td>
							</tr>
						</tbody>
					</table>
			</div>
		</div>
		

		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	</div>

	<!-- 弹窗 -->
	<div class="modal fade" id="add_modal" tabindex="-1" role="dialog" aria-labelledby="organization-add-label" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
					<h4 class="modal-title" id="expert-modal-label">新增</h4>
				</div>
				<div class="modal-body form-horizontal portlet">
					<div class="col-md-11 br-div"></div>
					<div class="col-md-12">
						<div class="line-height-label">
							<div class="col-md-3">
								<label class="control-label col-md-11">sku<span class="required">*</span></label>
							</div>
							<div class="col-md-7">
								<input id="add_sku_input" type="text" placeholder="仅支持单个SKU添加" value=""/>
								<#--<textarea class="form-control description" id="add_sku_input" rows="10" placeholder="SKU支持逗号拼接"></textarea>-->
							</div>
							<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
						</div>
					</div>
					<div class="col-md-11 br-div"></div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" onclick="saveBox()">保存</button>
					<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
				</div>
			</div>
		</div>
	</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/access.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery-ui/jquery-ui-1.10.2.custom.min.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
	<script type="text/javascript">

        $("#sku-noStockUp").val("${domain.query.noStockUp}");
		// 全选
		var checkAll = $("#task-list").find("input[name='checkAll']");
		// 子选项
		var recordIds = $("#task-list").find("input[name='query.ids']");
		
		checkAll.change(
		  function () {
			  recordIds.prop("checked", $(this).prop("checked"));
			  
			  recordIds.each(function(){
				  var f = $(this).is(":checked");
					var checkClass = $(this).prop("class");
					$("." + checkClass).each(function(){
						$(this).prop("checked",f);
					})
			  })
		  }
		);
		
		recordIds.change(function(){
			var checkedLenght = recordIds.filter(":checked").length;
			var length = recordIds.length;
			checkAll.prop("checked", checkedLenght == length);
		});
	
	
		// 分页
		var total = "${domain.page.totalCount}";
		var pageNo = "${domain.page.pageNo}";
		var pageSize = "${domain.page.pageSize}";
		$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

        var heights = $("body").height();
        if(heights>910){
            $("#fixed-bottom").addClass("fixed-bottom-height")
        }
        
        function downloadRecords() {
            var diglog = dialog({
                title: '导出库存',
                width: 800,
                height:320,
                url: CONTEXT_PATH + "warehouse/records/downloadmode?type=2",
                okValue: '确定',
                ok: function () {
                    // let uuid = getFileQueueUUID();
                    // beginExport(uuid, '导出库存');

                    var exportWindow = $(this.iframeNode.contentWindow.document.body);

                    var submitForm = exportWindow.find("#submit-form");

                    var exportType = submitForm.find("input[name='exportType']:checked").val();

                    var headers =  submitForm.find("input[name='selectHeaders']:checked");

                    var submitFormParam = $('#domain').serialize();
                    // 导出当前选择
                    if(exportType == 3) {
                        var recordIds = $("#task-list").find("input[name='query.ids']:checked");
                        if(recordIds.length == 0) {
                            layer.alert("请选择要操作的数据");
                            return false;
                        }else if (recordIds.length > 300) {
                            layer.alert("选择数量不能超过300!");
                            return false;
                        }
                        submitFormParam = submitFormParam + "&" +recordIds.serialize();
                    }

                    if(headers.length == 0) {
                        layer.alert("请选择要导出的表头字段");
                        return false;
                    }
                    var headersStr = headersJoin(headers);
                    submitFormParam = submitFormParam +"&selectHeaders="+headersStr+"&exportType="+exportType;
                    //还原分页
                    $("#page-no").val("${domain.page.pageNo}");

                    postExcelFile(submitFormParam, CONTEXT_PATH + "warehouse/records/download?");
                    // postExcelFile(submitFormParam, CONTEXT_PATH + "warehouse/records/download?uuid=" + uuid);

                    $("#page-no").val("1");

                    setTimeout(function () {
                        diglog.close().remove();
                    }, 100);

                    return true;
                },
                cancelValue: '取消',
                cancel: function () {}
            });
            diglog.show();
        }

        function headersJoin(headers) {
            var checkHeaders = "";
            for (var i = 0; i < headers.length; i++) {
                var header = headers[i].value;
                if (i == 0) {
                    checkHeaders += header;
                } else {
                    checkHeaders += "," + header;
                }
            }
            return checkHeaders;
        }
		function postExcelFile(params, url) {
            downloadByPostForm(params, url);
			/*var form = document.createElement("form");
			form.style.display = 'none';
			form.action = url + params;
			form.method = "post";
			document.body.appendChild(form);
			form.submit();
			form.remove();*/
		}
        
		var locationRegionList =  ${domain.locationRegionList};
        $("input[name='query.area']").select2({
			data : locationRegionList,
			placeholder : "区域",
			multiple: true,
			allowClear : true
		});
        
        var locationAisleList =  ${domain.locationAisleList};
        $("input[name='query.access']").select2({
			data : locationAisleList,
			placeholder : "通道",
			multiple: true,
			allowClear : true
		});

        function addBox(){
            $("#add_modal").modal('show');
            setTimeout( function(){
                $("#add_modal").draggable({
                    handle: ".modal-header"
                });
            }, 200 );
        }

        function saveBox(){

			var sku = $.trim($("#add_sku_input").val());
            if(!sku){
                layer.alert('请输入SKU！','error');
                return false;
			}
			if(sku.indexOf(",") != -1 || sku.indexOf("，") != -1){
                layer.alert('只支持添加单个SKU,请输入正确的SKU！','error');
                return false;
			}
            $.getJSON(CONTEXT_PATH + "warehouse/records/add?sku=" + sku, function(data) {
                if(data.status == "200"){
					$("#add_modal").modal('hide');
					alert("添加成功！");
				}else {
                    customizeLayer(data.message,'error');
                    $("#add_sku_input").val('');
                    $("#add_sku_input").focus();
				}
            });
		}
        
        var isFilterZero = "${domain.query.isFilterZero}";
        if (isFilterZero == "true") {
        	$("input[name='query.isFilterZero']").attr('checked', true);
        }
        function filterZero(obj) {
        	if ($(obj).is(':checked')) {
        		$(obj).val(true);
        	} else {
        		$(obj).val(false);
        	}
        }
	</script>
</body>
</html>