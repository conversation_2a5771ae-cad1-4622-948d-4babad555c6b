<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
<@header method="header" active="11040000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">仓库管理</a></li>
					<li class="active">收货周转框</li>
					<li class="active">关联详情</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<#assign query=domain.query>
		<!-- END PAGE HEADER-->
		<div class="row">
			</br>
		</div>
		
		<div class="row">
			<div class="col-md-12">	
			
					<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
						<colgroup>
							<col width="10%" />
							<col width="15%" />
							<col width="20%" />
							<col width="15%" />
							<col width="15%" />
							<col width="25%" />
						</colgroup>
						<thead>
							<tr>
								<th>序号</th>
								<th>周转框</th>
								<th>快递单号</th>
								<th>件数</th>
								<th>状态</th>
								<th>操作</th>
							</tr>
						</thead>
						<tbody>
							<#list domain.whBox.whBoxItems as whBoxItem>
								<tr>
									<td>${whBoxItem_index+1}</td>
									<td>${whBoxItem.boxNo}</td>
									<#if whBoxItem.orderType?? && whBoxItem.orderType == 1>
										<td>
											${whBoxItem.trackingNum}
											<#if whBoxItem.flags?contains("TJ")>
												<span style="color: red;font-size: 14px;font-weight: 600">(特急)</span>
											</#if>
										</td>
									<#else>
										<td>${whBoxItem.relationNo}</td>
									</#if>
									<td>${whBoxItem.quantity}</td>
									<td>
										<#if (whBoxItem.isCheckIn)?? && whBoxItem.isCheckIn==true>
											已入库
										<#else>
											未入库
										</#if>
									</td>
									<td>
										<#if (whBoxItem.isCheckIn)?? && whBoxItem.isCheckIn==true>
											——
										<#else>
											<a class="btn btn-xs btn-default" onclick="unbindItem(this)" url="${CONTEXT_PATH}warehouse/boxsh/unbindItem?box_no=${whBoxItem.boxNo}&relation_no=${whBoxItem.relationNo}&order_type=${whBoxItem.orderType}">
											 	解绑
											</a>
										</#if>
									</td>
								</tr>
							</#list>
						</tbody>
					</table>
			</div>
		</div>
	</div>
	
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript">   
	
		// 详情解锁周转框
		function unbindItem($this) {
			if (confirm("确定解锁周转框？")) {
				$.getJSON($this.getAttribute("url"), function(data) {
					if(data.status == 200){
						alert("解绑成功！");
						setTimeout(function(){
							
							window.location.reload();
						},2000);
					}else{
						layer.alert("解绑失败！");
					}
				});
			}
		}
	</script>
</body>
</html>