<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
	<@header method="header" active="11030200"><#include "/ftl/header.ftl"></@header>
	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">库存管理</a></li>
					<li class="active">库存移库</li>
					<li class="active">新增移库信息</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="col-md-9">
				<form action="${CONTEXT_PATH}stockMove/create"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="checkInForm" id="domain">
					<div class="form-body">
                        <div class="form-group" style="margin-top: 20px;">
                            <label class="control-label col-md-1" style="width: auto;">移出库位</label>
                            <div class="col-md-2">
                                <select name="outLocation" id="outLocation" class="form-control">
                                    <option value=""></option>
									<option value="0">可用库存</option>
									<@header method="auth" authCode="WAITING_QC_2">
                                    	<option value="1">等待QC</option>
									</@header>
									<@header method="auth" authCode="WAITING_UP_2">
                                    	<option value="2">等待上架</option>
									</@header>
									<@header method="auth" authCode="UPING_2">
                                    	<option value="3">上架中</option>
									</@header>
									<@header method="auth" authCode="ALLOCATED_2">
                                    	<option value="4">已分配</option>
									</@header>
									<@header method="auth" authCode="PICKED_STOCK_2">
                                    	<option value="5">已拣库存</option>
									</@header>
									<@header method="auth" authCode="PICKED_RETURN_STOCK">
                                    	<option value="6">已拣返架库存</option>
									</@header>
									<@header method="auth" authCode="PICK_LACK_STOCK">
                                    	<option value="7">拣货缺货库存</option>
									</@header>
									<@header method="auth" authCode="CANCEL_STOCK">
                                    	<option value="8">取消库存</option>
									</@header>
									<@header method="auth" authCode="ORDER_ALLOCATION_2">
                                    	<option value="9">订单调拨</option>
									</@header>
									<@header method="auth" authCode="STOCK_ALLOCATION_2">
                                    	<option value="10">库存调拨</option>
									</@header>
									<@header method="auth" authCode="DELIVER_2">
                                    	<option value="11">已交运</option>
										<option value="18">已退货</option>
									</@header>
									<@header method="auth" authCode="FROZEN">
                                    	<option value="12">冻结库存</option>
									</@header>
                                </select>
                            </div>

                            <label class="control-label col-md-1">移入库位</label>
                            <div class="col-md-2">
                                <select name="inLocation" id="inLocation" class="form-control">
                                	<option value=""></option>
                                	<option value="0">可用库存</option>
									<@header method="auth" authCode="WAITING_QC_2">
                                    	<option value="1">等待QC</option>
									</@header>
									<@header method="auth" authCode="WAITING_UP_2">
                                    	<option value="2">等待上架</option>
									</@header>
									<@header method="auth" authCode="UPING_2">
                                    	<option value="3">上架中</option>
									</@header>
									<@header method="auth" authCode="ALLOCATED_2">
                                    	<option value="4">已分配</option>
									</@header>
									<@header method="auth" authCode="PICKED_STOCK_2">
                                    	<option value="5">已拣库存</option>
									</@header>
									<@header method="auth" authCode="PICKED_RETURN_STOCK">
                                    	<option value="6">已拣返架库存</option>
									</@header>
									<@header method="auth" authCode="PICK_LACK_STOCK">
                                    	<option value="7">拣货缺货库存</option>
									</@header>
									<@header method="auth" authCode="CANCEL_STOCK">
                                    	<option value="8">取消库存</option>
									</@header>
									<@header method="auth" authCode="ORDER_ALLOCATION_2">
                                    	<option value="9">订单调拨</option>
									</@header>
									<@header method="auth" authCode="STOCK_ALLOCATION_2">
                                    	<option value="10">库存调拨</option>
									</@header>
									<@header method="auth" authCode="DELIVER_2">
                                    	<option value="11">已交运</option>
                                    	<option value="18">已退货</option>
									</@header>
									<@header method="auth" authCode="FROZEN">
										<option value="12">冻结库存</option>
									</@header>
                                </select>
                            </div>

                        </div>
						<div class="form-group" style="margin-top: 50px;">
							<div class="col-md-5">库存移库SKU</div>
						</div>
						<div class="form-group">
							<div class="col-md-5">
								<div id="input_sku">
									<input style="height:35px;border-radius: 8px !important;" onkeypress="if(event.keyCode==13) { getSkuStock(); return false;}"
                                           class="form-control" placeholder="请输入SKU，多个SKU请以英文,分开" id="skus" name="skus" type="text">
								</div>
							</div>
                            <div class="col-md-2">
								<div style="border-radius: 8px !important;width: 100%;" type="button" class="btn blue" onclick="getSkuStock();">
									<i class="icon-plus"></i> 添加SKU
								</div>
                            </div>
						</div>

						<div class="form-group" style="margin-top: 20px;">
							<div class="col-md-7">
								<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
									<thead>
										<tr>
											<th>序号</th>
											<th>SKU</th>
											<th>当前库位</th>
											<th>可移动库存</th>
											<th>确认移库数量</th>
											<th>操作</th>
										</tr>
									</thead>
									<tbody id="tbody">
									</tbody>
								</table>
							</div>
						</div>

                        <div class="form-group">
                            <label class="control-label col-md-1" style="width: auto;">备注</label>
                            <textarea cols="80"  rows="2" id="remark" name="remark"></textarea>
                        </div>
					</div>
				</form>
				<div class="col-md-9" style="margin-bottom: 20px;">
					<button class="btn blue" id="confirmMove">
						<i class="icon-search"></i> 确认移动库存
					</button>
				</div>
			</div>
			<br/>
		</div>
	</div>
	
		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript">

        function getSkuStock() {
            var skus = $('#skus').val();
			var outLocationType = $('#outLocation').val();
			if(outLocationType == ''){
                layer.alert('移出库位不能为空！','error');
                $('#skus').val('');
                return;
			}
			if($('#inLocation').val() == ''){
                layer.alert('移入库位不能为空！','error');
                $('#skus').val('');
                return;
			}
            if(skus == ''){
                layer.alert('sku不能为空！','error');
                return;
			}
            $.post(CONTEXT_PATH + "stockMove/queryStocks", {skus:skus,outLocationType:outLocationType}, function(data) {
                if (data.status == 200) {
                    var sku_array=new Array();
                    $("[id=data-sku]").each(function(){
                        sku_array.push($(this).text());
                    });
                    var errorSkuStr = '';
                    var index = sku_array.length;
                    if(data.body.stockList.length > 0){
						for (var i = 0; i<data.body.stockList.length;i++) {
                        	var number=$.inArray(data.body.stockList[i].sku, sku_array);
                        	if(data.body.stockList[i].locationSelectJson == undefined){
								errorSkuStr = data.body.stockList[i].sku + "," + errorSkuStr;
							}else {
								if(number == -1){
									let no = parseInt(index+1);
									var html = "<tr>" +
											"<td id='data-index'>"+parseInt(index+1)+"</td>"+
											"<td id='data-sku'>"+data.body.stockList[i].sku+"</td>"+
											"<td id='data-oldLocation' onchange='changeLocation(this)'><input name='locationSku' id='location_"+no+"' type='text'/></td>" +
											"<td id='data-quantity'class='"+data.body.stockList[i].sku+"'></td>"+
											"<td><input type='text' id='data-newQuantity' style='width:60px;' data-canRemoveQuantity='' value='1'></td>"+
											"<td onclick='onRemove(this)'><a>删除</a></td></tr>";
									$("#tbody").append(html);
									debugger
									$("input[id='location_"+no+"']").select2({
										data: data.body.stockList[i].locationSelectJson,
										placeholder: "请选择库位",
										multiple: false,
										allowClear: false
									});
									$("input[id='location_"+no+"']").select2("val", data.body.stockList[i].locationSelectJson[0].id);
									$("input[id='location_"+no+"']").parent().next().text(data.body.stockList[i].locationSelectJson[0].id);
									index = index+1;
								}else {
									var skuNum = parseInt($('.'+data.body.stockList[i].sku).text());
									var number=1+parseInt($('.'+data.body.stockList[i].sku).next().find("input").val());
									if(number > skuNum){
										customizeLayer(data.body.stockList[i].sku + "确认移库数量不能大于可移动库存！", "error");
										return false;
									}else {
										$('.'+data.body.stockList[i].sku).next().find("input").val(number);
									}
								}
							}
						}
						if(errorSkuStr != ''){
						    layer.alert(errorSkuStr + " 可移动库存为0",'error');
						}
					}
					if(data.message != undefined && data.message != ''){
						customizeLayer(data.message, 'error');
					}
                } else {
                    customizeLayer(data.message, "error");
                }
                $('#skus').val('');
            });
        }

		// 切换库位更新迁移库存
		function changeLocation(locationTd){
			let quantity = $(locationTd).find("input[name='locationSku']").val();
			$(locationTd).next().text(quantity);
		}

        function onRemove(obj){
            if(confirm("是否删除该条SKU?")){
                $(obj).parent().remove();
                // 重新编排
                $("#tbody").find('tr').each(function (indexValue) {
					indexValue++;
					$(this).find('td[id="data-index"]').text(indexValue);
                });
            }
        }

		$('#confirmMove').on("click",function () {
            var whStockMoveItems = [];
            var skus = '';
            var outLocationType = $('#outLocation').val();
            var inLocationType = $('#inLocation').val();
            var result = false;
            $('#tbody').find('tr').each(function (index) {
                var stockMoveItem = {};
                var dataIndex = $(this).find('td[id="data-index"]').text();
                var sku = $(this).find('td[id="data-sku"]').text();
				debugger
				var location = $(this).find('td[id="data-oldLocation"]').find(".select2-chosen:eq(0)").text();
				if(location == undefined || location == ''){
					location = $(this).find('td[id="data-oldLocation"]').text();
				}
                var canOutQuantity = $(this).find('td[id="data-quantity"]').text();
                var quantity = $(this).find('td>input[id="data-newQuantity"]').val();
                if(quantity == ''){
					layer.alert('第'+dataIndex+'个sku的确认移库数量为空！','error');
					result = false;
					return false;
				}
                if(location == undefined || location == ''){
					layer.alert('第'+dataIndex+'个sku的库位为空！','error');
					result = false;
					return false;
				}
                var r = /^\d+$/;
                if(!r.test(quantity)){
                    layer.alert("请填写正整数！", "error");
                    result = false;
                    return false;
                }
                if(quantity == 0){
                    layer.alert("第"+dataIndex+"个sku 不能填写0-不需要请删除！", "error");
                    result = false;
                    return false;
                }
				if(parseInt(quantity) > parseInt(canOutQuantity)){
                    layer.alert('第'+dataIndex+'个sku的确认移库数量大于可移动库存！','error');
                    result = false;
                    return false;
				}
                skus += "," + sku;
                stockMoveItem.sku = sku;
                stockMoveItem.locationNumber = location;
                stockMoveItem.quantity = quantity;
                stockMoveItem.outLocation = outLocationType;
                stockMoveItem.inLocation = inLocationType;
                whStockMoveItems.push(stockMoveItem);
                result = true;
			});

			var remark = $('#remark').val().replace(/\s/g,'');
			if(!remark){
			    layer.alert('备注不能为空！','error');
			    return false;
			}

            if(result && remark != ''){
				var param = {
					whStockMoveItemsStr:JSON.stringify(whStockMoveItems),
					remark: remark
				};
				$.post(CONTEXT_PATH + "stockMove/create", param, function(data) {
					if (data.status == 200) {
						layer.confirm('成功！',{
							icon: 1,
							btn: ['确定']
						},function () {
							location.href = CONTEXT_PATH + "stockMove";
						})
					} else {
						customizeLayer(data.message, "error");
					}
				});
			}
        });

        var outLocationVal, inLocationVal;
        $('#outLocation').on('change',function () {
            if(this.value == $('#inLocation').val()){
                layer.alert('移入库位跟移出库位一致，请重新选择！','error');
                $("#outLocation").val('');
                return;
			}
            if($("#tbody").find('tr').length > 0){
				if(confirm("更换库位将会清空列表中的数据，请确认是否更换！")){
					$("#tbody").find('tr').remove();
				}else {
					$("#outLocation").val(outLocationVal);
					return;
				}
			}
			outLocationVal = this.value;
        });
        $('#inLocation').on('change',function () {
            if(this.value == $('#outLocation').val()){
                layer.alert('移入库位跟移出库位一致，请重新选择！','error');
                $("#inLocation").val('');
                return;
            }
            if($("#tbody").find('tr').length > 0){
                if(confirm("更换库位将会清空列表中的数据，请确认是否更换！")){
                    $("#tbody").find('tr').remove();
                }else {
					$("#inLocation").val(inLocationVal);
					return;
				}
            }
			inLocationVal = this.value;
        });
	</script>
</body>
</html>