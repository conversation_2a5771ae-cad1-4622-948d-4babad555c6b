<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
		<style type="text/css">
			.modal-body input {
				width: 240px;
			}

			.top_bar {
				position: fixed;
				top: 0px;
			}

			.btn-xs {
				padding: 0px 5px;
			}

			#task-list td {
				vertical-align: middle;
			}

			#add_modal{
				margin-top:50px;overflow:hidden;
			}
		</style>
		</head>

<body>
<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li>
					<a href="#">库存</a>
				</li>
				<li class="active">SKU移库管理</li>
			</ul>
		</div>
	</div>
	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
		<div class="row">
			<div class="col-md-12 col-new-wms-8">
				<#assign query=domain.query>
					<form action="${CONTEXT_PATH}moveSkuLocationTask/search" class="form-horizontal form-bordered form-row-stripped" method="post" modelAttribute="domain" name="moveSkuLocationTaskForm" id="domain">
						<!-- 分页信息 -->
						<input id="page-no" type="hidden" name="page.pageNo" value="1">
						<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
						<div class="form-body">
							<div class="form-group">
								<label class="control-label col-md-1" style="width:65px">SKU:</label>
								<div class="col-md-2" style="width:150px">
									<input class="form-control" type="text" name="query.sku" placeholder="多个逗号分开" value="${query.sku }">
								</div>

								<label class="control-label col-md-1" style="width:65px">移库单号:</label>
								<div class="col-md-2" style="width:150px">
									<input class="form-control" type="text" name="query.taskNo" placeholder="多个逗号分开" value="${query.taskNo }">
								</div>

								<label class="control-label col-md-1" style="width:65px">状态:</label>
								<div class="col-md-2" style="width:150px">
									<input class="form-control" type="text" name="query.statusStr" value="${query.statusStr}">
								</div>

								<label class="control-label col-md-1" style="width:65px">创建人:</label>
								<div class="col-md-2" style="width:150px">
									<input class="form-control" type="text" name="query.createdBy" value="${query.createdBy}">
								</div>
								<label class="control-label col-md-1" style="width:65px">拣货人:</label>
								<div class="col-md-2" style="width:150px">
									<input class="form-control" type="text" name="query.pickPerson" value="${query.pickPerson}">
								</div>

								<label class="control-label col-md-1" style="width:65px">上架人:</label>
								<div class="col-md-2" style="width:150px">
									<input class="form-control" type="text" name="query.upPerson" value="${query.upPerson}">
								</div>

							</div>
							<div class="form-group">

								<label class="control-label col-md-1" style="width:65px">创建时间:</label>
								<div class="col-md-2" style="width:150px">
									<input class="form-control Wdate" type="text" name="query.fromCreationDate" placeholder="" readonly="readonly" value="${query.fromCreationDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
								<label class="control-label col-md-1" style="width:65px">到</label>
								<div class="col-md-2" style="width:150px">
									<input class="form-control Wdate" type="text" name="query.toCreationDate" placeholder="" readonly="readonly" value="${query.toCreationDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
								<label class="control-label col-md-1" style="width:65px">领取时间:</label>
								<div class="col-md-2" style="width:150px">
									<input class="form-control Wdate" type="text" name="query.fromReceiveDate" placeholder="" readonly="readonly" value="${query.fromReceiveDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
								<label class="control-label col-md-1" style="width:65px">到</label>
								<div class="col-md-2" style="width:150px">
									<input class="form-control Wdate" type="text" name="query.toReceiveDate" placeholder="" readonly="readonly" value="${query.toReceiveDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>

								<label class="control-label col-md-1" style="width:65px">完成时间:</label>
								<div class="col-md-2" style="width:150px">
									<input class="form-control Wdate" type="text" name="query.fromUpEndDate" placeholder="" readonly="readonly" value="${query.fromUpEndDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
								<label class="control-label col-md-1" style="width:65px">到</label>
								<div class="col-md-2" style="width:150px">
									<input class="form-control Wdate" type="text" name="query.toUpEndDate" placeholder="" readonly="readonly" value="${query.toUpEndDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>

							</div>
						</div>

						<div>
							<div class="pull-left" style="margin-left: 10px;">
								<button type="button" id="addBtn" class="btn btn-default" onclick="add()">
									<span class="icon-plus"></span> 新建SKU移库单
								</button>
								<button type="button" id="batchExceptionCompleteBtn" class="btn btn-default" onclick="cancelTask()">
									废弃SKU移库单
								</button>
							</div>

							<div class="col-md-offset-10" style="text-align: right">
								<button type="button" class="btn btn-default" onclick="downloadRecord()">
									<i class="icon-download"></i> 导出
								</button>
								<button type="button" class="btn default" onclick="formReset(this)">
									<i class="icon-refresh"></i> 重置
								</button>
								<button type="submit" class="btn blue">
									<i class="icon-search"></i> 查询
								</button>
							</div>
						</div>
					</form>
			</div>
			<br/>
		</div>

		<div class="row">
			<div id="fixedDiv" class="col-md-12">
				<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
					<colgroup>
						<col width="5%" />
						<col width="10%" />
						<col width="5%" />
						<col width="5%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
					</colgroup>
					<thead>
					<tr>
						<th>编号</th>
						<th>移库单号</th>
						<th>SKU</th>
						<th>PCS</th>
						<th>差异PCS</th>
						<th>周转筐</th>
						<th>状态</th>
						<th>创建人/创建时间</th>
						<th>拣货人/完成时间</th>
						<th>上架人/完成时间</th>
						<th>操作</th>
					</tr>
					</thead>
				</table>
			</div>
			<div class="col-md-12" id="task-list-warp">
				<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
					<colgroup>
						<col width="5%" />
						<col width="10%" />
						<col width="5%" />
						<col width="5%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
					</colgroup>
					<thead>
					<tr>
						<th><label class="checkbox-inline"><input type="checkbox" id="check-all" name="checkAll"> 编号</label></th>
						<th>移库单号</th>
						<th>SKU</th>
						<th>PCS</th>
						<th>差异PCS</th>
						<th>周转筐</th>
						<th>状态</th>
						<th>创建人/创建时间</th>
						<th>拣货人/完成时间</th>
						<th>上架人/完成时间</th>
						<th>操作</th>
					</tr>
					</thead>
					<tbody>
					<#list domain.moveLocationTasks as task>
						<tr>
							<td>
								<label class="checkbox-inline"><input type="checkbox" value="${task.id}" name="ids"> ${task.id}</label>
							</td>
							<td>${task.taskNo}</td>
							<td>${task.skuCount}</td>
							<td>${task.pcsCount}</td>
							<td>-</td>
							<td>${task.boxNo}</td>
							<td>${util('enumName', 'com.estone.warehouse.enums.MoveLocationTaskStatus', task.taskStatus)}</td>
							<td>${util('name',task.createdBy)} <br/> ${task.creationDate}</td>
							<td>${util('name',task.pickPerson)} <br/> ${task.pickingEndDate}</td>
							<td>${util('name',task.upPerson)} <br/> ${task.upEndDate}</td>
							<td>
								<#if task.taskStatus?? && task.taskStatus==0 >
									<a class="btn btn-default" href="${CONTEXT_PATH}moveSkuLocationTask/update?taskId=${task.id}">
										<i class="icon-edit"></i> 编辑
									</a>
								</#if>

									<a class="btn btn-default" href="${CONTEXT_PATH}moveSkuLocationTask/detail?taskId=${task.id}">
										<i class="icon-detail"></i> 明细
									</a>

								<button type="button" class="btn btn-xs btn-info" onclick="viewLog(${task.id}, 'moveSkuLocationTask')">日志</button>
							</td>
						</tr>
					</#list>
					</tbody>
				</table>
			</div>
		</div>
		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	</div>

	<#include "/common/footer.html">
</div>

<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
<script type="text/javascript">
    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";

    $("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });
    var heights = $("body").height();
    if(heights > 910) {
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }

    // 全选
    var checkAll = $("input[name='checkAll']");
    // 子选项
    var itemIds = $("input[name='ids']");
    checkAll.change(
        function() {
            itemIds.prop("checked", $(this).prop("checked"));
            itemIds.each(function() {
                var f = $(this).is(":checked");
                var checkClass = $(this).prop("class");
                $("." + checkClass).each(function() {
                    $(this).prop("checked", f);
                })
            })
        }
    );
    // 获取选中的记录
    function getCheckedIds() {
        var checkedIds = $("input[name='ids']:checked");
        return checkedIds;
    }

    // 创建人
    $.getJSON(CONTEXT_PATH + "system/saleusers/userByRole", function(json){
        if (json) {
            $("input[name='query.createdBy']").select2({
                data : json,
                placeholder : "创建人",
                allowClear : true
            });
            $("input[name='query.pickPerson']").select2({
                data : json,
                placeholder : "拣货人",
                allowClear : true
            });
            $("input[name='query.upPerson']").select2({
                data : json,
                placeholder : "上架人",
                allowClear : true
            });
        } else {
            $("input[name='query.createdBy']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
        }
    });

    // 状态
    var resultJson = ${domain.statusJson};
    $("input[name='query.statusStr']").select2({
        data : resultJson,
        placeholder : "状态",
        allowClear : true,
        multiple: true
    });
    // 新增
    function add() {
        window.location.href=CONTEXT_PATH + "moveSkuLocationTask/create";
    }
    //生成拣货任务
    function cancelTask(){
        debugger;
        var checkedDatas = getCheckedIds();
        if(checkedDatas.length == 0) {
            layer.alert("请选择要操作的移库单", 'error');
            return;
        }

        var ids = checkedDatas.serialize();
        $.post(CONTEXT_PATH + "moveSkuLocationTask/cancel?" + ids, function(result){
            if(result.status == 200) {
                layer.alert(result.message, "success");
                setTimeout(function () {
                    window.location.reload();
                }, 2000);
            } else {
                customizeLayer(result.message, "error");
            }
        });
    }

    // 导出
    function downloadRecord() {
        var checkedDatas = getCheckedIds();
        if(checkedDatas.length == 0) {
            var param = $("#domain").serialize();

            window.open(CONTEXT_PATH + "moveSkuLocationTask/download?" + param);
        } else {
            var ids = checkedDatas.serialize();
            window.open(CONTEXT_PATH + "moveSkuLocationTask/download?" + ids);
        }
    }
</script>
</body>

</html>