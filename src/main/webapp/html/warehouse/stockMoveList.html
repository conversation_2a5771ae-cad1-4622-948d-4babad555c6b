<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
	<#include "/common/include.html">
	<style type="text/css">
		#task-list td {
			vertical-align:middle;
		}
		.modal{
			margin-top: 60px;
		}
	</style>
</head>
<body>
<@header method="header" active="11030200"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">库存管理</a></li>
				<li class="active">库存移库</li>
			</ul>
		</div>
	</div>
	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
		<div class="row search-nav">
			<div class="col-md-12">
				<#assign query=domain.query>
				<form action="${CONTEXT_PATH}stockMove/search"
						   class="form-horizontal form-bordered form-row-stripped"
						   method="post" modelAttribute="domain" id="domain" name="stockMoveForm">
					<!-- 分页信息 -->
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
					<div class="form-group">
						<label class="control-label col-md-1">SKU</label>
						<div class="col-md-3">
							<input class="form-control" type="text" name="query.sku" placeholder="请输入SKU" value="${domain.query.sku}"
								 id="uuidSku" onkeypress="if(event.keyCode==13) { inputSku(this); return false;}">
						</div>

                        <label class="control-label col-md-1">移出库位</label>
                        <div class="col-md-3">
                            <input class="form-control" name="query.outLocation" type="text" value="${domain.query.outLocation}">
                        </div>

                        <label class="control-label col-md-1">移入库位</label>
                        <div class="col-md-3">
                            <input class="form-control" name="query.inLocation" type="text" value="${domain.query.inLocation}">
                        </div>

					</div>

					<div class="form-group">
						<label class="control-label col-md-1">创建时间</label>
						<div class="col-md-3">
							<div class="input-group">
								<input class="form-control Wdate" type="text" name="query.fromCreateDate" placeholder="" readonly="readonly" value="${domain.query.fromCreateDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								<span class="input-group-addon">到</span>
								<input class="form-control Wdate" type="text" name="query.toCreateDate" placeholder="" readonly="readonly" value="${domain.query.toCreateDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
							</div>
						</div>

						<label class="control-label col-md-1">操作人</label>
						<div class="col-md-3">
                            <div><input type="text" name="query.createBy" class="form-control" value="${domain.query.createBy}" /></div>
						</div>

					</div>

					<div>
						<div class="pull-left" style="margin-top: 10px;">
							<@header method="auth" authCode="SHELF_TRANSFER_CREATE_SHELF_TRANSFER">
								<a class="btn btn-default" href="${CONTEXT_PATH}stockMove/create">
									<span class="icon-plus"></span> 新增库存移库
								</a>
							</@header>
                            <@header method="auth" authCode="SHELF_TRANSFER_IMPORT_TRANSFER_SHELF">
							<span class="btn btn-default fileinput-button">
								<span class="icon-plus"> 导入移库</span>
								<input type="file" name="file" onchange="importItem(this)" />
							</span>
							<a class="btn btn-default" href="${CONTEXT_PATH}file/execl/move_import.xlsx">
								<i class="icon-download"></i> 下载移库范例Excel
							</a>
                            </@header>
						</div>
						<div class="col-md-offset-10" style="text-align: right">
							<button type="button" class="btn btn-default" onclick="formReset(this)">
                                <i class="icon-refresh"></i> 重置
                            </button>
							<button type="submit" class="btn blue"><i class="icon-search"></i> 查询</button>
                            <@header method="auth" authCode="SHELF_TRANSFER_MOVE_DOWNLOAD">
                            <button type="button" class="btn btn-default" onclick="download()">
                                <i class="icon-download"></i> 导出
                            </button>
                            </@header>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>

		<div class="row">
            <div id="fixedDiv" class="col-md-12">
                <table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
                    <colgroup>
                        <col width="5%"/>
                        <col width="5%" />
                        <col width="15%" />
                        <col width="15%" />
                        <col width="15%" />
                        <col width="15%" />
                        <col width="20%" />
                        <col width="10%" />
                    </colgroup>
                    <thead>
                    <tr>
                        <th>ID</th>
                        <th>SKU数量</th>
                        <th>移出库位</th>
                        <th>移入库位</th>
                        <th>操作人</th>
                        <th>操作时间</th>
                        <th>备注</th>
                        <th>移库明细</th>
                    </tr>
                    </thead>
                </table>
            </div>
			<div class="col-md-12" id="task-list-warp">
				<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                    <colgroup>
                        <col width="5%"/>
                        <col width="5%" />
                        <col width="15%" />
                        <col width="15%" />
                        <col width="15%" />
                        <col width="15%" />
                        <col width="20%" />
                        <col width="10%" />
                    </colgroup>
                    <thead>
                    <tr>
                        <th><label class="checkbox-inline"><input type="checkbox"  id="check-all" onclick="checkAll(this);">编号</label></th>
                        <th>SKU数量</th>
                        <th>移出库位</th>
                        <th>移入库位</th>
                        <th>操作人</th>
                        <th>操作时间</th>
                        <th>备注</th>
                        <th>移库明细</th>
                    </tr>
					</thead>
					<tbody>
						<#list domain.whStockMoves as sm>
							<tr>
								<td>
                                    <label class="checkbox-inline">
                                        <input type="checkbox" name="ids" value="${sm.moveId}" /> ${sm.moveId}
                                    </label>
                                </td>
								<td>${sm.quantity }</td>
								<td>${util('enumName', 'com.estone.warehouse.enums.StockLogType', sm.outLocation)}</td>
								<td>${util('enumName', 'com.estone.warehouse.enums.StockLogType', sm.inLocation)}</td>
								<td>${util('name',sm.createBy)}</td>
								<td>${sm.createDate }</td>
								<td>${sm.remark }</td>
								<td>
									<a class="btn btn-xs btn-default" target="_blank" href="${CONTEXT_PATH}stockMoveItem/queryItemsById?moveId=${sm.moveId }">
										<span class="icon-edit"></span>  明细
									</a>
								</td>
							</tr>
						</#list>
					</tbody>
				</table>
			</div>
		</div>
		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	</div>
	<#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript">

    var stockLocations = ${domain.stockLocations}
	$("input[name='query.outLocation']").select2({
		data : stockLocations,
		placeholder : "移出库位",
		allowClear : true
	});

    $("input[name='query.inLocation']").select2({
        data : stockLocations,
        placeholder : "移入库位",
        allowClear : true
    });

    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";
    $("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

    var heights = $("body").height();
    if(heights>910){
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }
    
    $.getJSON(CONTEXT_PATH + "system/saleusers/userByRole", function(json){
		if (json) {
			$("input[name='query.createBy']").select2({
				data : json,
				placeholder : "操作人",
				allowClear : true
			});
		} else {
			$("input[name='query.createBy']").attr("placeholder", "无数据!").attr("readonly", true);
		}
	});

    function getCheckedIds() {
        var checkedIds = $("input[name='ids']:checked");
        return checkedIds;
    }

    function download() {
        var diglog = dialog({
            title:"移库导出",
            width: 500,
            height: 80,
            url: CONTEXT_PATH + "stockMove/downloadmode",
            okValue: '确定',
            ok: function () {
                var exportWindow = $(this.iframeNode.contentWindow.document.body);

                var submitForm = exportWindow.find("#submit-form");

                var exportType = submitForm.find("input[name='exportType']:checked").val();

                var ids = getCheckedIds();
                var params = $('#domain').serialize();
                if(exportType == 3) {// 导出当前选择
                    if(ids.length == 0) {
                        layer.alert("请选择要操作的数据");
                        return false;
                    }
                    params = params + "&" + ids.serialize();
                }
                params = params + "&exportType=" + exportType;
                //还原分页
                $("#page-no").val("${domain.page.pageNo}");
                downloadByPostForm(params, "download?");
                $("#page-no").val("1");
                setTimeout(function () {
                    diglog.close().remove();
                }, 100);
                return true;
            },
            cancelValue:'取消',
            cancel: function () {}
        });
        diglog.show();
    }

	function importItem(target) {

		//检测上传文件的类型
		var filename = target.value;

		var ext, idx;
		if (filename == '') {
			$("#submit-upload").attr("disabled", true);
			layer.alert("请选择需要上传的文件!");
			return;
		} else {
			idx = filename.lastIndexOf(".");
			if (idx != -1) {
				ext = filename.substr(idx + 1).toUpperCase();
				ext = ext.toLowerCase();

				if (ext != 'xls' && ext != 'xlsx') {
					layer.alert("只能上传.Excel类型的文件!");
					return;
				}
			} else {
				layer.alert("只能上传.Excel类型的文件!");
				return;
			}
		}

		var r = confirm("确定上传" + filename + "?");

		if(!r) {
			return;
		}

		var uploadUrl = CONTEXT_PATH + "stockMove/import";

		var searchUrl = $("#domain").attr("action");

		$("#domain").attr("action", uploadUrl);

		$("#domain").ajaxSubmit(function(data) {
			if (data.status == 200) {
				if (data.message != null && data.message != '' && data.message != 'null') {
					customizeLayer(data.message, "error");
				} else {
					layer.alert('成功',function (index) {
						window.location.reload();
					});
				}
			} else {
				$(target).val(null);
				customizeLayer(data.message, "error");
			}

			$("#domain").attr("action", searchUrl);

		});

		$("#domain").attr("action", searchUrl);
	}
</script>
</body>
</html>