<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
    <link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		.pick-content-button{
			margin-top: 20px;
		}
	</style>
</head>
<body>
<@header method="header" active="18030000" ><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">库存管理</a></li>
				<li class="active">包材管理</li>
			</ul>
		</div>
	</div>

	<div class="container-fluid" style="background-color: white;border: none">
	<div class="row">
		<div class="col-md-12">
		<#assign query = domain.query />
			<form action="${CONTEXT_PATH}packagingMaterialManagement/search"
					   class="form-horizontal form-bordered form-row-stripped"
					   method="post" modelAttribute="domain" name="packagingMaterialForm" id ="domain">
                <!-- 分页信息 -->
                <input id="page-no" type="hidden" name="page.pageNo" value="1">
                <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
                <input id="isAlert" type="hidden" name="query.isAlert" value="${domain.query.isAlert}"/>
				<div class="form-body">
					<div class="form-group">
						<label class="control-label col-md-1">耗材货号</label>
						<div class="col-md-3">
							<input class="form-control" id="materialArticleNumber" name="query.materialArticleNumber" type="text" value="${domain.query.materialArticleNumber}">
						</div>
                        <label class="control-label col-md-1">耗材名称</label>
                        <div class="col-md-3">
                            <input class="form-control" id="name" name="query.likeName" placeholder="请输入关键词，例如：快递袋"  type="text" value="${domain.query.likeName}">
                        </div>
                        <label class="control-label col-md-1">耗材类型</label>
                        <div class="col-md-3">
                            <input class="form-control" id="type" name="query.type" type="text" value="${domain.query.type}">
                        </div>
					</div>

					<div class="form-group">
                        <label class="control-label col-md-1">耗材状态</label>
                        <div class="col-md-3">
                            <input class="form-control" id="status" name="query.status" type="text" value="${domain.query.status}">
                        </div>
						<label class="control-label col-md-1">使用环节</label>
						<div class="col-md-1">
							<select name="query.useLink" class="form-control" value="${query.useLink}" id="useLink-id">
								<option value="">全部</option>
								<option value="入库">入库</option>
								<option value="出库">出库</option>
							</select>
					</div>
					</div>
				</div>
				
				<div class="pick-content-button">
					<div class="pull-left" style="margin-left: 10px;">
                        <@header method="auth" authCode="CONSUMABLE_MATERIAL_ADD_MATERIAL">
                        <button type="button" class="btn  btn-default" onclick="setPackagingMaterial()">
                            <i class="icon-plus"></i> 添加包材
                        </button>
                        </@header>
                        <@header method="auth" authCode="CONSUMABLE_MATERIAL_DOWNLOAD_PARADIGM">
                        <a class="btn btn-default" href="${CONTEXT_PATH}file/execl/packaging_material_import.xlsx">
                            <i class="icon-download"></i> 下载导入模板
                        </a>
                        </@header>
                        <@header method="auth" authCode="CONSUMABLE_MATERIAL_IMPORT_MATERIAL">
                        <span class="btn btn-default fileinput-button">
                            <span class="icon-plus"> 导入包材</span>
                            <input type="file" name="file" onchange="importPackagingMaterial(this)" />
                        </span>
                        </@header>
					</div>

					<div class="col-md-offset-10" style="text-align: right">
                        <@header method="auth" authCode="CONSUMABLE_MATERIAL_DOWNLOAD">
						<button type="button" class="btn btn-default" id="downloadPackagingMaterial">
							<i class="icon-download"></i> 导出
						</button>
                        </@header>
						<button type="button" class="btn default" onclick="formReset(this)">
							<i class="icon-refresh"></i> 重置
						</button>
						<button type="submit" class="btn blue" onclick="search()">
							<i class="icon-search"></i> 查询
						</button>
					</div>
				</div>
			</form>
		</div>
		<br/>
	</div>
	<div class="row">
		<div id="fixedDiv" class="col-md-12">
			<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
				<colgroup>
                    <col width="10%" />
                    <col width="10%" />
                    <col width="10%" />
                    <col width="5%" />
                    <col width="7%" />
                    <col width="5%" />
                    <col width="5%" />
                    <col width="5%" />
                    <col width="10%" />
                    <col width="6%" />
                    <col width="6%" />
                    <col width="5%" />
                    <col width="7%" />
                    <col width="5%" />
                    <col width="4%" />
				</colgroup>
				<thead>
					<tr>
                        <th>图片</th>
                        <th>货号</th>
                        <th>名称</th>
                        <th>单位</th>
                        <th>箱规（每箱/件）</th>
                        <th>水位库存</th>
                        <th>仓库库存</th>
                        <th>使用环节</th>
                        <th>尺寸（mm）</th>
                        <th>重量（g）</th>
                        <th>销售使用重量（g）</th>
                        <th>耗材类型</th>
                        <th>备注</th>
                        <th>耗材状态</th>
                        <th>操作</th>
					</tr>
				</thead>
			</table>
		</div>
		<div class="col-md-12" id="by-list-warp">
			<table class="table table-striped table-bordered table-hover table-condensed" id="by-list">
                <colgroup>
                    <col width="10%" />
                    <col width="10%" />
                    <col width="10%" />
                    <col width="5%" />
                    <col width="7%" />
                    <col width="5%" />
                    <col width="5%" />
                    <col width="5%" />
                    <col width="10%" />
                    <col width="6%" />
                    <col width="6%" />
                    <col width="5%" />
                    <col width="7%" />
                    <col width="5%" />
                    <col width="4%" />
                </colgroup>
                <thead>
                    <tr>
                        <th>图片</th>
                        <th>货号</th>
                        <th>名称</th>
                        <th>单位</th>
                        <th>箱规（每箱/件）</th>
                        <th>水位库存</th>
                        <th>仓库库存</th>
                        <th>使用环节</th>
                        <th>尺寸（mm）</th>
                        <th>重量（g）</th>
                        <th>销售使用重量（g）</th>
                        <th>耗材类型</th>
                        <th>备注</th>
                        <th>耗材状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <div class="pick-content-button">
                    <div class="pull-left" style="margin:10px;">
                        <div class="btn yellow" onclick="showAlert();">
                            <#if domain.alertCount??>
                                库存预警（<span id="">${domain.alertCount}</span>）
                            <#else >
                                库存预警（<span id="">0</span>）
                            </#if>
                        </div>
                    </div>
                </div>
				<tbody>
                    <#assign warehouseId = util('localWarehouseId')/>
					<#list domain.whPackagingMaterialManagements as packagingMaterial>
						<tr>
							<td>
                                <#if packagingMaterial.images??>
                                    <#--<input type="hidden" id="img-${packagingMaterial.id}" value="${packagingMaterial.images?join(',')}"/>-->
                                    <img onclick="getMaterialImages('${packagingMaterial.images?join(',')}')" alt="" border="0" width="60px" height="60px" src="${packagingMaterial.images[0]}">
                                </#if>
                            </td>
							<td>${packagingMaterial.materialArticleNumber}</td>
							<td>${packagingMaterial.name}</td>
							<td>${packagingMaterial.unit}</td>
							<td>${packagingMaterial.boxSpecification}</td>
                            <td>${packagingMaterial.stockThreshold}</td>
                            <td>${packagingMaterial.whPackagingMaterialStock.surplusQuantity}</td>
							<td>${packagingMaterial.useLink}</td>
							<td>${packagingMaterial.length} * ${(!(packagingMaterial.width)??)?string('-',packagingMaterial.width)} * ${(!(packagingMaterial.height)??)?string('-',packagingMaterial.height)}</td>
							<td>${packagingMaterial.weight}</td>
							<td>${packagingMaterial.saleWeight}</td>
                            <td>${util('enumName','com.estone.warehouse.enums.MaterialType', packagingMaterial.type)}</td>
							<td>${packagingMaterial.remark}</td>
                            <td>${util('enumName','com.estone.warehouse.enums.MaterialStatus', packagingMaterial.status)}</td>
							<td>
                                <#if warehouseId == null || warehouseId == 1>
								<button type="button" class="btn btn-xs btn-info yellow" onclick="setPackagingMaterial(${packagingMaterial.id})">修改</button>
								<button type="button" class="btn btn-xs btn-info" onclick="viewLog(${packagingMaterial.id}, 'packagingMaterialManagement')">日志</button>
                                </#if>
							</td>
						</tr>
					</#list>
				</tbody>
			</table>
		</div>
	</div>
    <div id="fixed-bottom">
        <div id="pager"></div>
    </div>
</div>

<#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript">
    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";
    $("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});
	$("#useLink-id").val("${domain.query.useLink}");
    var materialTypes = ${domain.materialTypes};
    $("input[name='query.type']").select2({
        data : materialTypes,
        placeholder : "耗材类型",
        allowClear : true
    });

    var materialStatus = ${domain.materialStatus};
    $("input[name='query.status']").select2({
        data : materialStatus,
        placeholder : "耗材状态",
        allowClear : true
    });

    function setPackagingMaterial(id){
        var url = CONTEXT_PATH + "packagingMaterialManagement/update?whPackagingMaterialManagementId=";
       if(id){
           url = url + id;
       }
        dialog({
            title: '添加/编辑包材',
            url: url,
            width: 420,
            height: 580,
            top: 0,
            okValue: '确定',
            ok: function () {
                var exportWindow = $(this.iframeNode.contentWindow.document.body);
                var submitForm = exportWindow.find("#submit-form");
                var materialType = submitForm.find('#materialType').val();
                var materialArticleNumber = submitForm.find('#materialArticleNumber').val();
                var name = submitForm.find('#name').val();
                var unit = submitForm.find('#unit').val();

                var boxSpecification = submitForm.find('#boxSpecification').val();
                var weight = submitForm.find('#weight').val();
                var saleWeight = submitForm.find('#saleWeight').val();
                var length = submitForm.find('#length').val();
                var width = submitForm.find('#width').val();
                var height = submitForm.find('#height').val();
                var materialStatus = submitForm.find('#materialStatus').val();
                var stockThreshold = submitForm.find("#stockThreshold").val();
				var useLink="";
				var useLink1 = submitForm.find('#useLink1').prop('checked');
				if (useLink1){
					useLink='入库';
				}
				var useLink2 = submitForm.find('#useLink2').prop('checked')
				if (useLink2){
					if (!useLink) {
						useLink='出库';
					}else{
						useLink+=',出库';
					}

				}
				submitForm.find('#useLink').val(useLink);
                //正整数
                var reg = /^\+?[1-9][0-9]*$/;
                //保留两位小数
                var reg1 = /^\+?[1-9][0-9]*$|^[+]{0,1}(\d+\.\d{1,2})$/;
                if(!materialType){
                    layer.alert("请输入耗材类型!", 'error');
                    return false;
                }
                if(!materialArticleNumber){
                    layer.alert("请输入耗材货号!", 'error');
                    return false;
                }
                if(!name){
                    layer.alert("请输入耗材名称!", 'error');
                    return false;
                }
                if(!unit){
                    layer.alert("请输入耗材单位!", 'error');
                    return false;
                }

                if(!reg.test(boxSpecification)){
                    layer.alert("箱规请输入正确的正整数", 'error');
                    submitForm.find('#boxSpecification').val('');
                    return false;
                }

                if(!reg.test(stockThreshold)){
                    layer.alert("水位库存请输入正确的正整数", 'error');
                    submitForm.find('#stockThreshold').val('');
                    return false;
                }

                if(!reg1.test(weight)){
                    layer.alert("请输入正确的重量，支持正整数和小数点后两位!", 'error');
                    submitForm.find('#weight').val('');
                    return false;
                }
                if(saleWeight != undefined && saleWeight != '' && !reg1.test(saleWeight)){
                    layer.alert("请输入正确的销售使用重量，支持正整数和小数点后两位!", 'error');
                    submitForm.find('#saleWeight').val('');
                    return false;
                }
                if(!reg.test(length)){
                    layer.alert("尺寸长度请输入正确的正整数", 'error');
                    submitForm.find('#length').val('');
                    return false;
                }
                if(!reg.test(width)){
                    layer.alert("尺寸宽度请输入正确的正整数", 'error');
                    submitForm.find('#width').val('');
                    return false;
                }
                if(!reg.test(height)){
                    layer.alert("尺寸高度请输入正确的正整数", 'error');
                    submitForm.find('#height').val('');
                    return false;
                }
                if(!materialStatus){
                    layer.alert("请输入状态！", 'error');
                    return false;
				}
                var params = submitForm.serialize();

                $.ajax({
                    url: CONTEXT_PATH + "packagingMaterialManagement/saveUpdate",
                    type: "POST",
                    data: params,
                    success: function(data){
                        if (data.status == 200) {
							layer.confirm('成功！',{
								icon: 1,
								btn: ['确定']
							},function () {
								window.location.reload();
							})
                        } else {
							layer.confirm(data.message,{
								icon: 1,
								btn: ['确定']
							},function () {
								window.location.reload();
							})
                        }
                    }
                });
            },
            cancelValue: '关闭',
            cancel: function () {
                window.location.reload();
            }
        }).showModal();

    }

    $('#add_modal').on('hide.bs.modal', function () {
        window.location.reload();
    });

    function getMaterialImages(images) {
        if(images != ''){
            dialog({
                title: '包材图片',
                url: CONTEXT_PATH + "checkInException/getExceptionImages?exceptionImages="+images,
                width: 800,
                height: 680,
                top: 0
            }).showModal();
        }

    }

    function importPackagingMaterial(target) {

        //检测上传文件的类型
        var filename = target.value;

        var ext, idx;
        if (filename == '') {
            $("#submit-upload").attr("disabled", true);
            layer.alert("请选择需要上传的文件!");
            return;
        } else {
            idx = filename.lastIndexOf(".");
            if (idx != -1) {
                ext = filename.substr(idx + 1).toUpperCase();
                ext = ext.toLowerCase();

                if (ext != 'xls' && ext != 'xlsx') {
                    layer.alert("只能上传.Excel类型的文件!");
                    return;
                }
            } else {
                layer.alert("只能上传.Excel类型的文件!");
                return;
            }
        }

        var r = confirm("确定上传" + filename + "?");

        if(!r) {
            return;
        }

        var importUrl = CONTEXT_PATH + "packagingMaterialManagement/importPackagingMaterial";

        var searchUrl = $("#domain").attr("action");

        $("#domain").attr("action", importUrl);

        $("#domain").ajaxSubmit(function(data) {
            if (data.status == 200) {
				layer.confirm(data.message,{
					icon: 1,
					btn: ['确定']
				},function () {
					window.location.reload();
				})
            } else {
                $(target).val(null);
                customizeLayer(data.message, "error");
            }

            $("#domain").attr("action", searchUrl);

        });

        $("#domain").attr("action", searchUrl);
    }

    $('#downloadPackagingMaterial').on('click',function () {
		var params = $('#domain').serializeArray();
		var url= CONTEXT_PATH + "packagingMaterialManagement/download";
		var tempForm = document.createElement("form");
		tempForm.id="tempForm";
		tempForm.method="post";
		tempForm.action=url;
		tempForm.target="blank";

		for (var i = 0; i < params.length; i++) {
			if (params[i] != null && params[i] != "" && params[i] != undefined) {
				var input = document.createElement("input");
				input.type = "hidden";
				input.name = params[i].name;
				input.value = params[i].value;
				tempForm.appendChild(input);
			}
		}

		if (tempForm.attachEvent) {  // IE
			tempForm.attachEvent("onsubmit",function(){ window.open('about:blank','blank'); });
		} else if (tempForm.addEventListener) {  // DOM Level 2 standard
			tempForm.addEventListener("onsubmit",function(){ window.open('about:blank','blank'); });
		}
		document.body.appendChild(tempForm);
		if (document.createEvent) { // DOM Level 2 standard
			evt = document.createEvent("MouseEvents");
			evt.initMouseEvent("submit", true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
			tempForm.dispatchEvent(evt);
		} else if (tempForm.fireEvent) { // IE
			tempForm.fireEvent('onsubmit');
		}
		//必须手动的触发
		tempForm.submit();
		document.body.removeChild(tempForm);


	})

    // 用于切换库存预警显示情况
    function showAlert(){
        $("#isAlert").val('true');
        $("#domain").submit();
    }

    // 用于普通的查询，且是非库存预警的
    function search(){
        $("#isAlert").val('false');
        $("#domain").submit();
    }

</script>
</body>
</html>