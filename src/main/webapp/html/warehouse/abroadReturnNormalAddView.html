<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		.top_bar{
		    position:fixed;top:0px;
		}
		#task-list td {
			vertical-align:middle;
		}
	</style>
</head>
<body>
<@header method="header" active="17020000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12">
			<ul class="page-breadcrumb breadcrumb">
				<li><a href="#">仓库管理</a> </li>
				<li class="active">海外退件</li>
				<li class="active">新增海外退件</li>
			</ul>
		</div>
	</div>
	<div class="container-fluid">
		<h2 class="header-title">海外退件</h2>
		<#assign whAbroadReturn=domain.whAbroadReturn>
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="col-md-12">
				<form
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="abroadReturnForm" id="domain">
					<div class="form-body">
						<div class="form-group">
							<div class="col-md-3"></div>
							<label class="control-label col-md-1" style="width:130px;">返架仓库</label>
							<div class="col-md-3">
								<div>
									<input type="hidden" id="warehouseId"  value="1">
									<#if domain.warehouseId == 1>
										<input type="text"  readonly class="form-control"  value="汉海达">
									<#elseif domain.warehouseId == 3>
										<input type="text"  readonly class="form-control"  value="南宁仓">
									<#else>
										<input type="text"  readonly class="form-control"  value="美景">
									</#if>
								</div>
							</div>
						</div>
						<div class="form-group">
							<div class="col-md-3"></div>
							<label class="control-label col-md-1" style="width:130px;">退货批次号</label>
							<div class="col-md-3">
								<div>
									<input id="orderNo" type="text" name="domain.warehoseName" class="form-control"  value="${domain.warehoseName}" onkeypress="if(event.keyCode==13) { inputnext(this); return false;}" >
								</div>
							</div>
						</div>
						<div class="form-group">
							<div class="col-md-3"></div>
							<label class="control-label col-md-1" style="width:130px;">海外退件单号</label>
							<div class="col-md-3">
								<div>
								   <input type="text" id = "returnNo" name="whAbroadReturn.returnNo" readonly class="form-control"  value="${whAbroadReturn.returnNo}">
								</div>
							</div>
						</div>
						<div class="form-group">
							<div class="col-md-3"></div>
							<label class="control-label col-md-1" style="width:130px;">周转筐</label>
							<div class="col-md-3">
								<div><input id="box_no" type="text" name="whAbroadReturn.boxNo" class="form-control" value="${whAbroadReturn.boxNo}" onkeypress="if(event.keyCode==13) { getBoxNo(this); return false;}" /></div>
							</div>
						</div>
					<!--	<div class="form-group">
							<div class="col-md-3"></div>
							<label class="control-label col-md-1" style="width:130px;">扫描SKU</label>
							<div class="col-md-4">
								<div id="input_sku">
									<input class="form-control" type="text" onblur="$(this).val('');alert('请扫描SKU！', 'error');" onkeypress="if(event.keyCode==13) { inputnext(this); return false;}" id="sku" name="sku">
									<input type="hidden" name="notSku">
								</div>
							</div>
						</div>-->
						<div>
							<div id="check_scan_datas" class="border-gray p5 col-md-12" style="min-height:500px;">

							</div>
						</div>
					</div>
					<div class="col-md-offset-1" style="text-align: center">
							<button type="button" class="btn blue" id="save_abroad_return" onclick="save()">
								<i class="icon-search"></i> 保存
							</button>
					</div>
				</form>
			</div>
			<br/>
		</div>
	</div>

	<div id="fixed-bottom">
		<div id="pager"></div>
	</div>
	<#include "/common/footer.html">
</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/packing.js?v=${.now?datetime}"></script>
	<script type="text/javascript">
		var isScan = false;
        var uuIdCacheKey = 'CHECK_RETURN_FOR_UUID_CACHE_KEY_ABROAD';
        $(document).ready(function(){

            initSkuUuIdStorageCache(uuIdCacheKey);
        }); // end ready

		function getBoxNo(obj){
			var boxNo = $.trim($(obj).val());
			$.getJSON(CONTEXT_PATH + "warehouse/boxs/boxNo?type=5&boxNo=" + boxNo, function(data){
	    		if (data.status == 200) {
					isScan = true;
					$("#box_no").blur();
					if(confirm("是否使用该周转筐，确认后周转筐将被标记为已使用状态。")){
						$.get(CONTEXT_PATH + "warehouse/boxs/bindingPre/" + boxNo, function(data){
							if(data.status == 200){
								$('#box_no').val(boxNo);
								layer.alert("绑定周转筐成功！");
							}else{
								layer.alert("该周转筐绑定失败", "error");
								$('#box_no').val('');
							}
						});
					}else{
						$('#box_no').val('');
					}
				} else {
					$(obj).val("");
					layer.alert("该周转框不存在或者已使用！", "error");
				}
	    	});
		}

		function inputnext(obj) {
			debugger;
			// 检查是否还有输入框未输入值
			var visible_inputs = $('#input_scan').find("input:visible").length;
			for (i = 0; i < visible_inputs; i++) {
				var e = $('#input_scan').find("input:visible").get(i);
				if ($(e).val() == '') {
					$(e).focus();
					return false;
				}
			}

			var val = $('#orderNo').val();
			var warehouseId = $('#warehouseId').val();

			$.ajax({
				url:CONTEXT_PATH+"warehouse/abroadReturns/batchDetail",
				type:"POST",
				data:{
					orderNO:val.trim(),
					warehouseId:warehouseId
				},
				success:function(response){
					$("#check_scan_datas").html(response);
				}
			});
			$('#sku').val("");

		}



		function isMinus(obj){
			var r = /^\d+$/;
			if(!r.test($(obj).val())){
				$(obj).val("1");
				layer.alert("请填写正整数！", "error");
			}
			if($(obj).val() == 0){
				$(obj).val("1");
				layer.alert("不能填写0-不需要请删除！", "error");
			}
		}

		function onRemove(obj){
			if(confirm("是否删除该条SKU?")){
				$(obj).parent().remove();
				// 重新编排
				$("#tbody tr").each(function(i){
					refreshIndex(i, this);
				});
			}
		}

		// 子选项
		function checkChange(tar) {
			var f = $(tar).is(":checked");
			$("input[name='check-alls-1']").each(function() {
				$(this).attr('checked',f);
			})
		}

		// 子选项
		function checkChange2(tar) {
			var f = $(tar).is(":checked");
			$("input[name='check-alls-2']").each(function() {
				$(this).attr('checked',f);
			})
		}

		// 子选项
		function checkChange3(tar) {
			var f = $(tar).is(":checked");
			$("input[name='check-alls-3']").each(function() {
				$(this).attr('checked',f);
			})
		}

		// 保存
		function save(){
			debugger;
			// 退货批次id
			var returnNo = $("#returnNo").val();
			if (returnNo == ""){
				layer.alert("海外退件单号不能为空!",'error');
				return;
			}
			var orderNo = $('#orderNo').val();
			if (orderNo == ""){
				layer.alert("退货批次号不能为空!",'error');
				return;
			}
			var warehouseId = $('#warehouseId').val();
			if (warehouseId == ""){
				layer.alert("仓库属性不能为空!",'error');
				return;
			}
			var boxNo = $('#box_no').val();
			if (boxNo == ""){
				layer.alert("周转筐不能为空!",'error');
				return;
			}
			// 校验是否选择返架区
			var outIds = $("input[name='check-alls-1']:checked");
			var selectArea = "";
			for (var i = 0; i < outIds.length; i++) {
				var area = outIds[i].value;
				if (i == 0) {
					selectArea += area;
				} else {
					selectArea += "," + area;
				}
			}
			// 校验是否选择通道
			var outIds2 = $("input[name='check-alls-2']:checked");
			var selectWarehouse = "";
			for (var i = 0; i < outIds2.length; i++) {
				if (i == 0) {
					selectWarehouse = warehouseId;
				} /*else {
					selectWarehouse += "," + area;
				}*/
			}

			// 校验是否选择通道
			var outIds3 = $("input[name='check-alls-3']:checked");
			var virtualLocation = false;
			for (var i = 0; i < outIds3.length; i++) {
				virtualLocation=true;
			}

			if (!selectArea && !selectWarehouse && !virtualLocation) {
				layer.alert("请先选择至少一个返架区！");
				return false;
			}

			//核对提交数据
			var r = $.ajax({
				type : "post",
				async : false,
				url :CONTEXT_PATH +"warehouse/abroadReturns/createNormal",
				data : { returnNo : returnNo, orderNo : orderNo, boxNo:boxNo, selectArea:selectArea, selectWarehouse:selectWarehouse,virtualLocation:virtualLocation},
				timeout : 100000,
				beforeSend : function() {
					App.blockUI(null, null, 10000);
				},
				success : function(data) {
					if(data.status == 500){
						customizeLayer(data.message,'error');
					}else {
						layer.alert("生成成功!",'success');
						setTimeout(jump, 2000);
					}
				}
			});

			function jump(){
				location.href = CONTEXT_PATH + "warehouse/abroadReturns";
			}
		}
	</script>
</body>
</html>