<!DOCTYPE html>
<html lang="en" class="no-js">
<head>
	<meta charset="utf-8" />
	<title>ERP</title>
	<#include "/common/include.html">
	<style type="text/css">

	</style>

</head>

<body>
<!-- BEGIN CONTAINER -->
<div class="container-fluid" style="min-width: 280px;">

		<!--<div class="col-md-12">-->
			<form class="form-horizontal" id="submit-form" name="submitForm" action="" method="post">
						<!--<label class="control-label col-md-1">分配人员：</label>-->
						<!--<div class="col-md-3">-->
							<input id ="allocationUser" class="form-control" name="query.allocationUser" type="text" value="${query.allocationUser}">
						<!--</div>-->
			</form>
		<!--</div>-->
		<!-- end col -->

	<!-- end row -->
</div>
<!-- END CONTAINER -->
<script type="text/javascript">
	// 领取人
	$.getJSON(CONTEXT_PATH + "system/saleusers/allUser", function(json) {
		if(json) {
			$("input[name='query.allocationUser']").select2({
				data: json,
				placeholder: "分配人员",
				allowClear: true
			});
		} else {
			$("input[name='query.allocationUser']").attr("placeholder", "没有可分配人员!").attr("readonly", true);
		}
	});
</script>

<!-- END JAVASCRIPTS -->
</body>
<!-- END BODY -->
</html>