<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
<@header method="header" active="26000000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">仓库管理</a></li>
					<li class="active">调拨盘点</li>
				</ul>
			</div>
		</div>
		
		<div class="container-fluid">
			<h2 class="header-title">添加调拨盘点</h2>
			<#assign whInventory=domain.whInventory>
			<!-- END PAGE HEADER-->
			<div class="row">
				<div class="col-md-12">
					<form action="" class="form-horizontal form-bordered form-row-stripped"
						method="post" modelAttribute="domain" name="inventoryForm" id="domain">
						<div class="form-body">

							<div class="form-group">
                                <label class="control-label col-md-1" style="width:180px;"></label>
                                <div class="col-md-2"></div>
                                <label class="control-label col-md-1" style="width:180px;">调拨盘点类型</label>
                                <div class="col-md-5">
                                    <label class="radio-inline">
                                        <input type="radio" checked="checked" id="stock_allocation" name="inventoryType" value="10">
                                        库存调拨盘点
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="inventoryType" id="order_allocation" value="9">
                                        订单调拨盘点
                                    </label>
                                </div>
							</div>


							<div class="form-group">
								<label class="control-label col-md-1" style="width:180px;"></label>
								<div class="col-md-2"></div>
								<label class="control-label col-md-1" style="width:180px;">扫描SKU</label>
								<div class="col-md-3">
									<div id="input_sku">
										<input class="form-control" type="text" onblur="" onkeypress="if(event.keyCode==13) { inputnext(this); return false;}" id="sku" name="sku">
										<input type="hidden" name="notSku">
									</div>
								</div>
							</div>
							
							<div class="form-group">
								<label class="control-label col-md-1" style="width:180px;"></label>
								<div class="col-md-2"></div>
								<label class="control-label col-md-1" style="width:180px;"></label>
								<div class="col-md-5">
									<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
										<thead>
											<tr>
												<th>SKU</th>
												<th>标题</th>
												<th>调拨库存</th>
												<th>调整数量</th>
												<th>操作</th>
											</tr>
										</thead>
										<tbody id="tbody">
										</tbody>
									</table>
								</div>
							</div>
							
						</div>
						<div class="col-md-offset-1" style="text-align: center">
								<button type="button" onclick="saveAllocationSku();" class="btn blue">
									<i class="icon-search"></i> 保存
								</button>
							</div>
					</form>
				</div>
				<br/>
			</div>
		</div>

		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
		<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript">

		function saveAllocationSku() {

            if ($("#tbody tr")==null || $("#tbody tr").length==0) {
                layer.alert("请添加sku!", "error");
                return false;
            }

			var result = false;
            $('#tbody').find('tr').each(function (index) {

                var dataIndex = parseInt(index)+1;
                var canOutQuantity = $(this).find("td>input[name='whInventory.items["+index+"].skuNum']").val();
                var quantity = $(this).find("td>input[name='whInventory.items["+index+"].differencesNum']").val();
                if(quantity == ''){
                    layer.alert('第'+dataIndex+'个sku的调整量为空！','error');
                    result = false;
                    return false;
                }
                var r = /^\d+$/;
                if(!r.test(quantity)){
                    layer.alert("请填写正整数！", "error");
                    result = false;
                    return false;
                }
                if(quantity == 0){
                    layer.alert("第"+dataIndex+"个sku 不能填写0-不需要请删除！", "error");
                    result = false;
                    return false;
                }
                if(parseInt(quantity) > parseInt(canOutQuantity)){
                    layer.alert('第'+dataIndex+'个sku的调整数量大于调拨库存！','error');
                    result = false;
                    return false;
                }
                result = true;
            });
            if(result){
				$.ajax({
					url:CONTEXT_PATH+"warehouse/inventorys/addAllocation",
					type:"POST",
					data:$('#domain').serialize(),
					success:function(result){
                        if (result.status == 200) {
							layer.confirm(result.message,{
								icon: 1,
								btn: ['确定']
							},function () {
								location.href = CONTEXT_PATH + "warehouse/inventorys";
							})
                        } else {
							customizeLayer(result.message, "error");
                        }
					},
					error:function(){
						layer.alert('保存失败!', 'error');
					}
				});
			}
        }

		function inputnext(obj) {
			var sku_array=new Array();
			$("[id=articlenumber]").each(function(){
				sku_array.push($(this).text());
			  });
			
			var val = $('#sku').val();

			var type = $('input[type=radio][name=inventoryType]:checked').val();

            if(!(val.indexOf("=") == -1)) {
                val = val.split('=')[0];
            }

			if(!val || val.trim() == ''){
				layer.alert("请输入有效sku!");
				return ;
			}

			if (type == ''){
                layer.alert("请选择调拨盘点类型!");
                return ;
			}

			$.ajax({
				url:CONTEXT_PATH+"warehouse/inventorys/skuDetail",
				type:"POST",
				data:{
					sku:val.trim(),
					type:type
				},
				success:function(result){
					if(result.status == '500'){
						customizeLayer(result.message, "error");
					}else if(result.status == '200'){
						var whSku = JSON.parse(result.message);
						var i=sku_array.length;
						var number=$.inArray(whSku.sku, sku_array);
						var inventoryType = type == 9 ? 5 : 4;
						if(number==-1){
							var option="<tr><td id='articlenumber'>"+whSku.sku+"<input type='text' name='whInventory.items["+i+"].sku' style='width:60px;display:none' value='"+whSku.sku+"'>" +
									    "<input type='hidden' name='whInventory.type' value='"+inventoryType+"'></td>"+
										"<td>"+whSku.name+"<input type='text' name='whInventory.items["+i+"].skuLocation' style='width:60px;display:none' value='"+whSku.locationNumber+"'></td>"+
										"<td><input type='text' class='skuNum' readonly='readonly' name='whInventory.items["+i+"].skuNum' style='width:60px;' value='"+whSku.whStock.quantity+"'></td>"+
										"<td><input type='text' name='whInventory.items["+i+"].differencesNum' style='width:60px;' value='"+1+"'></td>"+
										"<td onclick='onRemove(this)'><i class='icon-remove'></i></td></tr>";
							$("#tbody").append(option);
						}else{
							$("[id=articlenumber]").each(function(){
								if(whSku.sku==$(this).text()){
								    var skuNum = parseInt($(this).next().next().find("input").val());
									var number=1+parseInt($(this).next().next().next().find("input").val());
                                    if(number > skuNum){
                                        layer.alert("调整数量不能大于调拨库存！", "error");
                                        return false;
                                    }else {
										$(this).next().next().next().find("input").val(number);
									}
								}
							});
						}
						$("input[name='notSku']").val("1");
					}else{
						customizeLayer("SKU:["+val+"]不存在", "error");
					}
				},
	            error:function(){
	                layer.alert('查询失败，请重新扫描!', 'error');
	            }
			});
			$('#sku').val("");
			
		}
		
		function onRemove(obj){
			if(confirm("是否删除该条SKU?")){
				$(obj).parent().remove();
				// 重新编排
				$("#tbody tr").each(function(i){
					refreshIndex(i, this);
				});
			}
		}

        $('input[type=radio][name=inventoryType]').change(function() {
            if($("#tbody").find('tr').length > 0){
                if(confirm("更换盘点类型将会清空列表中的数据，请确认是否更换！")){
                    $("#tbody").find('tr').remove();
                }
            }
        });
	</script>
</body>
</html>