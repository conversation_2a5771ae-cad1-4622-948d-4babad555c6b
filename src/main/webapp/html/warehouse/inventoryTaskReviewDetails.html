<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/html">
    <head>
        <title>易世通达仓库管理系统</title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <#include "/common/include.html">
        <link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
        <style type="text/css">
            .modal-body input {
                width: 240px;
            }

            .return-model {
                padding: 12px;
            }

            #return-info-btn {
                color: #ffff;
                background-color: #5bc0de;
                padding: 3px;
            }
        </style>
    </head>

    <body>
        <@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>

        <div id="page" style="background-color: rgb(231, 237, 248)">
            <div class="row">
                <div class="col-md-12" style="padding: 0">
                    <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                        <li>
                            <a href="#">盘点</a>
                        </li>
                        <li class="active">盘点审核明细</li>
                    </ul>
                </div>
            </div>
            <div class="container-fluid" style="background-color: white;border: none">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-body">
                            <div class="form-group">
                                <label class="control-label col-md-1">盘点任务号</label>
                                <div class="col-md-2">
                                    <input class="form-control" type="text" readonly="readonly" <#if domain.whInventoryTasks?? && domain.whInventoryTasks?size gt 0> value="${domain.whInventoryTasks[0].taskNo}"</#if> >
                                </div>
                                <label class="control-label col-md-1">盘点过程</label>
                                <div class="col-md-2">
                                    <input class="form-control" readonly="readonly" type="text" <#if domain.whInventoryTasks?? && domain.whInventoryTasks?size gt 0> value="${domain.whInventoryTasks[0].taskLevelName}" </#if>>
                                </div>
                                <label class="control-label col-md-1">盘点类型</label>
                                <div class="col-md-2">
                                    <input class="form-control" readonly="readonly" type="text" <#if domain.whInventoryTasks?? && domain.whInventoryTasks?size gt 0> value="${domain.whInventoryTasks[0].taskTypeName}" </#if>>
                                </div>
                                <label class="control-label col-md-1">领取人</label>
                                <div class="col-md-2">
                                    <input class="form-control" readonly="readonly" type="text" <#if domain.whInventoryTasks?? && domain.whInventoryTasks?size gt 0> value="${util('name',domain.whInventoryTasks[0].receiveUser)}" </#if>>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <hr/>
                <div class="row" style="margin-top: 10px;">
                    <div class="col-md-12">
                        <#assign query = domain.query>
                        <form class="form-horizontal form-bordered form-row-stripped" method="POST"
                              action="${CONTEXT_PATH}warehouse/inventoryTaskReviews/getDetails" modelAttribute="domain" id="domain">
                            <input type="hidden" name="query.taskId" value="${query.taskId}">
                            <input id="page-no" type="hidden" name="page.pageNo" value="1">
                            <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
                            <div class="form-body">
                                <div class="form-group">
                                    <label class="control-label col-md-1">SKU</label>
                                    <div class="col-md-3">
                                        <input class="form-control" id="" name="query.sku" type="text" placeholder="sku,多个之间用逗号分隔" value="${query.sku}">
                                    </div>
                                    <label class="control-label col-md-1">审核状态</label>
                                    <div class="col-md-3">
                                        <input class="form-control" name="query.reviewStatusStr" type="text" value="${query.reviewStatusStr}">
                                    </div>

                                </div>
                            </div>
                            <div>
                                <div class="pull-left" style="margin-bottom: 10px;">
                                    <@header method="auth" authCode="CHECK_BATCH_PASS">
                                    <button type="button" class="btn btn-default" onclick="batchConfirm()">
                                        <i class="icon-"></i>批量审核通过
                                    </button>
                                    </@header>
                                    <@header method="auth" authCode="CHECK_BATCH_REFUTE">
                                    <button type="button" class="btn btn-default" onclick="batchDismissed()">
                                        <i class="icon-"></i>批量驳回
                                    </button>
                                    </@header>
                                </div>
                                <div class="col-md-offset-8" style="text-align: right">
                                    <button type="button" class="btn btn-default" onclick="formReset(this)">
                                        <i class="icon-refresh"></i>重置
                                    </button>
                                    <button type="submit" class="btn blue">
                                        <i class="icon-search"></i>查询
                                    </button>
                            </div>
                            </div>
                        </form>
                    </div>
                <br/>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <!-- 内容 -->
                        <table class="table table-bordered table-hover table-condensed" id="inventoryTaskItem-list">
                            <colgroup>
                                <col width="4%"/>
                                <col width="4%"/>
                                <col width="4%"/>
                                <col width="4%"/>
                                <col width="4%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="4%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                                <col width="5%"/>
                            </colgroup>
                            <thead>
                            <tr>
                                <th><input type="checkbox" id="check-all" name="checkAll">全选</th>
                                <th>SKU</th>
                                <th>初盘/差异</th>
                                <th>复盘/差异</th>
                                <th>终盘/差异</th>
                                <th>确认数量</th>
                                <th>结果</th>
                                <th>外借待审核</br>数量</th>
                                <th>冻结库存</br>数量</th>
                                <th>任务用时(h)</th>
                                <th>SKU用时(min)</th>
                                <th>异常原因</th>
                                <th>关联批次</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <#list domain.whInventoryTaskItems as item>
                                <tr <#if item.status == 5 > style="color:red;" </#if> >
                                    <td>
                                        <input type="hidden" id="${item.id}_stockId" value="${item.stockId}"/>
                                        <input type="checkbox" class="ids" value="${item.id}" name="ids"> ${item.id}
                                    </td>
                                    <td>${item.sku }</td>
                                    <td>${item.inventoryQuantity}/${item.diffQuantity}</td>
                                    <td>${item.repeatQuantity}/${item.repeatDiffQuantity}</td>
                                    <td>${item.finallyQuantity}/${item.finallyDiffQuantity}</td>
                                    <td>${item.confirmQuantity }</td>
                                    <td>${item.confirmDiff }</td>
                                    <td>${item.wmsCheckLendQuantity}</td>
                                    <td>${item.frozenStockQuantity}</td>
                                    <td>${item.usableTime }</td>
                                    <td>${item.skuUsableTime }</td>
                                    <td>${item.anomalousCause }</td>
                                    <td>${item.batchNos }</td>
                                    <td>
                                        ${item.statusName }
                                        <input id="${item.id}_status" type="hidden" name="" value="${item.status}"/>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-info btn-xs" onclick="viewLog(${item.id}, 'whInventoryTaskItem')"
                                                style="margin: 5px">日志
                                        </button>
                                    </td>
                                </tr>
                            </#list>
                            </tbody>
                        </table>
                    <!-- 内容end -->
                    </div>
                </div>
                <div id="fixed-bottom">
                    <div id="pager"></div>
                </div>
            </div>
            <#include "/common/footer.html">
        </div>
        <script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
        <script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
        <script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
        <script type="text/javascript">
            // 分页
            var total = "${domain.page.totalCount}";
            var pageNo = "${domain.page.pageNo}";
            var pageSize = "${domain.page.pageSize}";
            $("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });

            var heights = $("body").height();
            if (heights > 910) {
                $("#fixed-bottom").addClass("fixed-bottom-height")
            }
            // 任务状态
            var statuses =  ${domain.statuses};
            $("input[name='query.reviewStatusStr']").select2({
                data: statuses,
                placeholder: "任务状态",
                multiple: true,
                allowClear: true
            });
            // 盘点过程
            var taskLevels =  ${domain.taskLevels};
            $("input[name='query.taskLevelStr']").select2({
                data: taskLevels,
                placeholder: "盘点过程",
                multiple: true,
                allowClear: true
            });
            //盘点类型
            var taskTypes =  ${domain.taskTypes};
            $("input[name='query.taskTypeStr']").select2({
                data: taskTypes,
                placeholder: "盘点类型",
                multiple: true,
                allowClear: true
            });

            // 领取人 PDA盘点任意界面的权限的人员
            $.getJSON(CONTEXT_PATH + "system/saleusers/userByPermissionCode?permissionCode=030100,030200,030300,030400", function (json) {
                if (json) {
                    $("input[name='query.receiveUser']").select2({
                        data: json,
                        placeholder: "领取",
                        allowClear: true
                    });
                } else {
                    $("input[name='query.receiveUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
                }
            });

            // 全选
            var checkAll = $("input[name='checkAll']");

            // 子选项
            var itemIds = $("input[name='ids']");
            checkAll.change(
                function () {
                    itemIds.prop("checked", $(this).prop("checked"));
                    itemIds.each(function () {
                        var f = $(this).is(":checked");
                        var checkClass = $(this).prop("class");
                        $("." + checkClass).each(function () {
                            $(this).prop("checked", f);
                        })
                    })
                }
            );

            // 获取选中的数据
            function getCheckedIds() {
                var checkedIds = $("input[name='ids']:checked");
                return checkedIds;
            }

            // 批量审核通过
            function batchConfirm() {
                var checkedDatas = getCheckedIds();
                if (checkedDatas.length == 0) {
                    layer.alert("请先勾选需要审核的盘点条目", "error");
                    return;
                } else {
                    var ids = "";
                    var stockIds = "";
                    for (var i = 0; i < checkedDatas.length; i++) {
                        var check = checkedDatas[i];
                        var id = $(check).val();
                        var status = $("#" + id + "_status").val();
                        var stockId = $("#" + id + "_stockId").val();
                        if (status == '7') {
                            layer.alert("勾选的盘点条目已有完成，请重新选择", "error");
                            return;
                        }
                        ids += id;
                        stockIds += stockId;
                        if (i != checkedDatas.length - 1) {
                            ids += ",";
                            stockIds += ",";
                        }
                    }
                    var expSkuList = {};
                    $.ajax({
                        url: CONTEXT_PATH + "warehouse/inventoryTaskReviews/getSkuQcCategoryDesc",
                        type: "POST",
                        data: {
                            "ids": ids
                        },
                        success: function (response) {
                            var message = '';
                            if (response.message != null) {
                                message = response.message
                                expSkuList = JSON.parse(response.message)
                            }
                            if (response.status == '500') {
                                customizeLayer('校验保质期sku失败：' + message, 'error');
                                return;
                            } else if (response.status == '200') {
                                if (expSkuList.length > 0) {
                                    //保质期sku只能单独审核
                                    if (checkedDatas.length > 1) {
                                        customizeLayer('保质期sku只能单独审核：' + message, 'error');
                                        return;
                                    }
                                    //保质期sku弹窗
                                    dialog({
                                        title: '确定审核通过盘点结果？',
                                        content: '确定审核通过盘点结果？',
                                        url: CONTEXT_PATH + "expManage/skuExpModel?sku=" + expSkuList[0] + "&stockIdStr=" + stockIds,
                                        width: 700,
                                        height: 250,
                                        top: 0,
                                        okValue: '确定',
                                        ok: function () {
                                            var errorMsg = "";
                                            var batchNoQty = [];
                                            var exportWindow = $(this.iframeNode.contentWindow.document.body);
                                            exportWindow.find('#tbody').find('tr').each(function (index) {
                                                var dataIndex = $(this).find('td[id="data-index"]').text();
                                                var dataBatch = $(this).find('td[id="data-batch"]').find('select[id="batchNo"]').val();
                                                var quantity = $(this).find('td>input[id="data-newQuantity"]').val();
                                                if (dataBatch == null || dataBatch == "" || dataBatch == undefined) {
                                                    errorMsg = '第' + dataIndex + '个sku选择批次为空！';
                                                    return;
                                                }
                                                if (quantity == '' || quantity == "undefined") {
                                                    errorMsg = '第' + dataIndex + '个sku的确认数量为空！';
                                                    return false;
                                                }
                                                var r = /^\d+$/;
                                                if (!r.test(quantity)) {
                                                    errorMsg = "请填写正整数！";
                                                    return false;
                                                }
                                                /*if(quantity == 0){
                                                    errorMsg="第"+dataIndex+"个sku 不能填写0-不需要请删除！", "error";
                                                    return false;
                                                }*/
                                                batchNoQty.push(dataBatch + "|" + quantity);
                                            });
                                            if (errorMsg != "") {
                                                layer.alert(errorMsg);
                                                return false;
                                            }
                                            // 导出当前选择
                                            if (batchNoQty && batchNoQty.length > 0) {
                                                $.ajax({
                                                    url: CONTEXT_PATH + "warehouse/inventoryTaskReviews/batchConfirm",
                                                    type: "POST",
                                                    data: {"ids": ids, "batchNos": JSON.stringify(batchNoQty)},
                                                    success: function (data) {
                                                        if (data.status == 200) {
                                                            layer.alert(data.message);
                                                        } else {
                                                            layer.alert(data.message);
                                                        }
                                                        setTimeout(function () {
                                                            location.reload();
                                                        }, 3000);
                                                    }
                                                });
                                            } else {
                                                layer.alert('请选择有效批次！');
                                                return;
                                            }
                                        },
                                        cancelValue: '取消操作',
                                        cancel: function () {
                                            //window.location.reload();
                                        }
                                    }).showModal()
                                } else {
                                    //非保质期sku弹窗
                                    var diglog = dialog({
                                        title: '',
                                        content: '确定审核通过盘点结果？',
                                        onshow: function () {
                                        },
                                        okValue: '确定',
                                        ok: function () {
                                            $.ajax({
                                                url: CONTEXT_PATH + "warehouse/inventoryTaskReviews/batchConfirm",
                                                type: "POST",
                                                data: {
                                                    "ids": ids
                                                },
                                                success: function (response) {
                                                    var message = '';
                                                    if (response.message != null) {
                                                        message = response.message
                                                    }
                                                    if (response.status == '500') {
                                                        customizeLayer('审核盘点失败：' + message, 'error');
                                                        return;
                                                    } else if (response.status == '200') {
                                                        layer.alert('审核成功：' + message);
                                                        setTimeout(function () {
                                                            window.location.reload();
                                                        }, 1500);
                                                    }
                                                },
                                                error: function () {
                                                    layer.alert('操作失败!', 'error');
                                                }
                                            });
                                            setTimeout(function () {
                                                diglog.close().remove();
                                            }, 100);
                                            return false;
                                        },
                                        cancelValue: '取消操作',
                                        cancel: function () {
                                        }
                                    }).showModal();
                                }

                            }
                        },
                        error: function () {
                            layer.alert('校验保质期sku失败!', 'error');
                        }
                    });
                }
            }

            // 批量驳回
            function batchDismissed() {
                var checkedDatas = getCheckedIds();
                if (checkedDatas.length == 0) {
                    layer.alert("请先勾选需要驳回的盘点条目", "error");
                    return;
                } else {
                    var ids = "";
                    for (var i = 0; i < checkedDatas.length; i++) {
                        var check = checkedDatas[i];
                        var id = $(check).val();
                        var status = $("#" + id + "_status").val();
                        if (status == '7') {
                            layer.alert("勾选的盘点条目已有完成，请重新选择", "error");
                            return;
                        }
                        ids += id;
                        if (i != checkedDatas.length - 1) {
                            ids += ",";
                        }
                    }
                    var diglog = dialog({
                        title: '确定驳回盘点结果？',
                        width: 350,
                        height: 100,
                        url: CONTEXT_PATH + "warehouse/pickInventoryDemands/discardInventoryDemandType",
                        onshow: function () {
                        },
                        okValue: '确定',
                        ok: function () {
                            var exportWindow = $(this.iframeNode.contentWindow.document.body);
                            var submitForm = exportWindow.find("#submit-form");
                            var submitFormParam = submitForm.serialize();

                            var type = submitForm.find("input[name='discardType']:checked").val();
                            // type = "reset";
                            // type = "discard";
                            if (type == null || type == '') {
                                layer.alert("请选择类型!", "error");
                                return false;
                            }

                            $.ajax({
                                url: CONTEXT_PATH + "warehouse/inventoryTaskReviews/batchDismissed",
                                type: "POST",
                                data: {
                                    "ids": ids, "type": type
                                },
                                success: function (response) {
                                    var message = '';
                                    if (response.message != null) {
                                        message = response.message
                                    }
                                    if (response.status == '500') {
                                        customizeLayer('驳回盘点盘点失败：' + message, 'error');
                                        return;
                                    } else if (response.status == '200') {
                                        layer.alert('已经成功驳回盘点：' + message);
                                        setTimeout(function () {
                                            window.location.reload();
                                        }, 1500);
                                    }
                                },
                                error: function () {
                                    layer.alert('操作失败!', 'error');
                                }
                            });
                            setTimeout(function () {
                                diglog.close().remove();
                            }, 100);
                            return false;
                        },
                        cancelValue: '取消操作',
                        cancel: function () {
                        }
                    }).showModal();
                }
            }

            // 导出
            function download() {
                if (getCheckedIds().length == 0) {
                    var param = $("#domain").serialize();
                    window.open(CONTEXT_PATH + "warehouse/inventoryTaskReviews/download?" + param);
                } else {
                    downloadByPost();
                }
            }

            // 超过500条不能用GET请求
            function downloadByPost() {
                var checkedDatas = getCheckedIds();
                var ids = "";
                for (var i = 0; i < checkedDatas.length; i++) {
                    var check = checkedDatas[i];
                    var id = $(check).val();
                    ids += id;
                    if (i != checkedDatas.length - 1) {
                        ids += ",";
                    }
                }
                var url = CONTEXT_PATH + "warehouse/inventoryTaskReviews/download";
                var tempForm = document.createElement("form");
                tempForm.id = "tempForm";
                tempForm.method = "post";
                tempForm.action = url;
                tempForm.target = "blank";
                var hideInput = document.createElement("input");
                hideInput.type = "hidden";
                hideInput.name = "ids";
                hideInput.value = ids;
                tempForm.appendChild(hideInput);
                if (tempForm.attachEvent) { // IE
                    tempForm.attachEvent("onsubmit", function () {
                        window.open('about:blank', 'blank');
                    });
                } else if (tempForm.addEventListener) { // DOM Level 2 standard
                    tempForm.addEventListener("onsubmit", function () {
                        window.open('about:blank', 'blank');
                    });
                }
                document.body.appendChild(tempForm);
                if (document.createEvent) { // DOM Level 2 standard
                    evt = document.createEvent("MouseEvents");
                    evt.initMouseEvent("submit", true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
                    tempForm.dispatchEvent(evt);
                } else if (tempForm.fireEvent) { // IE
                    tempForm.fireEvent('onsubmit');
                }
                //必须手动的触发
                tempForm.submit();
                document.body.removeChild(tempForm);
            }

            /**
             * 限制文件类型
             * @param target
             */
            function importInventory(target) {

                //检测上传文件的类型
                var filename = target.value;

                var ext, idx;
                if (filename == '') {
                    $("#submit-upload").attr("disabled", true);
                    layer.alert("请选择需要上传的文件!");
                    return;
                } else {
                    idx = filename.lastIndexOf(".");
                    if (idx != -1) {
                        ext = filename.substr(idx + 1).toUpperCase();
                        ext = ext.toLowerCase();

                        if (ext != 'xls' && ext != 'xlsx') {
                            layer.alert("只能上传.Excel类型的文件!");
                            return;
                        }
                    } else {
                        layer.alert("只能上传.Excel类型的文件!");
                        return;
                    }
                }

                var r = confirm("确定上传" + filename + "?");

                if (!r) {
                    return;
                }

                var uploadUrl = CONTEXT_PATH + "warehouse/inventoryTaskReviews/upload";

                var searchUrl = $("#domain").attr("action");

                $("#domain").attr("action", uploadUrl);

                $("#domain").ajaxSubmit(function (data) {
                    if (data.status == 200) {
                        layer.alert("成功！");
                        setTimeout(function () {
                            window.location.reload();
                        }, 1000);
                    } else {
                        customizeLayer(data.message, "error");
                    }

                    $("#domain").attr("action", searchUrl);
                });

                $("#domain").attr("action", searchUrl);
            }
        </script>
    </body>
</html>