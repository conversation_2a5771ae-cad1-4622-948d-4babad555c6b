<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		#task-list td {
			vertical-align: middle;
		}
	</style>
</head>
<body>
<@header method="header" active="18050000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li>
					<a href="#">库存管理</a>
				</li>
				<li class="active">耗材出库单管理</li>
			</ul>
		</div>
	</div>
	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">

		<!-- BEGIN PAGE HEADER-->
		<#assign query=domain.query>
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="col-md-12">
				<form action="${CONTEXT_PATH}materialOutOrder/search"
					  class="form-horizontal form-bordered form-row-stripped"
					  method="post" modelAttribute="domain" name="materialOutOrderForm" id="domain">
					<!-- 分页信息 -->
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-1">耗材型号</label>
							<div class="col-md-3">
								<input class="form-control" type="text" name="query.materialArticleNumber" placeholder="多个查询请以英文逗号分开"  value="${query.materialArticleNumber}">
							</div>
							<label class="control-label col-md-1">出库单号</label>
							<div class="col-md-3">
								<input class="form-control" type="text" name="query.orderNo" placeholder="多个查询请以英文逗号分开"  value="${query.orderNo }">
							</div>
							<label class="control-label col-md-1">领取人</label>
							<div class="col-md-3">
								<input class="form-control" name="query.receiveBy" type="text" value="${query.receiveBy}">
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1">创建人</label>
							<div class="col-md-3">
								<input class="form-control" name="query.creationBy" type="text" value="${query.creationBy}">
							</div>
							<label class="control-label col-md-1">出库时间</label>
							<div class="col-md-3">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.fromCreationDate"
										   placeholder=""
										   readonly="readonly" value="${query.fromCreationDate }"
										   onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.endCreationDate"
										   placeholder=""
										   readonly="readonly" value="${query.endCreationDate }"
										   onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>
							<label class="control-label col-md-1">出库单状态</label>
							<div class="col-md-3">
								<input class="form-control" name="query.status" type="text" value="${query.status}">
							</div>
						</div>
					</div>
					<div>

						<div class="pull-left" style="margin-left: 10px">
							<@header method="auth" authCode="CREATOR_MATERIAL_OUT_ORDER">
							<a class="btn btn-default" href="${CONTEXT_PATH}materialOutOrder/create">
								<span class="icon-plus"></span> 添加耗材出库单
							</a>
							</@header>
							<@header method="auth" authCode="MATERIAL_INVENTORY_BATCH_AUDIT">
							<button type="button" class="btn btn-default" onclick="materialInventoryBatchAudit()">
								<i class="icon-"></i>批量审核
							</button>
							</@header>

							<@header method="auth" authCode="IMPORT_MATERIAL_OUT_ORDER">
								<span class="btn btn-default fileinput-button">
									<span class="icon-plus"> 导入耗材出库单</span>
									<input type="file" name="file" onchange="importMaterialOutOrder(this)" />
								</span>
							</@header>
							<@header method="auth" authCode="DOWNLOAD_IMPORT_MATERIAL_OUT_ORDER">
								<a class="btn btn-default" href="${CONTEXT_PATH}file/execl/material_out_order_import.xlsx">
									<i class="icon-download"></i> 下载导入耗材出库单模板Excel
								</a>
							</@header>


						</div>
						<div class="col-md-offset-12" style="text-align: right">
                            <@header method="auth" authCode="MATERIAL_OUT_ORDER_DOWNLOAD_INVENTORY">
							<button type="button" class="btn btn-default" onclick="downloadStocks()">
								<i class="icon-download"></i> 导出
							</button>
                            </@header>
							<button type="button" class="btn btn-default" onclick="formReset(this)">
								<i class="icon-refresh"></i> 重置
							</button>
							<button type="submit" class="btn blue">
								<i class="icon-search"></i> 查询
							</button>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>

		<div class="row">
			<div class="col-md-12" id="task-list-warp">
				<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
					<thead>
					<tr>
						<th><label class="checkbox-inline"><input type="checkbox" name="checkAll"> 全选</label></th>
						<th>出库单号</th>
						<th>耗材</th>
                        <th>领用数量</th>
						<th>领取人</th>
						<th>创建人/创建时间</th>
						<th>备注</th>
						<th>出库单状态</th>
						<th>操作</th>
					</tr>
					</thead>
					<tbody>
					<#list domain.whPackagingMaterialOutOrders as stock>
					<tr>
						<td>
							<label class="checkbox-inline">
								<input class="${stock.id}" name="ids" type="checkbox" value="${stock.id}">
								${stock.id}
							</label>
						</td>
						<td>${stock.orderNo}</td>
						<td>${stock.materialArticleNumber}</td>
						<td>${stock.quantity}</td>
						<td>
							${util('name',stock.receiveBy)}
						</td>
						<td>
							${util('name',stock.creationBy)}<br/>${stock.creationDate}
						</td>
						<td>${stock.remark}</td>

						<td>${util('enumName', 'com.estone.warehouse.enums.MaterialOutOrderEnums', stock.status)}</td>

						<td>
							<#if stock.status?? &&(stock.status==1 || stock.status==3)>
								<a type="button" class="btn btn-xs btn-info" onclick="editOutOrder('${stock.id}','${stock.quantity}','${stock.materialArticleNumber}')">编辑</a>
								<a type="button" class="btn btn-xs btn-info" onclick="discardMaterialInventory('${stock.id}')">废弃</a>
							</#if>
							<button type="button" class="btn btn-xs btn-info" onclick="viewLog(${stock.id}, 'materialOutOrder')">日志</button>
						</td>
					</tr>
				</#list>
				</tbody>
				</table>
			</div>
		</div>

		<div id="fixed-bottom" >
			<div id="pager"><ur class="pages"><li class="select"><select><option>10</option></select></ur></div>
		</div>
	</div>

	<#include "/common/footer.html">
	<img id="enlarge" style='position:absolute;width:800px;height:800px;top:10%;left:15%;display:none;'/>
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/access.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
<script type="text/javascript">
	// 分页
	var total = "${domain.page.totalCount}";
	var pageNo = "${domain.page.pageNo}";
	var pageSize = "${domain.page.pageSize}";

	$("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });
	var heights = $("body").height();
	if(heights > 910) {
		$("#fixed-bottom").addClass("fixed-bottom-height")
	}
	// 分页
	var total = "${domain.page.totalCount}";
	var pageNo = "${domain.page.pageNo}";
	var pageSize = "${domain.page.pageSize}";
	$("#pager").initPager({total: total, pageNo: pageNo, pageSize: pageSize});

	var heights = $("body").height();
	if (heights > 910) {
		$("#fixed-bottom").addClass("fixed-bottom-height")
	}
	var isFilterZero = "${domain.query.isFilterZero}";
	if (isFilterZero == "true") {
		$("input[name='query.isFilterZero']").attr('checked', true);
	}
	function filterZero(obj) {
		if ($(obj).is(':checked')) {
			$(obj).val(true);
		} else {
			$(obj).val(false);
		}
	}
	// 类型
	var typeArray =${domain.statusList};
	$("input[name='query.status']").select2({
		data: typeArray,
		placeholder: "盘点单状态",
		multiple: false,
		allowClear: true
	});

	// 全选
	var checkAll = $("input[name='checkAll']");
	// 子选项
	var itemIds = $("input[name='ids']");
	checkAll.change(
			function() {
				itemIds.prop("checked", $(this).prop("checked"));
				itemIds.each(function() {
					var f = $(this).is(":checked");
					var checkClass = $(this).prop("class");
					$("." + checkClass).each(function() {
						$(this).prop("checked", f);
					})
				})
			}
	);
	// 获取选中的记录
	function getCheckedIds() {
		var checkedIds = $("input[name='ids']:checked");
		return checkedIds;
	}


	// 导出
	function downloadStocks() {
		var checkedDatas = getCheckedIds();

		var diglog = dialog({
			title: '导出',
			width: 350,
			height:100,
			url: CONTEXT_PATH + "transit/return/downloadmode",
			okValue: '确定',
			ok: function () {

				var exportWindow = $(this.iframeNode.contentWindow.document.body);

				var submitForm = exportWindow.find("#submit-form");

				var exportType = submitForm.find("input[name='exportType']:checked").val();

				var submitFormParam = submitForm.serialize();

				// 导出当前选择
				if(exportType == 3) {
					if(checkedDatas.length == 0) {
						layer.alert("请选择要操作的数据!");
						return false;
					} else if (checkedDatas.length > 300) {
						layer.alert("选择数量不能超过300!");
						return false;
					}

					submitFormParam = submitFormParam + "&" +checkedDatas.serialize();

				}

				//还原分页
				$("#page-no").val("${domain.page.pageNo}");


				var action = document.materialOutOrderForm.action;
				var target = document.materialOutOrderForm.target;
				var method = document.materialOutOrderForm.method;
				document.materialOutOrderForm.action= CONTEXT_PATH + "materialOutOrder/download?" + submitFormParam;
				document.materialOutOrderForm.target="_blank";
				document.materialOutOrderForm.method="POST";
				document.materialOutOrderForm.submit();
				document.materialOutOrderForm.target=target;
				document.materialOutOrderForm.action=action;
				document.materialOutOrderForm.method=method;

				$("#page-no").val("1");

				setTimeout(function () {
					diglog.close().remove();
				}, 100);

				return true;
			},
			cancelValue: '取消',
			cancel: function () {}
		});
		diglog.show();
	}
	// 领取人
	$.getJSON(CONTEXT_PATH + "system/saleusers/userByRole", function (json) {
		if (json) {
			$("input[name='query.creationBy']").select2({
				data: json,
				placeholder: "创建人",
				allowClear: true
			});
			$("input[name='query.receiveBy']").select2({
				data: json,
				placeholder: "领取人",
				allowClear: true
			});
		} else {
			$("input[name='query.inventoryBy']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
		}
	});


	function discardMaterialInventory(id) {
		if(id==null||id=='') {
			layer.alert("参数异常！", 'error');
			return;
		}
		var diglog = dialog({
			title:"耗材出库单废弃",
			width: 500,
			height: 150,
			url: CONTEXT_PATH + "materialOutOrder/discardMaterialOutOrder",
			okValue: '确定',
			ok: function () {
				debugger;
				var batchCommentWindow = $(this.iframeNode.contentWindow.document.body);
				var submitForm = batchCommentWindow.find("#submit-form");
				var remark = submitForm.find("#remark").val();
				if(submitForm) {
					$.getJSON(CONTEXT_PATH + "materialOutOrder/discardWhMaterialOutOrder?id=" + id+"&remark="+remark, function(result){
						if(result.status == 200) {
							layer.alert("废弃成功！", "success");
							setTimeout(function () {
								window.location.reload();
							}, 2000);
						} else {
							customizeLayer(result.message, "error");
						}
					});

				}
				setTimeout(function() {
					diglog.close().remove();
				}, 100);
				return false;
			},
			cancelValue:'取消',
			cancel: function () {}

		});
		diglog.show();
	}



	//审核
	function materialInventoryBatchAudit() {
		debugger;
		var ids = getCheckedIds();
		if(ids.length == 0) {
			layer.alert("请选择要操作的数据", 'error');
			return;
		}
		var diglog = dialog({
			title:"耗材出库单审批",
			width: 500,
			height: 150,
			url: CONTEXT_PATH + "packagingMaterialInventory/materialInventoryAudit",
			okValue: '确定',
			ok: function () {
				debugger;
				var batchCommentWindow = $(this.iframeNode.contentWindow.document.body);
				var submitForm = batchCommentWindow.find("#submit-form");
				var auditValue = submitForm.find("input[name='auditValue']:checked").val();
				var remark = submitForm.find("#remark").val();
				if ((auditValue!=null && auditValue!='' && auditValue=="false") && (remark==null || remark=='' || remark == undefined || remark.trim().length<=0)){
					layer.alert("驳回时需要填写备注！","error");
					return ;
				}
				debugger;
				if(submitForm) {
					$.getJSON(CONTEXT_PATH + "materialOutOrder/materialInventoryBatchAudit?auditValue="+auditValue+"&remark="+remark+"&"+ids.serialize(), function(result){
						if(result.status == 200) {
							layer.alert("审核成功！", "success");

						} else {
							layer.alert(result.message, "error");
						}
						setTimeout(function () {
							window.location.reload();
						}, 2000);
					});

				}
				setTimeout(function() {
					diglog.close().remove();
				}, 100);
				return false;
			},
			cancelValue:'取消',
			cancel: function () {}

		});
		diglog.show();
	}


	function editOutOrder(id,quantity,materialArticleNumber) {
		layer.prompt({
			formType: 0,
			value: quantity,
			title: '修改出库单领用数量',
			maxlength: 140,
		}, function(value, index, elem){
			$.ajax({
				url: CONTEXT_PATH + "materialOutOrder/editOutOrder",
				type: "POST",
				data: {id:id, quantity:value,materialArticleNumber:materialArticleNumber},
				success: function (result) {
					customizeLayer2(result);
				}
			});
		});
	}

	/**
	 * 限制文件类型
	 * @param target
	 */
	function importMaterialOutOrder(target) {

		//检测上传文件的类型
		var filename = target.value;

		var ext, idx;
		if (filename == '') {
			$("#submit-upload").attr("disabled", true);
			layer.alert("请选择需要上传的文件!");
			return;
		} else {
			idx = filename.lastIndexOf(".");
			if (idx != -1) {
				ext = filename.substr(idx + 1).toUpperCase();
				ext = ext.toLowerCase();

				if (ext != 'xls' && ext != 'xlsx') {
					layer.alert("只能上传.Excel类型的文件!");
					return;
				}
			} else {
				layer.alert("只能上传.Excel类型的文件!");
				return;
			}
		}

		var r = confirm("确定上传" + filename + "?");

		if(!r) {
			return;
		}
		var uploadUrl = CONTEXT_PATH + "materialOutOrder/import";
		$("#domain").attr("action", uploadUrl);
		$("#domain").ajaxSubmit(function(data) {
			if (data.status == 200) {
				alert("成功！");
				setTimeout(function () {
					location.reload()
				}, 1000);
			} else {
				layer.confirm(data.message ||'程序出错!导入不成功', {
					icon: 2,
					btn: ['确定'],
					btn1:function () {
						location.reload()
					},
					end:function(){
						location.reload()
					}
				});

			}
		});

	}

</script>
</body>
</html>