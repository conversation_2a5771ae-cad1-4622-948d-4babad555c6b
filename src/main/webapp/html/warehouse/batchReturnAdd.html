<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
#print-waybill{
    margin-left: 25%;
    margin-top: 20px;
}
.region-style{
    width: 30%;
    float: left;
}
</style>
</head>
<body>
	<@header method="header" active="17020000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">入库管理</a></li>
					<li class="active">退货批次</li>
					<li class="active">新增退货批次</li>
				</ul>
			</div>
		</div>
		<div class="container-fluid" style="background-color: white;border: none">
		<#assign whBatchReturn=domain.whBatchReturn>
		<div class="row">
			<div class="col-md-9">
				<form action="${CONTEXT_PATH}whBatchReturn/update"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="whBatchReturnForm" id="domain">
					<input type="hidden" id="whBatchReturnId" name="whBatchReturn.id" value="${whBatchReturn.id}">
					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-1" ></label>
							<div class="col-md-3"></div>
							<label class="control-label col-md-1" >退货批次号</label>
							<div class="col-md-5">
								<div>
								   <input type="text" name="whBatchReturn.orderNo" readonly class="form-control" value="${whBatchReturn.orderNo}">
								</div>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1" ></label>
							<div class="col-md-3"></div>
							<label class="control-label col-md-1">入库单类型</label>
							<div class="col-md-5">
								<select class="form-control" id ="returnWarehouseId" name="whBatchReturn.warehouseId" value="${whBatchReturn.warehouseId}" <#if (whBatchReturn.warehouseId) != null>disabled</#if> >
									<option <#if (whBatchReturn.warehouseId)?? && whBatchReturn.warehouseId == 1>selected</#if> value="1">本仓</option>
									<option <#if whBatchReturn.warehouseId == 2>selected</#if> value="2">中转仓</option>
									<option <#if whBatchReturn.warehouseId == 4>selected</#if> value="4">拼多多</option>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1" ></label>
							<div class="col-md-3"></div>
							<label class="control-label col-md-1" >扫描包裹</label>
							<div class="col-md-5">
								<div id="input_sku">
									<!--<input class="form-control" id="scan_no" style="height:100px;" type="text" onblur="" placeholder="扫描物流单号/唯一码" onkeypress="if(event.keyCode==13) { inputnext(this); return false;}"/>-->
									<textarea id="scan_no" class="form-control" cols="50" rows="5" type="text" placeholder="扫描物流单号/唯一码" value="" onkeypress="if(event.keyCode==13) { inputnext(this); return false;}"></textarea>
									<input type="hidden" id="uuidList" name="uuidList" value=""/>
									<input type="hidden" id="dellApvNoList" name="dellApvNoList" value=""/>
								</div>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1" ></label>
							<div class="col-md-3"></div>
							<label class="control-label col-md-1" ></label>
							<div class="col-md-5" style="max-height: 700px;overflow-y: scroll;">
								已扫描数据
								<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
									<thead>
										<tr>
											<th>追踪号</th>
											<th>YST</th>
											<th>SKU</th>
											<th>返架数量</th>
											<th>删除</th>
										</tr>
									</thead>
									<tbody id="tskubody">
										<#list whBatchReturn.batchReturnApvVos as returnApvVo>
											<tr>
												<td>${returnApvVo.trackingNo}</td>
												<td>${returnApvVo.apvNo}</td>
												<td>
                                                    <#list returnApvVo.items as item>
                                                        ${item.sku}
                                                        <#if item_index != returnApvVo.items?size-1 >
                                                            <hr/>
                                                        </#if>
                                                    </#list>
												</td>
												<td>
                                                    <#list returnApvVo.items as item>
                                                        ${item.quantity}
                                                        <#if item_index != returnApvVo.items?size-1 >
                                                            <hr/>
                                                        </#if>
                                                    </#list>
												</td>
												<td onclick='dell(this,"${returnApvVo.apvNo}")'><i class='icon-remove'></i></td>
											</tr>
										</#list>
									</tbody>
								</table>
							</div>
						</div>

					</div>
					<div class="col-md-offset-1" style="text-align: center;height: 100px;">
						<button type="submit" class="btn blue" id="save_return">
							<i class="icon-search"></i> 保存
						</button>
					</div>
				</form>
			</div>

			<div style="padding-top: 0%;">
				<div class="col-md-3" style="margin-left: -190px;">
					<h2>当前扫描：<span id="scan-history" style="font-weight:bold;color:red;"></span></h2>
					<h2>扫描结果：
						<span id="error" style="font-weight:bold;color:red;"></span>
						<span id="success" style="font-weight:bold;color:green;"></span>
					</h2>
					<h2>
						<span id="errorMes" style="color:red;"></span>
						<span id="successMes" style="color:green;"></span>
					</h2>
				</div>
			</div>
		</div>
	</div>

	<div id="fixed-bottom">
		<div id="pager"></div>
	</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/packing.js?v=${.now?datetime}"></script>
	<script type="text/javascript">

        var uuIdCacheKey = 'CHECK_BATCH_RETURN_FOR_UUID_CACHE_KEY_RETURN';
		var san_array = new Array();
		var uuid_array = new Array();
		var dellApvNoList_array = new Array();
		var num =  - 1;

        $(document).ready(function(){
            initSkuUuIdStorageCache(uuIdCacheKey);
        }); // end ready

		function cleanMes(){
            $("#error").text("");
            $("#success").text("");
            $("#errorMes").text("");
            $("#successMes").text("");
		}

		var userForm = $('#domain');
        var url = $("#domain").attr("action");
		$('#save_return').on('click', function(){
			var orderNo = $("input[name='whBatchReturn.orderNo']").val().trim();
			if(orderNo == undefined || orderNo == ''){
				layer.alert('批次号缺失','error');
				return false;
			}
			if(dellApvNoList_array.length == 0 && $('#tskubody').find('tr').length == 0){
				layer.alert('没有要保存的数据','error');
				return false;
			}
			$('#save_return').attr("disabled", true);
			if(uuid_array && uuid_array.length > 0){
				$("#uuidList").val(uuid_array);
			}
			if(dellApvNoList_array && dellApvNoList_array.length > 0){
				$("#dellApvNoList").val(dellApvNoList_array);
			}
			App.blockUI();

			$.post(url, $('#domain').serialize(), function(data){
				if(data.status == 500){
					customizeLayer("失败: " + data.message,'error');
					$('#save_return').attr("disabled", false);
				}else {
					alert("操作成功");
					setTimeout(jump, 2000);
				}
			});
			function jump(){
				location.href = CONTEXT_PATH + "whBatchReturn";
			}
		});

		function inputnext(obj) {
			var apv_array=new Array();
			$("[id=apv]").each(function(){
				apv_array.push($(this).text());
			});

			var orderNo = $("input[name='whBatchReturn.orderNo']").val().trim();
			var returnWarehouseId = $("#returnWarehouseId").val().trim();
			var val = $('#scan_no').val().trim();
			$("#scan-history").text(val);
			cleanMes();
			var uuid = '';
			var trackingNo = '';
			if(!val || val.trim() == ''){
				layer.alert("请输入有效的 物流单号/唯一码!",'error');
				$('#scan_no').val("");
				$('#scan_no').focus();
				return ;
			}

			if(!(val.indexOf("=") == -1)){
				uuid = val;
				if(san_array.contains(uuid)){
					layer.alert("唯一码重复扫描!",'error');
					$('#scan_no').val("");
					$('#scan_no').focus();
					return ;
				}
			}else {
				trackingNo = val;
				if(san_array.contains(trackingNo)){
					layer.alert("追踪号重复扫描!",'error');
					$('#scan_no').val("");
					$('#scan_no').focus();
					return ;
				}
			}
			$.ajax({
				url:CONTEXT_PATH+"whBatchReturn/scanNo",
				type:"POST",
				data:{
					id:$("#whBatchReturnId").val(),
					orderNo:orderNo,
					trackingNo:trackingNo,
					uuid:uuid,
					warehouseId:returnWarehouseId
				},
				success:function(result){
					if(result.status == "200"){
						san_array.push(val);

						// 扫描成功唯一码加入缓存
						//addUuIdStorageCache(uuIdCacheKey, uuid);
						console.log(result.body['SUCCESS']);
						var elements = result.body['SUCCESS'];

						for (var m = 0; m < elements.length; m++) {
							var whApv = elements[m].whApv;
							var skuAisle = elements[m].skuAisle;
							if (returnWarehouseId == '2'){
								var transferStocks = elements[m].transferStocks;
								if (apv_array.contains(whApv.fbaNo)) {
									layer.alert(whApv.fbaNo + " 订单已扫描", 'error');
									$("#error").text("失败");
									$("#errorMes").text("找不到订单");
									$('#scan_no').val("");
									$('#scan_no').focus();
									return;
								}
								var trackingNumber = whApv.trackingNumber;
								if(trackingNumber == '' || trackingNumber == null){
									trackingNumber = whApv.shippingOrderNo;
								}
								var i = apv_array.length;
								var size = whApv.whApvItems.length;
								var apvoption = "<tr>" +
										"<td>" + trackingNumber + "</td>" +
										"<td id='apv'>" + whApv.fbaNo + "</td>";

								var skuoption = "<td>";
								var quantityoption = "<td>";
								var inputoption = "";

								for (j = 0; j < size; j++) {
									num = num + 1;
									var whApvItem = whApv.whApvItems[j];
									skuoption += whApvItem.productSku;
									quantityoption += whApvItem.loadingQuantity;
									if (j < size - 1) {
										skuoption += "<hr/>";
										quantityoption += "<hr/>";
									}
									var aisle = '';
									if (skuAisle[whApvItem.productSku]) {
										// 通道
										aisle = skuAisle[whApvItem.productSku];
									}
									var warehouseId = '';
									if (transferStocks[whApvItem.productSku]) {
										// 通道
										warehouseId = transferStocks[whApvItem.productSku];
									}
									inputoption += "<input type='text' name='whBatchReturns[" + 0 + "].whBatchReturnApvItems[" + num + "].apvNo' style='display:none' value='" + whApv.fbaNo + "'/>";
									inputoption += "<input type='text' name='whBatchReturns[" + 0 + "].whBatchReturnApvItems[" + num + "].trackingNo' style='display:none' value='" + trackingNumber + "'/>";
									inputoption += "<input type='text' name='whBatchReturns[" + 0 + "].whBatchReturnApvItems[" + num + "].sku' style='display:none' value='" + whApvItem.productSku + "'/>";
									inputoption += "<input type='text' name='whBatchReturns[" + 0 + "].whBatchReturnApvItems[" + num + "].quantity' style='display:none' value='" + whApvItem.loadingQuantity + "'/>";
									inputoption += "<input type='text' name='whBatchReturns[" + 0 + "].whBatchReturnApvItems[" + num + "].warehouseId' style='display:none' value='" + warehouseId + "'/>";
									inputoption += "<input type='text' name='whBatchReturns[" + 0 + "].whBatchReturnApvItems[" + num + "].road' style='display:none' value='" + aisle + "'/>";
								}
							}else if (returnWarehouseId == '4'){
								var transferStocks = elements[m].transferStocks;
								if (apv_array.contains(whApv.fbaNo)) {
									layer.alert(whApv.fbaNo + " 订单已扫描", 'error');
									$("#error").text("失败");
									$("#errorMes").text("找不到订单");
									$('#scan_no').val("");
									$('#scan_no').focus();
									return;
								}
								var trackingNumber = whApv.trackingNumber;
								if(trackingNumber == '' || trackingNumber == null){
									trackingNumber = whApv.shippingOrderNo;
								}

								var size = whApv.temuReturnItems.length;
								var apvoption = "<tr>" +
										"<td>" + trackingNumber + "</td>" +
										"<td id='apv'>";

								var skuoption = "<td>";
								var quantityoption = "<td>";
								var inputoption = "";

								for (j = 0; j < size; j++) {
									num = num + 1;
									var whApvItem = whApv.temuReturnItems[j];
									apvoption += whApvItem.fbaNo;
									skuoption += whApvItem.productSku;
									quantityoption += whApvItem.loadingQuantity;
									if (j < size - 1) {
										apvoption += "<hr/>";
										skuoption += "<hr/>";
										quantityoption += "<hr/>";
									}
									var aisle = '';
									if (skuAisle[whApvItem.productSku]) {
										// 通道
										aisle = skuAisle[whApvItem.productSku];
									}
									var warehouseId = '';
									if (transferStocks[whApvItem.productSku]) {
										// 通道
										warehouseId = transferStocks[whApvItem.productSku];
									}
									inputoption += "<input type='text' name='whBatchReturns[" + 0 + "].whBatchReturnApvItems[" + num + "].apvNo' style='display:none' value='" + whApvItem.fbaNo + "'/>";
									inputoption += "<input type='text' name='whBatchReturns[" + 0 + "].whBatchReturnApvItems[" + num + "].trackingNo' style='display:none' value='" + trackingNumber + "'/>";
									inputoption += "<input type='text' name='whBatchReturns[" + 0 + "].whBatchReturnApvItems[" + num + "].sku' style='display:none' value='" + whApvItem.productSku + "'/>";
									inputoption += "<input type='text' name='whBatchReturns[" + 0 + "].whBatchReturnApvItems[" + num + "].quantity' style='display:none' value='" + whApvItem.loadingQuantity + "'/>";
									inputoption += "<input type='text' name='whBatchReturns[" + 0 + "].whBatchReturnApvItems[" + num + "].warehouseId' style='display:none' value='" + warehouseId + "'/>";
									inputoption += "<input type='text' name='whBatchReturns[" + 0 + "].whBatchReturnApvItems[" + num + "].road' style='display:none' value='" + aisle + "'/>";
								}
								apvoption += "</td>";
							}else {
								if (apv_array.contains(whApv.apvNo)) {
									layer.alert(whApv.apvNo + " 订单已扫描", 'error');
									$("#error").text("失败");
									$("#errorMes").text("找不到订单");
									$('#scan_no').val("");
									$('#scan_no').focus();
									return;
								}
								//$("#success").text("成功");
								//$("#successMes").text(whApv.apvNo);
								var i = apv_array.length;
								var size = whApv.whApvItems.length;
								var apvoption = "<tr>" +
										"<td>" + whApv.trackingNumber + "</td>" +
										"<td id='apv'>" + whApv.apvNo + "</td>";

								var skuoption = "<td>";
								var quantityoption = "<td>";
								var inputoption = "";

								for (j = 0; j < size; j++) {
									num = num + 1;
									var whApvItem = whApv.whApvItems[j];
									skuoption += whApvItem.sku;
									quantityoption += whApvItem.saleQuantity;
									if (j < size - 1) {
										skuoption += "<hr/>";
										quantityoption += "<hr/>";
									}
									var aisle = '';
									if (skuAisle[whApvItem.sku]) {
										// 通道
										aisle = skuAisle[whApvItem.sku];
									}
									inputoption += "<input type='text' name='whBatchReturns[" + 0 + "].whBatchReturnApvItems[" + num + "].apvNo' style='display:none' value='" + whApv.apvNo + "'/>";
									inputoption += "<input type='text' name='whBatchReturns[" + 0 + "].whBatchReturnApvItems[" + num + "].trackingNo' style='display:none' value='" + whApv.trackingNumber + "'/>";
									inputoption += "<input type='text' name='whBatchReturns[" + 0 + "].whBatchReturnApvItems[" + num + "].sku' style='display:none' value='" + whApvItem.sku + "'/>";
									inputoption += "<input type='text' name='whBatchReturns[" + 0 + "].whBatchReturnApvItems[" + num + "].quantity' style='display:none' value='" + whApvItem.saleQuantity + "'/>";
									inputoption += "<input type='text' name='whBatchReturns[" + 0 + "].whBatchReturnApvItems[" + num + "].warehouseId' style='display:none' value='" + whApvItem.whSku.warehouseId + "'/>";
									//inputoption += "<input type='hidden' name='whBatchReturns[" + 0 + "].whBatchReturnApvItems[" + num + "].isOptimal' style='display:none' value='" + isOptimal + "'/>";
									//inputoption += "<input type='text' name='whBatchReturns["+i+"].whBatchReturnApvItems["+j+"].warehouseId' style='display:none' value='"+result.body.warehouseId+"'/>";
									inputoption += "<input type='text' name='whBatchReturns[" + 0 + "].whBatchReturnApvItems[" + num + "].road' style='display:none' value='" + aisle + "'/>";
								}
							}
							apvoption += skuoption + "</td>";
							apvoption += quantityoption + "</td>";
							apvoption += "<td onclick='onRemove(this)'><i class='icon-remove'></i>" + inputoption;
							apvoption += "</td></tr>";

							$("#tskubody").prepend(apvoption);
							var uuidList =elements[m].uuidList;

							if(uuidList!=null&&uuidList!=''){
								uuid_array = uuid_array.concat(uuidList)
							}
							debugger;
							// 添加唯一码海外退件批次日志
							addWhUniqueLog(uuidList, orderNo);
						}
						// 失败项
						if(result.message){
							audioPlay('error');
							customizeLayer(result.message);
							$("#error").text("失败");
							$("#errorMes").text(result.message);
							$('#scan_no').val("");
							$('#scan_no').focus();
							//	xnLocationNumber(result);
						}
					}else{
						audioPlay('error');
						customizeLayer("失败: " + result.message,'error');
						$("#error").text("失败");
						$("#errorMes").text(result.message);
						$('#scan_no').val("");
						$('#scan_no').focus();
						//xnLocationNumber(result);
					}
				},
				error : function() {
					audioPlay('error');
					layer.alert("扫描:["+val+"]系统异常",'error');
					$("#error").text("失败");
					$("#errorMes").text("系统异常");
					$('#scan_no').val("");
					$('#scan_no').focus();
				}
			});
			$('#scan_no').val("");
			$('#scan_no').focus();

		}

		$('#returnWarehouseId').change(function(){
			if( $('#tskubody').find('tr').length > 0){
				if(confirm("变更仓库会清空已扫描数据，是否继续?")){
					$("#tskubody tr").each(function(i){
						$(this).remove();
					});
					san_array = new Array();
					uuid_array = new Array();
					dellApvNoList_array = new Array();
					num =  - 1;
				}
			}
		});


		function addWhUniqueLog(uuidList, orderNo) {
			debugger;
			var size=0;
			if (uuidList){
				size=uuidList.length;
			}
			for (i = 0; i < size; i++) {
				$.ajax({
					type : "GET",
					url :CONTEXT_PATH+"whBatchReturn/addWhUniqueSkuLog" ,
					data : {
						uuid : uuidList[i], orderNo: orderNo, step: 17
					},
					timeout : 100000,
					beforeSend : function() {
					},
					success : function(responese) {

					},
					error : function() {
					}
				});
			}
		}

		function xnLocationNumber(result){
			if(result.exceptionCode!=null && result.exceptionCode!=""){
				$("#errorMes").text(result.exceptionCode);
				customizeLayer("失败: 已扫描:" + result.exceptionCode+"不在正常库位上,请移库后继续操作!",'error');
				if (result.message!=null && result.message!=""){
					$("#errorMes").text("失败: 已扫描:"+result.exceptionCode+"不在正常库位上,请移库后继续操作!\n"+result.message+ "找不到订单或已创建(优选仓需销退单)");
				}else{
					$("#errorMes").text("已扫描:"+result.exceptionCode+"不在正常库位上,请移库后继续操作!");
				}
			}else{
				customizeLayer("失败: " + result.message+ "找不到订单",'error');
				$("#errorMes").text(result.message+ "找不到订单或已创建(优选仓需销退单)");
			}
			audioPlay('error');
			$("#error").text("失败");
			$('#scan_no').val("");
			$('#scan_no').focus();
		}

        // 添加唯一码库内返架日志
        function addWhUniqueSkuLog(uuid, returnNo, apvNo) {
            var r = $.ajax({
                type : "get",
                url :CONTEXT_PATH+"warehouse/returns/addWhUniqueSkuLog" ,
                data : {uuid : uuid, returnNo: returnNo, step: 5, apvNo: apvNo},
                timeout : 100000,
                beforeSend : function() {
                },
                success : function(responese) {

                },
                error : function() {
                }
            });
        }

		function onRemove(obj){
			if(confirm("是否删除该条记录?")){
				$(obj).parent().remove();
				// 重新编排
				$("#tskubody tr").each(function(i){
                    refreshFirstIndex(i, this);
				});
			}
		}

        function dell(obj, apvNo){
            if(confirm("是否删除该条记录?")){
                $(obj).parent().remove();
                dellApvNoList_array.push(apvNo);
            }
        }

        //从右边开始递归获取最右边的[]之间的数字
        function recursionFirstIndexOf(value, searchPrefixValue, searchSuffixValue) {
            var prefixIndex = value.indexOf(searchPrefixValue);

            if(prefixIndex != -1) {
                var suffixIndex = value.indexOf(searchSuffixValue);

                var newValue = value.substring(prefixIndex + 1, suffixIndex);

                if(newValue != '' && !testNumber(newValue)) {
                    value = value.substring(0, prefixIndex);
                    return recursionFirstIndexOf(value, searchPrefixValue, searchSuffixValue)
                } else {
                    return prefixIndex;
                }
            }

            return prefixIndex;
        }

        //刷新index
        function refreshFirstIndex(curIndex, obj) {
            if(obj) {

                var elements = obj.elements ? obj.elements : obj.getElementsByTagName('*');
                for(var i = 0, len = elements.length; i < len; i++) {
                    var el = elements[i];

                    var n = el.name, t = el.type, tag = el.tagName.toLowerCase();

                    if ((!n || el.disabled || t == 'reset' || t == 'button' ||
                            (t == 'submit' || t == 'image')
                        )) {
                        continue;
                    }

                    var jqEl = $(el);
                    var oldName = jqEl.prop("name");
                    var prefixIndex = recursionFirstIndexOf(oldName, "[", "]");

                    if(prefixIndex != -1) {
                        var suffixIndex = oldName.indexOf("]", prefixIndex);
                        var	newName = oldName.substring(0, prefixIndex + 1) + curIndex + oldName.substring(suffixIndex);

                        jqEl.prop("name", newName);
                    }
                }
            }
        }
	</script>
</body>
</html>