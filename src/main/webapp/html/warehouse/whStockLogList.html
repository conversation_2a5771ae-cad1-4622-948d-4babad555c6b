<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
<@header method="header" active="11050000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">仓库管理</a></li>
					<li class="active">库存日志</li>
				</ul>
			</div>
		</div>

		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">

		<#assign query=domain.query>
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="col-md-12">
				<form action="${CONTEXT_PATH}whStockLog/search"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="whStockLogForm" id="domain">
					<!-- 分页信息 -->
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-1">SKU</label>
							<div class="col-md-2">
								<input class="form-control" name="query.sku" type="text" value="${query.sku}" id="sku">
							</div>
							<label class="control-label col-md-1">库存类型</label>
							<div class="col-md-2">
								<input class="form-control" name="query.type" type="text" value="${query.type}">
							</div>
							<label class="control-label col-md-1">库存ID</label>
							<div class="col-md-2">
								<input class="form-control" name="query.stockId" type="text" value="${query.stockId}">
							</div>
							<label class="control-label col-md-1">操作人</label>
							<div class="col-md-2">
								<input class="form-control" name="query.createBy" type="text" value="${query.createBy}">
							</div>

						</div>
						<div class="form-group">
							<label class="control-label col-md-1">改变值</label>
							<div class="col-md-2">
								<select name="query.change" class="form-control" value="${query.change}">
									<option value="" <#if (query.change)??>selected</#if>>所有</option>
									<option value="true" <#if query.change == true>selected</#if>>调增</option>
									<option value="false" <#if (query.change)?? && query.change == false>selected</#if>>调减</option>
								</select>
							</div>
							<label class="control-label col-md-1">单据</label>
							<div class="col-md-2">
								<input class="form-control" name="query.content" placeholder="多个逗号分割" type="text" value="${query.content}" id="content">
							</div>
							<label class="control-label col-md-1">环节</label>
							<div class="col-md-2">
								<input class="form-control" name="query.stepList" type="text" value="${query.stepList}">
							</div>

							<label class="control-label col-md-1">是否连续</label>
							<div class="col-md-2">
								<select name="query.continuity" class="form-control" value="${query.continuity}">
									<option value="" <#if (query.continuity)??>selected</#if>>所有</option>
									<option value="true" <#if query.continuity == true>selected</#if>>连续</option>
									<option value="false" <#if (query.continuity)?? && query.continuity == false>selected</#if>>不连续</option>
								</select>
							</div>
						</div>

						<div class="form-group">

							<label class="control-label col-md-1">操作时间</label>
							<div class="col-md-2">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.fromDate" value="${query.fromDate}" readonly="readonly" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.toDate" value="${query.toDate}" readonly="readonly" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>
							<label class="control-label col-md-1">库位</label>
							<div class="col-md-2">
								<input class="form-control" name="query.locationNumber" type="text" value="${query.locationNumber}">
							</div>
						</div>

					</div>
					<div>
						<br />
						<div class="col-md-offset-10" style="text-align: right">
							<button type="button" class="btn btn-default" onclick="formReset(this)">
								<i class="icon-refresh"></i> 重置
							</button>
							<button type="submit" class="btn blue" onclick="return checkParam()">
								<i class="icon-search"></i> 查询
							</button>
                            <@header method="auth" authCode="NEW_INVENTORY_LOG_DOWNLOAD">
                            <button type="button" class="btn btn-default" onclick="download()">
                                <i class="icon-download"></i> 导出
                            </button>
                            </@header>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>
		
		<div class="row">
			<div id="fixedDiv" class="col-md-12">
				<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
					<colgroup>
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="20%" />
					</colgroup>
					<thead>
					<tr>
						<th>编号</th>
						<th>SKU</th>
						<th>原有库存</th>
						<th>修改库存</th>
						<th>修改后库存</th>
						<th>操作人</th>
						<th>操作时间</th>
						<th>备注</th>
					</tr>
					</thead>
				</table>
			</div>
			<div class="col-md-12" id="task-list-warp">
				<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
					<colgroup>
						<col width="5%" />
						<col width="10%" />
						<col width="5%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="20%" />
					</colgroup>
					<thead>
						<tr>
							<th>编号</th>
							<th>SKU</th>
							<th>库存ID</th>
							<th>库位</th>
							<th>原有库存</th>
							<th>修改库存</th>
							<th>修改后库存</th>
							<th>操作人</th>
							<th>操作时间</th>
							<th>备注</th>
						</tr>
					</thead>
					<tbody>
						<#list domain.whStockLogs as whStockLog>
							<tr <#if (whStockLog.continuity)?? && whStockLog.continuity == false> style='color: red;'</#if>>
								<td>${whStockLog.tableIndex}${whStockLog.id}</td>
								<td>${whStockLog.sku}</td>
					            <td>${whStockLog.stockId}</td>
								<td>${whStockLog.locationNumber}</td>
								<td>${whStockLog.originalQuantity}</td>
								<td>${whStockLog.quantity}</td>
								<#if whStockLog.originalQuantity + whStockLog.quantity<0 && whStockLog.step == '523'>
									<td>0</td>
								<#else>
									<td>${whStockLog.originalQuantity + whStockLog.quantity}</td>
								</#if>
								<td>${util('name',whStockLog.createBy)}</td>
								<td>${whStockLog.creationDate }</td>
								<td>${whStockLog.contentDetail }</td>
							</tr>
						</#list>
					</tbody>
				</table>
			</div>
		</div>
			<div id="fixed-bottom">
				<div id="pager"></div>
			</div>
	</div>
	

	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
	<script type="text/javascript">
		// 分页
		var total = "${domain.page.totalCount}";
		var pageNo = "${domain.page.pageNo}";
		var pageSize = "${domain.page.pageSize}";
		$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

        // 类型
        var typeArray = ${domain.typeList};
		$("input[name='query.type']").select2({
            data: typeArray,
            placeholder: "库存类型",
            multiple: false,
            allowClear: true
		});

		// 步骤
        var stepArray = ${domain.stepList};
		$("input[name='query.stepList']").select2({
            data: stepArray,
            placeholder: "环节",
            multiple: true,
            allowClear: true
		});
		
		$.getJSON(CONTEXT_PATH + "system/saleusers/userByRole?roleId=", function(json){
			if (json) {
				$("input[name='query.createBy']").select2({
					data : json,
					placeholder : "操作人",
					allowClear : true
				});
			} else {
				$("input[name='query.createBy']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
			}
		});

		function checkParam() {
		    var sku = $("input[name='query.sku']").val();
		    var type = $("input[name='query.type']").val();
			var stockId = $("input[name='query.stockId']").val();
			if (!sku){
				layer.alert("请输入SKU", "error");
				return false;
            }
            if (sku.indexOf("=") != -1){
                sku = sku.split("=")[0];
                $("input[name='query.sku']").val(sku);
            }
			if (!type){
                layer.alert("请输入库存类型", "error");
                return false;
			}
			return true;
        }

        function download() {
            var diglog = dialog({
                title: '导出库存',
                width: 800,
                height:320,
                url: CONTEXT_PATH + "whStockLog/downloadmode?type=2",
                okValue: '确定',
                ok: function () {
                    var exportWindow = $(this.iframeNode.contentWindow.document.body);

                    var submitForm = exportWindow.find("#submit-form");

                    var exportType = submitForm.find("input[name='exportType']:checked").val();

                    var headers =  submitForm.find("input[name='selectHeaders']:checked");

                    var submitFormParam = $('#domain').serialize();
                    // 导出当前选择
                    if(exportType == 3) {
						layer.alert("不支持选择导出数据");
						return false;
                    }

                    if(headers.length == 0) {
                        layer.alert("请选择要导出的表头字段");
                        return false;
                    }
                    var headersStr = headersJoin(headers);
                    submitFormParam = submitFormParam +"&selectHeaders="+headersStr+"&exportType="+exportType;
                    //还原分页
                    $("#page-no").val("${domain.page.pageNo}");
                    downloadByPostForm(submitFormParam, CONTEXT_PATH + "whStockLog/download?");
                    $("#page-no").val("1");

                    setTimeout(function () {
                        diglog.close().remove();
                    }, 100);

                    return true;
                },
                cancelValue: '取消',
                cancel: function () {}
            });
            diglog.show();
        }

        function headersJoin(headers) {
            var checkHeaders = "";
            for (var i = 0; i < headers.length; i++) {
                var header = headers[i].value;
                if (i == 0) {
                    checkHeaders += header;
                } else {
                    checkHeaders += "," + header;
                }
            }
            return checkHeaders;
        }
	</script>
</body>
</html>