<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<#include "/common/include.html">
		<style type="text/css">
			.modal-body input {
				width: 240px;
			}

			.return-model {
				padding: 12px;
			}

			#return-info-btn {
				color: #ffff;
				background-color: #5bc0de;
				padding: 3px;
			}
		</style>
	</head>

	<body>
		<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>

		<div id="page" style="background-color: rgb(231, 237, 248)">
			<div class="row">
				<div class="col-md-12" style="padding: 0">
					<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
						<li>
							<a href="#">盘点</a>
						</li>
						<li class="active">盘点需求</li>
					</ul>
				</div>
			</div>

			<!-- 内容 -->
			<div class="container-fluid" style="background-color: white;border: none">
				<div class="row">
					<div class="col-md-12">
						<#assign query = domain.query>
						<form action="${CONTEXT_PATH}warehouse/pickInventoryDemands/search" class="form-horizontal form-bordered form-row-stripped" method="post" modelAttribute="domain" name="pickInventoryDemandForm" id="domain">
							<!-- 分页信息 -->
							<input id="page-no" type="hidden" name="page.pageNo" value="1">
							<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">

							<div class="form-body">
								<div class="form-group">
									<label class="control-label col-md-1">SKU</label>
									<div class="col-md-3">
										<input class="form-control" id="" name="query.sku" type="text" placeholder="" value="${query.sku}">
									</div>

									<label class="control-label col-md-1">需求状态</label>
									<div class="col-md-3">
										<input class="form-control" name="query.statusStr" type="text" value="${query.statusStr}">
									</div>

									<label class="control-label col-md-1">拣货任务号</label>
									<div class="col-md-3">
										<input class="form-control" name="query.pickTaskNo" type="text" placeholder="" value="${query.pickTaskNo}">
									</div>
								</div>

								<div class="form-group">
									<label class="control-label col-md-1">拣货人</label>
									<div class="col-md-3">
										<input class="form-control" name="query.creationUser" type="text" value="${query.creationUser}">
									</div>

									<label class="control-label col-md-1">拣货时间</label>
									<div class="col-md-3">
										<div class="input-group">
											<input class="form-control Wdate" type="text" name="query.fromCreationDate" placeholder="" readonly="readonly" value="${query.fromCreationDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
											<span class="input-group-addon">到</span>
											<input class="form-control Wdate" type="text" name="query.endCreationDate" placeholder="" readonly="readonly" value="${query.endCreationDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
										</div>
									</div>

									<label class="control-label col-md-1">盘点任务号</label>
									<div class="col-md-3">
										<input class="form-control" name="query.inventoryTaskNo" type="text" value="${query.inventoryTaskNo}">
									</div>
								</div>

								<div class="form-group">
									<label class="control-label col-md-1">拣货任务类型</label>
									<div class="col-md-3">
										<input class="form-control" name="query.pickTaskTypeStr" type="text" value="${query.pickTaskTypeStr}">
									</div>

									<label class="control-label col-md-1">需求类型</label>
									<div class="col-md-3">
										<input class="form-control" name="query.demandTypeStr" type="text" value="${query.demandTypeStr}">
									</div>

									<label class="control-label col-md-1">盘点过程</label>
									<div class="col-md-3">
										<input class="form-control" name="query.taskLevelStr" type="text" value="${query.taskLevelStr}">
									</div>

								</div>

								<div class="form-group">
									<label class="control-label col-md-1">排序</label>
									<div class="col-md-3">
										<select name="query.orderBy" class="form-control">
											<#if query.orderBy == 'LOCATION_ASC'>
												<option selected="selected" value="LOCATION_ASC">按库位正序</option>
												<option value="">按时间倒序</option>
												<#else>
													<option value="LOCATION_ASC">按库位正序</option>
													<option selected="selected" value="">按时间倒序</option>
											</#if>
										</select>
									</div>
									<label class="control-label col-md-1">库存ID</label>
									<div class="col-md-3">
										<input class="form-control" name="query.stockIdStr" placeholder="请输入库存ID，多个用英文逗号分割" type="text" value="${query.stockIdStr}">
									</div>
								</div>

							</div>
							<div>
								<div class="pull-left" style="margin-bottom: 10px;">
                                    <@header method="auth" authCode="CHECK_GENERATE_CHECK_ORDER">
									<button type="button" class="btn btn-default" onclick="generateTask('false')">
										<i class="icon-add"></i>生成盘点任务
									</button>
                                    </@header>
                                    <@header method="auth" authCode="CHECK_DISCARD_CHECK_NEED">
									<button type="button" class="btn btn-default" onclick="batchDiscard()">
										<i class="icon-remove"></i>废弃盘点需求
									</button>
                                    </@header>
								</div>
								<div class="col-md-offset-8" style="text-align: right">
									<button type="button" class="btn btn-default" onclick="formReset(this)">
										<i class="icon-refresh"></i>重置
									</button>
									<button type="submit" class="btn blue">
										<i class="icon-search"></i>查询
									</button>
                                    <@header method="auth" authCode="CHECK_NEED_DOWNLOAD">
									<button type="button" class="btn btn-default" onclick="download()">
										<i class="icon-download"></i>导出
									</button>
                                    </@header>
								</div>
							</div>
						</form>
					</div>
					<br/>
				</div>

				<div class="row">
					<div class="col-md-12">
						<!-- 内容 -->
						<table class="table table-bordered table-hover table-condensed" id="pickInventoryDemand-list">
							<colgroup>
								<col width="5%" />
								<col width="7%" />
								<col width="5%" />
								<col width="5%" />
								<col width="8%" />
								<col width="6%" />
								<col width="4%" />
								<col width="6%" />
								<col width="3%" />
								<col width="3%" />
								<col width="3%" />
								<col width="4%" />
								<col width="7%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="4%" />
								<col width="8%" />
								<col width="6%" />
							</colgroup>
							<thead>
								<tr>
									<th><input type="checkbox" id="check-all" name="checkAll">全选</th>
									<th>拣货任务号</th>
										<th>需求类型</th>
											<th>拣货任务类型</th>
												<th>创建人/创建时间</th>
													<th>SKU</th>
													<th>库存ID</th>
													<th>库位</th>
														<th>需拣<br/>数量</th>
															<th>已拣<br/>数量</th>
																<th>拣货<br/>差异</th>
																	<th>拣货完成<br/>货位库存</th>
																		<th>盘点任务号</th>
																			<th>初盘/差异</th>
																				<th>复盘/差异</th>
																					<th>终盘/差异</th>
																						<th>确认数量</th>
																							<th>盘点人/盘点时间</th>
																								<th>状态</th>
								</tr>
							</thead>
							<tbody>
									<#list domain.whPickInventoryDemands as demand>
										<td>
											<input type="checkbox" class="ids" value="${demand.id}" name="ids"> ${demand.id}
										</td>
										<td>${demand.pickTaskNo}</td>
										<td>${demand.demandTypeName}</td>
										<td>${demand.pickTaskTypeName}</td>
										<td>
											${util('name',demand.creationUser)}
											<br />
											${demand.creationDate}
										</td>
										<td>${demand.sku}</td>
										<td>${demand.stockId}</td>
										<td>${demand.locationNumber}</td>
										<td>${demand.needQuantity}</td>
										<td>${demand.pickQuantity}</td>
										<td>${demand.diffQuantity}</td>
										<td>${demand.quantity}</td>
										<td>${demand.inventoryTaskNo}</td>
										<td>
											<#if demand.lastTaskItem?? >
												${demand.lastTaskItem.inventoryQuantity}/${demand.lastTaskItem.diffQuantity}
											</#if>
										</td>
										<td>
											<#if demand.lastTaskItem?? >
												${demand.lastTaskItem.repeatQuantity}/${demand.lastTaskItem.repeatDiffQuantity}
											</#if>
										</td>
										<td>
											<#if demand.lastTaskItem?? >
												${demand.lastTaskItem.finallyQuantity}/${demand.lastTaskItem.finallyDiffQuantity}
											</#if>
										</td>
										<td>
											<#if demand.lastTaskItem?? >
												${demand.lastTaskItem.confirmQuantity}
											</#if>
										</td>
										<td>
											<#if demand.lastTaskItem?? >
												${util('name',demand.lastTaskItem.inventoryUser)}
												<br />
												${demand.lastTaskItem.inventoryDate}
											</#if>
										</td>
										<td>
											${demand.statusName }
											<input id="${demand.id}_status" type="hidden" name="" value="${demand.status}"/>
											<button type="button" class="btn btn-info btn-xs" onclick="viewLog(${demand.id}, 'whPickInventoryDemand')" style="margin: 5px">日志</button>
										</td>
									</tr>
								</#list>
							</tbody>
						</table>
						<!-- 内容end -->
					</div>
				</div>
				<div id="fixed-bottom">
					<div id="pager"></div>
				</div>
			</div>
			<#include "/common/footer.html">
		</div>
		<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
		<script type="text/javascript">
			// 分页
			var total = "${domain.page.totalCount}";
			var pageNo = "${domain.page.pageNo}";
			var pageSize = "${domain.page.pageSize}";
			$("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });

			var heights = $("body").height();
			if(heights > 910) {
				$("#fixed-bottom").addClass("fixed-bottom-height")
			}
			// 状态
			var statuses =  ${domain.statuses};
			$("input[name='query.statusStr']").select2({
				data : statuses,
				placeholder : "状态",
				multiple: true,
				allowClear : true
			});
			// 盘点过程
			var taskLevels =  ${domain.taskLevels};
			$("input[name='query.taskLevelStr']").select2({
				data : taskLevels,
				placeholder : "盘点过程",
				multiple: true,
				allowClear : true
			});

			// 拣货任务类型
            var taskTypeArray = ${domain.taskTypes};
            $("input[name='query.pickTaskTypeStr']").select2({
                data : taskTypeArray,
                placeholder : "拣货任务类型",
				multiple: true,
                allowClear : true
            });

            // 需求类型
            var demandTypeArray = ${domain.demandTypes};
            $("input[name='query.demandTypeStr']").select2({
                data : demandTypeArray,
                placeholder : "需求类型",
				multiple: true,
                allowClear : true
            });
			// 拣货人 所有有PDA任意拣货任务的权限人员，或者库存查询页面有生成盘点需求的人员
			$.getJSON(CONTEXT_PATH + "system/saleusers/userByPermissionCode?permissionCode=040101,040102,040103," +
                "040104,040105,040106,040107,040108,040109,040110,040111,040112,040113,040114,040115,040200," +
                "040300,040400,040500,OTHER_SKU_GENERATE_CHECK_NEED", function(json) {
				if(json) {
					$("input[name='query.creationUser']").select2({
						data: json,
						placeholder: "拣货员",
						allowClear: true
					});
				} else {
					$("input[name='query.creationUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
				}
			});

			// 全选
			var checkAll = $("input[name='checkAll']");

			// 子选项
			var itemIds = $("input[name='ids']");
			checkAll.change(
				function() {
					itemIds.prop("checked", $(this).prop("checked"));
					itemIds.each(function() {
						var f = $(this).is(":checked");
						var checkClass = $(this).prop("class");
						$("." + checkClass).each(function() {
							$(this).prop("checked", f);
						})
					})
				}
			);
			// 获取选中的数据
			function getCheckedIds() {
				var checkedIds = $("input[name='ids']:checked");
				return checkedIds;
			}

			// 生成盘点任务弹窗提示
			function generateTask(checked){
                layer.open({
                    type: 2,
                    title: '盘点需求',
                    area:['500px','360px'],
                    shade:0.5,
                    content: CONTEXT_PATH +"warehouse/pickInventoryDemands/pickInventoryGenerateDemandEditInit",
                    btn: ['确认', '取消'],
                    btn1: function(index, layero) {
                        var iframeWin = layero.find('iframe')[0].contentWindow;
                        var locationRegions = iframeWin.document.getElementById('locationRegion').value;
                        var maxSkuNumber = iframeWin.document.getElementById('maxSkuNumber').value;
						var	inventoryNumber=iframeWin.document.getElementById('inventoryNumber').value;
                        batchGenerateTask(checked,locationRegions,maxSkuNumber,inventoryNumber);
                        layer.close(index);
                    },
                    btn2:function(index, layero) {
                    }
                });
            }

			// 生成盘点任务
			function batchGenerateTask(checked,locationRegions,maxSkuNumber,inventoryNumber){
                var checkedDatas = getCheckedIds();
                if(checkedDatas.length == 0 && locationRegions == "" && (maxSkuNumber == "" || maxSkuNumber <= 0)) {
                    layer.alert("请选择要操作的ID或输入要操作数据的相关条件", "error");
                    return;
                }
                var ids = "";
                for(var i = 0; i < checkedDatas.length; i++) {
                    var check = checkedDatas[i];
                    var id = $(check).val();
                    var status = $("#"+id+"_status").val();
                    if (status == '20') {
                        layer.alert("勾选的盘点需求已有废弃，请重新选择", "error");
                        return;
                    }
                    if (status == '7') {
                        layer.alert("勾选的盘点需求已盘点完成，请重新选择", "error");
                        return;
                    }
                    if (status != '0') {
                        layer.alert("只能选择待生成的盘点需求，请重新选择", "error");
                        return;
                    }
                    ids += id;
                    if(i != checkedDatas.length - 1) {
                        ids += ",";
                    }
                }
                $.ajax({
                    url:CONTEXT_PATH +"warehouse/pickInventoryDemands/batchGenerateTask",
                    type:"POST",
                    data:{"ids":ids, "checked":checked,"locationRegions":locationRegions,"maxSkuNumber":maxSkuNumber,"inventoryNumber":inventoryNumber},
                    success : function(response){
                        var message = '';
                        if (response.message != null) {
                            message = response.message
                        }
                        if (response.status == '500') {
                            if (response.exceptionCode =='check') {
                                var diglog = dialog({
                                    title: '请注意！',
                                    content: 'SKU：'+message,
                                    onshow: function() {},
                                    okValue: '继续提交',
                                    ok: function() {
                                        batchGenerateTask('true',locationRegions,maxSkuNumber,inventoryNumber);
                                        setTimeout(function() {
                                            diglog.close().remove();
                                        }, 100);
                                        return false;
                                    },
                                    cancelValue: '重新勾选',
                                    cancel: function() {}
                                }).showModal();
                            }else {
                                customizeLayer('生成盘点任务失败：'+message, 'error');
                                return;
                            }
                        } else if (response.status == '200') {
                            layer.confirm('已经成功生成盘点任务：'+message,{
                                icon: 1,
                                btn: ['确定']
                            },function () {
                                window.location.reload();
                            })
                        }
                    },
                     error:function(){
                         layer.alert('操作失败!', 'error');
                     }
                });
			}

			// 废弃盘点需求
			function batchDiscard(){
				var checkedDatas = getCheckedIds();
				if(checkedDatas.length == 0) {
					layer.alert("请先勾选需要废弃的盘点需求", "error");
					return;
				} else {
					var ids = "";
					for(var i = 0; i < checkedDatas.length; i++) {
						var check = checkedDatas[i];
						var id = $(check).val();
						var status = $("#"+id+"_status").val();
						if (status == '20') {
							layer.alert("勾选的盘点需求已有废弃，请重新选择", "error");
							return;
						}
						if (status == '1' || status == '3' || status == '5') {
							layer.alert("勾选的盘点需求已有盘点任务，请重新选择或去盘点任务中废弃", "error");
							return;
						}
						if (status == '7') {
							layer.alert("勾选的盘点需求已盘点完成，请重新选择", "error");
							return;
						}
						ids += id;
						if(i != checkedDatas.length - 1) {
							ids += ",";
						}
					}
					var diglog = dialog({
						title: '',
						content: '确定废弃盘点需求？',
						onshow: function() {},
						okValue: '确定',
						ok: function() {
							$.ajax({
								url:CONTEXT_PATH +"warehouse/pickInventoryDemands/batchDiscard",
								type:"POST",
								data:{
									"ids":ids
								},
								success : function(response){
									var message = '';
									if (response.message != null) {
										message = response.message
									}
									if (response.status == '500') {
										layer.alert('废弃盘点需求失败：'+message, 'error');
										return;
									} else if (response.status == '200') {
										alert('已经成功废弃盘点需求：'+message);
										layer.confirm('已经成功废弃盘点需求：'+message,{
											icon: 1,
											btn: ['确定']
										},function () {
											window.location.reload();
										})
									}
								},
					             error:function(){
					                 layer.alert('操作失败!', 'error');
					             }
							});
							setTimeout(function() {
								diglog.close().remove();
							}, 100);
							return false;
						},
						cancelValue: '取消操作',
						cancel: function() {}
					}).showModal();
				}
			}

			// 导出
			function download() {
				if(getCheckedIds().length == 0) {
					var param = $("#domain").serialize();
					window.open(CONTEXT_PATH + "warehouse/pickInventoryDemands/download?" + param);
				} else {
					downloadByPost();
				}
			}

			// 超过500条不能用GET请求
			function downloadByPost() {
				var checkedDatas = getCheckedIds();
				var ids = "";
				for(var i = 0; i < checkedDatas.length; i++) {
					var check = checkedDatas[i];
					var id = $(check).val();
					ids += id;
					if(i != checkedDatas.length - 1) {
						ids += ",";
					}
				}
				var url = CONTEXT_PATH + "warehouse/pickInventoryDemands/download";
				var tempForm = document.createElement("form");
				tempForm.id = "tempForm";
				tempForm.method = "post";
				tempForm.action = url;
				tempForm.target = "blank";
				var hideInput = document.createElement("input");
				hideInput.type = "hidden";
				hideInput.name = "ids";
				hideInput.value = ids;
				tempForm.appendChild(hideInput);
				if(tempForm.attachEvent) { // IE
					tempForm.attachEvent("onsubmit", function() { window.open('about:blank', 'blank'); });
				} else if(tempForm.addEventListener) { // DOM Level 2 standard
					tempForm.addEventListener("onsubmit", function() { window.open('about:blank', 'blank'); });
				}
				document.body.appendChild(tempForm);
				if(document.createEvent) { // DOM Level 2 standard
					evt = document.createEvent("MouseEvents");
					evt.initMouseEvent("submit", true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
					tempForm.dispatchEvent(evt);
				} else if(tempForm.fireEvent) { // IE
					tempForm.fireEvent('onsubmit');
				}
				//必须手动的触发
				tempForm.submit();
				document.body.removeChild(tempForm);
			}
		</script>
	</body>

</html>