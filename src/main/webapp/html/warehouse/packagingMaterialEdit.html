<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
    <title>ERP</title>

    <style type="text/css">
        .fileinput-button{
            position: relative;
            display: inline-block;
            overflow: hidden;
        }

        .fileinput-button input{
            position: absolute;
            left: 0;
            top: 0;
            opacity: 0;
            -ms-filter: 'alpha(opacity=0)';
        }

        .box{
            width:60px;
            height:60px;
            position:relative;
            background: white;
            overflow: hidden;
            float: left;
            margin: 5px;
        }
        .box .box-con{
            display: none;
            width:30px;
            height:30px;
            position: absolute;
            background: red;
            top:-15px;
            right:-15px;
            transform: rotate(45deg);
        }
        .box:hover .box-con{
            display: block;
        }
        .box .box-con span{
            position: absolute;
            bottom:-3px;
            display: block;
            width:30px;
            text-align: center;
            transform: rotate(-45deg);
        }
    </style>

</head>
<!-- END HEAD -->
<!-- BEGIN BODY -->
<body>
<!-- BEGIN CONTAINER -->
<div class="container-fluid">

    <div class="row">
        <div class="col-md-12">

            <form class="form-horizontal form-row-stripped" id="submit-form" name="submitForm" method="post">
                <input name="whPackagingMaterialManagement.id" type="hidden" value="${domain.whPackagingMaterialManagement.id}">
                <div class="form-group">
                    <label class="control-label col-md-2"><span class="required">*</span>耗材类型:</label>
                    <input style="width: 150px;" id="materialType" name="whPackagingMaterialManagement.type" type="text" value="${domain.whPackagingMaterialManagement.type}">
                </div>
                <div class="form-group">
                    <label class="control-label col-md-2"><span class="required">*</span>耗材货号:</label>
                    <input id="materialArticleNumber" name="whPackagingMaterialManagement.materialArticleNumber" type="text" value="${domain.whPackagingMaterialManagement.materialArticleNumber}">
                </div>
                <div class="form-group">
                    <label class="control-label col-md-2"><span class="required">*</span>耗材名称:</label>
                    <input id="name" name="whPackagingMaterialManagement.name" type="text" value="${domain.whPackagingMaterialManagement.name}">
                </div>
                <div class="form-group">
                    <label class="control-label col-md-2"><span class="required">*</span>耗材单位:</label>
                    <input id="unit" name="whPackagingMaterialManagement.unit" type="text" value="${domain.whPackagingMaterialManagement.unit}">
                </div>
                <div class="form-group">
                    <label class="control-label col-md-2"><span class="required">&nbsp;&nbsp</span>使用环节:</label>

                    <#if (domain.whPackagingMaterialManagement.useLink)?? && (domain.whPackagingMaterialManagement.useLink)?contains("入库")>
                         <input id="useLink1" type="checkbox"  value="入库" checked="checked">
                    <#else>
                         <input id="useLink1" type="checkbox"   value="">
                    </#if>
                    入库
                    <#if domain.whPackagingMaterialManagement.useLink?? && domain.whPackagingMaterialManagement.useLink?contains("出库")>
                        <input id="useLink2" type="checkbox"  value="出库" checked="checked">
                    <#else>
                        <input id="useLink2" type="checkbox"  value="">
                    </#if>
                    出库
                     <input id="useLink" type="hidden"  name="whPackagingMaterialManagement.useLink"  >
                </div>

                <div class="form-group">
                    <label class="control-label col-md-2"><span class="required">*</span>耗材箱规:</label>
                    <input id="boxSpecification" name="whPackagingMaterialManagement.boxSpecification" type="text" value="${domain.whPackagingMaterialManagement.boxSpecification}">
                </div>
                <div class="form-group">
                    <label class="control-label col-md-2"><span class="required">*</span>水位库存:</label>
                    <input id="stockThreshold" name="whPackagingMaterialManagement.stockThreshold" type="text" value="${domain.whPackagingMaterialManagement.stockThreshold}">
                </div>
                <div class="form-group">
                    <label class="control-label col-md-2"><span class="required">*</span>耗材重量:</label>
                    <input  id="weight" name="whPackagingMaterialManagement.weight" type="text" value="${domain.whPackagingMaterialManagement.weight}">
                </div>
                <div class="form-group">
                    <label class="control-label col-md-2"><span class="required">*</span>耗材尺寸:</label>
                    <input  style="width: 20%;margin-right: 20px;" id="length" name="whPackagingMaterialManagement.length" type="text" value="${domain.whPackagingMaterialManagement.length}">
                    <input  style="width: 20%;margin-right: 20px;" id="width" name="whPackagingMaterialManagement.width" type="text" value="${domain.whPackagingMaterialManagement.width}">
                    <input  style="width: 20%;" id="height" name="whPackagingMaterialManagement.height" type="text" value="${domain.whPackagingMaterialManagement.height}">
                </div>
                <div class="form-group">
                    <label class="control-label col-md-2">销售使用重量:</label>
                    <input  id="saleWeight" name="whPackagingMaterialManagement.saleWeight" type="text" value="${domain.whPackagingMaterialManagement.saleWeight}">
                </div>
                <div class="form-group">
                    <label class="control-label col-md-2">备注:</label>
                    <textarea class="form-control input-xlarge"  name="whPackagingMaterialManagement.remark" rows="4" style="width: 200px;margin-left: 50px;margin-top: -20px" id="remark"></textarea>
                </div>
                <div class="form-group">
                    <label class="control-label col-md-2"><span class="required">*</span>耗材状态:</label>
                    <input style="width: 150px;" id="materialStatus" name="whPackagingMaterialManagement.status" type="text" value="${domain.whPackagingMaterialManagement.status}">
                </div>
                <div class="form-group">
                    <label class="control-label col-md-2">耗材图片:</label>
                    <div style="width: 75%;margin-left: 23%">
                        <input class="form-control" type="hidden" id="imageUrl" name="whPackagingMaterialManagement.imageUrl" type="text" value="${domain.whPackagingMaterialManagement.imageUrl}">
                        <div class="img-container">
                            <#if domain.whPackagingMaterialManagement.images??>
                                <input id="existImages" name="existImages" value="${domain.whPackagingMaterialManagement.images?join(',')}" type="hidden">
                                <#list domain.whPackagingMaterialManagement.images as materialImg>
                                    <div class="box">
                                        <div>
                                            <img id="display-image-${materialImg_index}" class="img-list" width="60px" height="60px" src="${materialImg}" alt="" />
                                        </div>
                                        <div class="box-con">
                                            <span onclick="deleteImg('${materialImg}',this)">×</span>
                                        </div>
                                    </div>
                                </#list>
                            </#if>
                        </div>
                        <label class="btn btn-info fileinput-button">
                            <i class="icon-plus"></i>
                            <span>上传图片</span>
                            <input name="images" type="file" multiple="multiple" onchange="fileChange(this);">
                        </label>

                    </div>

                </div>
            </form>
        </div>
        <!-- end col -->
    </div>
    <!-- end row -->
</div>
<!-- END CONTAINER -->
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript">
    var materialTypes = ${domain.materialTypes};
    $("#remark").val("${domain.whPackagingMaterialManagement.remark}");
    $("input[name='whPackagingMaterialManagement.type']").select2({
        data : materialTypes,
        placeholder : "耗材类型",
        allowClear : true
    });
    var materialStatus = ${domain.materialStatus};
    $("input[name='whPackagingMaterialManagement.status']").select2({
        data : materialStatus,
        placeholder : "耗材状态",
        allowClear : true
    });

    var  img = $("#existImages").val();
    var images = null;
    var currentImageIndex = 0;
    if(img!=null && img !=''){
        images = img.split(",");
    }

    function fileChange(target) {
        var materialArticleNumber = $('#materialArticleNumber').val();
        if(!materialArticleNumber){
            layer.alert('请先输入货号!','error');
            return false;
        }
        var filename = target.value;
        if (filename == '') {
            layer.alert("请选择需要上传的文件!");
            return;
        } else {
            var ext, idx;
            idx = filename.lastIndexOf(".");
            if (idx != -1) {
                ext = filename.substr(idx + 1).toLowerCase();
                if (ext != 'jpg' && ext != 'jpeg' && ext != 'png' && ext !='gif') {
                    layer.alert("不支持" + ext + "文件上传!");
                    var file = $(target);
                    file.after(file.clone().val(""));
                    file.remove();
                    return;
                }
            } else {
                layer.alert("只能上传图片文件!");
                var file = $(target)
                file.after(file.clone().val(""));
                file.remove();
                return;
            }
        }
        var files = target.files;
        if(files.length>5){
            layer.alert("上传图片不能超过五张！",'error');
            return;
        }else{
            if (images != null && images.length + files.length>5) {
                layer.alert("最多只能传五张照片!", 'error');
                return;
            }
        }
        var r = confirm("确定上传?");
        if(!r) {
            return;
        }
        uploadExceptionImage(files.length);
    }

    function uploadExceptionImage(count){
        var uploadUrl = CONTEXT_PATH + "packagingMaterialManagement/uploadImage";
        var createUrl = $("#submit-form").attr("action");
        $("#submit-form").attr("action", uploadUrl);
        $("#submit-form").ajaxSubmit(function(data) {
            if (data.status == 200) {
                if (images != null && images.length > 0) {
                    images = JSON.parse(data.body.newImgs);
                    currentImageIndex = images.length - count;
                }else {
                    images = JSON.parse(data.body.newImgs);
                    currentImageIndex = 0;
                }
                layer.alert("图片上传成功!");
                $('#imageUrl').val(data.body.existImg);
                $('.img-container').children().remove();
                var html = '<input id="existImages" name="existImages" value="'+images.toString()+'" type="hidden">';
                $.each(images,function(i,value){
                    html += '<div class="box"><div><img id="display-image-'+i+'" class="img-list" width="60px" height="60px" src="'+value+'" alt="" /></div>' +
                            '<div class="box-con"><span onclick="deleteImg('+"'"+value+"'"+',this)">×</span></div></div>';
                });
                $('.img-container').append(html);
            }else {
                customizeLayer("图片上传失败: "+data.message, 'error');
            }
        });
        $("#submit-form").attr("action", createUrl);
    }

    function deleteImg(imgUrl,obj) {
        if(confirm("确认删除该图片吗?")){
            var existImgStr = $('#imageUrl').val();
            if(existImgStr){
                var imgStrArray = existImgStr.split(",");
                $.each(imgStrArray,function(i,value){
                    if(!(imgUrl.indexOf(value) == -1)){
                        imgStrArray.splice(i,1);
                    }
                });
                $(obj).closest('.box').remove();
                $('#imageUrl').val(imgStrArray.toString());
                if(images){
                    $.each(images,function (i,value) {
                        if(imgUrl == value){
                            images.splice(i,1);
                        }
                    });
                    $('#existImages').val(images.toString());
                }
            }
        }
    }
</script>

<!-- END JAVASCRIPTS -->
</body>
<!-- END BODY -->
</html>