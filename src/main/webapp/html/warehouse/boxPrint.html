<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<div class="modal-dialog enterprise-modal">
	<div class="modal-content" style="top: 100px">
		<div class="modal-header">
			<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
			<h4 class="modal-title" id="expert-modal-label">新增</h4>
		</div>
		<div class="modal-body form-horizontal portlet">
			<div class="printbtn">
			    <button onclick="myPreview();">打印预览</button> &nbsp;
			    <button onclick="myPrint();">打印</button>  &nbsp;
				<button onclick="myPrintDesign();">打印设计</button>  &nbsp;
			</div>
			
			<div id="print_content">
				<#list domain.whBoxs as whBox>
					<!-- 100*100 -->
					<div style="width: 95mm; height: 95mm; font-weight: 300; text-align: center;">
						<div style="width: 95mm; height: 5mm; text-align: center;">
						</div>       
						<div style="width: 95mm; height: 25mm; text-align: center;">
							<div style="width: 95mm; height: 15mm; text-align:center;">
								<img  src="${CONTEXT_PATH}servlet/qrcode?keycode=${whBox.boxNo}&size=110">
							</div>
						</div>
						<div style="width: 95mm; height: 3mm; text-align: center;">
						</div>
						<div style="width: 95mm; text-align: center;">
							<div style="text-align:center; height: 10mm; width: 95mm;margin-top: 2mm;font-size: 25px;">
								${whBox.boxNo}
								<input type="hidden" name="query.whBoxIds" value="${whBox.id}" />
							</div>
						</div>
						<#if domain.title??>
							<div style="margin-top:3mm; font-size: 36px;font-weight: bold; text-align: center;">
								${domain.title}
							</div>
						</#if>
					</div>
					
					<p style="page-break-after: always; margin: 0; padding: 0; line-height: 1px; font-size: 1px;clear: both;">&nbsp;</p>
				</#list>
			</div>
		
		
			<object width="0" height="0" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" id="LODOP_OB"> 
			    <embed width="0" height="0" type="application/x-print-lodop" id="LODOP_EM">
			</object>
			<!--     打印插件 -->
			
		</div>
	</div>
</div>
	
<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>

<script language="javascript">
	var LODOP; //声明为全局变量 
	function CheckIsInstall() {
		try {
			var LODOP = getLodop(document.getElementById('LODOP_OB'),
					document.getElementById('LODOP_EM'));
			if ((LODOP != null) && (typeof (LODOP.VERSION) != "undefined"))
				return LODOP.VERSION;
		} catch (err) {
			//layer.alert("Error:本机未安装或需要升级!");
		}
		return false;
	}
	function myPrint() {
		CreatePrintPage();
		LODOP.PRINT();
		var data = "?" + $("input[name='query.whBoxIds']").serialize();
		/* layer.alert(data); */
		$.getJSON(CONTEXT_PATH + "warehouse/boxs/success" + data);
	};
	function myPreview() {
		CreatePrintPage();
		LODOP.PREVIEW();
	};
	function myPrintDesign() {
		CreatePrintPage();
		LODOP.PRINT_DESIGN();
	};
	function CreatePrintPage() {
		LODOP = getLodop(document.getElementById('LODOP_OB'), document
				.getElementById('LODOP_EM'));
		LODOP.PRINT_INIT("打印");

		try {
			if (typeof (eval(CreatePrintPageWithImage)) == 'function') {
				return CreatePrintPageWithImage();
			}
		} catch (e) {
		}
	    
		LODOP.ADD_PRINT_HTM(0, 0, "100mm", "100mm", document
				.getElementById('print_content').innerHTML);
	};
	//表格打印采购单详情处使用
	function myPrinttable() {
		CreatePrintpagetable();
		LODOP.PRINT();
	};
	function myPreviewtable() {
		CreatePrintpagetable();
		LODOP.PREVIEW();
	};
	function CreatePrintpagetable() {
		LODOP = getLodop(document.getElementById('LODOP_OB'), document
				.getElementById('LODOP_EM'));
		LODOP.PRINT_INIT("打印");

		try {
			if (typeof (eval(CreatePrintPageWithImage)) == 'function') {
				return CreatePrintPageWithImage();
			}
		} catch (e) {
		}
		LODOP.ADD_PRINT_HTM(0, 0, "210mm", "297mm", document
				.getElementById('print_content').innerHTML);
		LODOP.SET_PRINT_PAGESIZE('2', "", "", '')
	}
</script>