<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}

		#task-list td {
			vertical-align: middle;
		}

		.group-inline {
			color: red;
		}


	</style>
</head>
<body>
<@header method="header" active="13070000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li>
					<a href="#">仓库报表</a>
				</li>
				<li class="active">出入库明细每月批次汇总</li>
			</ul>
		</div>
	</div>

	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
		<!-- BEGIN PAGE HEADER-->
		<#assign query=domain.query>
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="col-md-12">
				<form action="${CONTEXT_PATH}batchMonthCount/search"
					  class="form-horizontal form-bordered form-row-stripped"
					  method="post" modelAttribute="domain" name="whBatchMonthCountForm" id="domain">
					<!-- 分页信息 -->
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-1">查询日期</label>
							<div class="col-md-2 input-group type-label">
								<input class="form-control Wdate" type="text" name="query.queryTime" placeholder="" readonly="readonly" value="${query.queryTime }" onfocus="WdatePicker({startDate:'%y-%M',dateFmt:'yyyy-MM',alwaysUseStartDate:true})">
							</div>
							<label class="control-label col-md-1">仓库</label>
							<div class="col-md-2">
								<input class="form-control" name="query.storeCode" type="text" value="${query.storeCode}" placeholder="多个查询请以英文逗号分开">
							</div>
							<label class="control-label col-md-1" >SKU</label>
							<div class="col-md-2">
								<input class="form-control" type="text" name="query.sku" placeholder="多个查询请以英文逗号分开" value="${query.sku }">
							</div>
							<label class="control-label col-md-1">是否统计汇总</label>
							<div class="col-md-1">
								<select class="form-control" name="query.statisticsTotal" value="${query.statisticsTotal}">
									<option <#if query.statisticsTotal == false>selected</#if> value="false">否</option>
									<option <#if query.statisticsTotal == true>selected</#if> value="true">是</option>
								</select>
							</div>
						</div>
					</div>
					<div>
						<div class="col-md-offset-12" style="text-align: right">
							<button type="button" class="btn btn-default" onclick="formReset(this)">
								<i class="icon-refresh"></i> 重置
							</button>
							<button type="submit" class="btn blue">
								<i class="icon-search"></i> 查询
							</button>
                            <@header method="auth" authCode="CHECKIN_OR_CHECKOUT_DETAIL_SUMMARY_PER_MONTH_DOWNLOAD">
							<button type="button" class="btn btn-default" onclick="download()">
								<i class="icon-download"></i> 导出
							</button>
                            </@header>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>

		<div class="row">
			<div class="col-md-12" id="task-list-warp">
				<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
					<thead>
					<tr>
						<th><label class="checkbox-inline"><input type="checkbox" name="checkAll"> 选择</label></th>
						<th>仓库</th>
						<th>批次号</th>
						<th>SKU</th>
						<th>库存日期</th>
						<th>入库库存</th>
						<th>期末库存</th>
						<th>采购成本单价</th>
						<th>采购运费单价</th>
						<th>头程运费单价</th>
						<th>头程税费单价</th>
						<th>期末库存金额</th>
						<th>入库时间</th>
						
					</tr>
					</thead>
					<tbody>
					<#list domain.whBatchMonthCounts as count>
					<tr>
						<td>
							<label class="checkbox-inline">
								<input class="${count.id}" name="ids" type="checkbox" value="${count.id}">
								${count.id}
							</label>
						</td>
						<td>${count.storeCode}</td>
						<td>${count.batchNo}</td>
						<td>${count.sku}</td>
						<td>
							${count.countDate?string('yyyy-MM')}
						</td>
						<td>${count.quantity}</td>
						<td>${count.endStock}</td>
						<td>${count.purchaseCostPrice}</td>
						<td>${count.purchaseFreightPrice}</td>
						<td>${count.productFreightPrice}</td>
						<td>${count.productTaxPrice}</td>
						<td>${count.endAmount}</td>
						<td>${count.reportTime}</td>

					</tr>
				</#list>
				<tr>
					<td colspan="5"><span class="group-inline">汇总：</span></td>
					<td><span class="group-inline">${domain.whBatchMonthCount.totalStock}</span></td>
					<td colspan="5"></td>
					<td><span class="group-inline">${domain.whBatchMonthCount.totalEndAmount}</span></td>
				</tr>
				</tbody>
				</table>
			</div>
		</div>


		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	</div>

	<#include "/common/footer.html">
	<img id="enlarge" style='position:absolute;width:800px;height:800px;top:10%;left:15%;display:none;'/>
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/access.js?v=${.now?datetime}"></script>
<script type="text/javascript">
	// 分页
	var total = "${domain.page.totalCount}";
	var pageNo = "${domain.page.pageNo}";
	var pageSize = "${domain.page.pageSize}";

	$("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });
	var heights = $("body").height();
	if(heights > 910) {
		$("#fixed-bottom").addClass("fixed-bottom-height")
	}


	// 分页
	var total = "${domain.page.totalCount}";
	var pageNo = "${domain.page.pageNo}";
	var pageSize = "${domain.page.pageSize}";
	$("#pager").initPager({total: total, pageNo: pageNo, pageSize: pageSize});

	var heights = $("body").height();
	if (heights > 910) {
		$("#fixed-bottom").addClass("fixed-bottom-height")
	}


	var isFilterZero = "${domain.query.isFilterZero}";
	if (isFilterZero == "true") {
		$("input[name='query.isFilterZero']").attr('checked', true);
	}

	function filterZero(obj) {
		if ($(obj).is(':checked')) {
			$(obj).val(true);
		} else {
			$(obj).val(false);
		}
	}

	// 产品图放大
	function enlarge(obj){
		var url = $(obj).attr("src");
		$("#enlarge").attr("src", url);
		$("#enlarge").show(300);
	}

	$("#enlarge").click(function() {$("#enlarge").hide(100);});



	// 全选
	var checkAll = $("input[name='checkAll']");
	// 子选项
	var itemIds = $("input[name='ids']");
	checkAll.change(
			function() {
				itemIds.prop("checked", $(this).prop("checked"));
				itemIds.each(function() {
					var f = $(this).is(":checked");
					var checkClass = $(this).prop("class");
					$("." + checkClass).each(function() {
						$(this).prop("checked", f);
					})
				})
			}
	);


	// 获取选中的记录
	function getCheckedIds() {
		var checkedIds = $("input[name='ids']:checked");
		return checkedIds;
	}


	function download() {
		var checkedDatas = getCheckedIds();
		var params = $("#domain").serialize();
		if (checkedDatas.length > 0) {
			params = checkedDatas.serialize();
		}
		var url = CONTEXT_PATH + "batchMonthCount/download"
		downloadByPostForm(params, url);
	}
</script>
</body>
</html>