<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
#add_modal{
    margin-top:50px;overflow:hidden;
}
.portlet{
    height: 50px;
    margin-top: 10px;
}
.group-inline {
	color: red;
}
</style>
</head>
<body>
<@header method="header" active="13050000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">资产报表</a></li>
					<li class="active">资产变动明细表</li>
				</ul>
			</div>
		</div>

		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">

		<!-- BEGIN PAGE HEADER-->
		<#assign query=domain.query>
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="col-md-12">
				<form action="${CONTEXT_PATH}asset/item/search"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="checkInForm" id="domain">
					<!-- 分页信息 -->
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-1">SKU</label>
							<div class="col-md-3">
								<input class="form-control" name="query.skuStr" type="text" value="${query.skuStr}"  placeholder="多个sku以英文逗号分割">
							</div>
							<label class="control-label col-md-1">SKU状态</label>
							<div class="col-md-3">
								<input class="form-control" name="query.skuStatusList" type="text" value="${query.skuStatusList}"  placeholder="">
							</div>
							<label class="control-label col-md-1">明细日期</label>
							<div class="col-md-3">
                                <div class="input-group">
                                    <input class="form-control Wdate" type="text" name="query.fromDate" placeholder="" readonly="readonly" value="${query.fromDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    <span class="input-group-addon">到</span>
                                    <input class="form-control Wdate" type="text" name="query.toDate" placeholder="" readonly="readonly" value="${query.toDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1">库存类型</label>
							<div class="col-md-3">
								<input class="form-control" name="query.inventoryType" type="text" value="${query.inventoryType}">
							</div>
							<label class="control-label col-md-1">单据编号</label>
							<div class="col-md-3">
								<input class="form-control" name="query.orderNo" type="text" value="${query.orderNo}">
							</div>
							<label class="control-label col-md-1">入库类型</label>
							<div class="col-md-3">
								<select class="form-control" name="query.isPositive" value="${query.isPositive}">
									<option value=""></option>
									<option <#if query.isPositive == true>selected</#if> value="true">增加</option>
									<option <#if (query.isPositive)?? && query.isPositive == false>selected</#if> value="false">减少</option>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1">是否统计汇总</label>
							<div class="col-md-3">
								<select class="form-control" name="query.isTotal" value="${query.isTotal}">
									<option value="false">否</option>
									<option <#if query.isTotal == true>selected</#if> value="true">是</option>
								</select>
							</div>
							<label class="control-label col-md-1">查询重算数据</label>
							<div class="col-md-3">
								<select class="form-control" name="query.checkOrderAssetDetail" value="${query.checkOrderAssetDetail}">
									<option value="false">否</option>
									<option <#if query.checkOrderAssetDetail == true>selected</#if> value="true">是</option>
								</select>
							</div>
						</div>
					</div>
					<div>
						<div class="col-md-offset-10" style="text-align: right">
                            <@header method="auth" authCode="PROPERTY_MODIFY_DETAIL_COMPUTE">
							<button type="button" class="btn btn-default" onclick="calc()">
								<i class="icon-refresh"></i> 计算
							</button>
                            </@header>
							<button type="button" class="btn btn-default" onclick="formReset(this)">
								<i class="icon-refresh"></i> 重置
							</button>
							<button type="submit" class="btn blue">
								<i class="icon-search"></i> 查询
							</button>
                            <@header method="auth" authCode="PROPERTY_MODIFY_DETAIL_DOWNLOAD">
							<button type="button" class="btn btn-default" onclick="downloadRecords()">
								<i class="icon-download"></i> 导出
							</button>
                            </@header>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>
		
		<div class="row">
			<div id="fixedDiv" class="col-md-12">
				<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
                    <colgroup>
                        <col width="4%" />
                        <col width="5%" />
                        <col width="4%" />
                        <col width="7%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="7%" />
                        <col width="7%" />
                        <col width="7%" />
                        <col width="7%" />
                        <col width="7%" />
                        <col width="7%" />
                        <col width="7%" />
                        <col width="7%" />
                        <col width="7%" />
                        <col width="7%" />
                    </colgroup>
					<thead>
					<tr>
						<th><label class="checkbox-inline"><input type="checkbox" name="checkAll"> 选择</label></th>
                        <th>SKU</th>
                        <th>SKU状态</th>
                        <th>日期</th>
                        <th>库存类型</th>
                        <th>单据类型</th>
                        <th>单号</th>
                        <th>期初库存数量</th>
                        <th>期初库存单价</th>
                        <th>期初库存金额</th>
                        <th>本期入库数量</th>
                        <th>本期入库单价</th>
                        <th>本期入库金额</th>
                        <th>期末库存数量</th>
                        <th>期末库存金额</th>
                        <th>期末库存单价</th>
                    </tr>
					</thead>
				</table>
			</div>
			<div class="col-md-12" id="task-list-warp">
				<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
						<colgroup>
							<col width="4%" />
	                        <col width="5%" />
	                        <col width="4%" />
	                        <col width="7%" />
	                        <col width="5%" />
	                        <col width="5%" />
	                        <col width="7%" />
	                        <col width="7%" />
	                        <col width="7%" />
	                        <col width="7%" />
	                        <col width="7%" />
	                        <col width="7%" />
	                        <col width="7%" />
	                        <col width="7%" />
	                        <col width="7%" />
	                        <col width="7%" />
						</colgroup>
						<thead>
							<tr>
								<th><label class="checkbox-inline"><input type="checkbox" name="checkAll"> 选择</label></th>
		                        <th>SKU</th>
		                        <th>SKU状态</th>
		                        <th>日期</th>
		                        <th>库存类型</th>
		                        <th>单据类型</th>
		                        <th>单号</th>
		                        <th>期初库存数量</th>
		                        <th>期初库存单价</th>
		                        <th>期初库存金额</th>
		                        <th>本期入库数量</th>
		                        <th>本期入库单价</th>
		                        <th>本期入库金额</th>
		                        <th>期末库存数量</th>
		                        <th>期末库存金额</th>
		                        <th>期末库存单价</th>
							</tr>
						</thead>
						<tbody>
							<#list domain.whAssetChangeItems as item>
								<tr>
									<td><label class="checkbox-inline"><input class="${item.id}" name="query.ids" type="checkbox" value="${item.id}"> ${item.id} </label></td>
									<td>${item.sku}</td>
									<td>${util('enumDisplay','com.estone.sku.enums.WhSkuStatus',item.skuStatus+'')}</td>
									<td>${item.createDate}</td>
									<td>${item.inventoryTypeName}</td>
									<td>${item.orderTypeName}</td>
									<td>${item.orderNo}</td>
									<td>${item.openingInventoryQuantity}</td>
									<td>${item.openingInventoryPrice}</td>
									<td>${item.openingInventoryAmount}</td>
									<td>${item.currentCheckinQuantity}</td>
									<td>${item.currentCheckinPrice}</td>
									<td>${item.currentCheckinAmount}</td>
									<td>${item.endingInventoryQuantity}</td>
									<td>${item.endingInventoryAmount}</td>
									<td>${item.endingInventoryPrice}</td>
								</tr>
							</#list>
							<tr>
								<td colspan="10"><span class="group-inline">汇总：</span></td>
								<td><span class="group-inline">${domain.whAssetChangeItemGroup.groupQuantity}</span></td>
								<td><span class="group-inline">${domain.whAssetChangeItemGroup.groupPrice}</span></td>
								<td><span class="group-inline">${domain.whAssetChangeItemGroup.groupAmount}</span></td>
								<td colspan="3"></td>
							</tr>
						</tbody>
					</table>
			</div>
		</div>
		
		<div class="modal fade ui-popup" id="exportModal" tabindex="-1" role="dialog" aria-labelledby="exportLabel" aria-hidden="true">
		    <div class="modal-dialog">
		        <div class="modal-content">
		            <div class="modal-header">
		                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
		                <h4 class="modal-title" id="myModalLabel">导出资产明细</h4>
		            </div>
		            <div class="modal-body">
		            	<div class="form-group">
							<label class="radio-inline">
								<input type="radio" style="width: 25px;" name="exportType" value="CURRENT_ALL">所有
							</label>
							<label class="radio-inline">
								<input type="radio" style="width: 25px;" name="exportType" value="CURRENT_PAGE">当前页
							</label>
							<label class="radio-inline">
								<input type="radio" style="width: 25px;" name="exportType" value="CURRENT_CHECK">当前选择
							</label>
						</div>
		            </div>
		            <div class="modal-footer">
		                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
		                <button type="button" class="btn btn-primary" onclick="exportRecords()">导出</button>
		            </div>
		        </div>
		    </div>
		</div>
		
		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/access.js?v=${.now?datetime}"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery-ui/jquery-ui-1.10.2.custom.min.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
	<script type="text/javascript">
		// 全选
		var checkAll = $("#task-list").find("input[name='checkAll']");
		// 子选项
		var recordIds = $("#task-list").find("input[name='query.ids']");
		
		checkAll.change(
		  function () {
			  recordIds.prop("checked", $(this).prop("checked"));
			  
			  recordIds.each(function(){
				  var f = $(this).is(":checked");
					var checkClass = $(this).prop("class");
					$("." + checkClass).each(function(){
						$(this).prop("checked",f);
					})
			  })
		  }
		);
		
		recordIds.change(function(){
			var checkedLenght = recordIds.filter(":checked").length;
			var length = recordIds.length;
			checkAll.prop("checked", checkedLenght == length);
		});
	
	
		// 分页
		var total = "${domain.page.totalCount}";
		var pageNo = "${domain.page.pageNo}";
		var pageSize = "${domain.page.pageSize}";
		$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

        var heights = $("body").height();
        if(heights>910){
            $("#fixed-bottom").addClass("fixed-bottom-height")
        }
        
        var typeArray =  ${domain.inventoryTypes};
    	$("input[name='query.inventoryType']").select2({
    		data : typeArray,
    		placeholder : "库存类型",
    		allowClear : true
    	});
        
        function downloadRecords() {
        	$("#exportModal").modal({
                keyboard: true
            });
        }
        
        function exportRecords() {
        	var exportType = $("input[name='exportType']:checked").val();
        	var checkOrderAssetDetail = $('#domain').find("select[name='query.checkOrderAssetDetail']").val();
        	if (null == exportType) {
        		layer.alert("请选择导出条件", "error");
        		return false;
        	}
        	
        	var domain = $('#domain').serialize();
        	var params = domain + "&exportType=" + exportType;
        	
        	var recordIds = $("#task-list").find("input[name='query.ids']:checked");
        	if (exportType == 'CURRENT_CHECK' && recordIds.length <= 0) {
        		layer.alert("请勾选需要导出的数据", "error");
        		return false;
        	} else if (recordIds.length > 300) {
        		layer.alert("勾选数据不能超出300条", "error");
        		return false;
        	} else if(exportType == 'CURRENT_CHECK') {
	        	recordIds = recordIds.serialize();
	        	params = recordIds + "&exportType=" + exportType + "&query.checkOrderAssetDetail=" + checkOrderAssetDetail;
        	}
        	postExcelFile(params, CONTEXT_PATH + "asset/item/download?");
        	$("#exportModal").modal("hide");
        }
        
		function postExcelFile(params, url) {
			var form = document.createElement("form");
			form.style.display = 'none';
			form.action = url + params;
			form.method = "post";
			document.body.appendChild(form);
			form.submit();
			form.remove();
		}
		
		function calc() {
			if (confirm("计算所有未计算的数据，确定计算？")) {
				$.post(CONTEXT_PATH + "asset/item/calc", function(data) {
					if (data.status == 200) {
						layer.confirm("成功",{
							icon: 1,
							btn: ['确定']
						},function () {
							window.location.reload();
						})
					} else {
						customizeLayer(data.message);
					}
				});
			}
		}
		
		var statusSelectJson = ${domain.statusSelectJson};
		$("input[name='query.skuStatusList']").select2({
			data : statusSelectJson,
			placeholder : "状态",
			multiple: true,
			allowClear : true
		});
	</script>
</body>
</html>