<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}

		#task-list td {
			vertical-align: middle;
		}

		.group-inline {
			color: red;
		}


	</style>
</head>
<body>
<@header method="header" active="13070000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li>
					<a href="#">仓库报表</a>
				</li>
				<li class="active">每月库存汇总</li>
			</ul>
		</div>
	</div>

	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
		<!-- BEGIN PAGE HEADER-->
		<#assign query=domain.query>
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="col-md-12">
				<form action="${CONTEXT_PATH}invoicing/change/count/search"
					  class="form-horizontal form-bordered form-row-stripped"
					  method="post" modelAttribute="domain" name="whIwnvoicingChangeCountForm" id="domain">
					<!-- 分页信息 -->
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
					<input type="hidden" name="query.type" value="month">
					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-1">查询日期</label>
							<div class="col-md-3 input-group type-label">
								<input class="form-control Wdate" type="text" name="query.queryTime" placeholder="" readonly="readonly" value="${query.queryTime }" onfocus="WdatePicker({startDate:'%y-%M',dateFmt:'yyyy-MM',alwaysUseStartDate:true})">
							</div>
							<label class="control-label col-md-1">仓库</label>
							<div class="col-md-2">
								<input class="form-control" name="query.accountnumber" type="text" value="${query.accountnumber}" placeholder="多个查询请以英文逗号分开">
							</div>
							<label class="control-label col-md-1" >SKU</label>
							<div class="col-md-2">
								<input class="form-control" type="text" name="query.sku" placeholder="多个查询请以英文逗号分开" value="${query.sku }">
							</div>
							<label class="control-label col-md-1">是否统计汇总</label>
							<div class="col-md-1">
								<select class="form-control" name="query.statisticsTotal" value="${query.statisticsTotal}">
									<option <#if query.statisticsTotal == false>selected</#if> value="false">否</option>
									<option <#if query.statisticsTotal == true>selected</#if> value="true">是</option>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1">货主</label>
							<div class="col-md-3 input-group">
								<select class="form-control" name="query.virtualShippers" type="text" value="${query.virtualShippers}">
									<option value=""></option>
									<option value="1" <#if query.virtualShippers== 1>selected="selected"</#if> >香港易顺达有限公司</option>
									<option value="2" <#if query.virtualShippers== 2>selected="selected"</#if> >香港易通达有限公司</option>
								</select>
							</div>
							<label class="control-label col-md-1 type-label">过滤数据</label>
							<div class="col-md-2 type-label">
								<input style="margin-top: 10px;" name="query.isFilterZero" type="checkbox"
									   onchange="filterZero(this)" value="${query.isFilterZero}">
								<span class="control-label" style="color: #5f646b">只查看库存差异</span>
							</div>

                            <label class="control-label col-md-1">仓库类型</label>
                            <div class="col-md-2">
                                <select class="form-control" id="warehouseType" name="query.warehouseType" value="${query.warehouseType}">
                                    <option value="" <#if query.warehouseType == ''>selected="selected"</#if> >FBA</option>
                                    <option value="local" <#if query.warehouseType == 'local'>selected="selected"</#if> >本仓</option>
                                    <option value="transfer" <#if query.warehouseType == 'transfer'>selected="selected"</#if> >中转仓</option>
                                </select>
                            </div>
						</div>
					</div>
					<div>
						<div class="col-md-offset-12" style="text-align: right">
							<button type="button" class="btn btn-default" onclick="formReset(this)">
								<i class="icon-refresh"></i> 重置
							</button>
							<button type="submit" class="btn blue">
								<i class="icon-search"></i> 查询
							</button>
                            <@header method="auth" authCode="GATHER_OF_INVENTORY_PER_MONTH">
							<button type="button" class="btn btn-default" onclick="downloadStocks()">
								<i class="icon-download"></i> 导出
							</button>
                            </@header>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>

		<div class="row">
			<div class="col-md-12" id="task-list-warp">
				<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
					<thead>
					<tr>
						<th><label class="checkbox-inline"><input type="checkbox" name="checkAll"> 全选</label></th>
						<th>SKU</th>
                        <#if query.warehouseType == 'local'>
                            <th>销售属性</th>
                        </#if>
						<th>仓库</th>
						<th>库存日期</th>
						<th>末期库存</th>
						<th>末期采购成本单价</th>
						<th>期末采购运费单价</th>
						<th>期末头程运费单价</th>
						<th>期末头程税费单价</th>
						<th>期末退税单价</th>
						<th>期末调拨运费单价</th>
						<th>期末库存金额</th>
						<th>平台仓报告库存</th>
						<th>库存差异</th>
					</tr>
					</thead>
					<tbody>
					<#list domain.whInvoicingChangeCounts as changeCount>
					<tr>
						<td>
							<label class="checkbox-inline">
								<input class="${changeCount.id}" name="ids" type="checkbox" value="${changeCount.id}">
								${changeCount.id}
							</label>
						</td>
						<td>${changeCount.sku}</td>
                        <#if query.warehouseType == 'local'>
                            <td>${changeCount.salesProperty}</td>
                        </#if>

						<td>${changeCount.accountnumber}</td>
						<td>
							${changeCount.stockDate?string('yyyy-MM')}
						</td>

						<td>${changeCount.quantity}</td>
						<td>${changeCount.endingPurchasePrice}</td>
						<td>${changeCount.endingPurchaseCost}</td>
						<td>${changeCount.endingFirstTripCost}</td>
						<td>${changeCount.endingFirstTripTax}</td>
						<td>${changeCount.endingRefundTax}</td>
						<td>${changeCount.endingAllotCost}</td>
						<td>${changeCount.endingAmount}</td>
                        <#if query.warehouseType == ''>
						<td>${changeCount.plantReportQuantity}</td>
						<td>${changeCount.quantityDiff}</td>
                        <#else>
                            <td>\</td>
                            <td>\</td>
                        </#if>
					</tr>
				</#list>
				<tr>
                    <#if query.warehouseType == '' || query.warehouseType == 'transfer'>
						<td colspan="4"><span class="group-inline">汇总：</span></td>
						<#else >
						<td colspan="5"><span class="group-inline">汇总：</span></td>
                    </#if>
					<td><span class="group-inline">${domain.whInvoicingChangeCount.totalEndingInventoryQuantity}</span></td>
					<td colspan="6"></td>
					<td><span class="group-inline">${domain.whInvoicingChangeCount.totalEndingInventoryAmount}</span></td>
					<td><span class="group-inline">${domain.whInvoicingChangeCount.totalPlantReport}</span></td>
					<td><span class="group-inline">${domain.whInvoicingChangeCount.totalQuantityDiff}</span></td>
				</tr>
				</tbody>
				</table>
			</div>
		</div>


		<div id="fixed-bottom" >
			<div id="pager"><ur class="pages"><li class="select"><select><option>10</option></select></ur></div>
		</div>
	</div>

	<#include "/common/footer.html">
	<img id="enlarge" style='position:absolute;width:800px;height:800px;top:10%;left:15%;display:none;'/>
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/access.js?v=${.now?datetime}"></script>
<script type="text/javascript">
	// 分页
	var total = "${domain.page.totalCount}";
	var pageNo = "${domain.page.pageNo}";
	var pageSize = "${domain.page.pageSize}";

	$("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });
	var heights = $("body").height();
	if(heights > 910) {
		$("#fixed-bottom").addClass("fixed-bottom-height")
	}


	// 分页
	var total = "${domain.page.totalCount}";
	var pageNo = "${domain.page.pageNo}";
	var pageSize = "${domain.page.pageSize}";
	$("#pager").initPager({total: total, pageNo: pageNo, pageSize: pageSize});

	var heights = $("body").height();
	if (heights > 910) {
		$("#fixed-bottom").addClass("fixed-bottom-height")
	}


	var isFilterZero = "${domain.query.isFilterZero}";
	if (isFilterZero == "true") {
		$("input[name='query.isFilterZero']").attr('checked', true);
	}

	function filterZero(obj) {
		if ($(obj).is(':checked')) {
			$(obj).val(true);
		} else {
			$(obj).val(false);
		}
	}



	// 全选
	var checkAll = $("input[name='checkAll']");
	// 子选项
	var itemIds = $("input[name='ids']");
	checkAll.change(
			function() {
				itemIds.prop("checked", $(this).prop("checked"));
				itemIds.each(function() {
					var f = $(this).is(":checked");
					var checkClass = $(this).prop("class");
					$("." + checkClass).each(function() {
						$(this).prop("checked", f);
					})
				})
			}
	);


	// 获取选中的记录
	function getCheckedIds() {
		var checkedIds = $("input[name='ids']:checked");
		return checkedIds;
	}


	function downloadStocks() {
		var checkedDatas = getCheckedIds();
		var submitFormParam = $("#domain").serialize();
		submitFormParam = submitFormParam + "&" +checkedDatas.serialize();
		$.post(CONTEXT_PATH + "invoicing/change/count/download", submitFormParam, function(data){
			if (data.status == 200) {
				if (data.message==null || data.message==''){
					layer.alert('成功',function (index) {
						layer.close(index);
						diglog.close().remove();
						location.reload();
					});
				}else{
					customizeLayer(data.message);
				}
			} else {
				customizeLayer(data.message);
			}
		});

	}
</script>
</body>
</html>