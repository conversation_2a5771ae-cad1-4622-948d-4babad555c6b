<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
</head>

<body>
<div>
	<div id="check_scan_datas" class="border-gray p5">
		<#if true == domain.validateMap["isError"]>
			<div class="alert alert-danger">
				<h3 id="errorMsg">${domain.validateMap["errorMsg"]}</h3>
			</div>
		<#elseif (!domain.purchaseOrders) && (false == domain.hasExpressRecord)>
			<div class="alert alert-danger">
				<h3>签收成功，匹配采购单失败</h3>
				<!-- <p>该快递单号没有相关采购单！</p> -->
			</div>
		<#elseif ( !domain.purchaseOrders) && (true == domain.hasExpressRecord)>
			<div class="alert alert-danger">
				<input type="hidden" id="isRejectOrder" value="${domain.whPurchaseExpressRecord.status}"/>
				<#if domain.whPurchaseExpressRecord.status?? && domain.whPurchaseExpressRecord.status == 1>
					<h3>${domain.whPurchaseExpressRecord.comment}</h3>
				</#if >
				<h3>重复操作，该快递已签收</h3>
				<!--  <p>该快递单已经签收！</p> -->
			</div>
		<#else>
			<div><input type="hidden" id="hasShopifyTJSku" value="${domain.hasShopifyTJSku}"></div>
			<#list domain.purchaseOrders as purchaseOrder >
				<form class="form-horizontal submit-form mb20" name="submitForm"
					  id="submit-form-${parentLoopVar.index }"
					  action="" method="POST">
					<div id="common-param-${parentLoopVar.index }">
						<input type="hidden" name="whCheckIn.purchaseOrder.orderId" value="${purchaseOrder.orderId }">
						<input type="hidden" name="whCheckIn.purchaseOrderNo"
							   value="${purchaseOrder.purchaseOrderNo}"></input>
						<input type="hidden" name="whCheckIn.checkInType" value="1">
						<input type="hidden" name="whCheckIn.warehouseId" value="${purchaseOrder.warehouseId}">
						<input type="hidden" name="trackingNumber" value="${purchaseOrder.expressId }"></input>
					</div>
					<h3 class="purchase-info">${purchaseOrder.purchaseOrderNo } (${purchaseOrder.expressId })
						[${purchaseOrder.purchaseUserName}]
						<#if (purchaseOrder.flagsName)??>
							<span class="flags-name">${purchaseOrder.flagsName}</span>
						</#if>
					</h3>
					<#if (purchaseOrder.logisticsMark)??>
						<h3 class="flags-name">该包裹有补发配件</h3>
					</#if>
					<table style="display:none;"
						   class="table table-bordered table-striped table-condensed purchase-order">
						<colgroup>
							<col width="80"/>
							<col width="140"/>
							<col/>
							<col/>
							<col/>
							<col/>
							<col/>
							<col/>
						</colgroup>
						<thead>
						<tr class="">
							<th>图片</th>
							<th>SKU</th>
							<th>商品名称</th>
							<th>包含</th>
							<th>包装</th>
							<th>质检</th>
						</tr>
						</thead>
						<tbody>
						<#list purchaseOrder.purchaseOrderItems as item >
							<tr class="purchase-tr" id="purchase-tr-${parentLoopVar.index }-${loopVar.index}">
								<td><img alt="产品缩略图" border="0" width="80px" height="74px" src="${item.skuImage }"/>
								</td>
								<td>
									<a href="${CONTEXT_PATH}skus/skuDetail?sku=${item.sku }"
									   target="_blank">${item.sku }</a>
									<input type="hidden" name="whCheckIn.whCheckInItem.sku" value="${item.sku}">
									<input type="hidden" name="whCheckIn.whCheckInItem.skuId" value="${item.skuId}">
									<input type="hidden" name="whCheckIn.whCheckInItem.supplierId"
										   value="${purchaseOrder.supplierId}">
									<input type="hidden" name="whCheckIn.whCheckInItem.price"
										   value="${item.price}"></input>
									<input type="hidden" name="whCheckIn.boxNo" value=""></input>
								</td>
								<td>
									<div class="font-small word-wrap" style="width: 100px;">${item.skuName}</div>
								</td>
								<td>${item.productContain}</td>
								<td>${item.packagingMaterial }</td>
								<td>${item.qcType}</td>
							</tr>
						</#list>
						</tbody>
					</table>
				</form>
			</#list>
		</#if>
	</div>
</div>
</body>
</html>