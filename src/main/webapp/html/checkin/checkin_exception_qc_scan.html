<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
</head>
<body>
<div>
<div id="check_scan_datas" class="border-gray p5">
	<#if !domain.whSku>
		<div class="alert alert-danger">
			<h3>操作失败</h3>
			<p>没有找到相关产品！</p>
		</div>
		<#else>
			<!-- BEGIN PAGE -->
			<form name="submitForm" id="submit-form" isAllCheck="${domain.whCheckIn.isAllCheck}" qcNum="${domain.whCheckIn.whCheckInItem.quantity }" method="POST">
				<table>
					<tr>
						<td style="width: 55%;">
							<div>
								<div id="instock-cell">
									<h3 style="color: red;text-align: center;line-height:53px">${domain.whSku.specialTag}</h3>
									<!--<input id="un-scan-uuid" type="hidden" value="${domain.unScanSkuUuidStrs}"/>-->
									<input id="has-scan-num" type="hidden" value="${domain.hasScanNum}"/>
									<#if (domain.whCheckIn.whCheckInItem.firstOrderType)?? && domain.whCheckIn.whCheckInItem.firstOrderType?contains("8")>
										<h3 style="color: red;">此包裹含有补发配件！</h3>
									</#if>
									<#if (domain.whCheckIn.whCheckInItem.firstOrderType)?? && domain.whCheckIn.whCheckInItem.firstOrderType?contains("12")>
										<h3 style="color: red;">带样拍图入库！</h3>
									</#if>
									<#if domain.whCheckIn.whCheckInException?? &&  domain.whCheckIn.whCheckInException.tags?contains("2001")>
										<h3 style="color: red;">换图缺货订单！</h3>
									</#if >
									<#if domain.whCheckIn.whCheckInItem?? && domain.whCheckIn.whCheckInItem.firstOrderTypeName?? && domain.whCheckIn.whCheckInItem.firstOrderTypeName?contains('不贴标业务')>
										<h3><span style="color:red;font-weight: bold;">[不贴标业务]</span></h3>
									</#if>
									<h3>${domain.whCheckIn.purchaseOrderNo }
										<c:set value="${domain.whSku}" var="whSku"></c:set>
										<span style="color:black;font-size:22px;font-weight: 600">${whSku.qcType }</span>
										<span style="margin-left: 40px;">采购：${domain.purchaseUserName}
										<span style="margin-left: 40px;">入库：${util('name', domain.whCheckIn.createUser)}
										<span style="margin-left: 40px;">重量：${domain.whSku.weight}
									</h3>
									<#if domain.whCheckIn.shelfStraightHair == true>
										<div style="margin-top: 20px; display: flex; align-items: center; justify-content: center; gap: 10px;">
											<div style="width: 900px; padding: 10px; background-color: #FFFF00; color: #FF0000; font-weight: bolder; font-size: 20px; text-align: center;">不上架直发</div>
											<button type="button" class="btn btn-primary" onclick="printStraightHair()">补打直发标签</button>
										</div>
									</#if>
									<#if domain.whCheckIn.purchaseApvOutStock == true>
										<input type="hidden" name="whCheckIn.purchaseApvOutStock" value="${domain.whCheckIn.purchaseApvOutStock }"/>
										<input type="hidden" id="exceptionIdStr" value="${domain.exceptionId }"/>
									</#if>

									<table class="table table-bordered table-condensed mt10" id="item-list">
										<colgroup>
											<col width="100px" />
											<col width="100px" />
											<col width="100px" />
											<col width="100px" />
											<col width="250px" />
											<col width="100px" />
											<col width="100px" />
										</colgroup>
										<thead>
										<tr class="">
											<th>良品数量</th>
											<th>不良品类型</th>
											<th>异常数量</th>
											<th>异常周转码</th>
											<th>备注</th>
											<th>QC加工装袋</th>
											<th>操作</th>
										</tr>
										</thead>
										<tbody>

										<#assign whSku = domain.whSku>
										<tr id="tr-product-${whSku.id}">
											<td>
												<input number="true" min="0" digits="true"  required="true" name="whCheckIn.whCheckInItem.quantity" class="form-control instock-quantity" value="">
											</td>
											<td>
												<input class="form-control" id="exceptionType" name="whCheckIn.whCheckInException.exceptionType" type="text" onchange="changeExceptionType(this)" value="">
											</td>
											<td>
												<input number="true" min="0" digits="true"  required="true" name="whCheckIn.whCheckInException.quantity" class="form-control exception-quantity" value="">
											</td>
											<td>
												<input type="text" class="form-control" name="" id="exBoxNo"
													   value="" onkeypress="if(event.keyCode==13) { checkBox(this,'exception')}"tabindex="4">
												<span style="color: red;font-size: 20px;" class="span-boxNo"></span>
											</td>
											<td class="exception-comment-td">
												<input id="exceptionComment" type="text" class="form-control" name="whCheckIn.whCheckInException.exceptionComment" class=" form-control" value="">
												<button type="button" class="btn btn-xs btn-info" style="display: none;margin-top: 5px;" onclick="clickComment1(this)">非保质期SKU</button>
												<button type="button" class="btn btn-xs btn-info" style="display: none;margin-top: 5px;" onclick="clickComment2(this)">临近/已过保质期</button>
											</td>
											<td>
												<select id="qcPacking" name="qcPacking" class="form-control">
													<option value=""></option>
													<option value="9">超级加工</option>
													<option value="7">重加工</option>
													<option value="5">一般加工</option>
													<option value="3">轻加工</option>
													<option value="0">否</option>
												</select>
											</td>
											<td>
												<button type="button" class="btn btn-default btn-xs" id="single-build-btn" onclick="submitBuildInStock(${whSku.id});">
													提交
												</button>
												<div name="restricted-storage" style="margin-top: 20px;">
													<#assign restrictedStorage = (domain.whCheckIn.restrictedStorage)?string('true', 'false')>
													<#if restrictedStorage == 'true'>
														<span style="color: red;">SKU即将过保质期，请提交异常单！</span>
													<#else >
														<span style="color: red;"></span>
													</#if>
													<input type="hidden" name="whCheckIn.restrictedStorage" value="${restrictedStorage }"/>
													<input type="hidden" name="whCheckIn.days" value="${domain.whCheckIn.days }">
													<input type="hidden" name="whCheckIn.proDate" value="${domain.whCheckIn.proDate }">
													<input type="hidden" name="whCheckIn.expDate" value="${domain.whCheckIn.expDate }">
													<input type="hidden" name="whCheckIn.expWaitId" value="${domain.whCheckIn.expWaitId }">
													<input type="hidden" name="whCheckIn.expFlag" value="${domain.whCheckIn.expFlag }">
												</div>
											</td>

										</tr>
										<#if whSku.expSku == true>
											<tr class="exp-setting-tr">
												<td style="padding: 20px;" colspan="7">
													<#include "/checkin/expSettingContain.html">
												</td>
											</tr>
										</#if>
										<tr class="active">
											<td colspan="7" style="text-align:right;height: 30px"></td>
										</tr>
										<!-- 服装类业务html -->
										<#include "/checkin/qcClothingSizeContain.html">
										</tbody>
									</table>
								</div>

								<input type="hidden" name="uuidSku" id="hidden-uuidSku" value="${whSku.sku}">
								<input type="hidden" name="inputUuid" id="hidden-inputUuid" value="${domain.uuid}">
								<input type="hidden" id="purchaseOrderNo" value="${domain.whCheckIn.purchaseOrderNo }">
								<input type="hidden" id="trackingNumber" value="${domain.whCheckIn.trackingNumber }">
								<input type="hidden" name="inId" id="hidden-inId" value="${domain.whCheckIn.inId}">
								<input type="hidden" name="whCheckIn.whCheckInException.boxNo" id="exBoxNoParam" value=""/>
								<input type="hidden" id="checkInSkuFlags" value="${domain.whCheckIn.whCheckInItem.checkInSkuFlags}"/>
								<input type="hidden" id="flags" value="${domain.whCheckIn.flags}"/>
								<div id="product-cell">
									<#if domain.whCheckIn.whCheckInException?? >
										<h3 style="color: red">异常单处理入库
											<span style="margin-left: 40px;">异常员：${util('name', domain.whCheckIn.whCheckInException.exceptionUser)}
											<span style="margin-left: 40px;">备注信息：${ domain.whCheckIn.whCheckInException.exceptionComment}
										</h3>
									</#if >
									<#if domain.whCheckIn.flags?? && domain.whCheckIn.flags?index_of("SHOPIFY_TJ")!=-1>
										<h3 style="color: red">shopify全检加急入库</h3>
									</#if >
									<table class="table table-bordered table-condensed" id="product-table">
										<colgroup>
											<col width="100px" />
											<col width="200px" />
											<col width="200px" />
											<col width="100px" />
											<col width="100px" />
											<col width="100px" />
											<col width="100px"/>
										</colgroup>
										<thead>
											<tr class="">
												<th>产品图片</th>
												<th>SKU</th>
												<th>产品信息</th>
												<th>系统加工装袋</th>
                                                <th>包装属性</th>
												<th>产品标识</th>
												<th>入库数量</th>
												<th>抽检数量</th>
											</tr>
										</thead>
										<tbody>

											<c:set value="${domain.whSku}" var="whSku"></c:set>
											<tr id="tr-product-${whSku.id}">

												<td >
													<#if (whSku.whSkuExtend.qcImageUrl)?? && whSku.whSkuExtend.qcImageUrl!=''>
														<img alt="产品缩略图" border="0" width="80px" height="74px" src="${whSku.whSkuExtend.qcImageUrl }" />
													<#else>
														<img alt="产品缩略图" border="0" width="80px" height="74px" src="${whSku.imageUrl }" />
													</#if>
													<!--<img alt="产品缩略图" border="0" width="80px" height="74px" src="${whSku.imageUrl }" />-->
												</td>
												<td >
													<dl>
														<dt id="product-sku"><a href="${CONTEXT_PATH}skus/skuDetail?sku=${whSku.sku }" target="_blank">${whSku.sku }</a></dt>
														<dd>库位号：${domain.whCheckIn.whCheckInItem.location }</dd>
														<dd>开发员：${util('name',whSku.productDeveloper)}</dd>
														<dd>采购员：${util('name',whSku.productBuyer)}</dd>
													</dl>
													<span class="label custom label-sm label-warning">${whSku.statusName }</span>
												</td>
												<td >
													<dl>
														<dd id="product-name-${whSku.id}">产品名称：<span style="color: red">${whSku.name }</span></dd>
														<dd id="product-color-${whSku.id}">颜色：${whSku.color }</dd>
														<dd id="product-contain-${whSku.id}">包含：${whSku.contain }</dd>
														<dd id="product-weight-${whSku.id}">重量：${whSku.weight } g</dd>
														<dd id="product-packagingId-${whSku.id}">包装：</dd>
														<dd id="product-skuDecs-${whSku.id}">产品系统质检备注：
															<span style="font-size: 14px;color: red;font-weight: bold;font-weight: 600">${whSku.skuDecs }</span>
														</dd>
                                                        <dd id="product-skuDecs-${whSku.id}">仓库质检备注：
                                                            <span style="font-size: 14px;color: red;font-weight: bold;font-weight: 600">${whSku.feature }</span>
                                                        </dd>
                                                        <dd id="product-size-${whSku.id}">标准尺寸：
                                                            <span style="color: red;" >${whSku.contain}</span>
                                                        </dd>
                                                        <dd id="product-tagList-${whSku.id}">仓库标签：
                                                            <span style="font-size: 14px;color: red;font-weight: bold;font-weight: 600">${whSku.whSkuExtend.tags }</span>
                                                        </dd>
													</dl>
												</td>
												<td id="product-skuAlias-${whSku.id}" style="text-align: center;">
                                                    <#if !(domain.systemPack??) || domain.systemPack == 0>
                                                        <span class="qc-packing" data-value="${domain.systemPack}">否</span>
													<#else >
														<span class="qc-packing" data-value="${domain.systemPack}">
															${util('enumName', 'com.estone.sku.enums.ProcessType', domain.systemPack)}
														</span>
                                                    </#if>
												</td>
												<td id="product-skuAlias-${whSku.id}" >
													<dl>
														<#if (domain.whSkuWithPmsInfo??) && (domain.whSkuWithPmsInfo.isOriginalPackage == 'true')>
															<!--<dd style="text-align: center;"><span style="color: red;font-size: 20px;font-weight: 600;line-height: 3">是</span></dd>-->
															<dd>包装属性：<span style="font-size: 14px;color: red;font-weight: bold;font-weight: 600">${domain.whSkuWithPmsInfo.packageAttribute }</span></dd>
														<#else >
															<!--<dd style="text-align: center;"><span style="color: red;font-size: 20px;font-weight: 600;line-height: 3">否</span></dd>-->
															<dd>包装属性：<span style="font-size: 14px;color: red;font-weight: bold;font-weight: 600">${domain.whSkuWithPmsInfo.packageAttribute }</span></dd>
														</#if>
													</dl>
                                                </td>
												<td id="product-skuAlias-${whSku.id}" style="text-align: center;">
													<span style="color: red;font-size: 14px;font-weight: 600">${domain.whSkuWithPmsInfo.skuLabelName}</span>
												</td>
												<td class="qc-quantity">${domain.whCheckIn.whCheckInItem.quantity }</td>
												<td class="qc-quantity">${domain.qcQuantity }</td>
											</tr>
										</tbody>
									</table>
									<div id="translate-result">
										<div class="row">
											<div class="col-md-6 translate-src">
											</div>
											<div class="col-md-6 translate-dst" style="background-color: #f0f0f0">
											</div>
										</div>
									</div>
								</div>

                                <div id="product-cell-exception-count">
                                    <br>
                                    <div><strong>${whSku.sku} 历史异常 </strong></div>
                                    <br>
                                    <div><strong>异常统计（点击可查看最近入库异常单）</strong></div>
                                    <table style="text-align: center" class="table table-bordered table-condensed" id="product-table">
                                        <thead>
                                        <tr>
                                            <th>尺寸不符</th>
                                            <th>颜色不符</th>
                                            <th>损坏</th>
                                            <th>少配件</th>
                                            <th>来货与描述不符</th>
                                            <th>功能异常</th>
                                            <th>来货与图不符</th>
                                            <th>少货</th>
                                            <th>多货</th>
                                            <th>多配件</th>
                                            <th>缺少SKU</th>
                                            <th>尖锐物品</th>
                                            <th>其他原因</th>
                                        </tr>
                                        </thead>
                                        <tbody>

                                        <tr>
                                            <td onclick="showHistoryExceptionList('13')" style="color: red;font-size: 16px;">${domain.historyCheckInExceptionCount.sizeErrorCount}</td>
                                            <td onclick="showHistoryExceptionList('14')" style="color: red;font-size: 16px;">${domain.historyCheckInExceptionCount.colorErrorCount}</td>
                                            <td onclick="showHistoryExceptionList('15')" style="color: red;font-size: 16px;">${domain.historyCheckInExceptionCount.damageCount}</td>
                                            <td onclick="showHistoryExceptionList('16')" style="color: red;font-size: 16px;">${domain.historyCheckInExceptionCount.lessFittingCount}</td>
                                            <td onclick="showHistoryExceptionList('19')" style="color: red;font-size: 16px;">${domain.historyCheckInExceptionCount.descriptionErrorCount}</td>
                                            <td onclick="showHistoryExceptionList('21')" style="color: red;font-size: 16px;">${domain.historyCheckInExceptionCount.functionErrorCount}</td>
                                            <td onclick="showHistoryExceptionList('24')" style="color: red;font-size: 16px;">${domain.historyCheckInExceptionCount.imgNotMatchCount}</td>
                                            <td onclick="showHistoryExceptionList('11')" >${domain.historyCheckInExceptionCount.lessQuantityCount}</td>
                                            <td onclick="showHistoryExceptionList('12')" >${domain.historyCheckInExceptionCount.excessQuantityCount}</td>
                                            <td onclick="showHistoryExceptionList('17')" >${domain.historyCheckInExceptionCount.excessFittingCount}</td>
                                            <td onclick="showHistoryExceptionList('18')" >${domain.historyCheckInExceptionCount.lessSkuCount}</td>
                                            <td onclick="showHistoryExceptionList('31')" >${domain.historyCheckInExceptionCount.sharpCount}</td>
                                            <td onclick="showHistoryExceptionList('25')" >${domain.historyCheckInExceptionCount.othersCount}</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                    <div><strong>最近异常</strong></div>
                                    <table id="recent-exception-tab" style="text-align: center;font-size: 13px;display: none" class="table table-bordered table-condensed purchase-order">
                                        <thead>
                                        <tr>
                                            <th>异常单号</th>
                                            <th>异常时间</th>
                                            <th>异常类型</th>
                                            <th>异常状态</th>
                                            <th>提交时备注</th>
                                            <th>采购处理备注</th>
                                            <th>处理方式</th>
                                            <th>图片</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
							</div>
						</td>
						<td class="img-view">
							<div class="img-content">
								<#include "/sku/skuImgsContain.html">
							</div>
						</td>
					</tr>
				</table>
			</form>
	<!-- END PAGE -->
	</#if>

	<!-- 服装类业务script -->
	<#include "/checkin/checkinQcClothingSizeContainScript.html">
    <script type="text/javascript" src="${CONTEXT_PATH}js/jquery.imageView.js"></script>
    <script type="text/javascript" src="${CONTEXT_PATH}js/sku/sku-imgs.js?v=${.now?datetime}"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.region.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/prompt-message.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/sku/expSettingQC.js?v=${.now?datetime}"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
	<script type="text/javascript">
        // 异常类型
        //var exceptionTypeArray = jQuery.parseJSON($("#exceptionTypes").text());
		var exceptionTypeArray = ${domain.exceptionTypes};
        $("input[name='whCheckIn.whCheckInException.exceptionType']").select2({
            data : exceptionTypeArray,
            placeholder : "异常类型",
            multiple: true,
            allowClear : true
        });
        $(".instock-quantity").blur(function(){
            var $this = $(this);
            if($this.val() == ''){
                $this.val("");
                return;
            }
            var reg = /^\+?[1-9][0-9]*$/;
            if (!reg.test($this.val())) {
				getErrorInfoAlert("请输入正确的正整数");
                $this.val("");
                return;
            }
            //良品数
            var qty = parseInt($('.instock-quantity').val() == '' ? 0 : $('.instock-quantity').val());

            var inQty = parseInt(${domain.whCheckIn.whCheckInItem.quantity });

            //当前入库数 >采购数量
            if(qty  > inQty) {
				$this.val("");
            }
        });


		//LODOP打印不上架直发
		function printStraightHair(){
			try {
				LODOP = getLodop(document.getElementById('LODOP_OB'), document.getElementById('LODOP_EM'));
				LODOP.PRINT_INIT("打印");
				LODOP.ADD_PRINT_HTM(0, 0, "32mm", "18mm", '不上架直发');
				LODOP.PRINT();
				//LODOP.PREVIEW();
			} catch (error) {
				layer.alert("打印失败"+error);
			}
		}

        // 校验周转码
        function checkBox(obj,type){

            if(!obj.value || obj.value.replace(/\s/g,'') == ''){
				getErrorInfoAlert("请输入异常周转码!");
                return ;
            }
            var exBoxNo = obj.value.replace(/\s/g,'');
            if(exBoxNo.length < 4){
				getErrorInfoAlert("请输入正确的异常周转码!");
                return ;
            }
            var flag = hasUsedBoxNo(exBoxNo);
            if(flag){
				getErrorInfoAlert("该周转码已锁定，请重新输入!");
                return ;
            }

            if(obj.id == $("#exBoxNo").attr("id")){
                $("#exBoxNo").val('');// 清空中转码
            }

            if(exBoxNo==""){
				getErrorInfoAlert("请输入异常周转码！");
                return false;
            } else{
                // 验证有效性
                $.ajax({
                    url : CONTEXT_PATH + "checkin/scans/createCheckIn/checkBox",
                    data : {boxNo : exBoxNo,type:type},
                    success : function(json){
                        if (json.status == '500') {
							getErrorInfoAlert(json.message + '');
                        } else if (json.status == '200') {
                            $("#exBoxNoParam").val(exBoxNo);// 塞入异常中转码
							$("#exBoxNo").next(".span-boxNo").text(exBoxNo);// 塞入异常中转码
						}
                    },
                    error:function(){
						getErrorInfoAlert('校验失败，请重新扫描周转码!');
                    }
                });
            }

        }
        function hasUsedBoxNo(inputBoxNo){
            var flag = false;
            var boxNoSpans = $('.span-boxNo');
            for (var i = 0; i < boxNoSpans.length; i++) {
                var boxNoSpan = boxNoSpans[i];
                var boxNo = $(boxNoSpan).text();
                if(boxNo !=null && boxNo!='' && boxNo == inputBoxNo){
                    flag = true;
                    break;
                }
            }
            return flag;
        }
     	// 是否新品SKU
        /*var isFirst = ${domain.isFirst?string('true', 'false')};*/
        // 单个生成入库单
        function submitBuildInStock(id) {
       		// 是否新品
        	/*if (isFirst) {
        		confirmMake();
        		return false;
        	}*/
       		// 验证参数
        	if (!validateParams(id)) {
        		return false;
        	}
        	buildInStock(id);
        }

        // 单个生成入库单
        function buildInStock(id) {


			// 校验服装数据
			if(!chekclothingData()){
				return false;
			}

            clicked = true;
            // 生成入库单
            var params = $("#submit-form").serialize()+"&qcPacking="+qcPacking;

            $.ajax({
                url : "${CONTEXT_PATH}checkException/skus/pass",
                type: "POST",
                dataType : "json",
                data: params,
                error : function() {
                    App.unblockUI();
					getErrorInfoAlert("系统内部出错，请稍后重试。");
                },
                beforeSend : function() {
                    App.blockUI();
                },
                success: function(data) {
					App.unblockUI();
                    if(data.status == 200){
                        alert("操作成功");
                        $('#uuidSku').val('');
                        $('#hidden-uuidSku').val('');
                        $('#hidden-inId').val("");
                        $('#hidden-inputUuid').val("");
                        $('#s2id_exceptionType').find('li.select2-search-choice').remove();
                        $('.instock-quantity').val('');
                        $('.exception-quantity').val('');
                        $('.span-boxNo').text('');
                        $('#exceptionComment').val('');
                        $('#exceptionType').val('');
                        $('#qcPacking').val('');
                        $('#uuidSku').focus();

                        calsf();// 成功数量

                        // 标识成功
                        $("[id='tr-item-" + $("#product-sku").text() + "']").removeClass("danger").addClass("success");

                        // 清空中间产品信息
                        $("#product-cell").remove();

                        //清空历史异常
                        $("#product-cell-exception-count").remove();

                        // 清空图片
                        $('.img-view').remove();

                        //清除提交按钮
                        $('#single-build-btn').remove();

                        //清除服装属性
						$('#clothing-tab').remove();
                    }else {
						getErrorInfoAlert(data.message);
                    }
                }
            }); // end ajax
        }
        // 验证参数
        function validateParams(id) {
        	if($("#submit-form").attr("isAllCheck") == "true"){
        		if(!sessionStorage.getItem("isAllCheck")){
					getErrorInfoAlert("入库单无效，请确认！");
        			return false;
        		}
        	}
            var qcPacking = $("#qcPacking").val();
            // 当前行
            var currentTr = $("#tr-product-" + id);
            //良品数
            var qty = parseInt(currentTr.find('.instock-quantity').val() == '' ? 0 : currentTr.find('.instock-quantity').val());
            //不良品数
            var exQty = parseInt(currentTr.find('.exception-quantity').val() == '' ? 0 : currentTr.find('.exception-quantity').val());

            var inQty = parseInt(${domain.whCheckIn.whCheckInItem.quantity });

            var exceptionType = $("#exceptionType").val();
            var exBoxNoText = $("#exBoxNo").next(".span-boxNo").text();
            var reg = /^\+?[1-9][0-9]*$/;

            if((qcPacking == null || qcPacking=='')){
				getErrorInfoAlert("请选择QC是否加工装袋!");
                return false;
            }

            if (qty > 0 && !reg.test(qty)) {
				getErrorInfoAlert("良品数量输入错误，请输入正确的正整数");
                return false;
            }

            if (exQty > 0 && !reg.test(exQty)) {
				getErrorInfoAlert("不良品数量输入错误，请输入正确的正整数");
                return false;
            }

			if(qty+exQty < inQty){
				getErrorInfoAlert("良品数量+不良品数不能小于入库数量!");
                return false;
            }
            //异常类型选择不包含12.多货，良品数量+不良品数 不能大于入库数量

            if(exceptionType.indexOf("12") == -1 && (qty+exQty > inQty)){
				getErrorInfoAlert("良品数量+不良品数不能大于入库数量!");
                return false;
			}

            if(exceptionType != "" && exceptionType != 11 && exBoxNoText ==""){
				getErrorInfoAlert("请先绑定异常周转码！");
                return false;
            }
            if((exQty > 0 || exBoxNoText != '' || $('#exceptionComment').val() != '') && exceptionType == ''){
				getErrorInfoAlert("请输入异常类型！");
                return false;
            }
            if(qty <= 0 && exceptionType == '' && exBoxNoText==''
                && $('#exceptionComment').val() == '' && exQty <= 0){
				getErrorInfoAlert("良品数量不能为空！");
                return false;
            }
            
            return true;
        }

		$(document).ready(function(){
  			var images = null;
  			var currentImageIndex = null;
  			var sku = $('#product-sku').text();
		}); // end ready

        function showHistoryExceptionList(exceptionType) {

            var sku = $("#hidden-uuidSku").val();
            $.ajax({
                url : "${CONTEXT_PATH}checkInException/getRecentThreeExceptionsByExceptionType?sku="+sku+"&exceptionType="+exceptionType,
                type: "GET",
                dataType : "json",
                error : function() {
                    App.unblockUI();
					getErrorInfoAlert("系统内部出错，请稍后重试。");
                },
                beforeSend : function() {
                    App.blockUI();
                },
                success: function(data) {
                    App.unblockUI();
                    if(data.body.recentThreeExceptions){
                        var html = "";
                        for (var i = 0; i < data.body.recentThreeExceptions.length; i++ ){
                            var exception = data.body.recentThreeExceptions[i];
                            var firstEditHandleComment = '';
                            var lastPurchaseHandleComment = '';
                            var exceptionHandleWayName = '';
                            var image = '';
                            var creationDate = '';
                            if(exception.firstEditHandleComment){
                                firstEditHandleComment = exception.firstEditHandleComment;
                            }
                            if(exception.lastPurchaseHandleComment){
                                lastPurchaseHandleComment = exception.lastPurchaseHandleComment;
                            }
                            if(exception.exceptionHandleWayName){
                                exceptionHandleWayName = exception.exceptionHandleWayName;
                            }
                            if(exception.images){
                                image = exception.images[0];
                            }
                            if(exception.creationDate){
                                creationDate = new Date(exception.creationDate).format("yyyy-MM-dd hh:mm:ss");
                            }
                            html += "<tr>"
                                    +"<td>" + exception.id + "</td>"
                                    +"<td>" + creationDate + "</td>"
                                    +"<td>" + exception.exceptionTypeName + "</td>"
                                    +"<td>" + exception.exceptionStatusName + "</td>"
                                    +"<td>" + firstEditHandleComment + "</td>"
                                    +"<td>" + lastPurchaseHandleComment + "</td>"
                                    +"<td>" + exceptionHandleWayName + "</td>"
                                    +"<td> <input type='hidden' id ='img-"+exception.id+"' value='"+exception.images+"'/>"
                                    + "<img onclick='getExceptionImages("+exception.id+")'  border = '0' width='65px' height='65px' src='"+image+"'></td>"
                                    +"</tr>";
                        }
                        $("#recent-exception-tab").find("tbody").html(html);
                        $("#recent-exception-tab").css("display","table");
                    }
                }
            });// end ajax
        }

        function getExceptionImages(exceptionId){
            var  img = $("#img-"+exceptionId).val();
            var images = null;
            if(img!=null && img !=''){
                images = img.split(",");
            }
            dialog({
                title: '相关异常图片',
                url: CONTEXT_PATH + "checkInException/getExceptionImages?exceptionImages="+images,
                width: 800,
                height: 680,
                top: 0
            }).showModal();
        }
// ---------------------------------------------------------------------------------电子秤称重START--------------------------------------------------------------------------------- //        
 		// 读取电子秤称重数据
        $('#customsWeight').focus( function() {
        	// 标准重量
        	var netWeightStr = '${domain.whSku.netWeight}';
        	// 递归调用
        	callWeightRxtx(netWeightStr);
        });
 		
 		// 连需调用结果
 		var weightArray = new Array();
 		// 称重次数
 		var weightCount = 15;
 		// 称重验证次数
 		var validateWeightCount = 5;
 		
 		// 初始化称重数据
        function initWeightRxtx(value) {
        	weightArray = new Array();
     		weightCount = 15;
     		$('#customsWeight').val(value);
        }
 		
 		// 调用HTTP监听电子秤称重重量
        function callWeightRxtx(netWeightStr) {
        	$.ajax({
                url : 'http://127.0.0.1:9999/getWeight',
                type: 'POST',
                dataType: 'jsonp',
                jsonp:'jsonCallback',
                crossDomain: true,
                timeout: 2000,
                success : function(response) {
                	// 递归调用
                	if (weightCount <= 0) {
                		// 二十次必须全部成功
                    	if (weightArray && weightArray.length < validateWeightCount) {
                    		initWeightRxtx(null);
							getErrorInfoAlert('当前电子秤不稳定，请稳定后重新称重');
                    		return;
                    	}
                    	callMaxCountWeight(weightArray);
                		return;
                	}
                	
                    if (response.status == '200') {
                    	var result = response.result;
                    	if (!result) {
                    		initWeightRxtx(null);
							getErrorInfoAlert('请连接电子秤称重');
                    		return;
                    	}
                    	
                 		var customsWeight = Number(result);
                 		// 是否放置了货物
                    	if (customsWeight <= 0) {
                    		initWeightRxtx(null);
							getErrorInfoAlert('请去皮后放置SKU称重！当前称重重量：' + customsWeight);
                    		return;
                    	}
                 		
                    	// 验证净重，称重重量不可大于已有的净重
                        var netWeightStr = '${domain.whSku.netWeight}';
                        if (netWeightStr) {
                            var netWeight = Number(netWeightStr);
                            if ((netWeight > 0) && (customsWeight > netWeight)) {
                            	initWeightRxtx(null);
								getErrorInfoAlert('称重重量大于标准重量，请核对后重新称重！当前称重重量：' + customsWeight);
                                return;
                            }
                        }
                        
                        weightArray.push(customsWeight);
                    } else {
                    	initWeightRxtx(null);
						getErrorInfoAlert(response.message);
                    	return;
                    }
                    
                    // 递归调用
                    weightCount--;
                    setTimeout(function() {
	                    callWeightRxtx(netWeightStr);
					}, 25);
                },
                error:function() {
                	initWeightRxtx(null);
					getErrorInfoAlert('连接异常，请检查称重小工具是否启动');
                }
            });
        }
        
        // 获取电子秤稳定次数
        function callMaxCountWeight(arr) {
 			var obj = {};
 			for (var i = 0; i < arr.length; i++) {
 			    if (!obj[arr[i]]) {
 			        obj[arr[i]] = 1;
 			    }
 			    else {
 			        obj[arr[i]]++;
 			    }
 			}

 			// 数组中出现最多的元素的次数
 			var maxCountValue = 0;
 			// 数组中出现最多的元素
 			var maxCountWeight = arr[0];
 			for (var i in obj) {
 			    if (obj[i] > maxCountValue) {
 			    	maxCountValue = obj[i];
 			    	maxCountWeight = i;
 			    }
 			}
 			
 			if (maxCountValue < validateWeightCount) {
 				initWeightRxtx(null);
				getErrorInfoAlert('当前电子秤不稳定，请稳定后重新称重，稳定次数：' + maxCountValue);
        		return;
 			}
 			
 			initWeightRxtx(maxCountWeight);
 		}
// ---------------------------------------------------------------------------------电子秤称重END--------------------------------------------------------------------------------- //
		// 选择异常类型
		function changeExceptionType(obj) {
			var objDev = $(obj).parent().parent();
			var exceptionType = $(obj).val();
			if (exceptionType && exceptionType.indexOf('29') >= 0) {
				$(objDev).find('.exception-comment-td').find('button').css("display", "block");
			} else {
				$(objDev).find('.exception-comment-td').find('button').css("display", "none");
			}
		}

		function clickComment1(obj) {
			$(obj).parent().find("input[id='exceptionComment']").val($(obj).text());
		}

		function clickComment2(obj) {
			$(obj).parent().find("input[id='exceptionComment']").val($(obj).text());
		}
	</script>
</div>
</div>
</body>
</html>