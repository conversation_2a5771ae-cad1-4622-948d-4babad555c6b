<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<link rel="stylesheet" type="text/css" href="${CONTEXT_PATH}css/my-select.css?v=${.now?datetime}">
	<style type="text/css">
		.form-body {
			margin-top: 10px !important;
		}
		.modal-body input {
			width: 240px;
		}

		.exception-content {
			margin: 0px 0px 10px 10px;
			padding-bottom: 10px;
		}
		hr{
			width: 90%;
			margin-left: 15px;
		}
		.required{
			margin-right: 5px;
		}
	</style>
</head>
<body>
<@header method="header" active="10040000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">入库查询</a></li>
				<li><a href="#">入库异常单查询</a></li>
				<li class="active">入库异常单编辑</li>
			</ul>
		</div>
	</div>

	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
		<div class="row">
			<div class="col-md-12">
				<#assign query = domain.query>
				<#assign exception = domain.whCheckInException>
				<form class="form-horizontal form-bordered form-row-stripped" id ="domain">
					<div class="form-body">
						<div class="modal-body">
							<div class="exception-content">
								<input id="id" type="hidden" value="${domain.whCheckInException.id}"/>
								<div class="form-group">
									<label class="control-label col-md-2">SKU</label>
									<div class="col-md-3">
										<input type="text" class="form-control" id="sku" name="whCheckInException.sku"
											   value="" onkeypress="if(event.keyCode==13) { checkSku(this); return false;}">
									</div>
								</div>
								<div class="form-group">
									<label class="control-label col-md-2">周转码</label>
									<div class="col-md-3">
										<input type="text" class="form-control" name="whCheckInException.boxNo" id="boxNo"
											   value="" onkeypress="if(event.keyCode==13) { checkBox(this,'exception'); return false;}"tabindex="4">
										<span style="color: red;font-size: 20px;" class="span-boxNo"></span>
									</div>
								</div>
								<div class="form-group">
									<label class="control-label col-md-2">采购单号</label>
									<div class="col-md-3">
										<input class="form-control" id="purchaseOrderNo" name="whCheckInException.purchaseOrderNo" type="text"
											   value="" onkeypress="if(event.keyCode==13) { checkPurchaseOrderNo(this); return false;}"/>
									</div>
								</div>
								<div class="form-group">
									<label class="control-label col-md-2"><span class="required">*</span>快递单号</label>
									<div class="col-md-3">
										<input class="form-control" id="trackingNumber" name="whCheckInException.trackingNumber" type="text"
											   value="" onkeypress="if(event.keyCode==13) { checkTrackingNumber(this); return false;}"/>
									</div>
								</div>
								<div class="form-group">
									<label class="control-label col-md-2"><span class="required">*</span>异常数量</label>
									<div class="col-md-3">
										<input class="form-control" id="quantity" name="whCheckInException.quantity" type="text"/>
									</div>
								</div>
								<div class="form-group">
									<label class="control-label col-md-2"><span class="required">*</span>异常类型</label>
									<div class="col-md-2">
										<input class="form-control" id="exceptionType" name="whCheckInException.exceptionType" type="text" />
									</div>
								</div>
								<div class="form-group">
									<label class="control-label col-md-2"><span class="required">*</span>采购员</label>
									<input class="select-input" id="purchaseUser" name="whCheckInException.purchaseUser" type="hidden" value="">
									<div class="col-md-2 mySelect select-lt" placeholder="采购员" value=""></div>
								</div>
								<div class="form-group">
									<label class="control-label col-md-2"><span class="required">*</span>仓库</label>
									<div class="col-md-2">
										<select id="warehouseId" name="whCheckInException.warehouseId" class="form-control">
											<option value=""></option>
											<#list domain.warehouseList as warehouse>
												<option value="${warehouse.id }">${warehouse.id } - ${warehouse.name }</option>
											</#list>
										</select>
									</div>
								</div>

								<div class="form-group">
									<label class="control-label col-md-2">异常描述</label>
									<div class="col-md-3">
										<textarea rows="3" cols="50" id="exceptionComment" name="whCheckInException.exceptionComment"></textarea>
									</div>
								</div>

								<div class="form-group">
									<div class="col-sm-offset-4 col-sm-10">
										<a class="btn blue" style="margin-right: 20px;" id="save-add">
											<i class="icon-save"></i> 保存
										</a>
									</div>
								</div>
							</div>
						</div>

					</div>
				</form>
			</div>
			<br/>
		</div>
	</div>
	<#include "/common/footer.html">
</div>

<div style="display:none;" id="purchase-user">
	[{"id":"", "text":""}<#list domain.purchaseUsers as purchaseUser>,{"id":"${purchaseUser.id}", "text":"${purchaseUser.userName} - ${purchaseUser.employeeName}"}</#list>]
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.region.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/prompt-message.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/my-select.js?v=${.now?datetime}"></script>
<script type="text/javascript">

	$(document).ready(function () {
		var purchaseUserArry = jQuery.parseJSON($('#purchase-user').text());
		var mySelect = $('.mySelect').selectlt({
			isdisabled:false,
			isfiltration:true,
			selectedValue:'0',
			data:purchaseUserArry
		});
		//选中事件
		mySelect.on('select-change', function (e, data) {
			console.log('选中值:', data.data.value);
			$("input[name='whCheckInException.purchaseUser']").val(data.data.value);
		});
	});

	$('#sku').focus();
    // 异常类型
    //var exceptionTypeArray = jQuery.parseJSON($("#exceptionTypes").text());
	var exceptionTypeArray = ${domain.exceptionTypes}
    $("input[name='whCheckInException.exceptionType']").select2({
        data : exceptionTypeArray,
        placeholder : "异常类型",
        multiple: true,
        allowClear : true
    });

    function checkSku(obj) {
        var val = $('#sku').val();
		//兼容sku编码
        //var val = getSkuByBarCode(obj);

        if(!val || val.trim() == ''){
			getErrorInfoAlert("请输入有效sku!");
            return false;
        }
        if(!(val.indexOf("=") == -1)){
            var realSku = val.split('=')[0];
            $('#sku').val(realSku);
        }
        $('#boxNo').focus();
    }
    function checkPurchaseOrderNo(obj) {
        $('#trackingNumber').focus();
    }
    function checkTrackingNumber(obj) {
        $('#quantity').focus();
    }
    // 校验周转码
    function checkBox(obj,type){

        var boxNo = obj.value.replace(/\s/g,'');
        if(boxNo.length < 4){
			getErrorInfoAlert("请输入正确的周转码!");
            return ;
        }
        var flag = hasUsedBoxNo(boxNo);
        if(flag){
			getErrorInfoAlert("该周转码已锁定，请重新输入!");
            return ;
        }

        if(obj.id == $('#boxNo').attr("id")){
            $('#boxNo').val('');// 清空中转码
        }

		// 验证有效性
		$.ajax({
			url : CONTEXT_PATH + "checkin/scans/createCheckIn/checkBox",
			data : {boxNo : boxNo, type:type},
			success : function(json){
				if (json.status == '500') {
					getErrorInfoAlert(json.message + '');
				} else if (json.status == '200') {
					//buildInStock(parentIndex, index, boxNo);
					if(obj.id == $('#boxNo').attr("id")){
					    $('#boxNo').val(boxNo);
						$('#boxNo').next(".span-boxNo").text(boxNo);
                        $('#purchaseOrderNo').focus();
					}
				}
			},
			error:function(){
				getErrorInfoAlert('校验失败，请重新扫描周转码!');
			}
		});

        function hasUsedBoxNo(inputBoxNo){
            var flag = false;
            var boxNoSpans = $('.span-boxNo');
            for (var i = 0; i < boxNoSpans.length; i++) {
                var boxNoSpan = boxNoSpans[i];
                var boxNo = $(boxNoSpan).text();
                if(boxNo !=null && boxNo!='' && boxNo == inputBoxNo){
                    flag = true;
                    break;
                }
            }
            return flag;
        }

    }

    $('#save-add').on('click',function () {
        var param = $('#domain').serialize();

        var reg = /^\+?[1-9][0-9]*$/;
        //异常数量
        var quantity = $('#quantity').val();
        //快递单号
        var trackingNumber = $('#trackingNumber').val();
        //异常类型
        var exceptionType = $("#exceptionType").val();
        //采购员
        var purchaseUser = $('#purchaseUser').val();

        var  warehouseId = $('#warehouseId').val();

        var boxNo = $('#boxNo').val();
        var boxNoText = $('#boxNo').next(".span-boxNo").text();

        if(boxNo != '' && boxNoText == ''){
			getErrorInfoAlert("请先绑定周转码！");
            return false;
        }
        if(boxNo != '' && boxNoText != '' && boxNo != boxNoText){
			getErrorInfoAlert("输入的周转码与绑定的周转码不一致！");
            return false;
        }
        if(trackingNumber == null || trackingNumber == ''){
			getErrorInfoAlert("快递单号不能为空！");
            return false;
        }
        if(quantity == null || quantity == ''){
			getErrorInfoAlert("异常数量不能为空！");
            return false;
        }
        if (!reg.test(quantity)) {
			getErrorInfoAlert("请输入正确的正整数!");
            return false;
        }
        if(exceptionType == null || exceptionType == ''){
			getErrorInfoAlert("异常类型不能为空！");
            return false;
        }
        if(purchaseUser == null || purchaseUser == ''){
			getErrorInfoAlert("采购员不能为空！！");
            return false;
		}

		if(warehouseId == null || warehouseId == ''){
			getErrorInfoAlert("仓库不能为空！！");
            return false;
		}

        $.post(CONTEXT_PATH + "checkInException/save", param +"&type=ADD", function(data) {
            if(data.status == 200){
                location.href = CONTEXT_PATH + "checkInException/edit?id="+data.message;
            }else {
				getErrorInfoAlert("添加失败!");
            }
        });
    });

</script>
</body>
</html>