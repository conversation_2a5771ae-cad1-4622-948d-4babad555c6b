<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
    <head>
        <title>易世通达仓库管理系统</title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <#include "/common/include.html">
        <style type="text/css">
            .modal-body input {
                width: 240px;
            }
            .check-in-detail th{
                background-color:#E0E0E0 !important;
            }
            .description {
                margin-top: 50px;
                text-align: left;
                line-height: 1.5;
                width: 1000px;
                height: 100px;
                background-color: #8080804a;
            }
            .item{
                margin-top: 30px;
            }
        </style>
    </head>
    <body>
        <@header method="header" active="13010000"><#include "/ftl/header.ftl"></@header>

        <div id="page" style="background-color: rgb(231, 237, 248)">
            <div class="row">
                <div class="col-md-12" style="padding: 0">
                    <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                        <li><a href="#">系统设置</a></li>
                        <li class="active">入库免检设置</li>
                    </ul>
                </div>
            </div>

            <!-- 内容 -->
            <div class="container-fluid" style="background-color: white;border: none">
                <div class="row">
                    <div class="col-md-12">
                        <form autocomplete="off" action="${CONTEXT_PATH}exemptionQcConfig/update"
                              class="form-horizontal form-bordered form-row-stripped"
                              method="post" modelAttribute="domain" name="domain" id ="domain">
                            <input type="hidden" value="${domain.exemptionQcConfiguration.id}" name="exemptionQcConfiguration.id">
                            <h5><strong>免QC配置</strong></h5>
                            <div>
                                <div class="item">
                                    <h5><strong>统计入库异常的类型</strong></h5>
                                    <div>
                                        <#assign exceptionTypes = domain.exceptionTypes>
                                        <#list exceptionTypes as exceptionType>
                                            <#if domain.exemptionQcConfiguration?? && domain.exemptionQcConfiguration.exceptionTypeList?size gt 0>
                                                <#assign exceptionTypeList = domain.exemptionQcConfiguration.exceptionTypeList>
                                                <#if exceptionTypeList?seq_contains(exceptionType)>
                                                    <input type="checkbox" disabled="disabled" checked="checked" name="exemptionQcConfiguration.exceptionType"
                                                           value="${exceptionType.getCode()}">${exceptionType.getName()}
                                                <#else>
                                                    <input type="checkbox" disabled="disabled" name="exemptionQcConfiguration.exceptionType"
                                                           value="${exceptionType.getCode()}">${exceptionType.getName()}
                                                </#if>
                                            <#else>
                                                <input type="checkbox" disabled="disabled" name="exemptionQcConfiguration.exceptionType"
                                                       value="${exceptionType.getCode()}">${exceptionType.getName()}
                                            </#if>
                                        </#list>
                                    </div>
                                </div>

                                <div class="item">
                                    <h5><strong>供应商-SKU近未出现异常天数</strong></h5>
                                    <div style="width: 250px;">
                                        <input class="form-control" type="text" disabled="disabled" name="exemptionQcConfiguration.unhappenExceptionDay"
                                               onblur="checkNumber(this)"
                                               value="${domain.exemptionQcConfiguration.unhappenExceptionDay}">
                                    </div>
                                </div>

                                <div class="item">
                                    <h5><strong>供应商-SKU近未出现异常采购单数</strong></h5>
                                    <div style="width: 250px;">
                                        <input class="form-control" type="text" disabled="disabled" name="exemptionQcConfiguration.skuAbnormalPurchaseNum"
                                               onblur="checkNumber(this)"
                                               value="${domain.exemptionQcConfiguration.skuAbnormalPurchaseNum}">
                                    </div>
                                </div>

                                <div class="item">
                                    <h5><strong>排除SKU</strong></h5>
                                    <div>
                                        <#assign exclusiveSkuTypes = domain.exclusiveSkuTypes>
                                        <#list exclusiveSkuTypes as exclusiveSkuType>
                                            <#if domain.exemptionQcConfiguration?? && domain.exemptionQcConfiguration.exclusiveSkuTypeList??>
                                                <#assign exclusiveSkuTypeList = domain.exemptionQcConfiguration.exclusiveSkuTypeList>
                                                <#if exclusiveSkuTypeList?seq_contains(exclusiveSkuType)>
                                                    <input type="checkbox" disabled="disabled" checked="checked" name="exemptionQcConfiguration.exclusiveSkuType"
                                                           style="display: none;" value="${exclusiveSkuType.getCode()}">${exclusiveSkuType.getName()}
                                                <#else>
                                                    <input type="checkbox" disabled="disabled" name="exemptionQcConfiguration.exclusiveSkuType"
                                                           style="display: none;" value="${exclusiveSkuType.getCode()}">${exclusiveSkuType.getName()}
                                                </#if>
                                            <#else>
                                                <input type="checkbox" disabled="disabled" name="exemptionQcConfiguration.exclusiveSkuType"
                                                       style="display: none;" value="${exclusiveSkuType.getCode()}">${exclusiveSkuType.getName()}
                                            </#if>
                                            <#if exclusiveSkuType_has_next>
                                                、
                                            </#if>
                                        </#list>
                                    </div>
                                </div>
                                <div class="item">
                                    <h5><strong>入库单限制</strong></h5>
                                    <div style="width: 250px;">
                                        <span style="display: inline-block;">入库数量≤</span>
                                        <input class="form-control" style="display: inline-block; width: calc(100% - 5em);"
                                               type="text" disabled="disabled" name="exemptionQcConfiguration.checkInSkuNum"
                                               onblur="checkNumber(this)"
                                               value="${domain.exemptionQcConfiguration.checkInSkuNum}">
                                    </div>
                                </div>
                                <div class="item">
                                    <h5><strong>采购单价</strong></h5>
                                    <div style="width: 250px;">
                                        <span style="display: inline-block;">采购单价≤</span>
                                        <input class="form-control" style="display: inline-block; width: calc(100% - 5em);"
                                               type="text" disabled="disabled" name="exemptionQcConfiguration.purchasePrice"
                                               onblur="checkFloatNumber(this)"
                                               value="${domain.exemptionQcConfiguration.purchasePrice}">
                                    </div>
                                </div>
                                <div class="item">
                                    <h5><strong>状态</strong></h5>
                                    <div>
                                        <input type="radio" disabled="disabled" <#if domain.exemptionQcConfiguration?? && domain.exemptionQcConfiguration.status == 1>checked="checked"</#if>
                                               name="exemptionQcConfiguration.status" value="1"> 启用
                                        <input type="radio" disabled="disabled" <#if domain.exemptionQcConfiguration?? && domain.exemptionQcConfiguration.status == 0>checked="checked"</#if>
                                               name="exemptionQcConfiguration.status" value="0"> 禁用
                                    </div>
                                </div>
                            </div>
                            <div class="item">
                                <div class="col-md-offset-12" style="text-align: left">
                                    <button type="button" onclick="updateOrSave()" class="btn btn-info"><span id="buttonTips">修改</span></button>
                                    <#if domain.exemptionQcConfiguration?? && domain.exemptionQcConfiguration.id??>
                                        <button type="button" class="btn btn-default" onclick="viewLog(${domain.exemptionQcConfiguration.id}, 'exemptionQcConfig')">日志</button>
                                    <#else>
                                        <button type="button" class="btn btn-default" onclick="viewLog(1, 'exemptionQcConfig')">日志</button>
                                    </#if>
                                </div>
                            </div>
                        </form>
                        <div class="description">
                            <br/>免检功能说明：<br/><br/>
                            当免QC状态开启时，系统将在入库时计算SKU是否需要经过QC环节，如果采购单中供应商对应的SKU满足从当天开始往前至配置范围内的时间段内未出现勾选的异常时，点数入库提交后，将会在页面提示入库单免检，直接进入到上架环节，不再经过QC。
                        </div>
                    </div>
                    <br/>
                </div>
            </div>
            <#include "/common/footer.html">
        </div>
        <script type="text/javascript">
            // 用于标识当前按钮是修改还是保存。true表示修改按钮
            var isUpdate = true;
            function updateOrSave(){
                if (isUpdate){
                    // 点击修改按钮
                    $('input').each(function() {
                        // 排除sku的相关值需要固定写死
                        var name = $(this).attr("name");
                        if (name != 'exemptionQcConfiguration.exclusiveSkuType'){
                            $(this).prop('disabled', false);
                        }
                    });
                    $("#buttonTips").text("保存");
                    isUpdate = false;
                }else{
                    debugger;
                    // 点击提交按钮
                    var data = {};
                    data['unhappenExceptionDay']= $("input[name='exemptionQcConfiguration.unhappenExceptionDay']").val();
                    data['skuAbnormalPurchaseNum']= $("input[name='exemptionQcConfiguration.skuAbnormalPurchaseNum']").val();
                    data['checkInSkuNum']= $("input[name='exemptionQcConfiguration.checkInSkuNum']").val();
                    data['purchasePrice']= $("input[name='exemptionQcConfiguration.purchasePrice']").val();
                    data['status']=$("input[name='exemptionQcConfiguration.status']:checked").val();
                    // 获取选择的值
                    var exceptionTypes = [];
                    $("input[name='exemptionQcConfiguration.exceptionType']:checked").each(function(){
                        exceptionTypes.push($(this).val());
                    });
                    data['exceptionType']= join(exceptionTypes);
                    var exclusiveSkuType = [];
                    $("input[name='exemptionQcConfiguration.exclusiveSkuType']:checked").each(function(){
                        exclusiveSkuType.push($(this).val());
                    });
                    data['exclusiveSkuType']=join(exclusiveSkuType);
                    $.ajax({
                        url:CONTEXT_PATH +"exemptionQcConfig/update",
                        type:"POST",
                        dataType:'json',
                        contentType: "application/json;charset=utf-8",
                        data:JSON.stringify(data),
                        success : function(response){
                            var message = '';
                            if (response.message != null) {
                                message = response.message
                            }
                            if (response.status == '500') {
                                customizeLayer('修改失败：'+message, 'error');
                                return;
                            } else if (response.status == '200') {
                                layer.confirm('修改成功！',{
                                    icon: 1,
                                    btn: ['确定']
                                },function () {
                                    $('input').each(function() {
                                        // 排除sku的相关值需要固定写死
                                        var name = $(this).attr("name");
                                        if (name != 'exemptionQcConfiguration.exclusiveSkuType') {
                                            $(this).prop('disabled', true);
                                        }
                                    });
                                    $("#buttonTips").text("修改");
                                    isUpdate = true;
                                    window.location.reload();
                                })
                            }
                        },
                        error:function(){
                            customizeLayer('操作失败!', 'error');
                        }
                    });
                }
            }

            function checkFloatNumber(obj){
                var $this = $(obj);
                if($this.val() == '') {
                    $this.val("");
                    return;
                }
                var reg = /^\d+(\.\d+)?$/;
                if(!reg.test($this.val())) {
                    layer.alert("请输入正确的数字", 'error');
                    $this.val("");
                    return;
                }
            }


            function checkNumber(obj) {
                var $this = $(obj);
                if($this.val() == '') {
                    $this.val("");
                    return;
                }
                var reg = /^\+?[1-9][0-9]*$/;
                if(!reg.test($this.val())) {
                    layer.alert("请输入正确的正整数", 'error');
                    $this.val("");
                    return;
                }
            }

            function join(arrays){
                if (!arrays || arrays.length == 0){
                    return '';
                }
                var result = '';
                for (var i = 0; i < arrays.length; i++) {
                    var val = arrays[i];
                    if (i == 0) {
                        result += val;
                    } else {
                        result += "," + val;
                    }
                }
                return result;
            }
        </script>
    </body>
</html>