<tr id="count-tab-${purchaseOrder_index }-${item_index}">
    <td colspan="13" >
        <div><strong>${item.sku} 历史异常 </strong><span onclick="expand(this,${purchaseOrder_index },${item_index})" class="expand" style="color: #5b884b">【收起】</span></div>
        <div><strong>异常统计（点击可查看最近入库异常单）</strong></div>
        <br>
        <table style="width: 75%;text-align: center;display: table;" class="table table-bordered table-condensed purchase-order">
            <thead>
                <tr>
                    <th>尺寸不符</th>
                    <th>颜色不符</th>
                    <th>损坏</th>
                    <th>少配件</th>
                    <th>来货与描述不符</th>
                    <th>功能异常</th>
                    <th>来货与图不符</th>
                    <th>少货</th>
                    <th>多货</th>
                    <th>多配件</th>
                    <th>缺少SKU</th>
                    <th>尖锐物品</th>
                    <th>其他原因</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td onclick="showHistoryExceptionList('13',${purchaseOrder_index },${item_index})" style="color: red;font-size: 16px;">${item.historyCheckInExceptionCount.sizeErrorCount}</td>
                    <td onclick="showHistoryExceptionList('14',${purchaseOrder_index },${item_index})" style="color: red;font-size: 16px;">${item.historyCheckInExceptionCount.colorErrorCount}</td>
                    <td onclick="showHistoryExceptionList('15',${purchaseOrder_index },${item_index})" style="color: red;font-size: 16px;">${item.historyCheckInExceptionCount.damageCount}</td>
                    <td onclick="showHistoryExceptionList('16',${purchaseOrder_index },${item_index})" style="color: red;font-size: 16px;">${item.historyCheckInExceptionCount.lessFittingCount}</td>
                    <td onclick="showHistoryExceptionList('19',${purchaseOrder_index },${item_index})" style="color: red;font-size: 16px;">${item.historyCheckInExceptionCount.descriptionErrorCount}</td>
                    <td onclick="showHistoryExceptionList('21',${purchaseOrder_index },${item_index})" style="color: red;font-size: 16px;">${item.historyCheckInExceptionCount.functionErrorCount}</td>
                    <td onclick="showHistoryExceptionList('24',${purchaseOrder_index },${item_index})" style="color: red;font-size: 16px;">${item.historyCheckInExceptionCount.imgNotMatchCount}</td>
                    <td onclick="showHistoryExceptionList('11',${purchaseOrder_index },${item_index})" >${item.historyCheckInExceptionCount.lessQuantityCount}</td>
                    <td onclick="showHistoryExceptionList('12',${purchaseOrder_index },${item_index})" >${item.historyCheckInExceptionCount.excessQuantityCount}</td>
                    <td onclick="showHistoryExceptionList('17',${purchaseOrder_index },${item_index})" >${item.historyCheckInExceptionCount.excessFittingCount}</td>
                    <td onclick="showHistoryExceptionList('18',${purchaseOrder_index },${item_index})" >${item.historyCheckInExceptionCount.lessSkuCount}</td>
                    <td onclick="showHistoryExceptionList('31',${purchaseOrder_index },${item_index})" >${item.historyCheckInExceptionCount.sharpCount}</td>
                    <td onclick="showHistoryExceptionList('25',${purchaseOrder_index },${item_index})" >${item.historyCheckInExceptionCount.othersCount}</td>
                </tr>
            </tbody>
        </table>
    </td>
</tr>