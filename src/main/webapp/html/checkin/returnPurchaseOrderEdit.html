<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
    <style type="text/css">
		.content-title{
			margin-bottom: 60px;
		}
        .content-title label{
            text-align: right;
        }
		.readonly-row{
            padding: 10px;
			height: 500px;
			overflow-y: scroll;
		}
		.write-row{
			background: white;
			padding: 20px;
		}
		.btn-float{
			text-align: center;
		}
		.btn-float a{
            margin: 10px 20px;
        }
        .table-striped td img{
            width: 80px;
            height: 80px;
        }
        .table tbody>tr>th,td{
            text-align: center;
            vertical-align: middle !important;
        }
        .fixed-width{
            width: 200px;
        }
        .nowrap-text{
            width: 150px;
            height: 50px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            margin-left: 20px;
        }
    </style>
</head>
<body>
<@header method="header" active="10040000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li><a href="#">入库查询</a></li>
                <li><a href="#">退货单管理</a></li>
                <li class="active">退货单编辑</li>
            </ul>
        </div>
    </div>
    <#assign query = domain.query>
    <#assign returnPurchaseOrders = domain.returnPurchaseOrders>
    <#list returnPurchaseOrders as returnPurchaseOrder>
    <#if returnPurchaseOrder_index == 0>
    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">
        <div class="row readonly-row">
            <h4><strong>退货单--${returnPurchaseOrder.returnOrderNo}</strong></h4>
            <div class="col-md-12">
                <div class="form-body">
                    <div class="form-group content-title">
                        <h5><strong>退货信息</strong></h5>
                        <label class="control-label col-md-1">创建人</label>
                        <div class="col-md-3">
                            <input class="form-control" type="text" readonly value="${returnPurchaseOrder.createUserName}">
                        </div>
                        <label class="control-label col-md-1">是否为供应商责任</label>
                        <div class="col-md-3">
                            <input class="form-control" type="text" readonly value="<#if returnPurchaseOrder.isVendorBlame == 'true'>是<#elseif returnPurchaseOrder.isVendorBlame == 'false'>否<#else ></#if>">
                        </div>
                        <label class="control-label col-md-1">退货原因</label>
                        <div class="col-md-3">
                            <input class="form-control" type="text" readonly value="${returnPurchaseOrder.returnReason}">
                        </div>
                    </div>
                    <div class="form-group content-title">
                        <h5><strong>运费支付方式</strong></h5>
                        <label class="control-label col-md-1">运费承担方</label>
                        <div class="col-md-3">
                            <input class="form-control" type="text" readonly value="${returnPurchaseOrder.shippingCostBearer}">
                        </div>
                        <label class="control-label col-md-1">运费支付方式</label>
                        <div class="col-md-3">
                            <input class="form-control" type="text" readonly value="${returnPurchaseOrder.shippingCostType}">
                        </div>
                        <label class="control-label col-md-1">省份</label>
                        <div class="col-md-3">
                            <input class="form-control" type="text" id="state" readonly value="${returnPurchaseOrder.state}">
                        </div>
                    </div>

                    <div class="form-group content-title">
                        <h5><strong>退货收件人信息</strong></h5>
                        <label class="control-label col-md-1">退货收件人</label>
                        <div class="col-md-3">
                            <input class="form-control" type="text" readonly value="${returnPurchaseOrder.receiver}">
                        </div>
                        <label class="control-label col-md-1">收件人电话</label>
                        <div class="col-md-3">
                            <input class="form-control" type="text" readonly value="${returnPurchaseOrder.receiverPhone}">
                        </div>
                        <label class="control-label col-md-1">退货地址</label>
                        <div class="col-md-3">
                            <input class="form-control" type="text" readonly value="${returnPurchaseOrder.returnAddress}">
                        </div>
                    </div>
                </div>

            </div>

            <#if returnPurchaseOrder.id?? >
                <div class="col-md-12 content-title">
                    <h5><strong>退货内容 </strong></h5>
                    <table class="table table-condensed table-bordered  table-striped">
                        <tbody>
                        <tr>
                            <th>入库异常单号</th>
                            <th>采购单号</th>
                            <th>到货快递单号</th>
                            <th>图片</th>
                            <th>商品名称</th>
                            <th>SKU</th>
                            <th>退货数量</th>
                        </tr>
                         <#list returnPurchaseOrder.returnItems as returnItem>
                            <tr>
                                <td class="fixed-width"><p title="${returnItem.exceptionId}" class="nowrap-text">${returnItem.exceptionId}</p></td>
                                <td class="fixed-width"><p title="${returnItem.purchaseOrderNo}" class="nowrap-text">${returnItem.purchaseOrderNo}</p></td>
                                <td class="fixed-width"><p title="${returnItem.receiveShippingOrderNo}" class="nowrap-text">${returnItem.receiveShippingOrderNo}</p></td>
                                <td><img src="${returnItem.whSku.imageUrl}" alt=""/></td>
                                <td>${returnItem.whSku.name}</td>
                                <td>${returnItem.articleNumber}</td>
                                <td>${returnItem.returnQuantity}</td>
                            </tr>
                         </#list>
                        <#if !returnPurchaseOrder.returnItems >
                            <tr class="tc">
                                <td colspan="7" align="center">没有记录</td>
                            </tr>
                        </#if>
                        </tbody>
                    </table>
                </div>
            </#if>
        </div>
    </div>
    <div class="row write-row">
        <form class="form-horizontal form-bordered form-row-stripped"  id ="domain">
            <div class="form-body">
                <h4><strong>录入退货快递信息</strong></h4>
                <div class="form-group">
                    <h5><strong>采购备注：${returnPurchaseOrder.remark}</strong></h5>
                </div>
                <div id="packages">
    </#if>
                <div id="package_${returnPurchaseOrder_index}">
                    <input type="hidden" id="returnOrderNo_${returnPurchaseOrder_index}" name="returnPurchaseOrders[${returnPurchaseOrder_index}].returnOrderNo" value="${returnPurchaseOrder.returnOrderNo}"/>
                    <input type="hidden" id="id_${returnPurchaseOrder_index}" name="returnPurchaseOrders[${returnPurchaseOrder_index}].id" value="${returnPurchaseOrder.id}"/>
                    <input type="hidden" id="lightGoodsFlag_${returnPurchaseOrder_index}" name="returnPurchaseOrders[${returnPurchaseOrder_index}].lightGoodsFlag" value="<#if returnPurchaseOrder.lightGoodsFlag??>${returnPurchaseOrder.lightGoodsFlag}</#if>"/>
                    <input type="hidden" id="volumeWeightFlag_${returnPurchaseOrder_index}" name="returnPurchaseOrders[${returnPurchaseOrder_index}].volumeWeightFlag" value="${returnPurchaseOrder.volumeWeightFlag}"/>
                    <div class="form-group">
                        <label class="control-label col-md-1">
                            <strong style="float:left;">包裹${returnPurchaseOrder_index + 1}</strong>
                        </label>
                    <#if returnPurchaseOrder_index == 0>
                        <div class="col-md-1">
                            <button class="btn blue" onclick="appendPackage();return false;">添加包裹</button>
                        </div>
                    <#else>
                        <div class="col-md-1">
                            <button class="btn blue" onclick="deletePackage(${returnPurchaseOrder_index});return false;">删除包裹</button>
                        </div>
                    </#if>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-1">物流单号</label>
                        <div class="col-md-1">
                            <input class="form-control" type="text" id="returnShippingOrderNo_${returnPurchaseOrder_index}" name="returnPurchaseOrders[${returnPurchaseOrder_index}].returnShippingOrderNo" value="${returnPurchaseOrder.returnShippingOrderNo}">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-1">物流公司</label>
                        <div class="col-md-1">
                            <#if !(returnPurchaseOrder.shippingCompanyCode)?? || !(returnPurchaseOrder.shippingCompany)??>
                            <input class="form-control" type="text" id="shippingCompany_${returnPurchaseOrder_index}" name="returnPurchaseOrders[${returnPurchaseOrder_index}].shippingCompany"
                                   value="">
                            <#else >
                            <input class="form-control" type="text" id="shippingCompany_${returnPurchaseOrder_index}" name="returnPurchaseOrders[${returnPurchaseOrder_index}].shippingCompany"
                                   value="${(!returnPurchaseOrder.shippingCompanyCode)?string(returnPurchaseOrder.shippingCompany,returnPurchaseOrder.shippingCompanyCode)}">
                            </#if>
                        </div>

                        <label class="control-label col-md-1">重量</label>
                        <div class="col-md-1">
                            <input class="form-control" type="text" id="weight_${returnPurchaseOrder_index}" name="returnPurchaseOrders[${returnPurchaseOrder_index}].weight" onkeypress="if(event.keyCode==13) { checkWeight(this,${returnPurchaseOrder_index}); return false;}" tabindex="4"
                                   value="<#if returnPurchaseOrder.weight??>${returnPurchaseOrder.weight?if_exists?string.number}</#if>">
                        </div>
                        <label class="control-label col-md-1" >体积</label>
                        <label class="control-label col-md-1" style="width:88px;;color: #787676;" ><span style="color: red;" id="span-l_${returnPurchaseOrder_index}">*</span>长(CM)</label>
                        <div class="col-md-1">
                            <input class="form-control" type="text" id="length_${returnPurchaseOrder_index}" name="returnPurchaseOrders[${returnPurchaseOrder_index}].length" onkeypress="if(event.keyCode==13) { checkWeight(this,${returnPurchaseOrder_index}); return false;}" tabindex="4"
                                   value="<#if returnPurchaseOrder.length??>${returnPurchaseOrder.length?if_exists?string.number}</#if>" >
                        </div>
                        <label class="control-label col-md-1" style="width:88px;color: #787676;"><span style="color: red;" id="span-w_${returnPurchaseOrder_index}">*</span>宽(CM)</label>
                        <div class="col-md-1">
                            <input class="form-control" type="text" id="width_${returnPurchaseOrder_index}" name="returnPurchaseOrders[${returnPurchaseOrder_index}].width" onkeypress="if(event.keyCode==13) { checkWeight(this,${returnPurchaseOrder_index}); return false;}" tabindex="4"
                                   value="<#if returnPurchaseOrder.width??>${returnPurchaseOrder.width?if_exists?string.number}</#if>">
                        </div>
                        <label class="control-label col-md-1" style="width:88px;color: #787676;"><span style="color: red;" id="span-h_${returnPurchaseOrder_index}">*</span>高(CM)</label>
                        <div class="col-md-1">
                            <input class="form-control" type="text" id="height_${returnPurchaseOrder_index}" name="returnPurchaseOrders[${returnPurchaseOrder_index}].height" onkeypress="if(event.keyCode==13) { checkWeight(this,${returnPurchaseOrder_index}); return false;}" tabindex="4"
                                   value="<#if returnPurchaseOrder.height??>${returnPurchaseOrder.height?if_exists?string.number}</#if>">
                        </div>
                        <label class="control-label col-md-1">体积重</label>
                        <div class="col-md-1">
                            <input class="form-control" type="text" id="volumeWeight_${returnPurchaseOrder_index}" name="returnPurchaseOrders[${returnPurchaseOrder_index}].volumeWeight" readonly="readonly"
                                   value="<#if returnPurchaseOrder.volumeWeight??>${returnPurchaseOrder.volumeWeight?if_exists?string.number}</#if>">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-1">计费重量</label>
                        <div class="col-md-1">
                            <input class="form-control" type="text" id="costWeight_${returnPurchaseOrder_index}" name="returnPurchaseOrders[${returnPurchaseOrder_index}].costWeight" value="${returnPurchaseOrder.costWeight}" readonly="readonly"/>
                        </div>
                        <label class="control-label col-md-1">运费</label>
                        <div class="col-md-1">
                            <input class="form-control" type="number" min="0.001" step="0.001" id="returnShippingCost_${returnPurchaseOrder_index}" name="returnPurchaseOrders[${returnPurchaseOrder_index}].returnShippingCost" value="${returnPurchaseOrder.returnShippingCost}" onblur="changeThisReturnShippingCost(${returnPurchaseOrder_index},this);"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <h5 style="color:red"><strong id = "strong-mag_${returnPurchaseOrder_index}"></strong></h5>
                    </div>
                    <div class="form-group">
                        <hr></hr>
                    </div>
                </div>
            <#if !returnPurchaseOrder_has_next>
            </div>
            </#if>
    </#list>
                <div class="form-group">
                    <h5><strong>预估最终运费：</strong></h5>
                </div>
                <div class="form-group">
                    <label class="control-label col-md-1">总计费重量</label>
                    <div class="col-md-1">
                        <input class="form-control" type="text" id="costWeight" name="allCostWeight" readonly="readonly"
                               value="<#if domain.allCostWeight??>${domain.allCostWeight?if_exists?string.number}</#if>">
                    </div>
                    <label class="control-label col-md-1">总运费</label>
                    <div class="col-md-1">
                        <input class="form-control" type="text" id="returnShippingCost" name="allReturnShippingCost" readonly="readonly"
                               value="<#if domain.allReturnShippingCost??>${domain.allReturnShippingCost?if_exists?string.number}</#if>">
                    </div>
                </div>
                <div class="form-group">
                    <div class="btn-float">
                        <a class="btn blue" id="return"><i class="icon-reply"></i>取消</a>
                        <a class="btn blue" id="save"><i class="icon-save"></i> 提交</a>
                    </div>
                </div>
            </div>
		</form>
	</div>
	<#include "/common/footer.html">
</div>

<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.region.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/prompt-message.js"></script>
<script type="text/javascript">
    var reg = /^\+?[1-9][0-9]*$|^[+]{0,1}(\d+\.\d{1,3})$/;
    $(document).ready(function(){
        $("#returnShippingCost").blur(function(){
            var $this = $(this);
            if($this.val() == ''){
                $this.val("");
                return;
            }
            if (!reg.test($this.val())) {
				getErrorInfoAlert("请输入正确的运费，支持正整数和小数!");
                $this.val("");
                return;
            }
        });

        initElementBindEvents();
    }); // end ready

	function getShippingInfo(flag,packageIndex){
		var length = $('#length_'+packageIndex).val();
		var width = $('#width_'+packageIndex).val();
		var height = $('#height_'+packageIndex).val();
		if (flag == '1') {
			if (length == '' || width == '' || height == '') {
				return;
			}
		}
		var shippingCompany = $('#shippingCompany_'+packageIndex).val();
		var param = $('#domain').serialize() + "&shippingCompany="+shippingCompany+"&length="+length+"&width="+width+"&height="+height;;

		$.get(CONTEXT_PATH + "returnPurchaseOrder/getShippingInfo", param, function(data){
			if (data.status == 200) {
				$('#volumeWeightFlag_'+packageIndex).val(data.body.volumeWeightFlag);
				$('#volumeWeight_'+packageIndex).val(data.body.volumeWeight);
				getShippingCost(packageIndex);
			} else {
				getErrorInfoAlert(data.message);
			}
		});
	}

	//packageIndex为本包裹的编号
    function checkWeight(obj,packageIndex){
	    debugger;
        var weight = obj.value.replace(/\s/g,'');
        if (!reg.test(weight)) {
			getErrorInfoAlert("请输入正确的重量，支持正整数和小数!");
            $('#weight_'+packageIndex).val("");
            return;
        }
        getShippingCost(packageIndex);
    }

    function getShippingCost(packageIndex){
		var volumeWeightFlag = $('#volumeWeightFlag_'+packageIndex).val();
		var volumeWeight = $('#volumeWeight_'+packageIndex).val();
        var shippingCompany = $('#shippingCompany_'+packageIndex).val();
        var weight = $('#weight_'+packageIndex).val();
        var state = $('#state').val();
		var length = $('#length_'+packageIndex).val();
		var width = $('#width_'+packageIndex).val();
		var height = $('#height_'+packageIndex).val();
		if (weight ==''){
			return ;
		}
		if (volumeWeightFlag == 'true'){
			$("#span-l_"+packageIndex).text("*");
			$("#span-w_"+packageIndex).text("*");
			$("#span-h_"+packageIndex).text("*");
			if(length == '' ||  width == '' || height==''){
				$('#strong-mag_'+packageIndex).text('');
                changeReturnShippingCost(packageIndex,'');
				changeCostWeight(packageIndex,'');
				return ;
			}
		}else{
			$("#span-l_"+packageIndex).text("");
			$("#span-w_"+packageIndex).text("");
			$("#span-h_"+packageIndex).text("");
		}
        var param = $('#domain').serialize() + "&shippingCompany="+shippingCompany+"&weight="+weight+"&state="+state+
		"&length="+length+"&width="+width+"&height="+height+"&volumeWeight="+volumeWeight;

        $.get(CONTEXT_PATH + "returnPurchaseOrder/getShippingCost", param, function(data){
            if (data.status == 200) {
				$('#strong-mag_'+packageIndex).text('');
                changeReturnShippingCost(packageIndex,parseFloat(data.body.weightCost));
                var lightGoodsFlag = data.body.lightGoodsFlag;
                if(lightGoodsFlag == null || lightGoodsFlag == undefined){
                    lightGoodsFlag = false;
                }
                $('#lightGoodsFlag_'+packageIndex).val(lightGoodsFlag);
                if (data.message == '' || data.message == null) {
                    changeCostWeight(packageIndex,weight);
				}else{
                    changeCostWeight(packageIndex,volumeWeight);
					$('#strong-mag_'+packageIndex).text(data.message);
				}
            } else {
				$('#strong-mag_'+packageIndex).text('');
                changeReturnShippingCost(packageIndex,'');
                changeCostWeight(packageIndex,'');
				$('#lightGoodsFlag'+packageIndex).val('');
				getErrorInfoAlert(data.message);
            }
        });
    }

    $('#return').on('click',function () {
        location.href = CONTEXT_PATH + "returnPurchaseOrder";
    });

    $('#save').on('click',function () {
        var param = $('#domain').serialize();
        // 用于判断是否校验通过
        var checkResult = true;
        $.each($("input[id^='shippingCompany_']"),function(index,item){
            if(!$(item).val()){
                checkResult = false;
                getErrorInfoAlert("包裹"+(index+1)+"，请选择物流公司！");
                return false;
            }
        });
        if (!checkResult){
            return false;
        }

        //用于记录包裹中物流单号和包裹的映射集合
        var shippingOrderNoArray = {};
        $.each($("input[id^='returnShippingOrderNo_']"),function(index,item){
            var shippingNo = $(item).val();
            if(shippingNo) {
                var indexArray = shippingOrderNoArray[shippingNo];
                if (!indexArray){
                    indexArray = [];
                }
                indexArray.push(index+1);
                shippingOrderNoArray[shippingNo] = indexArray;
            }
        });
        $.each($("input[id^='returnShippingOrderNo_']"),function(index,item){
            var shippingNo = $(item).val();
            if(!shippingNo){
                checkResult = false;
                getErrorInfoAlert("包裹"+(index+1)+"，请输入物流单号！");
                return false;
            }
            var indexArray = shippingOrderNoArray[shippingNo];
            if (indexArray && indexArray.length > 1){
                checkResult = false;
                var indexStr = "";
                $.each(indexArray,function(index,item){
                    indexStr += item+',';
                });
                getErrorInfoAlert("包裹"+indexStr+"存在重复的物流单号！");
                return false;
            }
        });
        if (!checkResult){
            return false;
        }

        $.each($("input[id^='weight_']"),function(index,item){
            if (!reg.test($(item).val())) {
                checkResult = false;
                getErrorInfoAlert("包裹"+(index+1)+"，请输入正确的重量，支持正整数和小数!");
                return false;
            }
        });
        if (!checkResult){
            return false;
        }

        $.each($("span[id^='span-l_']"),function(index,item){
            if($(item).text() != '*'){
                return true;
            }
            var id = $(item).attr("id");
            var packageIndex = id.substr(id.indexOf("_")+1);
            var val = $("input[id='length_"+packageIndex+"']").val();
            if (!reg.test(val)){
                checkResult = false;
                getErrorInfoAlert("包裹"+(index+1)+"，长度为必填项，请输入长度值，支持正整数和小数!");
                return false;
            }
        });
        if (!checkResult){
            return false;
        }

        $.each($("span[id^='span-w_']"),function(index,item){
            if($(item).text() != '*'){
                return true;
            }
            var id = $(item).attr("id");
            var packageIndex = id.substr(id.indexOf("_")+1);
            var val = $("input[id='width_"+packageIndex+"']").val();
            if (!reg.test(val)){
                checkResult = false;
                getErrorInfoAlert("包裹"+(index+1)+"，宽度为必填项，请输入宽度值，支持正整数和小数!");
                return false;
            }
        });
        if (!checkResult){
            return false;
        }

        $.each($("span[id^='span-h_']"),function(index,item){
            if($(item).text() != '*'){
                return true;
            }
            var id = $(item).attr("id");
            var packageIndex = id.substr(id.indexOf("_")+1);
            var val = $("input[id='height_"+packageIndex+"']").val();
            if (!reg.test(val)){
                checkResult = false;
                getErrorInfoAlert("包裹"+(index+1)+"，高度为必填项，请输入高度值，支持正整数和小数!");
                return false;
            }
        });
        if (!checkResult){
            return false;
        }

        $.each($("input[id^='returnShippingCost_']"),function(index,item){
            if (!reg.test($(item).val())) {
                checkResult = false;
                getErrorInfoAlert("包裹"+(index+1)+"，请输入正确的运费，支持正整数和小数!");
                return false;
            }
        });
        if (!checkResult){
            return false;
        }

        if (!reg.test($('#returnShippingCost').val())) {
            checkResult = false;
			getErrorInfoAlert("请输入正确的运费，支持正整数和小数!");
            return false;
        }
        if (!checkResult){
            return false;
        }

        $.post(CONTEXT_PATH + "returnPurchaseOrder/save", param, function(data) {
            if(data.status == 200){
                location.href = CONTEXT_PATH + "returnPurchaseOrder";
            }else {
				getErrorInfoAlert("提交失败！" + data.message);
            }
        });
    });

    // 删除包裹信息
    function deletePackage(packageIndex){
        changeReturnShippingCost(packageIndex,'');
        changeCostWeight(packageIndex,'');
        $('#package_'+packageIndex).remove();
    }

    function appendPackage(){
        var lastChildrenId = $("#packages").children(":last-child").attr("id");
        var packageIndex = lastChildrenId.substr(lastChildrenId.indexOf("_")+1);
        packageIndex = parseInt(packageIndex) + 1;
        var shippingCompany = $("input[id^='shippingCompany_']")[0].value;
        var volumeWeightFlag = $("input[id^='volumeWeightFlag_']")[0].value;
        var returnOrderNo = $("input[id^='returnOrderNo_']")[0].value;
        var html = `<div id="package_`+packageIndex+`">
                    <input type="hidden" id="returnOrderNo_`+packageIndex+`" name="returnPurchaseOrders[`+packageIndex+`].returnOrderNo" value="`+returnOrderNo+`"/>
                    <input type="hidden" id="lightGoodsFlag_`+packageIndex+`" name="returnPurchaseOrders[`+packageIndex+`].lightGoodsFlag" value=""/>
                    <input type="hidden" id="volumeWeightFlag_`+packageIndex+`" name="returnPurchaseOrders[`+packageIndex+`].volumeWeightFlag" value="`+volumeWeightFlag+`"/>
                    <div class="form-group">
                        <label class="control-label col-md-1">
                            <strong style="float:left;">包裹`+(packageIndex+1)+`</strong>
                        </label>
                        <div class="col-md-1">
                            <button class="btn blue" onclick="deletePackage(`+packageIndex+`);return false;">删除包裹</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-1">物流单号</label>
                        <div class="col-md-1">
                            <input class="form-control" id="returnShippingOrderNo_`+packageIndex+`" type="text" name="returnPurchaseOrders[`+packageIndex+`].returnShippingOrderNo" value="">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-1">物流公司</label>
                        <div class="col-md-1">
                            <input class="form-control" type="text" id="shippingCompany_`+packageIndex+`"  name="returnPurchaseOrders[`+packageIndex+`].shippingCompany" value="`+shippingCompany+`">
                        </div>

                        <label class="control-label col-md-1">重量</label>
                        <div class="col-md-1">
                            <input class="form-control" type="text" id="weight_`+packageIndex+`" name="returnPurchaseOrders[`+packageIndex+`].weight" onkeypress="if(event.keyCode==13) { checkWeight(this,`+packageIndex+`); return false;}" tabindex="4"
                                   value="">
                        </div>
                        <label class="control-label col-md-1" >体积</label>
                        <label class="control-label col-md-1" style="width:88px;;color: #787676;" ><span style="color: red;" id="span-l_`+packageIndex+`">*</span>长(CM)</label>
                        <div class="col-md-1">
                            <input class="form-control" type="text" id="length_`+packageIndex+`" name="returnPurchaseOrders[`+packageIndex+`].length" onkeypress="if(event.keyCode==13) { checkWeight(this,`+packageIndex+`); return false;}" tabindex="4"
                                   value="" >
                        </div>
                        <label class="control-label col-md-1" style="width:88px;color: #787676;"><span style="color: red;" id="span-w_`+packageIndex+`">*</span>宽(CM)</label>
                        <div class="col-md-1">
                            <input class="form-control" type="text" id="width_`+packageIndex+`" name="returnPurchaseOrders[`+packageIndex+`].width" onkeypress="if(event.keyCode==13) { checkWeight(this,`+packageIndex+`); return false;}" tabindex="4"
                                   value="">
                        </div>
                        <label class="control-label col-md-1" style="width:88px;color: #787676;"><span style="color: red;" id="span-h_`+packageIndex+`">*</span>高(CM)</label>
                        <div class="col-md-1">
                            <input class="form-control" type="text" id="height_`+packageIndex+`" name="returnPurchaseOrders[`+packageIndex+`].height" onkeypress="if(event.keyCode==13) { checkWeight(this,`+packageIndex+`); return false;}" tabindex="4"
                                   value="">
                        </div>
                        <label class="control-label col-md-1">体积重</label>
                        <div class="col-md-1">
                            <input class="form-control" type="text" id="volumeWeight_`+packageIndex+`" name="returnPurchaseOrders[`+packageIndex+`].volumeWeight" readonly="readonly" value="">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-1">计费重量</label>
                        <div class="col-md-1">
                            <input class="form-control" type="text" id="costWeight_`+packageIndex+`" name="returnPurchaseOrders[`+packageIndex+`].costWeight" value="" readonly="readonly"/>
                        </div>
                        <label class="control-label col-md-1">运费</label>
                        <div class="col-md-1">
                            <input class="form-control" type="number" min="0.001" step="0.001" id="returnShippingCost_`+packageIndex+`" name="returnPurchaseOrders[`+packageIndex+`].returnShippingCost" value="" onblur="changeThisReturnShippingCost(`+packageIndex+`,this);"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <h5 style="color:red"><strong id = "strong-mag_`+packageIndex+`"></strong></h5>
                    </div>
                    <div class="form-group">
                        <hr></hr>
                    </div>
                </div>`
        $('#packages').append(html);
        // 对新增的元素进行事件绑定等操作
        initElementBindEvents();
    }

    // 初始化元素绑定事件
    function initElementBindEvents(){
        $("input[id^='weight_']").off('blur');
        $("input[id^='weight_']").blur(function(){
            var $this = $(this);
            if($this.val() == ''){
                $this.val("");
                return;
            }
            if (!reg.test($this.val())) {
                getErrorInfoAlert("请输入正确的重量，支持正整数和小数!");
                $this.val("");
                return;
            }
            var id = $this.attr("id");
            var packageIndex = id.substr(id.indexOf("_")+1);
            if($this.val() && packageIndex){
                getShippingCost(packageIndex);
            }
        });

        $("input[id^='length_']").off('blur');
        $("input[id^='length_']").blur(function(){
            var $this = $(this);
            if($this.val() == ''){
                $this.val("");
                return;
            }
            if (!reg.test($this.val())) {
                getErrorInfoAlert("请输入正确的长宽高，支持正整数和小数!");
                $this.val("");
                return;
            }
            var id = $this.attr("id");
            var packageIndex = id.substr(id.indexOf("_")+1);
            if($this.val() && packageIndex){
                getShippingInfo(1,packageIndex);
            }
        });

        $("input[id^='width_']").off('blur');
        $("input[id^='width_']").blur(function(){
            var $this = $(this);
            if($this.val() == ''){
                $this.val("");
                return;
            }
            if (!reg.test($this.val())) {
                getErrorInfoAlert("请输入正确的长宽高，支持正整数和小数!");
                $this.val("");
                return;
            }
            var id = $this.attr("id");
            var packageIndex = id.substr(id.indexOf("_")+1);
            if($this.val() && packageIndex){
                getShippingInfo(1,packageIndex);
            }
        });

        $("input[id^='height_']").off('blur');
        $("input[id^='height_']").blur(function(){
            var $this = $(this);
            if($this.val() == ''){
                $this.val("");
                return;
            }
            if (!reg.test($this.val())) {
                getErrorInfoAlert("请输入正确的长宽高，支持正整数和小数!");
                $this.val("");
                return;
            }
            var id = $this.attr("id");
            var packageIndex = id.substr(id.indexOf("_")+1);
            if($this.val() && packageIndex){
                getShippingInfo(1,packageIndex);
            }
        });

        $("input[id^='shippingCompany_']").off('change');
        $("input[id^='shippingCompany_']").change(function () {
            var id = $(this).attr("id");
            var packageIndex = id.substr(id.indexOf("_")+1);
            getShippingInfo(2,packageIndex);
        });

        var shippingCompanyArray = ${domain.shippingCompanys};
        $("input[id^='shippingCompany_']").select2({
            data : shippingCompanyArray,
            placeholder : "物流公司",
            multiple: false,
            allowClear : true
        });
    }

    // 用于修改重量的变化，packageIndex为包裹标识，newValue为计费重量的新值
    function changeCostWeight(packageIndex,newValue){
        var idPrefix = 'costWeight';
        $('#'+idPrefix+'_'+packageIndex).val(newValue);
        calculateAllValue(idPrefix);
    }

    //packageIndex 为包裹索引，obj为输入的this对象
    function changeThisReturnShippingCost(packageIndex,obj){
        var objVal = $(obj).val();
        if (!reg.test(objVal)) {
            getErrorInfoAlert("请输入正确的运费，支持正整数和小数!");
            $(obj).val("");
            calculateAllValue('returnShippingCost');
            return;
        }
        changeReturnShippingCost(packageIndex,objVal);
    }

    // 用于修改运费的变化，packageIndex为包裹标识，newValue为计费重量的新值
    function changeReturnShippingCost(packageIndex,newValue){
        var idPrefix = 'returnShippingCost';
        $('#'+idPrefix+'_'+packageIndex).val(newValue);
        calculateAllValue(idPrefix);
    }

    function calculateAllValue(idPrefix){
        var newAllCostValue = 0;
        var idPrefixList = $.each($('input[id^='+idPrefix+'_]'),function(index,item){
            var val = $(item).val();
            if (!val){
                val = 0;
            }
            newAllCostValue += parseFloat(val);
        });
        if(newAllCostValue == 0){
            newAllCostValue = '';
        }
        $('#'+idPrefix).val(newAllCostValue);
    }

</script>
</body>
</html>