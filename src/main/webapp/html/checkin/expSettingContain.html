<div><strong>保质期SKU </strong><span style="color: #555555">必填其中任意两项，会自动计算出第三项值</span></div>
<div style="margin-top: 10px;">
    <label class="control-label col-md-1" style="width: 105px;">保质期天数：</label>
    <div class="col-md-2">
        <input class="form-control" type="text" name="days"  value="" onblur="changeDays(this)" onkeypress="if(event.keyCode==13) { changeDays(this); return false;}">
    </div>
    <label class="control-label col-md-1" style="width: 105px;">生产日期：</label>
    <div class="col-md-2">
        <input class="form-control Wdate" type="text" readonly="readonly"
               name="proDate" value=""
               onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',alwaysUseStartDate:true,onpicked:function (){ChangeValue(this)}})">
    </div>
    <label class="control-label col-md-1" style="width: 105px;">到期日期：</label>
    <div class="col-md-2">
        <input class="form-control Wdate" type="text" readonly="readonly"
               name="expDate" value=""
               onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',alwaysUseStartDate:true,onpicked:function (){ChangeValue(this)}})">
    </div>
</div>