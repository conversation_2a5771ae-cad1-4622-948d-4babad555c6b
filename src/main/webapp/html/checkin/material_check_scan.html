<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
</head>

<body>
<div>
    <div id="check_scan_datas" class="border-gray p5">
    <#if !domain.purchaseOrders>
        <#if false == domain.hasCheckIn>
            <#if domain.messageInfo??>
                <div class="alert alert-danger">
                    <h3 class="message-info" style="font-size:48px;">${domain.messageInfo}</h3>
                </div>
            </#if>

        </#if>
        <#if true == domain.hasCheckIn>
            <div class="alert alert-danger">
                <h3 class="message-info"  style="font-size:48px;">该采购单已入库！</h3>
            </div>
        </#if>
    <#else>
        <#list domain.purchaseOrders as purchaseOrder >
            <div id="common-param-${purchaseOrder_index }">
                <!--  采购单号-->
                <input type="hidden" name="materialCheckIn.purchaseOrderNo" value="${purchaseOrder.purchaseOrderNo}"/>
                <!--  快递单号-->
                <input type="hidden" name="materialCheckIn.trackingNumber" value="${(!purchaseOrder.trackingNumber)?string(purchaseOrder.expressId,purchaseOrder.trackingNumber)}"/>
                <!-- 运费-->
                <input type="hidden" name="materialCheckIn.shippingCost" value="${purchaseOrder.shippingCost}"/>
                <!-- 总重量-->
                <input type="hidden" name="materialCheckIn.totalWeight" value="${purchaseOrder.totalWeight}"/>

            </div>
            <!--  采购单号-->
            <#if (purchaseOrder.logisticsMark)??>
                <h3 class="flags-name">此包裹含有补发配件，拆箱时请注意！</h3>
            </#if>
            <h3>
                ${purchaseOrder.purchaseOrderNo } | ${purchaseOrder.expressId} | [${purchaseOrder.purchaseUserName}]
                <span class="flags-name">[耗材采购单]</span>
            </h3>
            <#list purchaseOrder.purchaseOrderItems as item >
                <table style="margin-top: 30px;"	class="table table-bordered table-condensed purchase-order">
                    <colgroup>
                        <col width="10%" />
                        <col width="10%" />
                        <col width="20%" />
                        <col width="10%" />
                        <col width="10%" />
                        <col width="10%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="10%" />
                    </colgroup>
                    <thead>
                    <tr>
                        <th>图片</th>
                        <th>耗材货号</th>
                        <th>耗材名称</th>
                        <th>箱规</th>
                        <th>尺寸</th>
                        <th>入库单位</th>
                        <th>采购数量</th>
                        <th>已入库数量</th>
                        <th>入库中数量</th>
                        <th>本次入库数量</th>
                        <th>耗材供应商</th>
                        <th>包裹收货时间</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody>

                        <tr class="purchase-tr" id="purchase-tr-${purchaseOrder_index }-${item_index}">
                            <td>
                                <#if item.material.images??>
                                    <img onclick="getMaterialImages('${item.material.images?join(',')}')" alt="" border="0" width="60px" height="60px" src="${item.material.images[0]}">
                                </#if>
                            </td>
                            <td>
                                ${item.sku}
                                <input type="hidden" name="purchaseOrderItem.cancelHandleStatus" value="${item.cancelHandleStatus}">
                                <input type="hidden" name="materialCheckIn.checkInItem.articleNumber" value="${item.sku}">
                                <input type="hidden" name="materialCheckIn.checkInItem.purchasePrice" value="${item.price}">
                                <input type="hidden" name="materialCheckIn.checkInItem.purchaseQuantity" value="${item.quantity}">
                            </td>
                            <td>
                                ${item.material.name}
                            </td>
                            <td>
                                ${item.material.boxSpecification}
                            </td>
                            <td>
                                ${item.material.length} * ${(!(item.material.width)??)?string('-',item.material.width)} * ${(!(item.material.height)??)?string('-',item.material.height)}
                            </td>
                            <td>
                                ${item.material.unit}
                            </td>
                            <td style="text-align: center;font-size: 20px;">
                                <span class="purchase-quantity-minus-revoked" style="font-size: larger">${item.quantity - item.revokedQuantity}</span>
                                <!--  取消在途数量-->
                                <span class="revoked-quantity" style="display: none">${item.revokedQuantity }</span>
                                <!--  采购数量-->
                                <span class="purchase-quantity" style="display: none">${item.quantity}</span>
                                <!--  待取消在途数量-->
                                <#if item.waitHandlerQ?? && item.waitHandlerQ gt 0>
                                    <br/><br/><span style="color: red;">待取消: </span>
                                    <span class="waitHandlerQ" style="color: red">${item.waitHandlerQ }</span>
                                <#else>
                                    <span class="waitHandlerQ" style="display: none">0</span>
                                </#if>
                            </td>
                            <td style="text-align: center;color: red;font-size: 20px;">
                                <!--已经确认状态的入库数-->
                                <span class="instocked-quantity">${item.inedQuantity }</span>
                            </td>
                            <td style="text-align: center;color: red;font-size: 20px;">
                                <!--  未确认入库数量-->
                                <span class="undefined-quantity">${item.undefinedInQuantity }</span>
                            </td>
                            <td>
                                <input number="true" min="0" digits="true"  required="true" name="materialCheckIn.checkInItem.quantity" class="form-control instock-quantity" value="">
                                <#if item.revokedQuantity gt 0 >
                                    <span class="" style="color: red;font-size: 16px;">有取消在途${item.revokedQuantity}个，请联系采购</span>
                                </#if>
                            </td>
                            <td>
                                <input type="text" name="materialCheckIn.checkInItem.supplier" class="form-control" value=""/>
                            </td>
                            <td>
                                <input type="text" name="materialCheckIn.checkInItem.packageAcceptanceDate" readonly="readonly" class="form-control" value="${.now?string('yyyy-MM-dd HH:mm:ss')}" onclick="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})"/>
                            </td>
                            <td style="text-align: center;">
                                <button type="button" class="btn btn-default" id="single-build-btn-${purchaseOrder_index }-${item_index}" onclick="buildInStock(${purchaseOrder_index }, ${item_index});">
                                    <i class="icon-plus"></i>提交
                                </button>
                            </td>
                        </tr>

                    </tbody>
                </table>
            </#list>
        </#list>
    </#if>
    <script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
    <script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
    <script type="text/javascript">
        $(document).ready(function(){
            // 保证(入库数量 + 已入库数量 + 已入库数量)小于等于采购数量
            $(".instock-quantity").blur(function(){
                var $this = $(this);
                if($this.val() == ''){
                    $this.val("");
                    return;
                }

                var reg = /^\+?[1-9][0-9]*$/;
                if (!reg.test($this.val())) {
                    $this.val("");
                    layer.alert("请输入正确的正整数");
                    return;
                }

                // 当前入库数量
                var currentInstockQuantity = parseInt($this.val());

                // 所在的行
                var currentTr = $this.closest(".purchase-tr");

                // 已入库数量
                var instockedQuantity = parseInt(currentTr.find(".instocked-quantity").text());

                //从采购单生成入库单的数量（入库单未确认入库数）
                var undefinedInQuantity = parseInt(currentTr.find(".undefined-quantity").text());

                // 取消在途数量
                var revokedQuantity = parseInt(currentTr.find(".revoked-quantity").text());
                // 待取消在途
                var waitHandlerQ = parseInt(currentTr.find(".waitHandlerQ").text());

                // 采购数量
                var purchaseQuantity = parseInt(currentTr.find(".purchase-quantity").text());

                //当前入库数+未确认入库数量+已入库数量+取消在途数量 >采购数量
                if((currentInstockQuantity + undefinedInQuantity + revokedQuantity + instockedQuantity + waitHandlerQ) > purchaseQuantity) {
                    if((purchaseQuantity - undefinedInQuantity-instockedQuantity-revokedQuantity - waitHandlerQ)<0){
                        $this.val("");
                    } else{
                        $this.val("");
                    }
                }

            }).keyup(function () {
                if (event.keyCode == 13) {
                    $(this).blur();
                }
            });
        });

        function getMaterialImages(images) {
            if(images != ''){
                dialog({
                    title: '包材图片',
                    url: CONTEXT_PATH + "checkInException/getExceptionImages?exceptionImages="+images,
                    width: 800,
                    height: 680,
                    top: 0
                }).showModal();
            }

        }
        function buildInStock(parentIndex, index) {
            // 当前行
            var currentTr = $("#purchase-tr-" + parentIndex + "-" + index);
            var cancelHandleStatus = currentTr.find("[name='purchaseOrderItem.cancelHandleStatus']").val();

            // 当前入库数量
            var currentInstockQuantity = parseInt(currentTr.find('.instock-quantity').val() == '' ? 0 : currentTr.find('.instock-quantity').val());

            // 已入库数量
            var instockedQuantity = parseInt(currentTr.find(".instocked-quantity").text());

            //从采购单生成入库单的数量（入库单未确认入库数）
            var undefinedInQuantity = parseInt(currentTr.find(".undefined-quantity").text());

            // 取消在途数量
            var revokedQuantity = parseInt(currentTr.find(".revoked-quantity").text());

            // 待取消在途
            var waitHandlerQ = 0;
            if(cancelHandleStatus == 'wait_Handler' || cancelHandleStatus == 'part_Handler'){
                //待发处理状态  "wait_Handler","待处理","part_Handler","部分处理","already_Handler","已处理"
                waitHandlerQ = parseInt(currentTr.find(".waitHandlerQ").text());
            }

            // 采购数量
            var purchaseQuantity = parseInt(currentTr.find(".purchase-quantity").text());

            //当前入库数+未确认入库数量+已入库数量+取消在途数量+入库异常数量+当前入库异常数量 + 待取消
            var count = currentInstockQuantity + undefinedInQuantity + revokedQuantity + instockedQuantity + waitHandlerQ;

            var reg = /^\+?[1-9][0-9]*$/;
            if (currentInstockQuantity > 0 && !reg.test(currentInstockQuantity)) {
                layer.alert("良品数量输入错误，请输入正确的正整数");
                return;
            }

            //良品数量 不能大于入库数量
            if( count > purchaseQuantity) {
                layer.alert("当前入库良品数量不能大于采购数量！");
                return;
            }

            if(currentInstockQuantity <= 0){
                layer.alert("良品数量或不良品数量为空，请重新输入！");
                return;
            }

            var params = $("#common-param-" + parentIndex + ", #purchase-tr-" + parentIndex + "-" + index).regionSerialize()
                +"&purchaseQuantity="+purchaseQuantity;

            doBuild(currentTr, parentIndex, index, params);
        }

        function doBuild(currentTr, parentIndex, index, params) {
            $.ajax({
                url: "${CONTEXT_PATH}materialCheckIn/checkIn",
                type: "POST",
                dataType: "json",
                data: params,
                error: function () {
                    App.unblockUI();
                    layer.alert("系统内部出错，请稍后重试。");
                },
                beforeSend: function () {
                    App.blockUI();
                },
                success: function (data) {
                    App.unblockUI();
                    if (data.exceptionCode && data.exceptionCode == '50000000') {
                        $.error(data.message);
                        return;
                    }
                    if (data.status == 500) {
                        layer.alert('点数入库失败！错误信息：' + data.message);
                        return;
                    }
                    // 设置入库单ID
                    if (data.body.inStocks && data.body.inStocks.length > 0) {
                        var inStock = data.body.inStocks[0];

                        currentTr.css('background-color', '#AFEEEE');// 成功后改变背景颜色

                        // 清空中间区域
                        currentTr.find('.instock-quantity').each(function () {

                            var $this = $(this);

                            // 当前入库数量
                            var currentInstockQuantity = parseInt($this.val() == '' ? 0 : $this.val());

                            // 已入库数量
                            var instockedQuantity = parseInt(currentTr.find(".instocked-quantity").text());

                            //从采购单生成入库单的数量（入库单未确认入库数）
                            var undefinedInQuantity = parseInt(currentTr.find(".undefined-quantity").text());

                            // 取消在途数量
                            var revokedQuantity = parseInt(currentTr.find(".revoked-quantity").text());

                            var purchaseQuantity = parseInt(currentTr.find(".purchase-quantity").text());// 采购数量

                            //当前入库数+未确认入库数量+已入库数量 +取消在途>采购数量
                            if ((currentInstockQuantity + undefinedInQuantity + instockedQuantity + revokedQuantity) >= purchaseQuantity) {
                                $("#single-build-btn-" + parentIndex + "-" + index).remove();
                            }
                            // 未确认,当前入库数量数量改变
                            currentTr.find(".undefined-quantity").html(undefinedInQuantity + currentInstockQuantity);
                            currentTr.find(".check_in_quantity").html(undefinedInQuantity + currentInstockQuantity + instockedQuantity);
                            $this.val('');
                            $("#purchase-exp-" + parentIndex + "-" + index).find('input').val('');
                        }); //end each
                    } else {
                        layer.alert("生成入库单失败，请重新扫描后再试！错误信息: " + data.message);
                    }  // end length

                    $('#oldExpressId').val("");
                    $('#expressId').val("");
                    $('#expressId').focus();
                }
            }); // end ajax
        }
    </script>
    </div>
</div>
</body>
</html>