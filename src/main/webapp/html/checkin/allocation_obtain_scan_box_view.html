<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
	.modal-body input {
		width: 240px;
	}
	.top_bar{
    	position:fixed;top:0px;
	}
	#task-list td {
		vertical-align:middle;
	}
	.scan-a {
    	width: 100px;
    	height: 100px;
    	line-height: 85px;
    	text-align: center;
    	margin-bottom: 50px;
	}
	#d-top {
			right: 15px;
	}
</style>
</head>

<body>
	<@header method="header" active="11010000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">入库管理</a></li>
					<li class="active">调拨提货扫描</li>
				</ul>
			</div>
		</div>

		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">

			<!-- BEGIN PAGE HEADER-->

			<!-- END PAGE HEADER-->
			<div class="row">
				<div class="col-md-12" style="text-align: right;">
					<div class="form-group">
						<div class="panel-header" style="overflow: hidden;">
							<div style="text-align: left;margin-left: 180px" class="fl panel-title2" id="panel-title">
								<h1  style="color:blue;font-size:48px;">成功:<b>0</b></h1>
							</div>
						</div>
						<div style="text-align: left;margin-left: 180px" class="" id="checkIn-info">
							<!-- <h3  style="color:red;font-size:20px;">周转码: <b>R000003</b></h3>
							<h3  style="color:red;font-size:20px;">SKU: <b>A150-3</b></h3>
							<h3  style="color:red;font-size:20px;">数量: <b>5</b></h3> -->
						</div>

					</div>
					<div class="form-group">
						<label class="control-label col-md-1">提货人</label>
						<div class="col-md-2">
							<input class="form-control" id="obtainUser" name="obtainUser" type="text" value="">
						</div>

						<label class="control-label col-md-1">周转码</label>
						<div class="col-md-2">
							<input type="text" class="form-control" id="boxNo" onkeypress="if(event.keyCode==13) { inputBoxNo(this); return false;}" tabindex="4">
							<input type="hidden" id="hidden-boxNo" value="">
							<input type="hidden" id="hidden-boxSku" value="">
							<input type="hidden" id="hidden-inId" value="">
							<span id="boxNo-span" style="color: red;font-size: 20px;" class="control-inline"></span>
						</div>

						<label class="control-label col-md-1">SKU</label>
						<div class="col-md-2">
							<input type="text" class="form-control" id="uuidSku" onkeypress="if(event.keyCode==13) { inputnext(this); return false;}" tabindex="4">
							<input type="hidden" id="hidden-uuidSku" value="">
						</div>
					</div>

					<div class="clearfix"></div>

					<div>
						<div id="check_scan_datas" class="border-gray p5 col-md-11" style="min-height:500px;">

						</div>
					</div>
				</div>
			</div><!-- end row -->

		</div>
		<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.sound.js?v=${.now?datetime}"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/web-storage-cache.js"></script>
	<script type="text/javascript">
	
		var cacheKey = "obtain_success";
		var checkInItem = null;
		$(document).ready(function(){
			pageInit();
			
			var storage = new WebStorageCache();
	  		if (storage.get(cacheKey)) {
	  			lastSuc = storage.get(cacheKey);
	  			$('#panel-title').html('<h1  style="color:blue;font-size:48px;">成功：<b>'+lastSuc+'</b></h1>');
	  		}
	  		
	  		// 提货人更改事件
	  		$("#obtainUser").change(function(){
	  			showCalsf();
	  			$('#checkIn-info').html('');
	  		});
	  		
			$.getJSON(CONTEXT_PATH + "system/saleusers/userByRole?roleId=51", function(json){
				if (json) {
					$("input[name='obtainUser']").select2({
						data : json,
						placeholder : "提货人",
						allowClear : true
					});
				} else {
					$("input[name='obtainUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
				}
			});
	    });

		// 初始化
		function pageInit() {
			$('#check_scan_datas').html('');
			$('#uuidSku').val('');
			$('#hidden-uuidSku').val('');
			$('#hidden-boxNo').val("");
            $('#boxNo-span').text("");
            $('#hidden-boxSku').val("");
            $('#hidden-inId').val("");
            $('#boxNo').val("");
            $('#uuidSku').focus();
		}

		//加载入库单数据，输入周转码触发
		function inputBoxNo(obj){
			if(!obj.value || obj.value.replace(/\s/g,'') == ''){
				layer.alert("请输入周转码!", 'error');
				return ;
			}
			$('#checkIn-info').html('');
			var boxNo = $.trim(obj.value);
			var url = CONTEXT_PATH + "allocationCheckin/scans/checkBox";// 调拨入库
			$.ajax({
            	url: url,
            	data: {
            		boxNo:boxNo
            	},
            	success: function (json) {
            		if (json.status == '500') {
                    	layer.alert(json.message + '', 'error');
                    	$('#boxNo').val("");
		            	$('#boxNo').focus();
                        if(json.message.indexOf("未QC") != -1){
                            audioPlay('N_QC');
                        }
                   		return;
                	} else if (json.status == '200') {
                		var whCheckInItem = JSON.parse(json.message);
                		checkInItem = whCheckInItem;
		            	$('#hidden-boxNo').val(boxNo);
		            	$('#boxNo-span').text(boxNo);
		            	$('#hidden-boxSku').val(whCheckInItem.sku);
		            	$('#hidden-inId').val(whCheckInItem.inId);
		            	$('#boxNo').val("");
		            	$('#uuidSku').val("");
		            	$('#uuidSku').focus();
                	}
            	},
	            error:function(){
	                layer.alert('扫描失败，请重新扫描!', 'error');
	            }
			});
		}
		
		//加载SKU数据，输入SKU触发
		function inputnext(obj){
			if(!obj.value || obj.value.replace(/\s/g,'') == ''){
				layer.alert("请输入SKU!", 'error');
				$('#uuidSku').val('');
				$('#uuidSku').focus();
				return ;
			}
			
			var obtainUser = $('#obtainUser').val();
			if (obtainUser == null || obtainUser =='') {
				layer.alert("请选择提货员!", 'error');
				return ;
			}
			var uuidSku = $.trim(obj.value);
			if(uuidSku.indexOf("=") == -1) {
				layer.alert("请扫描唯一码!", 'error');
				return ;
			}
			var url = CONTEXT_PATH + "allocationCheckin/scans/upIng";// 调拨入库
			$.ajax({
				url : url,
				data : {uuid : uuidSku, obtainUser: obtainUser},
				success : function(json){
					if (json.status == '500') {
						layer.alert(json.message + '', 'error');
						$('#boxNo').val("");
						$('#boxNo').focus();
						if(json.message.indexOf("未QC") != -1){
							audioPlay('N_QC');
						}else {
							audioPlay('error');
						}
						return;
					} else if (json.status == '200') {
						audioPlay('success');

						calsf();// 提货计数
						showCheckInfo(checkInItem);

						$('#uuidSku').val('');
						$('#hidden-uuidSku').val('');
						$('#hidden-boxNo').val("");
						$('#boxNo-span').text("");
						$('#hidden-boxSku').val("");
						$('#hidden-inId').val("");
						$('#boxNo').val("");
						//$('#obtainUser').val("");
						$('#boxNo').focus();
						layer.alert("提货成功");

					}
				 },
				 error:function(){
					 layer.alert('扫描失败，请重新扫描!', 'error');
					 audioPlay('error');
				 }
			});

		}
		
		// 统计当前提货人提货的数量
	    function calsf(){
	    	var obtainUser = $('#obtainUser').val();
	    	key = obtainUser + "_" + cacheKey;
      		var storage = new WebStorageCache();
      		// 一开始没有缓冲，所以这次数量算1
      		var lastSuc = 0;
      		if (storage.get(key)) {
      			lastSuc = storage.get(key);
      			lastSuc ++;
      		} else {
      			lastSuc = 1;
      		}
      		storage.set(key, lastSuc , {exp : 5 * 60 * 60});
	    	$('#panel-title').html('<h1 style="color:blue;font-size:48px;">成功：<b>'+lastSuc+'</b></h1>');
	    }
		
		 function showCheckInfo(checkInItem){
			 var boxNo = $('#hidden-boxNo').val();
			 var html = '';
			 if (boxNo.indexOf('DB') != -1){
				 html += '<h3  style="color:red;font-size:20px;">周转码: <b>'+boxNo+'</b></h3>';
			 }
			 html += '<h3  style="color:red;font-size:20px;">SKU: <b>'+checkInItem.sku+'</b></h3>';
			 html += '<h3  style="color:red;font-size:20px;">数量: <b>'+checkInItem.quantity+'</b></h3>';
			 $('#checkIn-info').html(html);
		 }
		 
		 // 只显示，不累加
		 function showCalsf(){
		    	var obtainUser = $('#obtainUser').val();
		    	key = obtainUser + "_" + cacheKey;
	      		var storage = new WebStorageCache();
	      		
	      		var lastSuc = 0;
	      		if (storage.get(key)) {
	      			lastSuc = storage.get(key);
	      		}
	      		storage.set(key, lastSuc , {exp : 5 * 60 * 60});
		    	$('#panel-title').html('<h1 style="color:blue;font-size:48px;">成功：<b>'+lastSuc+'</b></h1>');
		  }
		 
	</script>
	<!-- END JAVASCRIPTS -->
</body>
</html>