<script type="text/javascript">


    // var clothingItemMap = ${domain.clothingItemAttr};
    var clothingItemMap = "{}";
    // initData();

    // 页面加载后初始化SKU服装尺寸
    function initData() {
        if (clothingItemMap.length==0){
            return;
        }
        // 初始化尺寸

        initClothingSize();

    }

    // 手动清空服装类型或者页面初始化SKU服装尺寸数据
    function initClothingSize(){
        // var clothingItemAttrValMap = ${domain.clothingItemAttrVal};
        var clothingItemAttrValMap = "{}";

        var theadTr = $("#clothing-thead-tr1");
        var tbodyTr1 = $("#clothing-tbody-tr1");
        var tbodyTr2 = $("#clothing-tbody-tr2");
        theadTr.html("");
        tbodyTr1.html("");
        tbodyTr2.html("");
        for(var key in clothingItemAttrValMap){
            var code = key;
            var val = clothingItemAttrValMap[code];
            if(!val){
                val = "";
            }
            if (code == "SKU") {
                $("#whCheckIn_checkInClothing_1_sku").val(val)
            }
            if (code == "CLOTHINGTYPE") {
                $("#whCheckIn_checkInClothing_1_clothingType").val(val)
            }

            var name = clothingItemMap[code];
            if (!name) continue;
            theadTr.append('<th>'+ name +'(cm)</th>');
            tbodyTr1.append('<td><input class="form-control checkIn-clothing" readonly name="whCheckIn.checkInClothing[0].'+ code +'" value="'+ val +'"></td>');
            tbodyTr2.append('<td><input class="form-control qc-clothing" onblur="chekclothingSize(this,\'' + val + '\')" name="whCheckIn.checkInClothing[1].'+ code +'" value="'+ val +'"></td>');
        }
    }

    // 服装尺寸失去焦点校验
    function chekclothingSize(obj, skuClothingSize) {
        $(obj).css("background-color","");
        debugger;
        if(!obj.value || obj.value.replace(/\s/g, '') == '') {
            alert("请填写尺寸!", 'error');
            $(obj).val('');
            return;
        }

        var size = obj.value.replace(/\s/g, '');
        var reg = /^[-\+]?\d+(\.\d+)?$/; //Double
        if(!reg.test(size)) {
            alert("请输入正确的尺寸", 'error');
            $(obj).val('');
            return;
        }
        $(obj).val(obj.value.trim());
        if(skuClothingSize != null && skuClothingSize != '' && reg.test(skuClothingSize)){
            size = size*1;
            skuClothingSize = skuClothingSize*1;
            if((size-2)>skuClothingSize || (size+2)<skuClothingSize){
                //onmouseout
                //$(tbodyTr2).find("[name='"+code+"']").css("background-color","red");
                $(obj).css("background-color","red");
            }
        }
    }
    // 提交校验服装尺寸
    function chekclothingData() {
        var tbodyTr2 = $("#clothing-tbody-tr2");
        if(tbodyTr2 != null){
            var inputArr = $(tbodyTr2).find(".checkIn-clothing");
            for(var i = 0;i<inputArr.length;i++){
                var obj = inputArr[i];
                if(!obj.value || obj.value.replace(/\s/g, '') == '') {
                    alert("请填写尺寸!", 'error');
                    return false;
                }
                var size = obj.value.replace(/\s/g, '');
                var reg = /^[-\+]?\d+(\.\d+)?$/; //Double
                if(!reg.test(size)) {
                    alert("请输入正确的尺寸", 'error');
                    $(obj).val('');
                    return false;
                }
            }
        }
        return true;
    }
</script>