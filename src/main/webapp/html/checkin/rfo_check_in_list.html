<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
    <#include "/common/webSocket.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		.form-bordered .control-label {
			width: 120px !important;
		}
	</style>
</head>
<body>
<@header method="header" active="10020000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">入库管理</a></li>
				<li class="active">退换货入库单管理</li>
			</ul>
		</div>
	</div>

	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
		<div class="row">
			<div class="col-md-12">
				<#assign query = domain.query>
				<form action="${CONTEXT_PATH}rfoCheckIn/search"
						   class="form-horizontal form-bordered form-row-stripped"
						   method="post" modelAttribute="domain" name="rfoCheckInForm" id ="domain">
					<!-- 分页信息 -->
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">

					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-1">入库单号</label>
							<div class="col-md-2">
								<input class="form-control" type="text" name="query.inIdStr" placeholder="多个查询逗号分开" value="${query.inIdStr }">
							</div>
							<label class="control-label col-md-1">退换货单号</label>
							<div class="col-md-2">
								<input class="form-control" type="text" name="query.returnFormNo" placeholder="多个查询逗号分开" value="${query.returnFormNo }">
							</div>
							<label class="control-label col-md-1">SKU</label>
							<div class="col-md-2">
								<input class="form-control" type="text" name="query.sku" placeholder="多个查询逗号分开" value="${query.sku }">
							</div>
							<label class="control-label col-md-1">单据状态</label>
							<div class="col-md-2">
								<input class="form-control" name="query.status" type="text" value="${query.status}">
							</div>
						</div>

						<div class="form-group">
							<label class="control-label col-md-1">创建人</label>
							<div class="col-md-2">
								<input class="form-control" name="query.createUser" type="text" value="${query.createUser}">
							</div>

							<label class="control-label col-md-1">上架人</label>
							<div class="col-md-2">
								<input class="form-control" name="query.upUser" type="text" value="${query.upUser}">
							</div>

							<label class="control-label col-md-1">创建时间</label>
							<div class="col-md-2">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.fromCreateTime" placeholder="" readonly="readonly" value="${query.fromCreateTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.toCreateTime" placeholder="" readonly="readonly" value="${query.toCreateTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>
							<label class="control-label col-md-1">上架时间</label>
							<div class="col-md-2">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.fromUpTime" placeholder="" readonly="readonly" value="${query.fromUpTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.toUpTime" placeholder="" readonly="readonly" value="${query.toUpTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>
						</div>
					</div>
					<div>
						<div class="pull-left" >
							<div class="btn-group">
                                <@header method="auth" authCode="CREATE_RFO_CHECK_IN_ORDER">
									<button type="button" class="btn btn-default" onclick="createRfoCheckInOrder()">
										<i class="icon-plus"></i>创建退换货入库单
									</button>
                                </@header>
							</div>
							<div class="btn-group">
								<@header method="auth" authCode="BATCH_DISCARD_RFO_CHECK_IN">
									<button type="button" class="btn btn-default" onclick="batchDiscardedRfoCheckInOrder()">
										<i class="icon-remove"></i>废弃单据
									</button>
								</@header>
							</div>
						</div>
						<div class="col-md-offset-12" style="text-align: right">
							<button type="button" class="btn btn-default" onclick="downloadRfoCheckIn()">
								<i class="icon-download"></i> 导出数据
							</button>
							<button type="button" onclick="formReset(this)" class="btn btn-default">
								<i class="icon-refresh"></i> 重置
							</button>
							<button type="submit" class="btn blue">
								<i class="icon-search"></i> 查询
							</button>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>

		<div class="row">
			<div id="fixedDiv" class="col-md-12">
				<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
					<colgroup>
						<col width="5%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="5%" />
						<col width="5%" />
						<col width="12%" />
						<col width="13%" />
						<col width="10%" />
						<col width="10%" />

					</colgroup>
					<thead>
					<tr>
						<th>全选</th>
						<th>入库单号</th>
						<th>退换货单号</th>
						<th>SKU</th>
						<th>库位</th>
						<th>入库数量</th>
						<th>不良品数量</th>
						<th>同步时间/创建人</th>
						<th>上架时间/上架人</th>
						<th>状态</th>
						<th>操作</th>
					</tr>
					</thead>
				</table>
			</div>
			<div class="col-md-12" id="task-list-warp">
				<!-- 内容 -->
				<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
					<colgroup>
						<col width="5%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="5%" />
						<col width="5%" />
						<col width="12%" />
						<col width="13%" />
						<col width="10%" />
						<col width="10%" />
					</colgroup>
					<thead>
					<tr>
						<th><input type="checkbox"  id="check-all" name="checkAll">全选</th>
						<th>入库单号</th>
						<th>退换货单号</th>
						<th>SKU</th>
						<th>库位</th>
						<th>入库数量</th>
						<th>不良品数量</th>
						<th>同步时间/创建人</th>
						<th>上架时间/上架人</th>
						<th>状态</th>
						<th>操作</th>
					</tr>
					</thead>
					<tbody>
					<#list domain.rfoCheckIns as checkIn>
						<tr>
							<td>
								<input type="checkbox" value="${checkIn.inId}" name="inIds">
							</td>
							<td>${checkIn.inId}</td>
							<td>${checkIn.returnFormNo}</td>
							<td>${checkIn.sku}</td>
							<td>${checkIn.locationNumber}</td>
							<td>${checkIn.quantity}</td>
							<td>${checkIn.badProductQuantity}</td>
							<td>
								${util('name',checkIn.createUser)}
								</br>
								${checkIn.createDate }
							</td>
							<td>
								${util('name',checkIn.upUser)}
								</br>
								${checkIn.upTime }
							</td>
							<td>
								${util('enumName', 'com.estone.checkin.enums.CheckInStatus', checkIn.status)}
							</td>
							<td>
								<button type="button" class="btn btn-info btn-xs" onclick="viewLog(${checkIn.inId}, 'rfoCheckIn')">日志</button>
							</td>
						</tr>
					</#list>
					</tbody>
				</table>
				<!-- 内容end -->
			</div>
		</div>
		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	</div>
	<!-- 入库单废弃原因内容  -->
	<div class="hide">
		<div id="discarded-content">
			入库单废弃原因：<br>
			<input type="hidden" id="discarded-reason" value="" class="form-control input-large" options="[{id:'', text:''},{id:'点错', text:'点错'},{id:'收错', text:'收错'},{id:'贴错', text:'贴错'},
						{id:'少配件', text:'少配件'},{id:'残次', text:'残次'},{id:'混料', text:'混料'},{id:'其他原因', text:'其他原因'}]" autocomplete="off">
		</div>
	</div>
	<#include "/common/footer.html">
</div>

<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/prompt-message.js"></script>
<script type="text/javascript">

    // 分页
    var total = "${domain.page.totalCount}";
	var pageNo = "${domain.page.pageNo}";
	var pageSize = "${domain.page.pageSize}";
	$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

    var heights = $("body").height();
	if(heights>910){
        $("#fixed-bottom").addClass("fixed-bottom-height")
	}

	// 状态
	var statusArray =  ${domain.statusJson};
	$("input[name='query.status']").select2({
		data : statusArray,
		placeholder : "状态",
		multiple: false,
		allowClear : true
	});
	
	// 入库人/创建人
	$.getJSON(CONTEXT_PATH + "system/saleusers/userByRole?roleId=51", function(json){
		if (json) {
			$("input[name='query.createUser']").select2({
				data : json,
				placeholder : "创建人",
				allowClear : true
			});
		} else {
			$("input[name='query.createUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
		}
	});

	// 上架人 =PDA退换货上架操作权限
	$.getJSON(CONTEXT_PATH + "system/saleusers/userByPermissionCode?permissionCode=020400", function(json){
		if (json) {
			$("input[name='query.upUser']").select2({
				data : json,
				placeholder : "上架人",
				allowClear : true
			});
		} else {
			$("input[name='query.upUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
		}
	});

	// 全选
	var checkAll = $("input[name='checkAll']");

	// 子选项
	var itemIds = $("input[name='inIds']");
	checkAll.change(
	  function () {
		  itemIds.prop("checked", $(this).prop("checked"));
		  itemIds.each(function(){
			  var f = $(this).is(":checked");
				var checkClass = $(this).prop("class");
				$("." + checkClass).each(function(){
					$(this).prop("checked",f);
				})
		  })
	  }
	);

	// 获取选中的入库单
	function getCheckedInIds() {
		var checkedInIds = $("input[name='inIds']:checked");
		return checkedInIds;
	}

	// 批量处理废弃入库单
	function batchDiscardedRfoCheckInOrder(){
		var checkedDatas = getCheckedInIds();
		if(checkedDatas.length == 0) {
			layer.alert("请选择要操作的单据", 'error');
			return;
		}
		var ids = "";
		for (var i = 0; i < checkedDatas.length; i++) {
			var check = checkedDatas[i];
			var id = $(check).val();
			ids += id;
			if (i != checkedDatas.length - 1) {
				ids += ",";
			}
		}
		var r = true;
		if (r) {

			var elem = $("#discarded-content").clone();
			getDiscardedOptions(elem.find("#discarded-reason"));
			var diglog = dialog({
				title: '废弃原因',
			    content: elem,
			    onshow: function () {
			    	elem.find("#discarded-reason").select2("open");
			    },
			    okValue: '确定',
		        ok: function () {
		        	var reason = elem.find("#discarded-reason").val();
					$.ajax({
						url:CONTEXT_PATH +"rfoCheckIn/batchDiscarded",
						type:"POST",
						data:{inIds:ids,reason:reason},
						success : function(response){
							layer.alert(response.message?response.message:"null", {closeBtn: 0}, function (index) {
								layer.close(index);
								window.location.reload();
							});
						}
					});
		        	setTimeout(function () {
			    		diglog.close().remove();
			    	}, 100);
			    	return false;
		        },
		        cancelValue: '取消',
		       	cancel: function () {}
			}).showModal();
		}
	}
	// 废弃下拉列表
	function getDiscardedOptions($component) {
		var options = eval('(' + $component.attr("options") + ')');
		if(options) {
			$component.select2({
				createSearchChoice: function(term, data) { if($(data).filter(function() { return this.text.localeCompare(term) === 0; }).length === 0) { return { id: term, text: term }; } },
				multiple: false,
				data: options
			});
		}
	}

	function createRfoCheckInOrder() {
		window.open(CONTEXT_PATH + "rfoCheckIn/create");
	}

	//导出
	function downloadRfoCheckIn() {
		var diglog = dialog({
			title:"导出",
			width: 350,
			height:100,
			url: CONTEXT_PATH + "returnFormOrder/downloadmode",
			okValue: '确定',
			ok: function () {
				var exportWindow = $(this.iframeNode.contentWindow.document.body);

				var submitForm = exportWindow.find("#submit-form");

				var exportType = submitForm.find("input[name='exportType']:checked").val();

				var params = $('#domain').serialize();

				// 导出当前选择
				if(exportType == 3) {
					var ids = getCheckedInIds();
					if(ids.length == 0) {
						layer.alert("请选择要操作的数据!");
						return false;
					} else if (ids.length > 300) {
						layer.alert("选择数量不能超过300!");
						return false;
					}
					params = params + "&" +ids.serialize();
				}
				params = params + "&exportType=" + exportType
				downloadByPostForm(params, CONTEXT_PATH + "rfoCheckIn/download");
				$("#page-no").val("1");

				setTimeout(function () {
					diglog.close().remove();
				}, 100);

				return true;
			},
			cancelValue:'取消',
			cancel: function () {}

		});
		diglog.show();
	}
</script>
</body>
</html>