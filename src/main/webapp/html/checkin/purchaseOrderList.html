<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
    <link rel="stylesheet" type="text/css" href="${CONTEXT_PATH}css/my-select.css?v=${.now?datetime}">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
        .table tbody>tr>td{
            vertical-align: middle;
			padding-bottom: unset;
		}
        .table thead > tr > th{
            vertical-align: middle;
			white-space: normal;
		}
	</style>
</head>
<body>
<@header method="header" active="10040000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">入库查询</a></li>
				<li class="active">采购列表</li>
			</ul>
		</div>
	</div>

	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
		<div class="row">
			<div class="col-md-12">
				<#assign query = domain.query>
				<form action="${CONTEXT_PATH}checkin/whPurchaseOrder/search"
						   class="form-horizontal form-bordered form-row-stripped"
						   method="post" modelAttribute="domain" name="purchaseOrderForm" id ="domain">
					<!-- 分页信息 -->
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">

					<div class="form-body">
                        <div class="form-group">
                            <label class="control-label col-md-1">采购单号</label>
                            <div class="col-md-3">
                                <input class="form-control" name="query.purchaseOrderNoStr" placeholder="请输入采购单号,支持逗号分割" type="text" value="${query.purchaseOrderNoStr}">
                            </div>
                            <label class="control-label col-md-1">快递单号</label>
                            <div class="col-md-3">
                                <input class="form-control" name="query.expressId" type="text" value="${query.expressId}">
                            </div>
                            <label class="control-label col-md-1">SKU</label>
                            <div class="col-md-3">
                                <input class="form-control" name="query.skuStr" placeholder="请输入sku,支持逗号分割" type="text" value="${query.skuStr}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-md-1">采购单状态</label>
                            <div class="col-md-3">
                                <input class="form-control" name="query.purchaseStatus" type="text" value="${query.purchaseStatus}">
                            </div>
                            <label class="control-label col-md-1">快递单状态</label>
                            <div class="col-md-3">
                                <input class="form-control" name="query.expressStatus" type="text" value="${query.expressStatus}">
                            </div>
                            <label class="control-label col-md-1">SKU状态</label>
                            <div class="col-md-3">
                                <input class="form-control" name="query.skuStatus" type="text" value="${query.skuStatus}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-md-1">采购员</label>
                            <input class="select-input" name="query.purchaseUserId" type="hidden" value="${query.purchaseUserId}">
                            <div class="col-md-3 mySelect select-lt" placeholder="采购员" value="${query.purchaseUserId}"></div>


                            <label class="control-label col-md-1">SKU标题</label>
                            <div class="col-md-3">
                                <input class="form-control" name="query.skuName" type="text" value="${query.skuName}">
                            </div>
                            <label class="control-label col-md-1">采购时间</label>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <input class="form-control Wdate" type="text" name="query.fromPurchaseDate" value="${query.fromPurchaseDate}" placeholder="" readonly="readonly" value="" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    <span class="input-group-addon">到</span>
                                    <input class="form-control Wdate" type="text" name="query.toPurchaseDate" value="${query.toPurchaseDate}" placeholder="" readonly="readonly" value="" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-md-1">预计到货时间</label>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <input class="form-control Wdate" type="text" name="query.fromDueDate" value="${query.fromDueDate}" placeholder="" readonly="readonly" value="" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    <span class="input-group-addon">到</span>
                                    <input class="form-control Wdate" type="text" name="query.toDueDate" value="${query.toDueDate}" placeholder="" readonly="readonly" value="" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>
                            <label class="control-label col-md-1">采购单类型</label>
                            <div class="col-md-3">
                                <input class="form-control" name="query.purchaseOrderType" type="text" value="${query.purchaseOrderType}">
                            </div>
                            <label class="control-label col-md-1">标签</label>
                            <div class="col-md-3">
                                <input class="form-control" name="query.orderFlag" type="text" value="${query.orderFlag}">
                            </div>
                        </div>
					</div>
					<div>
						<div class="col-md-offset-10" style="text-align: right">
                            <button type="button" class="btn btn-default" onclick="formReset(this)">
                                <i class="icon-refresh"></i> 重置
                            </button>
							<button type="submit" class="btn blue">
								<i class="icon-search"></i> 查询
							</button>
                            <@header method="auth" authCode="PURCHASE_ORDER_DOWNLOAD">
                                <button type="button" class="btn btn-default" onclick="downloadOrder()">
                                    <i class="icon-download"></i> 导出
                                </button>
                            </@header>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>

		<div class="row">
			<div id="fixedDiv" class="col-md-12">
				<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
					<colgroup>
                        <col width="5%" />
                        <col width="8%" />
                        <col width="8%" />
                        <col width="6%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="15%" />
                        <col width="5%" />
                        <col width="8%" />
                        <col width="8%" />
                        <col width="5%" />
                        <col width="6%" />
					</colgroup>
					<thead>
						<tr>
							<th><input type="checkbox"  id="check-alls" name="checkAll">全选</th>
                            <th>采购单</th>
                            <th>快递单号/状态</th>
                            <th>SKU</th>
                            <th>采购数量</th>
                            <th>入库数量</th>
                            <th>图片</th>
                            <th>SKU状态</th>
                            <th>单据类型</th>
                            <th>SKU标题</th>
                            <th>采购员</th>
                            <th>采购时间</th>
                            <th>预计到货时间</th>
                            <th>采购单状态</th>
                            <th>标签</th>
						</tr>
					</thead>
				</table>
			</div>
			<div class="col-md-12" id="task-list-warp">
				<!-- 内容 -->
				<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                    <colgroup>
                        <col width="5%" />
                        <col width="8%" />
                        <col width="8%" />
                        <col width="6%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="15%" />
                        <col width="5%" />
                        <col width="8%" />
                        <col width="8%" />
                        <col width="5%" />
                        <col width="6%" />
                    </colgroup>
                    <thead>
						<tr>
							<th><input type="checkbox"  id="check-alls" name="checkAll">全选</th>
                            <th>采购单</th>
                            <th>快递单号/状态</th>
                            <th>SKU</th>
                            <th>采购数量</th>
                            <th>入库数量</th>
                            <th>图片</th>
                            <th>SKU状态</th>
                            <th>单据类型</th>
                            <th>SKU标题</th>
                            <th>采购员</th>
                            <th>采购时间</th>
                            <th>预计到货时间</th>
                            <th>采购单状态</th>
                            <th>标签</th>
						</tr>
                    </thead>
                        <#list domain.whPurchaseOrders as whPurchaseOrder>
                            <#list whPurchaseOrder.items as item>
                                <tr>
                                    <#if item_index=0 >
                                        <td rowspan="${whPurchaseOrder.items ? size}">
                                            <input type="checkbox" value="${whPurchaseOrder.id}" name="ids">
                                            ${whPurchaseOrder.id}
                                        </td>
                                        <td rowspan="${whPurchaseOrder.items ? size}">
                                            ${whPurchaseOrder.purchaseOrderNo}
                                        </td>
                                        <td rowspan="${whPurchaseOrder.items ? size}">
                                            <#list whPurchaseOrder.purchaseToExpress as express>
                                                ${express.expressId}/${util('enumName', 'com.estone.checkin.enums.PurchaseExpressStatus', express.expressStatus)}<br/>
                                            </#list>
                                        </td>
                                    </#if>
                                    <td>
                                        ${item.sku}
                                    </td>
                                    <td>
                                        ${item.quantity}
                                    </td>
                                    <td>
                                        ${(!(item.upQuantity)??) ? string("0",item.upQuantity)}
                                    </td>
                                    <td>
                                        <#if whPurchaseOrder.purchaseOrderType == 'NCGHC'>
                                            <img src="${item.images[0]}" width="32px" height="32px" alt="" onclick="enlarge(this)"/>
                                        <#else >
                                            <img src="${item.skuImage}" width="32px" height="32px" alt="" onclick="enlarge(this)"/>
                                        </#if>
                                    </td>
                                    <td>
                                        ${util('enumName', 'com.estone.checkin.enums.PurchaseSkuStatus', item.skuStatus)}
                                    </td>
                                    <#if item_index=0 >
                                        <td rowspan="${whPurchaseOrder.items ? size}">
                                            ${util('enumName', 'com.estone.checkin.enums.PurchaseOrderType', whPurchaseOrder.purchaseOrderType)}
                                        </td>
                                    </#if>
                                    <td>
                                        <#if whPurchaseOrder.purchaseOrderType == 'NCGHC'>
                                            ${item.materialName}
                                        <#else >
                                            ${item.skuName}
                                        </#if>
                                    </td>
                                    <#if item_index=0 >
                                        <td rowspan="${whPurchaseOrder.items ? size}">
                                            ${whPurchaseOrder.purchaseUserName}
                                        </td>
                                        <td rowspan="${whPurchaseOrder.items ? size}">
                                            ${whPurchaseOrder.purchaseDate}
                                        </td>
                                        <td rowspan="${whPurchaseOrder.items ? size}">
                                            ${item.dueDate}
                                        </td>
                                        <td rowspan="${whPurchaseOrder.items ? size}">
                                            ${util('enumName', 'com.estone.checkin.enums.WmsPurchaseOrderStatus', whPurchaseOrder.purchaseStatus)}
                                        </td>
                                        <td rowspan="${whPurchaseOrder.items ? size}">
                                            ${whPurchaseOrder.flagName}
                                        </td>
                                    </#if>
                                </tr>
                            </#list>
                        </#list>
                            <td>汇总</td>
                            <td>${domain.purchaseOrderCount}</td>
                            <td>${domain.purchaseExpressCount}</td>
                            <td>${domain.purchaseSkuCount}</td>
                            <td>${domain.purchaseSkuQuantitySum}</td>
                            <td>${domain.purchaseSkuUpQuantitySum}</td>
                            <td colspan="9"></td>
                        </tr>
					<tbody>

					</tbody>
				</table>
				<!-- 内容end -->
			</div>
		</div>
		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	</div>
	<#include "/common/footer.html">
    <img id="enlarge" style='position:absolute;width:800px;height:800px;top:10%;left:15%;display:none;'/>
</div>
<div style="display:none;" id="purchase-user">
    [{"id":"", "text":""}<#list domain.purchaseUsersList as purchaseUser>,{"id":"${purchaseUser.id}", "text":"${purchaseUser.userName} - ${purchaseUser.employeeName}"}</#list>]
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.serializejson.min.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/my-select.js?v=${.now?datetime}"></script>
<script type="text/javascript">
    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";
    $("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

    var purchaseStatusArray = ${domain.purchaseStatus}
    $("input[name='query.purchaseStatus']").select2({
        data : purchaseStatusArray,
        placeholder : "采购单状态",
        multiple: false,
        allowClear : true
    });

    var purchaseOrderTypesArray = ${domain.purchaseOrderTypes}
    $("input[name='query.purchaseOrderType']").select2({
        data : purchaseOrderTypesArray,
        placeholder : "采购单类型",
        multiple: false,
        allowClear : true
    });

    var orderFlagArray = ${domain.orderFlags}
    $("input[name='query.orderFlag']").select2({
        data : orderFlagArray,
        placeholder : "标签",
        multiple: false,
        allowClear : true
    });

    var skuStatusArray = ${domain.skuStatus}
    $("input[name='query.skuStatus']").select2({
        data : skuStatusArray,
        placeholder : "SKU状态",
        multiple: false,
        allowClear : true
    });

    var expressStatusArray = ${domain.expressStatus}
    $("input[name='query.expressStatus']").select2({
        data : expressStatusArray,
        placeholder : "快递单状态",
        multiple: false,
        allowClear : true
    });

    var purchaseUserArry = jQuery.parseJSON($('#purchase-user').text());
    var mySelect = $('.mySelect').selectlt({
        isdisabled:false,
        isfiltration:true,
        selectedValue:'0',
        data:purchaseUserArry
    });
    //选中事件
    mySelect.on('select-change', function (e, data) {
        console.log('选中值:', data.data.value);
        $("input[name='query.purchaseUserId']").val(data.data.value);
    });

    // 全选
    var checkAll = $("input[name='checkAll']");

    // 子选项
    var itemIds = $("input[name='ids']");
    checkAll.change(
            function () {
                itemIds.prop("checked", $(this).prop("checked"));
                itemIds.each(function(){
                    var f = $(this).is(":checked");
                    var checkClass = $(this).prop("class");
                    $("." + checkClass).each(function(){
                        $(this).prop("checked",f);
                    })
                })
            }
    );

    // 产品图放大
    function enlarge(obj){
        var url = $(obj).attr("src");
        $("#enlarge").attr("src", url);
        $("#enlarge").show(300);
    }

    $("#enlarge").click(function() {$("#enlarge").hide(100);});

    // 获取选中的id
    function getCheckedIds() {
        var checkedIds = $("input[name='ids']:checked");
        return checkedIds;
    }

    function downloadOrder(){
        var checkedIds = getCheckedIds();
        var diglog = dialog({
            title: '导出',
            width: 800,
            height:300,
            url: CONTEXT_PATH + "apvs/downloadmode?type=1",
            okValue: '确定',
            ok: function () {
                var exportWindow = $(this.iframeNode.contentWindow.document.body);

                var submitForm = exportWindow.find("#submit-form");

                var exportType = submitForm.find("input[name='exportType']:checked").val();

                var submitFormParam = $('#domain').serialize();

                // 导出当前选择
                if(exportType == 3) {
                    if(checkedIds.length == 0) {
                        layer.alert("请选择要操作的数据");
                        return false;
                    }else if (checkedIds.length > 300) {
                        layer.alert("选择数量不能超过300!");
                        return false;
                    }
                    submitFormParam = submitFormParam + "&" +checkedIds.serialize();
                }
                submitFormParam = submitFormParam +  "&exportType=" + exportType;
                //还原分页
                $("#page-no").val("${domain.page.pageNo}");

                $.post(CONTEXT_PATH + "checkin/whPurchaseOrder/download", submitFormParam, function(data){
                    if (data.status == 200) {
                        if (data.message==null || data.message==''){
                            layer.alert('成功',function (index) {
                                layer.close(index);
                                diglog.close().remove();
                                location.reload();
                            });
                        }else{
                            customizeLayer(data.message);
                        }
                    } else {
                        customizeLayer(data.message);
                    }
                });
                return true;
            },
            cancelValue: '取消',
            cancel: function () {}
        });
        diglog.show();
    }
</script>
</body>
</html>