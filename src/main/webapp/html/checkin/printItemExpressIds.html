<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<meta content="text/html; charset=utf-8" http-equiv="Content-type">
	<title>Print</title>
	<style>
		@media print {
			.printbtn {
				display: none;
			}
		}
	</style>
</head>

<body style="padding: 0px; margin: 20px">
	<#if domain.itemExpressIds?? >
		<div class="printbtn">
			<button onclick="myPreview();">打印预览</button>
			<input type="hidden" id="print-size" value="${domain.itemExpressIds?size}">
			&nbsp;
			<button onclick="myPrint();">打印</button>
			&nbsp;
			<button onclick="myPrintDesign();">打印设计</button>
			&nbsp;
		</div>
	</#if>
	<form id="print_content">
		
		<div id="print-item-0">
		<#list domain.itemExpressIds as itemExpressId >

			<!-- 100*100 -->
			<!-- <div style="width: 95mm; height: 69mm; font-weight: 300; text-align: center;">
				<div style="width: 95mm; height: 35mm; text-align: center;">
				</div>       
				<div style="width: 95mm; height: 25mm; text-align: center;">
					<div style="width: 95mm; height: 15mm; text-align:center;">
						<img  src="${CONTEXT_PATH}servlet/barcode?keycode=${itemExpressId}&width=180&height=60">
					</div>
					<div style="text-align:center; height: 10mm; width: 95mm;margin-top: 1mm">
						${itemExpressId}
					</div>
				</div>
			</div> -->
			
			<!-- 50*25 -->
			<div style="width: 50mm; height: 24mm; font-weight: 300; text-align: center;">
				<div style="width: 50mm; height: 2mm; text-align: center;">
				</div>       
				<div style="width: 50mm; height: 22mm; text-align: center;">
					<div style="width: 50mm; height: 15mm; text-align:center;">
						<img  src="${CONTEXT_PATH}servlet/barcode?keycode=${itemExpressId}&width=180&height=60">
					</div>
					<div style="text-align:center; height: 6mm; width: 50mm;margin-top: 1mm;font-size: 12px;">
						${itemExpressId}<br/>
                        ${.now}
					</div>
				</div>
			</div>
			
			<p style="page-break-after: always; margin: 0; padding: 0; line-height: 1px; font-size: 1px;clear: both;">&nbsp;</p>
			</div><div id="print-item-${itemExpressId_index }">
		</#list>
		</div>
	</form>

	<object width="0" height="0" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" id="LODOP_OB">
		<embed width="0" height="0" type="application/x-print-lodop" id="LODOP_EM">
	</object>

	<!-- 打印插件 -->
	<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
	<script src="${CONTEXT_PATH }js/assets/plugins/jquery-1.10.2.min.js" type="text/javascript" ></script>
	<script language="javascript">
	
		var LODOP, P_ID="", waiting=false; //声明为全局变量
		function CheckIsInstall() {
			try {
				var LODOP = getLodop(document.getElementById('LODOP_OB'),
						document.getElementById('LODOP_EM'));
				if ((LODOP != null) && (typeof (LODOP.VERSION) != "undefined"))
					return LODOP.VERSION;
			} catch (err) {
				//alert("Error:本机未安装或需要升级!");
			}
			return false;
		}
		
		function myPrint() {
			myPagePrint2($("#print-size").val());
		};
		
		// 分页打印
		function myPagePrint2(size) {
			var pageSize = 20;
			var pagePai = 1;
            var length = Math.ceil(size/(pageSize));

			for (var i = 1; i <= length; i++) {
				var start = (i-1)*pageSize + 1;
				var end = i*pageSize;
				myPagePrint3(start, end);
			}
		};
		
		// 分页打印
		function myPagePrint3(start, end) {
			LODOP = getLodop(document.getElementById('LODOP_OB'), document
					.getElementById('LODOP_EM'));
			LODOP.PRINT_INIT("打印");
			var innerHtml = "";
			$("[id^='print-item-']").each(function(i, obj) {
				var index = i + 1;
				if($.trim(end) == "") {
					if(parseInt(start) == index) {
						innerHtml = $(obj).html();
						return false;
					}
				} else {
					if(index >= parseInt(start) && index <= parseInt(end)) {
						innerHtml = innerHtml + $(obj).html();
					}
				}
			});
			//LODOP.ADD_PRINT_HTM(0, 0, "100mm", "100mm", innerHtml);// 单排
			LODOP.ADD_PRINT_HTM(0, 0, "50mm", "25mm", innerHtml);// 单排
			LODOP.PRINT();
			//LODOP.PREVIEW();
		};

		function myPreview() {
			CreatePrintPage();
			LODOP.PREVIEW();
		};
		function myPrintDesign() {
			CreatePrintPage();
			LODOP.PRINT_DESIGN();
		};
		function CreatePrintPage() {
			LODOP = getLodop(document.getElementById('LODOP_OB'), document
					.getElementById('LODOP_EM'));
			LODOP.PRINT_INIT("打印");
			try {
				if (typeof (eval(CreatePrintPageWithImage)) == 'function') {
					return CreatePrintPageWithImage();
				}
			} catch (e) {
			}
			/* LODOP.ADD_PRINT_HTM(0, 0, "100mm", "100mm", document
					.getElementById('print_content').innerHTML); */
					
			LODOP.ADD_PRINT_HTM(0, 0, "50mm", "25mm", document
					.getElementById('print_content').innerHTML);
		};
	</script>
</body>
</html>