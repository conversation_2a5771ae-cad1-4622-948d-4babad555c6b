<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
    <#include "/common/include.html">
        <style type="text/css">
            #addModal .modal-body input select {
                width: 90%;
            }
            .control-label {
                margin-top: 2px;
            }
            .form-bordered .control-label {
                padding-top: 14px;
            }
            .form-horizontal .control-label {
                text-align: right;
            }
            .col-md-1 {
                padding-left: 10px;
                padding-right: 0px;
                width: 5%;
                font-size: 12px;
            }
            .col-md-2 {
                padding-left: 10px;
                padding-right: 0px;
                width: 7.5%;
                font-size: 12px;
            }
            .col-md-3{
                text-align: right;
            }
            .form-control {
                height: 30px;
                padding: 0px 4px;
                border: 1px solid #ddd;
                color: #000;
                font-size: 10px;
                font-weight: normal;
            }
            .form-bordered .form-group > div {
                padding: 4px 10px 0;
            }
            /*启用table滚动条*/
            .my-div-table{
                overflow-x: auto;
                overflow-y: auto;
                height: 654px;
                width: auto;
                padding-bottom: 38px;
                /*width:1920px;*/
            }
            /*禁用body滚动条*/
            body{
                overflow-x: hidden;
                overflow-y: hidden;
            }
            #box_info_table tr th{
                width:160px;
            }
        </style>
</head>
<body>
<@header method="header" active="12010000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li><a href="#">入库管理</a></li>
                <li class="active">保质期待发</li>
            </ul>
        </div>
    </div>

    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">
        <div class="row">
            <div class="col-md-12">
                <#assign query = domain.query>
                    <form action="${CONTEXT_PATH}expWaitShipments/search" class="form-horizontal form-bordered form-row-stripped"
                          method="post" modelAttribute="domain" name="expWaitShipmentsForm" id="domain">
                        <!-- 分页信息 -->
                        <input id="page-no" type="hidden" style="display:none" name="page.pageNo" value="1">
                        <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">

                        <div class="form-body">
                            <div class="form-group">
                                <label class="control-label col-md-1">录入人：</label>
                                <div class="col-md-2">
                                    <input class="form-control" type="text" name="query.createBy" value="${query.createBy}">
                                </div>

                                <label class="control-label col-md-1">录入时间：</label>
                                <div class="col-md-2">
                                    <input class="form-control Wdate" type="text" name="query.fromCreationDate" placeholder="" readonly="readonly" value="${query.fromCreationDate }"
                                           onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                                <label class="control-label col-md-1" style="font-weight: bolder">到</label>
                                <div class="col-md-2">
                                    <input class="form-control Wdate" type="text" name="query.toCreationDate" placeholder="" readonly="readonly" value="${query.toCreationDate }"
                                           onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>

                                <label class="control-label col-md-1">SKU：</label>
                                <div class="col-md-2">
                                    <input class="form-control" type="text" name="query.sku" placeholder="多个查询逗号分开" value="${query.sku}">
                                </div>
                            </div>

                        </div>
                        <div>
                            <div class="pull-left">
                                <div class="btn-group">
                                    <@header method="auth" authCode="EXP_WAIT_SHIPMENTS_ADD">
                                        <div style="border-radius: 4px !important;width: 100%;" type="button" class="btn btn-default" onclick="add();">
                                            <i class="icon-plus"></i> 录入待发数据
                                        </div>
                                    </@header>
                                </div>
                            </div>
                            <div class="col-md-offset-12" style="text-align: right">
                                <@header method="auth" authCode="EXP_WAIT_SHIPMENTS_DOWNLOAD">
                                    <button type="button" class="btn btn-default" onclick="download()">
                                        <i class="icon-download"></i> 导出
                                    </button>
                                </@header>
                                <button type="button" onclick="formReset(this)" class="btn btn-default">
                                    <i class="icon-refresh"></i> 重置
                                </button>
                                <button type="submit" class="btn blue">
                                    <i class="icon-search"></i> 查询
                                </button>
                            </div>
                        </div>
                    </form>
            </div>
        </div>
        <br/>
    </div>

    <div class="row">
        <div id="myFixedDiv" class="col-md-12">
            <table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
                <colgroup>
                    <col width="5%"/>
                    <col width="10%"/>
                    <col width="7%"/>
                    <col width="7%"/>
                    <col width="15%"/>
                    <col width="7%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                </colgroup>
                <thead>
                    <tr>
                        <th><input type="checkbox" id="check-all" name="checkAll">编号</th>
                        <th>SKU</th>
                        <th>待发仓库</th>
                        <th>待发数量</th>
                        <th>关联入库单</th>
                        <th>冲抵数量</th>
                        <th>入库单保质期天数</th>
                        <th>入库单保质期到期时间</th>
                        <th>创建人/创建时间</th>
                    </tr>
                </thead>
            </table>
        </div>
        <div class="col-md-12 my-div-table" id="task-list-warp">
            <!-- 内容 -->
            <table style="background-color: #ffffff" class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                <colgroup>
                    <col width="5%"/>
                    <col width="10%"/>
                    <col width="7%"/>
                    <col width="7%"/>
                    <col width="15%"/>
                    <col width="7%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                </colgroup>
                <tbody>
                <#list domain.expWaitShipmentsList as expWait>
                    <tr>
                        <td>
                            <input type="checkbox" value="${expWait.id}" name="ids">
                            ${expWait.id}
                        </td>
                        <td>${expWait.sku }</td>
                        <td>${expWait.warehouseType}</td>
                        <td>${expWait.quantity }</td>
                        <#if expWait.relationDetailJson??>
                            <#assign detail = expWait.relationDetailJson?eval>
                            <#assign inIds = expWait.relationId?split(",")>
                            <td>
                                <#list inIds as inId>
                                    ${inId }</br>
                                </#list>
                            </td>
                            <td>
                                <#list inIds as inId>
                                    ${detail[inId].relationOffsetQty }</br>
                                </#list>
                            </td>
                            <td>
                                <#list inIds as inId>
                                    ${detail[inId].expDays }</br>
                                </#list>
                            </td>
                            <td>
                                <#list inIds as inId>
                                    ${detail[inId].expDate }</br>
                                </#list>
                            </td>
                        <#else>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </#if>
                        <td>
                            ${util('name', expWait.createBy)}<br/>
                            ${expWait.creationDate}
                        </td>
                    </tr>
                </#list>
                </tbody>
            </table>
            <!-- 内容end -->
        </div>
    </div>

    <div id="fixed-bottom" class="fixed-bottom-height">
        <div id="pager"></div>
    </div>

    <div style="margin-top: 100px" class="modal fade" id="addModal" tabindex="-1" role="dialog" aria-labelledby="addModalLabel" aria-hidden="true">
        <div class="modal-dialog" style="width: 600px;height: 540px;">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="addModalLabel">添加待发数据</h4>
                </div>
                <div class="modal-body" style="height: 200px;">
                    <form>
                        <label class="control-label col-md-3">待发仓库：</label>
                        <div class="col-md-7">
                            <select class="form-control" name="warehouseType">
                                <option value="本地仓" selected>本地仓</option>
                            </select>
                        </div>
                        <label class="control-label col-md-11"><br/></label>
                        <label class="control-label col-md-3">待发SKU：</label>
                        <div class="col-md-7">
                            <input class="form-control" type="text" name="sku"/>
                        </div>
                        <label class="control-label col-md-11"><br/></label>
                        <label class="control-label col-md-3">待发数量：</label>
                        <div class="col-md-7">
                            <input class="form-control number" type="number" step="1" min="0" name="quantity"/>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">
                        取消
                    </button>
                    <button type="button" class="btn btn-primary" id="saveData">
                        确认
                    </button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->

</div>

<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript">
    document.getElementById("fixedTab").style.width = $('#task-list').css('width');
    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";
    $("#pager").initPager({total: total, pageNo: pageNo, pageSize: pageSize});

    $.getJSON(CONTEXT_PATH + "system/saleusers/userByRoleName?roleName=入库员", function(json) {
        if(json) {
            $("input[name='query.createBy']").select2({
                data: json,
                placeholder: "录入人",
                allowClear: true
            });
        } else {
            $("input[name='query.createBy']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
        }
    });

    // 全选
    var checkAll = $("input[name='checkAll']");

    // 子选项
    var itemIds = $("input[name='ids']");
    checkAll.change(
        function () {
            itemIds.prop("checked", $(this).prop("checked"));
            itemIds.each(function () {
                var f = $(this).is(":checked");
                var checkClass = $(this).prop("class");
                $("." + checkClass).each(function () {
                    $(this).prop("checked", f);
                })
            })
        }
    );

    // 获取选中的入库单
    function getCheckedIds() {
        var checkedIds = $("input[name='ids']:checked");
        var checkIds = "";
        for (var i = 0; i < checkedIds.length; i++) {
            var outId = checkedIds[i].value;
            if (i == 0) {
                checkIds += outId;
            } else {
                checkIds += "," + outId;
            }
        }
        return checkIds;
    }

    function download() {
        var diglog = dialog({
            title:"保质期临期导出",
            width: 500,
            height:210,
            url: CONTEXT_PATH + "expWaitShipments/downloadmode?type=1",
            okValue: '确定',
            ok: function () {
                var exportWindow = $(this.iframeNode.contentWindow.document.body);
                var submitForm = exportWindow.find("#submit-form");
                var exportType = submitForm.find("input[name='exportType']:checked").val();

                var ids = getCheckedIds();
                var params = $('#domain').serialize();
                if (exportType == 1){
                } else if(exportType == 3) {// 导出当前选择
                    if(ids.length == 0) {
                        layer.alert("请选择要操作的数据");
                        return false;
                    }
                    params = params +"&query.ids="+ids;
                }
                params = params + "&exportType="+exportType;
                //还原分页
                $("#page-no").val("${domain.page.pageNo}");

                downloadByPostForm(params, CONTEXT_PATH + "expWaitShipments/download?");
                $("#page-no").val("1");

                setTimeout(function () {
                    diglog.close().remove();
                }, 100);

                return true;
            },
            cancelValue:'取消',
            cancel: function () {}

        });
        diglog.show();
    }

    function add(){
        $("#addModal").modal('show');
    }

    $('#saveData').on('click',function () {
        var warehouseType = $("#addModal").find("select[name='warehouseType']").val();
        var sku = $("#addModal").find("input[name='sku']").val();
        var quantity = $("#addModal").find("input[name='quantity']").val();
        if(warehouseType == undefined || warehouseType == ''){
            layer.alert('待发仓库必填', 'error');
            return false;
        }
        if(sku == undefined || sku == ''){
            layer.alert('待发SKU必填', 'error');
            return false;
        }
        if(quantity == undefined || quantity == ''){
            layer.alert('待发数量必填', 'error');
            return false;
        }
        //3.设置提交按钮失效，以实现防止按钮重复点击
        $('#saveData').attr("disabled", true);

        var data = {
            warehouseType : warehouseType,
            sku : sku,
            quantity : quantity
        }

        $.ajax({
            url: CONTEXT_PATH + 'expWaitShipments/create',
            type: "POST",
            contentType: "application/json;charset=utf-8",
            dataType : "json",
            data: JSON.stringify(data),
            success: function(response) {
                if (response.status == '500') {
                    customizeLayer(response.message, 'error');
                } else{
                    layer.alert('录入成功！');
                }
            },
            error:function () {
                layer.alert("系统异常，操作失败!",'error');
            }
        });
        setTimeout(function() {
            //提交完成后按钮重新设置有效
            $('#saveData').removeAttr('disabled');
            $("#addModal").modal('hide');
            window.location.reload();
        }, 1500);
    });

</script>
</body>
</html>