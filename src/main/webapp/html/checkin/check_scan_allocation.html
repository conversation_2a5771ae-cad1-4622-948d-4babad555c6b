<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	</head>

	<body>
		<div>
			<div id="check_scan_datas" class="border-gray p5">

				<#if !domain.apvAllocations>
					<div class="alert alert-danger">
						<h3 style="font-size:48px;">${domain.messageInfo}</h3>
					</div>
					<#else>
						<div style="text-align: right;margin-bottom: -32px;">
							<button type="button" class="btn blue" id="finish-checkIn">完成点数</button>
						</div>
					
						<#list domain.apvAllocations as apvAllocation >
							<form class="form-horizontal submit-form mb20" name="submitForm" id="submit-form-${apvAllocation_index }" action="${CONTEXT_PATH}allocationCheckin/scans/checkIn" method="POST">
								<div id="common-param-${apvAllocation_index }">
									<!--  调拨单号-->
									<input type="hidden" name="whAllocationCheckIn.allocationOrderNo" value="${apvAllocation.allocationNo}"></input>
									<!--  箱号-->
									<input type="hidden" name="whAllocationCheckIn.bagNo" value="${domain.bagNo}"></input>
									<!--  入库类型-->
									<input type="hidden" name="whAllocationCheckIn.checkInType" value="4">
									<!--  仓库ID-->
									<input type="hidden" name="whAllocationCheckIn.warehouseId" value="${apvAllocation.deliveryWarehouseId}">
									<!--  目的仓库ID-->
									<input type="hidden" name="whAllocationCheckIn.toWarehouseId" value="${apvAllocation.destWarehouseId}">
									<!--  签收时间-->
									<input type="hidden" name="whAllocationCheckIn.recievedTimeStr" value="${apvAllocation.confirmTime}">
								</div>
								<!--  调拨单号-->
								<h3>${apvAllocation.allocationNo } | ${domain.bagNo}</h3>
								<table class="table table-bordered table-condensed allocation-order">
									<colgroup>
										<col width="7%" />
										<col width="13%" />
										<col width="20%" />
										<col width="10%" />
										<col width="5%" />
										<col width="5%" />
										<col width="5%" />
										<col width="25%" />
										<col width="5%" />
										<col width="10%" />
									</colgroup>
									<thead>
										<tr class="">
											<th>图片</th>
											<th>SKU</th>
											<th>商品名称</th>
											<th>质检</th>
											<th>已入库数量</th>
											<th>装箱数量</th>
											<th>良品数量</th>
											<th>不良品类型</th>
											<th>异常数量</th>
											<th>操作</th>
										</tr>
									</thead>
									<tbody>
										<#list apvAllocation.allocationItems as item >
											<tr class="allocation-tr allocation-tr-${item.sku}" id="allocation-tr-${apvAllocation_index }-${item_index}">
												<td><img class="sku-image" onclick="getSkuImage('${item.sku}')" alt="产品缩略图" border="0" width="80px" height="74px" src="${item.imageUrl }" /></td>
												<td>
													<a href="${CONTEXT_PATH}skus/skuDetail?sku=${item.sku }" target="_blank">${item.sku }</a>
													<input type="hidden" name="whAllocationCheckIn.whAllocationCheckInItem.sku" value="${item.sku}">
													<input type="hidden" name="whAllocationCheckIn.whAllocationCheckInItem.location" value="${item.locationNumber}">
													<input type="hidden" name="whAllocationCheckIn.whAllocationCheckInItem.boxQuantity" value="${item.boxNum}">
													<input type="hidden" name="whAllocationCheckIn.whAllocationCheckInItem.stockId" value="${item.stockId}">
													<#if item.afterSaleAndExpInfo?? && item.afterSaleAndExpInfo.afterSaleQty?? && item.afterSaleAndExpInfo.batchQty??>
														<input type="hidden" name="whAllocationCheckIn.checkInFlag" value="5,7">
													<#elseif !(item.afterSaleAndExpInfo.afterSaleQty)?? && item.afterSaleAndExpInfo.batchQty??>
														<input type="hidden" name="whAllocationCheckIn.checkInFlag" value="5">
													<#elseif !(item.afterSaleAndExpInfo.batchQty)?? && item.afterSaleAndExpInfo.afterSaleQty??>
														<input type="hidden" name="whAllocationCheckIn.checkInFlag" value="7">
													</#if>

													</br>
													</br><span style="color: red;">采购返回库位：${item.locationNumber}</span>
													<#if (item.whSku.locationNumber??) && ('WSW' == item.whSku.locationNumber)>
														</br>
														</br>
														<span class="" style="color: red;font-size: 14px;font-weight: 600">请先分配或同步库位</span>
													</#if>
												</td>
												<td>
													<dl>
														<dd id="product-name-${item.sku}">产品名称：${item.skuName}</dd>
														<dd id="product-isPackingAgain-${item.sku}">是否加工装袋：
															<#if !(item.whSku.floorLocation??) || item.whSku.floorLocation == 0>
                                                    			<span style="color: red;">否</span>
															<#else >
																<span style="color: red;">
																	${util('enumName', 'com.estone.sku.enums.ProcessType', item.whSku.floorLocation)}
																</span>
															</#if>
														</dd>
														<dd id="product-useOlderPackage-${item.sku}">是否带原包装发货：
															<#if ( item.useOlderPackage??) && (item.useOlderPackage == 'true')>
																<span style="color: red;">是</span>
															</#if>
															<#if (!item.useOlderPackage) || (item.useOlderPackage == 'false')>
																<span style="color: red;">否</span>
															</#if>
														</dd>
														<dd id="product-useOlderPackage-${item.sku}">产品标识：
															<span style="color: red;">${item.productFlag}</span>
														</dd>
													</dl>
												</td>
                                                <td style="width: 10%;">
                                                    <dl>
                                                        <dd id="product-skuDecs-${item.sku}">产品系统质检备注：
                                                            <span style="font-size: 14px;color: red;font-weight: bold;font-weight: 600">${item.skuQcCategoryDesc}</span>
                                                        </dd>
                                                        <dd id="product-skuFeature-${item.sku}">仓库质检备注：
                                                            <span style="font-size: 14px;color: red;font-weight: bold;font-weight: 600">${item.whSku.feature }</span>
                                                        </dd>
                                                    </dl>
                                                </td>
												<td style="text-align: center;color: red;font-size: 20px;">
													<!--  已入库（已经确认状态的入库数+未确认入库数量）-->
													<span class="check_in_quantity">${item.inedQuantity }</span>
													<div id="hidden-div-${apvAllocation_index }-${item_index}">
														<input type="hidden" name="l" value="${item.locationNumber}">
														<input type="hidden" name="n" value="${item.whSku.name}">
													</div>
												</td>
												<td style="text-align: center;">
													<span class="allocation-quantity" style="">${item.boxNum}</span>
													<!--已经确认状态的入库数-->
													<span class="instocked-quantity" style="display: none">${item.inedQuantity }</span>
												</td>
												<td>
													<input number="true" min="0" digits="true" required="true" name="whAllocationCheckIn.whAllocationCheckInItem.quantity" class=" form-control input-xsmall instock-quantity" readonly="readonly" value="">
												</td>
												<td>
													<input class="form-control" name="whAllocationCheckIn.whAllocationCheckInItem.exceptionTypeStr" id="item-exception-type-${apvAllocation_index }-${item_index}" type="text" value="">
												</td>
												<td>
													<input number="true" min="0" digits="true" required="true" name="whAllocationCheckIn.whAllocationCheckInItem.exceptionQuantity" class=" form-control input-xsmall exception-quantity" value="">
												</td>
												<td>
													<button type="button" class="btn btn-default btn-xs" id="single-build-btn-${apvAllocation_index }-${item_index}" onclick="buildInStock(${apvAllocation_index }, ${item_index});">
														<i class="icon-plus"></i>提交
													</button>
													<button type="button" class="btn btn-default btn-xs" style="margin-top: 10px;" onclick="printUuid('${item.sku}','${item.boxNum }')">
														<span class="icon-print"></span> 打印唯一码
													</button>
												</td>
											</tr>
										</#list>
									</tbody>
								</table>
							</form>
						</#list>
				</#if>
				<div id="images-div" class="container-fluid category-view display-none">
					<div class="" style='position:relative'>
						<div id="div_image" class="">
							<img id="display-image" src="" alt="" style="width: 800px;height: 800px" />
						</div>
						<button type="button" style="text-align: center;position:absolute;top:50%" class="btn btn-default" id="" onclick="lastImage();">
					<
				</button>
						<button type="button" style="text-align: center;position:absolute;top:50%;right:0" class="btn btn-default" id="" onclick="nextImage();">
					>
				</button>
					</div>
				</div>
				
				<div class="modal fade ui-popup" id="commit_modal" tabindex="-1" role="dialog" aria-labelledby="organization-add-label" aria-hidden="true">
					<div class="modal-dialog">
						<div class="modal-content">
							<div class="modal-header">
								<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
								<h4 class="modal-title" id="expert-modal-label">完成点数</h4>
							</div>
							<div class="modal-body form-horizontal portlet" style="height: 100px;">
								<div id="comfirm_put_sku_text"></div>
								<br/>
								<div>
									<label class="control-label col-md-2">主管密钥：</label>
									<div class="col-md-5">
										<input type="password" autocomplete="new-password" id="manager_confirm_secret_key" value="">
									</div>
								</div>
								<br/>
							</div>
							<div class="modal-footer">
								<div class="pull-left">
									<button type="button" class="btn btn-default" data-dismiss="modal">返回继续点数</button>
								</div>
								<button type="button" class="btn btn-primary" onclick="commitModal()">确定</button>
							</div>
						</div>
					</div>
				</div>
				<div id="dialog-form" title="补标数量" style="display: none">
					<table id="condition" style="border: 10px;">
						<tr>
							<td style="width: 100%;" colspan="2">
								<label style="margin-right: 20px;">SKU：</label><span style="margin-right: 20px;" id="printSku"></span>
								<label style="margin-right: 20px;">总装箱数：</label><span style="margin-right: 20px;" id="printQty"></span>
							</td>
							<td></td>
						</tr>
						<tr>
							<td style="width: 20%;">补标数量：</td>
							<td style="width: 70%;">
								<input class="form-control" type="text" name="printNum" id="printNum">
							</td>
						</tr>
					</table>
				</div>
				<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
				<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
				<script type="text/javascript" src="${CONTEXT_PATH}js/sku/expSettingCheckIn.js?v=${.now?datetime}"></script>
				<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
				<script type="text/javascript">
					// 异常类型
					//var exceptionTypeArray = jQuery.parseJSON($("#exceptionTypes").text());
					var exceptionTypeArray = ${domain.exceptionTypes}
					$("input[name='whAllocationCheckIn.whAllocationCheckInItem.exceptionTypeStr']").select2({
						data: exceptionTypeArray,
						placeholder: "异常类型",
						multiple: true,
						allowClear: true
					});
					$(document).ready(function() {
						// 保证(入库数量 + 已入库数量)小于等于装箱数量
						$(".exception-quantity").blur(function() {
							debugger;
							var $this = $(this);
							if($this.val() == '') {
								$this.val("");
								return;
							}

							var reg = /^\+?[1-9][0-9]*$/;
							if(!reg.test($this.val())) {
								getErrorInfoAlert("请输入正确的正整数");
								$this.val("");
								return;
							}

							var exceptionQuantity = parseInt($this.val());// 当前入库数量
							var currentTr = $this.closest(".allocation-tr");// 所在的行
							var currentInstockQuantity = currentTr.find(".instock-quantity").text();
							if (currentInstockQuantity == null || currentInstockQuantity == '') {
								currentInstockQuantity = 0;
							}
							currentInstockQuantity = parseInt(currentInstockQuantity);// 当前入库数量
							var instockedQuantity = parseInt(currentTr.find(".instocked-quantity").text());// 已入库数量
							var allocationQuantity = parseInt(currentTr.find(".allocation-quantity").text());// 装箱数量

							//当前入库数+异常数量+已入库数量 >装箱数量
							if((exceptionQuantity+currentInstockQuantity + instockedQuantity) > allocationQuantity) {
								getErrorInfoAlert("当前入库数+异常数量+已入库数量 >装箱数量");
								$this.val("");
								return;
							}
						});

						var images = null;
						var currentImageIndex = null;
					}); // end ready

					// 单个生成入库单
					function buildInStock(parentIndex, index) {
						var currentTr = $("#allocation-tr-" + parentIndex + "-" + index);// 当前行
						var currentSku = currentTr.find("[name='whAllocationCheckIn.whAllocationCheckInItem.sku']").val();
						var currentInstockQuantity = parseInt(currentTr.find('.instock-quantity').val() == '' ? 0 : currentTr.find('.instock-quantity').val());// 当前入库数量
						var currentExceptionQuantity = parseInt(currentTr.find(".exception-quantity").val() == "" ? 0 : currentTr.find(".exception-quantity").val());//当前入库异常数量
						var instockedQuantity = parseInt(currentTr.find(".instocked-quantity").text());// 已入库数量
						var allocationQuantity = parseInt(currentTr.find(".allocation-quantity").text());// 装箱数量
						var exceptionType = $("#item-exception-type-" + parentIndex + "-" + index).val();

						//当前入库数+已入库数量+当前入库异常数量
						var count = currentInstockQuantity + instockedQuantity + currentExceptionQuantity;

						var reg = /^\+?[1-9][0-9]*$/;
						if(currentInstockQuantity > 0 && !reg.test(currentInstockQuantity)) {
							getErrorInfoAlert("良品数量输入错误，请输入正确的正整数");
							return;
						}
						if(currentExceptionQuantity > 0 && !reg.test(currentExceptionQuantity)) {
							getErrorInfoAlert("异常数量输入错误，请输入正确的正整数");
							return;
						}

						//良品加不良品数量 不能大于入库数量
						if(count > allocationQuantity) {
							getErrorInfoAlert("当前入库良品加不良品数量不能大于装箱数量！");
							return;
						}
						
						//良品加不良品数量 不等于装箱
						if(count != allocationQuantity) {
							getErrorInfoAlert("当前入库良品加不良品数量不等于装箱数量！");
							return;
						}
						
						if((currentExceptionQuantity > 0 ) && exceptionType == '') {
							getErrorInfoAlert("请输入异常类型！");
							return;
						}
						if(currentInstockQuantity <= 0 && currentExceptionQuantity <= 0) {
							getErrorInfoAlert("良品数量或不良品数量为空，请重新输入！");
							return;
						}
						// 生成入库单
						var params = $("#common-param-" + parentIndex + ", #allocation-tr-" + parentIndex + "-" + index).regionSerialize();
						if (uuid_array.length > 0) {
							params = params +"&uuidList=" + encodeURIComponent(uuid_array);
						}
						$.ajax({
							url: "${CONTEXT_PATH}allocationCheckin/scans/checkIn",
							type: "POST",
							dataType: "json",
							data: params,
							error: function() {
								debugger;
								App.unblockUI();
								getErrorInfoAlert("系统内部出错，请稍后重试。");
							},
							beforeSend: function() {
								App.blockUI();
							},
							success: function(data) {
								App.unblockUI();

								if(data.exceptionCode && data.exceptionCode == '50000000') {
									$.error(data.message);
									return;
								}

								// 设置入库单ID
								if(data.body.inStocks && data.body.inStocks.length > 0) {
									$("#allocation-tr-" + parentIndex + "-" + index).find('.span-boxNo').text(''); // 清空中转码
									$("#allocation-tr-" + parentIndex + "-" + index).find("[name='whAllocationCheckIn.whAllocationCheckInItem.quantity']").val('');
									$("#allocation-tr-" + parentIndex + "-" + index).find("[name='whAllocationCheckIn.whAllocationCheckInItem.exceptionQuantity']").val('');
									$("#allocation-tr-" + parentIndex + "-" + index).find("[name='whAllocationCheckIn.whAllocationCheckInItem.exceptionTypeStr']").val('');

									calsf(); // 计算成功数
									var inStock = data.body.inStocks[0];
									// $("#hidden-div-" + parentIndex + "-" + index).find("[name='i']").val(inStock.inId);

									// 计件
									calsfPiece(currentInstockQuantity+ currentExceptionQuantity);

									currentTr.css('background-color', '#AFEEEE'); // 成功后改变背景颜色

									// iframe跳转打印页面
									/*if (inStock.inId) {
										var printPageUrl = '${CONTEXT_PATH}checkins/qr?';
										$('#printHtml').attr('src', printPageUrl + "s=" + currentSku + "&" + $("#hidden-div-" + parentIndex + "-" + index).regionSerialize()+ "&q="+currentInstockQuantity+"&i="+inStock.inId+"&allocationOrderNo="+inStock.allocationOrderNo);
										//自动打印
										setTimeout(IframeOnloadPrint, 100);
									}*/


									// 清空中间区域
									currentTr.find('.instock-quantity').each(function() {

										var $this = $(this);
										// var currentInstockQuantity = parseInt($this.val() == '' ? 0 : $this.val());// 当前入库数量
										// var currentExceptionQuantity = parseInt(currentTr.find(".exception-quantity").val());//当前入库异常数量
										var instockedQuantity = parseInt(currentTr.find(".instocked-quantity").text());// 已入库数量
										var allocationQuantity = parseInt(currentTr.find(".allocation-quantity").text()); // 装箱数量
										var checkInQtyStr = currentTr.find(".check_in_quantity").text();
										var checkInQuantity = 0; // 入库数量
										if (checkInQtyStr){
											checkInQuantity = parseInt(checkInQuantity);
										}

										//当前入库数+已入库数量 >装箱数量
										if((currentInstockQuantity + instockedQuantity + currentExceptionQuantity) >= allocationQuantity) {
											$("#single-build-btn-" + parentIndex + "-" + index).remove();
										}

										// 当前入库数量数量改变
										currentTr.find(".instocked-quantity").text(instockedQuantity + currentInstockQuantity + currentExceptionQuantity);
										currentTr.find(".check_in_quantity").html(instockedQuantity + currentInstockQuantity );
										$this.val('');
									}); //end each
								} else {
									getErrorInfoAlert("生成入库单失败！错误信息: " + data.message);
								} // end length

								$('#bagNo').val("");
								$('#bagNo').focus();

							    var locationNumber = currentTr.find("input[name='whAllocationCheckIn.whAllocationCheckInItem.location']").val();
							    var stockId = currentTr.find("input[name='whAllocationCheckIn.whAllocationCheckInItem.stockId']").val();
							    printLocationLabel(locationNumber,stockId);
							}
						}); // end ajax
					}
					const PRINT_CONFIG = {
						PAGE_SIZE: { width: "32mm", height: "18mm" },
						TEXT_AREA: { top: 21, left: 35, width: 121, height: 36 },
						FONT_SIZE: 15,
						BOLD: 1,
						TASK_NAME: "打印"
					};
					function printLocationLabel(locationNumber,stockId) {
						$.ajax({
							url: "${CONTEXT_PATH}allocationCheckin/scans/printLocationLabel",
							type: "POST",
							data: {
								locationNumber: locationNumber,
								stockId: stockId
							},
							success: function (printData) {
								try {
									if (printData.status == 200) {
										const LODOP = getLodop(document.getElementById('LODOP_OB'), document.getElementById('LODOP_EM'));

										if (!LODOP || !LODOP.PRINT_INIT) {
											throw new Error("LODOP 初始化失败");
										}

										LODOP.PRINT_INIT(PRINT_CONFIG.TASK_NAME);
										LODOP.SET_PRINT_PAGESIZE(0, PRINT_CONFIG.PAGE_SIZE.width, PRINT_CONFIG.PAGE_SIZE.height, 'Note');
										LODOP.ADD_PRINT_TEXT(
												PRINT_CONFIG.TEXT_AREA.top,
												PRINT_CONFIG.TEXT_AREA.left,
												PRINT_CONFIG.TEXT_AREA.width,
												PRINT_CONFIG.TEXT_AREA.height,
												printData.body.htmlContent
										);
										LODOP.SET_PRINT_STYLEA(0, "FontSize", PRINT_CONFIG.FONT_SIZE);
										LODOP.SET_PRINT_STYLEA(0, "Bold", PRINT_CONFIG.BOLD);
										LODOP.PRINT();
									} else {
										layer.alert("打印库区通道标签失败：" + printData.message, {
											icon: 2,
											title: '错误信息',
											time: 3500
										});
									}
								} catch (e) {
									console.error("打印异常:", e);
									alert("打印客户端初始化失败，请确认已正确安装并启动打印服务");
								}
							},
							error: function () {
								getErrorInfoAlert("打印库区通道标签时发生错误");
							}
						});
					}

                    function getSkuImage(sku){
                        dialog({
                            title: sku+'相关图片',
                            url: CONTEXT_PATH + "skus/getSkuImgs?sku="+sku,
                            width: 800,
                            height: 650,
                            top: 0
                        }).showModal();
                    }

					function hasUsedBoxNo(inputBoxNo) {
						var flag = false;
						var boxNoSpans = $('.span-boxNo');
						for(var i = 0; i < boxNoSpans.length; i++) {
							var boxNoSpan = boxNoSpans[i];
							var boxNo = $(boxNoSpan).text();
							if(boxNo != null && boxNo != '' && boxNo == inputBoxNo) {
								flag = true;
								break;
							}
						}
						return flag;
					}
					
					$('#finish-checkIn').on('click',function () {
		                var bagNo = $("input[name='whAllocationCheckIn.bagNo']").val();
		                if(bagNo && bagNo != ''){
		                    $.ajax({
		                        url : CONTEXT_PATH + "allocationCheckin/scans/batchCreateWhCheckInException",
		                        type:"POST",
		                        data : {bagNo : bagNo},
		                        success : function(data){
		                            if(data.status == 200){
										layer.confirm("成功！",{
											icon: 1,
											btn: ['确定']
										},function () {
											window.location.reload();
										})
		                            } else {
		                            	if (data.exceptionCode == 'waitManagerConfirm') {
		                            		$('#commit_modal').find('#comfirm_put_sku_text').val(null);
		                            		$('#commit_modal').find('#manager_confirm_secert_key').val(null);
		                            		$("#commit_modal").modal();
		                            		$('#commit_modal').find('#comfirm_put_sku_text').html(data.message);
		                            	} else {
											getErrorInfoAlert("完成点数失败," + data.message);
		                            	}
		                            }
		                        },
		                        error:function(){
									getErrorInfoAlert('完成点数失败!');
		                        }
		                    });
		                }else {
							getErrorInfoAlert("没有相关调拨单！");
						}
		            });
					
					// 提交编辑
					function commitModal() {
						var bagNo = $("input[name='whAllocationCheckIn.bagNo']").val();
						if(!bagNo || bagNo == '') {
							getErrorInfoAlert('未获取到箱号');
							return false;
						}
						
						var secretKey = $('#commit_modal').find('#manager_confirm_secret_key').val();
						if(!secretKey || secretKey == '') {
							getErrorInfoAlert('请输入主管密钥');
							return false;
						}
						
	                    $.ajax({
	                        url : CONTEXT_PATH + "allocationCheckin/scans/batchCreateWhCheckInException",
	                        type:"POST",
	                        data : {
	                        	bagNo : bagNo,
	                        	secretKey: secretKey
	                        },
	                        success : function(data){
	                            if(data.status == 200){
									layer.confirm('成功！',{
										icon: 1,
										btn: ['确定']
									},function () {
										window.location.reload();
									})
	                            } else {
									getErrorInfoAlert(data.message);
	                            }
	                        },
	                        error:function(){
								getErrorInfoAlert('完成点数失败!');
	                        }
	                    });
					}

					function printUuid(sku,quantity) {
						$.getJSON(CONTEXT_PATH + "expManage/checkExpSkuAndGetBatchInfo?scanSku=" + sku, function (data) {
							if (data.status == 500) {
								layer.alert(data.message, function (index) {
									layer.close(index);
								});
								return false;
							}
							$('#dialog-form>table>tbody').find('#printSku').text(sku);
							$('#dialog-form>table>tbody').find('#printQty').text(quantity);
							var expSku = false;
							if (data.body.expManages && data.body.expManages.length > 0) {
								var html = "<tr id=\"exp-sku-add-batch-tr\">\n" +
										"         <td>补标批次：</td>\n" +
										"         <td>\n" +
										"             <input id=\"checkBatchNo\" class=\"form-control dropdown-toggle\" data-toggle=\"dropdown\" value=\"\" id=\"\"/>\n" +
										"             <ul class=\"dropdown-menu my-dropdown\">\n" +
										"                 <li>\n" +
										"                     <div class=\"desc\">批次号</div>\n" +
										"                     <div class=\"percent\">到期时间</div>\n" +
										"                     <div class=\"percent\">剩余库存</div>\n" +
										"                 </li>\n";
								for (var i = 0; i < data.body.expManages.length; i++) {
									var expDate = new Date(data.body.expManages[i].expDate).format("yyyy-MM-dd");
									var batchNo = data.body.expManages[i].batchNo;
									html += "<li onclick='checkBatchNo(this)'>\n" +
											"    <div name='batchNo'>" + batchNo + "</div>\n" +
											"    <div name='expDate'>" + expDate + "</div>\n" +
											"    <div name='quantity'>" + data.body.expManages[i].quantity + "</div>\n" +
											"</li>\n";
								}
								html += "</ul></td></tr>";
								$('#dialog-form>table>tbody').append(html);
								expSku = true;
							}
							toPrintSkuQRCode(expSku,sku);
						});
					}

					function toPrintSkuQRCode(expSku,sku) {
						dialog({
							title: 'SKU补标',
							content: $('#dialog-form'),
							width: 500,
							top: 0,
							okValue: '确定',
							ok: function () {
								var reg = /^\+?[1-9][0-9]*$/;
								var num = $('#printNum').val();
								var checkBatchNo = $('#checkBatchNo').val();
								if (!reg.test(num)) {
									layer.alert("请输入正确的正整数", 'error');
									$('#printNum').val('');
									return false;
								}
								if (expSku && (checkBatchNo == null || checkBatchNo == '')) {
									layer.alert("保质期SKU请选择补标批次！", 'error');
									return false;
								}
								$.ajax({
									url: CONTEXT_PATH + "allocationCheckin/scans/printUuid",
									type: "POST",
									data: {sku: sku, quantity: num, checkBatchNo: checkBatchNo},
									success: function (data) {
										var printWindow = window.open();
										printWindow.document.write(data);
										printWindow.focus();
										clearDialog();
									}
								});
							},
							cancelValue: '关闭',
							cancel: function () {
								clearDialog();
							}
						}).showModal();
					}
					function checkBatchNo(obj){
						var batchNo = $(obj).find("div[name='batchNo']").text();
						$(obj).parent().parent().find("#checkBatchNo").val(batchNo);
					}
					function clearDialog() {
						$('#dialog-form>table>tbody').find("#exp-sku-add-batch-tr").remove()
						$('#dialog-form>table>tbody').find("#printNum").val('');
						$('#dialog-form>table>tbody').find('#printSku').text('');
						$('#dialog-form>table>tbody').find('#printQty').text('');
					}
				</script>
			</div>
		</div>
	</body>

</html>