<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<style type="text/css">
    .skuPackImage {
        padding: 10px;
        float: left;
    }
    .skuPackImagesOl {
        float: left;
        width: 138px;
        height: 400px;
        overflow: hidden;
        overflow-y: auto;
        white-space:nowrap;
    }
    .skuImgOthersOl {
        float: left;
        width: 138px;
        height: 600px;
        overflow: hidden;
        overflow-y: auto;
        white-space:nowrap;
    }
    .img-content{
        height: 800px;
    }
    .img-view{
        width: 65%;
    }
</style>

    <div id="check_scan_new_datas" class="border-gray p5">
        <#assign whSku=domain.whSku>
        <#if whSku>
        <form name="submitForm" id="submit-form"  >
            <table>
                <tr>
                    <h4>入库单为新品首单/供应商首单，请先录入SKU尺寸、重量等信息 <a onclick="skipIn(${domain.whCheckIn.inId})">跳过</a></h4>
                </tr>
                <tr valign="top">
                    <td class="img-view">
                        <table style="border-collapse:separate; border-spacing:0px 25px;">
                            <tr>
                                <td><h6 class="modal-title">SKU:${whSku.sku}</h6></td>
                            </tr>
                            <tr>
                                <td><h6 class="modal-title">SKU:${whSku.name}</h6></td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="img-content">
                                        <#include "/sku/skuImgsContain.html">
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </td>
                    <td>
                        <div  id="new_product_modal" >
                            <div class="modal-content-new-sku">
                                <h4>产品规格SKU尺寸</h4>
                                <h4>称重原因：<span style="color: red">${domain.verifyRemark}</span></h4>
                                <div>
                                   <!-- <form name="newProductForm" id="new-product-form" method="POST">-->
                                        <table style="border-collapse:separate; border-spacing:0px 25px;">
                                            <tr>
                                                <td>产品规格：</td>
                                                <td>
                                                    <div style="float:left;">
                                                        长(cm) &nbsp;<input style="display:inline;width: 70px;" placeholder="长" class="form-control" type="text" name="whSku.length" value="${whSku.length}" autocomplete="off" oninput="handleFloatInput(this)" onblur="check(this)">
                                                    </div>
                                                    <div style="float:left;margin-left: 15px;">
                                                        宽(cm) &nbsp;<input style="display:inline;width: 70px;" placeholder="宽" class="form-control" type="text" name="whSku.width" value="${whSku.width}" autocomplete="off" oninput="handleFloatInput(this)" onblur="check(this)">
                                                    </div>
                                                    <div style="float:left;margin-left: 15px;">
                                                        高(cm) &nbsp;<input style="display:inline;width: 70px;" placeholder="高" class="form-control" type="text" name="whSku.height" value="${whSku.height}" autocomplete="off" oninput="handleFloatInput(this)" onblur="check(this)" onkeypress="if(event.keyCode==13) { calculateVolume(this); return false;}" tabindex="4">
                                                    </div>
                                                </td>
                                            </tr>
                                            <!--<tr>
                                                <td>体积：</td>
                                                <td>
                                                    <input style="display:inline;width: 180px;" placeholder="体积" class="form-control" type="number" name="product_volume" value="" disabled="disabled"> cm³
                                                </td>
                                            </tr>-->
                                            <tr>
                                                <td style="vertical-align: top ;padding-top: 8px">电子秤类型：</td>
                                                <td>
                                                    <div style="float:left;">
                                                        <input type="radio" name="scaleType" value="COM" checked="checked"/>串口秤
                                                        <input type="radio" name="scaleType" value="USB"/> USB秤
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="vertical-align: top ;padding-top: 8px">称重重量：</td>
                                                <td>
                                                    <div style="float:left;">
                                                        <input id = "customsWeight" style="display:inline;width: 250px;" placeholder="单品核重" class="form-control" type="text" name="whSku.customsWeight" value="" min="0" onpaste="return false" autocomplete="off"/> g
                                                    </div>
                                                </td>
                                            </tr>

                                            <tr>
                                               <td>
                                                    <div style="float:left;">
                                                        <input type="hidden" name="whSku.sku" value="${whSku.sku }"/>
                                                        <input type="hidden" name="uuid" value="${domain.uuid }"/>
                                                        <input type="hidden" name="whSku.warehouseId" value="${whSku.warehouseId }"/>
                                                        <input type="hidden" name="inId" value= "${domain.whCheckIn.inId}"/>
                                                        <input type="hidden" name="firstOrderType" value= "${domain.firstOrderType}"/>
                                                        <input type="hidden" name="verifyRemark" value= "${domain.verifyRemark}"/>
                                                        <input type="hidden" name="firstWeighQc" value= ""/>
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>


                                    <!--</form>-->
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-primary" style="width:20%;" onclick="commitNewProduct(false)">保存</button>
                                </div>
                            </div>

                            <div class="modal fade ui-popup" id="new-product-msg-modal"  role="dialog" aria-labelledby="organization-update-label" aria-hidden="true" data-backdrop="false">
                                <div class="modal-dialog">
                                    <div class="modal-content" style="width: 380px;">
                                        <div class="modal-header">
                                            <h3 class="modal-title">提示</h3>
                                        </div>
                                        <div class="modal-body form-horizontal portlet" style="margin:30px;height:auto;">
                                            <input type="hidden" id="size-type" value="">
                                            <h4 class="modal-title" align="center">录入的数据超过100CM，请确定尺寸是否有误</h4>
                                        </div>
                                        <div class="modal-footer" style="text-align:center">
                                            <button type="button" class="btn blue" style="width:40%;" onclick="doNext()">继续</button>
                                            <button type="button" class="btn" style="width:40%;" onclick="doRewrite()">重新填写</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </#if>
    </div>

    <script>

        // 初始化图片
        $(document).ready(function(){
            var $compress=$('.skuImgOthersOl>div');
            var $mainImg=$('.skuImgMain>img');
            $compress.mousemove(function(){changeImg(this);});
            function changeImg(thisImg){
                for(var i=0; i<$compress.length; i++){
                    $compress[i].style.borderColor = '#dedede';
                }
                thisImg.style.borderColor = '#cd2426';
                $mainImg[0].src = thisImg.children[0].src;
            }
            initPackImageEvent();

        });

        // 跳过
        function skipIn(skipInId){
            // 标记入库单为新品称重，解除入库单跟QC人之间的关系
            if (!skipInId) {
                getErrorInfoAlert("未获取到入库单id");
                return false;
            }
            $.post(CONTEXT_PATH + "checkException/skus/skipNewProductWeight", {"inId": skipInId}, function(data) {
                if (data.status == 200) {
                    layer.alert("请将${whSku.sku}放到核重区域");
                    setTimeout(function() {
                        location.reload();
                    }, 3000);
                } else {
                    customizeLayer(data.message);
                }
            });
        }
        function check(obj) {
            var value = $(obj).val();
            value = value.replace(/\.0*$/,'');
            $(obj).val(value);
            if (value >= 100){
                $('#size-type').val(obj.name);
                $("#new-product-msg-modal").modal('show');
            }
        }

        function doRewrite() {
            var sizeType = $('#size-type').val();
            $("input[name='"+sizeType+"']").val('');
            $("input[name='"+sizeType+"']").focus();
            $("#new-product-msg-modal").modal('hide');
        }

        function doNext() {
            var sizeType = $('#size-type').val();
            if (sizeType == 'whSku.height'){
                var length = $("input[name='whSku.length']").val();
                var width = $("input[name='whSku.width']").val();
                var height = $("input[name='whSku.height']").val();

                if (!(length && width && height)) {
                    getErrorInfoAlert("产品规格长、宽、高 必填");
                    return false;
                }

                var volume = length * width * height;
                $("input[name='product_volume']").val(volume);
            }
            $("#new-product-msg-modal").modal('hide');
        }

        // 校验长、宽、高必填
        function calculateVolume(obj) {
            check(obj);
            var length = $("input[name='whSku.length']").val();
            var width = $("input[name='whSku.width']").val();
            var height = $(obj).val();

            if (!(length && width && height)) {
                getErrorInfoAlert("产品规格长、宽、高 必填");
                return false;
            }

            var volume = length * width * height;
            $("input[name='product_volume']").val(volume);
        }

        // 打开摄像头拍照
        function openCamera() {
            var sku = $('input[name="whSku.sku"]').val();
            var cameraUrl = CONTEXT_PATH + "skus/openCamera?sku="+sku;
            dialog({
                title: '',
                url: cameraUrl,
                width: 500,
                height: 540,
                top: 0,
                cancelValue: '关闭',
                cancel: function () {
                    var exportWindow = $(this.iframeNode.contentWindow.document.body);

                    var submitForm = exportWindow.find("#images").val();
                    if (submitForm == null || submitForm == '') {
                        return;
                    }
                    images = JSON.parse(submitForm);
                    updateOlImages(images);
                    $('#packImage>img')[0].src = images[images.length-1];
                },
                close:function () {

                }
            }).showModal();
        }

        // 上传图片
        function updateOlImages(images) {
            var  html = "";
            for (var i = 0; i < images.length; i++) {
                html += '<div class="skuImgOthers"><img src="'+images[i]+'" onerror="javascript:this.parentElement.style.display=\'none\'"/></div>';
            }
            $(".skuPackImagesOl").html(html);
            initPackImageEvent();

        }

         // 初始化图片
        function initPackImageEvent() {
            var $packImages=$('.skuPackImagesOl>div');
            var $packImage=$('#packImage>img');
            $packImages.mousemove(function(){changeImg2(this);});
            function changeImg2(thisImg){
                for(var i=0; i < $packImages.length; i++){
                    $packImages[i].style.borderColor = '#dedede';
                }
                thisImg.style.borderColor = '#cd2426';
                $packImage[0].src = thisImg.children[0].src;
            }
        }

        // 删除图片
        function removePhoto() {
            var packImage=$('#packImage>img');
            let packImageElement = packImage[0];
            var image = packImageElement.src;
            if (image == null || image.length == 0) {
                getErrorInfoAlert("还没有图片!");
                return;
            }
            let $input = $("input[name='whSku.sku']");
            $.ajax({
                url : CONTEXT_PATH + "skus/removePackImage",
                type: "POST",
                data : {sku : $input.val(), imagePath: image},
                success : function(data){
                    if (data.status == 200) {
                        alert("图片删除成功!");
                        let submitForm = data.message;
                        if (submitForm == null || submitForm == '') {
                            return;
                        }
                        images = JSON.parse(submitForm);
                        updateOlImages(images);
                        $('#packImage>img')[0].src = images[0];
                    }else {
                        getErrorInfoAlert("图片删除失败: "+data.message);
                    }
                },
                error:function(){
                    getErrorInfoAlert('图片删除失败!');
                }
            });
        }

        // 本地上传
        function fileChange(target) {

            var filename = target.value;
            if (filename == '') {
                getErrorInfoAlert("请选择需要上传的文件!");
                return;
            } else {
                var ext, idx;
                idx = filename.lastIndexOf(".");
                if (idx != -1) {
                    ext = filename.substr(idx + 1).toLowerCase();
                    if (ext != 'jpg' && ext != 'jpeg' && ext != 'png' && ext !='gif') {
                        getErrorInfoAlert("不支持" + ext + "文件上传!");
                        var file = $(target)
                        file.after(file.clone().val(""));
                        file.remove();
                        return;
                    }
                } else {
                    getErrorInfoAlert("只能上传图片文件!");
                    var file = $(target)
                    file.after(file.clone().val(""));
                    file.remove();
                    return;
                }
            }
            var files = target.files;
            if(files.length>1){
                getErrorInfoAlert("只能上传1张图片！");
                return;
            }
            var r = confirm("确定上传?");
            if(!r) {
                return;
            }
            uploadImage(files.length);
        }

        function handleFloatInput(input) {
            debugger;
            const rawValue = input.value;
            const regex = /^\d*\.?\d{0,1}$/;

            // 验证输入格式
            if (!regex.test(rawValue)) {
                input.value = rawValue.slice(0, -1).replace(/[^0-9.]/g, '');
                return;
            }

            // 仅允许一个小数点
            const parts = input.value.split('.');
            if (parts.length > 1) {
                input.value = parts[0]+"."+parts.slice(1).join('');
            }
        }

        // 本地上传图片
        function uploadImage(count){
            var uploadUrl = CONTEXT_PATH + "skus/uploadPackImage";

            var commitImages = $("#submit-form");
            var createUrl = commitImages.attr("action");
            commitImages.attr("action", uploadUrl);
            commitImages.ajaxSubmit(function(data) {
                console.log(data);
                if (data.status == 200) {
                    alert("图片上传成功!");
                    let submitForm = data.message;
                    if (submitForm == null || submitForm == '') {
                        return;
                    }
                    images = JSON.parse(submitForm);
                    updateOlImages(images);
                    $('#packImage>img')[0].src = images[images.length-1];
                }else {
                    getErrorInfoAlert("图片上传失败: "+data.message);
                }
            });
            commitImages.attr("action", createUrl);
        }

        // 搭配耗材
        var matchMaterials = ${domain.matchMaterials};
        $("input[name='whSku.matchMaterialsCode']").select2({
            data : matchMaterials,
            multiple:true,
            placeholder : "搭配耗材",
            allowClear : true
        });

        // 包材
        $("input[name='whSku.packagingId']").select2({
            data : matchMaterials,
            placeholder : "包材",
            allowClear : true
        });

        // ---------------------------------------------------------------------------------电子秤称重START--------------------------------------------------------------------------------- //
        // 初始化电子秤类型选择
        $(document).ready(function () {
            if (!localStorage.hasOwnProperty("scaleType")){
                return;
            }
            // 获取所存储的值
            var type = localStorage.getItem("scaleType");
            var scaleTypes = $("input[name='scaleType']");
            for(var i=0; i < scaleTypes.length; i++){
                var checkedResult = false;
                var scaleType = $(scaleTypes[i]).val();
                if(scaleType == type){
                    checkedResult = true;
                }
                $(scaleTypes[i]).attr("checked",checkedResult);
            }
        });

        // 注册改变选项事件
        $("input[type='radio'][name='scaleType']").on("change",function(){
            localStorage.setItem("scaleType",this.value);
        });

        // 读取电子秤称重数据
        $('#customsWeight').focus( function() {
            var scaleType = $("input[name='scaleType']:checked").val();
            if (scaleType == 'USB'){
                $('#customsWeight').val(null);
                $('#customsWeight').off("keydown");
                $('#customsWeight').on("keydown",AICheck);
                return;
            }
            // 标准重量
            var netWeightStr = '${domain.whSku.netWeight}';
            // 不允许输入
            $('#customsWeight').off("keydown");
            $('#customsWeight').on("keydown",function(){return false;});
            // 递归调用
            callWeightRxtx(netWeightStr);
        });

        // 连需调用结果
        var weightArray = new Array();
        // 称重次数
        var weightCount = 15;
        // 称重验证次数
        var validateWeightCount = 5;

        // 初始化称重数据
        function initWeightRxtx(value) {
            weightArray = new Array();
            weightCount = 15;
            $('#customsWeight').val(value);
            $('#customsWeight').blur();
        }

        //串口秤请求成功时回调
        function weightRxtxCallback(response) {
        }

        // 调用HTTP监听电子秤称重重量
        function callWeightRxtx(netWeightStr) {
            $.ajax({
                url : 'http://127.0.0.1:9999/getWeight',
                type: 'POST',
                dataType: 'jsonp',
                jsonp:'jsonCallback',
                jsonpCallback:'weightRxtxCallback',
                crossDomain: true,
                timeout: 2000,
                success : function(response) {
                    // 递归调用
                    if (weightCount <= 0) {
                        // 二十次必须全部成功
                        if (weightArray && weightArray.length < validateWeightCount) {
                            initWeightRxtx(null);
                            getErrorInfoAlert('当前电子秤不稳定，请稳定后重新称重');
                            return;
                        }
                        callMaxCountWeight(weightArray);
                        return;
                    }

                    if (response.status == '200') {
                        var result = response.result;
                        if (!result) {
                            initWeightRxtx(null);
                            getErrorInfoAlert('请连接电子秤称重');
                            return;
                        }

                        var customsWeight = Number(result);
                        // 是否放置了货物
                        if (customsWeight <= 0) {
                            initWeightRxtx(null);
                            getErrorInfoAlert('请去皮后放置SKU称重！当前称重重量：' + customsWeight);
                            return;
                        }

                        // 验证净重，称重重量不可大于已有的净重
                        var netWeightStr = '${domain.whSku.netWeight}';
                        if (netWeightStr) {
                            var netWeight = Number(netWeightStr);
                            if ((netWeight > 0) && (customsWeight > netWeight)) {
                                initWeightRxtx(null);
                                getErrorInfoAlert('称重重量大于标准重量，请核对后重新称重！当前称重重量：' + customsWeight);
                                return;
                            }
                        }

                        weightArray.push(customsWeight);
                    } else {
                        initWeightRxtx(null);
                        getErrorInfoAlert(response.message);
                        return;
                    }

                    // 递归调用
                    weightCount--;
                    setTimeout(function() {
                        callWeightRxtx(netWeightStr);
                    }, 25);
                },
                error:function() {
                    // 屏蔽由于jsonp调用而出现的弹窗界面
                    // $('.ui-popup').hide()
                    // $('.ui-popup-backdrop').hide();
                    initWeightRxtx(null);
                    getErrorInfoAlert('连接异常，请检查称重小工具是否启动');
                }
            });
        }

        // 获取电子秤稳定次数
        function callMaxCountWeight(arr) {
            var obj = {};
            for (var i = 0; i < arr.length; i++) {
                if (!obj[arr[i]]) {
                    obj[arr[i]] = 1;
                }
                else {
                    obj[arr[i]]++;
                }
            }

            // 数组中出现最多的元素的次数
            var maxCountValue = 0;
            // 数组中出现最多的元素
            var maxCountWeight = arr[0];
            for (var i in obj) {
                if (obj[i] > maxCountValue) {
                    maxCountValue = obj[i];
                    maxCountWeight = i;
                }
            }

            if (maxCountValue < validateWeightCount) {
                initWeightRxtx(null);
                getErrorInfoAlert('当前电子秤不稳定，请稳定后重新称重，稳定次数：' + maxCountValue);
                return;
            }

            initWeightRxtx(maxCountWeight);
        }
        // ---------------------------------------------------------------------------------电子秤称重END--------------------------------------------------------------------------------- //
        // ---------------------------------------------------------------------------------人工输入校验---------------------------------------------------------------------------------- //
        // 用于判断输入的是电子秤还是人。如果是人，则两次输入之间的时间间隔会大于等于20ms，否则两次输入的时间间隔会小于20ms
        AI_LIMITATION = 20;
        // 用于记录上次按按键的时间戳，未按下按键或电子秤输入结束或两次按下按键时间间隙小于等于20ms时，其会被置为0(ms)
        preTime = 0;
        // 用于记录是否在最后按下了回车键，电子秤会以回车键作为输入的结尾
        pressEnterCode=true;
        // 按钮常量
        BACKSPACE_CODE = 8;
        DELETE_CODE = 46;
        ENTER_CODE = 13;
        function AICheck(){
            //得到所按按键
            var code;
            if(window.event){ // IE8 及更早IE版本
                code=event.keyCode;
            }else if(event.which){ // IE9/Firefox/Chrome/Opera/Safari
                code=event.which;
            }
            // 禁止掉删除的相关按钮，防止手工编辑
            if(code && (code == BACKSPACE_CODE || code == DELETE_CODE)){
                return false;
            }

            if(preTime == 0){
                preTime = Date.now();
                // 监测输入，如果10倍限制时间内preTime不为0，则说明为手工输入的值
                setTimeout(function () {
                    if(preTime!= 0 || !pressEnterCode){
                        $('#customsWeight').val(null);
                    }
                },AI_LIMITATION * 10);
            }else{
                var span = Date.now() - preTime;
                preTime = 0;
                if(span > AI_LIMITATION){
                    $('#customsWeight').val(null);
                    return false;
                }
            }

            // 电子秤会以ENTER为结尾，表示输入结束
            if(code == ENTER_CODE){
                preTime = 0;
                pressEnterCode = true;
            }else{
                pressEnterCode = false;
            }

            return true;
        }
    </script>
