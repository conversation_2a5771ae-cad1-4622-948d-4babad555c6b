<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.content-title{
			margin-bottom: 60px;
		}
		.content-title label{
			text-align: right;
			margin-top: 0px !important;
		}
		.readonly-row{
			padding: 20px 20px 0px 20px;
		}
		.write-row{
			background: white;
			padding: 20px 20px 0px 20px;
		}
		.btn-float a{
			margin: 10px 20px;
		}
		.table-striped td img{
			width: 80px;
			height: 80px;
		}
		.table tbody>tr>th,td{
			text-align: center;
			vertical-align: middle !important;
		}
		.page-info{
			background-color: #f2f2f2;
		}
		.content-title-last{
			height: 10px;
		}
		.page-table{
			background-color: white;
			width: 70%;
		}
	</style>
</head>
<body>
<@header method="header" active="10020000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">到货管理</a></li>
				<li class="active">入库单明细</li>
			</ul>
		</div>
	</div>

	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
		<#assign whCheckIn = domain.whCheckIn>
		<#assign item = domain.whCheckIn.whCheckInItem>
		<div class="write-row">
			<h4><strong>入库单编号${whCheckIn.inId}<#if whCheckIn.isAllCheck>(全检)</#if></strong></h4>
		</div>

		<div class="row readonly-row">
			<div class="col-md-12 page-info">
				<div class="form-body">
					<div class="form-group content-title">
						<label class="control-label col-md-1">采购单号：</label>
						<div class="col-md-2">
							${whCheckIn.purchaseOrderNo}
						</div>
						<label class="control-label col-md-1">SKU：</label>
						<div class="col-md-2">
							${item.sku}
						</div>
						<label class="control-label col-md-1">系统加工状态：</label>
						<div class="col-md-2">
							${util('enumName', 'com.estone.sku.enums.ProcessType', whCheckIn.upSystemPacking)}
						</div>
						<label class="control-label col-md-1">签收时间：</label>
						<div class="col-md-2">
							<#if whCheckIn.whPurchaseExpressRecord?? >
								${whCheckIn.whPurchaseExpressRecord.getCreationDate() }
							</#if>
						</div>
					</div>
					<div class="form-group content-title content-title-last">
						<label class="control-label col-md-1">快递单号：</label>
						<div class="col-md-2">
							${whCheckIn.trackingNumber}
						</div>
						<label class="control-label col-md-1">点数良品数：</label>
						<div class="col-md-2">
							${item.quantity}
						</div>
						<label class="control-label col-md-1">QC加工状态：</label>
						<div class="col-md-2">
							${util('enumName', 'com.estone.sku.enums.ProcessType', whCheckIn.qcPacking)}
						</div>
						<label class="control-label col-md-1">贴标人/贴标时间：</label>
						<div class="col-md-2">
							${util('name',whCheckIn.tagUser)}/${whCheckIn.tagTime }
						</div>
					</div>
					<div class="form-group content-title content-title-last">
						<label class="control-label col-md-1">状态：</label>
						<div class="col-md-2">
							${whCheckIn.statusName }
						</div>
						<label class="control-label col-md-1">QC良品数：</label>
						<div class="col-md-2">
							${item.qcQuantity}
						</div>
						<label class="control-label col-md-1">来货包装属性：</label>
						<div class="col-md-2">
							${item.checkInPackageAttrName}
						</div>
						<label class="control-label col-md-1">QC时间：</label>
						<div class="col-md-2">
							${whCheckIn.qcTime }
						</div>
					</div>
					<div class="form-group content-title content-title-last">
						<label class="control-label col-md-1">类型：</label>
						<div class="col-md-2">
							${whCheckIn.checkInTypeName }
						</div>
						<label class="control-label col-md-1">全检数量：</label>
						<div class="col-md-2">
							${item.qcNum}
						</div>
						<label class="control-label col-md-1">系统包装属性：</label>
						<div class="col-md-2">
							${item.whSku.packageAttribute}
						</div>
						<label class="control-label col-md-1">提货时间：</label>
						<div class="col-md-2">
							${whCheckIn.obtainTime}
						</div>
					</div>
					<div class="form-group content-title content-title-last">
						<label class="control-label col-md-1">入库单类型：</label>
						<div class="col-md-2">
							<#if (whCheckIn.exceptionType)?? && whCheckIn.exceptionType == 2 >中转仓
							<#elseif whCheckIn.exceptionType == 3>优选仓
							<#else >本仓
							</#if>
						</div>
						<label class="control-label col-md-1">上架数量：</label>
						<div class="col-md-2">
							${item.upQuantity}
						</div>
						<label class="control-label col-md-1">时效（H）：</label>
						<div class="col-md-2">
							<#if whCheckIn.aging?? >${whCheckIn.aging }</#if>
						</div>
						<label class="control-label col-md-1">上架时间：</label>
						<div class="col-md-2">
							${whCheckIn.upTime }
						</div>
					</div>
					<div class="form-group content-title content-title-last">
						<label class="control-label col-md-1">供应商：</label>
						<div class="col-md-2">
							${whCheckIn.vendorName}
						</div>
						<label class="control-label col-md-1">异常原因：</label>
						<div class="col-md-2">
							${whCheckIn.comment}
						</div>
						<label class="control-label col-md-1">标签：</label>
						<div class="col-md-2">
							${item.firstOrderTypeName}
						</div>
					</div>

				</div>
			</div>
		<div class="write-row"></div>
	</div>
	<div class="container-fluid" style="background-color: white;border: none">
		<div class="row readonly-row">
			<div class="col-md-12 page-info">
				<div class="form-body">

					<#if whCheckIn.inId?? >
						<div class="col-md-12 content-title">
							<h5><strong>异常信息</strong></h5>
							<table class="table table-condensed table-bordered  table-striped page-table">
								<tbody>
								<tr>
									<th>点数异常周转码</th>
									<th>点数异常数</th>
									<th>点数异常类型</th>
									<th>QC异常数</th>
									<th>QC异常周转码</th>
									<th>QC异常类型</th>
								</tr>
								<#if !domain.whCheckInException && !domain.qcWhCheckInException>
									<tr class="tc">
										<td colspan="7" align="center">没有记录</td>
									</tr>
								<#else >
									<tr>
										<td><#if domain.whCheckInException?? >${domain.whCheckInException.boxNo}</#if></td>
										<td><#if domain.whCheckInException?? >${domain.whCheckInException.quantity}</#if></td>
										<td><#if domain.whCheckInException?? >${domain.whCheckInException.exceptionTypeName}</#if></td>
										<td><#if domain.qcWhCheckInException?? >${domain.qcWhCheckInException.boxNo}</#if></td>
										<td><#if domain.qcWhCheckInException?? >${domain.qcWhCheckInException.quantity}</#if></td>
										<td><#if domain.qcWhCheckInException?? >${domain.qcWhCheckInException.exceptionTypeName}</#if></td>
									</tr>

								</#if>
								</tbody>
							</table>
						</div>

					</#if>
				</div>
			</div>
		</div>
		<div class="write-row">
		</div>
	</div>
	<div class="container-fluid" style="background-color: white;border: none">
		<div class="row readonly-row">
			<div class="col-md-12 page-info">
				<div class="form-body">

					<#if whCheckIn.inId?? >
						<div class="col-md-12 content-title">
							<h5><strong>服装类SKU入库量尺</strong></h5>
							<#if !domain.clothingItemAttrValMap && !domain.checkInClothingAttrValMap>
								<tr class="tc">
									<td colspan="7" align="center">没有记录</td>
								</tr>
							<#else >
							<table class="table table-condensed table-bordered  table-striped page-table">
								<tbody>
									<tr>
										<th>分类</th>
										<#list (domain.checkInClothingAttrValMap)?keys as mKey>
											<th>${mKey}(cm)</th>
										</#list>
									</tr>
									<tr>
										<td>产品系统尺寸</td>
										<#list (domain.clothingItemAttrValMap)?keys as mKey>
											<#assign val = domain.clothingItemAttrValMap[mKey]>
											<td>${val}</td>
										</#list>
									</tr>

									<tr>
										<td>入库测量尺寸</td>
										<#list (domain.checkInClothingAttrValMap)?keys as cKey>
											<#assign checkInVal = domain.checkInClothingAttrValMap[cKey]>
											<td>${checkInVal}</td>
										</#list>
									</tr>
								</tbody>
							</table>
							</#if>
						</div>
					</#if>
				</div>
			</div>
		</div>
		<div class="write-row">
		</div>
	</div>
	<div class="container-fluid" style="background-color: white;border: none">
		<div class="row readonly-row">
			<div class="col-md-12 page-info">
				<div class="form-body">

					<#if whCheckIn.inId?? >
						<div class="col-md-12 content-title">
							<h5><strong>保质期信息</strong></h5>
							<table class="table table-condensed table-bordered  table-striped page-table">
								<tbody>
								<tr>
									<th>生产日期</th>
									<th>保质期天数</th>
									<th>到期日期</th>
								</tr>
								<tr>
									<td>${whCheckIn.proDate}</td>
									<td>${whCheckIn.days}</td>
									<td>${whCheckIn.expDate}</td>
								</tr>

								</tbody>
							</table>
						</div>

					</#if>
				</div>
			</div>
		</div>
		<div class="write-row">
		</div>
	</div>
	<#include "/common/footer.html">
</div>

<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
</body>
</html>