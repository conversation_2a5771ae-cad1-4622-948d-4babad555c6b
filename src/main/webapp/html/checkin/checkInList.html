<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		.check-in-detail th{
			background-color:#E0E0E0 !important;
		}
	</style>
</head>
<body>
<@header method="header" active="10020000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">到货管理</a></li>
				<li class="active">入库单</li>
			</ul>
		</div>
	</div>

	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
		<div class="row">
			<div class="col-md-12">
				<#assign query = domain.query>
				<form action="${CONTEXT_PATH}checkins/search"
						   class="form-horizontal form-bordered form-row-stripped"
						   method="post" modelAttribute="domain" name="checkInForm" id ="domain">
					<!-- 分页信息 -->
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">

					<#if true == domain.updateQC_NG_PENDING>
						<input id="query.updateQC_NG_PENDING" type="hidden" name="query.updateQC_NG_PENDING" value="true">
					</#if>

					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-1">ID</label>
							<div class="col-md-3">
								<input class="form-control" type="text" name="query.inId" placeholder="请输入ID" value="${query.inId }">
							</div>

							<label class="control-label col-md-1">SKU</label>
							<div class="col-md-3">
								<input class="form-control" id="uuidSku" type="text" name="query.sku" placeholder="请输入SKU" onkeypress="if(event.keyCode==13) { inputSku(this); return false;}" value="${query.sku }">
							</div>

							<label class="control-label col-md-1">快递单号</label>
							<div class="col-md-3">
								<input class="form-control" type="text" name="query.trackingNumber" placeholder="请输入快递单号" value="${query.trackingNumber }">
							</div>
						</div>

						<div class="form-group">
							<label class="control-label col-md-1">采购单号</label>
							<div class="col-md-3">
								<input class="form-control" type="text" name="query.purchaseOrderNo" placeholder="请输入采购单号" value="${query.purchaseOrderNo }">
							</div>

							<label class="control-label col-md-1">状态</label>
							<div class="col-md-3">
								<input class="form-control" name="query.statusStr" type="text" value="${query.statusStr}">
							</div>

							<label class="control-label col-md-1">入库时间</label>
							<div class="col-md-3">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.fromCreateDate" placeholder="" readonly="readonly" value="${query.fromCreateDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.toCreateDate" placeholder="" readonly="readonly" value="${query.toCreateDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>
						</div>

						<div class="form-group">
							<label class="control-label col-md-1">类型</label>
							<div class="col-md-3">
								<input class="form-control" name="query.checkInType" type="text" value="${query.checkInType}">
							</div>

							<label class="control-label col-md-1">入库人</label>
							<div class="col-md-3">
								<input class="form-control" name="query.createUser" type="text" value="${query.createUser}">
							</div>

							<label class="control-label col-md-1">唯一码</label>
							<div class="col-md-3">
								<input class="form-control" name="query.uuid" type="text" value="${query.uuid}">
							</div>
						</div>
						<div id="expand-area">
							<div class="form-group">
								<label class="control-label col-md-1">QC人</label>
								<div class="col-md-3">
									<input class="form-control" name="query.qcUser" type="text" value="${query.qcUser}">
								</div>

								<label class="control-label col-md-1">提货人</label>
								<div class="col-md-3">
									<input class="form-control" name="query.obtainUser" type="text" value="${query.obtainUser}">
								</div>

								<label class="control-label col-md-1">上架人</label>
								<div class="col-md-3">
									<input class="form-control" name="query.upUser" type="text" value="${query.upUser}">
								</div>
							</div>

							<div class="form-group">
								<label class="control-label col-md-1">QC时间</label>
								<div class="col-md-3">
									<div class="input-group">
										<input class="form-control Wdate" type="text" name="query.fromQcTime" placeholder="" readonly="readonly" value="${query.fromQcTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
										<span class="input-group-addon">到</span>
										<input class="form-control Wdate" type="text" name="query.toQcTime" placeholder="" readonly="readonly" value="${query.toQcTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
								</div>

								<label class="control-label col-md-1">提货时间</label>
								<div class="col-md-3">
									<div class="input-group">
										<input class="form-control Wdate" type="text" name="query.fromObtainTime" placeholder="" readonly="readonly" value="${query.fromObtainTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
										<span class="input-group-addon">到</span>
										<input class="form-control Wdate" type="text" name="query.toObtainTime" placeholder="" readonly="readonly" value="${query.toObtainTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
								</div>

								<label class="control-label col-md-1">上架时间</label>
								<div class="col-md-3">
									<div class="input-group">
										<input class="form-control Wdate" type="text" name="query.fromUpTime" placeholder="" readonly="readonly" value="${query.fromUpTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
										<span class="input-group-addon">到</span>
										<input class="form-control Wdate" type="text" name="query.toUpTime" placeholder="" readonly="readonly" value="${query.toUpTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
								</div>
							</div>

							<div class="form-group">
								<label class="control-label col-md-1">贴标人</label>
								<div class="col-md-3">
									<input class="form-control" name="query.tagUser" type="text" value="${query.tagUser}">
								</div>

								<label class="control-label col-md-1">贴标时间</label>
								<div class="col-md-3">
									<div class="input-group">
										<input class="form-control Wdate" type="text" name="query.fromTagTime" placeholder="" readonly="readonly" value="${query.fromTagTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
										<span class="input-group-addon">到</span>
										<input class="form-control Wdate" type="text" name="query.toTagTime" placeholder="" readonly="readonly" value="${query.toTagTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
								</div>
								
								<label class="control-label col-md-1">入库成功</label>
								<div class="col-md-3">
									<select name="query.isAddCreateQuantity" class="form-control">
										<option vlaue=""></option>
										<#list domain.isAddCreateQuantityList as status>
											<#if query.isAddCreateQuantity?? && query.isAddCreateQuantity?string ("true","false") == status[0]>
												<option selected="selected" value="${status[0]}">${status[1]}</option>
												<#else>
													<option value="${status[0]}">${status[1]}</option>
											</#if>
										</#list>
									</select>
								</div>
							</div>

							<div class="form-group">
								<label class="control-label col-md-1">采购上架成功</label>
								<div class="col-md-3">
									<select name="query.isPurchaseStockIn" class="form-control">
										<option vlaue=""></option>
										<#list domain.isPurchaseStockInList as status>
											<#if query.isPurchaseStockIn?? && query.isPurchaseStockIn?string ("true","false") == status[0]>
												<option selected="selected" value="${status[0]}">${status[1]}</option>
												<#else>
													<option value="${status[0]}">${status[1]}</option>
											</#if>
										</#list>
									</select>
								</div>

								<label class="control-label col-md-1">加库存成功</label>
								<div class="col-md-3">
									<select name="query.isInStock" class="form-control">
										<option vlaue=""></option>
										<#list domain.isInStockList as status>
											<#if query.isInStock?? && query.isInStock?string ("true","false") == status[0]>
												<option selected="selected" value="${status[0]}">${status[1]}</option>
												<#else>
													<option value="${status[0]}">${status[1]}</option>
											</#if>
										</#list>
									</select>
								</div>

								<label class="control-label col-md-1">废弃成功</label>
								<div class="col-md-3">
									<select name="query.isDiscarded" class="form-control">
										<option vlaue=""></option>
										<#list domain.isInStockList as status>
											<#if query.isDiscarded?? && query.isDiscarded?string ("true","false") == status[0]>
												<option selected="selected" value="${status[0]}">${status[1]}</option>
												<#else>
													<option value="${status[0]}">${status[1]}</option>
											</#if>
										</#list>
									</select>
								</div>
							</div>
							
							<div class="form-group">
								<label class="control-label col-md-1">是否需要核重</label>
								<div class="col-md-3">
									<select class="form-control" name="query.isVerifyWeight" value="${query.isVerifyWeight}">
										<option value=""></option>
										<option <#if (query.isVerifyWeight)?? && query.isVerifyWeight == true>selected</#if> value="true">是</option>
										<option <#if (query.isVerifyWeight)?? && query.isVerifyWeight == false>selected</#if> value="false">否</option>
									</select>
								</div>
								<label class="control-label col-md-1">标签</label>
								<div class="col-md-3">
									<input class="form-control" name="query.checkInFlag" type="text" value="${query.checkInFlag}">
								</div>
								<label class="control-label col-md-1">入库单类型</label>
								<div class="col-md-3">
									<select class="form-control" name="query.exceptionType" value="${query.exceptionType}">
										<option value=""></option>
										<option <#if (query.exceptionType)?? && query.exceptionType == 1>selected</#if> value="1">本仓</option>
										<option <#if (query.exceptionType)?? && query.exceptionType == 2>selected</#if> value="2">中转仓</option>
										<option <#if (query.exceptionType)?? && query.exceptionType == 3>selected</#if> value="3">优选仓</option>
									</select>
								</div>
							</div>

							<div class="form-group">
								<label class="control-label col-md-1">类型</label>
								<div class="col-md-3">
									<select name="query.purchaseType" class="form-control">
										<option vlaue=""></option>
										<#list domain.purchaseTypeList as status>
											<#if query.purchaseType == status[0]>
												<option selected="selected" value="${status[0]}">${status[0]} - ${status[1]}</option>
											<#else>
												<option value="${status[0]}">${status[0]} - ${status[1]}</option>
											</#if>
										</#list>
									</select>
								</div>
                                <label class="control-label col-md-1">上架区域</label>
                                <div class="col-md-3">
                                    <input class="form-control" id="query.area" name="query.area" type="text" value="${query.area}">
                                </div>
							</div>
						</div>
					</div>
					<div>
						<div class="pull-left" >
							<#if true == domain.updateQC_NG_PENDING>

								<div class="btn-group">
									<button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
										NG采购待处理 <i class="icon-angle-down"></i>
									</button>
									<ul class="dropdown-menu">
                                        <@header method="auth" authCode="NG_PROCUREMENT_PENDING_PASS">
										<li><a href="#" onclick="batchUpdateCheckInFromNG(9, '等待上架')">PASS</a></li>
										</@header>
										<@header method="auth" authCode="NG_PROCUREMENT_PENDING_NG">
										<li><a href="#" onclick="batchUpdateCheckInFromNG(7, 'QC不良品')">NG</a></li>
										</@header>
									</ul>
								</div>
							</#if>
							<div class="btn-group">
								<@header method="auth" authCode="CHECKIN_NORMAL_BATCH_DISCARD">
									<button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
										废弃入库单 <i class="icon-angle-down"></i>
									</button>
									<ul class="dropdown-menu">
										<li><a href="#" onclick="batchMoveCheckInDiscarded('ordinary')">废弃（普通）</a></li>
									</ul>
								</@header>

								<@header method="auth" authCode="CHECKIN_SUPER_BATCH_DISCARD">
									<button type="button" class="btn btn-default" onclick="batchMoveCheckInDiscarded('supper')">
										<i class="icon-remove"></i>废弃入库单(超级)
									</button>
                                </@header>
                                <@header method="auth" authCode="CHECKIN_PUSH_SKU_TO_OMS">
                                    <button type="button" class="btn btn-default" onclick="pushLastDateCheckinSkuListToOms()">
										<i class="icon-refresh"></i>推送上架SKU到OMS
									</button>
                                </@header>
							</div>
							<div class="btn-group">
                                <@header method="auth" authCode="CHECKIN_RETRY_PURCHASE_INTERFACE">
									<button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
										重试采购接口 <i class="icon-angle-down"></i>
									</button>
									<ul class="dropdown-menu">
										<li><a href="#" onclick="batchUpdatePurchaseOrder2AddQuantity()">重试点数入库接口</a></li>
									</ul>
                                </@header>
							</div>
							<div class="btn-group">
								<@header method="auth" authCode="BATCH_PUSH_CHECK_IN_INFO_TO_EPMS">
									<button type="button" class="btn btn-default" onclick="batchPush()">
										批量推送入库单信息到采购
									</button>
								</@header>
								<@header method="auth" authCode="CHECK_IN_ALLO_LOCATION">
								<button type="button" class="btn btn-default" onclick="alloLocation()">
									分配库位
								</button>
								</@header>
							</div>
						</div>
						<div class="col-md-offset-12" style="text-align: right">
							<button type="button" id="expandFn" class="btn blue">收起</button>
							<button type="button" onclick="formReset(this)" class="btn btn-default">
								<i class="icon-refresh"></i> 重置
							</button>
							<button type="submit" class="btn blue">
								<i class="icon-search"></i> 查询
							</button>
                            <@header method="auth" authCode="CHECKIN_DOWNLOAD">
                                <button type="button" class="btn btn-default" onclick="downloadCheckIn()">
                                    <i class="icon-download"></i> 导出
                                </button>
                            </@header>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>

		<div class="row">
			<div id="fixedDiv" class="col-md-12">
				<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
					<colgroup>
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="4%" />
						<col width="3%" />
						<col width="3%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
					</colgroup>
					<thead>
					<tr>
						<th>全选</th>
						<th>采购单号<br/>供应商</th>
						<th>快递单</th>
						<th>SKU</th>
						<th>库位</th>
						<th>点数<br/>良品数</th>
						<th>QC良<br/>品数</th>
						<th>包装<br/>领取<br/>数量</th>
						<th>签收时间</th>
						<th>贴标人/贴标时间</th>
						<th>QC时间</th>
						<th>提货时间</th>
						<th>上架时间</th>
						<th>入库人/入库时间</th>
						<th>入库类型</th>
						<th>入库单类型</th>
						<th>标签</th>
						<th>状态</th>
						<th>时效(H)</th>
						<th>异常原因</th>
						<th>操作项</th>
					</tr>
					</thead>
				</table>
			</div>
			<div class="col-md-12" id="task-list-warp">
				<!-- 内容 -->
				<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
					<colgroup>
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="4%" />
						<col width="3%" />
						<col width="3%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
					</colgroup>
					<thead>
					<tr>
						<th><input type="checkbox"  id="check-all" name="checkAll">全选</th>
						<th>采购单号<br/>供应商</th>
						<th>快递单</th>
						<th>SKU</th>
						<th>库位</th>
						<th>点数<br/>良品数</th>
						<th>QC良<br/>品数</th>
						<th>包装<br/>领取<br/>数量</th>
						<th>签收时间</th>
						<th>贴标人/贴标时间</th>
						<th>QC时间</th>
						<th>提货时间</th>
						<th>上架时间</th>
						<th>入库人/入库时间</th>
						<th>入库类型</th>
						<th>入库单类型</th>
						<th>标签</th>
						<th>状态</th>
						<th>时效(H)</th>
						<th>异常原因</th>
						<th>操作项</th>
					</tr>
					</thead>
					<tbody>
					<#list domain.whCheckIns as whCheckIn>
						<tr>
							<td>
								<input type="checkbox" value="${whCheckIn.inId}" name="inIds">
								${whCheckIn.inId}
							</td>
							<td>${whCheckIn.purchaseOrderNo}<br/><span style="color: #0b94ea">${whCheckIn.vendorName}</span></td>
							<td>${whCheckIn.trackingNumber}</td>
							<td>
								<#if whCheckIn.whCheckInItem??>${whCheckIn.whCheckInItem.sku }</#if>
							</td>
							<td>
								<#if whCheckIn.whCheckInItem??>${whCheckIn.whCheckInItem.location }</#if>
							</td>
							<td>${whCheckIn.whCheckInItem.quantity}</td>
							<td>${whCheckIn.whCheckInItem.qcQuantity}</td>
							<td>${whCheckIn.whCheckInItem.pickQty}</td>
							<td>
								<#if whCheckIn.whPurchaseExpressRecord?? >
									${whCheckIn.whPurchaseExpressRecord.getCreationDate() }
								</#if>
							</td>
							<td>
								${util('name',whCheckIn.tagUser)}
								</br>
								${whCheckIn.tagTime }
							</td>
							<td>${whCheckIn.qcTime }</td>
							<td>${whCheckIn.obtainTime }</td>
							<td>${whCheckIn.upTime }</td>
							<td>
								${util('name',whCheckIn.createUser)}
								</br>
								${whCheckIn.createDate }
							</td>
							<td>${whCheckIn.checkInTypeName }</td>
							<td>
								<#if (whCheckIn.exceptionType)?? && whCheckIn.exceptionType == 2 >中转仓
									<#elseif whCheckIn.exceptionType == 3>优选仓
									<#else >本仓
								</#if>
							</td>
							<td>${whCheckIn.whCheckInItem.firstOrderTypeName}</td>
							<td>
								${whCheckIn.statusName } <br />
							</td>
							<td>
								<#if whCheckIn.aging?? >${whCheckIn.aging }</#if>
							</td>
							<td>${whCheckIn.comment }</td>
							<td>
								<button type="button" class="btn btn-xs btn-warning" onclick="getCheckInDetail(${whCheckIn.inId})">详情</button>
								<button type="button" class="btn btn-info btn-xs" onclick="viewLog(${whCheckIn.inId}, 'checkIn')">日志</button>
								<a style="margin-top: 5px;" class="btn btn-xs btn-default qrcode-print" print_inId="${whCheckIn.inId}" print_quantity="${whCheckIn.whCheckInItem.quantity}">
									<i class="icon-qrcode"></i> 打印标签
								</a>
							</td>
						</tr>
					</#list>
					</tbody>
				</table>
				<!-- 内容end -->
			</div>
		</div>
		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	</div>

	<!-- 入库单废弃原因内容  -->
	<div class="hide">
		<div id="discarded-content">
			入库单废弃原因：<br>
			<input type="hidden" id="discarded-reason" value="" class="form-control input-large" options="[{id:'', text:''},{id:'点错', text:'点错'},{id:'收错', text:'收错'},{id:'贴错', text:'贴错'},
						{id:'少配件', text:'少配件'},{id:'残次', text:'残次'},{id:'混料', text:'混料'},{id:'其他原因', text:'其他原因'}]" autocomplete="off">
		</div>
	</div>
	
	<div id="dialog-form" title="打印份数" style="display: none">
        <form>
            <fieldset>
                <label for="printNum">打印份数：</label>
                <input type="text" name="printNum" id="printNum" class="text ui-widget-content ui-corner-all">
                <label style="padding-left:10px;" id="print_maxQuantity_label"></label>
            </fieldset>
        </form>
    </div>

	<div hidden="hidden" id="alloLocationModal">
		<label class="control-label col-md-2">库位</label>
		<div class="col-md-2">
			<input type="text" name="newLocation" class="form-control input-large"/>
		</div>
	</div>
    
	<#include "/common/footer.html">
</div>

<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/prompt-message.js"></script>
<script type="text/javascript">
    $('#expandFn').on('click', function (event) {
        if ($(this).hasClass('expand')) {
            $(this).text('收起');
            $('#expand-area').slideDown(200);
        } else {
            $(this).text('展开');
            $('#expand-area').slideUp(200);
        }
        $(this).toggleClass('expand');
        event.preventDefault();
    });

    // 分页
    var total = "${domain.page.totalCount}";
	var pageNo = "${domain.page.pageNo}";
	var pageSize = "${domain.page.pageSize}";
	$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

    var heights = $("body").height();
	if(heights>910){
        $("#fixed-bottom").addClass("fixed-bottom-height")
	}

    var checkInTypes = ${domain.checkInTypes};
    $("input[name='query.checkInType']").select2({
        data : checkInTypes,
        placeholder : "类型",
        allowClear : true
    });

    var checkInFlags = ${domain.checkInFlags};
    $("input[name='query.checkInFlag']").select2({
        data : checkInFlags,
        placeholder : "入库单标签",
        allowClear : true
    });

	// 状态
	var statusArray =  ${domain.statuses};
	$("input[name='query.statusStr']").select2({
		data : statusArray,
		placeholder : "状态",
		multiple: true,
		allowClear : true
	});

    var locationRegionList =  ${domain.locationRegionList};
    $("input[name='query.area']").select2({
        data : locationRegionList,
        placeholder : "区域",
        multiple: true,
        allowClear : true
    });
	
	// 入库人/创建人 =采购入库页面权限
	$.getJSON(CONTEXT_PATH + "system/saleusers/userByPermissionCode?permissionCode=10020100", function(json){
		if (json) {
			$("input[name='query.createUser']").select2({
				data : json,
				placeholder : "入库人",
				allowClear : true
			});
		} else {
			$("input[name='query.createUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
		}
	});

	// 贴标人 =采购入库页面及PDA贴标权限
	$.getJSON(CONTEXT_PATH + "system/saleusers/userByPermissionCode?permissionCode=10020100,060000", function(json){
		if (json) {
			$("input[name='query.tagUser']").select2({
				data : json,
				placeholder : "贴标人",
				allowClear : true
			});
		} else {
			$("input[name='query.tagUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
		}
	});
	
	// QC人 =入库质检页面权限
	$.getJSON(CONTEXT_PATH + "system/saleusers/userByPermissionCode?permissionCode=10030100", function(json){
		if (json) {
			$("input[name='query.qcUser']").select2({
				data : json,
				placeholder : "QC人",
				allowClear : true
			});
		} else {
			$("input[name='query.qcUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
		}
	});
	// 提货人
	$.getJSON(CONTEXT_PATH + "system/saleusers/userByRole?roleId=51", function(json){
		if (json) {
			$("input[name='query.obtainUser']").select2({
				data : json,
				placeholder : "提货人",
				allowClear : true
			});
		} else {
			$("input[name='query.obtainUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
		}
	});
	// 上架人 =PDASKU上架操作权限
	$.getJSON(CONTEXT_PATH + "system/saleusers/userByPermissionCode?permissionCode=020000", function(json){
		if (json) {
			$("input[name='query.upUser']").select2({
				data : json,
				placeholder : "上架人",
				allowClear : true
			});
		} else {
			$("input[name='query.upUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
		}
	});

	// 全选
	var checkAll = $("input[name='checkAll']");

	// 子选项
	var itemIds = $("input[name='inIds']");
	checkAll.change(
	  function () {
		  itemIds.prop("checked", $(this).prop("checked"));
		  itemIds.each(function(){
			  var f = $(this).is(":checked");
				var checkClass = $(this).prop("class");
				$("." + checkClass).each(function(){
					$(this).prop("checked",f);
				})
		  })
	  }
	);

	// 获取选中的入库单
	function getCheckedInIds() {
		var checkedInIds = $("input[name='inIds']:checked");
		return checkedInIds;
	}
	
	// 采购单入库
	function updatePurchaseOrder2StockIn(inId){
		window.location.href = CONTEXT_PATH + "checkins/updatePurchaseOrder2StockIn?inId=" + inId;
	}

	// 批量处理QC不良品入库单
	function batchUpdateCheckInFromNG(status, name){
		var checkedDatas = getCheckedInIds();
		if(checkedDatas.length == 0) {
			getErrorInfoAlert("请选择要操作的入库单");
			return;
		}
		var r = confirm("是否移动到["+name+"]?");
		if (r) {
			var inIds = checkedDatas.serialize();
			window.location.href = CONTEXT_PATH + "checkins/batchUpdateCheckInFromNG?" + inIds +"&status="+status;
		}
	}

	// 推送上架SKU到OMS
	function pushLastDateCheckinSkuListToOms() {
        $.ajax({
            url:CONTEXT_PATH +"checkins/pushLastDateCheckinSkuListToOms",
            type:"GET",
            data:{
            },
            success : function(response){
                if (response.status == '500') {
					getErrorInfoAlert(response.message + '');
                    return;
                } else if (response.status == '200') {
                    alert("操作成功！");
                }
            }
        });
    }
	// 批量处理废弃入库单
	function batchMoveCheckInDiscarded(type){
		var checkedDatas = getCheckedInIds();
		if(checkedDatas.length == 0) {
			getErrorInfoAlert("请选择要操作的入库单");
			return;
		}
		//var r = confirm("是否移动到[已废弃?");
		var r = true;
		if (r) {
			var inIds = checkedDatas.serialize();

			var elem = $("#discarded-content").clone();
			bindDiscardedOptions(elem.find("#discarded-reason"));
			var diglog = dialog({
				title: '入库单废弃原因',
			    content: elem,
			    onshow: function () {
			    	elem.find("#discarded-reason").select2("open");
			    },
			    okValue: '确定',
		        ok: function () {
		        	var reason = elem.find("#discarded-reason").val();
		        	// 入库单废弃原因： 点错，收错，贴错，少配件，残次，混料。
					// 点确认
                    $.getJSON("${CONTEXT_PATH}checkins/batchMoveCheckInDiscarded?" + inIds +"&reason="+reason+"&type="+type, function(data) {

                        if (data.status == 200) {
							if(data.message){
								getErrorInfoAlert(data.message);
							}else {
								alert("废弃成功！");
							}
                        } else {
							getErrorInfoAlert(data.message);
                        }
                    });
		        	setTimeout(function () {
			    		diglog.close().remove();
			    	}, 100);
			    	return false;
		        },
		        cancelValue: '取消',
		       	cancel: function () {}
			}).showModal();
		}
	}

	function batchPush() {
        var checkedDatas = getCheckedInIds();
        if(checkedDatas.length == 0) {
			getErrorInfoAlert("请选择要操作的入库单");
            return;
        }
        var inIds = checkedDatas.serialize();
        $.ajax({
            url:CONTEXT_PATH +"checkins/batchPush?"+inIds,
            type:"GET",
            data:{
            },
            success : function(response){
                if (response.status == '500') {
					getErrorInfoAlert(response.message + '');
                    return;
                } else if (response.status == '200') {
                    alert("操作成功！");
                }
            }
        });
    }
	
	// 批量重试采购单入库
	function batchUpdatePurchaseOrder2StockIn(){
		var checkedDatas = getCheckedInIds();
		if(checkedDatas.length == 0) {
			getErrorInfoAlert("请选择要操作的入库单");
			return;
		}
		var inIds = checkedDatas.serialize();
		window.location.href = CONTEXT_PATH + "checkins/batchUpdatePurchaseOrder2StockIn?" + inIds;
	}
	
	// 批量重试添加采购单待入库数量
	function batchUpdatePurchaseOrder2AddQuantity(){
		var checkedDatas = getCheckedInIds();
		if(checkedDatas.length == 0) {
			getErrorInfoAlert("请选择要操作的入库单");
			return;
		}
		var inIds = checkedDatas.serialize();
		window.location.href = CONTEXT_PATH + "checkins/batchUpdatePurchaseOrder2AddQuantity?" + inIds;
	}

	// 设置服装属性
	function setClothingAttr(id,checkInClothingAttr,clothingAttr){
		var checkInClothingAttrValMap = JSON.parse(checkInClothingAttr);
		var clothingItemAttrValMap = JSON.parse(clothingAttr);
		var theadTr = $("#clothing-thead-tr1"+id);
		var tbodyTr1 = $("#clothing-tbody-tr1"+id);
		var tbodyTr2 = $("#clothing-tbody-tr2"+id);
		theadTr.html("");
		tbodyTr1.html("");
		tbodyTr2.html("");
		debugger;
		for(var key in checkInClothingAttrValMap){
			var code = key;
			var val = clothingItemAttrValMap[code];
			if(!val){
				val = "";
			}
			var checkInVal = checkInClothingAttrValMap[code];
			if(!checkInVal){
				checkInVal = "";
			}
			theadTr.append('<th>'+ code +'(cm)</th>');
			tbodyTr1.append('<td>'+ val +'</td>');
			tbodyTr2.append('<td>'+ checkInVal +'</td>');
		}
	}

	// 下载
	function downloadCheckIn(){
		let uuid = getFileQueueUUID();
		var param = $("#domain").serialize();
		var checkedDatas = getCheckedInIds();
		if(checkedDatas.length > 0) {
			param = param + "&" +checkedDatas.serialize();
		}
		$.post(CONTEXT_PATH + "checkins/download", param, function(data){
			if (data.status == 200) {
				if (data.message==null || data.message==''){
					layer.alert('成功',function (index) {
						layer.close(index);
						diglog.close().remove();
						location.reload();
					});
				}else{
					customizeLayer(data.message);
				}
			} else {
				customizeLayer(data.message);
			}
		});
	}


	// 超过500条不能用GET请求
	function downloadCheckInByPost(uuid){
		var checkedDatas = getCheckedInIds();
		var inIds = "";
		for (var i = 0; i < checkedDatas.length; i++) {
			var checkIn = checkedDatas[i];
			var inId = $(checkIn).val();
			inIds += inId;
			if(i != checkedDatas.length-1){
				inIds += ",";
			}
		}
		var url= CONTEXT_PATH + "checkins/download";
		var tempForm = document.createElement("form");           
		tempForm.id="tempForm";           
		tempForm.method="post";              
		tempForm.action=url;       
		tempForm.target="blank";           
		var hideInput = document.createElement("input");           
		hideInput.type="hidden";           
		hideInput.name="inIds";  
		hideInput.value= inIds;         
		tempForm.appendChild(hideInput);

        //websocket导出
        var uuidHideInput = document.createElement("input");
        uuidHideInput.type="hidden";
        uuidHideInput.name="uuid";
        uuidHideInput.value= uuid;
        tempForm.appendChild(uuidHideInput);
		if (tempForm.attachEvent) {  // IE 
			tempForm.attachEvent("onsubmit",function(){ window.open('about:blank','blank'); });  
		} else if (tempForm.addEventListener) {  // DOM Level 2 standard  
			tempForm.addEventListener("onsubmit",function(){ window.open('about:blank','blank'); });  
		}              
		document.body.appendChild(tempForm);   
		if (document.createEvent) { // DOM Level 2 standard  
			evt = document.createEvent("MouseEvents");  
			evt.initMouseEvent("submit", true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);  
			tempForm.dispatchEvent(evt);  
		} else if (tempForm.fireEvent) { // IE  
			tempForm.fireEvent('onsubmit');  
		} 
		//必须手动的触发        
		tempForm.submit();
        beginExport(uuid, '导出入库单');
		document.body.removeChild(tempForm);
	}
	
	// 废弃下拉列表
	function bindDiscardedOptions($component) {
		var options =  eval( '(' + $component.attr("options") + ')' );
		if(options) {
			$component.select2({
                createSearchChoice:function(term, data) { if ($(data).filter(function() { return this.text.localeCompare(term)===0; }).length===0) {return {id:term, text:term};} },
                multiple: false,
                data: options
            });
		}
	}
	
	//打印标签
	$('.qrcode-print').on('click',function () {
		var inId = $(this).attr("print_inid");
		var maxQuantity = parseInt($(this).attr("print_quantity"));
        $("#print_maxQuantity_label").html("入库数量：" + maxQuantity);
		dialog({
            title: '打印份数',
            content: $('#dialog-form'),
            width: 400,
            height: 50,
            top: 0,
            okValue: '确定',
            dragStart: function () {
            },
            ok: function () {
                var reg = /^\+?[1-9][0-9]*$/;
                var num = $('#printNum').val();
                if(!reg.test(num)){
					getErrorInfoAlert("请输入正确的正整数");
                    $('#printNum').val('');
                    return false;
                }
                if(num > maxQuantity){
					getErrorInfoAlert("打印份数不可大于入库数量");
                    $('#printNum').val('');
                    return false;
                }
                $.ajax({
                    url: CONTEXT_PATH+"checkins/qrcode?inId="+inId+"&quantity="+num,
                    type: "GET",
                    success: function(data){
                        var printWindow=window.open();
                        printWindow.document.write(data);
                        printWindow.focus();
                        window.location.reload();
                    }
                });
            },
            cancelValue: '关闭',
            cancel: function () {
            	return;
            }
        }).showModal();
	})
	/*明细*/
	function getCheckInDetail(id) {
		window.open(CONTEXT_PATH + "checkin/checkinitems/checkinDetail?inId=" + id);
	}

	function alloLocation(){
		var checkedDatas = getCheckedInIds();
		if(checkedDatas.length == 0) {
			getErrorInfoAlert("请选择要操作的入库单");
			return;
		}
		if(checkedDatas.length > 1) {
			getErrorInfoAlert("只能选择一条入库单进行操作");
			return;
		}
		dialog({
			title: '分配库位',
			content: $('#alloLocationModal'),
			width: 400,
			height: 50,
			top: 0,
			okValue: '确定',
			dragStart: function () {
			},
			ok: function () {
				var newLocation = $("input[name='newLocation']").val();
				if(newLocation == undefined || newLocation == ''){
					getErrorInfoAlert("请输入新库位！");
					return false;
				}
				var params = checkedDatas.serialize() + "&newLocation=" + newLocation;
				$.post(CONTEXT_PATH + "checkins/alloLocationBtn", params, function(data) {
					if (data.status == '200') {
						alert("操作成功！");
						if(data.message != undefined && data.message != ''){
							customizeLayer(data.message, 'error');
						}
					} else if (data.status == '500') {
						getErrorInfoAlert(data.message + '');
						return;
					}
					$("input[name='newLocation']").val('');
				});
			},
			cancelValue: '关闭',
			cancel: function () {
				return;
			}
		}).showModal();
	}

</script>
</body>
</html>