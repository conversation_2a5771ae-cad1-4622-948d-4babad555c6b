jQuery(document).ready(function() {
	
	
});


// 绑定滚动事件
var top_tool_bar = $('#top_tool_bar');
var ttb = top_tool_bar.offset();
// 标题栏宽度
var width=new Array();

$(window).scroll(function(e) {
	//若滚动条离顶部大于100元素
	if ($(window).scrollTop() > 100)
		$("#d-top").fadeIn(500);//以1秒的间隔渐显id=gotop的元素
	else
		$("#d-top").fadeOut(500);//以1秒的间隔渐隐id=gotop的元素

	if ($(window).scrollTop() > ttb.top) {
		
		// 固定工具栏
		top_tool_bar.addClass('top_tool_bar');
		top_tool_bar.width(top_tool_bar.parent().width());
		
		// 固定标题栏
		$('#top_tool_bars').addClass('top_bar');
        
        $('#top_tool_bars').show().css('width',$('#data-list').css('width'));
        $('#user-list tr:eq(0)').find('th').each(function(i){
    		width[i]=parseInt($(this).css('width'));
    	 });
        $('#top_tool_bars').find('th').each(function(i){
            $(this).css('width',''+width[i]+'px');
         });
	} else {
		// 工具栏
		top_tool_bar.removeClass('top_tool_bar');
		top_tool_bar.width("100%");
		
		// 标题栏
		$('#top_tool_bars').hide();
        $('#top_tool_bars').removeClass('top_bar');
	}
});


// 全选
var checkAll = $("#user-list").find("input[name='checkAll']");

// 子选项
var userIds = $("#user-list").find("input[name='ids']");

checkAll.change(
  function () {
	  userIds.prop("checked", $(this).prop("checked"));
	  
	  userIds.each(function(){
		  var f = $(this).is(":checked");
			var checkClass = $(this).prop("class");
			
			$("." + checkClass).each(function(){
				$(this).prop("checked",f);
			})
	  })
  }
);

userIds.change(function(){
	var checkedLenght = userIds.filter(":checked").length;
	var length = userIds.length;
	checkAll.prop("checked", checkedLenght == length);
	
	// 改变背景色
	var $this = $(this);
	if($this.prop("checked")) {
		$this.closest("tr").find("td").css("background-color","#B1DB87");
	} else {
		$this.closest("tr").find("td").css("background-color","");
	}
});


$("input[name='page.pageSize']").select2({
	data : [{ id : '10', text : '10'}, { id : '50', text : '50'}
	, { id : '100', text : '100'}, { id : '300', text : '300'}
	, { id : '500', text : '500'}, { id : '1000', text : '1000'}],
	placeholder : "每页数目",
	allowClear : true
});


// 获取选中的序号
function getCheckedDatas() {
  	var valueList = $("#user-list").find('input[name="ids"]:checked');
  	return valueList;
}

// 批量修改用户信息
function batchUpdateUserRole() {
	// 得到选中的多选控件列表
	var checkedDatas = getCheckedDatas();
	
	if (checkedDatas.length == 0) {
		alert("请选择操作的用户");				
		return;
	}
	
	// 序列化成用户标识列表
	var userIds = checkedDatas.serialize();
	
	var diglog = dialog({
		title: '批量修改用户信息' ,
		width: 600,
		height: 350,
		url: CONTEXT_PATH + "system/saleusers/batch/update?" + userIds,
		okValue: '确定',
	    ok: function () {
	    	
	    	var updateWindow = $(this.iframeNode.contentWindow.document.body);
	    	var submitForm = updateWindow.find("#submit-form");
	    	submitForm.submit();
	    	
	    	var msg = "修改成功";
			
			toastr.options = {
		            closeButton: true,
		            debug: false,
		            timeOut: "5000",
		            positionClass: 'toast-top-center'
		    };
			toastr['success'](msg, "操作成功");
	    	
	    	setTimeout(function () {
	    		diglog.close().remove();
				
	    	}, 100);
	    	return false;
	    },
	    onclose: function () {
	    	setTimeout(jump, 2000);
		},
	    cancelValue: '取消',
	    cancel: function () {}
	});
	diglog.show();
}



function updateDepartment(userId){
	
	var userIds = "";
	if(userId){
		 userIds = "&ids=" + userId;
	}else{
		var checkedDatas = getCheckedDatas();
		
		if (checkedDatas.length == 0) {
			alert("请选择操作的用户");				
			return;
		}
		
		userIds = checkedDatas.serialize();
	}
	
	var diglog = dialog({
		title: '修改用户部门' ,
		width: 600,
		height: 450,
		url: CONTEXT_PATH + "system/saleusers/batch/update/department?" + userIds,
		okValue: '确定',
	    ok: function () {
	    	
	    	var updateWindow = $(this.iframeNode.contentWindow.document.body);
	    	var submitForm = updateWindow.find("#submit-form");
	    	
	    	var departmentId = submitForm.find("#department-id").val();
	    	
	    	if(!departmentId){
	    		alert("请选择一个部门！");
	    		return false;
	    	}
	    	
	    	submitForm.submit();
	    	
	    	var msg = "修改成功";
			
			toastr.options = {
		            closeButton: true,
		            debug: false,
		            timeOut: "5000",
		            positionClass: 'toast-top-center'
		    };
			toastr['success'](msg, "操作成功");
	    	
	    	setTimeout(function () {
	    		diglog.close().remove();
				
	    	}, 100);
	    	return false;
	    },
	    onclose: function () {
	    	setTimeout(jump, 2000);
		},
	    cancelValue: '取消',
	    cancel: function () {}
	});
	diglog.show();
	
}


//批量设置上级管理
function updateSupervisor(userId){
	
	var userIds = "";
	if(userId){
		 userIds = "&ids=" + userId;
	}else{
		var checkedDatas = getCheckedDatas();
		
		if (checkedDatas.length == 0) {
			alert("请选择操作的用户");				
			return;
		}
		
		userIds = checkedDatas.serialize();
	}
	
	var diglog = dialog({
		title: '修改用户上级管理' ,
		width: 600,
		height: 450,
		url: CONTEXT_PATH + "system/saleusers/batch/update/supervisor?" + userIds,
		okValue: '确定',
	    ok: function () {
	    	
	    	var updateWindow = $(this.iframeNode.contentWindow.document.body);
	    	var submitForm = updateWindow.find("#submit-form");
	    	
	    	var supervisorId = submitForm.find("#supervisor-id").val();
	    	
	    	if(!supervisorId){
	    		alert("请选择一个管理！");
	    		return false;
	    	}
	    	
	    	submitForm.submit();
	    	
	    	var msg = "修改成功";
			
			toastr.options = {
		            closeButton: true,
		            debug: false,
		            timeOut: "5000",
		            positionClass: 'toast-top-center'
		    };
			toastr['success'](msg, "操作成功");
	    	
	    	setTimeout(function () {
	    		diglog.close().remove();
				
	    	}, 100);
	    	return false;
	    },
	    onclose: function () {
	    	setTimeout(jump, 2000);
		},
	    cancelValue: '取消',
	    cancel: function () {}
	});
	diglog.show();
}


function jump(){
	document.userForm.submit();
}

//同步用户
function synchUser(){
	$.post(CONTEXT_PATH + "system/saleusers/synch", function(data){
		var msg = "同步已经在后台进行";
		
		toastr.options = {
	            closeButton: true,
	            debug: false,
	            timeOut: "5000",
	            positionClass: 'toast-top-center'
	    };
		toastr['success'](msg, "操作成功");
	});
}
	