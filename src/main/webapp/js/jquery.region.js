/*!
 * jQuery Region Plugin
 * 
 */
;(function($) 
{  
//$ means "jQuery" object.

/**
 * Plugin-method
 * Toggle Region Ignore: toggle whether the region is included within region submit.
 * @return jQuery object.
 */
$.fn.toggleRegionIgnore = function()
{
    this.toggleClass("region_ignore");
    return this;
};

function getIdSelector(idExp) {
    var selector = "";
    var ids = jQuery.trim(idExp).split(",");
    for(var i = 0; i < ids.length; ) {
        var id = jQuery.trim(ids[i]);
        selector += (id ? "#"+id : "noneTagSelector");
        if(++i < ids.length) {
            selector += ",";
        }
    }
    
    return selector;
}

function createWrapDiv(html)
{
    var fragment = document.createDocumentFragment();
    var div = document.createElement("div");
    fragment.appendChild( div );
    div.innerHTML = "div<div>"+html+"</div>";

    return div.lastChild;
}

/**
 * Plugin-method
 * Toggle Region : toggle whether the region is included and display within region submit.
 * @return jQuery object.
 */
$.fn.toggleRegion = function()
{
    this.toggleRegionIgnore().toggle();
    return this;
};



/**
 * To get a jQuery object indicating an 'Region', 
 *   which can be
 *   1)html form element: input, button, select, ... etc. or
 *   2)html block(div, form, ... etc.)
 * Note-BAK: //.filter(":enabled,:hidden");
 * @return
 */
$.fn.getInputs = function()
{
    if(this.length == 0)
    {
        return $([]);
    }else if(this.length == 1)
    {
        return _getInputs(this);
    }else
    {
        var ret = [];
        for(var i = 0; i < this.length; ++i)
        {
            jQuery.merge(ret, _getInputs( $(this[i]) ));
        }
        return $(ret);
    }
};

//Just like jQuery.fn.hasClass(), but is static function for performace crucial reason.
//Codes below come from jQuery-1.4.4 jQuery.fn.hasClass().
var _rclass = /[\n\t]/g;
function _hasClass(dom, className)
{
    return (" " + dom.className + " ").replace(_rclass, " ").indexOf(" " + className +" ") > -1;
}
function _getInputs(jqContext)
{
    var jqInputs = jqContext.find(":input").add(jqContext.filter(":input"));
    return jqInputs.filter(function()
    {   
        // Detect disabled.
        if(this.disabled)
        {
            return _hasClass(this, "selector");
        }
        
        // Detect whether within the ingore region.
        var cur = this;
        while ( cur )
        {
            if(cur.className && cur.className.indexOf("region_ignore") > -1)
            {
                return false;    
            }else if(jqContext[0] === cur)
            {
                break;
            }else
            {
                cur = cur.parentNode;
                if(!cur || !cur.ownerDocument)
                {
                    break;
                }
            }
        }
        
        // OK. pass
        return true;
    });
}

///////////////////////////////////////////////////////////////////////////////

//Private function : helper fn for console logging
//set $.fn.ajaxSubmit.debug to true to enable debug logging
function log() 
{
     if ($.fn.ajaxRegionSubmit.debug) 
     {
         var msg = Array.prototype.join.call(arguments,'');
         if (window.console && window.console.log)
             window.console.log(msg);
         else if (window.opera && window.opera.postError)
             window.opera.postError(msg);
    } 
};

function replaceElement(newElem, oldElem)
{
    if(oldElem)
    {
    	// Replace with empty.
    	if(!newElem)
    	{
    		$(oldElem).remove();
    		return oldElem;
    	}
    	
        //1.Clean data of old element.(//1: ELEMENT_NODE)
        if(oldElem.nodeType === 1)  
        {//Codes from jquery-1.4.4/$.fn.remove()
            jQuery.cleanData(oldElem.getElementsByTagName("*"));
            jQuery.cleanData([oldElem]);
        }
        
        //2.Replace DOM
        if(oldElem.parentNode)
        {
            oldElem.parentNode.replaceChild(newElem, oldElem);
        }
        
        //3.evalScript Node(IE,Chrome won't eval <script> within newElem when appending to DOM tree)
        if(jQuery.contains(document, newElem))
        {
            var scripts = ($.nodeName(newElem, "SCRIPT") ? [ newElem ] : $.makeArray(newElem
                    .getElementsByTagName("script")));
            for(var i = 0; i < scripts.length; ++i)
            {//Codes from jquery-1.4.4/function evalScript(). But ignore 'elem.parentNode.removeChild(elem)'.
                var elem = scripts[i];
                if ( elem.src ) {
                    jQuery.ajax( {url:elem.src, async:false, dataType:"script"} );
                } else {
                    jQuery.globalEval( elem.text || elem.textContent || elem.innerHTML || "" );
                }
            }
        }
        
    }
    return oldElem;
}

function getElementsOrderBySelector(selector, context)
{
    var elements = [];
    var selectors = selector.split(",");
    for ( var j = 0; j < selectors.length; ++j)
    {
        var selectorj = jQuery.trim(selectors[j]);
        if(selectorj != "")
        {
            jQuery(selectorj, context).each(function()
            {
                elements.push(this);
            });
        }
    }
    return jQuery(elements);
}

function replaceElementsById(selector, jqFromContext, jqToContext, callbackOneach)
{
    // Find element by id selector
    var $oldElements = getElementsOrderBySelector(selector, (jqToContext || document));
    if($oldElements.length == 0)
    {
        return $oldElements;
    }
    
    // Replace elements by new ones with same id.
    var newElements = [];
    var $subcache = jqFromContext.children();
    $oldElements.each(function(i)
    {
        var oldElem = this;
        var newElem = $subcache.filter(function(){return oldElem.id==this.id;})[0] 
                        || jqFromContext.find("#"+this.id)[0];
        if (oldElem != newElem && (false !== (callbackOneach ? callbackOneach(oldElem):true)))
        {
            replaceElement(newElem, oldElem);
            newElements.push(newElem);
        }
    });
    return $(newElements);
};

function httpData( xhr, type, s ) { // mostly lifted from jq1.4.4

    var ct = xhr.getResponseHeader('content-type') || '',
        xml = type === 'xml' || !type && ct.indexOf('xml') >= 0,
        data = xml ? xhr.responseXML : xhr.responseText;

    if (xml && data.documentElement.nodeName === 'parsererror') {
        if ($.error)
            $.error('parsererror');
    }
    if (s && s.dataFilter) {
        data = s.dataFilter(data, type);
    }
    if (typeof data === 'string') {
        if (type === 'json' || !type && ct.indexOf('json') >= 0) {
            data = parseJSON(data);
        } else if (type === "script" || !type && ct.indexOf("javascript") >= 0) {
            $.globalEval(data);
        }
    }
    return data;
};

/**
 * 
 */
jQuery.extend({ 
	render : function (options) {
	
		var regoinid = options.regoinid;
		var regionSelector = getIdSelector(regoinid);
		
		var targetid = options.targetid;
		var targetSelector = getIdSelector(targetid);
		
		//1.[Initialize]
		var result = (options.onBefore instanceof Function ? options.onBefore.apply(this) : eval(options.onBefore));
		if(result === false)
        {
            return false;
        }
		
		var jqRegion = jQuery(regionSelector);
	    
		//2.[Pre-handling] Pre-handing data.
		var onResponseSuccess = function(response) {
	        // Render
	        var jqResource = $(createWrapDiv(response.body) );
	        var jqElements = replaceElementsById(
	                targetSelector, jqResource, document);
	    };
	    
		var extendedOptions = {
	        success : function(responseText, statusText, xhr) {
	        	responseText = responseText.replace(/(^\s*)/g, "");
	            
	        	var isJson = false;
	        	if (responseText.match("^\{(.+:.+,*){1,}\}$")) {
	        		isJson = true;
	        	}
	        	
	            // Response object.
	            var response = {
	                body : responseText,
	                hasException : function() {
	                	if(isJson) {
	                		var jsonObject = eval("(" + responseText + ")");
	                		return jsonObject.status ? jsonObject.status == '500' : false; 
	                	}
	                	return false;
	                },
	                errorMessage : function() {
	                	if(isJson) {
	                		var jsonObject = eval("(" + responseText + ")");
	                		return jsonObject.message; 
	                	}
	                }
	            };
	            
	            if(response.hasException()) {
	            	alert(response.errorMessage());
	            	return;
	            }
	            
	            // replace html element
	        	onResponseSuccess(response);
	        	
	        	// execute onAfter
	        	var onAfterResult = (options.onAfter instanceof Function ? options.onAfter.call(this) : eval(options.onAfter));
	        }
	    };
		
		//3.[Ajax Submit] AJAX region submit, and start the asynchronous executing-flow ...
	    var submitOptions = jQuery.extend(true, extendedOptions, options);
	    
		jqRegion.ajaxRegionSubmit(submitOptions);
	}
});

/**
 * ajaxRegionSubmit() provides a mechanism for immediately submitting
 * an region(HTML block) using AJAX.
 * 
 * @see http://jquery.malsup.com/form/#api  ajaxSubmit()
 */ 
$.fn.ajaxRegionSubmit = function(options)
{
    var jqRegion = this;
   
    var method, action, url, $form = this;

    if (typeof options == 'function') {
        options = { success: options };
    }

    url = (typeof options.url === 'string') ? $.trim(options.url) : '';
    url = url || window.location.href || '';

    // url
    var orgType = options.type;
    options = $.extend({
        url:  url,
        type: "GET",
        iframeSrc: /^https/i.test(window.location.href || '') ? 'javascript:false' : 'about:blank'
    }, options || {});
    options.type = options.type.toUpperCase();
    
    // provide opportunity to alter form data before it is serialized
    if (options.beforeSerialize && options.beforeSerialize(this, options) === false)
    {
        log('ajaxRegionSubmit: submit aborted via beforeSerialize callback');
        return jqRegion;
    }
    
    // data
    var $inputs = jqRegion.getInputs();
    
    var dataArray = jqRegion.regionToArray( $inputs );
    
    if (options.data) 
    {
        options.extraData = options.data;
        
        //parse ? & url
        if(typeof options.data == "string") {
        	
        	var arr1 = options.data.split('&');
            for  (i in arr1) {
                var ta = arr1[i].split('=');
                dataArray.push( { name: ta[0], value: ta[1] } );
            }
        }
        else {
        	for (var n in options.data) 
            {
                if(options.data[n] instanceof Array) {
                    for(var k = 0; k < options.data[n].length; ++k) {
                    	dataArray.push( { name: n, value: options.data[n][k] } ); 
                    }
                }
                else {
                    dataArray.push( { name: n, value: options.data[n] } );
                }
            }
        }
    	
        	
        options.data = null;
    }
    
    // give pre-submit callback an opportunity to abort the submit 
    if (options.beforeSubmit && options.beforeSubmit(dataArray, this, options) === false)
    {
        log('ajaxRegionSubmit: submit aborted via beforeSubmit callback');
        return jqRegion;
    }

    // perform a load on the target only if dataType is not provided
    var onsucessHandlers = [];
    if (!options.dataType && options.target) 
    {
        var oldSuccess = options.success || function(){};
        onsucessHandlers.push(function(data) 
        {
            var fn = options.replaceTarget ? 'replaceWith' : 'html';
            $(options.target)[fn](data).each(oldSuccess, arguments);
        });
    }
    else if (options.success)
    {
        onsucessHandlers.push(options.success);
    }
    
    options.success = function(data, status, xhr)
    {// jQuery 1.4+ passes xhr as 3rd arg
        for (var i=0, max=onsucessHandlers.length; i < max; i++)
        {
            onsucessHandlers[i].apply(options, [data, status, xhr || jqRegion, jqRegion]);
        }
        onsucessHandlers = null;
    };
    
    
    // are there files to upload?
    var $vfiles = $inputs.filter(function(){
        return (this.type == "file" && this.value);
    });
   
    //  var mp = 'multipart/form-data';
    //  var  multipart = ($form.attr('enctype') == mp || $form.attr('encoding') == mp);
    var iframeMode = (options.iframe !== false && ($vfiles.length > 0 || options.iframe /* || multipart*/));
        
     // data serialization
    if(iframeMode)
    {//Form-Iframe mode, always use form-post way, and do not need data serialization.
        options.type = 'POST';
    }else
    {
        var q = options.data = $.param(dataArray);
        if (options.type == 'GET') 
        {//By default, we use 'GET' when possible.
            var checkURL = (options.url + (options.url.indexOf('?') >= 0 ? '&' : '?') + q);
           
            /**
             * @see http://support.microsoft.com/kb/208427/en-us   [Last Review: October 27, 2007]
             * -----------------------------------------------------------------------------------
             * Microsoft Internet Explorer has a maximum uniform resource locator (URL) length of 2,083 characters. 
             * Internet Explorer also has a maximum path length of 2,048 characters. 
             * This limit applies to both POST request and GET request URLs.
             * > If you are using the GET method, you are limited to a maximum of 2,048 characters, 
             *   minus the number of characters in the actual path.
             * > However, the POST method is not limited by the size of the URL for submitting name/value pairs.
             *   These pairs are transferred in the header and not in the URL. 
             * > RFC 2616, "Hypertext Transfer Protocol -- HTTP/1.1," does not specify any requirement for URL length.
             * For a further breakdown of the components, see the Wininet header file. 
             * For more information, see "General Syntax," section 3.2.1 of RFC 2616, 
             * "Hypertext Transfer Protocol -- HTTP/1.1,"  at the following Internet Society Web site:
             * ftp://ftp.isi.edu/in-notes/rfc2616.txt (ftp://ftp.isi.edu/in-notes/rfc2616.txt) 
             * ----------------------------------------------------------------------------------- 
             * 
             * Test Result:
             * IE7: GET URL longer than 2083(2048): xhr.status == 122  (No request actually sent.)
             * IE8: NO restrict, But Tomcat accept OK when 7455, bad when 7463. (Bad request 400)
             * FF3.6.13 : NO restrict, But Tomcat accept OK when 7392, bad when 7400. (Bad request 400)
             * Heard:
             * Opera9.60, FF3.0.3 Chrome 0.2.149.30: 4098
             */
            var needPOST = (checkURL.length > 2000);
            
            // OK.
            if(needPOST && orgType == undefined)
            {
                options.type = "POST";
            }else
            {
                options.url = checkURL;
                options.data = null;  // data is null for 'get'
            }
        }
    }
   
       
    // Submit data using AJAX way or Form-Iframe way.
    // Using options.iframe allows user to force iframe mode, and now defaulting to iframe mode if file input is detected
    if (iframeMode)
    {//Form-Iframe mode.
        // hack to fix Safari hang (thanks to Tim Molendijk for this)
        // see:  http://groups.google.com/group/jquery-dev/browse_thread/thread/36395b7ab510dd5d
        if (options.closeKeepAlive)
        {
            $.get(options.closeKeepAlive, fileUpload);
        }else{
            fileUpload();
        }
    } else
    {// AJAX.
        $.ajax(options);
    }

    return jqRegion;
    
    // private function for handling file uploads (hat tip to YAHOO!)
    function fileUpload()
    {
        appInnerFileUpload(jqRegion, $inputs, options);
    }
};



/**
 * @param jqRegion
 * @param opts
 * @return
 */
function appInnerFileUpload(jqRegion, $inputs, options)
{   
    options.inputs = null;
    var s = $.extend(true, {}, $.ajaxSettings, options);
    
    // Create iframe for file uploading.  Create a form to hold region data and then to submit.
    var now = (new Date().getTime());
    var iframeId = 'jqRegionIFrame' + now;
    var formId = 'jqRegionForm' + now;
    var wrapdiv = ['<div style="position:fixed;top:-1000px;left:-1000px">'
                  ,    '<iframe id="',iframeId,'" name="',iframeId,'" src="',s.iframeSrc,'"'
                  ,        ' style="position:fixed;top:-1000px;left:-1000px">'
                  ,    '</iframe>'
                  ,    '<form id="',formId,'" name="',formId,'" target="',iframeId,'" method="POST" action="',s.url,'"'
                           //ie borks in some cases when setting encoding
                  ,        (s.skipEncodingOverride ? '':' enctype="multipart/form-data" encoding="multipart/form-data"')
                  ,        ' style="position:fixed;top:-1000px;left:-1000px;">'
                  ,    '</form>'
                  ,'</div>'].join("");
    var $wrapdiv = $(wrapdiv);
    
    var xhr = { // mock object
            aborted: 0,
            responseText: null,
            responseXML: null,
            status: 0,
            statusText: 'n/a',
            getAllResponseHeaders: function() {},
            getResponseHeader: function() {},
            setRequestHeader: function() {},
            abort: function() {
                this.aborted = 1;
                $iframe.attr('src', s.iframeSrc); // abort op in progress
            }
    };
    
    var g = s.global;
    
    // trigger ajax global events so that activity/block indicators work like normal
    if (g && ! $.active++) $.event.trigger("ajaxStart");
    if (g) $.event.trigger("ajaxSend", [xhr, s]);

    if (s.beforeSend && s.beforeSend(xhr, s) === false)
    {
        if (s.global) {
            $.active--;
        }
        return;
    }
    if (xhr.aborted) {
        return;
    }
    
    // add submitting element to data if we know it
    //    var sub = form.clk;
    //    if (sub) {
    //        var n = sub.name;
    //        if (n && !sub.disabled) {
    //            s.extraData = s.extraData || {};
    //            s.extraData[n] = sub.value;
    //            if (sub.type == "image") {
    //                s.extraData[n+'.x'] = form.clk_x;
    //                s.extraData[n+'.y'] = form.clk_y;
    //            }
    //        }
    //    }
    //
    
    
    // Upload file... TODO: to be refactor! Using more OOD.  HF.2011-01-18
    s.$wrapdiv = $wrapdiv;
    s.inputs = $inputs;
    s.cbInvoked = false;
    s.xhr = xhr;
    
    function doSubmit()
    {
        jqRegion.doSubmitFileUpload(s);
    }
    
    if (s.forceSync)
    {
        doSubmit();
    }
    else
    {
        setTimeout(doSubmit, 10);  //  this lets dom updates render
    }

}
  

/**
 * @param jqRegion
 * @param options
 * @return
 */
$.fn.doSubmitFileUpload = function(opts)
{
    var jqRegion = this;

    var $wrapdiv = opts.$wrapdiv;
    var $iframe = $( $wrapdiv[0].firstChild );
    var $form = $( $wrapdiv[0].lastChild );
        
    // support timout
    if (opts.timeout)
        setTimeout(function() { timedOut = true; timeoutcb(); }, opts.timeout);

    // Save :file inputs need to swap back from the temp form. form inputs.
    var swapElement = [];
    
    // Swap the :file input and copy other normal inputs into form. And submit together. 
    try {
        // Add basic inputs data to form. instead of clone :inputs from document to form, we use value directly just as AJAX.
        // to improve speed.
        for(var i = 0, max = opts.inputs.length; i < max; ++i)
        {
            // Forms and their child elements should not use input names or ids that conflict with properties of a form,
            //  such as submit, length, or method. Name conflicts can cause confusing failures.
            // @see Additional Notes: http://api.jquery.com/submit/
            var el = opts.inputs[i];
            var n = el.name;
            if(el.id=="submit" || n=="submit" || n=="length" || n=="method")
            {
                continue;
            }
            
            //Append inputs.(file or others)
            if(el.type == "file" && el.value)
            {// Move the real file element to the hidden form - then submit it 
                var $element = $(el);
                var $clone = $element.clone();
                $element.after($clone).detach();  //required 1.4.2
                $form.append($element);
                swapElement.push({$clone:$clone, $element:$element});
            }else
            {
                //get value(recursion situation has been considered) 
                var v = $.fieldValue(el, true); 
                if (v && v.constructor == Array) 
                {
                    for(var j=0, jmax=v.length; j < jmax; ++j)
                    {
                        $form.append('<input type="hidden" name="'+n+'" value="'+v[j]+'"/>');
                    }
                }else if (v !== null && typeof v != 'undefined')
                {
                    $form.append('<input type="hidden" name="'+n+'" value="'+v+'"/>');
                }
            }
        }
        
        // Add "extra" data to form if provided in options
        if (opts.extraData)
        {
            for (var n in opts.extraData)
            {
                if (opts.extraData[n] instanceof Array)
                {
                    for(var k = 0; k < opts.extraData[n].length; ++k)
                    {
                        $form.append('<input type="hidden" name="'+n+'" value="'+opts.extraData[n][k]+'"/>');
                    }
                } else
                {
                    $form.append('<input type="hidden" name="'+n+'" value="'+opts.extraData[n]+'"/>');
                }
            }
            opts.extraData = null;
        }

        // add iframe to doc and submit the form
        $wrapdiv.appendTo(document.body);
        $iframe.bind("load", timeoutcb);
        $form[0].submit();
    }
    finally {
        // reset attrs and remove "extra" input elements
        for (var i = 0; i < swapElement.length; ++i)
        {
            var swap = swapElement[i];
            swap.$clone.replaceWith(swap.$element);
        }
        swapElement = null;
        $form.remove();
    }
    
    var timedOut = 0;
    var data, doc, domCheckCount = 50;
    function timeoutcb() 
    {
        var xhr = opts.xhr;
        if (opts.cbInvoked) 
            return;

        var ok = true;
        try
        {
            if (timedOut) {
                throw 'timeout';
            }
            
            // extract the server response from the iframe
            doc = $iframe.contents()[0];
            
            var isXml = opts.dataType == 'xml' || doc.XMLDocument || $.isXMLDoc(doc);
            log('isXml='+isXml);
            if (!isXml && window.opera && (doc.body == null || doc.body.innerHTML == '')) {
                if (--domCheckCount) {
                    // in some browsers (Opera) the iframe DOM is not always traversable when
                    // the onload callback fires, so we loop a bit to accommodate
                    log('requeing onLoad callback, DOM not available');
                    setTimeout(timeoutcb, 250);
                    return;
                }
                log('Could not access iframe DOM after mutiple tries.');
                return;
            }

            log('response detected');
            opts.cbInvoked = true;
            xhr.responseText = doc.body ? doc.body.innerHTML : null;
            xhr.responseXML = doc.XMLDocument ? doc.XMLDocument : doc;
            xhr.getResponseHeader = function(header){
                var headers = {'content-type': opts.dataType};
                return headers[header];
            };

            if (opts.dataType == 'json' || opts.dataType == 'script') {
                // see if user embedded response in textarea
                var ta = doc.getElementsByTagName('textarea')[0];
                if (ta)
                    xhr.responseText = ta.value;
                else {
                    // account for browsers injecting pre around json response
                    var pre = doc.getElementsByTagName('pre')[0];
                    if (pre)
                        xhr.responseText = pre.innerHTML;
                }             
            }
            else if (opts.dataType == 'xml' && !xhr.responseXML && xhr.responseText != null) 
            {
                xhr.responseXML = toXml(xhr.responseText);
            }
            else if(opts.dataType == 'text')
            {
                var pre = doc.getElementsByTagName('pre')[0];
                if (pre)
                {
                    xhr.responseText = $(pre).text();
                }
            }
            data = httpData(xhr, opts.dataType);
        }
        catch(e){
            log('error caught:',e);
            ok = false;
            xhr.error = e;
            // $.handleError(opts, xhr, 'error', e);
        }

        // ordering of these callbacks/triggers is odd, but that's how $.ajax does it
        {
            if (ok) {
                opts.success(data, 'success');
                try{
                  if (opts.global) $.event.trigger("ajaxSuccess", [xhr, opts]);
                }catch(e)
                {//TODO TODO TODO TODO TODO TODO TODO TODO 2011-01-13 for IE.
                    return;
                }
            }
            if (opts.global) $.event.trigger("ajaxComplete", [xhr, opts]);
            if (opts.global && ! --$.active) $.event.trigger("ajaxStop");
            if (opts.complete) opts.complete(xhr, ok ? 'success' : 'error');
    
            // clean up
            try{//try-catch for FireFox.
                setTimeout(function() 
                {
                    $wrapdiv.remove();
                    $iframe = null;
                    $wrapdiv = null;
                    data = null;
                    opts.xhr.getResponseHeader = null;
                    opts.xhr.responseXML = null;
                    opts.xhr = null;
                    opts = null;
                    xhr = null;
                }, 100);
            }catch(e){}
        }
    };

    function toXml(s, doc) {
        if (window.ActiveXObject) {
            doc = new ActiveXObject('Microsoft.XMLDOM');
            doc.async = 'false';
            doc.loadXML(s);
        }
        else{
            doc = (new DOMParser()).parseFromString(s, 'text/xml');
        }
        return (doc && doc.documentElement && doc.documentElement.tagName != 'parsererror') ? doc : null;
    };
};




/**
 * regionToArray() gathers REGION element data into an array of objects that can 
 * be passed to any of the following ajax functions: $.get, $.post, or load. 
 * Each object in the array has both a 'name' and 'value' property.  An example of
 * an array for a simple login form might be:
 *
 * [ { name: 'username', value: 'jresig' }, { name: 'password', value: 'secret' } ]
 *
 * It is this array that is passed to pre-submit callback functions provided to the
 * ajaxSubmit() and ajaxForm() methods.
 * 
 * @see jquery.form.js#formToArray()
 */
$.fn.regionToArray = function(inputs)
{
    var va = [];
    
    //so the region is something a block(form, div, etc.)
    inputs = (inputs || this.getInputs());
    for ( var i = 0, max = inputs.length; i < max; ++i)
    {
        var element = inputs[i];
        
        //get name
        var n = element.name;
        if(!n) 
        {
           continue;
        }
        
        //get value(recursion situation has been considered) 
        var v = $.fieldValue(element, true); 
        if (v && v.constructor == Array) 
        {
            for(var j=0, jmax=v.length; j < jmax; ++j)
            {
                va.push({name: n, value: v[j]});
            }
        }
        else if (v !== null && typeof v != 'undefined')
        {
            va.push({name: n, value: v});
        }
    }
    return va;
};


/**
 * Serializes region data into a 'submittable' string. This method will return a string
 * in the format: name1=value1&amp;name2=value2
 * 
 * @see jquery.form.js#formSerialize()
 */
$.fn.regionSerialize = function()
{
    // hand off to jQuery.param for proper encoding
    return $.param(this.regionToArray());
};

/**
 * Clears the region data.  Takes the following actions on the form's input fields:
 *  - input text fields will have their 'value' property set to the empty string
 *  - select elements will have their 'selectedIndex' property set to -1
 *  - checkbox and radio inputs will have their 'checked' property set to false
 *  - inputs of type submit, button, reset, and hidden will *not* be effected
 *  - button elements will *not* be effected
 */
$.fn.clearRegion = function() 
{
    var jqRegion = this;
    return jqRegion.getInputs().each(function()
    {
        $(this).clearFields();
    });
    return jqRegion;
};


/**
 * Clears the selected form elements.
 * @see jquery.form.js#clearFields()
 */
$.fn.clearFields = $.fn.clearInputs = function() {
    return this.each(function() {
        var t = this.type, tag = this.tagName.toLowerCase();
        if (t == 'text' || t == 'password' || tag == 'textarea')
            this.value = '';
        else if (t == 'checkbox' || t == 'radio')
            this.checked = false;
        else if (tag == 'select')
            this.selectedIndex = -1;
    });
};

/**
 * Resets the region data.  Causes all form elements to be reset to their original value.
 * 
 * Event: reset - W3C - //http://www.w3.org/TR/DOM-Level-2-Events/events.html
 * reset
 *   The reset event occurs when a form is reset. This event only applies to the FORM element.
 *       * Bubbles: Yes
 *       * Cancelable: No
 *       * Context Info: None
 * 
 * @see jquery.form.js#resetForm()
 */
$.fn.resetRegion = function() 
{
    var jqRegion = this;
    jqRegion.getInputs().each(function()
    {
        var t = this.type, tag = this.tagName.toLowerCase();
        if(t == 'checkbox' || t == 'radio')
        {
            this.checked = this.defaultChecked;
        }else if (tag == 'select') 
        {
            for(var j= 0; j < this.options.length; ++j)
            {
                this.options[j].selected = (j==0 ? true : this.options[j].defaultSelected);
            }
        }else
        {
            this.value = this.defaultValue;
            if(t == "file" &&  ($.browser.msie || $.browser.webkit))
            {   // Cannot set file's value to empty in IE and chrome13.(OK in others like FF,chrome,safari...)
                // However, we can set the value to empty by clone() in IE and Chrome13 (clone file will not take the value)
            	var $file = $(this);
            	$file.replaceWith($file.clone(true));
            }
        }
        
        //trigger a 'reset' event just like form reset does!
        $(this).trigger("reset");
    });
    return jqRegion;
};


/**
 * Returns the value(s) of the element in the matched set.  For example, consider the following form:
 *
 *  <form><fieldset>
 *    <input name="A" type="text" />
 *    <input name="A" type="text" />
 *    <input name="B" type="checkbox" value="B1" />
 *    <input name="B" type="checkbox" value="B2"/>
 *    <input name="C" type="radio" value="C1" />
 *    <input name="C" type="radio" value="C2" />
 *  </fieldset></form>
 *
 *  var v = $(':text').fieldValue();
 *  // if no values are entered into the text inputs
 *  v == ['','']
 *  // if values entered into the text inputs are 'foo' and 'bar'
 *  v == ['foo','bar']
 *
 *  var v = $(':checkbox').fieldValue();
 *  // if neither checkbox is checked
 *  v === undefined
 *  // if both checkboxes are checked
 *  v == ['B1', 'B2']
 *
 *  var v = $(':radio').fieldValue();
 *  // if neither radio is checked
 *  v === undefined
 *  // if first radio is checked
 *  v == ['C1']
 *
 * The successful argument controls whether or not the field element must be 'successful'
 * (per http://www.w3.org/TR/html4/interact/forms.html#successful-controls).
 * The default value of the successful argument is true.  If this value is false the value(s)
 * for each element is returned.
 *
 * Note: This method *always* returns an array.  If no valid value can be determined the
 *     array will be empty, otherwise it will contain one or more values.
 */
$.fn.fieldValue = function(successful) {
    for (var val=[], i=0, max=this.length; i < max; i++) {
        var el = this[i];
        var v = $.fieldValue(el, successful);
        if (v === null || typeof v == 'undefined' || (v.constructor == Array && !v.length))
            continue;
        v.constructor == Array ? $.merge(val, v) : val.push(v);
    }
    return val;
};


/**
 * Returns the value of the field element.
 */
$.fieldValue = function(el, successful) {
    var n = el.name, t = el.type, tag = el.tagName.toLowerCase();
    if (typeof successful == 'undefined') successful = true;

    if (successful && (!n  /*|| el.disabled*/ || t == 'reset' || t == 'button' ||
        (t == 'checkbox' || t == 'radio') && !el.checked ||
        (t == 'submit' || t == 'image') && el.form && el.form.clk != el ||
        tag == 'select' && el.selectedIndex == -1))
            return null;

    if (tag == 'select') {
        var index = el.selectedIndex;
        if (index < 0) return null;
        var a = [], ops = el.options;
        var one = (t == 'select-one');
        var max = (one ? index+1 : ops.length);
        for(var i=(one ? index : 0); i < max; i++) {
            var op = ops[i];
            if (op.selected) {
                var v = op.value;
                if (!v) // extra pain for IE...
                    v = (op.attributes && op.attributes['value'] && !(op.attributes['value'].specified)) ? op.text : op.value;
                if (one) return v;
                a.push(v);
            }
        }
        return a;
    }
    return el.value;
};

})(jQuery);
