(function(window, $, undefined) {
    if (window.pmsReport == undefined) {
        window.pmsReport = {};
    }
    window.pmsReport.schedulerJob = {
        //用于标识是否进行表格的初始化
        isInit:true,
        data: {
            params: {}
        },
        init: function() {
            this.initData();
            this.bindEvent();
            this.initTable();
            this.bindTableEvent();
        },
        initData: function(){
        },
        bindEvent: function() {
            var me = this;
            $("#search_btn").click(function(){
            	
                me.refreshTable();
            });
            $("#delete_btn").click(function(){
            	var selectId =  me.data.table.bootstrapTable("getSelections");
                if(selectId.length == 0){
                    layer.msg("请选择任务！");
                    return;
                }
                layer.confirm("确认删除？", function(index){
                	layer.close(index);
                	$.ajax({
                		url: CONTEXT_PATH + '/system/scheduler/jobs/delete',
                		type: "POST",
                		data: {
                			id: selectId[0].id
                		},
                		success: function(result){
                			if(result){
                				layer.msg("删除成功")
                			}else{
                				layer.msg("删除失败")
                			}
                			me.refreshTable();
                		}
                	}); 
                });
            });
            
            $("#add_btn").click(function(){
            	$("#jobName").attr("readonly",false);
                $("#add-scheduler-dialog input").val("");
                $("#add-scheduler-dialog").removeClass("hide").showDialog({
                    title: "添加任务",
                    width: "600px",
                    buttons : [{
                        id : "export_dialog",
                        text : "保存",
                        icons: {
                            primary: " ui-icon-check"
                         },
                        click : function() {
                        	
                        	var jobName=$("#jobName").val().trim();
                            var jobClass=$("#jobClass").val().trim();
                            var description=$("#description").val().trim();
                            var cronExpression=$("#cronExpression").val().trim();
                            var enable=$("#enable").val();
                            if(jobName==""){
	                           	layer.msg("请填写任务名！");
	                           	return false;
                            }
                            if(jobClass==""){
	                           	layer.msg("请填写任务类！");
	                           	return false;
                            }
                            if(description==""){
	                           	layer.msg("请填写任务描述！");
	                           	return false;
                            }
                            if(cronExpression==""){
	                           	layer.msg("请填写cron表达式！");
	                           	return false;
                            }
                           if(enable==""){
                        	   layer.msg("请选择是否启用！");
                        	   return false;
                           }
                           
                           var params={
                        		   "jobName":jobName,
                        		   "jobClass":jobClass,
                        		   "description":description,
                        		   "cronExpression":cronExpression,
                        		   "enable":enable
                           };
                           $.ajax({
                               url: CONTEXT_PATH + '/system/scheduler/jobs/save',
                               type: "POST",
                               async: false,
                               data:{
                        		   "jobName":jobName,
                        		   "jobClass":jobClass,
                        		   "description":description,
                        		   "cronExpression":cronExpression,
                        		   "enable":enable
                               },
                               success: function(result){
                            	   if(result){
                            		   layer.msg("添加成功");
                            	   }else{
                            		   layer.msg("添加失败");
                            	   }
                            	   
                               }
                           }); 
                           $(this).dialog("close");
                           me.refreshTable();
                        }
                    }, {
                        id : "close_dialog",
                        text : "关闭",
                        icons: {
                           primary: "ui-icon-closethick"
                        },
                        click : function() {
                            $(this).dialog("close");
                        }
                    }]
                });
            });
            
            $("#edit_btn").click(function(){
                var selectId =  me.data.table.bootstrapTable("getSelections");
                if(selectId.length == 0){
                    layer.msg("请选择任务！");
                    return;
                }
                $.ajax({
                    url: CONTEXT_PATH + '/system/scheduler/jobs/detail',
                    type: "POST",
                    data: {
                        id: selectId[0].id
                    },
                    success: function(result){
                        if(result != null){
                        	$("#jobName").val(result.jobName);
                            $("#jobClass").val(result.jobClass);
                            $("#description").val(result.description);
                            $("#cronExpression").val(result.cronExpression);
                            var ena=0;
                            if(result.enable){
                            	ena=1;
                            }
                            $("#enable").val(ena);
                            $("#jobName").attr("readonly",true);
                        }
                    }
                }); 
                //edit-scheduler-dialog
                $("#add-scheduler-dialog").removeClass("hide").showDialog({
                    title: "编辑任务",
                    width: "600px",
                    buttons : [{
                        id : "export_dialog",
                        text : "保存",
                        icons: {
                            primary: " ui-icon-check"
                         },
                        click : function() {
                        	 var jobName=$("#jobName").val().trim();
                             var jobClass=$("#jobClass").val().trim();
                             var description=$("#description").val().trim();
                             var cronExpression=$("#cronExpression").val().trim();
                             var enable=$("#enable").val();
                             if(jobName==""){
                            	 layer.msg("请填写任务名！");
                            	 return false;
                             }
                             if(jobClass==""){
                            	 layer.msg("请填写任务类！");
                            	 return false;
                             }
                             if(description==""){
                            	 layer.msg("请填写任务描述！");
                            	 return false;
                             }
                             if(cronExpression==""){
                            	 layer.msg("请填写cron表达式！");
                            	 return false;
                             }
                             if(enable==""){
                          	   layer.msg("请选择是否启用！");
                          	   return false;
                             }
                             
                          		   
                             
                             $.ajax({
                                 url: CONTEXT_PATH + '/system/scheduler/jobs/update',
                                 type: "POST",
                                 async: false,
                                 data:{
                                	 "jobName":jobName,
                            		   "jobClass":jobClass,
                            		   "description":description,
                            		   "cronExpression":cronExpression,
                            		   "enable":enable,
                            		   "id": selectId[0].id
                                 },
                                 success: function(result){
                                 }
                             }); 
                        	
                            $(this).dialog("close");
                            me.refreshTable();
                        }
                    }, {
                        id : "close_dialog",
                        text : "关闭",
                        icons: {
                           primary: "ui-icon-closethick"
                        },
                        click : function() {
                            $(this).dialog("close");
                        }
                    }]
                });
            });
        },
        
        getParams: function(){
        },
        initTable: function() {
            var me = this;
            me.getParams();
            this.data.table = $("#scheduler-job-table").intBootstrapTable({ 
            url: CONTEXT_PATH + '/system/scheduler/jobs/list',
            queryParams: function(params){
                params["params"] = JSON.stringify(me.data.params);
                params['init'] = me.isInit;
                console.log(params);
                return params;
            },
            // smartDisplay: false,
            clickToSelect: true,
            singleSelect: true,
            checkboxHeader: false,
            selectItemName: "id",
            toolbar: "#scheduler-toolbar",
            columns: [
                      {field: '', title: '', align: 'center', checkbox: true, valign: 'middle', width: "1%"}, 
                      {field: 'jobName', title: '任务名', align: 'center', valign: 'middle', width: "5%"},  
                      {field: 'jobClass', title: '任务类', align: 'center', valign: 'middle', width: "10%"},  
                      {field: 'cronExpression', title: 'cron表达式', align: 'center', valign: 'middle', width: "5%"},
                      {field: 'description', title: '任务描述', align: 'center', valign: 'middle', width: "5%"},
                      {field: 'startTime', title: '开始时间', align: 'center', valign: 'middle', width: "5%", formatter: function(value, row, index){
                          return long2FullDate(value);
                      }},  
                      {field: 'previousTime', title: '上次触发', align: 'center', valign: 'middle', width: "5%", formatter: function(value, row, index){
                          return long2FullDate(value);
                      }},
                      {field: 'nextTime', title: '下次触发', align: 'center', valign: 'middle', width: "5%", formatter: function(value, row, index){
                          return long2FullDate(value);
                      }},  
                      {field: 'executeCount', title: '触发次数', align: 'center', valign: 'middle', width: "5%"},
                      {field: 'enable', title: '状态', align: 'center', valign: 'middle', width: "5%", formatter: function(value, row, index){
                          return value ? "已启用" : "已禁用";
                      }}
            ],
            detailView : false
            });
            this.isInit = false;
        },
        refreshTable: function() {
            this.getParams();
            this.data.table.bootstrapTable("refresh");
        },
        bindTableEvent: function(){
            
        }
    };

    window.pmsReport.schedulerJob.init();
})(window, $, undefined);