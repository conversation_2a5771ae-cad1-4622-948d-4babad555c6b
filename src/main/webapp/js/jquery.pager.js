(function($) {	
	$.fn.pager = function(options) {
		$.fn.pager.defaults = {
				pageNo : 1,
				pageSize : 10,
				total : 0,
				listsize : 7,
				showPageSize:true,
				showPageCount:true,
				pageClick : function(){},
				sizeList : [10,30,50,100,300,500,1000]
		};
		var opts = $.extend( {}, $.fn.pager.defaults, options);
		
		var $this;
		return this.each(function() {
			
			$this = $(this);
			
			var pageCount = Math.ceil(opts.total / opts.pageSize);
			$this.empty().append(renderpager(parseInt(opts.pageNo),
									parseInt(pageCount),
									parseInt(opts.pageSize),
									opts.pageClick));			
		});
		
		// 显示页数
		function rendercount(pageNo,pageCount) {
			var countContent = "<span>页" + pageNo+"/"+pageCount + " </span>";
			var countInput = "<input size='3'>";
			var countLi = $("<li class='count'></li>");
			countLi.append(countContent).append(countInput);
			return countLi;
		};
		
		// go按钮
		function rendergo(pageClick) {
			var goButton = $("<li class='go page-number'></li>");	
			goButton.append("Go");
			goButton.click(function(){
				var goVal = $this.find("li.count input").val();
				if(goVal >= 1 && goVal <= Math.ceil(opts.total / opts.pageSize)) {
					opts.pageNo = goVal;
					pageClick(opts);
				}			
			});
			return goButton;
		}
	
		// 每一页按钮
		function renderpager(pageNo, pageCount, pageSize, pageClick) {
			var $pager = $('<ul class="pages"></ul>');
			
			// 页大小
			if(opts.showPageSize) {
				var sizeList = opts.sizeList;
				
				var curpagesize = $('<li class="select"></li>');
				
				var selectPageSize = $("<select></select>");
				for(var i = 0; i <sizeList.length; i++) {
					var optPageSize = $("<option>"+sizeList[i]+"</option>");
					selectPageSize.append(optPageSize);
				}
				curpagesize.append(selectPageSize);
				selectPageSize.change(function(){
									opts.pageNo = 1;
									opts.pageSize = $(this).val();
									pageClick(opts);													
				});
				// 当前页大小				
				selectPageSize.val(pageSize);				
				$pager.append(curpagesize);
			}
			
			// 首页,上一页
			$pager.append(renderButton('FIRST', pageNo, pageCount,pageClick))
				  .append(renderButton('PREVIOUS', pageNo, pageCount,pageClick));
				  
			// 其他页码
			var startPoint = 1;
			var endPoint = opts.listsize;
			var center = parseInt(opts.listsize / 2);
			if (pageNo > center) {
				startPoint = pageNo - center;
				endPoint = pageNo + center;
			}
			if (endPoint > pageCount) {
				startPoint = pageCount - opts.listsize - 1;
				endPoint = pageCount;
			}
			if (startPoint < 1) {
				startPoint = 1;
			}
			
			for ( var page = startPoint; page <= endPoint; page++) {						
				var currentButton = $('<li>' + (page) + '</li>');		
				page == pageNo ? currentButton.addClass('pgCurrent')
						: currentButton.addClass("page-number").click(function() {		
							opts.pageNo = this.firstChild.data;
							pageClick(opts);							
				});
				currentButton.appendTo($pager);		
			}
			
			// 末页，下一页			
			$pager.append(
					renderButton('NEXT', pageNo, pageCount, pageClick))
					.append(
							renderButton('LAST', pageNo, pageCount,
									pageClick));
			
			
			// 总页数
			if(opts.showPageCount) {				
				$pager.append(rendercount(pageNo, pageCount));	
				$pager.append(rendergo(pageClick));
			}	
			
			// 总数
			$pager.append("<li>共 " + opts.total + " 条记录</li>");
						
			return $pager;
		}
		
		function renderButton(buttonLabel, pageNo, pageCount, pageClick) {
			
			var destPage = 1;
			var buttonStr;
			switch (buttonLabel) {
			case "FIRST":
				buttonStr = "首页";
				destPage = 1;
				break;
			case "PREVIOUS":
				buttonStr = "« 上一页";
				destPage = pageNo - 1;
				break;
			case "NEXT":
				buttonStr = "下一页 »";
				destPage = pageNo + 1;
				break;
			case "LAST":
				buttonStr = "末页";
				destPage = pageCount;
				break;
			}
			
			if(destPage == "") {
				destPage = 1;
			}
			
			var $Button = $('<li>' + buttonStr + '</li>');
			if (buttonLabel == "FIRST" || buttonLabel == "PREVIOUS") {
				pageNo <= 1 ? $Button.addClass('pgEmpty') : $Button.addClass("page-number")
						.click(function() {
							opts.pageNo = destPage;
							pageClick(opts);						
				});
			} else {
				pageNo >= pageCount ? $Button.addClass('pgEmpty') : $Button.addClass("page-number")
						.click(function() {
							opts.pageNo = destPage;
							pageClick(opts);						
				});
			}
			return $Button;
		}				
	};	
})(jQuery);