// Ion.RangeSlider
// version 1.7.2
// https://github.com/IonDen/ion.rangeSlider
(function(c){var S=0,J=function(){var c=navigator.userAgent,a=/msie\s\d+/i;return 0<c.search(a)?(c=a.exec(c).toString(),c=c.split(" ")[1],9>c?!0:!1):!1}(),E;try{document.createEvent("TouchEvent"),E=!0}catch(da){E=!1}var I={init:function(m){var a=c.extend({min:10,max:100,from:null,to:null,type:"single",step:1,prefix:"",postfix:"",hasGrid:!1,hideText:!1,prettify:!0,onChange:null,onFinish:null},m),r='<span class="irs">',r=r+'<span class="irs-line"><span class="irs-line-left"></span><span class="irs-line-mid"></span><span class="irs-line-right"></span></span>',
r=r+'<span class="irs-min">0</span><span class="irs-max">1</span>',r=r+'<span class="irs-from">0</span><span class="irs-to">0</span><span class="irs-single">0</span>',r=r+"</span>",r=r+'<span class="irs-grid"></span>',F='<span class="irs-diapason"></span>',F=F+'<span class="irs-slider from"></span>',F=F+'<span class="irs-slider to"></span>';return this.each(function(){var e=c(this);if(!e.data("isActive")){e.data("isActive",!0);S++;this.pluginCount=S;"number"!==typeof a.from&&(a.from=a.min);"number"!==
typeof a.to&&(a.to=a.max);e.attr("value")&&(a.min=parseInt(e.attr("value").split(";")[0],10),a.max=parseInt(e.attr("value").split(";")[1],10));"number"===typeof e.data("from")&&(a.from=parseInt(e.data("from"),10));"number"===typeof e.data("to")&&(a.to=parseInt(e.data("to"),10));e.data("step")&&(a.step=parseFloat(e.data("step")));e.data("type")&&(a.type=e.data("type"));e.data("prefix")&&(a.prefix=e.data("prefix"));e.data("postfix")&&(a.postfix=e.data("postfix"));e.data("hasgrid")&&(a.hasGrid=e.data("hasgrid"));
e.data("hidetext")&&(a.hideText=e.data("hidetext"));e.data("prettify")&&(a.prettify=e.data("prettify"));a.from<a.min&&(a.from=a.min);a.to>a.max&&(a.to=a.max);"double"===a.type&&(a.from>a.to&&(a.from=a.to),a.to<a.from&&(a.to=a.from));var m=function(b){b=b.toString();a.prettify&&(b=b.replace(/(\d{1,3}(?=(?:\d\d\d)+(?!\d)))/g,"$1 "));return b},I='<span class="irs" id="irs-'+this.pluginCount+'"></span>';e[0].style.display="none";e.before(I);var w=c("#irs-"+this.pluginCount),T=c(document.body),U=c(window),
f,B,C,x,y,t,z,n,s,u,P,V,q=!1,v=!1,W=!0,g={},Q=0,K=0,L=0,k=0,A=0,G=0,R=0,M=0,N=0,X=0,p=0;parseInt(a.step,10)!==parseFloat(a.step)&&(p=a.step.toString().split(".")[1],p=Math.pow(10,p.length));this.updateData=function(b){a=c.extend(a,b);w.find("*").off();w.html("");Y()};this.removeSlider=function(){w.find("*").off();w.html("").remove();e.data("isActive",!1);e.show()};var Y=function(){w.html(r);f=w.find(".irs");B=f.find(".irs-min");C=f.find(".irs-max");x=f.find(".irs-from");y=f.find(".irs-to");t=f.find(".irs-single");
V=w.find(".irs-grid");a.hideText?(B[0].style.display="none",C[0].style.display="none",x[0].style.display="none",y[0].style.display="none",t[0].style.display="none"):(B.html(a.prefix+m(a.min)+a.postfix),C.html(a.prefix+m(a.max)+a.postfix));K=B.outerWidth();L=C.outerWidth();if("single"===a.type){if(f.append('<span class="irs-slider single"></span>'),z=f.find(".single"),z.on("mousedown",function(a){a.preventDefault();a.stopPropagation();D(a,c(this),null);v=q=!0;J&&c("*").prop("unselectable",!0)}),E)z.on("touchstart",
function(a){a.preventDefault();a.stopPropagation();D(a.originalEvent.touches[0],c(this),null);v=q=!0})}else"double"===a.type&&(f.append(F),n=f.find(".from"),s=f.find(".to"),P=f.find(".irs-diapason"),H(),n.on("mousedown",function(a){a.preventDefault();a.stopPropagation();c(this).addClass("last");s.removeClass("last");D(a,c(this),"from");v=q=!0;J&&c("*").prop("unselectable",!0)}),s.on("mousedown",function(a){a.preventDefault();a.stopPropagation();c(this).addClass("last");n.removeClass("last");D(a,c(this),
"to");v=q=!0;J&&c("*").prop("unselectable",!0)}),E&&(n.on("touchstart",function(a){a.preventDefault();a.stopPropagation();c(this).addClass("last");s.removeClass("last");D(a.originalEvent.touches[0],c(this),"from");v=q=!0}),s.on("touchstart",function(a){a.preventDefault();a.stopPropagation();c(this).addClass("last");n.removeClass("last");D(a.originalEvent.touches[0],c(this),"to");v=q=!0})),a.to===a.max&&n.addClass("last"));T.on("mouseup",function(){q&&(q=v=!1,u.removeAttr("id"),u=null,"double"===a.type&&
H(),O(),J&&c("*").prop("unselectable",!1))});T.on("mousemove",function(a){q&&(Q=a.pageX,Z())});E&&(U.on("touchend",function(){q&&(q=v=!1,u.removeAttr("id"),u=null,"double"===a.type&&H(),O())}),U.on("touchmove",function(a){q&&(Q=a.originalEvent.touches[0].pageX,Z())}));$();ba();a.hasGrid&&ca()},$=function(){k=f.width();G=z?z.width():n.width();A=k-G},D=function(b,d,l){$();W=!1;u=d;u.attr("id","irs-active-slider");d=u.offset().left;X=d+(b.pageX-d)-u.position().left;"single"===a.type?R=f.width()-G:"double"===
a.type&&("from"===l?(M=0,N=parseInt(s.css("left"),10)):(M=parseInt(n.css("left"),10),N=f.width()-G))},H=function(){var a=n.width(),d=parseInt(n[0].style.left,10)||n.position().left,l=(parseInt(s[0].style.left,10)||s.position().left)-d;P[0].style.left=d+a/2+"px";P[0].style.width=l+"px"},Z=function(){var b=Math.round(Q-X);"single"===a.type?(0>b&&(b=0),b>R&&(b=R),O()):"double"===a.type&&(b<M&&(b=M),b>N&&(b=N),O(),H());u[0].style.left=b+"px"},O=function(){var b={fromNumber:0,toNumber:0,fromPers:0,toPers:0,
fromX:0,toX:0},d=a.max-a.min,l;"single"===a.type?(b.fromX=parseInt(z[0].style.left,10)||z.position().left,b.fromPers=100*(b.fromX/A),l=d/100*b.fromPers+parseInt(a.min,10),b.fromNumber=Math.round(l/a.step)*a.step,p&&(b.fromNumber=parseInt(b.fromNumber*p,10)/p)):"double"===a.type&&(b.fromX=parseInt(n[0].style.left,10)||n.position().left,b.fromPers=100*(b.fromX/A),l=d/100*b.fromPers+parseInt(a.min,10),b.fromNumber=Math.round(l/a.step)*a.step,b.toX=parseInt(s[0].style.left,10)||s.position().left,b.toPers=
100*(b.toX/A),d=d/100*b.toPers+parseInt(a.min,10),b.toNumber=Math.round(d/a.step)*a.step,p&&(b.fromNumber=parseInt(b.fromNumber*p,10)/p,b.toNumber=parseInt(b.toNumber*p,10)/p));g=b;aa()},ba=function(){var b={fromNumber:a.from,toNumber:a.to,fromPers:0,toPers:0,fromX:0,toX:0},d=a.max-a.min;"single"===a.type?(b.fromPers=100*((b.fromNumber-a.min)/d),b.fromX=Math.round(A/100*b.fromPers),z[0].style.left=b.fromX+"px"):"double"===a.type&&(b.fromPers=100*((b.fromNumber-a.min)/d),b.fromX=Math.round(A/100*b.fromPers),
n[0].style.left=b.fromX+"px",b.toPers=100*((b.toNumber-a.min)/d),b.toX=Math.round(A/100*b.toPers),s[0].style.left=b.toX+"px",H());g=b;aa()},aa=function(){var b,d,l,c,f,h;h=G/2;"single"===a.type?(a.hideText||(x[0].style.display="none",y[0].style.display="none",l=a.prefix+m(g.fromNumber)+a.postfix,t.html(l),f=t.outerWidth(),h=g.fromX-f/2+h,0>h&&(h=0),h>k-f&&(h=k-f),t[0].style.left=h+"px",B[0].style.display=h<K?"none":"block",C[0].style.display=h+f>k-L?"none":"block"),e.attr("value",parseInt(g.fromNumber,
10))):"double"===a.type&&(a.hideText||(b=a.prefix+m(g.fromNumber)+a.postfix,d=a.prefix+m(g.toNumber)+a.postfix,l=g.fromNumber!=g.toNumber?a.prefix+m(g.fromNumber)+" \u2014 "+a.prefix+m(g.toNumber)+a.postfix:a.prefix+m(g.fromNumber)+a.postfix,x.html(b),y.html(d),t.html(l),b=x.outerWidth(),d=g.fromX-b/2+h,0>d&&(d=0),d>k-b&&(d=k-b),x[0].style.left=d+"px",l=y.outerWidth(),c=g.toX-l/2+h,0>c&&(c=0),c>k-l&&(c=k-l),y[0].style.left=c+"px",f=t.outerWidth(),h=g.fromX+(g.toX-g.fromX)/2-f/2+h,0>h&&(h=0),h>k-f&&
(h=k-f),t[0].style.left=h+"px",d+b<c?(t[0].style.display="none",x[0].style.display="block",y[0].style.display="block"):(t[0].style.display="block",x[0].style.display="none",y[0].style.display="none"),B[0].style.display=h<K||d<K?"none":"block",C[0].style.display=h+f>k-L||c+l>k-L?"none":"block"),e.attr("value",parseInt(g.fromNumber,10)+";"+parseInt(g.toNumber,10)));"function"===typeof a.onChange&&a.onChange.call(this,g);"function"!==typeof a.onFinish||v||W||a.onFinish.call(this,g)},ca=function(){w.addClass("irs-with-grid");
var b,d="",c=0,c=0,e="";for(b=0;20>=b;b++)c=Math.floor(k/20*b),c>=k&&(c=k-1),e+='<span class="irs-grid-pol small" style="left: '+c+'px;"></span>';for(b=0;4>=b;b++)c=Math.floor(k/4*b),c>=k&&(c=k-1),e+='<span class="irs-grid-pol" style="left: '+c+'px;"></span>',p?(d=a.min+(a.max-a.min)/4*b,d=d/a.step*a.step,d=parseInt(d*p,10)/p):(d=Math.round(a.min+(a.max-a.min)/4*b),d=Math.round(d/a.step)*a.step,d=m(d)),0===b?e+='<span class="irs-grid-text" style="left: '+c+'px; text-align: left;">'+d+"</span>":4===
b?(c-=100,e+='<span class="irs-grid-text" style="left: '+c+'px; text-align: right;">'+d+"</span>"):(c-=50,e+='<span class="irs-grid-text" style="left: '+c+'px;">'+d+"</span>");V.html(e)};Y()}})},update:function(c){return this.each(function(){this.updateData(c)})},remove:function(){return this.each(function(){this.removeSlider()})}};c.fn.ionRangeSlider=function(m){if(I[m])return I[m].apply(this,Array.prototype.slice.call(arguments,1));if("object"!==typeof m&&m)c.error("Method "+m+" does not exist for jQuery.ionRangeSlider");
else return I.init.apply(this,arguments)}})(jQuery);