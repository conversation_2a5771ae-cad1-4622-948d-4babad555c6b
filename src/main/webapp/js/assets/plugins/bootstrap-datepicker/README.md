# [bootstrap-datepicker](http://eternicode.github.com/bootstrap-datepicker/) [![Build Status](https://travis-ci.org/eternicode/bootstrap-datepicker.png?branch=master)](https://travis-ci.org/eternicode/bootstrap-datepicker)

This is a fork of <PERSON>'s [original code](http://www.eyecon.ro/bootstrap-datepicker/);
thanks go to him for getting this thing started!

Please note that this fork is not used on <PERSON>'s page at this time, nor is it maintained or
contributed to by him (yet?)

Versions are incremented according to [semver](http://semver.org/).

[Online Demo](http://eternicode.github.com/bootstrap-datepicker/)

# Requirements

* [Bootstrap](http://twitter.github.com/bootstrap/) 2.0.4+
* [jQuery](http://jquery.com/) 1.7.1+

These are the specific versions bootstrap-datepicker is tested against (`js` files) and built against (`css` files).  Use other versions at your own risk.

# Example

Attached to a field with the format specified via options:

```html
<input type="text" value="02-16-2012" class="datepicker">
```
```javascript
$('.datepicker').datepicker({
    format: 'mm-dd-yyyy'
});
```

Attached to a field with the format specified via data tag:

```html
<input type="text" value="02/16/12" data-date-format="mm/dd/yy" class="datepicker" >
```
```javascript
$('.datepicker').datepicker();
```

As component:

```html
<div class="input-append date datepicker" data-date="12-02-2012" data-date-format="dd-mm-yyyy">
    <input size="16" type="text" value="12-02-2012" readonly><span class="add-on"><i class="icon-th"></i></span>
</div>
```
```javascript
$('.datepicker').datepicker();
```

Attached to non-field element, using events to work with the date values.

```html
<div class="alert alert-error" id="alert">
    <strong>Oh snap!</strong>
</div>
<table class="table">
    <thead>
        <tr>
          <th>
              Start date
              <a href="#" class="btn small" id="date-start" data-date-format="yyyy-mm-dd" data-date="2012-02-20">Change</a>
          </th>
          <th>
              End date
              <a href="#" class="btn small" id="date-end" data-date-format="yyyy-mm-dd" data-date="2012-02-25">Change</a>
          </th>
        </tr>
    </thead>
    <tbody>
        <tr>
          <td id="date-start-display">2012-02-20</td>
          <td id="date-end-display">2012-02-25</td>
        </tr>
    </tbody>
</table>
```
```javascript
var startDate = new Date(2012,1,20);
var endDate = new Date(2012,1,25);
$('#date-start')
    .datepicker()
    .on('changeDate', function(ev){
        if (ev.date.valueOf() > endDate.valueOf()){
            $('#alert').show().find('strong').text('The start date must be before the end date.');
        } else {
            $('#alert').hide();
            startDate = new Date(ev.date);
            $('#date-start-display').text($('#date-start').data('date'));
        }
        $('#date-start').datepicker('hide');
    });
$('#date-end')
    .datepicker()
    .on('changeDate', function(ev){
        if (ev.date.valueOf() < startDate.valueOf()){
            $('#alert').show().find('strong').text('The end date must be after the start date.');
        } else {
            $('#alert').hide();
            endDate = new Date(ev.date);
            $('#date-end-display').text($('#date-end').data('date'));
        }
        $('#date-end').datepicker('hide');
    });
```

As inline datepicker:

```html
<div class="datepicker"></div>
```
```javascript
$('.datepicker').datepicker();
```


# Using bootstrap-datepicker.js

Call the datepicker via javascript:

```javascript
$('.datepicker').datepicker()
```

## Dependencies

Requires bootstrap's dropdown component (`dropdowns.less`) for some styles.

A standalone .css file (including necessary dropdown styles) can be generated by running `build/build_standalone.less` through the `lessc` compiler:

```bash
$ lessc build/build_standalone.less datepicker.css
```

## Data API

As with bootstrap's own plugins, datepicker provides a data-api that can be used to instantiate datepickers without the need for custom javascript.  For most datepickers, simply set `data-provide="datepicker"` on the element you want to initialize, and it will be intialized lazily, in true bootstrap fashion.  For inline datepickers, this can alternatively be `data-provide="datepicker-inline"`; these will be immediately initialized on page load, and cannot be lazily loaded.

You can disable datepicker's data-api in the same way as you would disable other bootstrap plugins:

```javascript
$(document).off('.datepicker.data-api');
```

## No Conflict

```javascript
var datepicker = $.fn.datepicker.noConflict(); // return $.fn.datepicker to previously assigned value
$.fn.bootstrapDP = datepicker;                 // give $().bootstrapDP the bootstrap-datepicker functionality
```

## Options

All options that take a "Date" can handle a `Date` object; a String formatted according to the given `format`; or a timedelta relative to today, eg '-1d', '+6m +1y', etc, where valid units are 'd' (day), 'w' (week), 'm' (month), and 'y' (year).

Most options can be provided via data-attributes.  An option can be converted to a data-attribute by taking its name, replacing each uppercase letter with its lowercase equivalent preceded by a dash, and prepending "data-date-" to the result.  For example, `startDate` would be `data-date-start-date`, `format` would be `data-date-format`, and `daysOfWeekDisabled` would be `data-date-days-of-week-disabled`.

### format

String.  Default: 'mm/dd/yyyy'

The date format, combination of d, dd, D, DD, m, mm, M, MM, yy, yyyy.

* d, dd: Numeric date, no leading zero and leading zero, respectively.  Eg, 5, 05.
* D, DD: Abbreviated and full weekday names, respectively.  Eg, Mon, Monday.
* m, mm: Numeric month, no leading zero and leading zero, respectively.  Eg, 7, 07.
* M, MM: Abbreviated and full month names, respectively.  Eg, Jan, January
* yy, yyyy: 2- and 4-digit years, respectively.  Eg, 12, 2012.

### weekStart

Integer.  Default: 0

Day of the week start. 0 (Sunday) to 6 (Saturday)

### calendarWeeks

Boolean. Default: false

Whether or not to show week numbers to the left of week rows.

### startDate

Date.  Default: Beginning of time

The earliest date that may be selected; all earlier dates will be disabled.

### endDate

Date.  Default: End of time

The latest date that may be selected; all later dates will be disabled.

### daysOfWeekDisabled

String, Array.  Default: '', []

Days of the week that should be disabled. Values are 0 (Sunday) to 6 (Saturday). Multiple values should be comma-separated. Example: disable weekends: `'0,6'` or `[0,6]`.

### autoclose

Boolean.  Default: false

Whether or not to close the datepicker immediately when a date is selected.

### startView

Number, String.  Default: 0, 'month'

The view that the datepicker should show when it is opened.  Accepts values of 0 or 'month' for month view (the default), 1 or 'year' for the 12-month overview, and 2 or 'decade' for the 10-year overview.  Useful for date-of-birth datepickers.

### minViewMode

Number, String.  Default: 0, 'days'

Set a limit for the view mode.  Accepts: 'days' or 0, 'months' or 1, and 'years' or 2.
Gives the ability to pick only a month or an year.  The day is set to the 1st for 'months', and the month is set to January for 'years'.

### todayBtn

Boolean, "linked".  Default: false

If true or "linked", displays a "Today" button at the bottom of the datepicker to select the current date.  If true, the "Today" button will only move the current date into view; if "linked", the current date will also be selected.

### todayHighlight

Boolean.  Default: false

If true, highlights the current date.

### clearBtn

Boolean.  Default: false

If true, displays a "Clear" button at the bottom of the datepicker to clear the input value. If "autoclose" is also set to true, this button will also close the datepicker.

### keyboardNavigation

Boolean.  Default: true

Whether or not to allow date navigation by arrow keys.

### language

String.  Default: 'en'

The IETF code (eg  "en" for English, "pt-BR" for Brazilian Portuguese) of the language to use for month and day names.  These will also be used as the input's value (and subsequently sent to the server in the case of form submissions).  If a full code (eg "de-DE") is supplied the picker will first check for an "de-DE" language and if not found will fallback and check for a "de" language.  If an unknown language code is given, English will be used.  See I18N below.

### forceParse

Boolean.  Default: true

Whether or not to force parsing of the input value when the picker is closed.  That is, when an invalid date is left in the input field by the user, the picker will forcibly parse that value, and set the input's value to the new, valid date, conforming to the given `format`.

### inputs

Array.  Default: None

A list of inputs to be used in a range picker, which will be attached to the selected element.  Allows for explicitly creating a range picker on a non-standard element.

### beforeShowDay

Function(Date).  Default: $.noop

A function that takes a date as a parameter and returns one of the following values:

 * undefined to have no effect
 * A Boolean, indicating whether or not this date is selectable
 * A String representing additional CSS classes to apply to the date's cell
 * An object with the following properties:
   * `enabled`: same as the Boolean value above
   * `classes`: same as the String value above
   * `tooltip`: a tooltip to apply to this date, via the `title` HTML attribute

### orientation

String.  Default: "auto"

A space-separated string consisting of one or two of "left" or "right", "top" or "bottom", and "auto" (may be omitted); for example, "top left", "bottom" (horizontal orientation will default to "auto"), "right" (vertical orientation will default to "auto"), "auto top".  Allows for fixed placement of the picker popup.

"orientation" refers to the location of the picker popup's "anchor"; you can also think of it as the location of the trigger element (input, component, etc) relative to the picker.

"auto" triggers "smart orientation" of the picker.  Horizontal orientation will default to "left" and left offset will be tweaked to keep the picker inside the browser viewport; vertical orientation will simply choose "top" or "bottom", whichever will show more of the picker in the viewport.

## Markup

Format a component.

```html
<div class="input-append date datepicker" data-date="12-02-2012" data-date-format="dd-mm-yyyy">
    <input class="span2" size="16" type="text" value="12-02-2012"><span class="add-on"><i class="icon-th"></i></span>
</div>
```

Create a date-range picker:

```html
<div class="input-daterange" id="datepicker">
    <input class="input-small" name="start" value="2012-04-05" />
    <span class="add-on">to</span>
    <input class="input-small" name="end" value="2012-04-07" />
</div>
```

## Methods

### .datepicker(options)

Initializes an datepicker.

### remove

Arguments: None

Remove the datepicker.  Removes attached events, internal attached objects, and
added HTML elements.

```javascript
$('.datepicker').datepicker('remove');
```

### show

Arguments: None

Show the datepicker.

```javascript
$('.datepicker').datepicker('show');
```

### hide

Arguments: None

Hide the datepicker.

```javascript
$('.datepicker').datepicker('hide');
```

### update

Arguments:

* date (String|Date)

Update the datepicker with given argument or the current input value.

If `date` is provided and is a Date objects, it is assumed to be a "local" date object, and will be converted to UTC for internal use.

```javascript
$('.datepicker').datepicker('update');
$('.datepicker').datepicker('update', '2011-03-05');
$('.datepicker').datepicker('update', new Date(2011, 2, 5));
```

### setDate

Arguments:

* date (Date)

Sets the internal date.  `date` is assumed to be a "local" date object, and will be converted to UTC for internal use.

### setUTCDate

Arguments:

* date (Date)

Sets the internal date.  `date` is assumed to be a UTC date object, and will not be converted.

### getDate

Arguments: None

Returns a localized date object representing the internal date object of the first datepicker in the selection.

### setUTCDate

Arguments: None

Returns the internal UTC date object, as-is and unconverted to local time, of the first datepicker in the selection.

### setStartDate

Arguments:

* startDate (Date)

Sets a new lower date limit on the datepicker.

```javascript
$('.datepicker').datepicker('setStartDate', '2012-01-01');
```

Omit startDate (or provide an otherwise falsey value) to unset the limit.

```javascript
$('.datepicker').datepicker('setStartDate');
$('.datepicker').datepicker('setStartDate', null);
```

### setEndDate

Arguments:

* endDate (Date)

Sets a new upper date limit on the datepicker.

```javascript
$('.datepicker').datepicker('setEndDate', '2012-12-31');
```

Omit endDate (or provide an otherwise falsey value) to unset the limit.

```javascript
$('.datepicker').datepicker('setEndDate');
$('.datepicker').datepicker('setEndDate', null);
```

### setDaysOfWeekDisabled

Arguments:

* daysOfWeekDisabled (String|Array)

Sets the days of week that should be disabled.

```javascript
$('.datepicker').datepicker('setDaysOfWeekDisabled', [0,6]);
```

Omit daysOfWeekDisabled (or provide an otherwise falsey value) to unset the disabled days.

```javascript
$('.datepicker').datepicker('setDaysOfWeekDisabled');
$('.datepicker').datepicker('setDaysOfWeekDisabled', null);
```

## Events

Datepicker triggers a number of events in certain circumstances.  All events have extra data attached to the event object that is passed to any event handlers:

```javascript
$('.datepicker').datepicker()
    .on(picker_event, function(e){
        # `e` here contains the extra attributes
    });
```

* `date`: the relevant Date object, in local timezone.
* `format([format])`: a function to make formatting `date` easier.  `format` can be any format string that datepicker supports.  If `format` is not given, the format set on the datepicker will be used.

### show

Fired when the date picker is displayed.

### hide

Fired when the date picker is hidden.

### clearDate

Fired when the date is cleared, normally when the "clear" button (enabled with the `clearBtn` option) is pressed.

### changeDate

Fired when the date is changed.

### changeYear

Fired when the *view* year is changed from decade view.

### changeMonth

Fired when the *view* month is changed from year view.

## Keyboard support

The datepicker includes some keyboard navigation:

### up, down, left, right arrow keys

By themselves, left/right will move backward/forward one day, up/down will move back/forward one week.

With the shift key, up/left will move backward one month, down/right will move forward one month.

With the ctrl key, up/left will move backward one year, down/right will move forward oone year.

Shift+ctrl behaves the same as ctrl -- that is, it does not change both month and year simultaneously, only the year.

### escape

The escape key can be used to hide and re-show the datepicker; this is necessary if the user wants to manually edit the value.

### enter

When the picker is visible, enter will simply hide it.  When the picker is not visible, enter will have normal effects -- submitting the current form, etc.

## I18N

The plugin supports i18n for the month and weekday names and the `weekStart` option.  The default is English ('en'); other available translations are avilable in the `js/locales/` directory, simply include your desired locale after the plugin.  To add more languages, simply add a key to `$.fn.datepicker.dates`, before calling `.datepicker()`.  Example:

```javascript
$.fn.datepicker.dates['en'] = {
    days: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
    daysShort: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
    daysMin: ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa", "Su"],
    months: ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
    monthsShort: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
    today: "Today"
};
```

Right-to-left languages may also include `rtl: true` to make the calendar display appropriately.

If your browser (or those of your users) is displaying characters wrong, chances are the browser is loading the javascript file with a non-unicode encoding.  Simply add `charset="UTF-8"` to your `script` tag:

```html
<script type="text/javascript" src="bootstrap-datepicker.XX.js" charset="UTF-8"></script>
```

```javascript
$('.datepicker').datepicker({
    language: XX    //as you defined in bootstrap-datepicker.XX.js
});
```
