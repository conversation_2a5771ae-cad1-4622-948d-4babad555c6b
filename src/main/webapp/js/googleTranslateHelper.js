/*!
 * Google translate API help library
 * 
 * version: 1.0
 * 
 * author: <PERSON><PERSON><PERSON>
 * 
 * ref library: jquery.js 
 * 
 * description:
 * Provide the encapsulation request method and analysis for Google translation API.
 * Language list Url: http://api.fanyi.Google.com/api/trans/product/apidoc#languageList

 */

var googleTranslate = {
	/**
	 * method:getTranslateDtd
	 * 
	 * @param {String} query - original
	 * @param {String} fromLanguage - Translation source language
	 * @param {String} toLanguage - Translation language
	 * @param {Deferred} dtd - Deferred Obj
	 * 
	 * @desc
	 * Through the https protocol using get way to request Google translation API to obtain the translation content.
	 * 
	 */
	getTranslateDtd: function(query, toLanguage, dtd) {
		if(toLanguage == 'US') toLanguage = 'en';
		if(toLanguage == 'CA') toLanguage = 'en';
		if(toLanguage == 'UK') toLanguage = 'en';
		if(toLanguage == 'FR') toLanguage = 'fr';
		if(toLanguage == 'DE') toLanguage = 'de';
		if(toLanguage == 'IT') toLanguage = 'it';
		if(toLanguage == 'ES') toLanguage = 'es';
		if(toLanguage == 'US') toLanguage = 'en';
		if(toLanguage == 'JP') toLanguage = 'ja';
		if(toLanguage == 'IN') toLanguage = 'hi';
		if(toLanguage == 'CN') toLanguage = 'cn';
		query = encodeURIComponent(query);
		$.ajax({
			url: 'http://query.yahooapis.com/v1/public/yql',
			dataType: 'jsonp',
			data: {
				q: "select * from json where url=\" http://translate.google.cn/translate_a/single?client=gtx&sl=auto&tl=" + toLanguage + "&dt=t&q=" + query + "\"",
				format: "json"
			},
			success: function(data) {
				dtd.resolve(data);
			},
			error: function() {
				dtd.reject();
			}
		});
	},

	/**
	 * method:toFormat
	 * 
	 * @param {Object} sourceData - By translated data
	 * 
	 * @return {Object} Format Data
	 * 
	 * @desc
	 * Converts the original api return data format to the data format provided by the library
	 * 
	 */
	toFormat: function(sourceData) {
		try{
			var toLanguage = sourceData.query.lang;
			var fromLanguage = sourceData.query.results.json.json[2];
			var dst = sourceData.query.results.json.json[0].json.json[0];
			var src = sourceData.query.results.json.json[0].json.json[1];
			var formatData = {
				'from': fromLanguage,
				'to': toLanguage,
				'src': src,
				'dst': dst
			};
			return formatData;
		}catch(e){
			console.log(e);
			return undefined;
		}
	}

};