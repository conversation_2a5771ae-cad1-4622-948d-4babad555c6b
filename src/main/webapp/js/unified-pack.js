/**
 * 统一包装页面JavaScript
 * 支持多种包装场景的统一处理
 */

// 全局变量
var currentMode = 'auto'; // 当前扫描模式
var successCount = 0; // 成功计数
var totalCount = 0; // 总计数
var currentOrders = []; // 当前订单列表
var currentPackageInfo = null; // 当前包装信息

// 页面初始化
$(document).ready(function() {
    initializePage();
    bindEvents();
    loadPrinterSettings();
});

/**
 * 初始化页面
 */
function initializePage() {
    // 设置焦点到扫描输入框
    $('#scan-input').focus();
    
    // 初始化计数器
    updateCounters();
    
    // 加载缓存的成功计数
    var storage = new WebStorageCache();
    var cachedSuccess = storage.get('unified_pack_success_count');
    if (cachedSuccess) {
        successCount = parseInt(cachedSuccess);
        updateCounters();
    }
    
    // 初始化打印机
    if (typeof initPrinter === 'function') {
        initPrinter();
    }
    
    // 获取打印机列表
    if (typeof getPrinterList === 'function') {
        getPrinterList();
    }
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 扫描模式切换
    $('.scan-mode-btn').click(function() {
        var mode = $(this).data('mode');
        switchScanMode(mode);
    });
    
    // 扫描输入框事件
    $('#scan-input').on('input', function() {
        var value = $(this).val().trim();
        if (value.length > 0) {
            // 自动识别模式下，根据输入内容提示
            if (currentMode === 'auto') {
                showInputHint(value);
            }
        }
    });
    
    // 清空按钮事件
    $('#clear-btn').click(function() {
        clearScan();
    });
}

/**
 * 切换扫描模式
 */
function switchScanMode(mode) {
    currentMode = mode;
    
    // 更新按钮状态
    $('.scan-mode-btn').removeClass('active');
    $('[data-mode="' + mode + '"]').addClass('active');
    
    // 更新状态显示
    $('#current-mode').text(getModeDisplayName(mode));
    
    // 更新输入框提示
    updateInputPlaceholder(mode);
    
    // 清空当前内容
    clearMainContent();
    
    // 重新聚焦输入框
    $('#scan-input').focus();
}

/**
 * 获取模式显示名称
 */
function getModeDisplayName(mode) {
    var modeNames = {
        'auto': '自动',
        'order': '订单',
        'sku': 'SKU',
        'split': '拆分'
    };
    return modeNames[mode] || '自动';
}

/**
 * 更新输入框提示
 */
function updateInputPlaceholder(mode) {
    var placeholders = {
        'auto': '请扫描订单号、SKU或包裹号...',
        'order': '请扫描订单号...',
        'sku': '请扫描SKU或唯一码...',
        'split': '请扫描原包裹号...'
    };
    $('#scan-input').attr('placeholder', placeholders[mode] || placeholders['auto']);
}

/**
 * 处理扫描
 */
function handleScan() {
    var input = $('#scan-input').val().trim();
    if (!input) {
        showMessage('请输入扫描内容', 'error');
        return;
    }
    
    // 增加总计数
    totalCount++;
    updateCounters();
    
    // 根据模式处理扫描
    switch (currentMode) {
        case 'auto':
            handleAutoScan(input);
            break;
        case 'order':
            handleOrderScan(input);
            break;
        case 'sku':
            handleSkuScan(input);
            break;
        case 'split':
            handleSplitScan(input);
            break;
        default:
            handleAutoScan(input);
    }
}

/**
 * 自动识别扫描
 */
function handleAutoScan(input) {
    showMessage('正在识别扫描内容...', 'info');
    
    $.ajax({
        url: CONTEXT_PATH + 'apv/unified/pack/autoScan',
        type: 'POST',
        data: { input: input },
        timeout: 30000,
        success: function(response) {
            if (response.success) {
                handleScanSuccess(response.data, input);
            } else {
                showMessage(response.message || '扫描失败', 'error');
            }
        },
        error: function() {
            showMessage('扫描请求失败，请重试', 'error');
        }
    });
}

/**
 * 订单扫描
 */
function handleOrderScan(input) {
    showMessage('正在查询订单信息...', 'info');
    
    $.ajax({
        url: CONTEXT_PATH + 'apv/split/pack/scanOrder',
        type: 'GET',
        data: { input: input },
        timeout: 30000,
        success: function(response) {
            if (response.success) {
                displayOrderList(response.data);
                successCount++;
                updateCounters();
                showMessage('订单查询成功', 'success');
            } else {
                showMessage(response.message || '未找到订单', 'error');
            }
        },
        error: function() {
            showMessage('订单查询失败，请重试', 'error');
        }
    });
}

/**
 * SKU扫描
 */
function handleSkuScan(input) {
    showMessage('正在匹配SKU...', 'info');
    
    // 如果有当前订单，尝试匹配SKU
    if (currentOrders.length > 0) {
        var matchedOrder = findOrderBySku(input);
        if (matchedOrder) {
            handleSkuMatch(matchedOrder, input);
        } else {
            showMessage('当前订单中未找到匹配的SKU', 'error');
        }
    } else {
        // 没有当前订单，尝试查找包含此SKU的订单
        searchOrdersBySku(input);
    }
}

/**
 * 拆分包装扫描
 */
function handleSplitScan(input) {
    showMessage('正在处理拆分包装...', 'info');
    
    $.ajax({
        url: CONTEXT_PATH + 'apv/split/pack/scanOrder',
        type: 'GET',
        data: { input: input },
        timeout: 30000,
        success: function(response) {
            if (response.success) {
                displaySplitOrders(response.data);
                successCount++;
                updateCounters();
                showMessage('拆分订单加载成功', 'success');
            } else {
                showMessage(response.message || '未找到拆分订单', 'error');
            }
        },
        error: function() {
            showMessage('拆分订单查询失败，请重试', 'error');
        }
    });
}

/**
 * 处理扫描成功
 */
function handleScanSuccess(data, input) {
    if (data.type === 'order') {
        displayOrderList(data.orders);
    } else if (data.type === 'sku') {
        handleSkuMatch(data.order, input);
    } else if (data.type === 'split') {
        displaySplitOrders(data.orders);
    }
    
    successCount++;
    updateCounters();
    showMessage('扫描成功', 'success');
    
    // 清空输入框并重新聚焦
    $('#scan-input').val('').focus();
}

/**
 * 显示订单列表
 */
function displayOrderList(orders) {
    currentOrders = orders;
    var html = '';
    
    orders.forEach(function(order) {
        html += buildOrderCard(order);
    });
    
    $('#order-list-container').html(html);
    
    // 在主内容区域显示订单详情
    if (orders.length === 1) {
        displayOrderDetails(orders[0]);
    } else {
        displayMultipleOrdersView(orders);
    }
}

/**
 * 构建订单卡片HTML
 */
function buildOrderCard(order) {
    var statusClass = getStatusClass(order.status);
    var html = '<div class="order-info-card" data-apv-no="' + order.apvNo + '">';
    html += '<div class="order-header">';
    html += '<span class="order-number">' + order.apvNo + '</span>';
    html += '<span class="order-status ' + statusClass + '">' + order.statusName + '</span>';
    html += '</div>';
    html += '<div class="order-size">' + (order.waybillSize || '100*100') + '</div>';
    
    if (order.whApvItems && order.whApvItems.length > 0) {
        html += '<table class="sku-table">';
        html += '<thead><tr><th>SKU</th><th>数量</th></tr></thead>';
        html += '<tbody>';
        order.whApvItems.forEach(function(item) {
            html += '<tr>';
            html += '<td>' + item.sku + '</td>';
            html += '<td>' + item.saleQuantity + '</td>';
            html += '</tr>';
        });
        html += '</tbody></table>';
    }
    
    html += '</div>';
    return html;
}

/**
 * 获取状态样式类
 */
function getStatusClass(status) {
    if (status === 1 || status === 31) {
        return 'status-waiting';
    } else if (status === 2 || status === 32) {
        return 'status-processing';
    } else {
        return 'status-completed';
    }
}

/**
 * 显示订单详情
 */
function displayOrderDetails(order) {
    var html = '<div class="package-info">';
    html += '<h4>包装信息</h4>';
    html += '<p><strong>订单号：</strong>' + order.apvNo + '</p>';
    html += '<p><strong>状态：</strong>' + order.statusName + '</p>';
    html += '<p><strong>面单尺寸：</strong>' + (order.waybillSize || '100*100') + '</p>';
    html += '</div>';
    
    if (order.whApvItems && order.whApvItems.length > 0) {
        html += '<table class="sku-table">';
        html += '<thead><tr><th>SKU</th><th>订单数量</th><th>已包装</th></tr></thead>';
        html += '<tbody>';
        order.whApvItems.forEach(function(item) {
            html += '<tr>';
            html += '<td>' + item.sku + '</td>';
            html += '<td>' + item.saleQuantity + '</td>';
            html += '<td>' + (item.packQty || 0) + '</td>';
            html += '</tr>';
        });
        html += '</tbody></table>';
    }
    
    $('#main-content-area').html(html);
    
    // 更新面单尺寸信息
    $('#waybill-size-info').text('面单尺寸：' + (order.waybillSize || '100*100'));
}

/**
 * 更新计数器
 */
function updateCounters() {
    $('#success-count').text(successCount);
    $('#total-count').text(totalCount);
    
    // 缓存成功计数
    var storage = new WebStorageCache();
    storage.set('unified_pack_success_count', successCount, {exp: 86400}); // 24小时过期
}

/**
 * 显示消息
 */
function showMessage(message, type) {
    var messageArea = $('#message-area');
    var className = type === 'error' ? 'error-message' : 
                   type === 'success' ? 'success-message' : 'info-message';
    
    messageArea.removeClass('error-message success-message info-message')
              .addClass(className)
              .text(message)
              .show();
    
    // 3秒后自动隐藏
    setTimeout(function() {
        messageArea.fadeOut();
    }, 3000);
}

/**
 * 清空扫描
 */
function clearScan() {
    $('#scan-input').val('').focus();
    $('#message-area').hide();
}

/**
 * 清空主内容区域
 */
function clearMainContent() {
    $('#main-content-area').html('<div class="bubble-container"><div class="bubble-text">请扫描订单号或SKU开始包装</div></div>');
    $('#order-list-container').empty();
    currentOrders = [];
}

/**
 * 显示输入提示
 */
function showInputHint(value) {
    // 根据输入内容显示提示
    var hint = '';
    if (value.startsWith('YST')) {
        hint = '检测到订单号格式';
    } else if (value.length > 10 && /^[A-Z0-9]+$/.test(value)) {
        hint = '检测到SKU格式';
    } else if (value.includes('-') || value.includes('_')) {
        hint = '检测到包裹号格式';
    }
    
    if (hint) {
        // 可以在这里显示提示信息
        console.log(hint);
    }
}

/**
 * 加载打印机设置
 */
function loadPrinterSettings() {
    // 加载打印机配置
    if (typeof window.getPrinterList === 'function') {
        window.getPrinterList();
    }
}
