<?xml version='1.0' encoding='utf-8'?>
<ehcache>

	<!-- 
	timeToIdleSeconds=120
	timeToLiveSeconds=180
	表示此缓存最多可以存活3分钟，如果期间超过2分钟未访问 那么此缓存失效
	 -->

	<diskStore path="java.io.tmpdir" />
	
	<defaultCache maxElementsInMemory="10000" eternal="false"
		overflowToDisk="true" timeToIdleSeconds="500" timeToLiveSeconds="1000"
		diskPersistent="false" diskExpiryThreadIntervalSeconds="120" />
	
	<!-- 用户 -->
	<cache name="USER_CACHE" maxElementsInMemory="10000" eternal="true"
		timeToIdleSeconds="30" timeToLiveSeconds="60" overflowToDisk="true">
		<cacheExtensionFactory 
			class="com.whq.tool.cache.ehcache.EhcacheExtensionFactory"
			properties="loadType=lazy"/>
		<cacheEventListenerFactory class="com.whq.tool.cache.ehcache.CacheRefreshCacheEventListenerFactory" />	
	</cache>
	
	<!-- 系统参数 -->
	<cache name="SYSTEM_PARAM_CACHE" maxElementsInMemory="10000" eternal="true"
		timeToIdleSeconds="500" timeToLiveSeconds="1000" overflowToDisk="true">
		<cacheExtensionFactory 
			class="com.whq.tool.cache.ehcache.EhcacheExtensionFactory"
			properties="loadType=lazy"/>
		<cacheEventListenerFactory class="com.whq.tool.cache.ehcache.CacheRefreshCacheEventListenerFactory" />	
	</cache>

	<!-- 唯一码自增编号 -->
	<cache name="UNIQUE_INCR_CACHE" maxElementsInMemory="10000" eternal="true"
		timeToIdleSeconds="500" timeToLiveSeconds="1000" overflowToDisk="true">
		<cacheExtensionFactory
			class="com.whq.tool.cache.ehcache.EhcacheExtensionFactory"
			properties="loadType=lazy"/>
		<cacheEventListenerFactory class="com.whq.tool.cache.ehcache.CacheRefreshCacheEventListenerFactory" />
	</cache>

	
	<!-- apv 扫描-->
	<cache name="SCAN_APV_ID" maxElementsInMemory="10000" eternal="true"
		timeToIdleSeconds="500" timeToLiveSeconds="1000" overflowToDisk="true">
		<cacheExtensionFactory 
			class="com.whq.tool.cache.ehcache.EhcacheExtensionFactory"
			properties="loadType=lazy"/>
		<cacheEventListenerFactory class="com.whq.tool.cache.ehcache.CacheRefreshCacheEventListenerFactory" />	
	</cache>
	
	<!-- 仓库列表缓存 -->
	<cache name="WAREHOUSE_CACHE" maxElementsInMemory="10000" eternal="true"
		timeToIdleSeconds="500" timeToLiveSeconds="1000" overflowToDisk="true">
		<cacheExtensionFactory 
			class="com.whq.tool.cache.ehcache.EhcacheExtensionFactory"
			properties="loadType=lazy"/>
		<cacheEventListenerFactory class="com.whq.tool.cache.ehcache.CacheRefreshCacheEventListenerFactory" />	
	</cache>
	
	<!-- 库位区域列表缓存 -->
	<cache name="STOCK_ZONE_CACHE" maxElementsInMemory="10000" eternal="true"
		timeToIdleSeconds="1000" timeToLiveSeconds="2000" overflowToDisk="true">
		<cacheExtensionFactory 
			class="com.whq.tool.cache.ehcache.EhcacheExtensionFactory"
			properties="loadType=lazy"/>
		<cacheEventListenerFactory class="com.whq.tool.cache.ehcache.CacheRefreshCacheEventListenerFactory" />	
	</cache>
	
	<!-- 国家列表缓存 -->
	<cache name="COUNTRY_CACHE" maxElementsInMemory="10000" eternal="true"
		timeToIdleSeconds="1000" timeToLiveSeconds="2000" overflowToDisk="true">
		<cacheExtensionFactory 
			class="com.whq.tool.cache.ehcache.EhcacheExtensionFactory"
			properties="loadType=lazy"/>
		<cacheEventListenerFactory class="com.whq.tool.cache.ehcache.CacheRefreshCacheEventListenerFactory" />	
	</cache>
	
	<!-- 物流列表缓存 -->
	<cache name="LOGISTICS_CACHE" maxElementsInMemory="10000" eternal="true"
		timeToIdleSeconds="1000" timeToLiveSeconds="2000" overflowToDisk="true">
		<cacheExtensionFactory 
			class="com.whq.tool.cache.ehcache.EhcacheExtensionFactory"
			properties="loadType=lazy"/>
		<cacheEventListenerFactory class="com.whq.tool.cache.ehcache.CacheRefreshCacheEventListenerFactory" />	
	</cache>
	
	<!-- 平台列表缓存 -->
	<cache name="PLATFORM_CACHE" maxElementsInMemory="10000" eternal="true"
		timeToIdleSeconds="1000" timeToLiveSeconds="2000" overflowToDisk="true">
		<cacheExtensionFactory 
			class="com.whq.tool.cache.ehcache.EhcacheExtensionFactory"
			properties="loadType=lazy"/>
		<cacheEventListenerFactory class="com.whq.tool.cache.ehcache.CacheRefreshCacheEventListenerFactory" />	
	</cache>
	
</ehcache>