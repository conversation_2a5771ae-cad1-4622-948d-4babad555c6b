#汉海达
yml-config:
  active-mq-host: tcp://**********48:61616
  active-mq-queue-scan: shipscan
  rabbit-mq-queue-oms: PUSH_WMS_ORDER
  rabbit-mq-queue-oms-push-sku-weight: SALE_SUTTLE_AUDIT_TO_WMS
  rabbit-mq-queue-oms-push-lack-order: SALE_WMS_LACK_ORDER
  rabbit-mq-queue-tms: TMS_WMS_SHIPPING_INFO_QUEUE
  rabbit-mq-queue-pms: PMS_WH_EXCEPTION_PMS_QUEUE
  rabbit-mq-queue-pms-push-purchase-order: EPMS_WMS_PURCHASE_INFO_QUEUE
  rabbit-mq-queue-pms-push-return-purchase-order-info: EPMS_PUSH_RETURN_ORDER_TO_WMS_QUEUE
  rabbit-mq-queue-fms-sku-curd: FMS_WMS_SKU_CRUD_QUEUE
  rabbit-mq-queue-product-qualtity-item-to-wms: PRODUCT_QUALTITY_ITEM_TO_WMS_QUEUE
  rabbit-mq-queue-product-wms-receipt-info-queue: PRODUCT_WMS_RECEIPT_INFO_QUEUE
  rabbit-mq-queue-epms-oos-to-wms: EPMS_OOS_TO_WMS_QUEUE_TO_HHD
  rabbit-mq-queue-product-2-wms-category-att: PRODUCT_2_WMS_CATEGORY_ATT_QUEUE2
  rabbit-mq-queue-fms-qc-sku-curd: FMS_2_PRODUCT_QCTU_QUEUE2
  rabbit-mq-queue-product-identification: PRODUCT_SINGLE_UPDATE_TO_WMS_QUEUE
  rabbit-mq-queue-user-mgt-push-user-2-wms-queue: USER_MGT_PUSH_USER_2_WMS_QUEUE
  rabbit-mq-queue-user-mgt-push-dimission-user-2-wms-queue: USER_MGT_PUSH_DIMISSION_USER_2_WMS_QUEUE
  rabbit-mq-queue-product-sync-notice-queue: PRODUCT_SYNC_NOTICE_QUEUE
  rabbit-mq-switch-listener: true

spring:
  #  data:
  #    elasticsearch:
  #      cluster-name: es-cluster-prod
  #      cluster-nodes: ***********:9300,***********:9300,***********:9300
  rabbitmq:
    addresses: *************:5672
    #addresses: ************:5672,************:5672,************:5672
    username: admin
    password: '!QAZxsw2'
  datasource:
    wms:
      type: com.alibaba.druid.pool.DruidDataSource
      driver-class-name: com.mysql.jdbc.Driver
      url: jdbc:mysql://**********14:3306/wms?useUnicode=true&characterEncoding=UTF-8&rewriteBatchedStatements=true&autoReconnect=true&useSSL=false
      username: root
      password: 'Admin-0808'
      validation-query: SELECT 1;
      sql-script-encoding: UTF-8
      initial-size: 1
      max-active: 1000
      max-wait: 60000
      min-idle: 1
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-open-prepared-statements: 20
      remove-abandoned-timeout: 5000
      max-pool-prepared-statement-per-connection-size: 300
    wms-query:
      type: com.alibaba.druid.pool.DruidDataSource
      driver-class-name: com.mysql.jdbc.Driver
      url: jdbc:mysql://**********24:3306/wms?useUnicode=true&characterEncoding=UTF-8&rewriteBatchedStatements=true&autoReconnect=true&useSSL=false
      username: readonly
      password: '!QAZxsw2'
      validation-query: SELECT 1;
      sql-script-encoding: UTF-8
      initial-size: 1
      max-active: 1000
      max-wait: 60000
      min-idle: 1
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-open-prepared-statements: 20
      remove-abandoned-timeout: 5000
      max-pool-prepared-statement-per-connection-size: 300
    amq:
      type: com.alibaba.druid.pool.DruidDataSource
      driver-class-name: com.mysql.jdbc.Driver
      url: jdbc:mysql://**********14:3306/wms_amq?useUnicode=true&characterEncoding=UTF-8&rewriteBatchedStatements=true&autoReconnect=true
      username: root
      password: 'Admin-0808'
      validation-query: SELECT 1;
      sql-script-encoding: UTF-8
      initial-size: 1
      max-active: 1000
      max-wait: 60000
      min-idle: 1
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-open-prepared-statements: 20
      remove-abandoned-timeout: 5000
      max-pool-prepared-statement-per-connection-size: 300
    publish-tidb:
      url: ********************************************************************************************************************************************
      type: com.alibaba.druid.pool.DruidDataSource
      driver-class-name: com.mysql.jdbc.Driver
      username: readonly_for_tidb
      password: "!QAZxsw2"
      validation-query: SELECT 1;
      sql-script-encoding: UTF-8
      initial-size: 1
      max-active: 300
      max-wait: 60000
      min-idle: 1
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-open-prepared-statements: 20
      remove-abandoned-timeout: 5000
      max-pool-prepared-statement-per-connection-size: 300
  redis:
    url: redis://**********49:6379
    host: **********49
    port: 6379
    timeout: 5000
    lettuce:
      pool:
        max-active: 200
        max-idle: 10
        max-wait: -1
        min-idle: 1
  kafka:
    # 指定kafka 代理地址，可以多个
    bootstrap-servers: ***********:9092,***********:9092,***********:9092
    #bootstrap-servers: ************:9092
    producer:
      # 消息序列化方式
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      # 重试次数
      retries: 1
      request:
        timeout:
          ms: 30000
    consumer:
      # 键的反序列化方式
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 值的反序列化方式
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 是否自动提交偏移量，默认值是true,为了避免出现重复数据和数据丢失，可以把它设置为false,然后手动提交偏移量
      enable-auto-commit: false
    listener:
      concurrency: 1
      #listner负责ack，每调用一次，就立即commit
      ack-mode: manual_immediate
#  boot:
#    admin:
#      client:
#        instance:
#          name: wms-service-hhd
#          prefer-ip: true
#        url: http://**********:31110
#        auto-registration: true
#management:
#  endpoints:
#    web:
#      exposure:
#        include: "*"
#        exclude: env
#  endpoint:
#    health:
#      show-details: ALWAYS
#  health:
#    rabbit:
#      enabled: false
erp:
  # 文件服务器相关
  seaweed:
    url: http://************:8888
    max-total: 200
cainiao-linkcp:
    appkey: 293730
    secretKey: 45220y50JjP555h6LP30c3LTx5071SeZ
    url: https://link.cainiao.com/gateway/link.do
    fromCode: SZU902
### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
xxl:
  job:
    admin:
      addresses: http://**********00:8181/xxl-job-admin
    accessToken: 5df26666b185fbf0b3437482125d341e
    executor:
      address: ''
      ip:
      port: 9999
      logretentiondays: 7
      logpath: /var/log/xxl-job
# amazon sp-api服务器配置
platform:
  amazon:
    sp_local_dev_service_url: http://**********:31100/amazon-sp-local
# 产品系统相关
product:
  config:
    push_check_in_exception_url: http://**********:31100/product/api/purchase/creatAbnormalOptimization

#跨越API配置
ky-express:
  appKey: '83198'
  appSecret: B277CE3B0375EDF1C8FF2EA57C501F47
  customerCode: '075571599510'
  platformFlag: L348B6YCRP7X8OTG5NFXZQNY9QWOYCEC
  callbackUrl: