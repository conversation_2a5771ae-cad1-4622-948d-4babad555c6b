<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryMergeSkuStockMatchRecordCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM merge_sku_stock_match_record
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND merge_sku_manage_id = :merge_sku_manage_id]>
        <[AND merge_sku_manage_id in (:merge_sku_manage_id_list)]>
        <[AND discard_stock_id = :discard_stock_id]>
        <[AND match_stock_id = :match_stock_id]>
        <[AND match_stock_location = :match_stock_location]>
        <[AND creation_date = :creation_date]>
        <[AND creation_by = :creation_by]>
        <[AND last_update_by = :last_update_by]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryMergeSkuStockMatchRecordList" >
    <content >
      <![CDATA[
        SELECT id, merge_sku_manage_id, discard_stock_id, match_stock_id, match_stock_location, 
        creation_date, creation_by, last_update_by, last_update_date
        FROM merge_sku_stock_match_record
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND merge_sku_manage_id = :merge_sku_manage_id]>
        <[AND merge_sku_manage_id in (:merge_sku_manage_id_list)]>
        <[AND discard_stock_id = :discard_stock_id]>
        <[AND match_stock_id = :match_stock_id]>
        <[AND match_stock_location = :match_stock_location]>
        <[AND creation_date = :creation_date]>
        <[AND creation_by = :creation_by]>
        <[AND last_update_by = :last_update_by]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryMergeSkuStockMatchRecordByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, merge_sku_manage_id, discard_stock_id, match_stock_id, match_stock_location, 
        creation_date, creation_by, last_update_by, last_update_date
        FROM merge_sku_stock_match_record
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryMergeSkuStockMatchRecord" >
    <content >
      <![CDATA[
        SELECT id, merge_sku_manage_id, discard_stock_id, match_stock_id, match_stock_location, 
        creation_date, creation_by, last_update_by, last_update_date
        FROM merge_sku_stock_match_record
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND merge_sku_manage_id = :merge_sku_manage_id]>
        <[AND discard_stock_id = :discard_stock_id]>
        <[AND match_stock_id = :match_stock_id]>
        <[AND match_stock_location = :match_stock_location]>
        <[AND creation_date = :creation_date]>
        <[AND creation_by = :creation_by]>
        <[AND last_update_by = :last_update_by]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createMergeSkuStockMatchRecord" >
    <content >
      <![CDATA[
        INSERT INTO merge_sku_stock_match_record (merge_sku_manage_id, discard_stock_id, match_stock_id, match_stock_location, 
          creation_date, creation_by, last_update_by, last_update_date)
        VALUES (:merge_sku_manage_id, :discard_stock_id, :match_stock_id, :match_stock_location, 
          :creation_date, :creation_by, :last_update_by, :last_update_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteMergeSkuStockMatchRecordByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM merge_sku_stock_match_record
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateMergeSkuStockMatchRecordByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE merge_sku_stock_match_record
        SET <[merge_sku_manage_id = :merge_sku_manage_id,]>
          <[discard_stock_id = :discard_stock_id,]>
          <[match_stock_id = :match_stock_id,]>
          <[match_stock_location = :match_stock_location,]>
          <[creation_date = :creation_date,]>
          <[creation_by = :creation_by,]>
          <[last_update_by = :last_update_by,]>
          <[last_update_date = :last_update_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>