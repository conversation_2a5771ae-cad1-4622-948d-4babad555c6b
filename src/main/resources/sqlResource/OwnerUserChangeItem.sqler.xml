<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryOwnerUserChangeItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM owner_user_change_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND owner_user_change_id = :owner_user_change_id]>
        <[AND old_owner_user_id = :old_owner_user_id]>
        <[AND new_owner_user_id = :new_owner_user_id]>
        <[AND sku = :sku]>
        <[AND allot_num = :allot_num]>
        <[AND stock_type = :stock_type]>
        <[AND create_by = :create_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryOwnerUserChangeItemList" >
    <content >
      <![CDATA[
        SELECT id, owner_user_change_id, old_owner_user_id, new_owner_user_id, sku, allot_num, 
        stock_type, create_by, creation_date, last_updated_by, last_update_date
        FROM owner_user_change_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND owner_user_change_id = :owner_user_change_id]>
        <[AND old_owner_user_id = :old_owner_user_id]>
        <[AND new_owner_user_id = :new_owner_user_id]>
        <[AND sku = :sku]>
        <[AND allot_num = :allot_num]>
        <[AND stock_type = :stock_type]>
        <[AND create_by = :create_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryOwnerUserChangeItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, owner_user_change_id, old_owner_user_id, new_owner_user_id, sku, allot_num, 
        stock_type, create_by, creation_date, last_updated_by, last_update_date
        FROM owner_user_change_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryOwnerUserChangeItem" >
    <content >
      <![CDATA[
        SELECT id, owner_user_change_id, old_owner_user_id, new_owner_user_id, sku, allot_num, 
        stock_type, create_by, creation_date, last_updated_by, last_update_date
        FROM owner_user_change_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND owner_user_change_id = :owner_user_change_id]>
        <[AND old_owner_user_id = :old_owner_user_id]>
        <[AND new_owner_user_id = :new_owner_user_id]>
        <[AND sku = :sku]>
        <[AND allot_num = :allot_num]>
        <[AND stock_type = :stock_type]>
        <[AND create_by = :create_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createOwnerUserChangeItem" >
    <content >
      <![CDATA[
        INSERT INTO owner_user_change_item (owner_user_change_id, old_owner_user_id, new_owner_user_id, sku, allot_num, 
          stock_type, create_by, creation_date, last_updated_by, last_update_date)
        VALUES (:owner_user_change_id, :old_owner_user_id, :new_owner_user_id, :sku, :allot_num, 
          :stock_type, :create_by, :creation_date, :last_updated_by, :last_update_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteOwnerUserChangeItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM owner_user_change_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateOwnerUserChangeItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE owner_user_change_item
        SET <[owner_user_change_id = :owner_user_change_id,]>
          <[old_owner_user_id = :old_owner_user_id,]>
          <[new_owner_user_id = :new_owner_user_id,]>
          <[sku = :sku,]>
          <[allot_num = :allot_num,]>
          <[stock_type = :stock_type,]>
          <[create_by = :create_by,]>
          <[creation_date = :creation_date,]>
          <[last_updated_by = :last_updated_by,]>
          <[last_update_date = :last_update_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>