<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryAllocateReturnOrderCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM allocate_return_order
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:ids)]>
        <[AND order_no = :order_no]>
        <[AND order_no IN (:orderNoList)]>
        <[AND status = :status]>
        <[AND remark = :remark]>
        <[AND print_date = :print_date]>
        <[AND print_by = :print_by]>
        <[AND load_date = :load_date]>
        <[AND load_by = :load_by]>
        <[AND receive_date = :receive_date]>
        <[AND receive_by = :receive_by]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAllocateReturnOrderList" >
    <content >
      <![CDATA[
        SELECT id, order_no, status, remark, print_date, print_by, load_date, load_by, receive_date, 
        receive_by, creation_date, create_by, last_update_date, last_updated_by
        FROM allocate_return_order
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:ids)]>
        <[AND order_no = :order_no]>
        <[AND order_no IN (:orderNoList)]>
        <[AND status = :status]>
        <[AND remark = :remark]>
        <[AND print_date = :print_date]>
        <[AND print_by = :print_by]>
        <[AND load_date = :load_date]>
        <[AND load_by = :load_by]>
        <[AND receive_date = :receive_date]>
        <[AND receive_by = :receive_by]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAllocateReturnOrderByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, order_no, status, remark, print_date, print_by, load_date, load_by, receive_date, 
        receive_by, creation_date, create_by, last_update_date, last_updated_by
        FROM allocate_return_order
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAllocateReturnOrder" >
    <content >
      <![CDATA[
        SELECT id, order_no, status, remark, print_date, print_by, load_date, load_by, receive_date, 
        receive_by, creation_date, create_by, last_update_date, last_updated_by
        FROM allocate_return_order
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND order_no = :order_no]>
        <[AND status = :status]>
        <[AND remark = :remark]>
        <[AND print_date = :print_date]>
        <[AND print_by = :print_by]>
        <[AND load_date = :load_date]>
        <[AND load_by = :load_by]>
        <[AND receive_date = :receive_date]>
        <[AND receive_by = :receive_by]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createAllocateReturnOrder" >
    <content >
      <![CDATA[
        INSERT INTO allocate_return_order (order_no, status, remark, print_date, print_by, load_date, load_by, 
          receive_date, receive_by, creation_date, create_by, last_update_date, last_updated_by
          )
        VALUES (:order_no, :status, :remark, :print_date, :print_by, :load_date, :load_by, 
          :receive_date, :receive_by, :creation_date, :create_by, :last_update_date, :last_updated_by
          )
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteAllocateReturnOrderByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM allocate_return_order
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateAllocateReturnOrderByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE allocate_return_order
        SET <[order_no = :order_no,]>
          <[status = :status,]>
          <[remark = :remark,]>
          <[print_date = :print_date,]>
          <[print_by = :print_by,]>
          <[load_date = :load_date,]>
          <[load_by = :load_by,]>
          <[receive_date = :receive_date,]>
          <[receive_by = :receive_by,]>
          <[creation_date = :creation_date,]>
          <[create_by = :create_by,]>
          <[last_update_date = :last_update_date,]>
          <[last_updated_by = :last_updated_by,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryAllocateReturnOrderAndItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM allocate_return_order r
        WHERE 1 = 1
        <[AND r.id = :id]>
        <[AND r.order_no = :order_no]>
        <[AND r.id IN (:ids)]>
        <[AND r.order_no IN (:orderNoList)]>
        <[AND r.status = :status]>
        <[AND r.remark = :remark]>
        <[AND r.print_date = :print_date]>
        <[AND r.print_by = :print_by]>
        <[AND r.load_date = :load_date]>
        <[AND r.load_by = :load_by]>
        <[AND r.receive_date = :receive_date]>
        <[AND r.receive_by = :receive_by]>
        <[AND r.creation_date = :creation_date]>
        <[AND r.create_by = :create_by]>
        <[AND r.last_update_date = :last_update_date]>
        <[AND r.last_updated_by = :last_updated_by]>

        <[AND r.creation_date >= :fromCreateDate]>
        <[AND r.creation_date <= :toCreateDate]>
        <[AND r.print_date >= :fromPrintDate]>
        <[AND r.print_date <= :toPrintDate]>
        <[AND r.load_date >= :fromLoadDate]>
        <[AND r.load_date <= :toLoadDate]>
        <[AND r.receive_date >= :fromReceiveDate]>
        <[AND r.receive_date <= :toReceiveDate]>
        <[AND r.id IN (SELECT i.return_id FROM allocate_return_order_item i WHERE i.sku = :sku)]>
        <[AND r.id IN (SELECT i.return_id FROM allocate_return_order_item i WHERE i.sku IN (:skuList))]>

         <[AND r.order_no IN (SELECT i.return_order_no FROM wh_return_item i WHERE i.status = :returnStatus)]>
         <[AND r.order_no IN (SELECT i.return_order_no FROM wh_return_item i LEFT JOIN wh_return r ON r.id = i.return_id WHERE r.return_no = :returnNo)]>
         <[AND r.order_no IN (SELECT i.return_order_no FROM wh_return_item i LEFT JOIN wh_return r ON r.id = i.return_id WHERE r.return_no IN (:returnNoList))]>

        <[:MATE_DIFF]>
        <[:RETURN_DIFF]>
        <[:SCAN_DIFF]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryAllocateReturnOrderAndItemList" >
    <content >
      <![CDATA[
        SELECT r.id, r.order_no, r.status, r.remark, r.print_date, r.print_by, r.load_date, r.load_by, r.receive_date,
        r.receive_by, r.creation_date, r.create_by, r.last_update_date, r.last_updated_by

        ,i.id, i.sku, i.return_id, i.quantity, i.mate_quantity, i.scan_quantity, i.status, i.remark,
        i.creation_date, i.create_by, i.last_update_date, i.last_updated_by
        ,sku.id, sku.sku, sku.location_number, sku.name, sku.warehouse_id
        FROM allocate_return_order r
        LEFT JOIN allocate_return_order_item i ON r.id = i.return_id
        LEFT JOIN wh_sku sku ON sku.sku = i.sku
        INNER JOIN (
          SELECT r.id
          FROM allocate_return_order r
          WHERE 1 = 1
          <[AND r.id = :id]>
          <[AND r.order_no = :order_no]>
          <[AND r.id IN (:ids)]>
          <[AND r.order_no IN (:orderNoList)]>
          <[AND r.status = :status]>
          <[AND r.remark = :remark]>
          <[AND r.print_date = :print_date]>
          <[AND r.print_by = :print_by]>
          <[AND r.load_date = :load_date]>
          <[AND r.load_by = :load_by]>
          <[AND r.receive_date = :receive_date]>
          <[AND r.receive_by = :receive_by]>
          <[AND r.creation_date = :creation_date]>
          <[AND r.create_by = :create_by]>
          <[AND r.last_update_date = :last_update_date]>
          <[AND r.last_updated_by = :last_updated_by]>

          <[AND r.creation_date >= :fromCreateDate]>
          <[AND r.creation_date <= :toCreateDate]>
          <[AND r.print_date >= :fromPrintDate]>
          <[AND r.print_date <= :toPrintDate]>
          <[AND r.load_date >= :fromLoadDate]>
          <[AND r.load_date <= :toLoadDate]>
          <[AND r.receive_date >= :fromReceiveDate]>
          <[AND r.receive_date <= :toReceiveDate]>

          <[AND r.id IN (SELECT i.return_id FROM allocate_return_order_item i WHERE i.sku = :sku)]>
          <[AND r.id IN (SELECT i.return_id FROM allocate_return_order_item i WHERE i.sku IN (:skuList))]>

          <[AND r.order_no IN (SELECT i.return_order_no FROM wh_return_item i WHERE i.status = :returnStatus)]>
          <[AND r.order_no IN (SELECT i.return_order_no FROM wh_return_item i LEFT JOIN wh_return r ON r.id = i.return_id WHERE r.return_no = :returnNo)]>
          <[AND r.order_no IN (SELECT i.return_order_no FROM wh_return_item i LEFT JOIN wh_return r ON r.id = i.return_id WHERE r.return_no IN (:returnNoList))]>

          <[:MATE_DIFF]>
          <[:RETURN_DIFF]>
          <[:SCAN_DIFF]>

          <[:LIMIT]>
        ) rr ON rr.id = r.id
        WHERE 1 = 1
        ORDER BY r.id DESC
      ]]>
    </content>
  </sql>

</sqlmap>