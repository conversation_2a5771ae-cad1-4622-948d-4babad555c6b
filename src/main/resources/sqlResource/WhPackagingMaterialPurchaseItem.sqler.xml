<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhPackagingMaterialPurchaseItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_packaging_material_purchase_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND material_purchase_id = :material_purchase_id]>
        <[AND material_article_number = :material_article_number]>
        <[AND name = :name]>
        <[AND box_specification = :box_specification]>
        <[AND unit = :unit]>
        <[AND quantity = :quantity]>
        <[AND remark = :remark]>
      ]]>
    </content>
  </sql>


  <sql datasource="dataSource" id="queryWhPackagingMaterialPurchaseItemList" >
    <content >
      <![CDATA[
        SELECT id, material_purchase_id, material_article_number, name, box_specification,
        unit, quantity, remark, over_due_quantity
        FROM wh_packaging_material_purchase_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND material_purchase_id = :material_purchase_id]>
        <[AND material_article_number = :material_article_number]>
        <[AND name = :name]>
        <[AND box_specification = :box_specification]>
        <[AND unit = :unit]>
        <[AND quantity = :quantity]>
        <[AND remark = :remark]>
      ]]>
    </content>
  </sql>


  <sql datasource="dataSource" id="queryWhPackagingMaterialPurchaseItemDtoList" >
    <content >
      <![CDATA[
        SELECT i.id, (select m.purchase_order_no from material_check_in m where m.in_id=c.in_id ) AS 'c.purchase_order_no' ,c.purchase_quantity ,c.quantity,
        i.material_article_number, i.name,i.quantity,
        p.task_no,p.warehouse_verify_date
        FROM
        material_check_in_item c
        LEFT join wh_packaging_material_purchase p on c.material_purchase_id=p.id
	    LEFT JOIN  wh_packaging_material_purchase_item i ON i.material_purchase_id = p.id
        WHERE 1 = 1
        <[AND i.id = :id]>
        <[AND i.id in (:ids)]>
        <[AND i.material_purchase_id = :material_purchase_id]>
        <[AND i.material_article_number = :material_article_number]>
        <[AND i.name = :name]>
        <[AND i.box_specification = :box_specification]>
        <[AND i.unit = :unit]>
        <[AND i.quantity = :quantity]>
        <[AND i.remark = :remark]>
        <[AND i.material_article_number = :materialArticleNumber]>
        <[AND i.material_article_number in (:materialArticleNumberList)]>
        <[AND i.up_status = :up_status]>
        <[AND p.warehouse_verify_date <= :to_warehouse_verify_date]>
        <[AND p.task_no = :task_no]>
        <[AND p.task_no in (:taskNoList)]>
        AND c.article_number=i.material_article_number

      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryMaterialPurchaseItemDtoList" >
    <content >
      <![CDATA[
        SELECT i.id, i.material_article_number, i.name,i.quantity,
        p.task_no,p.warehouse_verify_date
        FROM
        wh_packaging_material_purchase_item i
        LEFT join wh_packaging_material_purchase p ON i.material_purchase_id = p.id
        WHERE 1 = 1
        <[AND i.id = :id]>
        <[AND i.id in (:itemIds)]>
        <[AND i.material_purchase_id = :material_purchase_id]>
        <[AND i.material_article_number = :material_article_number]>
        <[AND i.name = :name]>
        <[AND i.box_specification = :box_specification]>
        <[AND i.unit = :unit]>
        <[AND i.quantity = :quantity]>
        <[AND i.remark = :remark]>
        <[AND i.material_article_number = :materialArticleNumber]>
        <[AND i.material_article_number in (:materialArticleNumberList)]>
        <[AND i.up_status = :up_status]>
        <[AND p.warehouse_verify_date <= :to_warehouse_verify_date]>
        <[AND p.task_no = :task_no]>
        <[AND p.task_no in (:taskNoList)]>
      ]]>
    </content>
  </sql>


  
  <sql datasource="dataSource" id="queryWhPackagingMaterialPurchaseItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, material_purchase_id, material_article_number, name, box_specification, 
        unit, quantity, remark, over_due_quantity
        FROM wh_packaging_material_purchase_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhPackagingMaterialPurchaseItem" >
    <content >
      <![CDATA[
        SELECT id, material_purchase_id, material_article_number, name, box_specification, 
        unit, quantity, remark, over_due_quantity
        FROM wh_packaging_material_purchase_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND material_purchase_id = :material_purchase_id]>
        <[AND material_article_number = :material_article_number]>
        <[AND name = :name]>
        <[AND box_specification = :box_specification]>
        <[AND unit = :unit]>
        <[AND quantity = :quantity]>
        <[AND remark = :remark]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhPackagingMaterialPurchaseItem" >
    <content >
      <![CDATA[
        INSERT INTO wh_packaging_material_purchase_item (material_purchase_id, material_article_number, name, box_specification, 
          unit, quantity, remark, over_due_quantity, up_status)
        VALUES (:material_purchase_id, :material_article_number, :name, :box_specification, 
          :unit, :quantity, :remark, :over_due_quantity, :up_status)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhPackagingMaterialPurchaseItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_packaging_material_purchase_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhPackagingMaterialPurchaseItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_packaging_material_purchase_item
        SET <[material_purchase_id = :material_purchase_id,]>
          <[material_article_number = :material_article_number,]>
          <[name = :name,]>
          <[box_specification = :box_specification,]>
          <[unit = :unit,]>
          <[quantity = :quantity,]>
          <[remark = :remark,]>
          <[over_due_quantity = :over_due_quantity,]>
          <[up_status = :up_status,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>