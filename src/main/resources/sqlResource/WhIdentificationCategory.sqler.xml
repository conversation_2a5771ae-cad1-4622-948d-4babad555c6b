<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhIdentificationCategoryCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_identification_category
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND code = :code]>
        <[AND name = :name]>
        <[AND update_by = :update_by]>
        <[AND update_time = :update_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhIdentificationCategoryList" >
    <content >
      <![CDATA[
        SELECT id, code, name, update_by, update_time
        FROM wh_identification_category
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND code = :code]>
        <[AND name = :name]>
        <[AND update_by = :update_by]>
        <[AND update_time = :update_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhIdentificationCategoryByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, code, name, update_by, update_time
        FROM wh_identification_category
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhIdentificationCategory" >
    <content >
      <![CDATA[
        SELECT id, code, name, update_by, update_time
        FROM wh_identification_category
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND code = :code]>
        <[AND name = :name]>
        <[AND update_by = :update_by]>
        <[AND update_time = :update_time]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryMaxCode" >
    <content >
      <![CDATA[
        SELECT  code
        FROM wh_identification_category
        WHERE 1 = 1
        ORDER BY code DESC LIMIT 1
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhIdentificationCategory" >
    <content >
      <![CDATA[
        INSERT INTO wh_identification_category (code, name, update_by, update_time)
        VALUES (:code, :name, :update_by, :update_time)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhIdentificationCategoryByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_identification_category
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhIdentificationCategoryByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_identification_category
        SET <[code = :code,]>
          <[name = :name,]>
          <[update_by = :update_by,]>
          <[update_time = :update_time,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>