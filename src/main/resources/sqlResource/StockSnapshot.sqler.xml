<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryStockSnapshotCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM end_stock_local
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND store_code = :store_code]>
        <[AND stock_id = :stock_id]>
        <[AND quantity = :quantity]>
        <[AND count_date = :count_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryStockSnapshotList" >
    <content >
      <![CDATA[
        SELECT id, sku, store_code, stock_id, quantity, count_date
        FROM end_stock_local
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND store_code = :store_code]>
        <[AND stock_id = :stock_id]>
        <[AND quantity = :quantity]>
        <[AND count_date = :count_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryStockSnapshotByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, sku, store_code, stock_id, quantity, count_date
        FROM end_stock_local
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryStockSnapshot" >
    <content >
      <![CDATA[
        SELECT id, sku, store_code, stock_id, quantity, count_date
        FROM end_stock_local
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND store_code = :store_code]>
        <[AND stock_id = :stock_id]>
        <[AND quantity = :quantity]>
        <[AND count_date = :count_date]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="createStockSnapshot" >
    <content >
      <![CDATA[
        INSERT INTO end_stock_local (sku, store_code, stock_id, quantity, count_date)
        VALUES (:sku, :store_code, :stock_id, :quantity, :count_date)
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="createTransferStockSnapshot" >
    <content >
      <![CDATA[
        INSERT INTO end_stock_transfer (sku, store_code, stock_id, quantity, count_date)
        VALUES (:sku, :store_code, :stock_id, :quantity, :count_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteStockSnapshotByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM end_stock_local
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="deleteByCountDate" >
    <content >
      <![CDATA[
        DELETE FROM <[:DELETE_FROM]>
        WHERE 1 = 1
        AND count_date <= :count_date
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateStockSnapshotByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE end_stock_local
        SET <[sku = :sku,]>
          <[store_code = :store_code,]>
          <[stock_id = :stock_id,]>
          <[quantity = :quantity,]>
          <[count_date = :count_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryLocalStockSnapshot" >
    <content >
      <![CDATA[
        SELECT
            NULL AS id,
            T1.sku,
            T1.id AS stock_id,
            NULL AS store_code,
            DATE_SUB( CURDATE(), INTERVAL 1 DAY ) AS count_date,
            (
              IFNULL(T1.surplus_quantity,0)
              + IFNULL(T1.allot_quantity,0)
              + IFNULL(T1.pick_quantity,0)
              + IFNULL(T1.allocation_quantity,0)
              + IFNULL(T1.order_allocation_quantity,0)
              + IFNULL(T1.pick_return_quantity,0)
              + IFNULL(T1.pick_not_quantity,0)
              + IFNULL(T1.cancel_quantity,0)
              + IFNULL( f.frozen_quantity, 0 )
            ) AS quantity
        FROM
            wh_stock T1
            LEFT JOIN frozen_stock f ON f.stock_id = T1.id
        HAVING
            quantity >0
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="queryTransferStockSnapshot" >
    <content >
      <![CDATA[
        SELECT
            NULL AS id,
            sku,
            id AS stock_id,
            store AS store_code,
            DATE_SUB( CURDATE(), INTERVAL 1 DAY ) AS count_date,
            (
              IFNULL( surplus_quantity, 0 )
              + IFNULL( allot_quantity, 0 )
              + IFNULL( pick_quantity, 0 )
              + IFNULL( pick_return_quantity, 0 )
            ) AS quantity
        FROM
            wh_transfer_stock
        HAVING
            quantity >0
      ]]>
    </content>
  </sql>
</sqlmap>