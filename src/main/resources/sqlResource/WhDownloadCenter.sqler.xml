<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhDownloadCenterCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_download_center
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND content = :content]>
        <[AND type = :type]>
        <[AND status = :status]>
        <[AND created_by = :created_by]>
        <[AND url = :url]>
        <[AND created_date = :created_date]>
        <[AND modified_date = :modified_date]>
        <[AND remark = :remark]>
        <[AND created_date >= :from_create_date]>
        <[AND created_date <= :to_create_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhDownloadCenterList" >
    <content >
      <![CDATA[
        SELECT id, content, type, status, created_by, url, created_date, modified_date, remark
        FROM wh_download_center
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND content = :content]>
        <[AND type = :type]>
        <[AND status = :status]>
        <[AND created_by = :created_by]>
        <[AND url = :url]>
        <[AND created_date = :created_date]>
        <[AND modified_date = :modified_date]>
        <[AND remark = :remark]>
        <[AND created_date >= :from_create_date]>
        <[AND created_date <= :to_create_date]>
        ORDER BY id DESC
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhDownloadCenterByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, content, type, status, created_by, url, created_date, modified_date, remark
        FROM wh_download_center
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhDownloadCenter" >
    <content >
      <![CDATA[
        SELECT id, content, type, status, created_by, url, created_date, modified_date, remark
        FROM wh_download_center
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND content = :content]>
        <[AND type = :type]>
        <[AND status = :status]>
        <[AND created_by = :created_by]>
        <[AND url = :url]>
        <[AND created_date = :created_date]>
        <[AND modified_date = :modified_date]>
        <[AND remark = :remark]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhDownloadCenter" >
    <content >
      <![CDATA[
        INSERT INTO wh_download_center (content, type, status, created_by, url, created_date, modified_date, 
          remark)
        VALUES (:content, :type, :status, :created_by, :url, :created_date, :modified_date, 
          :remark)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhDownloadCenterByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_download_center
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhDownloadCenterByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_download_center
        SET <[content = :content,]>
          <[type = :type,]>
          <[status = :status,]>
          <[created_by = :created_by,]>
          <[url = :url,]>
          <[created_date = :created_date,]>
          <[modified_date = :modified_date,]>
          <[remark = :remark,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>