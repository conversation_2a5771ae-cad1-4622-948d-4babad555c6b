<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhUniqueSkuCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_unique_sku t1
        left join unique_sku_exp_relation re on re.sku =t1.sku and re.uuid = t1.uuid

        <[ :skuLogSQL]>

        <[ :apvNo]>
        <[ :apvNoList]>

        <[ :trackingNo]>
        <[ :trackingNoList]>

        <[ :dd]>
        <[ :ddList]>
        WHERE 1 = 1
        <[AND t1.id = :id]>
        <[AND t1.relation_id = :relation_id]>
        <[AND t1.type = :type]>
        <[AND t1.uuid = :uuid]>
        <[AND t1.sku = :sku]>
        <[AND t1.all_check_scan_status = :all_check_scan_status]>
        <[AND t1.created_by = :created_by]>
        <[AND t1.creation_date = :creation_date]>
        <[AND t1.creation_date >= :START_CREATION_DATE]>
        <[AND t1.creation_date <= :END_CREATION_DATE]>
        <[AND t1.source_from = :source_from]>
        <[AND t1.step = :step]>
        <[AND apv_no = :apv_no]>
        <[AND re.exp_no IN (:expNoList)]>
        <[AND re.exp_no = :exp_no]>
        <[AND skuLog.content like :remarks]>
        <[ :uuidListSQL]>
        <[ :OPERATOR_JSON_SQL]>
        <[AND t1.id IN (:ids)]>
        <[AND t1.sku IN (:skuList)]>
        <[AND FIND_IN_SET(:tag, t1.tag)]>

        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_5 WHERE order_no = :returnNo)]>
        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_5 WHERE order_no IN (:returnNoList))]>

        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_7 WHERE order_no = :overSeaReturnNo)]>
        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_7 WHERE order_no IN (:overSeaReturnNoList))]>

        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_6 WHERE order_no = :transferReturnNo)]>
        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_6 WHERE order_no IN (:transferReturnNoList))]>

        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_13 WHERE order_no = :returnFormNo)]>
        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_13 WHERE order_no IN (:returnFormNoList))]>

        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_13 WHERE order_no = :rfoInId)]>
        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_13 WHERE order_no IN (:rfoInIdList))]>

        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_13 WHERE order_no = :lendNo)]>
        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_13 WHERE order_no IN (:lendNoList))]>

        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_13 WHERE order_no = :fbaNo)]>
        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_13 WHERE order_no IN (:fbaNoList))]>

        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_13 WHERE order_no = :spoNo)]>
        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_13 WHERE order_no IN (:spoNoList))]>
        <[ :stepParam]>
        <[ :startOperateDate]>
        <[ :endOperateDate]>
        <[ :QUERY_APV_NO]>

      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhUniqueSkuList" >
    <content >
      <![CDATA[
        SELECT t1.id, t1.relation_id, t1.type, t1.uuid, t1.sku, t1.created_by, t1.creation_date, t1.all_check_scan_status, t1.source_from, t1.step, apv_no,t1.tag
        , re.exp_no, t1.operator_json
        FROM wh_unique_sku t1
        left join unique_sku_exp_relation re on re.sku =t1.sku and re.uuid = t1.uuid

        <[ :skuLogSQL]>

        <[ :apvNo]>
        <[ :apvNoList]>

        <[ :trackingNo]>
        <[ :trackingNoList]>

        <[ :dd]>
        <[ :ddList]>

        WHERE 1 = 1
        <[AND t1.id = :id]>
        <[AND t1.relation_id = :relation_id]>
        <[AND t1.type = :type]>
        <[AND t1.uuid = :uuid]>
        <[AND t1.all_check_scan_status = :all_check_scan_status]>
        <[AND t1.sku = :sku]>
        <[AND t1.created_by = :created_by]>
        <[AND t1.creation_date = :creation_date]>
        <[AND t1.creation_date >= :START_CREATION_DATE]>
        <[AND t1.creation_date <= :END_CREATION_DATE]>
        <[AND t1.source_from = :source_from]>
        <[AND t1.step = :step]>
        <[AND apv_no = :apv_no]>
        <[AND re.exp_no IN (:expNoList)]>
        <[AND re.exp_no = :exp_no]>
        <[AND skuLog.content like :remarks]>

        <[ :uuidListSQL]>
        <[ :OPERATOR_JSON_SQL]>

        <[AND t1.id IN (:ids)]>
        <[AND t1.sku IN (:skuList)]>
        <[AND FIND_IN_SET(:tag, t1.tag)]>

        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_5 WHERE order_no = :returnNo)]>
        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_5 WHERE order_no IN (:returnNoList))]>

        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_7 WHERE order_no = :overSeaReturnNo)]>
        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_7 WHERE order_no IN (:overSeaReturnNoList))]>

        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_6 WHERE order_no = :transferReturnNo)]>
        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_6 WHERE order_no IN (:transferReturnNoList))]>

        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_13 WHERE order_no = :returnFormNo)]>
        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_13 WHERE order_no IN (:returnFormNoList))]>

        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_13 WHERE order_no = :rfoInId)]>
        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_13 WHERE order_no IN (:rfoInIdList))]>
        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_13 WHERE order_no = :lendNo)]>
        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_13 WHERE order_no IN (:lendNoList))]>
        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_13 WHERE order_no = :fbaNo)]>
        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_13 WHERE order_no IN (:fbaNoList))]>
        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_13 WHERE order_no = :spoNo)]>
        <[AND t1.id IN (SELECT unique_id from wh_unique_sku_log_13 WHERE order_no IN (:spoNoList))]>
        <[ :stepParam]>
        <[ :startOperateDate]>
        <[ :endOperateDate]>
        <[ :QUERY_APV_NO]>

        ORDER BY id DESC
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhUniqueSkuByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, relation_id, type, uuid, sku, created_by, creation_date, all_check_scan_status, source_from, step, apv_no, tag, operator_json
        FROM wh_unique_sku
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhUniqueSku" >
    <content >
      <![CDATA[
        SELECT id, relation_id, type, uuid, sku, created_by, creation_date, all_check_scan_status, source_from, step, apv_no, tag, operator_json
        FROM wh_unique_sku
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND relation_id = :relation_id]>
        <[AND type = :type]>
        <[AND uuid = :uuid]>
        <[AND sku = :sku]>
        <[AND all_check_scan_status = :all_check_scan_status]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND source_from = :source_from]>
        <[AND step = :step]>
        <[AND apv_no = :apv_no]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhUniqueSku" >
    <content >
      <![CDATA[
        INSERT INTO wh_unique_sku (relation_id, type, uuid, sku, created_by, creation_date, all_check_scan_status, source_from, step, apv_no, tag, operator_json)
        VALUES (:relation_id, :type, :uuid, :sku, :created_by, :creation_date, :all_check_scan_status, :source_from, :step, :apv_no,:tag, :operator_json)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhUniqueSkuByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_unique_sku
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhUniqueSkuByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_unique_sku
        SET <[relation_id = :relation_id,]>
          <[type = :type,]>
          <[uuid = :uuid,]>
          <[sku = :sku,]>
          <[created_by = :created_by,]>
          <[all_check_scan_status = :all_check_scan_status,]>
          <[creation_date = :creation_date,]>
          <[source_from = :source_from,]>
          <[step = :step,]>
          <[apv_no = :apv_no,]>
          <[apv_no = NULL, id = :updateApvNoNull,]>
          <[tag = :tag,]>
          <[operator_json = :operator_json,]>

        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhUniqueSkuBindingApvCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_unique_sku sku
        LEFT JOIN wh_apv apv ON apv.apv_no=sku.apv_no
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND uuid = :uuid]>
        <[AND step = :step]>
        <[AND sku.apv_no = :apv_no]>

        <[AND sku.apv_no in( :apvNoList)]>
        <[ :uuidListSQL]>
        <[AND id IN (:ids)]>
        <[AND sku IN (:skuList)]>

        <[AND apv.status = :apvStatus]>
        <[AND apv.status IN (:apvStatusList)]>
        <[AND apv.apv_type = :apvType]>
        <[AND apv.apv_type IN (:apvTypeList)]>

        AND sku.apv_no is not null

      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhUniqueSkuBindingApvList" >
    <content >
      <![CDATA[
        SELECT sku.id, uuid, sku, source_from, step, sku.apv_no, apv.status, apv.apv_type, sku.tag
        FROM wh_unique_sku sku
        LEFT JOIN wh_apv apv ON apv.apv_no=sku.apv_no
        WHERE 1 = 1
        <[AND sku.id = :id]>
        <[AND sku = :sku]>
        <[AND uuid = :uuid]>
        <[AND step = :step]>
        <[AND sku.apv_no = :apv_no]>

        <[AND sku.apv_no in( :apvNoList)]>
        <[ :uuidListSQL]>
        <[AND sku.id IN (:ids)]>
        <[AND sku IN (:skuList)]>

        <[AND apv.status = :apvStatus]>
        <[AND apv.status IN (:apvStatusList)]>
        <[AND apv.apv_type = :apvType]>
        <[AND apv.apv_type IN (:apvTypeList)]>

        AND sku.apv_no is not null
        ORDER BY sku.id DESC
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="queryHistoryScanPrintUniqueSkuList" >
    <content>
      <![CDATA[
        SELECT sku.id, sku.relation_id, sku.all_check_scan_status, sku.type,sku.tag
        FROM wh_unique_sku sku
        WHERE sku.relation_id != 0
        AND sku.id IN(
          SELECT log.unique_id
          FROM <[ :TABLE_INDEX]>
          WHERE 1 = 1
          AND log.unique_id = sku.id
          <[ :LOCATE_FILTER]>
        )
        <[AND sku.step = :step]>
        <[AND sku.source_from IN (:source_from)]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryMaxUuidFromUniqueSkuBySku" >
    <content>
      <![CDATA[
        SELECT id, relation_id, type, uuid, sku, created_by, creation_date, all_check_scan_status, source_from, step, apv_no, tag, operator_json
        FROM wh_unique_sku
        WHERE 1 = 1
        <[AND sku = :sku]>
        <[AND creation_date >= :startDate]>
        ORDER BY uuid DESC LIMIT 1
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="getWhUniqueSkuByUuidList" >
    <content>
      <![CDATA[
        SELECT apv_no
        FROM wh_unique_sku
        WHERE 1 = 1
        <[:uuidSkuList]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="deleteWhUniqueSkus" >
    <content >
      <![CDATA[
        DELETE FROM wh_unique_sku
        WHERE 1 = 1
        AND id IN <[:Ids]>
      ]]>
    </content>
  </sql>
</sqlmap>