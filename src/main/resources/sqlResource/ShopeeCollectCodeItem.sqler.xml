<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryShopeeCollectCodeItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM shopee_collect_code_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND shopee_collect_code_id = :shopee_collect_code_id]>
        <[AND deliver_no like :deliver_no]>
        <[AND order_no = :order_no]>
        <[AND binding_result = :binding_result]>
        <[AND syn_time = :syn_time]>

        <[AND deliver_no in (:deliverNoList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryShopeeCollectCodeItemList" >
    <content >
      <![CDATA[
        SELECT id, shopee_collect_code_id, deliver_no, order_no, binding_result, syn_time
        FROM shopee_collect_code_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND shopee_collect_code_id = :shopee_collect_code_id]>
        <[AND deliver_no like :deliver_no]>
        <[AND order_no = :order_no]>
        <[AND binding_result = :binding_result]>
        <[AND syn_time = :syn_time]>

        <[AND deliver_no in (:deliverNoList)]>
        <[AND shopee_collect_code_id in (:shopeeCollectIds)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryShopeeCollectCodeItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, shopee_collect_code_id, deliver_no, order_no, binding_result, syn_time
        FROM shopee_collect_code_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryShopeeCollectCodeItem" >
    <content >
      <![CDATA[
        SELECT id, shopee_collect_code_id, deliver_no, order_no, binding_result, syn_time
        FROM shopee_collect_code_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND shopee_collect_code_id = :shopee_collect_code_id]>
        <[AND deliver_no = :deliver_no]>
        <[AND order_no = :order_no]>
        <[AND binding_result = :binding_result]>
        <[AND syn_time = :syn_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createShopeeCollectCodeItem" >
    <content >
      <![CDATA[
        INSERT INTO shopee_collect_code_item (shopee_collect_code_id, deliver_no, order_no, binding_result, syn_time
          )
        VALUES (:shopee_collect_code_id, :deliver_no, :order_no, :binding_result, :syn_time
          )
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteShopeeCollectCodeItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM shopee_collect_code_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateShopeeCollectCodeItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE shopee_collect_code_item
        SET <[shopee_collect_code_id = :shopee_collect_code_id,]>
          <[deliver_no = :deliver_no,]>
          <[order_no = :order_no,]>
          <[binding_result = :binding_result,]>
          <[syn_time = :syn_time,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>