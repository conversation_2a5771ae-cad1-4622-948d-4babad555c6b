<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhInvoicingChangeCountCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM <[:COME_FROM]>
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:idList)]>
        <[AND sku = :sku]>
        <[AND sku IN (:skuList)]>
        <[AND accountNumber = :accountNumber]>
        <[AND accountNumber IN (:berList)]>
        <[AND stock_date >= :from_stock_date]>
        <[AND stock_date <= :to_stock_date]>
        <[AND quantity = :quantity]>
        <[AND ending_purchase_price = :ending_purchase_price]>
        <[AND ending_purchase_cost = :ending_purchase_cost]>
        <[AND ending_first_trip_cost = :ending_first_trip_cost]>
        <[AND ending_first_trip_tax = :ending_first_trip_tax]>
        <[AND ending_refund_tax = :ending_refund_tax]>
        <[AND ending_allot_cost = :ending_allot_cost]>
        <[AND ending_amount = :ending_amount]>
        <[AND plant_report_quantity = :plant_report_quantity]>
        <[AND quantity_diff = :quantity_diff]>
        <[AND count_date = :count_date]>
        <[:FILTER_BY_TYPE]>
        <[:QUERY_FOR_FMIS]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhInvoicingChangeCountList" >
    <content >
      <![CDATA[
        SELECT id, sku, accountNumber, stock_date, quantity, ending_purchase_price, ending_purchase_cost, 
        ending_first_trip_cost, ending_first_trip_tax, ending_refund_tax, ending_allot_cost, 
        ending_amount, plant_report_quantity, quantity_diff, count_date, merchant_id
        FROM <[:COME_FROM]>
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:idList)]>
        <[AND sku = :sku]>
        <[AND sku IN (:skuList)]>
        <[AND accountNumber = :accountNumber]>
        <[AND accountNumber IN (:berList)]>
        <[AND stock_date >= :from_stock_date]>
        <[AND stock_date <= :to_stock_date]>
        <[AND quantity = :quantity]>
        <[AND ending_purchase_price = :ending_purchase_price]>
        <[AND ending_purchase_cost = :ending_purchase_cost]>
        <[AND ending_first_trip_cost = :ending_first_trip_cost]>
        <[AND ending_first_trip_tax = :ending_first_trip_tax]>
        <[AND ending_refund_tax = :ending_refund_tax]>
        <[AND ending_allot_cost = :ending_allot_cost]>
        <[AND ending_amount = :ending_amount]>
        <[AND plant_report_quantity = :plant_report_quantity]>
        <[AND quantity_diff = :quantity_diff]>
        <[AND count_date = :count_date]>
        <[:FILTER_BY_TYPE]>
        <[:QUERY_FOR_FMIS]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhInvoicingChangeCountByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, sku, accountNumber, stock_date, quantity, ending_purchase_price, ending_purchase_cost, 
        ending_first_trip_cost, ending_first_trip_tax, ending_refund_tax, ending_allot_cost, 
        ending_amount, plant_report_quantity, quantity_diff, count_date, merchant_id
        FROM wh_invoicing_change_count
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhInvoicingChangeCount" >
    <content >
      <![CDATA[
        SELECT id, sku, accountNumber, stock_date, quantity, ending_purchase_price, ending_purchase_cost, 
        ending_first_trip_cost, ending_first_trip_tax, ending_refund_tax, ending_allot_cost, 
        ending_amount, plant_report_quantity, quantity_diff, count_date, merchant_id
        FROM wh_invoicing_change_count
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND accountNumber = :accountNumber]>
        <[AND stock_date = :stock_date]>
        <[AND quantity = :quantity]>
        <[AND ending_purchase_price = :ending_purchase_price]>
        <[AND ending_purchase_cost = :ending_purchase_cost]>
        <[AND ending_first_trip_cost = :ending_first_trip_cost]>
        <[AND ending_first_trip_tax = :ending_first_trip_tax]>
        <[AND ending_refund_tax = :ending_refund_tax]>
        <[AND ending_allot_cost = :ending_allot_cost]>
        <[AND ending_amount = :ending_amount]>
        <[AND plant_report_quantity = :plant_report_quantity]>
        <[AND quantity_diff = :quantity_diff]>
        <[AND count_date = :count_date]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhInvoicingChangeSum" >
    <content >
      <![CDATA[
        SELECT
            SUM(quantity) as totalEndingInventoryQuantity,
            SUM(ending_amount) as totalEndingInventoryAmount,
            null as totalPlantReport,
            null as totalQuantityDiff
        FROM <[:COME_FROM]>
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:idList)]>
        <[AND sku = :sku]>
        <[AND sku IN (:skuList)]>
        <[AND accountNumber = :accountNumber]>
        <[AND accountNumber IN (:berList)]>
        <[AND stock_date >= :from_stock_date]>
        <[AND stock_date <= :to_stock_date]>
        <[AND quantity = :quantity]>
        <[AND ending_purchase_price = :ending_purchase_price]>
        <[AND ending_purchase_cost = :ending_purchase_cost]>
        <[AND ending_first_trip_cost = :ending_first_trip_cost]>
        <[AND ending_first_trip_tax = :ending_first_trip_tax]>
        <[AND ending_refund_tax = :ending_refund_tax]>
        <[AND ending_allot_cost = :ending_allot_cost]>
        <[AND ending_amount = :ending_amount]>
        <[AND plant_report_quantity = :plant_report_quantity]>
        <[AND quantity_diff = :quantity_diff]>
        <[AND count_date = :count_date]>
        <[:FILTER_BY_TYPE]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhInvoicingChangeCount" >
    <content >
      <![CDATA[
        INSERT INTO wh_invoicing_change_count (sku, accountNumber, stock_date, quantity, ending_purchase_price, ending_purchase_cost, 
          ending_first_trip_cost, ending_first_trip_tax, ending_refund_tax, ending_allot_cost, merchant_id,
          ending_amount, plant_report_quantity, quantity_diff, count_date)
        VALUES (:sku, :accountNumber, :stock_date, :quantity, :ending_purchase_price, :ending_purchase_cost, 
          :ending_first_trip_cost, :ending_first_trip_tax, :ending_refund_tax, :ending_allot_cost, :merchant_id,
          :ending_amount, :plant_report_quantity, :quantity_diff, :count_date)
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="createWhInvoicingChangeTransferCount" >
    <content >
      <![CDATA[
        INSERT INTO wh_invoicing_change_count_transfer (sku, accountNumber, stock_date, quantity, ending_purchase_price, ending_purchase_cost,
          ending_first_trip_cost, ending_first_trip_tax, ending_refund_tax, ending_allot_cost, merchant_id,
          ending_amount, plant_report_quantity, quantity_diff, count_date)
        VALUES (:sku, :accountNumber, :stock_date, :quantity, :ending_purchase_price, :ending_purchase_cost,
          :ending_first_trip_cost, :ending_first_trip_tax, :ending_refund_tax, :ending_allot_cost, :merchant_id,
          :ending_amount, :plant_report_quantity, :quantity_diff, :count_date)
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="createWhInvoicingChangeLocalCount" >
    <content >
      <![CDATA[
        INSERT INTO wh_invoicing_change_count_local (sku, accountNumber, stock_date, quantity, ending_purchase_price, ending_purchase_cost,
          ending_first_trip_cost, ending_first_trip_tax, ending_refund_tax, ending_allot_cost, merchant_id,
          ending_amount, plant_report_quantity, quantity_diff, count_date)
        VALUES (:sku, :accountNumber, :stock_date, :quantity, :ending_purchase_price, :ending_purchase_cost,
          :ending_first_trip_cost, :ending_first_trip_tax, :ending_refund_tax, :ending_allot_cost, :merchant_id,
          :ending_amount, :plant_report_quantity, :quantity_diff, :count_date)
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="createWhInvoicingChangeMonthTransferCount" >
    <content >
      <![CDATA[
        INSERT INTO wh_invoicing_change_month_count_transfer (sku, accountNumber, stock_date, quantity, ending_purchase_price, ending_purchase_cost,
          ending_first_trip_cost, ending_first_trip_tax, ending_refund_tax, ending_allot_cost, merchant_id,
          ending_amount, plant_report_quantity, quantity_diff, count_date)
        VALUES (:sku, :accountNumber, :stock_date, :quantity, :ending_purchase_price, :ending_purchase_cost,
          :ending_first_trip_cost, :ending_first_trip_tax, :ending_refund_tax, :ending_allot_cost, :merchant_id,
          :ending_amount, :plant_report_quantity, :quantity_diff, :count_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhInvoicingChangeCountByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_invoicing_change_count
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="deleteWhInvoicingChangeCountByQuery" >
    <content >
      <![CDATA[
        DELETE FROM <[:DELETE_FROM]>
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:idList)]>
        <[AND sku = :sku]>
        <[AND sku IN (:skuList)]>
        <[AND accountNumber = :accountNumber]>
        <[AND accountNumber IN (:berList)]>
        <[AND stock_date >= :from_stock_date]>
        <[AND stock_date <= :to_stock_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhInvoicingChangeCountByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_invoicing_change_count
        SET <[sku = :sku,]>
          <[accountNumber = :accountNumber,]>
          <[stock_date = :stock_date,]>
          <[quantity = :quantity,]>
          <[ending_purchase_price = :ending_purchase_price,]>
          <[ending_purchase_cost = :ending_purchase_cost,]>
          <[ending_first_trip_cost = :ending_first_trip_cost,]>
          <[ending_first_trip_tax = :ending_first_trip_tax,]>
          <[ending_refund_tax = :ending_refund_tax,]>
          <[ending_allot_cost = :ending_allot_cost,]>
          <[ending_amount = :ending_amount,]>
          <[plant_report_quantity = :plant_report_quantity,]>
          <[quantity_diff = :quantity_diff,]>
          <[count_date = :count_date,]>
          <[merchant_id = :merchant_id,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateWhInvoicingChangeCountMonthLocal" >
    <content >
      <![CDATA[
        UPDATE wh_invoicing_change_month_count_local
        SET <[sku = :sku,]>
          <[accountNumber = :accountNumber,]>
          <[stock_date = :stock_date,]>
          <[quantity = :quantity,]>
          <[ending_purchase_price = :ending_purchase_price,]>
          <[ending_purchase_cost = :ending_purchase_cost,]>
          <[ending_first_trip_cost = :ending_first_trip_cost,]>
          <[ending_first_trip_tax = :ending_first_trip_tax,]>
          <[ending_refund_tax = :ending_refund_tax,]>
          <[ending_allot_cost = :ending_allot_cost,]>
          <[ending_amount = :ending_amount,]>
          <[plant_report_quantity = :plant_report_quantity,]>
          <[quantity_diff = :quantity_diff,]>
          <[count_date = :count_date,]>
          <[merchant_id = :merchant_id,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryEndItemList" >
    <content >
      <![CDATA[
        SELECT
          t1.id AS drpId,
          t1.sku,
          t1.merchant_id,
          t1.store_code,
          t1.end_stock,
          t1.end_purchase_cost_price,
          t1.end_purchase_freight_price,
          t1.end_product_freight_price,
          t1.end_product_tax_price,
          t1.tax_reimbursement_price,
          t1.allot_freight_price
        FROM <[:COME_FROM]> t1
        JOIN  (
            SELECT sku, store_code, MAX(report_time) AS max_report_time
            FROM <[:COME_FROM]>
            where report_time <= :end_create_time
            <[and sku IN (:skuList)]>
            group by sku,store_code
        )t2 ON t1.sku = t2.sku AND t1.store_code = t2.store_code AND t1.report_time = t2.max_report_time
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="queryEndItemListByIds" >
    <content >
      <![CDATA[
        SELECT
          id AS drpId,
          sku,
          merchant_id,
          store_code,
          end_stock,
          end_purchase_cost_price,
          end_purchase_freight_price,
          end_product_freight_price,
          end_product_tax_price,
          tax_reimbursement_price,
          allot_freight_price
        FROM <[:COME_FROM]>
        WHERE 1=1
        <[AND id IN (:idList)]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="createWhInvoicingChangeMonthCount" >
    <content >
      <![CDATA[
        INSERT INTO wh_invoicing_change_month_count (sku, accountNumber, stock_date, quantity, ending_purchase_price, ending_purchase_cost,
          ending_first_trip_cost, ending_first_trip_tax, ending_refund_tax, ending_allot_cost, merchant_id,
          ending_amount, plant_report_quantity, quantity_diff, count_date)
        VALUES (:sku, :accountNumber, :stock_date, :quantity, :ending_purchase_price, :ending_purchase_cost,
          :ending_first_trip_cost, :ending_first_trip_tax, :ending_refund_tax, :ending_allot_cost, :merchant_id,
          :ending_amount, :plant_report_quantity, :quantity_diff, :count_date)
      ]]>
    </content>
  </sql>

    <sql datasource="dataSource" id="createWhInvoicingChangeMonthCountLocal" >
        <content >
            <![CDATA[
                INSERT INTO wh_invoicing_change_month_count_local (sku, accountNumber, stock_date, quantity, ending_purchase_price, ending_purchase_cost,
                  ending_first_trip_cost, ending_first_trip_tax, ending_refund_tax, ending_allot_cost, merchant_id,
                  ending_amount, plant_report_quantity, quantity_diff, count_date,ending_price,ending_total_amount,sales_property,discount,promotion_label)
                VALUES (:sku, :accountNumber, :stock_date, :quantity, :ending_purchase_price, :ending_purchase_cost,
                  :ending_first_trip_cost, :ending_first_trip_tax, :ending_refund_tax, :ending_allot_cost, :merchant_id,
                  :ending_amount, :plant_report_quantity, :quantity_diff, :count_date,
                  :ending_price, :ending_total_amount, :sales_property, :discount, :promotion_label)
            ]]>
        </content>
    </sql>

  <sql datasource="dataSource" id="queryWhInvoicingChangeMonthSum" >
    <content >
      <![CDATA[
        SELECT
            SUM(quantity) as totalEndingInventoryQuantity,
            SUM(ending_amount) as totalEndingInventoryAmount,
            SUM(plant_report_quantity) as totalPlantReport,
            SUM(quantity_diff) as totalQuantityDiff
        FROM <[:COME_FROM]>
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:idList)]>
        <[AND sku = :sku]>
        <[AND sku IN (:skuList)]>
        <[AND accountNumber = :accountNumber]>
        <[AND accountNumber IN (:berList)]>
        <[AND stock_date >= :from_stock_date]>
        <[AND stock_date <= :to_stock_date]>
        <[AND quantity = :quantity]>
        <[AND ending_purchase_price = :ending_purchase_price]>
        <[AND ending_purchase_cost = :ending_purchase_cost]>
        <[AND ending_first_trip_cost = :ending_first_trip_cost]>
        <[AND ending_first_trip_tax = :ending_first_trip_tax]>
        <[AND ending_refund_tax = :ending_refund_tax]>
        <[AND ending_allot_cost = :ending_allot_cost]>
        <[AND ending_amount = :ending_amount]>
        <[AND plant_report_quantity = :plant_report_quantity]>
        <[AND quantity_diff = :quantity_diff]>
        <[AND count_date = :count_date]>
        <[:FILTER_BY_TYPE]>
        <[:VIRTUAL_SHIPPERS_SQL]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhInvoicingChangeCountMonthCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM <[:COME_FROM]>
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:idList)]>
        <[AND sku = :sku]>
        <[AND sku IN (:skuList)]>
        <[AND accountNumber = :accountNumber]>
        <[AND accountNumber IN (:berList)]>
        <[AND stock_date >= :from_stock_date]>
        <[AND stock_date <= :to_stock_date]>
        <[AND quantity = :quantity]>
        <[AND ending_purchase_price = :ending_purchase_price]>
        <[AND ending_purchase_cost = :ending_purchase_cost]>
        <[AND ending_first_trip_cost = :ending_first_trip_cost]>
        <[AND ending_first_trip_tax = :ending_first_trip_tax]>
        <[AND ending_refund_tax = :ending_refund_tax]>
        <[AND ending_allot_cost = :ending_allot_cost]>
        <[AND ending_amount = :ending_amount]>
        <[AND plant_report_quantity = :plant_report_quantity]>
        <[AND quantity_diff = :quantity_diff]>
        <[AND count_date = :count_date]>
        <[:FILTER_BY_TYPE]>
        <[:VIRTUAL_SHIPPERS_SQL]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhInvoicingChangeCountMonthList" >
    <content >
      <![CDATA[
        SELECT <[:COME_DATA]>
        FROM <[:COME_FROM]>
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:idList)]>
        <[AND sku = :sku]>
        <[AND sku IN (:skuList)]>
        <[AND accountNumber = :accountNumber]>
        <[AND accountNumber IN (:berList)]>
        <[AND stock_date >= :from_stock_date]>
        <[AND stock_date <= :to_stock_date]>
        <[AND quantity = :quantity]>
        <[AND ending_purchase_price = :ending_purchase_price]>
        <[AND ending_purchase_cost = :ending_purchase_cost]>
        <[AND ending_first_trip_cost = :ending_first_trip_cost]>
        <[AND ending_first_trip_tax = :ending_first_trip_tax]>
        <[AND ending_refund_tax = :ending_refund_tax]>
        <[AND ending_allot_cost = :ending_allot_cost]>
        <[AND ending_amount = :ending_amount]>
        <[AND plant_report_quantity = :plant_report_quantity]>
        <[AND quantity_diff = :quantity_diff]>
        <[AND count_date = :count_date]>
        <[:FILTER_BY_TYPE]>
        <[:VIRTUAL_SHIPPERS_SQL]>
      ]]>
    </content>
  </sql>
</sqlmap>