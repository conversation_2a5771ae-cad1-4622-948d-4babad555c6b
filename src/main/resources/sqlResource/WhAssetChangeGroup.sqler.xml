<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap>
  <sql datasource="dataSource" id="queryWhAssetChangeGroupList">
    <content >
      <![CDATA[
        SELECT
            id, sku, create_date as create_date_start, null as create_date_end,
            opening_inventory_quantity, opening_inventory_price, opening_inventory_amount,
            0 as ending_inventory_quantity, 0 as ending_inventory_price, 0 as ending_inventory_amount
        FROM wh_asset_change_item o
        WHERE create_date < :statistics_start_time
        <[AND sku IN (:sku_list)]>
        AND id = (
            SELECT MAX(id) FROM wh_asset_change_item t
            WHERE t.sku = o.sku AND create_date < :statistics_start_time
            <[AND sku IN (:sku_list)]>
        )
        UNION ALL
        SELECT
            id, sku, create_date as create_date_start, null as create_date_end,
            opening_inventory_quantity, opening_inventory_price, opening_inventory_amount,
            0 as ending_inventory_quantity, 0 as ending_inventory_price, 0 as ending_inventory_amount
        FROM wh_asset_change_item o
        WHERE create_date <= :statistics_end_time
        AND sku not in (
            SELECT sku FROM wh_asset_change_item
            WHERE create_date < :statistics_start_time
            <[AND sku IN (:sku_list)]> GROUP BY sku
        )
        <[AND sku IN (:sku_list)]>
        AND id = (
            SELECT MAX(id) FROM wh_asset_change_item t
            WHERE t.sku = o.sku
            AND create_date <= :statistics_end_time
            AND sku not in (
                SELECT sku FROM wh_asset_change_item
                WHERE create_date < :statistics_start_time
                <[AND sku IN (:sku_list)]> GROUP BY sku
            )
            <[AND sku IN (:sku_list)]>
        )
        UNION ALL
        SELECT
            id, sku, null as create_date_start, create_date as create_date_end,
            0 as opening_inventory_quantity, 0 as opening_inventory_price, 0 as opening_inventory_amount,
            ending_inventory_quantity, ending_inventory_price, ending_inventory_amount
        FROM wh_asset_change_item o
        WHERE create_date <= :statistics_end_time
        <[AND sku IN (:sku_list)]>
        AND id = (
            SELECT MAX(id) FROM wh_asset_change_item t
            WHERE t.sku = o.sku AND create_date <= :statistics_end_time
            <[AND sku IN (:sku_list)]>
        )
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAssetChangeGroupTotal" >
    <content >
      <![CDATA[
        SELECT
            SUM(opening_inventory_quantity) as opening_inventory_quantity,
            SUM(opening_inventory_amount) as opening_inventory_amount,
            SUM(ending_inventory_quantity) as ending_inventory_quantity,
            SUM(ending_inventory_amount) as ending_inventory_amount
        FROM (
            SELECT
                id, sku, create_date as create_date_start, null as create_date_end,
                opening_inventory_quantity, opening_inventory_price, opening_inventory_amount,
                0 as ending_inventory_quantity, 0 as ending_inventory_price, 0 as ending_inventory_amount
            FROM wh_asset_change_item o
            WHERE create_date < :statistics_start_time
            <[:sku_list]>
            AND id = (
                SELECT MAX(id) FROM wh_asset_change_item t
                WHERE t.sku = o.sku AND create_date < :statistics_start_time
                <[:sku_list]>
            )
            UNION ALL
            SELECT
                id, sku, create_date as create_date_start, null as create_date_end,
                opening_inventory_quantity, opening_inventory_price, opening_inventory_amount,
                0 as ending_inventory_quantity, 0 as ending_inventory_price, 0 as ending_inventory_amount
            FROM wh_asset_change_item o
            WHERE create_date <= :statistics_end_time
            AND sku not in (
                SELECT sku FROM wh_asset_change_item
                WHERE create_date < :statistics_start_time
                <[:sku_list]> GROUP BY sku
            )
            <[:sku_list]>
            AND id = (
                SELECT MAX(id) FROM wh_asset_change_item t
                WHERE t.sku = o.sku
                AND create_date <= :statistics_end_time
                AND sku not in (
                    SELECT sku FROM wh_asset_change_item
                    WHERE create_date < :statistics_start_time
                    <[:sku_list]> GROUP BY sku
                )
                <[:sku_list]>
            )
            UNION ALL
            SELECT
                id, sku, null as create_date_start, create_date as create_date_end,
                0 as opening_inventory_quantity, 0 as opening_inventory_price, 0 as opening_inventory_amount,
                ending_inventory_quantity, ending_inventory_price, ending_inventory_amount
            FROM wh_asset_change_item o
            WHERE create_date <= :statistics_end_time
            <[:sku_list]>
            AND id = (
                SELECT MAX(id) FROM wh_asset_change_item t
                WHERE t.sku = o.sku AND create_date <= :statistics_end_time
                <[:sku_list]>
            )
        ) t
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryAssetItemSkuCount" >
    <content >
      <![CDATA[
      	SELECT count(1) FROM (
            SELECT t.sku FROM wh_asset_change_item t
            LEFT JOIN wh_sku sku ON (t.sku = sku.sku)
            WHERE 1 = 1
            <[AND t.sku IN (:sku_list)]>
            <[AND t.create_date >= :statistics_start_time]>
            <[AND t.create_date <= :statistics_end_time]>
             <[AND sku.status IN (:sku_status_list)]>
            GROUP BY t.sku
        ) t
  	  ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryAssetItemSkuList" >
    <content >
      <![CDATA[
      	SELECT t.sku as sku FROM wh_asset_change_item t
      	LEFT JOIN wh_sku sku ON (t.sku = sku.sku)
		WHERE 1 = 1
        <[AND t.sku IN (:sku_list)]>
        <[AND t.create_date >= :statistics_start_time]>
        <[AND t.create_date <= :statistics_end_time]>
         <[AND sku.status IN (:sku_status_list)]>
        GROUP BY t.sku
  	  ]]>
    </content>
  </sql>
</sqlmap>