<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="querywhSkuPreallocatStockRecordCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_sku_preallocat_stock_record
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND created_date = :created_date]>
        <[AND sku = :sku]>
        <[AND preallocat_stock_quantity = :preallocat_stock_quantity]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND status = :status]>
        <[AND modified_date = :modified_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querywhSkuPreallocatStockRecordList" >
    <content >
      <![CDATA[
        SELECT id, created_date, sku, preallocat_stock_quantity, warehouse_id, status, modified_date
        FROM wh_sku_preallocat_stock_record
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND created_date = :created_date]>
        <[AND sku = :sku]>
        <[AND preallocat_stock_quantity = :preallocat_stock_quantity]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND status = :status]>
        <[AND modified_date = :modified_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querywhSkuPreallocatStockRecordByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, created_date, sku, preallocat_stock_quantity, warehouse_id, status, modified_date
        FROM wh_sku_preallocat_stock_record
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querywhSkuPreallocatStockRecord" >
    <content >
      <![CDATA[
        SELECT id, created_date, sku, preallocat_stock_quantity, warehouse_id, status, modified_date
        FROM wh_sku_preallocat_stock_record
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND created_date = :created_date]>
        <[AND sku = :sku]>
        <[AND preallocat_stock_quantity = :preallocat_stock_quantity]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND status = :status]>
        <[AND modified_date = :modified_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createwhSkuPreallocatStockRecord" >
    <content >
      <![CDATA[
        INSERT INTO wh_sku_preallocat_stock_record (created_date, sku, preallocat_stock_quantity, warehouse_id, status, 
          modified_date)
        VALUES (:created_date, :sku, :preallocat_stock_quantity, :warehouse_id, :status, 
          :modified_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deletewhSkuPreallocatStockRecordByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_sku_preallocat_stock_record
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updatewhSkuPreallocatStockRecordByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_sku_preallocat_stock_record
        SET <[created_date = :created_date,]>
          <[sku = :sku,]>
          <[preallocat_stock_quantity = :preallocat_stock_quantity,]>
          <[warehouse_id = :warehouse_id,]>
          <[status = :status,]>
          <[modified_date = :modified_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="batchInsertOrUpdatePreStock" >
    <content >
      <![CDATA[
       INSERT INTO wh_sku_preallocat_stock_record (created_date, sku, preallocat_stock_quantity,
       warehouse_id, status)
        VALUES
        <[:INSERT_SQL]>
        ON DUPLICATE KEY UPDATE
        <[:UPDATE_SQL]>
      ]]>
    </content>
  </sql>
</sqlmap>