<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap>

	<sql datasource="dataSource" id="queryMergeCheckoutStatisticsList">
		<content>
      <![CDATA[
        	SELECT
				pick.create_by AS userId,
				t.username AS userName,
				t.name AS name,
				COUNT(DISTINCT(apv.apv_no)) AS mergeSingCount
				
				<[:merge_date]>
			FROM
				wh_apv apv 
				JOIN wh_picking_task_item pick ON apv.id=pick.apv_id
				JOIN t_user t ON pick.create_by=t.user_id
			WHERE 
				1=1
				<[AND pick.create_by IN (:user_id_list)]>
				<[AND pick.created_date >= :start_merge_time]>
				<[AND pick.created_date <= :end_merge_time]>
			<[:group_by_merge_date]>
      ]]>
		</content>
	</sql>

	<!--出拣货看板-->
	<sql datasource="dataSource" id="queryPickCheckoutStatisticsList">
		<content>
      <![CDATA[
        	SELECT 
				t.user_id AS userId, t.username AS userName, t.name AS name, 
				COUNT(DISTINCT IF(pick.task_type IN (1, 5, 7, 10), apv_item.apv_id, NULL)) AS pickSsSingCount, 
				COUNT(DISTINCT IF(pick.task_type IN (2, 6, 8, 11), apv_item.apv_id, NULL)) AS pickSmSingCount,
				COUNT(DISTINCT IF(pick.task_type IN (15), apv_item.apv_id, NULL)) AS pickSApvCount,

				COUNT(DISTINCT IF(pick.task_type in (3,4,9,12) AND (pick.cross_floor IS NULL OR pick.cross_floor = 0), apv_item.apv_id, NULL)) AS pickMmSingCount,
				COUNT(DISTINCT IF(pick.task_type in (3,4,9,12) AND pick.cross_floor = 1, apv_item.apv_id, NULL)) AS pickCrossMmSingCount,

				COUNT(DISTINCT IF(pick.task_type IN (13), apv_item.apv_id, NULL)) AS pickAisleSsApvCount,
				COUNT(DISTINCT IF(pick.task_type IN (14), apv_item.apv_id, NULL)) AS pickAisleSmApvCount,
				SUM(IF(pick.task_type IN (1, 5, 7, 10), apv_item.pick_quantity, 0)) AS pickSsPcsCount,
				SUM(IF(pick.task_type IN (2, 6, 8, 11), apv_item.pick_quantity, 0)) AS pickSmPcsCount,
				SUM(IF(pick.task_type IN (15), apv_item.pick_quantity, 0)) AS pickSPcsCount,
				SUM(IF(pick.task_type in (3,4,9,12) AND (pick.cross_floor IS NULL OR pick.cross_floor = 0), apv_item.pick_quantity, 0)) AS pickMmPcsCount,
				SUM(IF(pick.task_type in (3,4,9,12) AND pick.cross_floor = 1, apv_item.pick_quantity, 0)) AS pickCrossMmPcsCount,
				SUM(IF(pick.task_type IN (13), apv_item.pick_quantity, 0)) AS pickAisleSsPcsCount,
				SUM(IF(pick.task_type IN (14), apv_item.pick_quantity, 0)) AS pickAisleSmPcsCount,
				COUNT(DISTINCT IF(pick.task_type IN (1, 5, 7, 10), CONCAT(pick.task_no, apv_item.sku), NULL)) AS pickSsSkuCount,
				COUNT(DISTINCT IF(pick.task_type IN (2, 6, 8, 11), CONCAT(pick.task_no, apv_item.sku), NULL)) AS pickSmSkuCount,
				COUNT(DISTINCT IF(pick.task_type IN (15), CONCAT(pick.task_no, apv_item.sku), NULL)) AS pickSSkuCount,
				COUNT(DISTINCT IF(pick.task_type in (3,4,9,12) AND (pick.cross_floor IS NULL OR pick.cross_floor = 0),CONCAT(pick.task_no,apv_item.sku), NULL)) AS pickMmSkuCount,
				COUNT(DISTINCT IF(pick.task_type in (3,4,9,12) AND pick.cross_floor = 1,CONCAT(pick.task_no,apv_item.sku), NULL)) AS pickCrossMmSkuCount,
				COUNT(DISTINCT IF(pick.task_type IN (13), CONCAT(pick.task_no, apv_item.sku), NULL)) AS pickAisleSsSkuCount, 
				COUNT(DISTINCT IF(pick.task_type IN (14), CONCAT(pick.task_no, apv_item.sku), NULL)) AS pickAisleSmSkuCount, 
				DATE_FORMAT(tApv.pick_finish_time, '%H') AS pickDate,

				COUNT(DISTINCT IF(pick.task_type IN (59), apv_item.apv_id, NULL)) AS pickJitSsSingCount,
				COUNT(DISTINCT IF(pick.task_type IN (60), apv_item.apv_id, NULL)) AS pickJitSmSingCount,
				COUNT(DISTINCT IF(pick.task_type IN (61, 62), apv_item.apv_id, NULL)) AS pickJitMmSingCount,
				SUM(IF(pick.task_type IN (59), apv_item.pick_quantity, 0)) AS pickJitSsPcsCount,
				SUM(IF(pick.task_type IN (60), apv_item.pick_quantity, 0)) AS pickJitSmPcsCount,
				SUM(IF(pick.task_type IN (61, 62), apv_item.pick_quantity, 0)) AS pickJitMmPcsCount,
				COUNT(DISTINCT IF(pick.task_type IN (59), CONCAT(pick.task_no, apv_item.sku), NULL)) AS pickJitSsSkuCount,
				COUNT(DISTINCT IF(pick.task_type IN (60), CONCAT(pick.task_no, apv_item.sku), NULL)) AS pickJitSmSkuCount,
				COUNT(DISTINCT IF(pick.task_type IN (61, 62), CONCAT(pick.task_no, apv_item.sku), NULL)) AS pickJitMmSkuCount,
				0 AS pickOpSsPcsCount,
				0 AS pickOpSmPcsCount,
				0 AS pickOpSvPcsCount,
				0 AS pickOpMmPcsCount,
				0 AS pickOpSsSkuCount,
				0 AS pickOpSmSkuCount,
				0 AS pickOpSvSkuCount,
				0 AS pickOpMmSkuCount,
				0 AS pickOpSsCount,
				0 AS pickOpSmCount,
				0 AS pickOpSvCount,
				0 AS pickOpMmCount,
				0 AS pickHvSsCount,
				0 AS pickHvSmCount,
				0 AS pickHvSvCount,
				0 AS pickHvMmCount,
				0 AS pickHvSsPcsCount,
				0 AS pickHvSmPcsCount,
				0 AS pickHvSvPcsCount,
				0 AS pickHvMmPcsCount,
				0 AS pickHvSsSkuCount,
				0 AS pickHvSmSkuCount,
				0 AS pickHvSvSkuCount,
				0 AS pickHvMmSkuCount
			FROM (
				SELECT 
					apv.id,
					apv.complete_status,
					track.pick_finish_time
				FROM 
					wh_apv apv LEFT JOIN apv_track track ON apv.apv_no = track.apv_no 
				WHERE 1 = 1
					<[AND apv.status IN (:apv_status_condition)]>
					AND track.pick_finish_time >= :start_pick_time 
					AND track.pick_finish_time <= :end_pick_time
			) tApv
				LEFT JOIN wh_apv_item apv_item ON apv_item.apv_id = tApv.id
				LEFT JOIN wh_picking_task pick ON tApv.complete_status = pick.task_no
				JOIN t_user t ON pick.receive_person = t.user_id
			WHERE 1 = 1
				AND apv_item.pick_quantity IS NOT NULL AND  apv_item.pick_quantity > 0
			GROUP BY pickDate 
      ]]>
		</content>
	</sql>

	<!--中转仓出拣货看板-->
	<sql datasource="dataSource" id="queryTransferPickCheckoutStatisticsList">
		<content>
			<![CDATA[
        	SELECT
				t.user_id AS userId, t.username AS userName, t.name AS name,
				COUNT(DISTINCT IF(pick.task_type IN (51), apv_item.relevant_no, NULL)) AS pickTransferSsCount,
				COUNT(DISTINCT IF(pick.task_type IN (52,64), apv_item.relevant_no, NULL)) AS pickTransferSmCount,
				COUNT(DISTINCT IF(pick.task_type IN (53,54,65,66), apv_item.relevant_no, NULL)) AS pickTransferMmCount,
				SUM(IF(pick.task_type IN (51), apv_item.pick_quantity, 0)) AS pickTransferSsPcsCount,
				SUM(IF(pick.task_type IN (52,64), apv_item.pick_quantity, 0)) AS pickTransferSmPcsCount,
				SUM(IF(pick.task_type IN (53,54,65,66), apv_item.pick_quantity, 0)) AS pickTransferMmPcsCount,
				COUNT(DISTINCT IF(pick.task_type IN (51), CONCAT(pick.task_no, apv_item.sku), NULL)) AS pickTransferSsSkuCount,
				COUNT(DISTINCT IF(pick.task_type IN (52,64), CONCAT(pick.task_no, apv_item.sku), NULL)) AS pickTransferSmSkuCount,
				COUNT(DISTINCT IF(pick.task_type IN (53,54,65,66), CONCAT(pick.task_no, apv_item.sku), NULL)) AS pickTransferMmSkuCount,

				COUNT(DISTINCT IF(pick.task_type IN (21), apv_item.relevant_no, NULL)) AS pickAsnFirstCount,
				SUM(IF(pick.task_type IN (21), apv_item.pick_quantity, 0)) AS pickAsnFirstPcsCount,
				COUNT(DISTINCT IF(pick.task_type IN (21), CONCAT(pick.task_no, apv_item.sku), NULL)) AS pickAsnFirstSkuCount,

				COUNT(DISTINCT IF(pick.task_type IN (20), apv_item.relevant_no, NULL)) AS pickAsnFbaSingCount,
				SUM(IF(pick.task_type IN (20), apv_item.pick_quantity, 0)) AS pickAsnFbaSingPcsCount,
				COUNT(DISTINCT IF(pick.task_type IN (20), CONCAT(pick.task_no, apv_item.sku), NULL)) AS pickAsnFbaSingSkuCount,
				DATE_FORMAT(tApv.pick_finish_time, '%H') AS pickDate
			FROM (
				SELECT
					apv.fba_no,
					track.task_no,
					track.pick_finish_time
				FROM
					wh_fba_allocation apv LEFT JOIN apv_track track ON apv.fba_no = track.apv_no
				WHERE 1 = 1
					<[AND apv.status IN (:transfer_status_condition)]>
					AND track.pick_finish_time >= :start_pick_time
					AND track.pick_finish_time <= :end_pick_time
			) tApv
				LEFT JOIN wh_apv_out_stock_chain apv_item ON apv_item.relevant_no = tApv.fba_no
				LEFT JOIN wh_picking_task pick ON tApv.task_no = pick.task_no
				JOIN t_user t ON  pick.receive_person = t.user_id
			WHERE 1 = 1
				AND apv_item.pick_quantity IS NOT NULL AND  apv_item.pick_quantity > 0
			GROUP BY pickDate
      ]]>
		</content>
	</sql>

	<!--TEMU出拣货看板-->
	<sql datasource="dataSource" id="queryTemuPickCheckoutStatisticsList">
		<content>
			<![CDATA[
        	SELECT
				t.user_id AS userId, t.username AS userName, t.name AS name,
				COUNT(DISTINCT IF(pick.task_type IN (56,57), tpoi.package_sn, NULL)) AS pickPddSingCount,
				SUM(IF(pick.task_type IN (56,57), tpoi.pick_quantity, 0)) AS pickPddSingPcsCount,
				COUNT(DISTINCT IF(pick.task_type IN (56,57), CONCAT(pick.task_no, tpoi.sku), NULL)) AS pickPddSingSkuCount,
				COUNT(DISTINCT IF(pick.task_type IN (56,57) and tpoi.real_quantity = 1, tpoi.package_sn, NULL)) AS pickTransferSsCount,
				SUM(IF(pick.task_type IN (56,57) and tpoi.real_quantity = 1, tpoi.pick_quantity, 0)) AS pickTransferSsPcsCount,
				COUNT(DISTINCT IF(pick.task_type IN (56,57) and tpoi.real_quantity = 1, CONCAT(pick.task_no, tpoi.sku), NULL)) AS pickTransferSsSkuCount,
				COUNT(DISTINCT IF(pick.task_type IN (56,57) and tpoi.real_quantity > 1, tpoi.package_sn, NULL)) AS pickTransferSmCount,
				SUM(IF(pick.task_type IN (56,57) and tpoi.real_quantity > 1, tpoi.pick_quantity, 0)) AS pickTransferSmPcsCount,
				COUNT(DISTINCT IF(pick.task_type IN (56,57) and tpoi.real_quantity > 1, CONCAT(pick.task_no, tpoi.sku), NULL)) AS pickTransferSmSkuCount,
				DATE_FORMAT(tpoi.pick_order_time, '%H') AS pickDate
			FROM
			    temu_prepare_order_item tpoi
			    LEFT JOIN wh_picking_task_item item ON tpoi.id = item.apv_id
				LEFT JOIN wh_picking_task pick ON pick.id = item.task_id
				JOIN t_user t ON  pick.receive_person = t.user_id
			WHERE 1 = 1
				<[AND tpoi.package_status IN (:pdd_status_condition)]>
				AND tpoi.pick_quantity IS NOT NULL AND  tpoi.pick_quantity > 0
				AND tpoi.pick_time >= :start_pick_time
				AND tpoi.pick_time <= :end_pick_time
			GROUP BY pickDate
      ]]>
		</content>
	</sql>


	<!--出库作业报表(区分通道任务)-->
	<sql datasource="dataSource" id="queryPickCheckoutStatisticsListByTaskType">
		<content>
      <![CDATA[
        	SELECT
				t.user_id AS userId,
				t.username AS userName,
				t.name AS name,
				COUNT(DISTINCT IF(pick.task_type in (1,5,7,10), apv_item.apv_id, NULL)) AS pickSsSingCount,
				COUNT(DISTINCT IF(pick.task_type in (2,6,8,11), apv_item.apv_id , NULL)) AS pickSmSingCount,
				COUNT(DISTINCT IF(pick.task_type in (15), apv_item.apv_id, NULL)) AS pickSApvCount,
				COUNT(DISTINCT IF(pick.task_type in (3,4,9,12) AND (pick.cross_floor IS NULL OR pick.cross_floor = 0), apv_item.apv_id, NULL)) AS pickMmSingCount,
				COUNT(DISTINCT IF(pick.task_type in (3,4,9,12) AND pick.cross_floor = 1, apv_item.apv_id, NULL)) AS pickCrossMmSingCount,
				COUNT(DISTINCT IF(pick.task_type in (13), apv_item.apv_id , NULL)) AS pickAisleSsApvCount,
				COUNT(DISTINCT IF(pick.task_type in (14), apv_item.apv_id, NULL)) AS pickAisleSmApvCount,
				COUNT(DISTINCT IF(pick.task_type in (311), apv_item.apv_id, NULL)) AS pickOpSsCount,
				COUNT(DISTINCT IF(pick.task_type in (312), apv_item.apv_id, NULL)) AS pickOpSmCount,
				COUNT(DISTINCT IF(pick.task_type in (313), apv_item.apv_id, NULL)) AS pickOpSvCount,
				COUNT(DISTINCT IF(pick.task_type in (314), apv_item.apv_id, NULL)) AS pickOpMmCount,
				COUNT(DISTINCT IF(pick.task_type in (31), apv_item.apv_id, NULL)) AS pickHvSsCount,
				COUNT(DISTINCT IF(pick.task_type in (32), apv_item.apv_id, NULL)) AS pickHvSmCount,
				COUNT(DISTINCT IF(pick.task_type in (33), apv_item.apv_id, NULL)) AS pickHvSvCount,
				COUNT(DISTINCT IF(pick.task_type in (34), apv_item.apv_id, NULL)) AS pickHvMmCount,

				SUM(IF(pick.task_type in (1,5,7,10), apv_item.pick_quantity, 0)) AS pickSsPcsCount,
				SUM(IF(pick.task_type in (2,6,8,11), apv_item.pick_quantity, 0))AS pickSmPcsCount,
				SUM(IF(pick.task_type in (15), apv_item.pick_quantity, 0)) AS pickSPcsCount ,
				SUM(IF(pick.task_type in (3,4,9,12) AND (pick.cross_floor IS NULL OR pick.cross_floor = 0), apv_item.pick_quantity, 0)) AS pickMmPcsCount,
				SUM(IF(pick.task_type in (3,4,9,12) AND pick.cross_floor = 1, apv_item.pick_quantity, 0)) AS pickCrossMmPcsCount,
				SUM(IF(pick.task_type in (13), apv_item.pick_quantity, 0))AS pickAisleSsPcsCount,
				SUM(IF(pick.task_type in (14), apv_item.pick_quantity, 0)) AS pickAisleSmPcsCount,
				SUM(IF(pick.task_type in (311), apv_item.pick_quantity, 0)) AS pickOpSsPcsCount,
				SUM(IF(pick.task_type in (312), apv_item.pick_quantity, 0)) AS pickOpSmPcsCount,
				SUM(IF(pick.task_type in (313), apv_item.pick_quantity, 0)) AS pickOpSvPcsCount,
				SUM(IF(pick.task_type in (314), apv_item.pick_quantity, 0)) AS pickOpMmPcsCount,
				SUM(IF(pick.task_type in (31), apv_item.pick_quantity, 0)) AS pickHvSsPcsCount,
				SUM(IF(pick.task_type in (32), apv_item.pick_quantity, 0)) AS pickHvSmPcsCount,
				SUM(IF(pick.task_type in (33), apv_item.pick_quantity, 0)) AS pickHvSvPcsCount,
				SUM(IF(pick.task_type in (34), apv_item.pick_quantity, 0)) AS pickHvMmPcsCount,

				COUNT(DISTINCT IF(pick.task_type in (1,5,7,10),CONCAT(pick.task_no,apv_item.sku), NULL)) AS pickSsSkuCount,
				COUNT(DISTINCT IF(pick.task_type in (2,6,8,11),CONCAT(pick.task_no,apv_item.sku), NULL)) AS pickSmSkuCount,
				COUNT(DISTINCT IF(pick.task_type in (15),CONCAT(pick.task_no,apv_item.sku), NULL)) AS pickSSkuCount,
				COUNT(DISTINCT IF(pick.task_type in (3,4,9,12) AND (pick.cross_floor IS NULL OR pick.cross_floor = 0),CONCAT(pick.task_no,apv_item.sku), NULL)) AS pickMmSkuCount,
				COUNT(DISTINCT IF(pick.task_type in (3,4,9,12) AND pick.cross_floor = 1,CONCAT(pick.task_no,apv_item.sku), NULL)) AS pickCrossMmSkuCount,
				COUNT(DISTINCT IF(pick.task_type in (13),CONCAT(pick.task_no,apv_item.sku), NULL)) AS pickAisleSsSkuCount,
				COUNT(DISTINCT IF(pick.task_type in (14),CONCAT(pick.task_no,apv_item.sku), NULL)) AS pickAisleSmSkuCount,
				COUNT(DISTINCT IF(pick.task_type in (311),CONCAT(pick.task_no,apv_item.sku), NULL)) AS pickOpSsSkuCount,
				COUNT(DISTINCT IF(pick.task_type in (312),CONCAT(pick.task_no,apv_item.sku), NULL)) AS pickOpSmSkuCount,
				COUNT(DISTINCT IF(pick.task_type in (313),CONCAT(pick.task_no,apv_item.sku), NULL)) AS pickOpSvSkuCount,
				COUNT(DISTINCT IF(pick.task_type in (314),CONCAT(pick.task_no,apv_item.sku), NULL)) AS pickOpMmSkuCount,
				COUNT(DISTINCT IF(pick.task_type in (31),CONCAT(pick.task_no,apv_item.sku), NULL)) AS pickHvSsSkuCount,
				COUNT(DISTINCT IF(pick.task_type in (32),CONCAT(pick.task_no,apv_item.sku), NULL)) AS pickHvSmSkuCount,
				COUNT(DISTINCT IF(pick.task_type in (33),CONCAT(pick.task_no,apv_item.sku), NULL)) AS pickHvSvSkuCount,
				COUNT(DISTINCT IF(pick.task_type in (34),CONCAT(pick.task_no,apv_item.sku), NULL)) AS pickHvMmSkuCount,

				COUNT(DISTINCT IF(pick.task_type IN (59), apv_item.apv_id, NULL)) AS pickJitSsSingCount,
				COUNT(DISTINCT IF(pick.task_type IN (60), apv_item.apv_id, NULL)) AS pickJitSmSingCount,
				COUNT(DISTINCT IF(pick.task_type IN (61,62), apv_item.apv_id, NULL)) AS pickJitMmSingCount,
				SUM(IF(pick.task_type IN (59), apv_item.pick_quantity, 0)) AS pickJitSsPcsCount,
				SUM(IF(pick.task_type IN (60), apv_item.pick_quantity, 0)) AS pickJitSmPcsCount,
				SUM(IF(pick.task_type IN (61,62), apv_item.pick_quantity, 0)) AS pickJitMmPcsCount,
				COUNT(DISTINCT IF(pick.task_type IN (59), CONCAT(pick.task_no, apv_item.sku), NULL)) AS pickJitSsSkuCount,
				COUNT(DISTINCT IF(pick.task_type IN (60), CONCAT(pick.task_no, apv_item.sku), NULL)) AS pickJitSmSkuCount,
				COUNT(DISTINCT IF(pick.task_type IN (61,62), CONCAT(pick.task_no, apv_item.sku), NULL)) AS pickJitMmSkuCount

				<[:pick_date]>
			FROM
				(
					SELECT
						apv.id,
						max(task.id) taskId
					FROM
						wh_apv apv left join wh_picking_task_item piskitem on piskitem.apv_id=apv.id
						left join wh_picking_task task on task.id=piskitem.task_id
					WHERE
						1=1
						AND apv.status IN (:apv_status_condition)
						<[AND task.picking_end_date >= :start_pick_time]>

						GROUP BY apv.id
				) tApv
			LEFT JOIN wh_apv_item apv_item ON apv_item.apv_id=tApv.id
			LEFT JOIN wh_picking_task pick ON tApv.taskId = pick.id
			JOIN t_user t ON pick.receive_person=t.user_id
			WHERE
				1=1
				AND pick.task_status=2
				<[AND pick.receive_person IN (:user_id_list)]>
				<[AND pick.picking_end_date >= :start_pick_time]>
				<[AND pick.picking_end_date <= :end_pick_time]>
				<[AND ((pick.task_type in (3,4,9,12) AND apv_item.salesperson=:pick_warehouse_id) OR (pick.task_type in (1,2,5,6,7,8,10,11,13,14,15,311,312,313,314,31,32,33,34,59,60,61,62)))]>
				AND apv_item.pick_quantity > 0
			<[:group_by_pick_date]>
      ]]>
		</content>
	</sql>

    <sql datasource="dataSource" id="queryPickCheckoutStatisticsListByFba">
        <content>
            <![CDATA[
                    SELECT
                        t.user_id AS userId, t.username AS userName, t.name AS name
                        , COUNT(DISTINCT IF(pick.task_type = 20, CONCAT(pick.task_no,wpts.sku), NULL)) AS pickFbaSkuCount
                        , SUM( IF(pick.is_asn = 20 , wpts.pick_quantity , 0))  AS pickFbaPcsCount
                        , COUNT(DISTINCT IF(pick.task_type = 21, CONCAT(pick.task_no,wpts.sku), NULL)) AS pickOverseaHeadwaySkuCount
                        , SUM( IF(pick.is_asn = 21 , wpts.pick_quantity , 0))  AS pickOverseaHeadwayPcsCount
                        , COUNT(DISTINCT IF(pick.task_type = 51, CONCAT(pick.task_no, wpts.sku), NULL)) AS pickTransferSsSkuCount
						, SUM(IF(pick.is_asn = 51, wpts.pick_quantity, 0)) AS pickTransferSsPcsCount
						, COUNT(DISTINCT IF(pick.task_type = 52, CONCAT(pick.task_no, wpts.sku), NULL)) AS pickTransferSmSkuCount
						, SUM(IF(pick.is_asn = 52, wpts.pick_quantity, 0)) AS pickTransferSmPcsCount
						, COUNT(DISTINCT IF(pick.task_type = 53, CONCAT(pick.task_no, wpts.sku), NULL)) AS pickTransferMmSkuCount
						, SUM(IF(pick.is_asn = 53, wpts.pick_quantity, 0)) AS pickTransferMmPcsCount
						, COUNT(DISTINCT IF(pick.task_type = 53 AND pick.cross_floor = 1, CONCAT(pick.task_no, wpts.sku), NULL)) AS pickTransferCrossMmSkuCount
						, SUM(IF(pick.is_asn = 53 AND pick.cross_floor = 1, wpts.pick_quantity, 0)) AS pickTransferCrossMmPcsCount
						, COUNT(DISTINCT IF(pick.task_type = 64, CONCAT(pick.task_no,wpts.sku), NULL)) AS pickWarehouseSingleSkuCount
						, SUM(IF(pick.task_type =64 , wpts.pick_quantity, 0)) AS pickWarehouseSinglePcsCount
						, COUNT(DISTINCT IF(pick.task_type = 64 AND
							(SELECT COUNT(1)>=1
									FROM wh_fba_allocation wfa LEFT JOIN wh_fba_allocation_item wfai ON wfa.id = wfai.fba_id
									LEFT JOIN wh_picking_task_item wpti ON wpti.apv_id = wfa.id
									WHERE pick.id = wpti.task_id AND wfa.apv_type = 'SS' AND wpts.sku = wfai.product_sku),
							CONCAT(pick.task_no,wpts.sku), NULL)) AS pickWarehouseSingleSsSkuCount
						, SUM(IF(pick.task_type =64 AND
								(SELECT COUNT(1)>=1
									FROM wh_fba_allocation wfa LEFT JOIN wh_fba_allocation_item wfai ON wfa.id = wfai.fba_id
									LEFT JOIN wh_picking_task_item wpti ON wpti.apv_id = wfa.id
									WHERE pick.id = wpti.task_id AND wfa.apv_type = 'SS' AND wpts.sku = wfai.product_sku),
								(SELECT SUM(wfai.pick_quantity)
									FROM wh_fba_allocation wfa LEFT JOIN wh_fba_allocation_item wfai ON wfa.id = wfai.fba_id
									LEFT JOIN wh_picking_task_item wpti ON wpti.apv_id = wfa.id
									WHERE pick.id = wpti.task_id AND wfa.apv_type = 'SS' AND wpts.sku = wfai.product_sku), 0)) AS pickWarehouseSingleSsPcsCount
						, COUNT(DISTINCT IF(pick.task_type = 64 AND
							 (SELECT COUNT(1)>=1
										FROM wh_fba_allocation wfa LEFT JOIN wh_fba_allocation_item wfai ON wfa.id = wfai.fba_id
										LEFT JOIN wh_picking_task_item wpti ON wpti.apv_id = wfa.id
										WHERE pick.id = wpti.task_id AND wfa.apv_type = 'SM' AND wpts.sku = wfai.product_sku),
							CONCAT(pick.task_no,wpts.sku), NULL)) AS pickWarehouseSingleSmSkuCount
						, SUM(IF(pick.task_type =64 AND
						 		(SELECT COUNT(1)>=1
									FROM wh_fba_allocation wfa LEFT JOIN wh_fba_allocation_item wfai ON wfa.id = wfai.fba_id
									LEFT JOIN wh_picking_task_item wpti ON wpti.apv_id = wfa.id
									WHERE pick.id = wpti.task_id AND wfa.apv_type = 'SM' AND wpts.sku = wfai.product_sku),
								(SELECT SUM(wfai.pick_quantity)
									FROM wh_fba_allocation wfa LEFT JOIN wh_fba_allocation_item wfai ON wfa.id = wfai.fba_id
									LEFT JOIN wh_picking_task_item wpti ON wpti.apv_id = wfa.id
									WHERE pick.id = wpti.task_id AND wfa.apv_type = 'SM' AND wpts.sku = wfai.product_sku), 0)) AS pickWarehouseSingleSmPcsCount
						, COUNT(DISTINCT IF(pick.task_type in (66,65), CONCAT(pick.task_no,wpts.sku), NULL)) AS pickWarehouseMmSkuCount
						, SUM(IF(pick.task_type in (66,65) , wpts.pick_quantity, 0)) AS pickWarehouseMmPcsCount
                        <[:pick_date]>
                    FROM
                        wh_picking_task pick
                        LEFT JOIN wh_picking_task_sku wpts ON pick.id = wpts.task_id
                        JOIN t_user t ON pick.receive_person = t.user_id
                    WHERE
                        1=1
                        AND pick.is_asn IN ( 20, 21, 51,52,53,65)
                        AND pick.task_status=2
                        <[AND pick.receive_person IN (:user_id_list)]>
                        <[AND pick.picking_end_date >= :start_pick_time]>
                        <[AND pick.picking_end_date <= :end_pick_time]>
                    <[:group_by_pick_date]>
              ]]>
        </content>
    </sql>

	<sql datasource="dataSource" id="queryPickRfoStatistics">
		<content>
			<![CDATA[
				SELECT
					t.user_id AS userId,t.username AS userName,t.name AS name
					, COUNT(DISTINCT t.id) AS pickRfoSingCount
					, COUNT(t.sku) AS pickRfoSkuCount
					, SUM(t.pick_quantity) AS pickRfoPcsCount
					 <[:pick_date]>
				FROM
					(
					SELECT t.user_id,t.username,t.name,o.id,o.pick_end_time,o.pick_by,i.sku,i.pick_quantity
					FROM
						return_form_order o
						LEFT JOIN return_form_order_item i ON i.rfo_id = o.id
						JOIN t_user t ON o.pick_by = t.user_id
					WHERE o.`status` > 3
						<[AND o.pick_by IN (:user_id_list)]>
						<[AND o.pick_end_time >= :start_pick_time]>
						<[AND o.pick_end_time <= :end_pick_time]>
					GROUP BY o.id,i.sku
					) t
				<[:group_by_pick_date]>
            ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryPickLendStatistics">
		<content>
			<![CDATA[
				SELECT
					t.user_id AS userId, t.username AS userName, t.name AS name
					, COUNT(DISTINCT item.lend_id) AS pickLendSingCount
					, COUNT(DISTINCT CONCAT(item.lend_id,wli.sku)) AS pickLendSkuCount
					, SUM(wli.pick_quantity) AS pickLendPcsCount
					<[:pick_date]>
				FROM
					wh_lend_picking_task lend
					LEFT JOIN wh_lend_picking_task_item item ON lend.id = item.task_id
					LEFT JOIN wh_lend_item wli ON wli.wh_lend_id = item.lend_id
					JOIN t_user t ON lend.receive_person = t.user_id
					WHERE lend.task_status IN (14,16) AND wli.pick_quantity > 0
					<[AND lend.receive_person IN (:user_id_list)]>
					<[AND lend.picking_end_date >= :start_pick_time]>
					<[AND lend.picking_end_date <= :end_pick_time]>
				<[:group_by_pick_date]>
            ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryGridRfoStatistics">
		<content>
			<![CDATA[
				SELECT
					t.user_id AS userId,t.username AS userName,t.name AS name
					, COUNT(DISTINCT t.id) AS softRfoSingCount
					, COUNT(t.sku) AS softRfoSkuCount
					, SUM(t.grid_quantity) AS softRfoPcsCount
					 <[:soft_date]>
				FROM
					(
					SELECT t.user_id,t.username,t.name,o.id,o.grid_time,o.grid_by,i.sku,i.grid_quantity
					FROM
						return_form_order o
						LEFT JOIN return_form_order_item i ON i.rfo_id = o.id
						JOIN t_user t ON o.grid_by = t.user_id
					WHERE o.`status` > 3
						<[AND o.grid_by IN (:user_id_list)]>
						<[AND o.grid_time >= :start_soft_time]>
						<[AND o.grid_time <= :end_soft_time]>
					GROUP BY o.id,i.sku
					) t
				<[:group_by_soft_date]>
            ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryPickSpoStatistics">
		<content>
			<![CDATA[
				SELECT
					t.user_id AS userId,t.username AS userName,t.name AS name
					, COUNT(DISTINCT t.id) AS pickSpoSingCount
					, COUNT(t.sku) AS pickSpoSkuCount
					, SUM(t.pick_qty) AS pickSpoPcsCount
					 <[:pick_date]>
				FROM
					(
					SELECT t.user_id,t.username,t.name,o.id,o.pick_end_time,o.pick_by,i.sku,i.pick_qty
					FROM
						shop_order o
						LEFT JOIN shop_order_item i ON i.order_id = o.id
						JOIN t_user t ON o.pick_by = t.user_id
					WHERE o.`status` > 4 AND i.pick_qty > 0
						<[AND o.pick_by IN (:user_id_list)]>
						<[AND o.pick_end_time >= :start_pick_time]>
						<[AND o.pick_end_time <= :end_pick_time]>
					GROUP BY o.id,i.sku
					) t
				<[:group_by_pick_date]>
            ]]>
		</content>
	</sql>

	<!--<sql datasource="dataSource" id="queryPickCheckoutStatisticsByTemu">
		<content>
			<![CDATA[
				SELECT
					t.user_id AS userId,t.username AS userName,t.name AS name
					, COUNT(DISTINCT t.id) AS pickTemuCount
					, COUNT(t.sku) AS pickTemuSkuCount
					, SUM(t.pick_quantity) AS pickTemuPcsCount
					 <[:pick_date]>
				FROM
					(
					SELECT t.user_id,t.username,t.name,o.id,i.pick_time,i.pick_by,i.sku,i.pick_quantity
					FROM
						temu_prepare_order o
						LEFT JOIN temu_prepare_order_item i ON i.prepare_order_id = o.id
						JOIN t_user t ON i.pick_by = t.user_id
					WHERE o.`status` > 5 AND i.pick_quantity > 0
						<[AND o.pick_by IN (:user_id_list)]>
						<[AND i.pick_time >= :start_pick_time]>
						<[AND i.pick_time <= :end_pick_time]>
					GROUP BY o.id,i.sku
					) t
				<[:group_by_pick_date]>
            ]]>
		</content>
	</sql>-->

	<sql datasource="dataSource" id="querySaleDirectCheckoutStatisticsList">
		<content>
			<![CDATA[
                    SELECT
                        t.user_id AS userId, t.username AS userName, t.name AS name
 						<[:OBTAIN_TIME]>
                        <[:FILED_NAME]>
                    FROM
                        wh_check_in wc
						 <[:TABLE_NAME]>
                        JOIN t_user t ON wc.obtain_user = t.user_id
                    WHERE
                        1=1
                        <[AND wc.obtain_user IN (:user_id_list)]>
                        <[AND wc.obtain_time >= :start_pick_time]>
                        <[AND wc.obtain_time <= :end_pick_time]>
                    <[:group_by_obtain_time]>
              ]]>
		</content>
	</sql>


	<sql datasource="dataSource" id="queryPickCheckoutStatisticsByTemu">
		<content>
			<![CDATA[
                    SELECT
                        t.user_id AS userId, t.username AS userName, t.name AS name
 						<[:pick_date]>
                        <[:FILED_NAME]>
                    FROM
                        wh_picking_task pick
						 <[:TABLE_NAME]>
                        JOIN t_user t ON pick.receive_person = t.user_id
                    WHERE
                        1=1
                        AND pick.is_asn IN (56)
                        AND pick.task_status=2
                        <[AND pick.receive_person IN (:user_id_list)]>
                        <[AND pick.picking_end_date >= :start_pick_time]>
                        <[AND pick.picking_end_date <= :end_pick_time]>
                    <[:group_by_pick_date]>
              ]]>
		</content>
	</sql>


	<sql datasource="dataSource" id="queryGridSpoStatistics">
		<content>
			<![CDATA[
				SELECT
					t.user_id AS userId,t.username AS userName,t.name AS name
					, COUNT(DISTINCT t.id) AS softSpoSingCount
					, COUNT(t.sku) AS softSpoSkuCount
					, SUM(t.grid_qty) AS softSpoPcsCount
					 <[:soft_date]>
				FROM
					(
					SELECT t.user_id,t.username,t.name,o.id,o.grid_time,o.grid_by,i.sku,i.grid_qty
					FROM
						shop_order o
						LEFT JOIN shop_order_item i ON i.order_id = o.id
						JOIN t_user t ON o.grid_by = t.user_id
					WHERE o.`status` > 5 AND i.grid_qty > 0
						<[AND o.grid_by IN (:user_id_list)]>
						<[AND o.grid_time >= :start_soft_time]>
						<[AND o.grid_time <= :end_soft_time]>
					GROUP BY o.id,i.sku
					) t
				<[:group_by_soft_date]>
            ]]>
		</content>
	</sql>


	<sql datasource="dataSource" id="queryPickCheckoutFbaSingCount">
		<content>
			<![CDATA[
                    SELECT
                        t.user_id AS userId, t.username AS userName, t.name AS name
                        , COUNT(DISTINCT IF(pick.is_asn = 20 , wpti.apv_id , NULL)) AS pickFbaSingCount
                        , COUNT(DISTINCT IF(pick.is_asn = 21 , wpti.apv_id , NULL)) AS pickOverseaHeadwaySingCount
                        , COUNT(DISTINCT IF(pick.is_asn = 51 AND wfa.status != 20, wpti.apv_id, NULL)) AS pickTransferSsCount
						, COUNT(DISTINCT IF(pick.is_asn = 52 AND wfa.status != 20, wpti.apv_id, NULL)) AS pickTransferSmCount
						, COUNT(DISTINCT IF(pick.is_asn = 53, wpti.apv_id, NULL)) AS pickTransferMmCount
						, COUNT(DISTINCT IF(pick.is_asn = 53 AND pick.cross_floor = 1, wpti.apv_id, NULL)) AS pickTransferCrossMmCount
						, COUNT(DISTINCT IF(pick.task_type = 64, wpti.apv_id, NULL)) AS pickWarehouseSingleCount
						, COUNT(DISTINCT IF(pick.task_type = 64 AND wfa.apv_type = 'SS' AND wfa.status != 20, wpti.apv_id, NULL)) AS pickWarehouseSingleSsCount
						, COUNT(DISTINCT IF(pick.task_type = 64 AND wfa.apv_type = 'SM' AND wfa.status != 20, wpti.apv_id, NULL)) AS pickWarehouseSingleSmCount
						, COUNT(DISTINCT IF(pick.task_type in (66,65), wpti.apv_id, NULL)) AS pickWarehouseMmCount
                        <[:pick_date]>
                    FROM
                        wh_picking_task pick
                        LEFT JOIN wh_picking_task_item wpti ON pick.id = wpti.task_id
                        LEFT JOIN wh_fba_allocation wfa ON wfa.id = wpti.apv_id
                        JOIN t_user t ON pick.receive_person = t.user_id
                    WHERE
                        1=1
                        AND pick.is_asn IN ( 20, 21,51,52,53,65)
                        AND pick.task_status=2
                        <[AND pick.receive_person IN (:user_id_list)]>
                        <[AND pick.picking_end_date >= :start_pick_time]>
                        <[AND pick.picking_end_date <= :end_pick_time]>
                    <[:group_by_pick_date]>
              ]]>
		</content>
	</sql>



	<sql datasource="dataSource" id="queryPickExceptionCheckoutStatisticsList">
		<content>
      <![CDATA[
        	SELECT 
				t.user_id AS userId,
				t.username AS userName,
				t.name AS name,
				COUNT(DISTINCT IF(tApv.apv_type='SS', apv_item.apv_id, NULL)) AS pickSsSingCount, 
				COUNT(DISTINCT IF(tApv.apv_type='SM', apv_item.apv_id , NULL)) AS pickSmSingCount, 
				COUNT(DISTINCT IF(tApv.apv_type='MM', apv_item.apv_id, NULL)) AS pickMmSingCount, 
				COUNT(DISTINCT CONCAT(tApv.task_no,apv_item.sku)) AS pickSkuCount,
				SUM(IF(tApv.apv_type='SS', apv_item.pick_quantity, 0)) AS pickSsPcsCount,
				SUM(IF(tApv.apv_type='SM', apv_item.pick_quantity, 0))AS pickSmPcsCount,
				SUM(IF(tApv.apv_type='MM', apv_item.pick_quantity, 0)) AS pickMmPcsCount 
				
				<[:pick_date]>
			FROM 
				(
					SELECT 
						apv.id,
						apv.apv_no,
						task.task_no, 
						apv.apv_type,
						task.receive_person,
						task.picking_end_date 
					FROM 
						wh_picking_task task JOIN wh_picking_task_item task_item ON task.id=task_item.task_id JOIN wh_apv apv ON task_item.apv_id=apv.id 
					WHERE 
						1=1
						AND apv.status IN (:apv_status_condition) 
						AND task.task_status=2 
						AND task.task_type=4 
						<[AND task.receive_person IN (:user_id_list)]>
						<[AND task.picking_end_date >= :start_pick_time]>
						<[AND task.picking_end_date <= :end_pick_time]>
				) tApv
			LEFT JOIN wh_apv_item apv_item ON apv_item.apv_id=tApv.id 
			JOIN t_user t ON tApv.receive_person=t.user_id  
			WHERE
				1=1
				AND apv_item.pick_quantity > 0
			<[:group_by_pick_date]>
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="querySoftCheckoutStatisticsList">
		<content>
      <![CDATA[
        	SELECT 
				track.sow_user AS userId,
				t.username AS userName,
        		t.name AS name,
				COUNT(DISTINCT IF(apv.ship_status NOT IN (22,23),  apv.apv_no, NULL)) AS softSingCount,
				COUNT(IF(apv.ship_status NOT IN (22,23), item.sku, NULL)) AS softSkuCount,
				SUM(IF(apv.ship_status NOT IN (22,23), item.sale_quantity, 0)) AS softPcsCount,
				COUNT(DISTINCT IF(apv.ship_status IN (22,23),  apv.apv_no, NULL)) AS softJitSingCount,
				COUNT(IF(apv.ship_status IN (22,23), item.sku, NULL)) AS softJitSkuCount,
				SUM(IF(apv.ship_status IN (22,23), item.sale_quantity, 0)) AS softJitPcsCount
				<[:soft_date]>
			FROM 
				wh_apv_item item
				JOIN wh_apv apv ON item.apv_id=apv.id 
				JOIN apv_track track ON track.apv_no=apv.apv_no
				JOIN t_user t ON track.sow_user=t.user_id
			WHERE 
				1=1
				<[AND apv.status IN (:apv_status_condition)]>
				AND track.sow_finish_time IS NOT NULL 
				<[AND track.sow_user IN (:user_id_list)]>
				<[AND track.sow_finish_time >= :start_soft_time]>
        		<[AND track.sow_finish_time <= :end_soft_time]>
        		<[AND ((pick.task_type in (3,4,9,12) AND item.salesperson=:warehouse_id) OR (pick.task_type in (1,2,5,6,7,8,10,11,13,14,15,59,60,61,62)))]>
			<[:group_by_soft_date]>
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryTransferSoftCheckoutStatisticList">
		<content>
		<![CDATA[
        	SELECT
				item.grid_by AS userId,
				t.username AS userName,
        		t.name AS name,
				COUNT(DISTINCT(fba.fba_no)) AS transferSoftSingCount,
				COUNT(item.product_sku) AS transferSoftSkuCount,
				SUM(item.grid_quantity) AS transferSoftPcsCount,
				DATE_FORMAT(item.grid_time, '%H') AS softDate,
				0 AS softSingCount,
				0 AS softSkuCount,
				0 AS softPcsCount,
				0 AS softJitSingCount,
				0 AS softJitSkuCount,
				0 AS softJitPcsCount
			FROM
				wh_fba_allocation_item item
				JOIN wh_fba_allocation fba ON item.fba_id=fba.id
				JOIN t_user t ON item.grid_by=t.user_id
			WHERE
				1=1
				<[AND fba.status IN (:transfer_status_condition)]>
				AND item.grid_time IS NOT NULL
				<[AND item.grid_time >= :start_soft_time]>
        		<[AND item.grid_time <= :end_soft_time]>
        		<[AND fba.purpose_house IN (:purposeHouseList)]>
        		<[:TRANSFER_CONDITION]>
			GROUP BY softDate
      	]]>
		</content>
	</sql>


	<sql datasource="dataSource" id="queryFbaPackingCheckoutStatistics">
		<content>
			<![CDATA[
        	SELECT
				fba.box_push_by AS userId,
				t.username AS userName,
        		t.name AS name,
				COUNT(DISTINCT(fba.fba_no)) AS asnPackingSingCount,
				COUNT( DISTINCT CASE WHEN fba.apv_type = 'SS' THEN fba.fba_no ELSE NULL END ) AS packingTransferSsCount,
				COUNT( DISTINCT CASE WHEN fba.apv_type = 'SM' THEN fba.fba_no ELSE NULL END ) AS packingTransferSmCount,
				COUNT( DISTINCT CASE WHEN fba.apv_type = 'MM' THEN fba.fba_no ELSE NULL END ) AS packingTransferMmCount,
				DATE_FORMAT(fba.box_push_time, '%H') AS packingDate
			FROM
				wh_fba_allocation fba
				JOIN t_user t ON fba.box_push_by=t.user_id
			WHERE
				1=1
				<[AND fba.status IN (:transfer_status_condition)]>
				AND fba.box_push_time IS NOT NULL
				<[AND fba.box_push_time >= :start_soft_time]>
        		<[AND fba.box_push_time <= :end_soft_time]>
        		<[AND fba.purpose_house IN (:transferPurposeHouseList)]>
        		<[AND fba.purpose_house NOT IN (:purposeHouseNotInList)]>
        		<[AND fba.purpose_house IN (:asnPurposeHouseList)]>
        		<[:TRANSFER_CONDITION]>
        		<[:ASN_TRANSFER_ORDER_FILTER]>
			GROUP BY packingDate
      	]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryPddPackingCheckoutStatistics">
		<content>
			<![CDATA[
        	SELECT
				tpoi.grid_by AS userId,
				t.username AS userName,
        		t.name AS name,
				COUNT(DISTINCT(tpoi.package_sn)) AS asnPackingSingCount,
				COUNT( DISTINCT CASE WHEN tpoi.real_quantity = 1 THEN tpoi.package_sn ELSE NULL END ) AS packingTransferSsCount,
				COUNT( DISTINCT CASE WHEN tpoi.real_quantity > 1 THEN tpoi.package_sn ELSE NULL END ) AS packingTransferSmCount,
				0 AS packingTransferMmCount,
				DATE_FORMAT(tpoi.grid_time, '%H') AS packingDate
			FROM
				temu_prepare_order_item tpoi
				JOIN t_user t ON tpoi.grid_by=t.user_id
			WHERE
				1=1
				<[AND tpoi.package_status IN (:pdd_status_condition)]>
				AND tpoi.grid_time IS NOT NULL
				<[AND tpoi.grid_time >= :start_soft_time]>
        		<[AND tpoi.grid_time <= :end_soft_time]>
			GROUP BY packingDate
      	]]>
		</content>
	</sql>


	<sql datasource="dataSource" id="queryAsnSoftCheckoutStatistics">
		<content>
			<![CDATA[
				SELECT
					wfa.sow_user AS userId,
					t.username AS userName,
					t.name AS name,
					COUNT(DISTINCT wfa.fba_no) AS softSingCount,
					COUNT(DISTINCT wfa.product_sku) AS softSkuCount,
					SUM(wfa.pick_quantity) AS softPcsCount
					<[:soft_date]>
				FROM
					(
					SELECT
						t.sow_user,
						f.fba_no,
						i.product_sku,
						i.pick_quantity,
						f.deliver_time
					FROM
						wh_fba_allocation_item i
						JOIN wh_fba_allocation f ON i.fba_id = f.id
						JOIN apv_track t ON t.apv_no = f.fba_no
					WHERE
						1 = 1
						AND t.sow_finish_time IS NOT NULL
						<[AND f.status IN (:apv_status_condition)]>
						<[AND t.sow_user IN (:user_id_list)]>
						<[AND f.purpose_house IN (:purposeHouseInList)]>
              			<[AND f.purpose_house NOT IN (:purposeHouseNotInList)]>
						<[AND f.deliver_time >= :start_soft_time]>
        				<[AND f.deliver_time <= :end_soft_time]>
        				<[:TRANSFER_CONDITION]>
					GROUP BY
						f.id,i.fn_sku
					) wfa
					JOIN t_user t ON wfa.sow_user = t.user_id
				<[:group_by_soft_date]>
     		]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryTemuSoftCheckoutStatistics">
		<content>
			<![CDATA[
				SELECT
					wfa.sow_user AS userId,
					t.username AS userName,
					t.name AS name,
					COUNT(DISTINCT wfa.fba_no) AS softSingCount,
					COUNT(DISTINCT wfa.sku) AS softSkuCount,
					SUM(wfa.grid_quantity) AS softPcsCount,
					COUNT(DISTINCT IF(real_quantity = 1,wfa.fba_no,NULL)) AS softSingSsCount,
					COUNT(DISTINCT IF(real_quantity = 1,wfa.sku,NULL)) AS softSkuSsCount,
					SUM(IF(real_quantity = 1,wfa.grid_quantity,0)) AS softPcsSsCount,
					COUNT(DISTINCT IF(real_quantity > 1,wfa.fba_no,NULL)) AS softSingSmCount,
					COUNT(DISTINCT IF(real_quantity > 1,wfa.sku,NULL)) AS softSkuSmCount,
					SUM(IF(real_quantity > 1,wfa.grid_quantity,0)) AS softPcsSmCount
					<[:temu_packing_date]>
				FROM
					(
					SELECT
						i.grid_by as sow_user,
						f.prepare_order_no as fba_no,
						i.sku,
						i.grid_quantity,
						i.real_quantity,
						i.grid_time
					FROM
						temu_prepare_order_item i
						JOIN temu_prepare_order f ON i.prepare_order_id = f.id
					WHERE
						1 = 1
						AND i.grid_time IS NOT NULL
						<[AND i.grid_by IN (:user_id_list)]>
						<[AND i.grid_time >= :start_packing_time]>
        				<[AND i.grid_time <= :end_packing_time]>
					GROUP BY
						f.id,i.sku
					) wfa
					JOIN t_user t ON wfa.sow_user = t.user_id
				<[:temu_group_by_packing_date]>
     		]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryTransferSoftCheckoutStatistics">
		<content>
			<![CDATA[
				SELECT
					wfa.sow_user AS userId,
					t.username AS userName,
					t.name AS name,
					COUNT(DISTINCT wfa.fba_no) AS softSingCount,
					COUNT(DISTINCT wfa.product_sku) AS softSkuCount,
					SUM(wfa.pick_quantity) AS softPcsCount
					<[:soft_date]>
				FROM
					(
						SELECT
							i.grid_by AS sow_user,
							f.fba_no AS fba_no,
							i.product_sku AS product_sku,
							i.pick_quantity AS pick_quantity,
							f.deliver_time AS deliver_time
						FROM
							wh_fba_allocation_item i
							JOIN wh_fba_allocation f ON i.fba_id = f.id
						WHERE
							1 = 1
							AND i.grid_time IS NOT NULL
							<[AND f.status IN (:apv_status_condition)]>
							<[AND i.grid_by IN (:user_id_list)]>
							<[AND f.purpose_house IN (:purposeHouseInList)]>
							<[AND f.purpose_house NOT IN (:purposeHouseNotInList)]>
							<[AND f.deliver_time >= :start_soft_time]>
							<[AND f.deliver_time <= :end_soft_time]>
							<[:TRANSFER_CONDITION]>
						GROUP BY
							f.id,i.fn_sku
					) wfa
					JOIN t_user t ON wfa.sow_user = t.user_id
				<[:group_by_soft_date]>
     		]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryWarehouseSoftCheckoutStatistics">
		<content>
			<![CDATA[
				SELECT
					wfa.sow_user AS userId,
					t.username AS userName,
					t.name AS name,
					COUNT(DISTINCT wfa.fba_no) AS warehouseSoftGridSingCount,
					COUNT(DISTINCT wfa.product_sku) AS warehouseSoftGridSkuCount,
					SUM(wfa.grid_quantity) AS warehouseSoftGridPcsCount
					<[:soft_date]>
				FROM
					(
					SELECT
						i.grid_by as sow_user,
						f.fba_no,
						i.product_sku,
						i.grid_quantity,
						i.grid_time AS deliver_time
					FROM
						wh_fba_allocation_item i
						JOIN wh_fba_allocation f ON i.fba_id = f.id
					WHERE
						1 = 1
						AND i.grid_time IS NOT NULL
						AND f.purpose_house = 'SMT'
						<[AND f.status IN (:apv_status_condition)]>
						<[AND i.grid_by IN (:user_id_list)]>
						<[AND i.grid_time >= :start_soft_time]>
        				<[AND i.grid_time <= :end_soft_time]>
        				AND f.id IN (
							SELECT wh_asn_id
							FROM wh_asn_extra ae
							WHERE (f.purpose_house, ae.package_method) IN (('Shein', 8), ('Shein', 9), ('SMT', 4), ('SMT', 7), ('SMT', 5), ('SMT', 6))
								OR ae.package_method IS NULL)
					GROUP BY
						f.id,i.fn_sku,i.product_sku
					) wfa
					JOIN t_user t ON wfa.sow_user = t.user_id
				<[:group_by_soft_date]>
     		]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryAsnSoftOverseasCheckoutStatistics">
		<content>
			<![CDATA[
				SELECT
					wfa.sow_user AS userId,
					t.username AS userName,
					t.name AS name,
					COUNT(DISTINCT wfa.fba_no) AS asnSoftOverseasSingCount,
					COUNT(DISTINCT wfa.product_sku) AS asnSoftOverseasSkuCount,
					SUM(wfa.pick_quantity) AS asnSoftOverseasPcsCount
					<[:soft_date]>
				FROM
					(
					SELECT
						t.sow_user,
						f.fba_no,
						i.product_sku,
						i.pick_quantity,
						f.deliver_time
					FROM
						wh_fba_allocation_item i
						JOIN wh_fba_allocation f ON i.fba_id = f.id
						JOIN apv_track t ON t.apv_no = f.fba_no
					WHERE
						1 = 1
						AND t.sow_finish_time IS NOT NULL
						AND f.purpose_house != 'SMT'
						AND f.id IN (
							SELECT wh_asn_id
							FROM wh_asn_extra ae
							WHERE (f.purpose_house, ae.package_method) NOT IN (('Shein', 8), ('Shein', 9))
								OR ae.package_method IS NULL)
						<[AND f.status IN (:apv_status_condition)]>
						<[AND t.sow_user IN (:user_id_list)]>
						<[AND f.purpose_house IN (:purposeHouseInList)]>
						<[AND f.deliver_time >= :start_soft_time]>
        				<[AND f.deliver_time <= :end_soft_time]>
					GROUP BY
						f.id,i.fn_sku
					) wfa
					JOIN t_user t ON wfa.sow_user = t.user_id
				<[:group_by_soft_date]>
     		]]>
		</content>
	</sql>

	<!--包含直发非GPSR订单。不含GPSR、JIT等数据-->
	<sql datasource="dataSource" id="queryPackingCheckoutStatisticsList">
		<content>
      <![CDATA[
        	SELECT 
				track.pack_user AS userId,
				t.username AS userName,
        		t.name AS name,
				COUNT( DISTINCT CASE WHEN apv.apv_type = 'SS' THEN apv.apv_no ELSE NULL END ) AS packingSsSingCount,
				COUNT( DISTINCT CASE WHEN apv.apv_type = 'SM' THEN apv.apv_no ELSE NULL END ) AS packingSmSingCount,
				COUNT( DISTINCT CASE WHEN apv.apv_type = 'MM' THEN apv.apv_no ELSE NULL END ) AS packingMmSingCount,
				SUM(CASE WHEN apv.apv_type ='SS' THEN item.sale_quantity ELSE 0 END ) AS packingSsPcsCount,
				SUM(CASE WHEN apv.apv_type ='SM' THEN item.sale_quantity ELSE 0 END ) AS packingSmPcsCount,
				SUM(CASE WHEN apv.apv_type ='MM' THEN item.sale_quantity ELSE 0 END ) AS packingMmPcsCount
				<[:packing_date]>
			FROM 
				wh_apv_item item
				JOIN wh_apv apv ON item.apv_id=apv.id 
				JOIN apv_track track ON track.apv_no=apv.apv_no 
				JOIN t_user t ON track.pack_user=t.user_id
			WHERE 
				1=1
				<[AND apv.status IN (:apv_status_condition)]>
				AND track.pack_finish_time IS NOT NULL
				AND apv.ship_status NOT IN (22,23)
				AND (INSTR(apv.buyer_checkout, 'GPSR') IS NULL OR INSTR(apv.buyer_checkout, 'GPSR') = 0)
				<[AND track.pack_user IN (:user_id_list)]>
				<[AND track.pack_finish_time >= :start_packing_time]>
        		<[AND track.pack_finish_time <= :end_packing_time]>
			<[:group_by_packing_date]>
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryJitPackingCheckoutStatistics">
		<content>
			<![CDATA[
        	SELECT
				track.pack_user AS userId,
				t.username AS userName,
        		t.name AS name,
				COUNT(DISTINCT CASE WHEN apv.apv_type = 'SS' THEN apv.apv_no ELSE NULL END) AS packingJitSsSingCount,
				COUNT(DISTINCT CASE WHEN apv.apv_type = 'SM' THEN apv.apv_no ELSE NULL END) AS packingJitSmSingCount,
				COUNT(DISTINCT CASE WHEN apv.apv_type = 'MM' THEN apv.apv_no ELSE NULL END) AS packingJitMmSingCount,
				SUM(CASE WHEN apv.apv_type ='SS' THEN item.sale_quantity ELSE 0 END ) AS packingJitSsPcsCount,
				SUM(CASE WHEN apv.apv_type ='SM' THEN item.sale_quantity ELSE 0 END ) AS packingJitSmPcsCount,
				SUM(CASE WHEN apv.apv_type ='MM' THEN item.sale_quantity ELSE 0 END ) AS packingJitMmPcsCount

				<[:packing_date]>
			FROM
				wh_apv_item item
				JOIN wh_apv apv ON item.apv_id=apv.id
				JOIN apv_track track ON track.apv_no=apv.apv_no
				JOIN t_user t ON track.pack_user=t.user_id
			WHERE
				1=1
				<[AND apv.status IN (:apv_status_condition)]>
				AND track.pack_finish_time IS NOT NULL
				AND apv.ship_status IN (22,23)
				<[AND track.pack_user IN (:user_id_list)]>
				<[AND track.pack_finish_time >= :start_packing_time]>
        		<[AND track.pack_finish_time <= :end_packing_time]>
			<[:group_by_packing_date]>
      ]]>
		</content>
	</sql>


	<sql datasource="dataSource" id="queryDirectPackingCheckoutStatisticsList">
		<content>
			<![CDATA[
        	SELECT
				track.pack_user AS userId,
				t.username AS userName,
        		t.name AS name,
				COUNT(DISTINCT CASE WHEN apv.apv_type = 'SS' THEN apv.apv_no ELSE NULL END) AS directPackingSsSingCount,
				COUNT(DISTINCT CASE WHEN apv.apv_type = 'SM' THEN apv.apv_no ELSE NULL END) AS directPackingSmSingCount,
				COUNT(DISTINCT CASE WHEN apv.apv_type = 'MM' THEN apv.apv_no ELSE NULL END) AS directPackingMmSingCount,
				SUM(CASE WHEN apv.apv_type ='SS' THEN item.sale_quantity ELSE 0 END ) AS directPackingSsPcsCount,
				SUM(CASE WHEN apv.apv_type ='SM' THEN item.sale_quantity ELSE 0 END ) AS directPackingSmPcsCount,
				SUM(CASE WHEN apv.apv_type ='MM' THEN item.sale_quantity ELSE 0 END ) AS directPackingMmPcsCount
				<[:packing_date]>
			FROM
				wh_apv_item item
				JOIN wh_apv apv ON item.apv_id=apv.id
				JOIN apv_track track ON track.apv_no=apv.apv_no
				JOIN t_user t ON track.pack_user=t.user_id
			WHERE
				1=1
				AND track.pack_finish_time IS NOT NULL
				AND apv.ship_status = 21
				<[AND apv.status IN (:apv_status_condition)]>
				<[AND track.pack_user IN (:user_id_list)]>
				<[AND track.pack_finish_time >= :start_packing_time]>
        		<[AND track.pack_finish_time <= :end_packing_time]>
			<[:group_by_packing_date]>
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryGpsrPackingCheckoutStatisticsList">
		<content>
			<![CDATA[
				SELECT
					track.pack_user AS userId,
					t.username AS userName,
					t.name AS name,
					COUNT(DISTINCT CASE WHEN apv.apv_type ='SS' THEN apv.apv_no ELSE NULL END ) AS gpsrPackingSsCount,
					COUNT(DISTINCT CASE WHEN apv.apv_type ='SM' THEN apv.apv_no ELSE NULL END ) AS gpsrPackingSmCount,
					COUNT(DISTINCT CASE WHEN apv.apv_type ='MM' THEN apv.apv_no ELSE NULL END ) AS gpsrPackingMmCount,
					SUM(CASE WHEN apv.apv_type ='SS' THEN item.sale_quantity ELSE 0 END ) AS gpsrPackingSsPcsCount,
					SUM(CASE WHEN apv.apv_type ='SM' THEN item.sale_quantity ELSE 0 END ) AS gpsrPackingSmPcsCount,
					SUM(CASE WHEN apv.apv_type ='MM' THEN item.sale_quantity ELSE 0 END ) AS gpsrPackingMmPcsCount
					<[:packing_date]>
				FROM
					wh_apv_item item
					JOIN wh_apv apv ON item.apv_id=apv.id
					LEFT JOIN apv_track track ON track.apv_no = apv.apv_no
					LEFT JOIN t_user t ON t.user_id = track.pack_user
				WHERE
					1=1
					AND track.pack_finish_time IS NOT NULL
					AND INSTR( apv.buyer_checkout , "GPSR" ) > 0
					<[AND apv.status IN (:apv_status_condition)]>
					<[AND track.pack_user IN (:user_id_list)]>
					<[AND track.pack_finish_time >= :start_packing_time]>
					<[AND track.pack_finish_time <= :end_packing_time]>
				<[:group_by_packing_date]>
      		]]>
		</content>
	</sql>


	<sql datasource="dataSource" id="queryAsnPackingCheckoutStatisticsList">
		<content>
			<![CDATA[
        	select
				track.pack_user as userId,
				u.username as userName,
				u.name,
				COUNT(DISTINCT CASE WHEN fa.apv_type = 'SS' AND item.tag NOT REGEXP 'POLYESTER_OTHER|COTTON100|SILK100' THEN fa.fba_no ELSE NULL END) AS asnPackingSsCount,
				COUNT(DISTINCT CASE WHEN fa.apv_type = 'SM' AND item.tag NOT REGEXP 'POLYESTER_OTHER|COTTON100|SILK100' THEN fa.fba_no ELSE NULL END) AS asnPackingSmCount,
				COUNT(DISTINCT CASE WHEN fa.apv_type = 'MM' AND item.tag NOT REGEXP 'POLYESTER_OTHER|COTTON100|SILK100' THEN fa.fba_no ELSE NULL END) AS asnPackingMmCount,
				COUNT(DISTINCT CASE WHEN fa.apv_type = 'SS' AND item.tag REGEXP 'POLYESTER_OTHER|COTTON100|SILK100' THEN fa.fba_no ELSE NULL END) AS asnPackingSsWashingCount,
				COUNT(DISTINCT CASE WHEN fa.apv_type = 'SM' AND item.tag REGEXP 'POLYESTER_OTHER|COTTON100|SILK100' THEN fa.fba_no ELSE NULL END) AS asnPackingSmWashingCount,
				COUNT(DISTINCT CASE WHEN fa.apv_type = 'MM' AND item.tag REGEXP 'POLYESTER_OTHER|COTTON100|SILK100' THEN fa.fba_no ELSE NULL END) AS asnPackingMmWashingCount,
				SUM(CASE WHEN fa.apv_type ='SS' AND item.tag NOT REGEXP 'POLYESTER_OTHER|COTTON100|SILK100' THEN item.quantity ELSE 0 END ) AS asnPackingSsPcsCount,
				SUM(CASE WHEN fa.apv_type ='SM' AND item.tag NOT REGEXP 'POLYESTER_OTHER|COTTON100|SILK100' THEN item.quantity ELSE 0 END ) AS asnPackingSmPcsCount,
				SUM(CASE WHEN fa.apv_type ='MM' AND item.tag NOT REGEXP 'POLYESTER_OTHER|COTTON100|SILK100' THEN item.quantity ELSE 0 END ) AS asnPackingMmPcsCount,
				SUM(CASE WHEN fa.apv_type ='SS' AND item.tag REGEXP 'POLYESTER_OTHER|COTTON100|SILK100' THEN item.quantity ELSE 0 END ) AS asnPackingSsWashingPcsCount,
				SUM(CASE WHEN fa.apv_type ='SM' AND item.tag REGEXP 'POLYESTER_OTHER|COTTON100|SILK100' THEN item.quantity ELSE 0 END ) AS asnPackingSmWashingPcsCount,
				SUM(CASE WHEN fa.apv_type ='MM' AND item.tag REGEXP 'POLYESTER_OTHER|COTTON100|SILK100' THEN item.quantity ELSE 0 END ) AS asnPackingMmWashingPcsCount
				<[:packing_date]>
			FROM wh_fba_allocation fa
			LEFT JOIN wh_fba_allocation_item item on item.fba_id = fa.id
			LEFT JOIN apv_track track ON track.apv_no = fa.fba_no
			LEFT JOIN t_user u ON u.user_id = track.pack_user
			WHERE
				fa.purpose_house in ('SMT','Shein')
				and fa.id in (SELECT ae.wh_asn_id FROM wh_asn_extra ae
					WHERE (fa.purpose_house,ae.package_method) in (('Shein', 8), ('Shein', 9)))
				<[AND fa.status IN (:fba_status_condition)]>
				<[AND track.pack_finish_time >= :start_packing_time]>
				<[AND track.pack_finish_time <= :end_packing_time]>
				<[AND track.pack_user in (:user_id_list)]>
			<[:group_by_packing_date]>
      		]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryWarehousePackingCheckoutStatistics">
		<content>
			<![CDATA[
        	select
				track.pack_user as userId,
				u.username as userName,
				u.name,
				COUNT(DISTINCT CASE WHEN fa.apv_type = 'SS' THEN fa.fba_no ELSE NULL END) AS warehousePackingSsCount,
				COUNT(DISTINCT CASE WHEN fa.apv_type = 'SM' THEN fa.fba_no ELSE NULL END) AS warehousePackingSmCount,
				COUNT(DISTINCT CASE WHEN fa.apv_type = 'MM' THEN fa.fba_no ELSE NULL END) AS warehousePackingMmCount,
				SUM(CASE WHEN fa.apv_type = 'SS' THEN item.loading_quantity ELSE 0 END ) AS warehousePackingSsPcsCount,
				SUM(CASE WHEN fa.apv_type = 'SM' THEN item.loading_quantity ELSE 0 END ) AS warehousePackingSmPcsCount,
				SUM(CASE WHEN fa.apv_type ='MM' THEN item.loading_quantity ELSE 0 END ) AS warehousePackingMmPcsCount
				<[:packing_date]>
			FROM wh_fba_allocation fa
			LEFT JOIN wh_fba_allocation_item item on item.fba_id = fa.id
			LEFT JOIN apv_track track ON track.apv_no = fa.fba_no
			LEFT JOIN t_user u ON u.user_id = track.pack_user
			WHERE
				fa.purpose_house in ('SMT')
				and fa.id in (SELECT ae.wh_asn_id FROM wh_asn_extra ae
					WHERE (fa.purpose_house,ae.package_method) in (('SMT', 4), ('SMT', 7), ('Shein', 8), ('Shein', 9),('SMT', 5), ('SMT', 6)) OR ae.package_method IS NULL)
				<[AND fa.status IN (:fba_status_condition)]>
				<[AND track.pack_finish_time >= :start_packing_time]>
				<[AND track.pack_finish_time <= :end_packing_time]>
				<[AND track.pack_user in (:user_id_list)]>
			<[:group_by_packing_date]>
      		]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryAsnFirstPackingCheckoutStatistics">
		<content>
			<![CDATA[
				select
					track.pack_user as userId,
					u.username as userName,
					u.name,
					COUNT(DISTINCT fa.fba_no) AS asnFirstPackCount,
					SUM(IFNULL(item.loading_quantity,0)) AS asnFirstPackPcsCount
					<[:packing_date]>
				FROM wh_fba_allocation fa
				LEFT JOIN wh_fba_allocation_item item on item.fba_id = fa.id
				LEFT JOIN apv_track track ON track.apv_no = fa.fba_no
				LEFT JOIN t_user u ON u.user_id = track.pack_user
				WHERE 1=1
					<[AND fa.status IN (:fba_status_condition)]>
					<[AND track.pack_finish_time >= :start_packing_time]>
					<[AND track.pack_finish_time <= :end_packing_time]>
					<[AND track.pack_user in (:user_id_list)]>
					<[AND fa.purpose_house IN (:asnPurposeHouseList)]>
					AND fa.id IN (SELECT wh_asn_id FROM wh_asn_extra ae WHERE (fa.purpose_house,ae.package_method) NOT IN (('SMT',4),('SMT',7),('Shein',8),('Shein',9)) OR ae.package_method IS NULL )
				<[:group_by_packing_date]>
      		]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryScanCheckoutStatisticsList">
		<content>
      <![CDATA[
        	SELECT 
				track.deliver_user AS userId,
				t.username AS userName,
        		t.name AS name,
				COUNT(DISTINCT IF(apv.ship_status != 1, apv.apv_no, NULL)) AS scanSingCount,
				SUM(IF(apv.ship_status != 1,item.sale_quantity,0) ) AS scanSingPcsCount,
				COUNT(DISTINCT IF(apv.ship_status = 1, apv.apv_no, NULL)) AS securityCheckScanSingCount,
				SUM(IF(apv.ship_status = 1,item.sale_quantity,0) ) AS securityCheckScanSingPcsCount
				<[:scan_date]>
			FROM
				wh_apv apv
				JOIN apv_track track ON track.apv_no=apv.apv_no 
				JOIN t_user t ON track.deliver_user=t.user_id
				LEFT JOIN wh_apv_item item ON item.apv_id = apv.id
			WHERE 
				1=1
				<[AND apv.status IN (:apv_status_condition)]>
				AND track.deliver_time IS NOT NULL 
				<[AND track.deliver_user IN (:user_id_list)]>
				<[AND track.deliver_time >= :start_scan_time]>
        		<[AND track.deliver_time <= :end_scan_time]>
			<[:group_by_scan_date]>
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryScanCheckoutPanelStatistics">
		<content>
			<![CDATA[
        	SELECT
				track.deliver_user AS userId,
				t.username AS userName,
        		t.name AS name,
				COUNT(DISTINCT IF(apv.ship_status not in (1,22,23), apv.apv_no, NULL)) AS scanSingCount,
				COUNT(DISTINCT IF(apv.ship_status in (22,23), apv.apv_no, NULL)) AS scanJitOrderCount
				<[:scan_date]>
			FROM
				wh_apv apv
				JOIN apv_track track ON track.apv_no=apv.apv_no
				JOIN t_user t ON track.deliver_user=t.user_id
				LEFT JOIN wh_apv_item item ON item.apv_id = apv.id
			WHERE
				1=1
				<[AND apv.status IN (:apv_status_condition)]>
				AND track.deliver_time IS NOT NULL
				<[AND track.deliver_user IN (:user_id_list)]>
				<[AND track.deliver_time >= :start_scan_time]>
        		<[AND track.deliver_time <= :end_scan_time]>
			<[:group_by_scan_date]>
      ]]>
		</content>
	</sql>

    <sql datasource="dataSource" id="queryScanTransferCheckoutStatistics">
        <content>
            <![CDATA[
        	SELECT
				fa.deliver_by AS userId,
				t.username AS userName,
        		t.name AS name,
				COUNT(DISTINCT fa.id) AS transferOrderCount,
				SUM(fai.loading_quantity) AS transferOrderPcsCount
				<[:scan_date]>
			FROM wh_fba_allocation fa
	             INNER JOIN wh_fba_allocation_item fai ON fai.fba_id = fa.id
	             INNER JOIN wh_asn_extra ae ON ae.wh_asn_id = fa.id AND ae.type != 1
	             INNER JOIN t_user t ON fa.deliver_by=t.user_id
			WHERE
				1=1
				<[AND fa.status IN (:apv_status_condition)]>
				AND fa.deliver_time IS NOT NULL
				<[AND fa.purpose_house IN (:purposeHouseList)]>
				<[:TRANSFER_ORDER_FILTER]>
				<[AND fa.deliver_by IN (:user_id_list)]>
				<[AND fa.deliver_time >= :start_scan_time]>
        		<[AND fa.deliver_time <= :end_scan_time]>
			<[:group_by_scan_date]>
      ]]>
        </content>
    </sql>

	<sql datasource="dataSource" id="queryScanWarehouseCheckoutStatistics">
		<content>
			<![CDATA[
        	SELECT
				fa.deliver_by AS userId,
				t.username AS userName,
        		t.name AS name,
				COUNT(DISTINCT fa.id) AS warehouseOrderCount,
				SUM(fai.loading_quantity) AS warehouseOrderPcsCount
				<[:scan_date]>
			FROM wh_fba_allocation fa
	             INNER JOIN wh_fba_allocation_item fai ON fai.fba_id = fa.id
	             INNER JOIN wh_asn_extra ae ON ae.wh_asn_id = fa.id AND ae.type != 1
	             INNER JOIN t_user t ON fa.deliver_by=t.user_id
			WHERE
				1=1
				<[AND fa.status IN (:apv_status_condition)]>
				AND fa.deliver_time IS NOT NULL
				AND fa.purpose_house = 'SMT'
				AND fa.id in (SELECT ae.wh_asn_id FROM wh_asn_extra ae
					WHERE (fa.purpose_house,ae.package_method) in (('SMT', 4), ('SMT', 7), ('Shein', 8), ('Shein', 9),('SMT', 5), ('SMT', 6)) OR ae.package_method IS NULL)
				<[AND fa.deliver_by IN (:user_id_list)]>
				<[AND fa.deliver_time >= :start_scan_time]>
        		<[AND fa.deliver_time <= :end_scan_time]>
			<[:group_by_scan_date]>
      ]]>
		</content>
	</sql>

    <sql datasource="dataSource" id="queryScanTemuCheckoutStatistics">
        <content>
            <![CDATA[
        	SELECT
					i.deliver_by AS userId,
					t.username AS userName,
					t.name AS name,
					COUNT(i.package_sn) AS temuPackageCount,
					SUM(i.grid_quantity) AS temuPackagePcsCount
					<[:scan_date]>
				FROM temu_prepare_order_item i
				    INNER JOIN t_user t ON i.deliver_by = t.user_id
				WHERE
                    1 = 1
                    AND i.deliver_time IS NOT NULL
                    <[AND i.package_status IN (:apv_status_condition)]>
                    <[AND i.deliver_by IN (:user_id_list)]>
                    <[AND i.deliver_time >= :start_soft_time]>
                    <[AND i.deliver_time <= :end_soft_time]>
				<[:group_by_scan_date]>
      ]]>
        </content>
    </sql>


	<sql datasource="dataSource" id="queryScanPackageCardStatistics">
		<content>
			<![CDATA[
        	SELECT
        			wss.scanner AS userId,
					t.username AS userName,
					t.name AS name,
					SUM(IF(wss.is_transfer_house = false AND wss.bag_no NOT LIKE 'SMTJIT%',1,0)) AS 'localPackageCardCount',
					SUM(IF(wss.is_transfer_house = false AND wss.bag_no LIKE 'SMTJIT%',1,0)) AS 'jitPackageCardCount',
					SUM(IF(wss.is_transfer_house = true AND wss.bag_no LIKE 'TEMU%',1,0)) AS 'temuPackageCardCount',
					SUM(IF(wss.is_transfer_house = true AND wss.bag_no LIKE 'SMTCF%',1,0)) AS 'cfPackageCardCount',
					SUM(IF(wss.is_transfer_house = true AND wss.bag_no NOT LIKE 'TEMU%' AND wss.bag_no NOT LIKE 'SMTCF%',1,0)) AS 'transferPackageCardCount'
					<[:scan_date]>
				FROM wh_scan_shipment wss
				    INNER JOIN t_user t ON wss.scanner = t.user_id
				WHERE
                    1 = 1
                    AND wss.scan_date IS NOT NULL
                    <[AND wss.scanner IN (:user_id_list)]>
                    <[AND wss.scan_date >= :start_scan_time]>
                    <[AND wss.scan_date <= :end_scan_time]>
				<[:group_by_scan_date]>
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryLoadingPackageCardStatistics">
		<content>
			<![CDATA[
        	SELECT
        			wss.scanner AS userId,
					t.username AS userName,
					t.name AS name,
					SUM(IF(wss.is_transfer_house = false,1,0)) AS 'localPackageCardCount',
					SUM(IF(wss.is_transfer_house = true AND wss.bag_no LIKE 'TEMU%',1,0)) AS 'temuPackageCardCount',
					SUM(IF(wss.is_transfer_house = true AND wss.bag_no NOT LIKE 'TEMU%',1,0)) AS 'transferPackageCardCount'
					<[:loading_date]>
				FROM wh_scan_shipment wss
				    INNER JOIN t_user t ON wss.scanner = t.user_id
				WHERE
                    1 = 1
                    AND wss.load_date IS NOT NULL
                    <[AND wss.load_user IN (:user_id_list)]>
                    <[AND wss.load_date >= :start_loading_time]>
                    <[AND wss.load_date <= :end_loading_time]>
				<[:group_by_loading_date]>
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="queryLoadingCheckoutStatisticsList">
		<content>
      <![CDATA[
        	SELECT 
				track.load_user AS userId,
				t.username AS userName,
        		t.name AS name,
				COUNT(apv.apv_no) AS loadingSingCount,
				SUM(apv.actual_weight) AS loadingWeight,
				COUNT(DISTINCT wss.bag_no) AS bagCount
				
				<[:loading_date]>
			FROM 
				wh_apv apv 
				JOIN apv_track track ON track.apv_no=apv.apv_no
				LEFT JOIN wh_scan_shipment_to_apv wssta ON apv.apv_no = wssta.apv_no
				LEFT JOIN wh_scan_shipment wss ON wss.id = wssta.scan_shipment_id
				JOIN t_user t ON track.load_user=t.user_id
			WHERE 
				1=1
				<[AND apv.status IN (:apv_status_condition)]>
				AND track.load_date IS NOT NULL 
				<[AND track.load_user IN (:user_id_list)]>
				<[AND track.load_date >= :start_loading_time]>
				<[AND track.load_date <= :end_loading_time]>
			<[:group_by_loading_date]>
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryWarehouseCheckoutStatistics">
		<content>
			<![CDATA[
        	SELECT
				wfa.load_by AS userId,
				t.username AS userName,
        		t.name AS name,
				COUNT(DISTINCT wfa.fba_no) AS warehouseLoadingSingCount,
			    SUM(wfa.product_weight)  AS warehouseLoadingWeight
				<[:warehouse_loading_date]>
			FROM
				(
					SELECT
						fa_item.load_by,
						fa.fba_no,
						fa_item.product_weight,
						fa_item.load_time
					FROM
						wh_fba_allocation_item fa_item
						JOIN wh_fba_allocation fa ON fa_item.fba_id = fa.id
					WHERE
						1 = 1
						AND fa.status = 18
						AND fa.purpose_house = 'SMT'
						AND fa.id in (SELECT ae.wh_asn_id FROM wh_asn_extra ae WHERE (fa.purpose_house,ae.package_method) in (('SMT', 4), ('SMT', 7), ('Shein', 8), ('Shein', 9),('SMT', 5), ('SMT', 6)) OR ae.package_method IS NULL)
						AND fa_item.load_time IS NOT NULL
						<[AND fa_item.load_by IN (:user_id_list)]>
						<[AND fa_item.load_time >= :start_loading_time]>
						<[AND fa_item.load_time <= :end_loading_time]>
					GROUP BY
						fa.id,fa_item.box_no
				) wfa
				JOIN t_user t ON wfa.load_by=t.user_id
			<[:warehouse_group_by_loading_date]>
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryTransferCheckoutStatistics">
		<content>
			<![CDATA[
        	SELECT
				ws.load_user AS userId,
				t.username AS userName,
        		t.name AS name,
				COUNT(fa.fba_no) AS loadingSingCount,
				SUM(fa_item.product_weight)  AS loadingWeight,
				DATE_FORMAT(ws.load_date, '%H') AS loadingDate
			FROM
				wh_fba_allocation fa
				LEFT JOIN wh_fba_allocation_item fa_item ON fa.id = fa_item.fba_id
				LEFT JOIN wh_scan_shipment_to_apv sta ON fa.fba_no = sta.apv_no
				LEFT JOIN wh_scan_shipment ws ON ws.id = sta.scan_shipment_id
				JOIN t_user t ON ws.load_user=t.user_id
			WHERE
				1=1
				<[AND fa.status IN (:transfer_status_condition)]>
				AND ws.load_date IS NOT NULL
				<[AND ws.load_date >= :start_loading_time]>
				<[AND ws.load_date <= :end_loading_time]>
				AND ws.is_transfer_house=1
			GROUP BY loadingDate
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryPddCheckoutStatistics">
		<content>
			<![CDATA[
        	SELECT
				ws.load_user AS userId,
				t.username AS userName,
        		t.name AS name,
				COUNT(tpoi.package_sn) AS loadingSingCount,
				IF (SUM(tpoi.actual_weight) IS NULL, 0, SUM(tpoi.actual_weight)/1000) AS loadingWeight,
				DATE_FORMAT(ws.load_date, '%H') AS loadingDate
			FROM
				temu_prepare_order_item tpoi
				LEFT JOIN wh_scan_shipment_to_apv sta ON tpoi.package_sn = sta.apv_no
				LEFT JOIN wh_scan_shipment ws ON ws.id = sta.scan_shipment_id
				JOIN t_user t ON ws.load_user=t.user_id
			WHERE
				1=1
				<[AND tpoi.package_status IN (:pdd_status_condition)]>
				AND ws.load_date IS NOT NULL
				<[AND ws.load_date >= :start_loading_time]>
				<[AND ws.load_date <= :end_loading_time]>
				AND ws.is_transfer_house=1
			GROUP BY loadingDate
      ]]>
		</content>
	</sql>


	
	<sql datasource="dataSource" id="queryAllocationPickCheckoutStatisticsList">
		<content>
      <![CDATA[
        	SELECT
				t.user_id AS userId,
				t.username AS userName,
        		t.name AS name,
				COUNT(DISTINCT pick_task_item.sku) as allocationSkuCount,
				SUM(IFNULL(pick_task_item.pick_quantity, 0)) AS allocationPcsCount
				<[:pick_date]>
			FROM wh_allocation_pick_task pick_task
			LEFT JOIN wh_allocation_pick_task_item pick_task_item ON pick_task_item.task_id=pick_task.task_id
			JOIN t_user t ON pick_task.update_by=t.user_id
			WHERE
				1=1
				<[AND pick_task.task_status = :status]>
				<[AND pick_task.update_by IN (:user_id_list)]>
				<[AND pick_task.update_time >= :start_pick_time]>
				<[AND pick_task.update_time <= :end_pick_time]>
				<[:group_by_pick_date]>
      ]]>
		</content>
	</sql>


	<sql datasource="dataSource" id="queryArossSoftCheckoutStatisticsList">
		<content>
			<![CDATA[
				SELECT
					t.user_id AS userId,
					t.username AS userName,
					t.name AS name,
					h.softTime AS softTime,
					h.allocationNo AS allocationNo,
					h.sku AS sku,
					h.softIdentify AS softIdentify
				FROM
				(
					SELECT
						demand_sku.sow_user AS userId,
						DATE_FORMAT(demand_sku.sow_time, '%Y-%m-%d') AS softTime,
						demand.allocation_no AS allocationNo,
						demand_sku.sku AS sku,
						CONCAT(demand.allocation_no,demand_sku.sku) AS softIdentify
					FROM wh_allocation_demand_sku demand_sku
					LEFT JOIN wh_allocation_demand demand ON demand.task_id = demand_sku.task_id
					WHERE 1=1
					<[AND demand_sku.sow_user IN (:user_id_list)]>
					<[AND demand_sku.sow_time >= :start_soft_time]>
					<[AND demand_sku.sow_time <= :end_soft_time]>
					AND demand.allocation_no IN (
						SELECT allocation.allocation_no
						FROM wh_apv_allocation allocation
						WHERE allocation.allocation_no = demand.allocation_no
						AND allocation.allocation_type = 2
						AND allocation.allocation_status IN (5, 6)
					)
				) h JOIN t_user t ON h.userId=t.user_id
				<[:group_by_soft_date]>
     		 ]]>
		</content>
	</sql>


	<sql datasource="dataSource" id="queryDemandApvItemList">
		<content>
      <![CDATA[
        	SELECT
				demand.allocation_no,
				demand_item.apv_no,
				demand_item.apv_item,
				demand_item.apv_data_json
			FROM
				(
					SELECT task_id,is_push,apv_no,apv_item, apv_data_json FROM wh_allocation_demand_item WHERE is_push=1 AND id IN (SELECT MAX(id) FROM wh_allocation_demand_item GROUP BY apv_no)
				) demand_item
				LEFT JOIN wh_allocation_demand demand ON demand.task_id=demand_item.task_id
				<[:SOFT_QUERY_SQL]>
			WHERE 1 = 1
			<[:ALLOCATION_STATUS_SQL]>
			<[AND demand.task_status = :task_status]>
			<[AND demand.allocation_no IN (:allocation_no_list)]>
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryFbaFnSkuTagStatisticsList">
		<content>
			<![CDATA[
				SELECT
					t.user_id AS userId, t.username AS userName, t.name AS name
					, COUNT(DISTINCT IF(wfai.process_type = 3, CONCAT(wfai.fba_id,wfai.fn_sku), NULL)) AS ptFnSkuCount
					, COUNT(DISTINCT IF(wfai.process_type = 5, CONCAT(wfai.fba_id,wfai.fn_sku), NULL)) AS pltFnSkuCount
					, COUNT(DISTINCT IF(wfai.process_type = 7, CONCAT(wfai.fba_id,wfai.fn_sku), NULL)) AS pwtFnSkuCount
					, SUM( IF(wfai.process_type = 3, wfai.grid_quantity, 0)) AS ptFnSkuPcsCount
					, SUM( IF(wfai.process_type = 5, wfai.grid_quantity, 0)) AS pltFnSkuPcsCount
					, SUM( IF(wfai.process_type = 7, wfai.grid_quantity, 0)) AS pwtFnSkuPcsCount
					<[:tag_date]>
				FROM
					(
					SELECT
						fba_id,fn_sku,product_sku,process_type,floor(MIN(grid_quantity)/IFNULL(sku_suit_num,1)) AS grid_quantity,tag_by,tag_time
					FROM
						wh_fba_allocation_item
					WHERE
						tag_by IS NOT NULL AND tag_time IS NOT NULL
						<[AND tag_by IN (:user_id_list)]>
						<[AND tag_time >= :start_handle_time]>
						<[AND tag_time <= :end_handle_time]>
					GROUP BY fba_id,fn_sku
					) wfai
					JOIN t_user t ON wfai.tag_by = t.user_id
				WHERE
					1 = 1
				<[:group_by_tag_date]>
              ]]>
		</content>
	</sql>
	<sql datasource="dataSource" id="queryFbaSkuTagStatistics">
		<content>
			<![CDATA[
				SELECT
					t.user_id AS userId, t.username AS userName, t.name AS name
					, COUNT(DISTINCT IF(wfai.process_type = 3, CONCAT(wfai.product_sku), NULL)) AS ptSkuCount
					, COUNT(DISTINCT IF(wfai.process_type = 5, CONCAT(wfai.product_sku), NULL)) AS pltSkuCount
					, COUNT(DISTINCT IF(wfai.process_type = 7, CONCAT(wfai.product_sku), NULL)) AS pwtSkuCount
					, SUM( IF(wfai.process_type = 3, wfai.grid_quantity, 0)) AS ptPcsCount
					, SUM( IF(wfai.process_type = 5, wfai.grid_quantity, 0)) AS pltPcsCount
					, SUM( IF(wfai.process_type = 7, wfai.grid_quantity, 0)) AS pwtPcsCount
					<[:tag_date]>
				FROM
					(
					SELECT
						fba_id,fn_sku,product_sku,process_type,grid_quantity,tag_by,tag_time
					FROM
						wh_fba_allocation_item
					WHERE
						tag_by IS NOT NULL AND tag_time IS NOT NULL
						<[AND tag_by IN (:user_id_list)]>
						<[AND tag_time >= :start_handle_time]>
						<[AND tag_time <= :end_handle_time]>
					GROUP BY fba_id,fn_sku,product_sku
					) wfai
					JOIN t_user t ON wfai.tag_by = t.user_id
				WHERE
					1 = 1
				<[:group_by_tag_date]>
              ]]>
		</content>
	</sql>


	<sql datasource="dataSource" id="queryJitPickStatistics">
		<content>
			<![CDATA[
				SELECT
					t.user_id AS userId, t.username AS userName, t.name AS name
					, COUNT(DISTINCT p.fba_id) AS jitPickOrderCount
					, COUNT(i.product_sku) AS jitPickSkuCount
					<[:sorting_date]>
				FROM
					jit_pick_box_item p
					JOIN wh_fba_allocation_item i ON i.fba_id = p.fba_id
					JOIN t_user t ON t.user_id = p.pick_by
				WHERE
					p.`status` = 2
					<[AND p.pick_by IN (:user_id_list)]>
					<[AND p.pick_time >= :start_handle_time]>
					<[AND p.pick_time <= :end_handle_time]>
					<[:group_by_sorting_date]>
              ]]>
		</content>
	</sql>
	<sql datasource="dataSource" id="queryPddPickStatistics">
		<content>
			<![CDATA[
				SELECT
					t.user_id AS userId, t.username AS userName, t.name AS name
					, COUNT(DISTINCT i.prepare_order_id) AS pddPickOrderCount
					, COUNT(i.sku) AS pddPickSkuCount
					<[:sorting_date]>
				FROM
					temu_pick_box_item p
					JOIN temu_prepare_order_item i ON i.id = p.relation_item_id
					JOIN t_user t ON t.user_id = p.pick_by
				WHERE 1=1
					<[AND p.pick_by IN (:user_id_list)]>
					<[AND p.pick_time >= :start_handle_time]>
					<[AND p.pick_time <= :end_handle_time]>
					<[:group_by_sorting_date]>
              ]]>
		</content>
	</sql>

</sqlmap>