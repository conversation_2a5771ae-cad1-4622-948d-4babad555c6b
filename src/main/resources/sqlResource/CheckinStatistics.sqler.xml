<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap>

	<sql datasource="dataSource" id="queryDeliveryCheckinStatisticsList">
		<content>
      <![CDATA[
        	SELECT 
        		purchase.created_by AS userId,
        		t.username AS userName,
        		t.name AS name,
        		SUM(purchase.weight) AS deliveryWeight,
				COUNT(purchase.tracking_number) AS deliveryOrderCount, 
				SUM(purchase.quantity) AS deliveryUnitCount
				
				<[:delivery_date]>
			FROM 
				wh_purchase_express_record purchase 
				JOIN t_user t ON purchase.created_by=t.user_id 
			WHERE 
				1=1
				<[AND purchase.created_by IN (:user_id_list)]>
				<[AND purchase.creation_date >= :start_handle_time]>
				<[AND purchase.creation_date <= :end_handle_time]>
				<[AND purchase.warehouse_id = :warehouse_id]>
			<[:group_by_delivery_date]>
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="queryCheckinStatisticsList">
		<content>
      <![CDATA[
        	SELECT 
				checkin.create_user AS userId,
				t.username AS userName,
				t.name AS name,
				COUNT(DISTINCT checkin.purchase_order_no) AS checkinPoOrderCount
				<[:checkin_date]>
			FROM
				wh_check_in_item item
				JOIN wh_check_in checkin ON item.in_id=checkin.in_id
				JOIN wh_sku whSku ON whSku.sku=item.sku 
				JOIN t_user t ON checkin.create_user=t.user_id 
			WHERE 1=1
				<[AND checkin.status = :status]>
				<[AND checkin.status != :NOT_STATUS]>
				<[AND checkin.create_user IN (:user_id_list)]>
				<[AND checkin.qc_time >= :start_handle_time]>
				<[AND checkin.qc_time <= :end_handle_time]>
				<[AND checkin.create_date >= :start_create_date]>
				<[AND checkin.create_date <= :end_create_date]>
			<[:group_by_checkin_date]>
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryCheckinAllocationList">
		<content>
			<![CDATA[
        	SELECT
				checkin.create_user AS userId,
				t.username AS userName,
				t.name AS name,
				COUNT(DISTINCT item.sku) AS checkinAllocationSkuCount,
				SUM(IFNULL(item.qc_quantity, 0)) AS checkinAllocationPcsCount
				<[:chekcin_allocation_date]>
			FROM
				wh_allocation_check_in_item item
				JOIN wh_allocation_check_in checkin ON item.in_id=checkin.in_id
				JOIN t_user t ON checkin.create_user=t.user_id
			WHERE 1=1
				<[AND checkin.status = :status]>
				<[AND checkin.status != :NOT_STATUS]>
				<[AND checkin.create_user IN (:user_id_list)]>
				<[AND checkin.create_date >= :start_handle_time]>
				<[AND checkin.create_date <= :end_handle_time]>
			<[:group_by_checkin_allocation_date]>
      ]]>
		</content>
	</sql>
	<sql datasource="dataSource" id="queryCheckQcAllocationList">
		<content>
			<![CDATA[
        	SELECT
				checkin.qc_user AS userId,
				t.username AS userName,
				t.name AS name,
				COUNT(DISTINCT item.sku) AS checkinAllocationSkuCount,
				SUM(IFNULL(item.qc_quantity, 0)) AS checkinAllocationPcsCount
				<[:chekcin_allocation_date]>
			FROM
				wh_allocation_check_in_item item
				JOIN wh_allocation_check_in checkin ON item.in_id=checkin.in_id
				JOIN t_user t ON checkin.qc_user=t.user_id
			WHERE 1=1
				<[AND checkin.status = :status]>
				<[AND checkin.status != :NOT_STATUS]>
				<[AND checkin.qc_user IN (:user_id_list)]>
				<[AND checkin.qc_time >= :start_handle_time]>
				<[AND checkin.qc_time <= :end_handle_time]>
			<[:group_by_checkin_allocation_date]>
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="queryCheckinPurchaseStatisticsList">
		<content>
      <![CDATA[
        	SELECT
				checkin.create_user AS userId,
				t.username AS userName,
				t.name AS name,
				COUNT(DISTINCT checkin.tracking_number) AS checkinOrderCount,
				SUM(checkin.quantity) AS checkinUnitCount
				
				<[:checkin_date]>
			FROM (
				SELECT 
					checkin.create_user,
					checkin.qc_time,
					checkin.purchase_order_no,
					purchase.tracking_number,
					purchase.quantity
				FROM 
					wh_check_in checkin LEFT JOIN wh_purchase_express_record purchase ON checkin.tracking_number=purchase.tracking_number
				 WHERE 1=1
					AND checkin.in_id IN (
                        SELECT in_id FROM (
                            SELECT in_id FROM wh_check_in WHERE 1=1
                            <[AND status = :status]>
                            <[AND status != :NOT_STATUS]>
                            <[AND qc_time >= :start_handle_time]>
                            <[AND qc_time <= :end_handle_time]>
                            <[AND create_date >= :start_create_date]>
				    		<[AND create_date <= :end_create_date]>
				    		<[AND check_in_type != :NOT_CHECKIN_TYPE]>
                            GROUP BY tracking_number
                        ) t
                    )
                    <[AND checkin.status = :status]>
                    <[AND checkin.status != :NOT_STATUS]>
                    <[AND checkin.create_user IN (:user_id_list)]>
					<[AND checkin.qc_time >= :start_handle_time]>
					<[AND checkin.qc_time <= :end_handle_time]>
					<[AND checkin.create_date >= :start_create_date]>
				    <[AND checkin.create_date <= :end_create_date]>
				    <[AND check_in_type != :NOT_CHECKIN_TYPE]>
				GROUP BY checkin.tracking_number
			) checkin JOIN t_user t ON checkin.create_user=t.user_id
			<[:group_by_checkin_date]>
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="queryCheckinTagStatisticsList">
		<content>
      <![CDATA[
        	SELECT 
				checkin.tag_user AS userId,
				t.username AS userName,
				t.name AS name
				, COUNT(DISTINCT IF(checkin.isPacking = 5, checkin.sku, NULL)) AS checkinProcessTagSkuCount
				, SUM(IF(checkin.isPacking = 5, checkin.quantity, 0)) AS checkinProcessTagPcsCount
				, COUNT(DISTINCT IF(checkin.isPacking = 3, checkin.sku, NULL)) AS checkinProcessLightTagSkuCount
				, SUM(IF(checkin.isPacking = 3, checkin.quantity, 0)) AS checkinProcessLightTagPcsCount
				, COUNT(DISTINCT IF(checkin.isPacking = 7, checkin.sku, NULL)) AS checkinProcessWeightTagSkuCount
				, SUM(IF(checkin.isPacking = 7, checkin.quantity, 0)) AS checkinProcessWeightTagPcsCount
				, COUNT(DISTINCT IF(checkin.isPacking = 9, checkin.sku, NULL)) AS checkinProcessSuperTagSkuCount
				, SUM(IF(checkin.isPacking = 9, checkin.quantity, 0)) AS checkinProcessSuperTagPcsCount
				, COUNT(DISTINCT IF(checkin.isPacking = 0, checkin.sku, NULL)) AS checkinOrdinaryTagSkuCount
				, SUM(IF(checkin.isPacking = 0, checkin.quantity, 0)) AS checkinOrdinaryTagPcsCount
				, COUNT(DISTINCT IF(checkin.isPacking IN (3, 5, 7, 9), checkin.sku, NULL)) AS checkinProcessSkuCount
				, SUM(IF(checkin.isPacking IN (3, 5, 7, 9), checkin.quantity, 0)) AS checkinProcessPcsCount
				, COUNT(DISTINCT IF(checkin.isPacking = 0, checkin.sku, NULL)) AS checkinNormalSkuCount
				, SUM(IF(checkin.isPacking = 0, checkin.quantity, 0)) AS checkinNormalPcsCount
				<[:chekcin_tag_date]>
			FROM 
				(
					SELECT checkin.tag_user,checkin.qc_time
					,item.sku,item.quantity,checkin.purchase_order_no
					,(
					CASE
						WHEN FIND_IN_SET('11', IFNULL(item.first_order_type,'空')) > 0 AND ((s.floor_location IS NULL AND s.sku_alias = 'true') OR s.floor_location = 5) THEN 5
					 WHEN FIND_IN_SET('11', IFNULL(item.first_order_type,'空')) > 0 AND ((s.floor_location IS NULL AND (s.sku_alias = 'false' OR s.sku_alias IS NULL)) OR s.floor_location = 0) THEN 0
					 WHEN FIND_IN_SET('11', IFNULL(item.first_order_type,'空')) = 0  THEN checkin.is_qc_packing
					ELSE s.floor_location END
					)AS isPacking
					FROM wh_check_in_item item
						JOIN wh_check_in checkin ON item.in_id = checkin.in_id
						JOIN wh_sku s ON s.sku = item.sku
					WHERE 1=1
					<[AND checkin.status = :status]>
					<[AND checkin.status != :NOT_STATUS]>
					<[AND checkin.tag_user IN (:user_id_list)]>
					<[AND checkin.qc_time >= :start_handle_time]>
					<[AND checkin.qc_time <= :end_handle_time]>
					<[AND checkin.create_date >= :start_create_date]>
					<[AND checkin.create_date <= :end_create_date]>
				)checkin
				JOIN t_user t ON checkin.tag_user=t.user_id
			<[:group_by_checkin_tag_date]>
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="queryQcCheckinStatisticsList">
		<content>
      <![CDATA[
        	SELECT 
				checkin.qc_user AS userId,
				t.username AS userName,
        		t.name AS name,
				
				COUNT(IF(checkin.is_all_check != 1, item.sku, NULL)) AS qcSkuCount,
				SUM(IF(checkin.is_all_check != 1, item.quantity, 0)) AS qcPcsCount,
				COUNT(IF(checkin.is_all_check = 1, item.sku, NULL)) AS allCheckQcSkuCount,
				SUM(IF(checkin.is_all_check = 1, item.quantity, 0)) AS allCheckQcPcsCount
				
				<[:qc_date]>
			FROM 
				wh_check_in_item item
				JOIN wh_check_in checkin ON item.in_id=checkin.in_id 
				JOIN t_user t ON checkin.qc_user=t.user_id
			WHERE 
				1=1
				AND checkin.status>=9
				<[AND checkin.qc_user IN (:user_id_list)]>
				<[AND checkin.qc_time >= :start_handle_time]>
				<[AND checkin.qc_time <= :end_handle_time]>
				<[AND item.sku = (SELECT whSku.sku FROM wh_sku whSku WHERE whSku.sku=item.sku AND whSku.warehouse_id = :warehouse_id)]>
			<[:group_by_qc_date]>
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="queryPickupCheckinStatisticsList">
		<content>
      <![CDATA[
        	SELECT 
				checkin.obtain_user AS userId,
				t.username AS userName,
        		t.name AS name,
        		
        		COUNT(sku) AS pickupSkuCount,
				SUM(quantity) AS pickupPcsCount
        		
				<[:pickup_date]>
			FROM 
				wh_check_in_item item
				JOIN wh_check_in checkin ON item.in_id=checkin.in_id
				JOIN t_user t ON checkin.obtain_user=t.user_id
			WHERE 
				1=1
				AND checkin.status>=11
				<[AND checkin.obtain_user IN (:user_id_list)]>
				<[AND checkin.obtain_time >= :start_handle_time]>
				<[AND checkin.obtain_time <= :end_handle_time]>
				<[AND item.sku = (SELECT whSku.sku FROM wh_sku whSku WHERE whSku.sku=item.sku AND whSku.warehouse_id = :warehouse_id)]>
				<[AND checkin.status NOT IN (:not_in_status_list)]>
			<[:group_by_pickup_date]>
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="queryPutawayCheckinStatisticsList">
		<content>
      <![CDATA[
        	SELECT 
				checkin.up_user AS userId,
				t.username AS userName,
        		t.name AS name,
        		
        		SUM(CASE WHEN FIND_IN_SET('9', item.first_order_type) > 0 THEN 1 ELSE 0 END) AS directPutawaySkuCount,
    			SUM(CASE WHEN FIND_IN_SET('9', item.first_order_type) = 0 THEN 1 ELSE 0 END) AS putawaySkuCount,
				SUM(CASE WHEN FIND_IN_SET('9', item.first_order_type) > 0 THEN quantity ELSE 0 END) AS directPutawayPcsCount,
    			SUM(CASE WHEN FIND_IN_SET('9', item.first_order_type) = 0 THEN quantity ELSE 0 END) AS putawayPcsCount
        		
				<[:putaway_date]>
			FROM 
				wh_check_in_item item
				JOIN wh_check_in checkin ON item.in_id=checkin.in_id
				JOIN t_user t ON checkin.up_user=t.user_id
			WHERE 
				1=1
				AND checkin.status>=13
				<[AND checkin.up_user IN (:user_id_list)]>
				<[AND checkin.up_time >= :start_handle_time]>
				<[AND checkin.up_time <= :end_handle_time]>
				<[AND item.sku = (SELECT whSku.sku FROM wh_sku whSku WHERE whSku.sku=item.sku AND whSku.warehouse_id = :warehouse_id)]>
			<[:group_by_putaway_date]>
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryAllocationPutAwayCheckInStatistics">
		<content>
			<![CDATA[
        	SELECT
				checkin.up_user AS userId,
				t.username AS userName,
        		t.name AS name,

        		COUNT(sku) AS allocationPutawaySkuCount,
				SUM(quantity) AS allocationPutawayPcsCount

				<[:putaway_date]>
			FROM
				wh_allocation_check_in_item item
				JOIN wh_allocation_check_in checkin ON item.in_id=checkin.in_id
				JOIN t_user t ON checkin.up_user=t.user_id
			WHERE
				1=1
				AND checkin.status>=13
				<[AND checkin.up_user IN (:user_id_list)]>
				<[AND checkin.up_time >= :start_handle_time]>
				<[AND checkin.up_time <= :end_handle_time]>
				<[AND item.sku = (SELECT whSku.sku FROM wh_sku whSku WHERE whSku.sku=item.sku AND whSku.warehouse_id = :warehouse_id)]>
			<[:group_by_putaway_date]>
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="queryReturnCheckinStatisticsList">
		<content>
      <![CDATA[
        	SELECT 
        		t.user_id AS userId,
				t.username AS userName,
        		t.name AS name,
        		
        		COUNT(ri.sku) AS returnSkuCount,
				SUM(ri.complete_quantity) AS returnPcsCount
        		
				<[:return_date]>
        	FROM 
        		wh_return_item ri
        		LEFT JOIN wh_return r ON r.id=ri.return_id
        		JOIN t_user t ON r.return_user=t.user_id 
        	WHERE
        		1=1
        		AND r.status IN (3, 4)
        		<[AND r.return_user IN (:user_id_list)]>
				<[AND r.complete_date >= :start_handle_time]>
				<[AND r.complete_date <= :end_handle_time]>
			<[:group_by_return_date]>
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="queryAbroadReturnCheckinStatisticsList">
		<content>
      <![CDATA[
        	SELECT 
        		t.user_id AS userId,
				t.username AS userName,
        		t.name AS name,
        		
        		COUNT(bri.sku) AS abroadReturnSkuCount,
				SUM(bri.complete_quantity) AS abroadReturnPcsCount
        		
				<[:abroad_return_date]>
        	FROM 
        		wh_abroad_return_item bri
        		LEFT JOIN wh_abroad_return br ON br.id=bri.return_id
        		JOIN t_user t ON br.return_user=t.user_id 
        	WHERE
        		1=1
        		AND br.status IN (5, 7)
        		AND bri.complete_quantity > 0
        		<[AND br.return_user IN (:user_id_list)]>
				<[AND br.complete_date >= :start_handle_time]>
				<[AND br.complete_date <= :end_handle_time]>
			<[:group_by_abroad_return_date]>
      ]]>
		</content>
	</sql>

    <sql datasource="dataSource" id="queryReturnedBatchStatisticsList">
        <content>
            <![CDATA[
                SELECT
                    t.user_id AS userId, t.username AS userName, t.name AS name,
                    COUNT(DISTINCT wbr.id) AS returnedBatchCount
                    , COUNT(wbrai.id) AS packageCount
                    <[:returned_batch_date]>
                FROM
                    wh_batch_return wbr
                    LEFT JOIN wh_batch_return_apv_item wbrai ON wbrai.order_no = wbr.order_no
                    JOIN t_user t ON wbr.created_by = t.user_id
                WHERE
                    1=1
                    <[AND wbr.created_by IN (:user_id_list)]>
                    <[AND wbr.creation_date >= :start_handle_time]>
                    <[AND wbr.creation_date <= :end_handle_time]>
                <[:group_by_returned_batch_date]>
            ]]>
        </content>
    </sql>


    <sql datasource="dataSource" id="queryReturnedSeedingStatisticsList">
        <content>
            <![CDATA[
               SELECT
                    t.user_id AS userId, t.username AS userName, t.name AS name,
                    COUNT(DISTINCT wbr.sku) AS returnedSeedingSkuCount,
	                COUNT(wbr.id) AS returnedSeedingPcsCount
                   	<[:returned_seeding_date]>
                FROM
                    wh_batch_return_uuid wbr
                    JOIN t_user t ON wbr.grid_by = t.user_id
                WHERE
                    LEFT(wbr.order_no,2) = 'TH'
                    <[AND wbr.grid_by IN (:user_id_list)]>
                    <[AND wbr.grid_date >= :start_handle_time]>
                    <[AND wbr.grid_date <= :end_handle_time]>
                    <[:group_by_returned_seeding_date]>
            ]]>
        </content>
    </sql>

	<sql datasource="dataSource" id="queryExceptionStatistics">
		<content>
			<![CDATA[
				SELECT
					t.user_id AS userId,
					t.username AS userName,
					t.NAME AS NAME,
					COUNT( wed.exception_id ) AS handleUnConfirmCount,
					DATE_FORMAT( wed.handle_draft_date, '%Y-%m-%d' ) AS handleDate
				FROM
					handle_exception_date wed
					LEFT JOIN wh_check_in_exception wcie ON wed.exception_id = wcie.id
					JOIN t_user t ON wed.handle_draft_by = t.user_id
				WHERE
					wcie.exception_form IN ( 1, 3, 8, 9 )
					<[AND wed.handle_draft_by in (:user_id_list)]>
					<[AND wed.handle_draft_date >= :start_handle_time]>
					<[AND wed.handle_draft_date <= :end_handle_time]>
					<[:group_by_handle_draft_date]>
            ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryFinishExceptionStatistics">
		<content>
			<![CDATA[
				SELECT
					t.user_id AS userId,
					t.username AS userName,
					t.NAME AS NAME,
					COUNT(wed.exception_id) AS finishCount
					,DATE_FORMAT(wed.finish_date, '%Y-%m-%d') AS handleDate
				FROM
					handle_exception_date wed
					LEFT JOIN wh_check_in_exception wcie ON wed.exception_id = wcie.id
					JOIN t_user t ON wed.finish_by = t.user_id
				WHERE
					wcie.exception_form IN ( 1, 3, 8, 9 )
					<[AND wed.finish_by in (:user_id_list)]>
					<[AND wed.finish_date >= :start_handle_time]>
					<[AND wed.finish_date <= :end_handle_time]>
					<[:group_by_finish_date]>
            ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryCheckinViewStatisticsList">
		<content>
			<![CDATA[
        	SELECT
				1 AS userId,
				'1' AS userName,
				'1' AS name,
				COUNT(DISTINCT checkin.purchase_order_no) AS checkinPoOrderCount
				<[:checkin_date]>
			FROM wh_check_in checkin
			WHERE 1 = 1
				<[AND checkin.create_date >= :start_handle_time]>
				<[AND checkin.create_date <= :end_handle_time]>
				<[AND checkin.status NOT IN (:not_in_status_list)]>
			<[:group_by_checkin_date]>
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryCheckinTagViewStatisticsList">
		<content>
			<![CDATA[
        	SELECT
				1 AS userId,
				'1' AS userName,
				'1' AS name,
				COUNT(IF(checkin.in_system_packing IN (3, 5, 7,9), true, NULL)) AS checkinProcessSkuCount,
				SUM(IF(checkin.in_system_packing IN (3, 5, 7,9), item.quantity, NULL)) AS checkinProcessPcsCount,
				COUNT(IF(checkin.in_system_packing = 0, true, NULL)) AS checkinNormalSkuCount,
				SUM(IF(checkin.in_system_packing = 0, item.quantity, NULL)) AS checkinNormalPcsCount
				<[:checkin_tag_date]>
			FROM
				wh_check_in_item item
				JOIN wh_check_in checkin ON item.in_id=checkin.in_id
			WHERE 1 = 1
				<[AND checkin.create_date >= :start_handle_time]>
				<[AND checkin.create_date <= :end_handle_time]>
				<[AND checkin.status NOT IN (:not_in_status_list)]>
			<[:group_by_checkin_tag_date]>
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryCheckinPurchaseViewStatisticsList">
		<content>
			<![CDATA[
        	SELECT
				1 AS userId,
				'1' AS userName,
				'1' AS name,
				COUNT(DISTINCT checkin.tracking_number) AS checkinOrderCount,
				SUM(checkin.quantity) AS checkinUnitCount
				<[:checkin_date]>
			FROM (
				SELECT
					checkin.create_user,
					checkin.create_date,
					checkin.purchase_order_no,
					purchase.tracking_number,
					purchase.quantity
				FROM
					wh_check_in checkin LEFT JOIN wh_purchase_express_record purchase ON checkin.tracking_number=purchase.tracking_number
				 WHERE 1 = 1
					AND checkin.in_id IN (
                            SELECT in_id FROM wh_check_in WHERE 1 = 1
							<[AND checkin.status NOT IN (:not_in_status_list)]>
                            <[AND create_date >= :start_handle_time]>
                            <[AND create_date <= :end_handle_time]>
                            GROUP BY tracking_number
                    )
                    <[AND checkin.status NOT IN (:not_in_status_list)]>
                    <[AND create_date >= :start_handle_time]>
                    <[AND create_date <= :end_handle_time]>
                    GROUP BY tracking_number
			) checkin
			<[:group_by_checkin_date]>
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryQcCheckinViewStatisticsList">
		<content>
			<![CDATA[
        	SELECT
				1 AS userId,
				'1' AS userName,
				'1' AS name,
				COUNT(IF(checkin.is_all_check != 1, item.sku, NULL)) AS qcSkuCount,
				SUM(IF(checkin.is_all_check != 1, item.quantity, 0)) AS qcPcsCount,
				COUNT(IF(checkin.is_all_check = 1, item.sku, NULL)) AS allCheckQcSkuCount,
				SUM(IF(checkin.is_all_check = 1, item.quantity, 0)) AS allCheckQcPcsCount
				<[:qc_date]>
			FROM
				wh_check_in_item item
				JOIN wh_check_in checkin ON item.in_id=checkin.in_id
			WHERE
				1=1
				<[AND checkin.status NOT IN (:not_in_status_list)]>
				<[AND checkin.qc_time >= :start_handle_time]>
				<[AND checkin.qc_time <= :end_handle_time]>
				<[:group_by_qc_date]>
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryPutawayCheckinViewStatisticsList">
		<content>
			<![CDATA[
        	SELECT
				1 AS userId,
				'1' AS userName,
				'1' AS name,
        		SUM(CASE WHEN FIND_IN_SET('9', item.first_order_type) > 0 THEN 1 ELSE 0 END) AS directPutawaySkuCount,
    			SUM(CASE WHEN FIND_IN_SET('9', item.first_order_type) = 0 THEN 1 ELSE 0 END) AS putawaySkuCount,
				SUM(CASE WHEN FIND_IN_SET('9', item.first_order_type) > 0 THEN quantity ELSE 0 END) AS directPutawayPcsCount,
    			SUM(CASE WHEN FIND_IN_SET('9', item.first_order_type) = 0 THEN quantity ELSE 0 END) AS putawayPcsCount
				<[:putaway_date]>
			FROM
				wh_check_in_item item
				JOIN wh_check_in checkin ON item.in_id=checkin.in_id
			WHERE
				1=1
				<[AND checkin.up_time >= :start_handle_time]>
				<[AND checkin.up_time <= :end_handle_time]>
			<[:group_by_putaway_date]>
      ]]>
		</content>
	</sql>

</sqlmap>