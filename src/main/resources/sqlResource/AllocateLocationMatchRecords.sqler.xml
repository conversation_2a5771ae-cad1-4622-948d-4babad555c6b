<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap>

    <sql datasource="dataSource" id="queryLocationMatchRecordCounts">
        <content ><![CDATA[
        SELECT COUNT(1)
        FROM allocate_location_match_record
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id in (:recordIds)]>
        <[AND sku = :sku]>
        <[AND sku in (:skus)]>
        <[AND location = :location]>
        <[AND location in (:locations)]>
        <[AND calculate_after_sell = :calculate_after_sell]>
        <[AND exp = :exp]>
        <[AND relate_receipt_no = :relate_receipt_no]>
        <[AND relate_receipt_no in (:relateReceiptNos)]>
        <[AND handle_result = :handle_result]>
        <[AND handle_result in (:handle_result_code_list)]>
        <[AND allocate_date >= :from_allocation_time]>
        <[AND allocate_date <= :to_allocation_time]>
        ]]></content>
    </sql>


    <sql datasource="dataSource" id="queryLocationMatchRecords">
        <content ><![CDATA[
        SELECT id,sku,allocate_phase,handle_result,location,allocate_person,allocate_date,calculate_after_sell
                ,exp,relate_receipt_no,remark
        FROM allocate_location_match_record
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id in (:recordIds)]>
        <[AND sku = :sku]>
        <[AND sku in (:skus)]>
        <[AND location = :location]>
        <[AND location in (:locations)]>
        <[AND calculate_after_sell = :calculate_after_sell]>
        <[AND exp = :exp]>
        <[AND relate_receipt_no = :relate_receipt_no]>
        <[AND relate_receipt_no in (:relateReceiptNos)]>
        <[AND handle_result = :handle_result]>
        <[AND handle_result in (:handle_result_code_list)]>
        <[AND allocate_date >= :from_allocation_time]>
        <[AND allocate_date <= :to_allocation_time]>
        ORDER BY id DESC
        ]]></content>
    </sql>


    <sql datasource="dataSource" id="insertLocationMatchRecords">
        <content ><![CDATA[
        INSERT INTO allocate_location_match_record(sku,allocate_phase,handle_result,location,allocate_person
            ,allocate_date,calculate_after_sell,exp,relate_receipt_no,remark)
        VALUES (:sku,:allocate_phase,:handle_result,:location,:allocate_person
            ,:allocate_date,:calculate_after_sell,:exp,:relate_receipt_no,:remark)

        ]]></content>
    </sql>


    <sql datasource="dataSource" id="updateLocationMatchRecords">
        <content ><![CDATA[
        UPDATE allocate_location_match_record
        SET id = id
        <[,sku = :sku]>
        <[,allocate_phase = :allocate_phase]>
        <[,handle_result = :handle_result]>
        <[,location = :location]>
        <[,allocate_person = :allocate_person]>
        <[,allocate_date = :allocate_date]>
        <[,calculate_after_sell = :calculate_after_sell]>
        <[,exp = :exp]>
        <[,relate_receipt_no = :relate_receipt_no]>
        <[,remark = :remark]>
        WHERE id = :id

        ]]></content>
    </sql>
</sqlmap>