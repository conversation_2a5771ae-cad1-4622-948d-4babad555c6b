<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhUniqueApvGridCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_unique_apv_grid
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_no = :task_no]>
        <[AND apv_no = :apv_no]>
        <[AND sku = :sku]>
        <[AND uuid = :uuid]>
        <[AND unique_id = :unique_id]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>

        <[AND unique_id IN ( :unique_id_list)]>
        <[:PREPARE_ORDER_NO]>
        <[:UP_TO_DATE]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhUniqueApvGridList" >
    <content >
      <![CDATA[
        SELECT id, task_no, apv_no, sku, uuid, unique_id, creation_date, create_by
        FROM wh_unique_apv_grid
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_no = :task_no]>
        <[AND apv_no = :apv_no]>
        <[AND sku = :sku]>
        <[AND uuid = :uuid]>
        <[AND unique_id = :unique_id]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
        <[AND unique_id IN ( :unique_id_list)]>
        <[:PREPARE_ORDER_NO]>
        <[:UP_TO_DATE]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhUniqueApvGridByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, task_no, apv_no, sku, uuid, unique_id, creation_date, create_by
        FROM wh_unique_apv_grid
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhUniqueApvGrid" >
    <content >
      <![CDATA[
        SELECT id, task_no, apv_no, sku, uuid, unique_id, creation_date, create_by
        FROM wh_unique_apv_grid
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_no = :task_no]>
        <[AND apv_no = :apv_no]>
        <[AND sku = :sku]>
        <[AND uuid = :uuid]>
        <[AND unique_id = :unique_id]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
        <[AND unique_id IN ( :unique_id_list)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhUniqueApvGrid" >
    <content >
      <![CDATA[
        INSERT INTO wh_unique_apv_grid (task_no, apv_no, sku, uuid, unique_id, creation_date, create_by)
        VALUES (:task_no, :apv_no, :sku, :uuid, :unique_id, :creation_date, :create_by)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhUniqueApvGridByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_unique_apv_grid
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhUniqueApvGridByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_unique_apv_grid
        SET <[task_no = :task_no,]>
          <[apv_no = :apv_no,]>
          <[sku = :sku,]>
          <[uuid = :uuid,]>
          <[unique_id = :unique_id,]>
          <[creation_date = :creation_date,]>
          <[create_by = :create_by,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>