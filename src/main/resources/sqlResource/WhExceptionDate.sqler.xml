<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhExceptionDateCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_exception_date
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND exception_id = :exception_id]>
        <[AND status = :status]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND quantity = :quantity]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhExceptionDateList" >
    <content >
      <![CDATA[
        SELECT id, exception_id, status, created_by, creation_date, quantity
        FROM wh_exception_date
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND exception_id = :exception_id]>
        <[AND status = :status]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND quantity = :quantity]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhExceptionDateByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, exception_id, status, created_by, creation_date, quantity
        FROM wh_exception_date
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhExceptionDate" >
    <content >
      <![CDATA[
        SELECT id, exception_id, status, created_by, creation_date, quantity
        FROM wh_exception_date
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND exception_id = :exception_id]>
        <[AND status = :status]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND quantity = :quantity]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhExceptionDate" >
    <content >
      <![CDATA[
        INSERT INTO wh_exception_date (exception_id, status, created_by, creation_date, quantity)
        VALUES (:exception_id, :status, :created_by, :creation_date, :quantity)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhExceptionDateByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_exception_date
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhExceptionDateByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_exception_date
        SET <[exception_id = :exception_id,]>
          <[status = :status,]>
          <[created_by = :created_by,]>
          <[creation_date = :creation_date,]>
          <[quantity = :quantity,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>