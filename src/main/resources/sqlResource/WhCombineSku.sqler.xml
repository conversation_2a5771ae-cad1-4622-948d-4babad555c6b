<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhCombineSkuCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_combine_sku
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND spu = :spu]>
        <[AND status = :status]>
        <[AND name = :name]>
        <[AND image_url = :image_url]>
        <[AND product_created_by = :product_created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND customs_weight = :customs_weight]>
        <[AND net_weight = :net_weight]>
        <[AND id IN (:id_list)]>
        <[AND spu IN (:spu_list)]>
        <[AND spu IN (SELECT spu FROM wh_combine_sku_item WHERE sku IN (:sku_list))]>
        <[AND creation_date >= :from_create_time]>
        <[AND creation_date <= :to_create_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhCombineSkuList" >
    <content >
      <![CDATA[
        SELECT s.id, s.spu, s.status, s.name, s.image_url, s.product_created_by, s.creation_date, s.last_update_date,
        s.customs_weight, s.net_weight, i.spu, i.sku, i.quantity
        FROM wh_combine_sku s
        LEFT JOIN wh_combine_sku_item i ON i.spu = s.spu
        INNER JOIN (
         SELECT id
        FROM wh_combine_sku
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND spu = :spu]>
        <[AND status = :status]>
        <[AND name = :name]>
        <[AND image_url = :image_url]>
        <[AND product_created_by = :product_created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND customs_weight = :customs_weight]>
        <[AND net_weight = :net_weight]>
        <[AND id IN (:id_list)]>
        <[AND spu IN (:spu_list)]>
        <[AND spu IN (SELECT spu FROM wh_combine_sku_item WHERE sku IN (:sku_list))]>
        <[AND creation_date >= :from_create_time]>
        <[AND creation_date <= :to_create_time]>
        <[:LIMIT]>
        ) so on so.id = s.id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhCombineSkuByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, spu, status, name, image_url, product_created_by, creation_date, last_update_date, 
        customs_weight, net_weight
        FROM wh_combine_sku
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhCombineSku" >
    <content >
      <![CDATA[
        SELECT id, spu, status, name, image_url, product_created_by, creation_date, last_update_date, 
        customs_weight, net_weight
        FROM wh_combine_sku
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND spu = :spu]>
        <[AND status = :status]>
        <[AND name = :name]>
        <[AND image_url = :image_url]>
        <[AND product_created_by = :product_created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND customs_weight = :customs_weight]>
        <[AND net_weight = :net_weight]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryIdBySpus" >
    <content >
      <![CDATA[
        SELECT id, spu
        FROM wh_combine_sku
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND spu = :spu]>
        <[AND status = :status]>
        <[AND name = :name]>
        <[AND image_url = :image_url]>
        <[AND product_created_by = :product_created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND customs_weight = :customs_weight]>
        <[AND net_weight = :net_weight]>
        <[AND id IN (:id_list)]>
        <[AND spu IN (:spu_list)]>
        <[AND spu IN (SELECT spu FROM wh_combine_sku_item WHERE sku IN (:sku_list))]>
        <[AND creation_date >= :from_create_time]>
        <[AND creation_date <= :to_create_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhCombineSku" >
    <content >
      <![CDATA[
        INSERT INTO wh_combine_sku (spu, status, name, image_url, product_created_by, creation_date, last_update_date, 
          customs_weight, net_weight)
        VALUES (:spu, :status, :name, :image_url, :product_created_by, :creation_date, :last_update_date, 
          :customs_weight, :net_weight)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhCombineSkuByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_combine_sku
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhCombineSkuByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_combine_sku
        SET <[spu = :spu,]>
          <[status = :status,]>
          <[name = :name,]>
          <[image_url = :image_url,]>
          <[product_created_by = :product_created_by,]>
          <[creation_date = :creation_date,]>
          <[last_update_date = :last_update_date,]>
          <[customs_weight = :customs_weight,]>
          <[net_weight = :net_weight,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryLocationNumberBySpu" >
    <content >
      <![CDATA[
        SELECT
        ws.location_number AS locationNumber
        FROM wh_combine_sku wcs INNER JOIN wh_stock ws
        ON wcs.spu = ws.sku
        WHERE 1 = 1
        <[AND wcs.spu = :spu]>
        AND ws.location_number IS NOT NULL
        LIMIT 1
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhCombineSkuStocks" >
    <content >
      <![CDATA[
        SELECT ws.id, ws.spu as sku, ws.name,wr.location_number,wr.warehouse_id,
        wr.surplus_quantity, wr.pick_quantity, wr.pick_not_quantity, ws.image_url,wr.id,
        wr.qc_quantity, wr.waiting_up_quantity, wr.up_quantity, wr.allot_quantity, wr.cancel_quantity, wr.pick_return_quantity
		<[:on_way_quantity]>
        FROM wh_combine_sku ws
        LEFT JOIN wh_stock wr ON wr.sku = ws.spu
        WHERE 1 = 1
        <[AND ws.id = :id]>
        <[AND ws.spu = :spu]>
        <[AND FIND_IN_SET(:location_number,wr.location_number)]>
      ]]>
    </content>
  </sql>
</sqlmap>