<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryHandleExceptionDateCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM handle_exception_date
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND exception_id = :exception_id]>
        <[AND handle_draft_by = :handle_draft_by]>
        <[AND handle_draft_date = :handle_draft_date]>
        <[AND finish_by = :finish_by]>
        <[AND finish_date = :finish_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryHandleExceptionDateList" >
    <content >
      <![CDATA[
        SELECT id, exception_id, handle_draft_by, handle_draft_date, finish_by, finish_date
        FROM handle_exception_date
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND exception_id = :exception_id]>
        <[AND handle_draft_by = :handle_draft_by]>
        <[AND handle_draft_date = :handle_draft_date]>
        <[AND finish_by = :finish_by]>
        <[AND finish_date = :finish_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryHandleExceptionDateByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, exception_id, handle_draft_by, handle_draft_date, finish_by, finish_date
        FROM handle_exception_date
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryHandleExceptionDate" >
    <content >
      <![CDATA[
        SELECT id, exception_id, handle_draft_by, handle_draft_date, finish_by, finish_date
        FROM handle_exception_date
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND exception_id = :exception_id]>
        <[AND handle_draft_by = :handle_draft_by]>
        <[AND handle_draft_date = :handle_draft_date]>
        <[AND finish_by = :finish_by]>
        <[AND finish_date = :finish_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createHandleExceptionDate" >
    <content >
      <![CDATA[
        INSERT INTO handle_exception_date (exception_id, handle_draft_by, handle_draft_date, finish_by, finish_date
          )
        VALUES (:exception_id, :handle_draft_by, :handle_draft_date, :finish_by, :finish_date
          )
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteHandleExceptionDateByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM handle_exception_date
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateHandleExceptionDateByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE handle_exception_date
        SET <[exception_id = :exception_id,]>
          <[handle_draft_by = :handle_draft_by,]>
          <[handle_draft_date = :handle_draft_date,]>
          <[finish_by = :finish_by,]>
          <[finish_date = :finish_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>