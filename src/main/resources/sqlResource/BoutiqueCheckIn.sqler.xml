<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryBoutiqueCheckInCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM boutique_check_in
        WHERE 1 = 1
        <[AND in_id = :in_id]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND purpose_house = :purpose_house]>
        <[AND gp_qty = :gp_qty]>
        <[AND bad_qty = :bad_qty]>
        <[AND create_date = :create_date]>
        <[AND create_user = :create_user]>
        <[AND is_purchase_stock_in = :is_purchase_stock_in]>
        <[AND is_in_stock = :is_in_stock]>
        <[AND sku IN (:sku_list)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryBoutiqueCheckInList" >
    <content >
      <![CDATA[
        SELECT in_id, purchase_order_no, sku, quantity, purpose_house, gp_qty, bad_qty, create_date, 
        create_user, is_purchase_stock_in, is_in_stock
        FROM boutique_check_in
        WHERE 1 = 1
        <[AND in_id = :in_id]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND purpose_house = :purpose_house]>
        <[AND gp_qty = :gp_qty]>
        <[AND bad_qty = :bad_qty]>
        <[AND create_date = :create_date]>
        <[AND create_user = :create_user]>
        <[AND is_purchase_stock_in = :is_purchase_stock_in]>
        <[AND is_in_stock = :is_in_stock]>
        <[AND sku IN (:sku_list)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryBoutiqueCheckInByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT in_id, purchase_order_no, sku, quantity, purpose_house, gp_qty, bad_qty, create_date, 
        create_user, is_purchase_stock_in, is_in_stock
        FROM boutique_check_in
        WHERE 1 = 1
        AND in_id = :in_id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryBoutiqueCheckIn" >
    <content >
      <![CDATA[
        SELECT in_id, purchase_order_no, sku, quantity, purpose_house, gp_qty, bad_qty, create_date, 
        create_user, is_purchase_stock_in, is_in_stock
        FROM boutique_check_in
        WHERE 1 = 1
        <[AND in_id = :in_id]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND purpose_house = :purpose_house]>
        <[AND gp_qty = :gp_qty]>
        <[AND bad_qty = :bad_qty]>
        <[AND create_date = :create_date]>
        <[AND create_user = :create_user]>
        <[AND is_purchase_stock_in = :is_purchase_stock_in]>
        <[AND is_in_stock = :is_in_stock]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createBoutiqueCheckIn" >
    <content >
      <![CDATA[
        INSERT INTO boutique_check_in (purchase_order_no, sku, quantity, purpose_house, gp_qty, bad_qty, create_date, 
          create_user, is_purchase_stock_in, is_in_stock)
        VALUES (:purchase_order_no, :sku, :quantity, :purpose_house, :gp_qty, :bad_qty, :create_date, 
          :create_user, :is_purchase_stock_in, :is_in_stock)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteBoutiqueCheckInByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM boutique_check_in
        WHERE 1 = 1
        AND in_id = :in_id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateBoutiqueCheckInByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE boutique_check_in
        SET <[purchase_order_no = :purchase_order_no,]>
          <[sku = :sku,]>
          <[quantity = :quantity,]>
          <[purpose_house = :purpose_house,]>
          <[gp_qty = :gp_qty,]>
          <[bad_qty = :bad_qty,]>
          <[create_date = :create_date,]>
          <[create_user = :create_user,]>
          <[is_purchase_stock_in = :is_purchase_stock_in,]>
          <[is_in_stock = :is_in_stock,]>
        in_id = in_id
        WHERE 1 = 1
        AND in_id = :in_id
      ]]>
    </content>
  </sql>
</sqlmap>