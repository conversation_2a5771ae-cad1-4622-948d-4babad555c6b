<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryExemptionQcConfigurationCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM exemption_qc_configuration
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND exception_type = :exception_type]>
        <[AND unhappen_exception_day = :unhappen_exception_day]>
        <[AND sku_abnormal_purchase_num = :sku_abnormal_purchase_num]>
        <[AND exclusive_sku_type = :exclusive_sku_type]>
        <[AND status = :status]>
        <[AND last_update_time = :last_update_time]>
        <[AND creation_time = :creation_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryExemptionQcConfigurationList" >
    <content >
      <![CDATA[
        SELECT id, exception_type, unhappen_exception_day, sku_abnormal_purchase_num, check_in_sku_num, purchase_price,
         exclusive_sku_type, status, last_update_time, creation_time
        FROM exemption_qc_configuration
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND exception_type = :exception_type]>
        <[AND unhappen_exception_day = :unhappen_exception_day]>
        <[AND sku_abnormal_purchase_num = :sku_abnormal_purchase_num]>
        <[AND exclusive_sku_type = :exclusive_sku_type]>
        <[AND status = :status]>
        <[AND last_update_time = :last_update_time]>
        <[AND creation_time = :creation_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryExemptionQcConfigurationByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, exception_type, unhappen_exception_day, sku_abnormal_purchase_num, check_in_sku_num, purchase_price, exclusive_sku_type, status, last_update_time,
        creation_time
        FROM exemption_qc_configuration
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryExemptionQcConfiguration" >
    <content >
      <![CDATA[
        SELECT id, exception_type, unhappen_exception_day, sku_abnormal_purchase_num, check_in_sku_num, purchase_price, exclusive_sku_type, status, last_update_time,
        creation_time
        FROM exemption_qc_configuration
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND exception_type = :exception_type]>
        <[AND unhappen_exception_day = :unhappen_exception_day]>
        <[AND sku_abnormal_purchase_num = :sku_abnormal_purchase_num]>
        <[AND exclusive_sku_type = :exclusive_sku_type]>
        <[AND status = :status]>
        <[AND last_update_time = :last_update_time]>
        <[AND creation_time = :creation_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createExemptionQcConfiguration" >
    <content >
      <![CDATA[
        INSERT INTO exemption_qc_configuration (exception_type, unhappen_exception_day, sku_abnormal_purchase_num, check_in_sku_num, purchase_price
         exclusive_sku_type, status, last_update_time, creation_time)
        VALUES (:exception_type, :unhappen_exception_day, :sku_abnormal_purchase_num, :check_in_sku_num, :purchase_price
         :exclusive_sku_type, :status, :last_update_time, :creation_time)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteExemptionQcConfigurationByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM exemption_qc_configuration
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateExemptionQcConfigurationByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE exemption_qc_configuration
        SET <[exception_type = :exception_type,]>
          <[unhappen_exception_day = :unhappen_exception_day,]>
          <[sku_abnormal_purchase_num = :sku_abnormal_purchase_num,]>
          <[check_in_sku_num = :check_in_sku_num,]>
          <[purchase_price = :purchase_price,]>
          <[exclusive_sku_type = :exclusive_sku_type,]>
          <[status = :status,]>
          <[last_update_time = :last_update_time,]>
          <[creation_time = :creation_time,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>