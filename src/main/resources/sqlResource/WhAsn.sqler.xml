<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhAsnCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_asn
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND receiving_code = :receiving_code]>
        <[AND reference_no = :reference_no]>
        <[AND contract_no = :contract_no]>
        <[AND transit_type = :transit_type]>
        <[AND income_type = :income_type]>
        <[AND warehouse_code = :warehouse_code]>
        <[AND transit_warehouse_code = :transit_warehouse_code]>
        <[AND sm_code = :sm_code]>
        <[AND shipping_method = :shipping_method]>
        <[AND tracking_number = :tracking_number]>
        <[AND receiving_status = :receiving_status]>
        <[AND eta_date = :eta_date]>
        <[AND receiving_add_time = :receiving_add_time]>
        <[AND receiving_modify_time = :receiving_modify_time]>
        <[AND region_id_level0 = :region_id_level0]>
        <[AND region_id_level1 = :region_id_level1]>
        <[AND region_id_level2 = :region_id_level2]>
        <[AND street = :street]>
        <[AND contacter = :contacter]>
        <[AND contact_phone = :contact_phone]>
        <[AND box_total = :box_total]>
        <[AND sku_total = :sku_total]>
        <[AND sku_species = :sku_species]>
        <[AND status = :status]>
        <[AND dt_type = :dt_type]>
        <[AND ccf_type = :ccf_type]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND receiving_desc = :receiving_desc]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAsnList" >
    <content >
      <![CDATA[
        SELECT id, receiving_code, reference_no, contract_no, transit_type, income_type, 
        warehouse_code, transit_warehouse_code, sm_code, shipping_method, tracking_number, 
        receiving_status, eta_date, receiving_add_time, receiving_modify_time, region_id_level0, 
        region_id_level1, region_id_level2, street, contacter, contact_phone, box_total, 
        sku_total, sku_species, status, dt_type, ccf_type, created_by, creation_date, last_updated_by, 
        last_update_date, receiving_desc, order_no, order_name, type
        FROM wh_asn
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND receiving_code = :receiving_code]>
        <[AND reference_no = :reference_no]>
        <[AND contract_no = :contract_no]>
        <[AND transit_type = :transit_type]>
        <[AND income_type = :income_type]>
        <[AND warehouse_code = :warehouse_code]>
        <[AND transit_warehouse_code = :transit_warehouse_code]>
        <[AND sm_code = :sm_code]>
        <[AND shipping_method = :shipping_method]>
        <[AND tracking_number = :tracking_number]>
        <[AND receiving_status = :receiving_status]>
        <[AND eta_date = :eta_date]>
        <[AND receiving_add_time = :receiving_add_time]>
        <[AND receiving_modify_time = :receiving_modify_time]>
        <[AND region_id_level0 = :region_id_level0]>
        <[AND region_id_level1 = :region_id_level1]>
        <[AND region_id_level2 = :region_id_level2]>
        <[AND street = :street]>
        <[AND contacter = :contacter]>
        <[AND contact_phone = :contact_phone]>
        <[AND box_total = :box_total]>
        <[AND sku_total = :sku_total]>
        <[AND sku_species = :sku_species]>
        <[AND status = :status]>
        <[AND dt_type = :dt_type]>
        <[AND ccf_type = :ccf_type]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND receiving_desc = :receiving_desc]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAsnByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, receiving_code, reference_no, contract_no, transit_type, income_type, 
        warehouse_code, transit_warehouse_code, sm_code, shipping_method, tracking_number, 
        receiving_status, eta_date, receiving_add_time, receiving_modify_time, region_id_level0, 
        region_id_level1, region_id_level2, street, contacter, contact_phone, box_total, 
        sku_total, sku_species, status, dt_type, ccf_type, created_by, creation_date, last_updated_by, 
        last_update_date, receiving_desc,order_no, order_name, type
        FROM wh_asn
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAsn" >
    <content >
      <![CDATA[
        SELECT id, receiving_code, reference_no, contract_no, transit_type, income_type, 
        warehouse_code, transit_warehouse_code, sm_code, shipping_method, tracking_number, 
        receiving_status, eta_date, receiving_add_time, receiving_modify_time, region_id_level0, 
        region_id_level1, region_id_level2, street, contacter, contact_phone, box_total, 
        sku_total, sku_species, status, dt_type, ccf_type, created_by, creation_date, last_updated_by, 
        last_update_date, receiving_desc,order_no, order_name, type
        FROM wh_asn
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND receiving_code = :receiving_code]>
        <[AND reference_no = :reference_no]>
        <[AND contract_no = :contract_no]>
        <[AND transit_type = :transit_type]>
        <[AND income_type = :income_type]>
        <[AND warehouse_code = :warehouse_code]>
        <[AND transit_warehouse_code = :transit_warehouse_code]>
        <[AND sm_code = :sm_code]>
        <[AND shipping_method = :shipping_method]>
        <[AND tracking_number = :tracking_number]>
        <[AND receiving_status = :receiving_status]>
        <[AND eta_date = :eta_date]>
        <[AND receiving_add_time = :receiving_add_time]>
        <[AND receiving_modify_time = :receiving_modify_time]>
        <[AND region_id_level0 = :region_id_level0]>
        <[AND region_id_level1 = :region_id_level1]>
        <[AND region_id_level2 = :region_id_level2]>
        <[AND street = :street]>
        <[AND contacter = :contacter]>
        <[AND contact_phone = :contact_phone]>
        <[AND box_total = :box_total]>
        <[AND sku_total = :sku_total]>
        <[AND sku_species = :sku_species]>
        <[AND status = :status]>
        <[AND dt_type = :dt_type]>
        <[AND ccf_type = :ccf_type]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND receiving_desc = :receiving_desc]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhAsn" >
    <content >
      <![CDATA[
        INSERT INTO wh_asn (receiving_code, reference_no, contract_no, transit_type, income_type,
          warehouse_code, transit_warehouse_code, sm_code, shipping_method, tracking_number, 
          receiving_status, eta_date, receiving_add_time, receiving_modify_time, region_id_level0, 
          region_id_level1, region_id_level2, street, contacter, contact_phone, box_total, 
          sku_total, sku_species, status, dt_type, ccf_type, created_by, creation_date, 
          last_updated_by, last_update_date, receiving_desc,order_no, order_name, type)
        VALUES (:receiving_code, :reference_no, :contract_no, :transit_type, :income_type,
          :warehouse_code, :transit_warehouse_code, :sm_code, :shipping_method, :tracking_number, 
          :receiving_status, :eta_date, :receiving_add_time, :receiving_modify_time, :region_id_level0, 
          :region_id_level1, :region_id_level2, :street, :contacter, :contact_phone, :box_total, 
          :sku_total, :sku_species, :status, :dt_type, :ccf_type, :created_by, :creation_date, 
          :last_updated_by, :last_update_date, :receiving_desc,:order_no, :order_name, :type)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhAsnByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_asn
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhAsnByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_asn
        SET <[receiving_code = :receiving_code,]>
          <[reference_no = :reference_no,]>
          <[contract_no = :contract_no,]>
          <[transit_type = :transit_type,]>
          <[income_type = :income_type,]>
          <[warehouse_code = :warehouse_code,]>
          <[transit_warehouse_code = :transit_warehouse_code,]>
          <[sm_code = :sm_code,]>
          <[shipping_method = :shipping_method,]>
          <[tracking_number = :tracking_number,]>
          <[receiving_status = :receiving_status,]>
          <[eta_date = :eta_date,]>
          <[receiving_add_time = :receiving_add_time,]>
          <[receiving_modify_time = :receiving_modify_time,]>
          <[region_id_level0 = :region_id_level0,]>
          <[region_id_level1 = :region_id_level1,]>
          <[region_id_level2 = :region_id_level2,]>
          <[street = :street,]>
          <[contacter = :contacter,]>
          <[contact_phone = :contact_phone,]>
          <[box_total = :box_total,]>
          <[sku_total = :sku_total,]>
          <[sku_species = :sku_species,]>
          <[status = :status,]>
          <[dt_type = :dt_type,]>
          <[ccf_type = :ccf_type,]>
          <[created_by = :created_by,]>
          <[creation_date = :creation_date,]>
          <[last_updated_by = :last_updated_by,]>
          <[last_update_date = :last_update_date,]>
          <[deliver_time = :deliver_time,]>
          <[receiving_desc = :receiving_desc,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhAsnsAndItems">
    <content>
      <![CDATA[
         SELECT wa.id, wa.type, wa.receiving_code, wa.reference_no, wa.contract_no, wa.transit_type, wa.income_type, wa.
          warehouse_code, wa.transit_warehouse_code, wa.sm_code, wa.shipping_method, wa.tracking_number, wa.
          receiving_status, wa.receiving_add_time, wa.receiving_modify_time, wa.box_total, wa.
          sku_total, wa.sku_species, wa.status, wa.receiving_desc, wa.deliver_time,wa.order_no, wa.order_name, wa.type,
          wai.id,wai.wh_asn_id,wai.product_sku, wai.product_barcode, wai.quantity, wai.received_quantity, wai. putaway_qty, wai.box_no,
          wai.putaway_quantity, wai.product_price, wai.rd_update_time,wai.seller_sku, wai.seller_sku_name,wai.product_length, wai. product_width,
          wai.product_height, wai.product_weight, wai.allot_quantity, wai.pick_quantity, wai.loading_quantity, wai.warehouse_id
          FROM wh_asn wa left join wh_asn_item wai on wa.id=wai.wh_asn_id
		INNER JOIN (
			select wa.id
			from wh_asn wa
			WHERE 1 = 1
	        <[AND wa.id = :id]>
	        <[AND wa.receiving_code LIKE :receiving_code]>
	        <[AND wa.receiving_code IN (:receivingCodeList)]>
	        <[AND wa.warehouse_code = :warehouse_code]>
	        <[AND wa.status = :status]>
	        <[AND wa.status IN (:statusList)]>
	        <[AND wa.tracking_number = :tracking_number]>
	        <[AND wa.tracking_number IN :trackingNoList]>
	        <[AND wa.receiving_add_time >= :from_creation_date]>
	        <[AND wa.receiving_add_time <= :to_creation_date]>
	        <[AND wa.id IN (select item.wh_asn_id from wh_asn_item item where item.product_sku = :sku)]>
	        <[AND wa.id IN (select item.wh_asn_id from wh_asn_item item where item.product_sku IN (:skuList))]>
	        <[AND wa.id IN (:ids)]>
	        Order by wa.receiving_add_time desc
	        <[:LIMIT]>
		) whasn on wa.id = whasn.id
  		]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhAsnsAndItemCount">
    <content>
      <![CDATA[
  		select count(distinct wa.id)
		from wh_asn wa
  		 WHERE 1 = 1
            <[AND wa.id = :id]>
	        <[AND wa.receiving_code LIKE :receiving_code]>
	        <[AND wa.receiving_code IN (:receivingCodeList)]>
	        <[AND wa.warehouse_code = :warehouse_code]>
	        <[AND wa.status = :status]>
	        <[AND wa.status IN (:statusList)]>
	        <[AND wa.tracking_number = :tracking_number]>
	        <[AND wa.tracking_number IN :trackingNoList]>
	        <[AND wa.receiving_add_time >= :from_creation_date]>
	        <[AND wa.receiving_add_time <= :to_creation_date]>
	        <[AND wa.id IN (select item.wh_asn_id from wh_asn_item item where item.product_sku = :sku)]>
	        <[AND wa.id IN (select item.wh_asn_id from wh_asn_item item where item.product_sku in (:skuList))]>
	        <[AND wa.id IN (:ids)]>
  		]]>
    </content>
  </sql>
</sqlmap>