<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryTemuPrepareOrderItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM temu_prepare_order_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id in (:ids)]>
        <[AND prepare_order_id = :prepare_order_id]>
        <[AND skc = :skc]>
        <[AND sku = :sku]>
        <[AND label_code = :label_code]>
        <[AND prepare_quantity = :prepare_quantity]>
        <[AND allot_quantity = :allot_quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND pick_by = :pick_by]>
        <[AND pick_time = :pick_time]>
        <[AND grid_quantity = :grid_quantity]>
        <[AND grid_status = :grid_status]>
        <[AND grid_by = :grid_by]>
        <[AND grid_time = :grid_time]>
        <[AND package_sn = :package_sn]>
        <[AND source_from = :source_from]>
        <[AND sku_id in (:skuIdList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTemuPrepareOrderItemList" >
    <content >
      <![CDATA[
        SELECT id, prepare_order_id, skc, sku, label_code, prepare_quantity, allot_quantity, source_from,temu_code_url,
        pick_quantity, pick_by, pick_time, grid_quantity, grid_status, grid_by, grid_time, tag,merge_time,temu_tag_url,
        sku_id, sku_name, real_quantity, actual_weight, package_status, box_pick_time, deliver_time, deliver_by, load_time,
        package_sn
        FROM temu_prepare_order_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id in (:ids)]>
        <[AND prepare_order_id = :prepare_order_id]>
        <[AND skc = :skc]>
        <[AND sku = :sku]>
        <[AND label_code = :label_code]>
        <[AND prepare_quantity = :prepare_quantity]>
        <[AND allot_quantity = :allot_quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND pick_by = :pick_by]>
        <[AND pick_time = :pick_time]>
        <[AND grid_quantity = :grid_quantity]>
        <[AND grid_status = :grid_status]>
        <[AND grid_by = :grid_by]>
        <[AND grid_time = :grid_time]>
        <[AND package_sn = :package_sn]>
        <[AND source_from = :source_from]>
        <[AND sku_id in (:skuIdList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTemuPrepareOrderItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, prepare_order_id, skc, sku, label_code, prepare_quantity, allot_quantity, source_from,temu_code_url,
        pick_quantity, pick_by, pick_time, grid_quantity, grid_status, grid_by, grid_time, tag,merge_time,temu_tag_url,
        sku_id, sku_name, real_quantity, actual_weight, package_status, box_pick_time, deliver_time, deliver_by, load_time,
        package_sn
        FROM temu_prepare_order_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTemuPrepareOrderItem" >
    <content >
      <![CDATA[
        SELECT id, prepare_order_id, skc, sku, label_code, prepare_quantity, allot_quantity, source_from,temu_code_url,
        pick_quantity, pick_by, pick_time, grid_quantity, grid_status, grid_by, grid_time, tag,merge_time,temu_tag_url,
        sku_id, sku_name, real_quantity, actual_weight, package_status, box_pick_time, deliver_time, deliver_by, load_time,
        package_sn
        FROM temu_prepare_order_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id in (:ids)]>
        <[AND prepare_order_id = :prepare_order_id]>
        <[AND skc = :skc]>
        <[AND sku = :sku]>
        <[AND label_code = :label_code]>
        <[AND prepare_quantity = :prepare_quantity]>
        <[AND allot_quantity = :allot_quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND pick_by = :pick_by]>
        <[AND pick_time = :pick_time]>
        <[AND grid_quantity = :grid_quantity]>
        <[AND grid_status = :grid_status]>
        <[AND grid_by = :grid_by]>
        <[AND grid_time = :grid_time]>
        <[AND source_from = :source_from]>
        <[AND sku_id in (:skuIdList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createTemuPrepareOrderItem" >
    <content >
      <![CDATA[
        INSERT INTO temu_prepare_order_item (prepare_order_id, skc, sku, label_code, prepare_quantity, allot_quantity,source_from,
          pick_quantity, pick_by, pick_time, grid_quantity, grid_status, grid_by, grid_time, tag,merge_time, temu_code_url, temu_tag_url,
          sku_id, sku_name, real_quantity, actual_weight, package_status, package_sn, box_pick_time, deliver_time, deliver_by, load_time)
        VALUES (:prepare_order_id, :skc, :sku, :label_code, :prepare_quantity, :allot_quantity, :source_from,
          :pick_quantity, :pick_by, :pick_time, :grid_quantity, :grid_status, :grid_by, :grid_time, :tag,:merge_time, :temu_code_url, :temu_tag_url,
          :sku_id, :sku_name, :real_quantity, :actual_weight, :package_status, :package_sn, :box_pick_time, :deliver_time, :deliver_by, :load_time)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteTemuPrepareOrderItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM temu_prepare_order_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="deleteTemuPrepareOrderItems" >
    <content >
      <![CDATA[
        DELETE FROM temu_prepare_order_item
        WHERE 1 = 1
        <[AND id in (:ids)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateTemuPrepareOrderItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE temu_prepare_order_item
        SET <[prepare_order_id = :prepare_order_id,]>
          <[skc = :skc,]>
          <[sku_id = :sku_id,]>
          <[sku = :sku,]>
          <[sku_name = :sku_name,]>
          <[label_code = :label_code,]>
          <[prepare_quantity = :prepare_quantity,]>
          <[real_quantity = :real_quantity,]>
          <[allot_quantity = :allot_quantity,]>
          <[pick_quantity = :pick_quantity,]>
          <[pick_by = :pick_by,]>
          <[pick_time = :pick_time,]>
          <[grid_quantity = :grid_quantity,]>
          <[grid_status = :grid_status,]>
          <[grid_by = :grid_by,]>
          <[grid_time = :grid_time,]>
          <[package_sn = :package_sn,]>
          <[actual_weight = :actual_weight,]>
          <[package_status = :package_status,]>
          <[box_pick_time = :box_pick_time,]>
          <[deliver_time = :deliver_time,]>
          <[deliver_by = :deliver_by,]>
          <[load_time = :load_time,]>
          <[source_from = :source_from,]>
          <[pick_order_time = :pick_order_time,]>
          <[tag = :tag,]>
          <[merge_time = :merge_time,]>
          <[temu_code_url = :temu_code_url,]>
          <[temu_tag_url = :temu_tag_url,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="unPackCountInTemuTask" >
    <content >
      <![CDATA[
        SELECT
            COUNT(*)
        FROM
            wh_picking_task_item wpti
			LEFT JOIN wh_picking_task t ON t.id = wpti.task_id
            LEFT JOIN temu_prepare_order_item tpoi ON tpoi.package_sn = wpti.apv_no
            LEFT JOIN wh_fba_allocation wa ON wa.fba_no = wpti.apv_no
        WHERE
            (tpoi.package_status = 7 OR wa.status = 15)
            AND t.`task_status` != 5
            AND wpti.task_id IN ( SELECT task_id FROM wh_picking_task_item WHERE apv_no = :package_sn )
           <[AND t.task_type = :task_type]>
      ]]>
    </content>
  </sql>
</sqlmap>