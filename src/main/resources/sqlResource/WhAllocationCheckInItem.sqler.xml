<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhAllocationCheckInItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_allocation_check_in_item
        WHERE 1 = 1
        <[AND item_id = :item_id]>
        <[AND in_id = :in_id]>
        <[AND sku_id = :sku_id]>
        <[AND sku = :sku]>
        <[AND box_quantity = :box_quantity]>
        <[AND quantity = :quantity]>
        <[AND exception_quantity = :exception_quantity]>
        <[AND qc_quantity = :qc_quantity]>
        <[AND qc_exception_quantity = :qc_exception_quantity]>
        <[AND up_quantity = :up_quantity]>
        <[AND purchase_price = :purchase_price]>
        <[AND comment = :comment]>
        <[AND exception_type = :exception_type]>
        <[AND qc_exception_type = :qc_exception_type]>
        <[AND location = :location]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAllocationCheckInItemList" >
    <content >
      <![CDATA[
        SELECT item_id, in_id, sku_id, sku, box_quantity, quantity, exception_quantity, qc_quantity, 
        qc_exception_quantity, up_quantity, purchase_price, comment, exception_type, qc_exception_type, 
        location
        FROM wh_allocation_check_in_item
        WHERE 1 = 1
        <[AND item_id = :item_id]>
        <[AND in_id = :in_id]>
        <[AND sku_id = :sku_id]>
        <[AND sku = :sku]>
        <[AND box_quantity = :box_quantity]>
        <[AND quantity = :quantity]>
        <[AND exception_quantity = :exception_quantity]>
        <[AND qc_quantity = :qc_quantity]>
        <[AND qc_exception_quantity = :qc_exception_quantity]>
        <[AND up_quantity = :up_quantity]>
        <[AND purchase_price = :purchase_price]>
        <[AND comment = :comment]>
        <[AND exception_type = :exception_type]>
        <[AND qc_exception_type = :qc_exception_type]>
        <[AND location = :location]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAllocationCheckInItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT item_id, in_id, sku_id, sku, box_quantity, quantity, exception_quantity, qc_quantity, 
        qc_exception_quantity, up_quantity, purchase_price, comment, exception_type, qc_exception_type, 
        location
        FROM wh_allocation_check_in_item
        WHERE 1 = 1
        AND item_id = :item_id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAllocationCheckInItem" >
    <content >
      <![CDATA[
        SELECT item_id, in_id, sku_id, sku, box_quantity, quantity, exception_quantity, qc_quantity, 
        qc_exception_quantity, up_quantity, purchase_price, comment, exception_type, qc_exception_type, 
        location
        FROM wh_allocation_check_in_item
        WHERE 1 = 1
        <[AND item_id = :item_id]>
        <[AND in_id = :in_id]>
        <[AND sku_id = :sku_id]>
        <[AND sku = :sku]>
        <[AND box_quantity = :box_quantity]>
        <[AND quantity = :quantity]>
        <[AND exception_quantity = :exception_quantity]>
        <[AND qc_quantity = :qc_quantity]>
        <[AND qc_exception_quantity = :qc_exception_quantity]>
        <[AND up_quantity = :up_quantity]>
        <[AND purchase_price = :purchase_price]>
        <[AND comment = :comment]>
        <[AND exception_type = :exception_type]>
        <[AND qc_exception_type = :qc_exception_type]>
        <[AND location = :location]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhAllocationCheckInItem" >
    <content >
      <![CDATA[
        INSERT INTO wh_allocation_check_in_item (in_id, sku_id, sku, box_quantity, quantity, exception_quantity, qc_quantity, 
          qc_exception_quantity, up_quantity, purchase_price, comment, exception_type, 
          qc_exception_type, location, stock_id)
        VALUES (:in_id, :sku_id, :sku, :box_quantity, :quantity, :exception_quantity, :qc_quantity, 
          :qc_exception_quantity, :up_quantity, :purchase_price, :comment, :exception_type, 
          :qc_exception_type, :location, :stock_id)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhAllocationCheckInItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_allocation_check_in_item
        WHERE 1 = 1
        AND item_id = :item_id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhAllocationCheckInItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_allocation_check_in_item
        SET <[in_id = :in_id,]>
          <[sku_id = :sku_id,]>
          <[sku = :sku,]>
          <[box_quantity = :box_quantity,]>
          <[quantity = :quantity,]>
          <[exception_quantity = :exception_quantity,]>
          <[qc_quantity = :qc_quantity,]>
          <[qc_exception_quantity = :qc_exception_quantity,]>
          <[up_quantity = :up_quantity,]>
          <[purchase_price = :purchase_price,]>
          <[comment = :comment,]>
          <[exception_type = :exception_type,]>
          <[qc_exception_type = :qc_exception_type,]>
          <[location = :location,]>
          <[stock_id = :stock_id,]>
        item_id = item_id
        WHERE 1 = 1
        AND item_id = :item_id
      ]]>
    </content>
  </sql>
</sqlmap>