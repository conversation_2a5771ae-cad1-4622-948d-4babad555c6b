<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryReturnFormOrderCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM return_form_order
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_form_no = :return_form_no]>
        <[AND supplier = :supplier]>
        <[AND shipping_method = :shipping_method]>
        <[AND shipping_company = :shipping_company]>
        <[AND shipping_order_no = :shipping_order_no]>
        <[AND return_shipping = :return_shipping]>
        <[AND freight_carrier = :freight_carrier]>
        <[AND pay_way = :pay_way]>
        <[AND return_address = :return_address]>
        <[AND receiver = :receiver]>
        <[AND receiver_phone = :receiver_phone]>
        <[AND remark = :remark]>
        <[AND push_by = :push_by]>
        <[AND push_by_name = :push_by_name]>
        <[AND push_time = :push_time]>
        <[AND pick_by = :pick_by]>
        <[AND pick_end_time = :pick_end_time]>
        <[AND box_by = :box_by]>
        <[AND box_time = :box_time]>
        <[AND ship_by = :ship_by]>
        <[AND ship_time = :ship_time]>
        <[AND status = :status]>
        <[AND back_remark = :back_remark]>
        <[AND return_goods_type = :return_goods_type]>
        <[AND id IN (:idList)]>
        <[AND grid_by = :grid_by]>
        <[AND grid_time = :grid_time]>
        <[AND back_order_no = :back_order_no]>
        <[AND back_method = :back_method]>
        <[AND vendor_code = :vendor_code]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryReturnFormOrderList" >
    <content >
      <![CDATA[
        SELECT id, return_form_no, supplier, shipping_method, shipping_company, shipping_order_no, wms_to_pms_time,
        return_shipping, freight_carrier, pay_way, return_address, receiver, receiver_phone, back_order_no,back_method,
        remark, push_by, push_by_name, push_time, pick_by, pick_end_time, box_by, box_time, vendor_code,
        ship_by, ship_time, status, back_remark, update_stock, return_goods_type, grid_time, grid_by,volume_weight,volume_weight_cost,
        wms_checked_flag, wms_check_user, wms_check_date
        FROM return_form_order
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_form_no = :return_form_no]>
        <[AND supplier = :supplier]>
        <[AND shipping_method = :shipping_method]>
        <[AND shipping_company = :shipping_company]>
        <[AND shipping_order_no = :shipping_order_no]>
        <[AND return_shipping = :return_shipping]>
        <[AND freight_carrier = :freight_carrier]>
        <[AND pay_way = :pay_way]>
        <[AND return_address = :return_address]>
        <[AND receiver = :receiver]>
        <[AND receiver_phone = :receiver_phone]>
        <[AND remark = :remark]>
        <[AND push_by = :push_by]>
        <[AND push_by_name = :push_by_name]>
        <[AND push_time = :push_time]>
        <[AND pick_by = :pick_by]>
        <[AND pick_end_time = :pick_end_time]>
        <[AND box_by = :box_by]>
        <[AND box_time = :box_time]>
        <[AND ship_by = :ship_by]>
        <[AND ship_time = :ship_time]>
        <[AND status = :status]>
        <[AND back_remark = :back_remark]>
        <[AND return_goods_type = :return_goods_type]>
        <[AND id IN (:idList)]>
        <[AND grid_by = :grid_by]>
        <[AND grid_time = :grid_time]>
        <[AND back_order_no = :back_order_no]>
        <[AND back_method = :back_method]>
        <[AND vendor_code = :vendor_code]>
      ]]>
    </content>
  </sql>


  <sql datasource="dataSource" id="queryReturnFormOrderAndItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(rfo.id)
        FROM return_form_order rfo
        WHERE 1 = 1
        <[AND rfo.id = :id]>
        <[AND rfo.return_form_no = :return_form_no]>
        <[AND rfo.supplier = :supplier]>
        <[AND rfo.shipping_method = :shipping_method]>
        <[AND rfo.shipping_company = :shipping_company]>
        <[AND rfo.shipping_order_no = :shipping_order_no]>
        <[AND rfo.shipping_order_no in (:shippingOrderNoList)]>
        <[AND rfo.return_shipping = :return_shipping]>
        <[AND rfo.freight_carrier = :freight_carrier]>
        <[AND rfo.pay_way = :pay_way]>
        <[AND rfo.return_address = :return_address]>
        <[AND rfo.receiver = :receiver]>
        <[AND rfo.receiver_phone = :receiver_phone]>
        <[AND rfo.remark = :remark]>
        <[AND rfo.push_by = :push_by]>
        <[AND rfo.push_by_name = :push_by_name]>
        <[AND rfo.push_time = :push_time]>
        <[AND rfo.pick_by = :pick_by]>
        <[AND rfo.pick_end_time = :pick_end_time]>
        <[AND rfo.box_by = :box_by]>
        <[AND rfo.box_time = :box_time]>
        <[AND rfo.ship_by = :ship_by]>
        <[AND rfo.ship_time = :ship_time]>
        <[AND rfo.status = :status]>
        <[AND rfo.back_remark = :back_remark]>
        <[AND rfo.return_goods_type = :return_goods_type]>
        <[AND rfo.vendor_code = :vendor_code]>

        <[AND rfo.return_form_no IN (:returnFormNoList)]>
        <[AND rfo.id IN (:idList)]>
        <[AND rfo.status IN (:statusList)]>
        <[AND rfo.id IN (SELECT rfo_id FROM return_form_order_item WHERE sku IN (:skuList))]>
        <[AND rfo.id IN (SELECT rfo_id FROM return_form_order_item WHERE sku = :sku)]>
        <[AND rfo.push_time >= :fromPushDate]>
        <[AND rfo.push_time <= :toPushDate]>
        <[AND rfo.pick_end_time >= :fromPickDate]>
        <[AND rfo.pick_end_time <= :toPickDate]>
        <[AND rfo.box_time >= :fromBoxDate]>
        <[AND rfo.box_time <= :toBoxDate]>
        <[AND rfo.ship_time >= :fromShipDate]>
        <[AND rfo.ship_time <= :toShipDate]>
        <[AND rfo.grid_by = :grid_by]>
        <[AND rfo.grid_time >= :fromGridDate]>
        <[AND rfo.grid_time <= :toGridDate]>
        <[AND rfo.back_order_no = :back_order_no]>
        <[AND rfo.back_method = :back_method]>
        <[AND rfo.back_order_no IN (:backOrderNoList)]>
        <[:BACKNO_OR_RFONO_QUREY]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryReturnFormOrderAndItems" >
    <content >
      <![CDATA[
        SELECT rfo.id, rfo.return_form_no, rfo.supplier, rfo.shipping_method, rfo.shipping_company, rfo.shipping_order_no,
        rfo.return_shipping, rfo.freight_carrier, rfo.pay_way, rfo.return_address, rfo.receiver, rfo.receiver_phone, rfo.wms_to_pms_time,
        rfo.remark, rfo.push_by, rfo.push_by_name, rfo.push_time, rfo.pick_by, rfo.pick_end_time, rfo.box_by, rfo.box_time,
        rfo.ship_by, rfo.ship_time, rfo.status, rfo.back_remark, rfo.update_stock, rfo.return_goods_type,rfo.grid_time, rfo.grid_by, rfoi.grid_quantity,
        rfoi.id, rfoi.rfo_id, rfoi.box_no, rfoi.warehouse_id, rfoi.sku, rfoi.location, rfoi.quantity, rfoi.loading_quantity,rfoi.url,rfoi.remark,
        rfoi.allot_quantity, rfoi.pick_quantity, rfoi.product_length, rfoi.product_width, rfoi.product_height, rfoi.product_weight,
        rfo.volume_weight,rfo.volume_weight_cost, rfo.back_order_no,rfo.back_method,rfo.vendor_code,rfo.wms_checked_flag,rfo.wms_check_user,
        rfo.wms_check_date
        FROM return_form_order rfo
        LEFT JOIN return_form_order_item rfoi ON rfoi.rfo_id = rfo.id
        INNER JOIN (
			select rfo.id
			from return_form_order rfo
			WHERE 1 = 1
	        <[AND rfo.id = :id]>
            <[AND rfo.return_form_no = :return_form_no]>
            <[AND rfo.supplier = :supplier]>
            <[AND rfo.shipping_method = :shipping_method]>
            <[AND rfo.shipping_company = :shipping_company]>
            <[AND rfo.shipping_order_no = :shipping_order_no]>
            <[AND rfo.shipping_order_no in (:shippingOrderNoList)]>
            <[AND rfo.return_shipping = :return_shipping]>
            <[AND rfo.freight_carrier = :freight_carrier]>
            <[AND rfo.pay_way = :pay_way]>
            <[AND rfo.return_address = :return_address]>
            <[AND rfo.receiver = :receiver]>
            <[AND rfo.receiver_phone = :receiver_phone]>
            <[AND rfo.remark = :remark]>
            <[AND rfo.push_by = :push_by]>
            <[AND rfo.push_by_name = :push_by_name]>
            <[AND rfo.push_time = :push_time]>
            <[AND rfo.pick_by = :pick_by]>
            <[AND rfo.pick_end_time = :pick_end_time]>
            <[AND rfo.box_by = :box_by]>
            <[AND rfo.box_time = :box_time]>
            <[AND rfo.ship_by = :ship_by]>
            <[AND rfo.ship_time = :ship_time]>
            <[AND rfo.status = :status]>
            <[AND rfo.back_remark = :back_remark]>
            <[AND rfo.return_goods_type = :return_goods_type]>
            <[AND rfo.vendor_code = :vendor_code]>

            <[AND rfo.return_form_no IN (:returnFormNoList)]>
            <[AND rfo.id IN (:idList)]>
            <[AND rfo.status IN (:statusList)]>
            <[AND rfo.id IN (SELECT rfo_id FROM return_form_order_item WHERE sku IN (:skuList))]>
            <[AND rfo.id IN (SELECT rfo_id FROM return_form_order_item WHERE sku = :sku)]>
            <[AND rfo.push_time >= :fromPushDate]>
            <[AND rfo.push_time <= :toPushDate]>
            <[AND rfo.pick_end_time >= :fromPickDate]>
            <[AND rfo.pick_end_time <= :toPickDate]>
            <[AND rfo.box_time >= :fromBoxDate]>
            <[AND rfo.box_time <= :toBoxDate]>
            <[AND rfo.ship_time >= :fromShipDate]>
            <[AND rfo.ship_time <= :toShipDate]>
            <[AND rfo.grid_by = :grid_by]>
            <[AND rfo.grid_time >= :fromGridDate]>
            <[AND rfo.grid_time <= :toGridDate]>
            <[AND rfo.back_order_no = :back_order_no]>
            <[AND rfo.back_method = :back_method]>
            <[AND rfo.back_order_no IN (:backOrderNoList)]>
            <[:BACKNO_OR_RFONO_QUREY]>
            ORDER BY rfo.id DESC
	        <[:LIMIT]>
		) wfa on rfo.id = wfa.id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryReturnFormOrderByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, return_form_no, supplier, shipping_method, shipping_company, shipping_order_no, 
        return_shipping, freight_carrier, pay_way, return_address, receiver, receiver_phone, wms_to_pms_time,
        remark, push_by, push_by_name, push_time, pick_by, pick_end_time, box_by, box_time,
        ship_by, ship_time, status, back_remark, update_stock, return_goods_type, grid_time, grid_by,
        volume_weight,volume_weight_cost, back_order_no,back_method,vendor_code,wms_checked_flag, wms_check_user, wms_check_date
        FROM return_form_order
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryReturnFormOrder" >
    <content >
      <![CDATA[
        SELECT id, return_form_no, supplier, shipping_method, shipping_company, shipping_order_no, 
        return_shipping, freight_carrier, pay_way, return_address, receiver, receiver_phone, 
        remark, push_by, push_by_name, push_time, pick_by, pick_end_time, box_by, box_time, wms_to_pms_time,
        ship_by, ship_time, status, back_remark, update_stock, return_goods_type, grid_time, grid_by,
        volume_weight,volume_weight_cost, back_order_no,back_method,vendor_code,wms_checked_flag, wms_check_user, wms_check_date
        FROM return_form_order
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_form_no = :return_form_no]>
        <[AND supplier = :supplier]>
        <[AND shipping_method = :shipping_method]>
        <[AND shipping_company = :shipping_company]>
        <[AND shipping_order_no = :shipping_order_no]>
        <[AND return_shipping = :return_shipping]>
        <[AND freight_carrier = :freight_carrier]>
        <[AND pay_way = :pay_way]>
        <[AND return_address = :return_address]>
        <[AND receiver = :receiver]>
        <[AND receiver_phone = :receiver_phone]>
        <[AND remark = :remark]>
        <[AND push_by = :push_by]>
        <[AND push_by_name = :push_by_name]>
        <[AND push_time = :push_time]>
        <[AND pick_by = :pick_by]>
        <[AND pick_end_time = :pick_end_time]>
        <[AND box_by = :box_by]>
        <[AND box_time = :box_time]>
        <[AND ship_by = :ship_by]>
        <[AND ship_time = :ship_time]>
        <[AND grid_time = :grid_time]>
        <[AND grid_by = :grid_by]>
        <[AND status = :status]>
        <[AND back_remark = :back_remark]>
        <[AND return_goods_type = :return_goods_type]>
        <[AND back_method = :back_method]>
        <[AND back_order_no = :back_order_no]>
        <[AND vendor_code = :vendor_code]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createReturnFormOrder" >
    <content >
      <![CDATA[
        INSERT INTO return_form_order (return_form_no, supplier, shipping_method, shipping_company, shipping_order_no, vendor_code,
          return_shipping, freight_carrier, pay_way, return_address, receiver, receiver_phone, return_goods_type,
          remark, push_by, push_by_name, push_time, pick_by, pick_end_time, box_by, update_stock,wms_to_pms_time,
          box_time, ship_by, ship_time, status, back_remark, grid_time, grid_by,volume_weight,volume_weight_cost, back_order_no,back_method)
        VALUES (:return_form_no, :supplier, :shipping_method, :shipping_company, :shipping_order_no, :vendor_code,
          :return_shipping, :freight_carrier, :pay_way, :return_address, :receiver, :receiver_phone, :return_goods_type,
          :remark, :push_by, :push_by_name, :push_time, :pick_by, :pick_end_time, :box_by, :update_stock, :wms_to_pms_time,
          :box_time, :ship_by, :ship_time, :status, :back_remark, :grid_time, :grid_by,:volume_weight,:volume_weight_cost, :back_order_no,:back_method)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteReturnFormOrderByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM return_form_order
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateReturnFormOrderByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE return_form_order
        SET <[return_form_no = :return_form_no,]>
          <[supplier = :supplier,]>
          <[shipping_method = :shipping_method,]>
          <[shipping_company = :shipping_company,]>
          <[shipping_order_no = :shipping_order_no,]>
          <[return_shipping = :return_shipping,]>
          <[freight_carrier = :freight_carrier,]>
          <[pay_way = :pay_way,]>
          <[return_address = :return_address,]>
          <[receiver = :receiver,]>
          <[receiver_phone = :receiver_phone,]>
          <[remark = :remark,]>
          <[push_by = :push_by,]>
          <[push_by_name = :push_by_name,]>
          <[push_time = :push_time,]>
          <[pick_by = :pick_by,]>
          <[pick_end_time = :pick_end_time,]>
          <[box_by = :box_by,]>
          <[box_time = :box_time,]>
          <[ship_by = :ship_by,]>
          <[ship_time = :ship_time,]>
          <[status = :status,]>
          <[back_remark = :back_remark,]>
          <[update_stock = :update_stock,]>
          <[grid_by = :grid_by,]>
          <[grid_time = :grid_time,]>
          <[wms_to_pms_time = :wms_to_pms_time,]>
          <[return_goods_type = :return_goods_type,]>
          <[volume_weight = :volume_weight,]>
          <[back_order_no = :back_order_no,]>
          <[back_method = :back_method,]>
          <[volume_weight_cost = :volume_weight_cost,]>
          <[volume_weight = null, id=:update_weight_id,]>
          <[volume_weight_cost = null, id=:update_weight_id,]>
          <[vendor_code = :vendor_code,]>
          <[wms_checked_flag = :wms_checked_flag,]>
          <[wms_check_user = :wms_check_user,]>
          <[wms_check_date = :wms_check_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>