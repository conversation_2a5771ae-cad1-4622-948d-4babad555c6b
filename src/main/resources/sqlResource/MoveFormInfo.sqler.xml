<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryMoveFormInfoCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM move_form_info
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND move_id = :move_id]>
        <[AND old_location = :old_location]>
        <[AND new_location = :new_location]>
        <[AND plan_num = :plan_num]>
        <[AND reality_num = :reality_num]>
        <[AND sku = :sku]>
        <[AND name = :name]>
        <[AND box_num = :box_num]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryMoveFormInfoList" >
    <content >
      <![CDATA[
        SELECT id, move_id, old_location, new_location, plan_num, reality_num, sku, name, box_num, diff_num,up_status
        FROM move_form_info
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND move_id = :move_id]>
        <[AND move_id IN (:moveIdList)]>
        <[AND old_location = :old_location]>
        <[AND new_location = :new_location]>
        <[AND plan_num = :plan_num]>
        <[AND reality_num = :reality_num]>
        <[AND sku = :sku]>
        <[AND name = :name]>
        <[AND box_num = :box_num]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryMoveFormInfoByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, move_id, old_location, new_location, plan_num, reality_num, sku,name, box_num, diff_num,up_status
        FROM move_form_info
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryMoveFormInfo" >
    <content >
      <![CDATA[
        SELECT id, move_id, old_location, new_location, plan_num, reality_num, sku,name,box_num, diff_num,up_status
        FROM move_form_info
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND move_id = :move_id]>
        <[AND old_location = :old_location]>
        <[AND new_location = :new_location]>
        <[AND plan_num = :plan_num]>
        <[AND reality_num = :reality_num]>
        <[AND sku = :sku]>
        <[AND name = :name]>
        <[AND box_num = :box_num]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createMoveFormInfo" >
    <content >
      <![CDATA[
        INSERT INTO move_form_info (move_id, old_location, new_location, plan_num, reality_num, sku,name,box_num, diff_num ,up_status)
        VALUES (:move_id, :old_location, :new_location, :plan_num, :reality_num, :sku,:name,:box_num, :diff_num ,:up_status)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteMoveFormInfoByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM move_form_info
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

    <sql datasource="dataSource" id="deleteMoveFormInfoByPrimaryKeys" >
        <content >
            <![CDATA[
        DELETE FROM move_form_info
        WHERE 1 = 1
        AND id IN (:ids)
      ]]>
        </content>
    </sql>
  
  <sql datasource="dataSource" id="updateMoveFormInfoByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE move_form_info
        SET <[move_id = :move_id,]>
          <[old_location = :old_location,]>
          <[new_location = :new_location,]>
          <[plan_num = :plan_num,]>
          <[reality_num = :reality_num,]>
          <[diff_num = :diff_num,]>
          <[sku = :sku,]>
          <[name = :name,]>
          <[box_num = :box_num,]>
          <[up_status = :up_status,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

    <sql datasource="dataSource" id="queryRelevanceMoveForm" >
        <content >
            <![CDATA[
        SELECT COUNT(1)
        FROM move_form_info mofi right join (
            select DISTINCT move_id from move_form mf
            where 1 = 1
        <[AND mf.state IN (:states)]>
        <[AND mf.move_id != :moveIdNo]>
        ) qmfi on qmfi.move_id = mofi.move_id
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND move_id = :move_id]>
        <[AND old_location = :old_location]>
        <[AND new_location = :newLocations]>
        <[AND plan_num = :plan_num]>
        <[AND reality_num = :reality_num]>
        <[AND sku = :sku]>
        <[AND name = :name]>
        <[AND box_num = :box_num]>
      ]]>
        </content>
    </sql>
</sqlmap>