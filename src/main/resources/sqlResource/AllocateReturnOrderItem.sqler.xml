<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryAllocateReturnOrderItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM allocate_return_order_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND return_id = :return_id]>
        <[AND quantity = :quantity]>
        <[AND mate_quantity = :mate_quantity]>
        <[AND scan_quantity = :scan_quantity]>
        <[AND status = :status]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAllocateReturnOrderItemList" >
    <content >
      <![CDATA[
        SELECT id, sku, return_id, quantity, mate_quantity, scan_quantity, status, remark, 
        creation_date, create_by, last_update_date, last_updated_by
        FROM allocate_return_order_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND return_id = :return_id]>
        <[AND quantity = :quantity]>
        <[AND mate_quantity = :mate_quantity]>
        <[AND scan_quantity = :scan_quantity]>
        <[AND status = :status]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAllocateReturnOrderItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, sku, return_id, quantity, mate_quantity, scan_quantity, status, remark, 
        creation_date, create_by, last_update_date, last_updated_by
        FROM allocate_return_order_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAllocateReturnOrderItem" >
    <content >
      <![CDATA[
        SELECT id, sku, return_id, quantity, mate_quantity, scan_quantity, status, remark, 
        creation_date, create_by, last_update_date, last_updated_by
        FROM allocate_return_order_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND return_id = :return_id]>
        <[AND quantity = :quantity]>
        <[AND mate_quantity = :mate_quantity]>
        <[AND scan_quantity = :scan_quantity]>
        <[AND status = :status]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createAllocateReturnOrderItem" >
    <content >
      <![CDATA[
        INSERT INTO allocate_return_order_item (sku, return_id, quantity, mate_quantity, scan_quantity, status, remark, 
          creation_date, create_by, last_update_date, last_updated_by)
        VALUES (:sku, :return_id, :quantity, :mate_quantity, :scan_quantity, :status, :remark, 
          :creation_date, :create_by, :last_update_date, :last_updated_by)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteAllocateReturnOrderItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM allocate_return_order_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateAllocateReturnOrderItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE allocate_return_order_item
        SET <[sku = :sku,]>
          <[return_id = :return_id,]>
          <[quantity = :quantity,]>
          <[mate_quantity = :mate_quantity,]>
          <[scan_quantity = :scan_quantity,]>
          <[status = :status,]>
          <[remark = :remark,]>
          <[creation_date = :creation_date,]>
          <[create_by = :create_by,]>
          <[last_update_date = :last_update_date,]>
          <[last_updated_by = :last_updated_by,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>