<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryJitPickBoxItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM jit_pick_box_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND box_id = :box_id]>
        <[AND fba_id = :fba_id]>
        <[AND status = :status]>
        <[AND pick_by = :pick_by,]>
        <[AND pick_time = :pick_time,]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryJitPickBoxItemList" >
    <content >
      <![CDATA[
        SELECT id, box_id, fba_id, status,pick_by,pick_time
        FROM jit_pick_box_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND box_id = :box_id]>
        <[AND fba_id = :fba_id]>
        <[AND status = :status]>
        <[AND pick_by = :pick_by,]>
        <[AND pick_time = :pick_time,]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryJitPickBoxItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, box_id, fba_id, status,pick_by,pick_time
        FROM jit_pick_box_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryJitPickBoxItem" >
    <content >
      <![CDATA[
        SELECT id, box_id, fba_id, status,pick_by,pick_time
        FROM jit_pick_box_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND box_id = :box_id]>
        <[AND fba_id = :fba_id]>
        <[AND status = :status]>
        <[AND pick_by = :pick_by,]>
        <[AND pick_time = :pick_time,]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createJitPickBoxItem" >
    <content >
      <![CDATA[
        INSERT INTO jit_pick_box_item (box_id, fba_id, status,pick_by,pick_time)
        VALUES (:box_id, :fba_id, :status,:pick_by,:pick_time)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteJitPickBoxItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM jit_pick_box_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateJitPickBoxItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE jit_pick_box_item
        SET <[box_id = :box_id,]>
          <[fba_id = :fba_id,]>
          <[status = :status,]>
          <[pick_by = :pick_by,]>
          <[pick_time = :pick_time,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>