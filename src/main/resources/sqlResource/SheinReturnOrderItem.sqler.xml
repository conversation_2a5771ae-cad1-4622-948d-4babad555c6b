<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="querySheinReturnOrderItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM shein_return_order_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_id = :return_id]>
        <[AND return_no = :return_no]>
        <[AND skc = :skc]>
        <[AND platform_sku = :platform_sku]>
        <[AND merchant_sku = :merchant_sku]>
        <[AND sku = :sku]>
        <[AND attr_collect = :attr_collect]>
        <[AND merchant_item_no = :merchant_item_no]>
        <[AND order_no = :order_no]>
        <[AND deliver_order_no = :deliver_order_no]>
        <[AND return_quantity = :return_quantity]>
        <[AND update_time = :update_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySheinReturnOrderItemList" >
    <content >
      <![CDATA[
        SELECT id, return_id, return_no, skc, platform_sku, merchant_sku, sku, attr_collect, 
        merchant_item_no, order_no, deliver_order_no, return_quantity, update_time
        FROM shein_return_order_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_id = :return_id]>
        <[AND return_no = :return_no]>
        <[AND skc = :skc]>
        <[AND platform_sku = :platform_sku]>
        <[AND merchant_sku = :merchant_sku]>
        <[AND sku = :sku]>
        <[AND attr_collect = :attr_collect]>
        <[AND merchant_item_no = :merchant_item_no]>
        <[AND order_no = :order_no]>
        <[AND deliver_order_no = :deliver_order_no]>
        <[AND return_quantity = :return_quantity]>
        <[AND update_time = :update_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySheinReturnOrderItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, return_id, return_no, skc, platform_sku, merchant_sku, sku, attr_collect, 
        merchant_item_no, order_no, deliver_order_no, return_quantity, update_time
        FROM shein_return_order_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySheinReturnOrderItem" >
    <content >
      <![CDATA[
        SELECT id, return_id, return_no, skc, platform_sku, merchant_sku, sku, attr_collect, 
        merchant_item_no, order_no, deliver_order_no, return_quantity, update_time
        FROM shein_return_order_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_id = :return_id]>
        <[AND return_no = :return_no]>
        <[AND skc = :skc]>
        <[AND platform_sku = :platform_sku]>
        <[AND merchant_sku = :merchant_sku]>
        <[AND sku = :sku]>
        <[AND attr_collect = :attr_collect]>
        <[AND merchant_item_no = :merchant_item_no]>
        <[AND order_no = :order_no]>
        <[AND deliver_order_no = :deliver_order_no]>
        <[AND return_quantity = :return_quantity]>
        <[AND update_time = :update_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createSheinReturnOrderItem" >
    <content >
      <![CDATA[
        INSERT INTO shein_return_order_item (return_id, return_no, skc, platform_sku, merchant_sku, sku, attr_collect, 
          merchant_item_no, order_no, deliver_order_no, return_quantity, update_time
          )
        VALUES (:return_id, :return_no, :skc, :platform_sku, :merchant_sku, :sku, :attr_collect, 
          :merchant_item_no, :order_no, :deliver_order_no, :return_quantity, :update_time
          )
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteSheinReturnOrderItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM shein_return_order_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateSheinReturnOrderItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE shein_return_order_item
        SET <[return_id = :return_id,]>
          <[return_no = :return_no,]>
          <[skc = :skc,]>
          <[platform_sku = :platform_sku,]>
          <[merchant_sku = :merchant_sku,]>
          <[sku = :sku,]>
          <[attr_collect = :attr_collect,]>
          <[merchant_item_no = :merchant_item_no,]>
          <[order_no = :order_no,]>
          <[deliver_order_no = :deliver_order_no,]>
          <[return_quantity = :return_quantity,]>
          <[update_time = :update_time,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateItemReturnIdByReturnNo" >
    <content >
      <![CDATA[
        UPDATE shein_return_order_item SET return_id = :return_id WHERE return_no = :return_no
      ]]>
    </content>
  </sql>
</sqlmap>