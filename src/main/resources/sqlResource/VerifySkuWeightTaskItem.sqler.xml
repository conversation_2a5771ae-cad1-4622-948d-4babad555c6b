<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryVerifySkuWeightTaskItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM verify_sku_weight_task_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND sku = :sku]>
        <[AND sku IN (:skus)]>
        <[AND status = :status]>
        <[AND status IN (:statuses)]>
        <[AND weight = :weight]>
        <[AND weighing_weight = :weighing_weight]>
        <[AND weight_difference = :weight_difference]>
        <[AND created_by = :created_by]>
        <[AND created_date = :created_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND weighing_date = :weighing_date]>
        <[AND weighing_by = :weighing_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryVerifySkuWeightTaskItemList" >
    <content >
      <![CDATA[
        SELECT id, task_id, sku, status, weight, weighing_weight, weight_difference,
        created_by, created_date, last_update_date, last_update_by, weighing_date, weighing_by
        FROM verify_sku_weight_task_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND sku = :sku]>
        <[AND sku IN (:skus)]>
        <[AND status = :status]>
        <[AND status IN (:statuses)]>
        <[AND weight = :weight]>
        <[AND weighing_weight = :weighing_weight]>
        <[AND weight_difference = :weight_difference]>
        <[AND created_by = :created_by]>
        <[AND created_date = :created_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND weighing_date = :weighing_date]>
        <[AND weighing_by = :weighing_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryVerifySkuWeightTaskItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, task_id, sku, status, weight, weighing_weight, weight_difference,
        created_by, created_date, last_update_date, last_update_by, weighing_date, weighing_by
        FROM verify_sku_weight_task_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryVerifySkuWeightTaskItem" >
    <content >
      <![CDATA[
        SELECT id, task_id, sku, status, weight, weighing_weight, weight_difference,
        created_by, created_date, last_update_date, last_update_by, weighing_date, weighing_by
        FROM verify_sku_weight_task_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND sku = :sku]>
        <[AND status = :status]>
        <[AND weight = :weight]>
        <[AND weighing_weight = :weighing_weight]>
        <[AND weight_difference = :weight_difference]>
        <[AND created_by = :created_by]>
        <[AND created_date = :created_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND weighing_date = :weighing_date]>
        <[AND weighing_by = :weighing_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createVerifySkuWeightTaskItem" >
    <content >
      <![CDATA[
        INSERT INTO verify_sku_weight_task_item (task_id, sku, status, weight, weighing_weight, weight_difference,
          created_by, created_date, last_update_date, last_update_by, weighing_date, weighing_by,specification
          ,generate_by_verify_sku_weight_id)
        VALUES (:task_id, :sku, :status, :weight, :weighing_weight, :weight_difference,
          :created_by, :created_date, :last_update_date, :last_update_by, :weighing_date, :weighing_by
          , :specification , :generate_by_verify_sku_weight_id)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteVerifySkuWeightTaskItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM verify_sku_weight_task_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateVerifySkuWeightTaskItemByPrimaryKey" >
    <content >
      <![CDATA[
         UPDATE verify_sku_weight_task_item
        SET <[task_id = :task_id,]>
          <[sku = :sku,]>
          <[status = :status,]>
          <[weight = :weight,]>
          <[weighing_weight = :weighing_weight,]>
          <[weight_difference = :weight_difference,]>
          <[created_by = :created_by,]>
          <[created_date = :created_date,]>
          <[last_update_date = :last_update_date,]>
          <[last_update_by = :last_update_by,]>
          <[weighing_by = :weighing_by,]>
          <[weighing_date = :weighing_date,]>
          <[specification = :specification,]>
          <[packaging_attribute = :packaging_attribute,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>