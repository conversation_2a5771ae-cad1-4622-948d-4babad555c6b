<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="querySmtAidcSettlementCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM smt_aidc_settlement
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND biz_order_code = :biz_order_code]>
        <[AND biz_bill_code = :biz_bill_code]>
        <[AND biz_bill_code IN (:bizBillCodeList)]>
        <[AND seller_id = :seller_id]>
        <[AND settle_amount = :settle_amount]>
        <[AND apportion_charge_base_amount = :apportion_charge_base_amount]>
        <[AND weight = :weight]>
        <[AND theoretical_weight = :theoretical_weight]>
        <[AND seller_discount_rate = :seller_discount_rate]>
        <[AND discount_rate = :discount_rate]>
        <[AND apportion_ratio = :apportion_ratio]>
        <[AND creation_date = :creation_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySmtAidcSettlementList" >
    <content >
      <![CDATA[
        SELECT id, biz_order_code, biz_bill_code, seller_id, settle_amount, apportion_charge_base_amount, 
        weight, theoretical_weight, seller_discount_rate, discount_rate, apportion_ratio, 
        creation_date
        FROM smt_aidc_settlement
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND biz_order_code = :biz_order_code]>
        <[AND biz_bill_code = :biz_bill_code]>
        <[AND biz_bill_code IN (:bizBillCodeList)]>
        <[AND seller_id = :seller_id]>
        <[AND settle_amount = :settle_amount]>
        <[AND apportion_charge_base_amount = :apportion_charge_base_amount]>
        <[AND weight = :weight]>
        <[AND theoretical_weight = :theoretical_weight]>
        <[AND seller_discount_rate = :seller_discount_rate]>
        <[AND discount_rate = :discount_rate]>
        <[AND apportion_ratio = :apportion_ratio]>
        <[AND creation_date = :creation_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySmtAidcSettlementByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, biz_order_code, biz_bill_code, seller_id, settle_amount, apportion_charge_base_amount, 
        weight, theoretical_weight, seller_discount_rate, discount_rate, apportion_ratio, 
        creation_date
        FROM smt_aidc_settlement
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySmtAidcSettlement" >
    <content >
      <![CDATA[
        SELECT id, biz_order_code, biz_bill_code, seller_id, settle_amount, apportion_charge_base_amount, 
        weight, theoretical_weight, seller_discount_rate, discount_rate, apportion_ratio, 
        creation_date
        FROM smt_aidc_settlement
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND biz_order_code = :biz_order_code]>
        <[AND biz_bill_code = :biz_bill_code]>
        <[AND seller_id = :seller_id]>
        <[AND settle_amount = :settle_amount]>
        <[AND apportion_charge_base_amount = :apportion_charge_base_amount]>
        <[AND weight = :weight]>
        <[AND theoretical_weight = :theoretical_weight]>
        <[AND seller_discount_rate = :seller_discount_rate]>
        <[AND discount_rate = :discount_rate]>
        <[AND apportion_ratio = :apportion_ratio]>
        <[AND creation_date = :creation_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createSmtAidcSettlement" >
    <content >
      <![CDATA[
        INSERT INTO smt_aidc_settlement (biz_order_code, biz_bill_code, seller_id, settle_amount, apportion_charge_base_amount, 
          weight, theoretical_weight, seller_discount_rate, discount_rate, apportion_ratio, 
          creation_date)
        VALUES (:biz_order_code, :biz_bill_code, :seller_id, :settle_amount, :apportion_charge_base_amount, 
          :weight, :theoretical_weight, :seller_discount_rate, :discount_rate, :apportion_ratio, 
          :creation_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteSmtAidcSettlementByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM smt_aidc_settlement
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateSmtAidcSettlementByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE smt_aidc_settlement
        SET <[biz_order_code = :biz_order_code,]>
          <[biz_bill_code = :biz_bill_code,]>
          <[seller_id = :seller_id,]>
          <[settle_amount = :settle_amount,]>
          <[apportion_charge_base_amount = :apportion_charge_base_amount,]>
          <[weight = :weight,]>
          <[theoretical_weight = :theoretical_weight,]>
          <[seller_discount_rate = :seller_discount_rate,]>
          <[discount_rate = :discount_rate,]>
          <[apportion_ratio = :apportion_ratio,]>
          <[creation_date = :creation_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>