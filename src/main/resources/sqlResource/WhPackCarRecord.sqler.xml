<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhPackCarRecordCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_pack_car_record
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:ids)]>
        <[AND load_date = :load_date]>
        <[AND logistics_company_code = :logistics_company_code]>
        <[AND total_bag_num = :total_bag_num]>
        <[AND load_user = :load_user]>
        <[AND print_user = :print_user]>
        <[AND print_num = :print_num]>
         <[AND platform_type = :platform_type]>
        <[AND shipping_company_code = :shipping_company_code]>
        <[AND shipping_company_name = :shipping_company_name]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND load_no = :load_no]>
        <[AND status = :status]>
        <[AND delivery_type = :delivery_type]>
        <[AND shopee_pdf = :shopee_pdf]>
        <[AND load_date >= :from_load_date]>
        <[AND load_date <= :to_load_date]>
        <[AND print_num >= :than_quantity]>
        <[AND print_num <= :less_quantity]>
        <[AND id IN (select pack_car_id from wh_scan_shipment where pickup_order_no in (:pickupOrderNos))]>
        <[AND id IN (select pack_car_id from wh_scan_shipment where express_order_no in (:expressOrderNos))]>
        <[AND id IN (select f.load_id from wh_fba_allocation f LEFT JOIN  wh_fba_allocation_item i ON f.id=i.fba_id where i.temu_tag_url in (:lbxNoList))]>
        <[:FILTER_IS_TRANSFER)]>
        <[:QUERY_NANNING_PACK)]>
        <[:QUERY_NOT_NANNING_PACK)]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhPackCarRecordList" >
    <content >
      <![CDATA[
        SELECT id, load_date, logistics_company_code, total_bag_num, load_user, print_user, load_invoice_url, upload_user, is_transfer,
        print_num, shipping_company_code, shipping_company_name, warehouse_id, load_no, status, shopee_pdf, deliver_file_url, delivery_type, platform_type
        FROM wh_pack_car_record
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:ids)]>
        <[AND load_date = :load_date]>
        <[AND logistics_company_code = :logistics_company_code]>
        <[AND total_bag_num = :total_bag_num]>
        <[AND load_user = :load_user]>
        <[AND print_user = :print_user]>
        <[AND print_num = :print_num]>
        <[AND shipping_company_code = :shipping_company_code]>
        <[AND shipping_company_name = :shipping_company_name]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND load_no = :load_no]>
        <[AND load_date >= :from_load_date]>
        <[AND load_date <= :to_load_date]>
        <[AND status = :status]>
        <[AND delivery_type = :delivery_type]>
        <[AND platform_type = :platform_type]>
        <[AND shopee_pdf = :shopee_pdf]>
        <[AND print_num >= :than_quantity]>
        <[AND print_num <= :less_quantity]>
        <[AND id IN (select pack_car_id from wh_scan_shipment where pickup_order_no in (:pickupOrderNos))]>
        <[AND id IN (select pack_car_id from wh_scan_shipment where express_order_no in (:expressOrderNos))]>
         <[AND id IN (select f.load_id from wh_fba_allocation f LEFT JOIN  wh_fba_allocation_item i ON f.id=i.fba_id where i.temu_tag_url in (:lbxNoList))]>
        <[:FILTER_IS_TRANSFER)]>
        <[:QUERY_NANNING_PACK)]>
        <[:QUERY_NOT_NANNING_PACK)]>
        ORDER BY id DESC
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhPackCarRecordByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, load_date, logistics_company_code, total_bag_num, load_user, print_user, load_invoice_url, upload_user, is_transfer,
        print_num, shipping_company_code, shipping_company_name, warehouse_id, load_no, status, shopee_pdf, deliver_file_url, delivery_type, platform_type
        FROM wh_pack_car_record
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhPackCarRecord" >
    <content >
      <![CDATA[
        SELECT id, load_date, logistics_company_code, total_bag_num, load_user, print_user, load_invoice_url, upload_user, is_transfer,
        print_num, shipping_company_code, shipping_company_name, warehouse_id, load_no, status, shopee_pdf, deliver_file_url, delivery_type, platform_type
        FROM wh_pack_car_record
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND load_date = :load_date]>
        <[AND logistics_company_code = :logistics_company_code]>
        <[AND total_bag_num = :total_bag_num]>
        <[AND load_user = :load_user]>
         <[AND platform_type = :platform_type]>
        <[AND print_user = :print_user]>
        <[AND print_num = :print_num]>
        <[AND shipping_company_code = :shipping_company_code]>
        <[AND shipping_company_name = :shipping_company_name]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND load_no = :load_no]>
        <[AND status = :status]>
        <[AND delivery_type = :delivery_type]>
        <[AND shopee_pdf = :shopee_pdf]>
        <[AND id IN (select pack_car_id from wh_scan_shipment where pickup_order_no in (:pickupOrderNos))]>
        <[AND id IN (select pack_car_id from wh_scan_shipment where express_order_no in (:expressOrderNos))]>
        <[AND id IN (select f.load_id from wh_fba_allocation f LEFT JOIN  wh_fba_allocation_item i ON f.id=i.fba_id where temu_tag_url in (:expressOrderNos))]>
        <[:FILTER_IS_TRANSFER)]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="createWhPackCarRecord" >
    <content >
      <![CDATA[
        INSERT INTO wh_pack_car_record (load_date, logistics_company_code, total_bag_num, load_user, print_user, load_invoice_url, upload_user, is_transfer,
          print_num, shipping_company_code, shipping_company_name, warehouse_id, load_no, status, shopee_pdf, deliver_file_url, delivery_type, platform_type
          )
        VALUES (:load_date, :logistics_company_code, :total_bag_num, :load_user, :print_user, :load_invoice_url, :upload_user, :is_transfer,
          :print_num, :shipping_company_code, :shipping_company_name, :warehouse_id, :load_no, :status, :shopee_pdf, :deliver_file_url, :delivery_type, :platform_type
          )
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="deleteWhPackCarRecordByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_pack_car_record
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateWhPackCarRecordByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_pack_car_record
        SET <[load_date = :load_date,]>
          <[logistics_company_code = :logistics_company_code,]>
          <[total_bag_num = :total_bag_num,]>
          <[load_user = :load_user,]>
          <[print_user = :print_user,]>
          <[print_num = :print_num,]>
          <[shipping_company_code = :shipping_company_code,]>
          <[shipping_company_name = :shipping_company_name,]>
          <[warehouse_id = :warehouse_id,]>
          <[load_no = :load_no,]>
          <[status = :status,]>
          <[shopee_pdf = :shopee_pdf,]>
          <[deliver_file_url = :deliver_file_url,]>
          <[load_invoice_url = :load_invoice_url,]>
          <[upload_user = :upload_user,]>
          <[is_transfer = :is_transfer,]>
          <[delivery_type = :delivery_type,]>
          <[platform_type = :platform_type,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>