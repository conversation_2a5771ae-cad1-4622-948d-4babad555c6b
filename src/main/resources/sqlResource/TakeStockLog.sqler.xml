<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryTakeStockLogCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM take_stock_log
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND log_source = :log_source]>
        <[AND creation_date = :creation_date]>
        <[AND record_id = :record_id]>
        <[AND content = :content]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTakeStockLogList" >
    <content >
      <![CDATA[
        SELECT id, log_source, creation_date, record_id, content
        FROM <[:table_index]>
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND log_source = :log_source]>
        <[AND creation_date = :creation_date]>
        <[AND record_id = :record_id]>
        <[AND content = :content]>
        ORDER BY creation_date desc
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTakeStockLogByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, log_source, creation_date, record_id, content
        FROM take_stock_log
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTakeStockLog" >
    <content >
      <![CDATA[
        SELECT id, log_source, creation_date, record_id, content
        FROM take_stock_log
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND log_source = :log_source]>
        <[AND creation_date = :creation_date]>
        <[AND record_id = :record_id]>
        <[AND content = :content]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createTakeStockLog" >
    <content >
        <![CDATA[
            INSERT INTO <[:table_index]> (record_id, log_source, creation_date, content)
            VALUES (:record_id, :log_source, :creation_date,  :content)
        ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteTakeStockLogByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM take_stock_log
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateTakeStockLogByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE take_stock_log
        SET <[log_source = :log_source,]>
          <[creation_date = :creation_date,]>
          <[record_id = :record_id,]>
          <[content = :content,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>