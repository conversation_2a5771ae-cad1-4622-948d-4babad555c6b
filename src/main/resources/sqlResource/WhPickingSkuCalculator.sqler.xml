<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhPickingSkuCalculatorCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_picking_sku_calculator
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND task_sku_id = :task_sku_id]>
        <[AND task_sku_id in (:taskSkuIds)]>
        <[AND sku = :sku]>
        <[AND creation_date = :creation_date]>
        <[AND calculator_json = :calculator_json]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhPickingSkuCalculatorList" >
    <content >
      <![CDATA[
        SELECT id, task_id, task_sku_id, sku, creation_date, calculator_json
        FROM wh_picking_sku_calculator
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND task_sku_id = :task_sku_id]>
        <[AND task_sku_id in (:taskSkuIds)]>
        <[AND sku = :sku]>
        <[AND creation_date = :creation_date]>
        <[AND calculator_json = :calculator_json]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhPickingSkuCalculatorByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, task_id, task_sku_id, sku, creation_date, calculator_json
        FROM wh_picking_sku_calculator
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhPickingSkuCalculator" >
    <content >
      <![CDATA[
        SELECT id, task_id, task_sku_id, sku, creation_date, calculator_json
        FROM wh_picking_sku_calculator
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND task_sku_id = :task_sku_id]>
        <[AND sku = :sku]>
        <[AND creation_date = :creation_date]>
        <[AND calculator_json = :calculator_json]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhPickingSkuCalculator" >
    <content >
      <![CDATA[
        INSERT INTO wh_picking_sku_calculator (task_id, task_sku_id, sku, creation_date, calculator_json)
        VALUES (:task_id, :task_sku_id, :sku, :creation_date, :calculator_json)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhPickingSkuCalculatorByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_picking_sku_calculator
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhPickingSkuCalculatorByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_picking_sku_calculator
        SET <[task_id = :task_id,]>
          <[task_sku_id = :task_sku_id,]>
          <[sku = :sku,]>
          <[creation_date = :creation_date,]>
          <[calculator_json = :calculator_json,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="batchDeleteWhPickingSkuCalculatorByTaskSkuIds" >
    <content >
      <![CDATA[
        DELETE FROM wh_picking_sku_calculator
        WHERE 1 = 1
        AND task_sku_id IN (:taskSkuIds)
      ]]>
    </content>
  </sql>


  <sql datasource="dataSource" id="updateWhPickingSkuCalculatorByTaskSkuId" >
    <content >
      <![CDATA[
        UPDATE wh_picking_sku_calculator
        SET <[task_id = :task_id,]>
          <[sku = :sku,]>
          <[calculator_json = :calculator_json,]>
        id = id
        WHERE 1 = 1
        AND task_sku_id = :task_sku_id
      ]]>
    </content>
  </sql>

</sqlmap>