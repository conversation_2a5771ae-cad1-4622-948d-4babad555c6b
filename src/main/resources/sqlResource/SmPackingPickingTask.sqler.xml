<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="querySmPackingPickingTaskCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM sm_packing_picking_task spt
        WHERE 1 = 1
          <[AND spt.id = :id]>
          <[AND spt.id in (:idList)]>
          <[AND spt.task_no = :task_no]>
          <[AND spt.box_no = :box_no]>
          <[AND spt.task_status = :task_status]>
          <[AND spt.receive_person = :receive_person]>
          <[AND spt.created_by = :created_by]>
          <[AND spt.receive_date = :receive_date]>
          <[AND spt.created_date = :created_date]>
          <[AND spt.last_update_date = :last_update_date]>
          <[AND spt.last_update_by = :last_update_by]>
          <[AND spt.is_printing = :is_printing]>
          <[AND spt.task_type = :task_type]>
          <[AND spt.task_type IN (:taskTypesList)]>
          <[AND spt.receive_date >= :fromCreatedDate]>
          <[AND spt.receive_date <= :toCreatedDate]>
          <[AND spt.picking_end_date>= :fromReceiveDate]>
          <[AND spt.picking_end_date <= :toReceiveDate]>
          <[AND spt.id in (SELECT DISTINCT i.task_id FROM sm_packing_picking_task_item i LEFT JOIN sm_packing_exception e ON i.spe_id=e.id WHERE e.apv_no IN (:apvNoList))]>
          <[AND spt.id in (SELECT DISTINCT i.task_id FROM sm_packing_picking_task_item i LEFT JOIN sm_packing_exception e ON i.spe_id=e.id WHERE e.apv_no = :apv_no)]>
          <[AND spt.id in (SELECT DISTINCT i.task_id FROM sm_packing_picking_task_item i LEFT JOIN sm_packing_exception e ON i.spe_id=e.id WHERE e.sku IN (:skuList))]>
          <[AND spt.id in (SELECT DISTINCT i.task_id FROM sm_packing_picking_task_item i LEFT JOIN sm_packing_exception e ON i.spe_id=e.id WHERE e.sku = :sku)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySmPackingPickingTaskList" >
    <content >
      <![CDATA[
        SELECT spt.id, spt.task_no, spt.box_no, spt.task_status, spt.receive_person, spt.created_by, spt.receive_date,
        spt.created_date, spt.last_update_date, spt.last_update_by, spt.is_printing, spt.task_type, spt.picking_end_date
        <[:COUNT_SQL]>
        FROM sm_packing_picking_task spt
        WHERE 1 = 1
        <[AND spt.id = :id]>
        <[AND spt.id in (:idList)]>
        <[AND spt.task_no = :task_no]>
        <[AND spt.box_no = :box_no]>
        <[AND spt.task_status = :task_status]>
        <[AND spt.receive_person = :receive_person]>
        <[AND spt.created_by = :created_by]>
        <[AND spt.receive_date = :receive_date]>
        <[AND spt.created_date = :created_date]>
        <[AND spt.last_update_date = :last_update_date]>
        <[AND spt.last_update_by = :last_update_by]>
        <[AND spt.is_printing = :is_printing]>
        <[AND spt.task_type = :task_type]>
        <[AND spt.task_type IN (:taskTypesList)]>
        <[AND spt.receive_date >= :fromCreatedDate]>
        <[AND spt.receive_date <= :toCreatedDate]>
        <[AND spt.picking_end_date>= :fromReceiveDate]>
        <[AND spt.picking_end_date <= :toReceiveDate]>
        <[AND spt.id in (SELECT DISTINCT i.task_id FROM sm_packing_picking_task_item i LEFT JOIN sm_packing_exception e ON i.spe_id=e.id WHERE e.apv_no IN (:apvNoList))]>
        <[AND spt.id in (SELECT DISTINCT i.task_id FROM sm_packing_picking_task_item i LEFT JOIN sm_packing_exception e ON i.spe_id=e.id WHERE e.apv_no = :apv_no)]>
        <[AND spt.id in (SELECT DISTINCT i.task_id FROM sm_packing_picking_task_item i LEFT JOIN sm_packing_exception e ON i.spe_id=e.id WHERE e.sku IN (:skuList))]>
        <[AND spt.id in (SELECT DISTINCT i.task_id FROM sm_packing_picking_task_item i LEFT JOIN sm_packing_exception e ON i.spe_id=e.id WHERE e.sku = :sku)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySmPackingPickingTaskByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, task_no, box_no, task_status, receive_person, created_by, receive_date, 
        created_date, last_update_date, last_update_by, is_printing, task_type, picking_end_date
        FROM sm_packing_picking_task
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySmPackingPickingTask" >
    <content >
      <![CDATA[
        SELECT id, task_no, box_no, task_status, receive_person, created_by, receive_date, 
        created_date, last_update_date, last_update_by, is_printing, task_type, picking_end_date
        FROM sm_packing_picking_task
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_no = :task_no]>
        <[AND box_no = :box_no]>
        <[AND task_status = :task_status]>
        <[AND receive_person = :receive_person]>
        <[AND created_by = :created_by]>
        <[AND receive_date = :receive_date]>
        <[AND created_date = :created_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND is_printing = :is_printing]>
        <[AND task_type = :task_type]>
        <[AND task_type IN (:taskTypesList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createSmPackingPickingTask" >
    <content >
      <![CDATA[
        INSERT INTO sm_packing_picking_task (task_no, box_no, task_status, receive_person, created_by, receive_date, 
          created_date, last_update_date, last_update_by, is_printing, task_type, picking_end_date)
        VALUES (:task_no, :box_no, :task_status, :receive_person, :created_by, :receive_date, 
          :created_date, :last_update_date, :last_update_by, :is_printing, :task_type, :picking_end_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteSmPackingPickingTaskByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM sm_packing_picking_task
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateSmPackingPickingTaskByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE sm_packing_picking_task
        SET <[task_no = :task_no,]>
          <[box_no = :box_no,]>
          <[task_status = :task_status,]>
          <[receive_person = :receive_person,]>
          <[created_by = :created_by,]>
          <[receive_date = :receive_date,]>
          <[created_date = :created_date,]>
          <[last_update_date = :last_update_date,]>
          <[last_update_by = :last_update_by,]>
          <[is_printing = :is_printing,]>
          <[task_type = :task_type,]>
          <[picking_end_date = :picking_end_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="querySmPackingItemSkuList" >
    <content >
      <![CDATA[
        SELECT t.id AS task_id, t.task_no AS task_no, e.sku AS sku,  e.id AS sm_exception_id, e.stock_id,
        (IFNULL(e.less_quantity,0)- IFNULL(e.pick_quantity,0)) AS quantity , t.task_status AS task_status,
        e.pick_quantity AS pick_quantity , ti.status AS status ,ti.id AS sm_picking_item_id ,(select ws.location_number from wh_stock ws where ws.id=e.stock_id) AS 'location_number'
        FROM sm_packing_picking_task t
        LEFT JOIN sm_packing_picking_task_item ti ON t.id=ti.task_id
        LEFT JOIN sm_packing_exception e ON ti.spe_id=e.id
        WHERE 1 = 1
        <[AND t.id = :idList]>
      ]]>
    </content>
  </sql>


</sqlmap>