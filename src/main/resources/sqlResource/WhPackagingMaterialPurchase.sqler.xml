<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhPackagingMaterialPurchaseCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_packaging_material_purchase
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id in (:ids)]>
        <[AND task_no = :task_no]>
        <[AND task_no in (:taskNoList)]>
        <[AND status = :status]>
        <[AND remark = :remark]>
        <[AND push_date = :push_date]>
        <[AND creation_date = :creation_date]>
        <[AND created_by = :created_by]>
        <[AND supervisor_verify_by = :supervisor_verify_by]>
        <[AND supervisor_verify_date = :supervisor_verify_date]>
        <[AND manager_verify_by = :manager_verify_by]>
        <[AND manager_verify_date = :manager_verify_date]>
        <[AND warehouse_verify_by = :warehouse_verify_by]>
        <[AND warehouse_verify_date = :warehouse_verify_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND id in (SELECT material_purchase_id FROM wh_packaging_material_purchase_item WHERE material_article_number in (:materialArticleNumberList) )]>
        <[AND id in (SELECT material_purchase_id FROM wh_packaging_material_purchase_item WHERE material_article_number = :materialArticleNumber )]>
        <[AND creation_date >= :from_creation_date]>
        <[AND creation_date <= :to_creation_date]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhPackagingMaterialPurchaseList" >
    <content >
      <![CDATA[
        SELECT p.id, p.task_no, p.status, p.remark, p.push_date, p.creation_date, p.created_by, p.supervisor_verify_by,
        p.supervisor_verify_date, p.manager_verify_by, p.manager_verify_date, p.warehouse_verify_by,
        p.warehouse_verify_date, p.last_update_date, p.last_updated_by,p.up_status,
        i.material_article_number, i.name, i.unit,  i.box_specification, i.quantity, i.remark ,i.material_purchase_id,i.id,
        i.over_due_quantity,i.up_status,<[:estiCanUseDaysColumnCalculate]> AS esti_can_use_days
        FROM wh_packaging_material_purchase p
        LEFT JOIN  wh_packaging_material_purchase_item i ON p.id=material_purchase_id
        <[:joinStockSql]>
        <[:joinOnWayTableSql]>
        <[:joinDayUseTableSql]>
        INNER JOIN (
           SELECT  wpm.id  FROM wh_packaging_material_purchase wpm
           WHERE 1 = 1
          <[AND wpm.id = :id]>
          <[AND wpm.id in (:ids)]>
          <[AND wpm.task_no = :task_no]>
          <[AND wpm.task_no in (:taskNoList)]>
          <[AND wpm.status = :status]>
          <[AND wpm.status in (:statusList)]>
          <[AND wpm.remark = :remark]>
          <[AND wpm.push_date = :push_date]>
          <[AND wpm.creation_date = :creation_date]>
          <[AND wpm.created_by = :created_by]>
          <[AND wpm.supervisor_verify_by = :supervisor_verify_by]>
          <[AND wpm.supervisor_verify_date = :supervisor_verify_date]>
          <[AND wpm.manager_verify_by = :manager_verify_by]>
          <[AND wpm.manager_verify_date = :manager_verify_date]>
          <[AND wpm.warehouse_verify_by = :warehouse_verify_by]>
          <[AND wpm.warehouse_verify_date = :warehouse_verify_date]>
          <[AND wpm.warehouse_verify_date <= :to_warehouse_verify_date]>
          <[AND wpm.last_update_date = :last_update_date]>
          <[AND wpm.last_updated_by = :last_updated_by]>
          <[AND wpm.creation_date >= :from_creation_date]>
          <[AND wpm.creation_date <= :to_creation_date]>
          <[AND wpm.id in (SELECT material_purchase_id FROM wh_packaging_material_purchase_item WHERE material_article_number in (:materialArticleNumberList) )]>
          <[AND wpm.id in (SELECT material_purchase_id FROM wh_packaging_material_purchase_item WHERE material_article_number = :materialArticleNumber )]>
          <[AND wpm.id in (SELECT material_purchase_id FROM wh_packaging_material_purchase_item WHERE IFNULL(over_due_quantity,quantity) >= :quantity )]>
          <[:IS_NOT_COMPLETE]>
          ORDER BY wpm.creation_date DESC
           <[:LIMIT]>
         ) wpm on wpm.id = p.id

      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhPackagingMaterialPurchaseByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, task_no, status, remark, push_date, creation_date, created_by, supervisor_verify_by, 
        supervisor_verify_date, manager_verify_by, manager_verify_date, warehouse_verify_by, 
        warehouse_verify_date, last_update_date, last_updated_by,up_status
        FROM wh_packaging_material_purchase
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhPackagingMaterialPurchase" >
    <content >
      <![CDATA[
        SELECT id, task_no, status, remark, push_date, creation_date, created_by, supervisor_verify_by, 
        supervisor_verify_date, manager_verify_by, manager_verify_date, warehouse_verify_by, 
        warehouse_verify_date, last_update_date, last_updated_by,up_status
        FROM wh_packaging_material_purchase
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_no = :task_no]>
        <[AND status = :status]>
        <[AND remark = :remark]>
        <[AND push_date = :push_date]>
        <[AND creation_date = :creation_date]>
        <[AND created_by = :created_by]>
        <[AND supervisor_verify_by = :supervisor_verify_by]>
        <[AND supervisor_verify_date = :supervisor_verify_date]>
        <[AND manager_verify_by = :manager_verify_by]>
        <[AND manager_verify_date = :manager_verify_date]>
        <[AND warehouse_verify_by = :warehouse_verify_by]>
        <[AND warehouse_verify_date = :warehouse_verify_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhPackagingMaterialPurchase" >
    <content >
      <![CDATA[
        INSERT INTO wh_packaging_material_purchase (task_no, status, remark, push_date, creation_date, created_by, supervisor_verify_by, 
          supervisor_verify_date, manager_verify_by, manager_verify_date, warehouse_verify_by, 
          warehouse_verify_date, last_update_date, last_updated_by, up_status)
        VALUES (:task_no, :status, :remark, :push_date, :creation_date, :created_by, :supervisor_verify_by, 
          :supervisor_verify_date, :manager_verify_by, :manager_verify_date, :warehouse_verify_by, 
          :warehouse_verify_date, :last_update_date, :last_updated_by, :up_status)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhPackagingMaterialPurchaseByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_packaging_material_purchase
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhPackagingMaterialPurchaseByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_packaging_material_purchase
        SET <[task_no = :task_no,]>
          <[status = :status,]>
          <[remark = :remark,]>
          <[push_date = :push_date,]>
          <[creation_date = :creation_date,]>
          <[created_by = :created_by,]>
          <[supervisor_verify_by = :supervisor_verify_by,]>
          <[supervisor_verify_date = :supervisor_verify_date,]>
          <[manager_verify_by = :manager_verify_by,]>
          <[manager_verify_date = :manager_verify_date,]>
          <[warehouse_verify_by = :warehouse_verify_by,]>
          <[warehouse_verify_date = :warehouse_verify_date,]>
          <[last_update_date = :last_update_date,]>
          <[last_updated_by = :last_updated_by,]>
          <[up_status = :up_status,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryMaterialPurchase" >
    <content >
      <![CDATA[
        SELECT p.material_article_number, p.name, p.unit, p.box_specification, p.remark,
        s.surplus_quantity
        FROM wh_packaging_material_management p
        LEFT JOIN wh_packaging_material_stock s
        ON p.material_article_number=s.material_article_number
        WHERE 1 = 1
        <[AND p.material_article_number = :material_article_number]>
        <[AND p.material_article_number in (:materialArticleNumberList)]>
        <[AND p.name = :name]>
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="getWhPackagingMaterialPurchaseList" >
    <content >
      <![CDATA[
        SELECT pi.id,pi.material_article_number, pi.name, pi.unit, pi.box_specification, pi.remark,pi.quantity,
        s.surplus_quantity,p.task_no,p.id
        FROM wh_packaging_material_purchase p
        LEFT JOIN wh_packaging_material_purchase_item pi
        ON p.id=pi.material_purchase_id
        LEFT JOIN wh_packaging_material_stock s
        ON s.material_article_number=pi.material_article_number
        WHERE 1 = 1
        <[AND p.id = :id]>
      ]]>
    </content>
  </sql>
</sqlmap>