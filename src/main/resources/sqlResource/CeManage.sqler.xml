<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryCeManageCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM ce_manage
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND status = :status]>
        <[AND spu = :spu]>
        <[AND toy_flag = :toy_flag]>
        <[AND upload_image_flag = :upload_image_flag]>
        <[AND sync_ce_info_date = :sync_ce_info_date]>
        <[AND photo_date = :photo_date]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND wms_image_urls = :wms_image_urls]>
        <[AND pack_image_urls = :pack_image_urls]>
        <[AND need_ce_certified = :need_ce_certified]>
        <[AND platform_qualification = :platform_qualification]>
        <[AND plat_category_id = :plat_category_id]>
        <[AND ce_info = :ce_info]>
        <[AND sku IN (:sku_list)]>
        <[AND spu IN (:spu_list)]>
        <[AND id IN (:id_list)]>
        <[AND sync_ce_info_date >= :from_sync_ce_info_date]>
        <[AND sync_ce_info_date <= :to_sync_ce_info_date]>
        <[AND photo_date >= :from_photo_date]>
        <[AND photo_date <= :to_photo_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryCeManageList" >
    <content >
      <![CDATA[
        SELECT id, sku, toy_flag, upload_image_flag, sync_ce_info_date, photo_date, created_by,
        creation_date, last_updated_by, last_update_date,wms_image_urls, pack_image_urls,ce_info,plat_category_id,platform_qualification,
        ce_image_urls, is_photographed, is_real_photo_taken, is_ce_photo_taken,spu,need_ce_certified,status,ce_task_no
        FROM ce_manage
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND status = :status]>
        <[AND spu = :spu]>
        <[AND toy_flag = :toy_flag]>
        <[AND upload_image_flag = :upload_image_flag]>
        <[AND sync_ce_info_date = :sync_ce_info_date]>
        <[AND photo_date = :photo_date]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND wms_image_urls = :wms_image_urls]>
        <[AND pack_image_urls = :pack_image_urls]>
        <[AND need_ce_certified = :need_ce_certified]>
        <[AND platform_qualification = :platform_qualification]>
        <[AND plat_category_id = :plat_category_id]>
        <[AND ce_info = :ce_info]>
        <[AND sku IN (:sku_list)]>
        <[AND spu IN (:spu_list)]>
        <[AND id IN (:id_list)]>
        <[AND sync_ce_info_date >= :from_sync_ce_info_date]>
        <[AND sync_ce_info_date <= :to_sync_ce_info_date]>
        <[AND photo_date >= :from_photo_date]>
        <[AND photo_date <= :to_photo_date]>
        <[:LIMIT]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryCeManageStockList" >
    <content >
      <![CDATA[
         SELECT s.id,s.location_number,s.sku,IFNULL(surplus_quantity,0) + IFNULL(allot_quantity,0) AS locationQuantity
		,ws.name AS skuName,ws.image_url AS skuImg
        FROM
		wh_stock s
		LEFT JOIN wh_sku ws ON ws.sku = s.sku
        WHERE 1 = 1
        <[AND s.sku in (:sku_list)]>
        <[AND s.id in (:idList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryCeManageByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, sku, toy_flag, upload_image_flag, sync_ce_info_date, photo_date, created_by, 
        creation_date, last_updated_by, last_update_date, wms_image_urls, pack_image_urls, plat_category_id,platform_qualification,
        ce_info, ce_image_urls, is_photographed, is_real_photo_taken, is_ce_photo_taken,spu,need_ce_certified,status,ce_task_no
        FROM ce_manage
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryCeManage" >
    <content >
      <![CDATA[
        SELECT id, sku, toy_flag, upload_image_flag, sync_ce_info_date, photo_date, created_by, 
        creation_date, last_updated_by, last_update_date, wms_image_urls, pack_image_urls, plat_category_id,platform_qualification,
        ce_info, ce_image_urls, is_photographed, is_real_photo_taken, is_ce_photo_taken,spu,need_ce_certified,status,ce_task_no
        FROM ce_manage
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND toy_flag = :toy_flag]>
        <[AND upload_image_flag = :upload_image_flag]>
        <[AND sync_ce_info_date = :sync_ce_info_date]>
        <[AND photo_date = :photo_date]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND wms_image_urls = :wms_image_urls]>
        <[AND pack_image_urls = :pack_image_urls]>
        <[AND need_ce_certified = :need_ce_certified]>
        <[AND platform_qualification = :platform_qualification]>
        <[AND plat_category_id = :plat_category_id]>
        <[AND ce_info = :ce_info]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createCeManage" >
    <content >
      <![CDATA[
        INSERT INTO ce_manage (sku, toy_flag, upload_image_flag, sync_ce_info_date, photo_date, created_by, 
          creation_date, last_updated_by, last_update_date, wms_image_urls, pack_image_urls, plat_category_id,platform_qualification,
          ce_info,spu,is_photographed,is_real_photo_taken,is_ce_photo_taken,need_ce_certified,status)
        VALUES (:sku, :toy_flag, :upload_image_flag, :sync_ce_info_date, :photo_date, :created_by, 
          :creation_date, :last_updated_by, :last_update_date, :wms_image_urls, :pack_image_urls, :plat_category_id,:platform_qualification,
          :ce_info, :spu, :is_photographed, :is_real_photo_taken, :is_ce_photo_taken, :need_ce_certified, :status)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteCeManageByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM ce_manage
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateCeManageByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE ce_manage
        SET <[sku = :sku,]>
          <[toy_flag = :toy_flag,]>
          <[upload_image_flag = :upload_image_flag,]>
          <[sync_ce_info_date = :sync_ce_info_date,]>
          <[photo_date = :photo_date,]>
          <[created_by = :created_by,]>
          <[creation_date = :creation_date,]>
          <[last_updated_by = :last_updated_by,]>
          <[last_update_date = :last_update_date,]>
          <[wms_image_urls = :wms_image_urls,]>
          <[pack_image_urls = :pack_image_urls,]>
          <[ce_info = :ce_info,]>
          <[spu = :spu,]>
          <[ce_image_urls = :ce_image_urls,]>
          <[status = :status,]>
          <[ce_task_no = :ce_task_no,]>
          <[is_photographed = :is_photographed,]>
          <[is_real_photo_taken = :is_real_photo_taken,]>
          <[is_ce_photo_taken = :is_ce_photo_taken,]>
          <[plat_category_id = :plat_category_id,]>
          <[platform_qualification = :platform_qualification,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>