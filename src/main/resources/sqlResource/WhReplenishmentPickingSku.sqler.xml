<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhReplenishmentPickingSkuCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_replenishment_picking_sku
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND sku = :sku]>
        <[AND sku_name = :sku_name]>
        <[AND location = :location]>
        <[AND quantity = :quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND status = :status]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND remark = :remark]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhReplenishmentPickingSkuList" >
    <content >
      <![CDATA[
        SELECT id, task_id, sku, sku_name, location, quantity, pick_quantity, status, creation_date, 
        last_update_date, remark, stock_id
        FROM wh_replenishment_picking_sku
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND sku = :sku]>
        <[AND sku_name = :sku_name]>
        <[AND location = :location]>
        <[AND quantity = :quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND status = :status]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND remark = :remark]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhReplenishmentPickingTaskAndSkus" >
    <content >
      <![CDATA[
        SELECT wpts.id, wpts.task_id, wpts.sku, wpts.sku_name,
          IF(wpts.stock_id IS NOT NULL, (SELECT location_number FROM wh_stock WHERE wh_stock.id = wpts.stock_id), wpts.location) AS 'wpts.location' ,
          wpts.quantity, wpts.pick_quantity, wpts.status,
          wpts.creation_date, wpts.last_update_date, wpts.remark, wpts.stock_id,
          wpt.task_type, wpt.is_asn, wpt.task_no, wpt.warehouse_type, wpt.task_status, wpt.waybill_type,
          ws.warehouse_id, ws.location_number, ws.name,ws.image_url
        FROM wh_replenishment_picking_sku wpts
        LEFT JOIN wh_sku ws on ws.sku = wpts.sku
        LEFT JOIN wh_replenishment_picking_task wpt on wpt.id =  wpts.task_id
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND sku = :sku]>
        <[AND sku_name = :sku_name]>
        <[AND location = :location]>
        <[AND quantity = :quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND status = :status]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND remark = :remark]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhReplenishmentPickingSkuByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, task_id, sku, sku_name, location, quantity, pick_quantity, status, creation_date, 
        last_update_date, remark, stock_id
        FROM wh_replenishment_picking_sku
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhReplenishmentPickingSku" >
    <content >
      <![CDATA[
        SELECT id, task_id, sku, sku_name, location, quantity, pick_quantity, status, creation_date, 
        last_update_date, remark, stock_id
        FROM wh_replenishment_picking_sku
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND sku = :sku]>
        <[AND sku_name = :sku_name]>
        <[AND location = :location]>
        <[AND quantity = :quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND status = :status]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND remark = :remark]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhReplenishmentPickingSku" >
    <content >
      <![CDATA[
        INSERT INTO wh_replenishment_picking_sku (task_id, sku, sku_name, location, quantity, pick_quantity, status, 
          creation_date, last_update_date, remark, stock_id)
        VALUES (:task_id, :sku, :sku_name, :location, :quantity, :pick_quantity, :status, 
          :creation_date, :last_update_date, :remark, :stock_id)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhReplenishmentPickingSkuByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_replenishment_picking_sku
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhReplenishmentPickingSkuByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_replenishment_picking_sku
        SET <[task_id = :task_id,]>
          <[sku = :sku,]>
          <[sku_name = :sku_name,]>
          <[location = :location,]>
          <[quantity = :quantity,]>
          <[pick_quantity = :pick_quantity,]>
          <[status = :status,]>
          <[creation_date = :creation_date,]>
          <[last_update_date = :last_update_date,]>
          <[remark = :remark,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>