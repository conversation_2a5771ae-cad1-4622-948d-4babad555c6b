<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap>
	<!--推单量 -->
	<sql datasource="dataSource" id="queryOrderPushed">
		<content>
			<![CDATA[
			select COUNT(DISTINCT apv.id) as quantity,
			COUNT(DISTINCT apv.id, if(apv.status = 4,TRUE, NULL)) as out_of_stock_quantity
			FROM wh_apv apv
            WHERE 1 = 1
            AND apv.status != 2
        	<[AND apv.creation_date >= :start_time]>
	    	<[AND apv.creation_date < :end_time]>
      		]]>
		</content>
	</sql>
	
	<!--发货订单量 -->
	<sql datasource="dataSource" id="queryOrderShiped">
		<content>
      		<![CDATA[
      		select COUNT(DISTINCT apv.id) as quantity
			FROM wh_apv apv
			INNER JOIN apv_track t on apv.apv_no = t.apv_no
            WHERE 1 = 1
            AND apv.status in (17,18)
			<[AND IFNULL(t.deliver_time, t.load_date) >= :start_time]>
			<[AND IFNULL(t.deliver_time, t.load_date) < :end_time]>

      		]]>
		</content>
	</sql>

	<!--发货产品数 -->
	<sql datasource="dataSource" id="querySkuShiped">
		<content>
      		<![CDATA[
      		select SUM(inner_item.sale_quantity) as quantity
			FROM wh_apv apv
			INNER JOIN apv_track t on apv.apv_no = t.apv_no
			LEFT JOIN wh_apv_item inner_item on inner_item.apv_id = apv.id
            WHERE 1 = 1
            AND apv.status in (17,18)
			<[AND IFNULL(t.deliver_time, t.load_date) >= :start_time]>
			<[AND IFNULL(t.deliver_time, t.load_date) < :end_time]>

      		]]>
		</content>
	</sql>
	
	<!--采购入库单数 -->
	<sql datasource="dataSource" id="queryCheckin">
		<content>
      		<![CDATA[
      		SELECT COUNT(1) as quantity
        	FROM wh_check_in
        	WHERE 1 = 1
        	AND status IN (3,5,7,9,11,12,13)
			<[AND create_date >= :start_time]>
			<[AND create_date < :end_time]>
      		]]>
		</content>
	</sql>

	<!--采购入库件数 -->
	<sql datasource="dataSource" id="querySkuCheckin">
		<content>
      		<![CDATA[
      		select SUM(i.quantity) as quantity
			FROM wh_check_in ch
			INNER JOIN wh_check_in_item i  ON i.in_id = ch.in_id
            WHERE 1 = 1
            AND ch.status = 13
			<[AND ch.up_time >= :start_time]>
			<[AND ch.up_time < :end_time]>
      		]]>
		</content>
	</sql>
	
	<!--24小时超时订单数 -->
	<sql datasource="dataSource" id="queryOver24hOrder">
		<content>
      		<![CDATA[
      		select COUNT(DISTINCT apv.id) as quantity
			FROM wh_apv apv
			INNER JOIN apv_track t on apv.apv_no = t.apv_no
            WHERE 1 = 1
            AND apv.status in (17,18)
            <[AND IFNULL(t.deliver_time, t.load_date) >= :start_time]>
			<[AND IFNULL(t.deliver_time, t.load_date) < :end_time]>

	    	AND (
				TIMESTAMPDIFF(SECOND, IFNULL(apv.deliver_date,apv.creation_date), t.deliver_time) > 86400
				OR
				TIMESTAMPDIFF(SECOND, IFNULL(apv.deliver_date,apv.creation_date), t.load_date) > 86400
	    	)
      		]]>
		</content>
	</sql>
	
	<!--24小时超时上架SKU数 -->
	<sql datasource="dataSource" id="queryOver24hCheckin">
		<content>
      		<![CDATA[
      		SELECT COUNT(DISTINCT i.sku,if(ch.status = 13,TRUE, NULL)) AS upQuantity,
			COUNT(DISTINCT ex.sku) AS exUpQuantity,
			COUNT(DISTINCT i.sku,if((ch.status = 13 AND ch.comment IS NOT NULL),TRUE,NULL)) AS ngUpQuantity
			FROM wh_check_in ch
			INNER JOIN wh_check_in_item i ON i.in_id = ch.in_id
			LEFT JOIN wh_check_in_exception ex ON ex.in_id = ch.in_id
			WHERE 1 = 1
			<[AND ch.up_time >= :start_time]>
			<[AND ch.up_time < :end_time]>
			AND TIMESTAMPDIFF(SECOND, ch.create_date, ch.up_time) > 86400
      		]]>
		</content>
	</sql>

	<!--24小时发货率 -->
	<sql datasource="dataSource" id="queryShipedRateBy24h">
		<content>
      		<![CDATA[
      		select COUNT(DISTINCT apv.id) as quantity
			FROM wh_apv apv
			INNER JOIN apv_track t on apv.apv_no = t.apv_no
            WHERE 1 = 1
            AND apv.status in (17,18)
			<[AND IFNULL(t.deliver_time, t.load_date) >= :start_time]>
			<[AND IFNULL(t.deliver_time, t.load_date) < :end_time]>
	    	AND (TIMESTAMPDIFF(SECOND, apv.creation_date, t.deliver_time) <= 86400 OR TIMESTAMPDIFF(SECOND, apv.creation_date, t.load_date) <= 86400)

      		]]>
		</content>
	</sql>

	<!--36小时发货率 -->
	<sql datasource="dataSource" id="queryShipedRateBy36h">
		<content>
			<![CDATA[
      		select  COUNT(DISTINCT apv.id) as quantity
			FROM wh_apv apv
			INNER JOIN apv_track t on apv.apv_no = t.apv_no
            WHERE 1 = 1
            AND apv.status in (17,18)
			<[AND IFNULL(t.deliver_time, t.load_date) >= :start_time]>
			<[AND IFNULL(t.deliver_time, t.load_date) < :end_time]>
	    	AND (TIMESTAMPDIFF(SECOND, apv.creation_date, t.deliver_time) <= 129600 OR TIMESTAMPDIFF(SECOND, apv.creation_date, t.load_date) <= 129600)
      		]]>
		</content>
	</sql>

	<!--上架订单数 -->
	<sql datasource="dataSource" id="queryUpLocationOrder">
		<content>
			<![CDATA[
      		SELECT COUNT(1) as quantity
        	FROM wh_check_in
        	WHERE 1 = 1
        	AND status = 13
			<[AND up_time >= :start_time]>
			<[AND up_time < :end_time]>
      		]]>
		</content>
	</sql>

	<!--24小时上架率 -->
	<sql datasource="dataSource" id="queryUpLocationRateBy24h">
		<content>
      		<![CDATA[
      		SELECT COUNT(1) as quantity
        	FROM wh_check_in
        	WHERE 1 = 1
        	AND status = 13
			<[AND up_time >= :start_time]>
			<[AND up_time < :end_time]>
			AND TIMESTAMPDIFF(SECOND, create_date, up_time) <= 86400
      		]]>
		</content>
	</sql>

	<!--48小时上架率 -->
	<sql datasource="dataSource" id="queryUpLocationRateBy48h">
		<content>
			<![CDATA[
      		SELECT COUNT(1) as quantity
        	FROM wh_check_in
        	WHERE 1 = 1
        	AND status = 13
			<[AND up_time >= :start_time]>
			<[AND up_time < :end_time]>
			AND TIMESTAMPDIFF(SECOND, create_date, up_time) <= 172800
      		]]>
		</content>
	</sql>

	<!--装车订单数 -->
	<sql datasource="dataSource" id="queryLoadedOrder">
		<content>
			<![CDATA[
				SELECT COUNT(DISTINCT apv.id) as quantity
				FROM wh_apv apv
				LEFT JOIN wh_scan_shipment_to_apv s on apv.apv_no = s.apv_no
				LEFT JOIN wh_scan_shipment  shipment on s.scan_shipment_id = shipment.id
				WHERE 1 = 1
				AND apv.status = 18
				<[AND shipment.load_date >= :start_time]>
				<[AND shipment.load_date < :end_time]>
      		]]>
		</content>
	</sql>

	<!--结袋并装车订单数 -->
	<sql datasource="dataSource" id="queryPackedAndLoadedOrder">
		<content>
			<![CDATA[
				SELECT COUNT(DISTINCT apv.id) as quantity
				FROM wh_apv apv
				LEFT JOIN wh_scan_shipment_to_apv s on apv.apv_no = s.apv_no
				LEFT JOIN wh_scan_shipment  shipment on s.scan_shipment_id = shipment.id
				WHERE 1 = 1
				AND apv.status = 18
				<[AND shipment.load_date >= :start_time]>
				<[AND shipment.load_date < :end_time]>
				<[AND shipment.scan_date >= :start_time]>
				<[AND shipment.scan_date < :end_time]>
      		]]>
		</content>
	</sql>
	<!--结袋订单数 -->
	<sql datasource="dataSource" id="queryPackedOrder">
		<content>
			<![CDATA[
				SELECT COUNT(DISTINCT apv.id) as quantity
				FROM wh_apv apv
				LEFT JOIN wh_scan_shipment_to_apv s on apv.apv_no = s.apv_no
				LEFT JOIN wh_scan_shipment  shipment on s.scan_shipment_id = shipment.id
				WHERE 1 = 1
				AND apv.status IN(17,18)
				<[AND shipment.scan_date >= :start_time]>
				<[AND shipment.scan_date < :end_time]>
      		]]>
		</content>
	</sql>
	<!--24小时装车率 -->
	<sql datasource="dataSource" id="queryLoadedRateBy24h">
		<content>
			<![CDATA[
				SELECT COUNT(DISTINCT apv.id) as quantity
				FROM wh_apv apv
				LEFT JOIN wh_scan_shipment_to_apv s on apv.apv_no = s.apv_no
				LEFT JOIN wh_scan_shipment  shipment on s.scan_shipment_id = shipment.id
				WHERE 1 = 1
				AND apv.status = 18
				<[AND shipment.load_date >= :start_time]>
				<[AND shipment.load_date < :end_time]>
				AND TIMESTAMPDIFF(SECOND, shipment.scan_date, shipment.load_date) <= 86400
      		]]>
		</content>
	</sql>

	<!--36小时装车率 -->
	<sql datasource="dataSource" id="queryLoadedRateBy36h">
		<content>
			<![CDATA[
				SELECT COUNT(DISTINCT apv.id) as quantity
				FROM wh_apv apv
				LEFT JOIN wh_scan_shipment_to_apv s on apv.apv_no = s.apv_no
				LEFT JOIN wh_scan_shipment  shipment on s.scan_shipment_id = shipment.id
				WHERE 1 = 1
				AND apv.status = 18
				<[AND shipment.load_date >= :start_time]>
				<[AND shipment.load_date < :end_time]>
				AND TIMESTAMPDIFF(SECOND, shipment.scan_date, shipment.load_date) <= 129600
      		]]>
		</content>
	</sql>


	<!--盘点盈亏数-->
	<sql datasource="dataSource" id="queryInventoryProfitAndLoss">
		<content>
      		<![CDATA[
      		SELECT
      		SUM(if(l.quantity >= 0,l.quantity,0)) as profit_quantity,
      		SUM(if(l.quantity < 0,l.quantity,0)) as loss_quantity
			FROM wh_log l
			LEFT JOIN wh_sku sku on sku.sku = l.sku
			WHERE 1 = 1
			AND type in (2, 10)
			AND l.sku NOT IN (select p.material_article_number from wh_packaging_material_management p)
			<[AND sku.warehouse_id = :warehouse_id]>
        	<[AND l.creation_date >= :start_time]>
			<[AND l.creation_date < :end_time]>
      		]]>
		</content>
	</sql>
	
	<!--盘点金额-->
	<sql datasource="dataSource" id="queryInventoryAmount">
		<content>
      		<![CDATA[
        	SELECT
      		SUM(if(l.quantity >= 0,l.quantity*sku.average_purchase_price,0)) as profit_amount,
      		SUM(if(l.quantity < 0,l.quantity*sku.average_purchase_price,0)) as loss_amount
			FROM wh_log l
			LEFT JOIN wh_sku sku on sku.sku = l.sku
			WHERE 1 = 1
			AND type in (2, 10)
			AND l.sku NOT IN (select p.material_article_number from wh_packaging_material_management p)
			<[AND sku.warehouse_id = :warehouse_id]>
        	<[AND l.creation_date >= :start_time]>
			<[AND l.creation_date < :end_time]>
      		]]>
		</content>
	</sql>

	<!--月盘点sku数和pcs-->
	<sql datasource="dataSource" id="queryInventorySkuAndPcs">
		<![CDATA[
			SELECT
      		COUNT(DISTINCT sku.sku) as inventory_sku,
      		SUM(l.quantity) as inventory_pcs
			FROM wh_log l
			LEFT JOIN wh_sku sku on sku.sku = l.sku
			WHERE 1 = 1
			AND type in (2, 10)
			AND l.sku NOT IN (select p.material_article_number from wh_packaging_material_management p)
			<[AND sku.warehouse_id = :warehouse_id]>
        	<[AND l.creation_date >= :start_time]>
			<[AND l.creation_date < :end_time]>
		]]>
	</sql>

	<!--排除停产，存档状态的sku-->
	<sql datasource="dataSource" id="querySkuCount">
		<![CDATA[
			SELECT
      		COUNT(DISTINCT sku.sku) as inventory_sku_count,
      		SUM(l.quantity) as inventory_pcs_count
			FROM wh_log l
			LEFT JOIN wh_sku sku on sku.sku = l.sku
			WHERE 1 = 1
			<[AND sku.warehouse_id = :warehouse_id]>
			AND type in (2, 10)
			AND sku.status NOT IN (9,10)
			AND l.sku NOT IN (select p.material_article_number from wh_packaging_material_management p)
		]]>
	</sql>

	<!--查询时间库存金额-->
	<sql datasource="dataSource" id="queryRecordAmount">
		<content>
      		<![CDATA[
        	SELECT
      		SUM((s.qc_quantity+s.waiting_up_quantity+s.up_quantity+s.surplus_quantity+s.pick_quantity+s.cancel_quantity+s.allot_quantity+s.pick_not_quantity)*sku.average_purchase_price) as quantity
			FROM wh_stock s
			LEFT JOIN wh_sku sku on sku.sku = s.sku
			WHERE 1 = 1
			AND s.sku NOT IN (select p.material_article_number from wh_packaging_material_management p)
			<[AND sku.warehouse_id = :warehouse_id]>
			<[AND s.creation_date >= :start_time]>
			<[AND s.creation_date < :end_time]>
      		]]>
		</content>
	</sql>

	<!--入库单相关 -->
	<sql datasource="dataSource" id="queryCheckInRelated">
		<content>
			<![CDATA[
				SELECT
				COUNT(DISTINCT ch.purchase_order_no) as po_count,
				COUNT(DISTINCT i.sku) AS sku_count,
				COUNT(DISTINCT i.sku,if(ch.status = 5,TRUE,NULL)) ng_sku_count,
				SUM(if(ch.status = 5, i.quantity, 0)) AS ng_quantity_sum,
				SUM(ex.quantity) AS exception_quantity_sum,
				COUNT(DISTINCT ex.sku) AS exception_sku_count
				FROM wh_check_in ch
				INNER JOIN wh_check_in_item i ON i.in_id = ch.in_id
				LEFT JOIN wh_check_in_exception ex ON ex.in_id = ch.in_id
				WHERE ch.status IN (3,5,7,9,11,12,13)
				<[AND ch.create_date >= :start_time]>
				<[AND ch.create_date < :end_time]>
      		]]>
		</content>
	</sql>

	<!--快递收货相关 -->
	<sql datasource="dataSource" id="queryRecordRelated">
		<content>
			<![CDATA[
				SELECT
				COUNT(DISTINCT id) AS record_order_count,
				SUM(quantity) AS record_order_quantity_sum
				FROM wh_purchase_express_record
				WHERE 1 = 1
				<[AND creation_date >= :start_time]>
				<[AND creation_date < :end_time]>
      		]]>
		</content>
	</sql>

	<!--合单相关 -->
	<sql datasource="dataSource" id="queryMergeOrderRelated">
		<content>
			<![CDATA[
				SELECT
				COUNT(DISTINCT apv.id, IF(apv.original_order_id <= 1,TRUE, NULL)) once_merge_order_count,
				COUNT(DISTINCT apv.id, IF(apv.original_order_id <= 2,TRUE, NULL)) once_and_twice_merge_order_count
				FROM wh_apv apv
				INNER JOIN apv_track t on apv.apv_no = t.apv_no
				WHERE 1 = 1
				<[AND t.merge_time >= :start_time]>
				<[AND t.merge_time < :end_time]>
      		]]>
		</content>
	</sql>

	<!--合单相关 -->
	<sql datasource="dataSource" id="queryMergeDeliverAndLoadingOrder">
		<content>
			<![CDATA[
				SELECT
				COUNT(DISTINCT apv.id, IF(apv.original_order_id <= 1,TRUE, NULL)) once_merge_deliver_and_loading_order_count,
				COUNT(DISTINCT apv.id, IF(apv.original_order_id <= 2,TRUE, NULL)) once_and_twice_merge_deliver_and_loading_order_count
				FROM wh_apv apv
				INNER JOIN apv_track t on apv.apv_no = t.apv_no
				WHERE 1 = 1
				AND apv.status IN(17,18)
				<[AND IFNULL(t.deliver_time, t.load_date) >= :start_time]>
				<[AND IFNULL(t.deliver_time, t.load_date) < :end_time]>
      		]]>
		</content>
	</sql>

	<!--拆分包裹件数 -->
	<!--取入库单中入库时间为当天快递单号统计，并去重 -->
	<sql datasource="dataSource" id="querySplitOrderCount">
		<content>
			<![CDATA[
      		SELECT COUNT(DISTINCT tracking_number) as quantity
        	FROM wh_check_in
        	WHERE 1 = 1
			<[AND create_date >= :start_time]>
			<[AND create_date < :end_time]>
      		]]>
		</content>
	</sql>
</sqlmap>