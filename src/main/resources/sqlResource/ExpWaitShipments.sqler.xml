<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryExpWaitShipmentsCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM exp_wait_shipments
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND warehouse_type = :warehouse_type]>
        <[AND quantity = :quantity]>
        <[AND offset_quantity = :offset_quantity]>
        <[AND relation_id = :relation_id]>
        <[AND create_by = :create_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND last_update_date >= :fromCreationDate]>
        <[AND last_update_date <= :toCreationDate]>
        <[AND sku in (:sku_list)]>
        <[:QUERY_UNDONE_CONDITION]>
        <[:RELATION_ID_LIKE_CONDITION]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryExpWaitShipmentsList" >
    <content >
      <![CDATA[
        SELECT id, sku, warehouse_type, quantity, offset_quantity, relation_id, relation_detail_json,
        create_by, creation_date, last_update_by, last_update_date
        FROM exp_wait_shipments
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND warehouse_type = :warehouse_type]>
        <[AND quantity = :quantity]>
        <[AND offset_quantity = :offset_quantity]>
        <[AND relation_id = :relation_id]>
        <[AND create_by = :create_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND last_update_date >= :fromCreationDate]>
        <[AND last_update_date <= :toCreationDate]>
        <[AND sku in (:sku_list)]>
        <[:QUERY_UNDONE_CONDITION]>
        <[:RELATION_ID_LIKE_CONDITION]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryExpWaitShipmentsByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, sku, warehouse_type, quantity, offset_quantity, relation_id, relation_detail_json,
        create_by, creation_date, last_update_by, last_update_date
        FROM exp_wait_shipments
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryExpWaitShipments" >
    <content >
      <![CDATA[
        SELECT id, sku, warehouse_type, quantity, offset_quantity, relation_id, relation_detail_json,
        create_by, creation_date, last_update_by, last_update_date
        FROM exp_wait_shipments
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND warehouse_type = :warehouse_type]>
        <[AND quantity = :quantity]>
        <[AND offset_quantity = :offset_quantity]>
        <[AND relation_id = :relation_id]>
        <[AND exp_days = :exp_days]>
        <[AND exp_date = :exp_date]>
        <[AND create_by = :create_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createExpWaitShipments" >
    <content >
      <![CDATA[
        INSERT INTO exp_wait_shipments (sku, warehouse_type, quantity, offset_quantity, relation_id, relation_detail_json,
          create_by, creation_date, last_update_by, last_update_date)
        VALUES (:sku, :warehouse_type, :quantity, :offset_quantity, :relation_id, :relation_detail_json,
          :create_by, :creation_date, :last_update_by, :last_update_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteExpWaitShipmentsByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM exp_wait_shipments
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateExpWaitShipmentsByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE exp_wait_shipments
        SET <[sku = :sku,]>
          <[warehouse_type = :warehouse_type,]>
          <[quantity = :quantity,]>
          <[offset_quantity = :offset_quantity,]>
          <[relation_id = :relation_id,]>
          <[relation_detail_json = :relation_detail_json,]>
          <[create_by = :create_by,]>
          <[creation_date = :creation_date,]>
          <[last_update_by = :last_update_by,]>
          <[last_update_date = :last_update_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>