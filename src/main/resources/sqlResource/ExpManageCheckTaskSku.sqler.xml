<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryExpManageCheckTaskSkuCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM exp_manage_check_task_sku
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND relation_id = :relation_id]>
        <[AND batch_no = :batch_no]>
        <[AND uuid = :uuid]>
        <[AND sku = :sku]>
        <[AND status = :status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryExpManageCheckTaskSkuList" >
    <content >
      <![CDATA[
        SELECT id, relation_id, batch_no, uuid, sku, status
        FROM exp_manage_check_task_sku
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND relation_id = :relation_id]>
        <[AND batch_no = :batch_no]>
        <[AND uuid = :uuid]>
        <[AND sku = :sku]>
        <[AND status = :status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryExpManageCheckTaskSkuByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, relation_id, batch_no, uuid, sku, status
        FROM exp_manage_check_task_sku
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryExpManageCheckTaskSku" >
    <content >
      <![CDATA[
        SELECT id, relation_id, batch_no, uuid, sku, status
        FROM exp_manage_check_task_sku
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND relation_id = :relation_id]>
        <[AND batch_no = :batch_no]>
        <[AND uuid = :uuid]>
        <[AND sku = :sku]>
        <[AND status = :status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createExpManageCheckTaskSku" >
    <content >
      <![CDATA[
        INSERT INTO exp_manage_check_task_sku (relation_id, batch_no, uuid, sku, status)
        VALUES (:relation_id, :batch_no, :uuid, :sku, :status)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteExpManageCheckTaskSkuByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM exp_manage_check_task_sku
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateExpManageCheckTaskSkuByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE exp_manage_check_task_sku
        SET <[relation_id = :relation_id,]>
          <[batch_no = :batch_no,]>
          <[uuid = :uuid,]>
          <[sku = :sku,]>
          <[status = :status,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>