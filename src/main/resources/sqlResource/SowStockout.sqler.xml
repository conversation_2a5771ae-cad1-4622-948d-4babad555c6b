<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="querySowStockoutCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM sow_stockout
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND stockout_box = :stockout_box]>
        <[AND box = :box]>
        <[AND apv_no = :apv_no]>
        <[AND apv_id = :apv_id]>
        <[AND sku = :sku]>
        <[AND need_quantity = :need_quantity]>
        <[AND scan_quantity = :scan_quantity]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND status = :status]>
        <[AND fn_sku = :fn_sku]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySowStockoutList" >
    <content >
      <![CDATA[
        SELECT id, stockout_box, box, apv_no, apv_id, sku, need_quantity, scan_quantity, 
        created_by, creation_date, status, old_task_type, fn_sku
        FROM sow_stockout
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND stockout_box = :stockout_box]>
        <[AND box = :box]>
        <[AND apv_no = :apv_no]>
        <[AND apv_id = :apv_id]>
        <[AND sku = :sku]>
        <[AND need_quantity = :need_quantity]>
        <[AND scan_quantity = :scan_quantity]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND status = :status]>
        <[AND fn_sku = :fn_sku]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySowStockoutByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, stockout_box, box, apv_no, apv_id, sku, need_quantity, scan_quantity, 
        created_by, creation_date, status, old_task_type, fn_sku
        FROM sow_stockout
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySowStockout" >
    <content >
      <![CDATA[
        SELECT id, stockout_box, box, apv_no, apv_id, sku, need_quantity, scan_quantity, 
        created_by, creation_date, status, old_task_type, fn_sku
        FROM sow_stockout
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND stockout_box = :stockout_box]>
        <[AND box = :box]>
        <[AND apv_no = :apv_no]>
        <[AND apv_id = :apv_id]>
        <[AND sku = :sku]>
        <[AND need_quantity = :need_quantity]>
        <[AND scan_quantity = :scan_quantity]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND status = :status]>
        <[AND fn_sku = :fn_sku]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createSowStockout" >
    <content >
      <![CDATA[
        INSERT INTO sow_stockout (stockout_box, box, apv_no, apv_id, sku, need_quantity, scan_quantity, 
          created_by, creation_date, status, old_task_type, fn_sku)
        VALUES (:stockout_box, :box, :apv_no, :apv_id, :sku, :need_quantity, :scan_quantity, 
          :created_by, :creation_date, :status, :old_task_type, :fn_sku)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteSowStockoutByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM sow_stockout
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteSowStockoutByStockoutBox" >
    <content >
      <![CDATA[
        DELETE FROM sow_stockout
        WHERE 1 = 1
        AND stockout_box = :stockout_box
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateSowStockoutByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE sow_stockout
        SET <[stockout_box = :stockout_box,]>
          <[box = :box,]>
          <[apv_no = :apv_no,]>
          <[apv_id = :apv_id,]>
          <[sku = :sku,]>
          <[fn_sku = :fn_sku,]>
          <[need_quantity = :need_quantity,]>
          <[scan_quantity = :scan_quantity,]>
          <[created_by = :created_by,]>
          <[creation_date = :creation_date,]>
          <[status = :status,]>
          <[old_task_type = :old_task_type,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  
  <sql datasource="dataSource" id="updateSowStockoutByStockoutBox" >
    <content >
      <![CDATA[
        UPDATE sow_stockout
        SET 
          <[status = :status,]>
        id = id
        WHERE 1 = 1
        AND stockout_box = :stockout_box
      ]]>
    </content>
  </sql>
  
</sqlmap>