<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="selectExistTable" >
    <content >
      <![CDATA[
        SELECT !ISNULL(TABLE_NAME) as exist  
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = :data_source_table
         <[AND TABLE_NAME = :table_name]>;
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="executeSqlScript" >
    <content >
      <![CDATA[
       <[:sql_content]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="pmsSaleChannel" >
    <content >
      <![CDATA[
      	SELECT name, code  
        FROM salechannel
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="findAllCountrys" >
    <content >
      <![CDATA[
      	select id,shortname,name,chinesename,aliasname1,aliasname2,aliasname3,aliasname4,aliasname5,
        aliasname6,aliasname7,aliasname8,aliasname9,aliasname10,twowordname,szcountrychinesename,szcountrycode,alionlinename
        from country
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateUpTimeLocal" >
    <content >
      <![CDATA[
       UPDATE frozen_stock
        SET check_in_up_time = :check_in_up_time
        WHERE stock_id = :stock_id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateUpTimeTransfer" >
    <content >
      <![CDATA[
       UPDATE transfer_stock_time_relation
        SET last_up_time = :last_up_time
        WHERE stock_id = :stock_id
      ]]>
    </content>
  </sql>

</sqlmap>