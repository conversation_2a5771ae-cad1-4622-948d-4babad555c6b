<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhSecurityApvReturnCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_security_apv_return
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_id = :return_id]>
        <[AND apv_no = :apv_no]>
        <[AND status = :status]>
        <[AND apv_no IN (:apvNos)]>
        <[AND status IN (:statuses)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhSecurityApvReturnList" >
    <content >
      <![CDATA[
        SELECT id, return_id, apv_no, status
        FROM wh_security_apv_return
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_id = :return_id]>
        <[AND apv_no = :apv_no]>
        <[AND status = :status]>
        <[AND apv_no IN (:apvNos)]>
        <[AND status IN (:statuses)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhSecurityApvReturnByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, return_id, apv_no, status
        FROM wh_security_apv_return
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhSecurityApvReturn" >
    <content >
      <![CDATA[
        SELECT id, return_id, apv_no, status
        FROM wh_security_apv_return
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_id = :return_id]>
        <[AND apv_no = :apv_no]>
        <[AND status = :status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhSecurityApvReturn" >
    <content >
      <![CDATA[
        INSERT INTO wh_security_apv_return (return_id, apv_no, status)
        VALUES (:return_id, :apv_no, :status)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhSecurityApvReturnByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_security_apv_return
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhSecurityApvReturnByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_security_apv_return
        SET <[return_id = :return_id,]>
          <[apv_no = :apv_no,]>
          <[status = :status,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>