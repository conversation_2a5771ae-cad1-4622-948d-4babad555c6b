<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >

  <sql datasource="dataSource" id="querySegmentBySkusForAli" >
    <content >
      <![CDATA[
        SELECT seg.*, NULL AS name, NULL AS image_url
        FROM t_unique_no_segment seg
        where seg.sku in (:skus)
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhUniqueSkus" >
    <content >
      <![CDATA[
        SELECT *
        FROM t_wh_unique_sku
        WHERE creation_date BETWEEN STR_TO_DATE(:dateBegin, '%Y-%m-%d') AND STR_TO_DATE(:dateEnd, '%Y-%m-%d')
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateUniqueNoSegment" >
    <content >
      <![CDATA[
        UPDATE t_unique_no_segment
        SET
          <[unique_no_min = :unique_no_min,]>
          <[unique_no_max = :unique_no_max,]>
          <[update_date = :update_date,]>
          <[updated_by = :updated_by,]>
          id = id
        WHERE sku = :sku
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="deleteBySkuForAli">
    <content >
      <![CDATA[
        DELETE FROM t_unique_no_segment WHERE sku in (:sku)
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="saveUniqueNoSegment">
    <content >
      <![CDATA[
        INSERT INTO t_unique_no_segment (sku, unique_no_min, unique_no_max, creation_date, created_by)
        VALUES (:sku, :unique_no_min, :unique_no_max, :creation_date, :created_by)
      ]]>
    </content>
  </sql>

</sqlmap>