<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhLendUniqueCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_lend_unique
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND lend_no = :lend_no]>
        <[AND sku = :sku]>
        <[AND unique_id = :unique_id]>
        <[AND unique_sku = :unique_sku]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhLendUniqueList" >
    <content >
      <![CDATA[
        SELECT id, lend_no, sku, unique_id, unique_sku, creation_date, last_update_date
        FROM wh_lend_unique
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND lend_no = :lend_no]>
        <[AND sku = :sku]>
        <[AND unique_id = :unique_id]>
        <[AND unique_sku = :unique_sku]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhLendUniqueByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, lend_no, sku, unique_id, unique_sku, creation_date, last_update_date
        FROM wh_lend_unique
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhLendUnique" >
    <content >
      <![CDATA[
        SELECT id, lend_no, sku, unique_id, unique_sku, creation_date, last_update_date
        FROM wh_lend_unique
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND lend_no = :lend_no]>
        <[AND sku = :sku]>
        <[AND unique_id = :unique_id]>
        <[AND unique_sku = :unique_sku]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhLendUnique" >
    <content >
      <![CDATA[
        INSERT INTO wh_lend_unique (lend_no, sku, unique_id, unique_sku, creation_date, last_update_date
          )
        VALUES (:lend_no, :sku, :unique_id, :unique_sku, :creation_date, :last_update_date
          )
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhLendUniqueByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_lend_unique
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhLendUniqueByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_lend_unique
        SET <[lend_no = :lend_no,]>
          <[sku = :sku,]>
          <[unique_id = :unique_id,]>
          <[unique_sku = :unique_sku,]>
          <[creation_date = :creation_date,]>
          <[last_update_date = :last_update_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>