<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhBatchReturnUuidCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_batch_return_uuid
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND order_no = :order_no]>
        <[AND sku = :sku]>
        <[AND sku in (:skus)]>
        <[AND uuid = :uuid]>
        <[AND status = :status]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND apv_no = :apv_no]>
        <[AND stock_id = :stock_id]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhBatchReturnUuidList" >
    <content >
      <![CDATA[
        SELECT id, order_no, sku, uuid,status, created_by, creation_date, apv_no,stock_id
        FROM wh_batch_return_uuid
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND order_no = :order_no]>
        <[AND order_no in (:orderNos)]>
        <[AND sku = :sku]>
        <[AND sku in (:skus)]>
        <[AND uuid = :uuid]>
        <[AND uuid in (:uuids)]>
        <[AND status = :status]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND apv_no = :apv_no]>
        <[AND stock_id = :stock_id]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhBatchReturnUuidAndLocationList" >
    <content >
      <![CDATA[
        SELECT u.id, u.order_no, u.sku, u.uuid,u.status, u.created_by, u.creation_date, u.apv_no,u.stock_id,
        case when s.warehouse_id IN (2,4) then
            (select location_number from wh_stock where id IN (SELECT stock_id from wh_transfer_stock where id = u.stock_id))
         else
            (select location_number from wh_stock where id = u.stock_id)
         end as location_number
        FROM wh_batch_return_uuid u
        INNER JOIN wh_batch_return s on s.order_no=u.order_no
        WHERE 1 = 1
        <[AND u.id = :id]>
        <[AND u.order_no = :order_no]>
        <[AND u.order_no in (:orderNos)]>
        <[AND u.sku = :sku]>
        <[AND u.sku in (:skus)]>
        <[AND u.uuid = :uuid]>
        <[AND u.uuid in (:uuids)]>
        <[AND u.status = :status]>
        <[AND u.created_by = :created_by]>
        <[AND u.creation_date = :creation_date]>
        <[AND u.apv_no = :apv_no]>
        <[AND u.stock_id = :stock_id]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryNewBatchReturnUuidAndLocationList" >
    <content >
      <![CDATA[
        SELECT u.id, u.order_no, u.sku, u.uuid,u.status, u.created_by, u.creation_date, u.apv_no,u.stock_id,
        case when i.warehouse_id IN (2,4,8,9) then
            (select location_number from wh_stock where id IN (SELECT stock_id from wh_transfer_stock where id = u.stock_id))
         else
            (select location_number from wh_stock where id = u.stock_id)
         end as location_number
         ,(SELECT location_type FROM wh_location WHERE location = location_number) AS location_type
        FROM wh_batch_return_uuid u
        INNER JOIN wh_intermediate_return s on s.order_no=u.order_no
        LEFT JOIN wh_intermediate_return_apv_item i on i.intermediate_return_id = s.id AND u.apv_no = i.apv_no AND u.sku = i.sku
        WHERE 1 = 1
        <[AND u.id = :id]>
        <[AND u.order_no = :order_no]>
        <[AND u.order_no in (:orderNos)]>
        <[AND u.sku = :sku]>
        <[AND u.sku in (:skus)]>
        <[AND u.uuid = :uuid]>
        <[AND u.uuid in (:uuids)]>
        <[AND u.status = :status]>
        <[AND u.created_by = :created_by]>
        <[AND u.creation_date = :creation_date]>
        <[AND u.apv_no = :apv_no]>
        <[AND u.stock_id = :stock_id]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhBatchReturnUuidByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, order_no, sku, uuid,status, created_by, creation_date, apv_no,stock_id
        FROM wh_batch_return_uuid
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhBatchReturnUuid" >
    <content >
      <![CDATA[
        SELECT id, order_no, sku, uuid,status, created_by, creation_date, apv_no,stock_id
        FROM wh_batch_return_uuid
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND order_no = :order_no]>
        <[AND sku = :sku]>
        <[AND sku in (:skus)]>
        <[AND uuid = :uuid]>
        <[AND status = :status]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND apv_no = :apv_no]>
        <[AND stock_id = :stock_id]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhBatchReturnUuid" >
    <content >
      <![CDATA[
        INSERT INTO wh_batch_return_uuid (order_no, sku, uuid, status, created_by, creation_date, apv_no,stock_id)
        VALUES (:order_no, :sku, :uuid, :status, :created_by, :creation_date, :apv_no, :stock_id)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhBatchReturnUuidByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_batch_return_uuid
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="deleteWhBatchReturnUuidByApvNoList" >
    <content >
      <![CDATA[
        DELETE FROM wh_batch_return_uuid
        WHERE 1 = 1
        AND apv_no IN (:apvNoList)
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateWhBatchReturnUuidByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_batch_return_uuid
        SET <[order_no = :order_no,]>
          <[sku = :sku,]>
          <[uuid = :uuid,]>
          <[status = :status,]>
          <[created_by = :created_by,]>
          <[creation_date = :creation_date,]>
          <[apv_no = :apv_no,]>
          <[grid_by = :grid_by,]>
          <[grid_date = :grid_date,]>
          <[stock_id = :stock_id,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="createOldWarehouseWhBatchReturnUuid" >
    <content >
      <![CDATA[
        INSERT INTO wh_batch_return_uuid (id, order_no, sku, uuid, status, created_by, creation_date, apv_no,stock_id)
        VALUES (:id, :order_no, :sku, :uuid, :status, :created_by, :creation_date, :apv_no, :stock_id)
      ]]>
    </content>
  </sql>


  <sql datasource="dataSource" id="queryZfReturnUuid" >
    <content >
      <![CDATA[
        SELECT wus.sku, CONCAT(wus.sku,'=',wus.uuid) AS uuid,ci.sku_id AS stock_id,ci.`comment` AS pickStockId
        FROM wh_unique_sku wus
        LEFT JOIN wh_check_in_item ci ON ci.in_id = wus.relation_id
        WHERE 1=1
        AND wus.sku = :sku
        AND wus.uuid = :uuid
      ]]>
    </content>
  </sql>
</sqlmap>