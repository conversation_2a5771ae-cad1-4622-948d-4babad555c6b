<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryPickInventoryDemandToApvCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM pick_inventory_demand_to_apv
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_sku_id = :task_sku_id]>
        <[AND apv_id = :apv_id]>
        <[AND task_id = :task_id]>
        <[AND sku = :sku]>
        <[AND apv_id in (:apv_id_list)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPickInventoryDemandToApvList" >
    <content >
      <![CDATA[
        SELECT id, task_sku_id, apv_id, sku, task_id
        FROM pick_inventory_demand_to_apv
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_sku_id = :task_sku_id]>
        <[AND apv_id = :apv_id]>
        <[AND task_id = :task_id]>
        <[AND sku = :sku]>
        <[AND apv_id in (:apv_id_list)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPickInventoryDemandToApvByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, task_sku_id, apv_id, sku, task_id
        FROM pick_inventory_demand_to_apv
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPickInventoryDemandToApv" >
    <content >
      <![CDATA[
        SELECT id, task_sku_id, apv_id, sku, task_id
        FROM pick_inventory_demand_to_apv
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_sku_id = :task_sku_id]>
        <[AND apv_id = :apv_id]>
        <[AND task_id = :task_id]>
        <[AND sku = :sku]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createPickInventoryDemandToApv" >
    <content >
      <![CDATA[
        INSERT INTO pick_inventory_demand_to_apv (task_sku_id, apv_id, sku, task_id)
        VALUES (:task_sku_id, :apv_id, :sku, :task_id)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deletePickInventoryDemandToApvByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM pick_inventory_demand_to_apv
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updatePickInventoryDemandToApvByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE pick_inventory_demand_to_apv
        SET <[task_sku_id = :task_sku_id,]>
          <[apv_id = :apv_id,]>
          <[task_id = :task_id,]>
          <[sku = :sku,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>