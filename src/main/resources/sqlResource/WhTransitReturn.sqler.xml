<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhTransitReturnCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_transit_return
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_no = :return_no]>
        <[AND status = :status]>
        <[AND box_no = :box_no]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND complete_date = :complete_date]>
        <[AND receive_date = :receive_date]>
        <[AND return_user = :return_user]>
        <[AND remark = :remark]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhTransitReturnList" >
    <content >
      <![CDATA[
        SELECT id, return_no, status, box_no, created_by, creation_date, last_update_date, 
        last_update_by, complete_date, receive_date, return_user, remark
        FROM wh_transit_return
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_no = :return_no]>
        <[AND status = :status]>
        <[AND box_no = :box_no]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND complete_date = :complete_date]>
        <[AND receive_date = :receive_date]>
        <[AND return_user = :return_user]>
        <[AND remark = :remark]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhTransitReturnByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, return_no, status, box_no, created_by, creation_date, last_update_date, 
        last_update_by, complete_date, receive_date, return_user, remark
        FROM wh_transit_return
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhTransitReturn" >
    <content >
      <![CDATA[
        SELECT id, return_no, status, box_no, created_by, creation_date, last_update_date, 
        last_update_by, complete_date, receive_date, return_user, remark
        FROM wh_transit_return
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_no = :return_no]>
        <[AND status = :status]>
        <[AND box_no = :box_no]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND complete_date = :complete_date]>
        <[AND receive_date = :receive_date]>
        <[AND return_user = :return_user]>
        <[AND remark = :remark]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhTransitReturn" >
    <content >
      <![CDATA[
        INSERT INTO wh_transit_return (return_no, status, box_no, created_by, creation_date, last_update_date, 
          last_update_by, complete_date, receive_date, return_user, remark)
        VALUES (:return_no, :status, :box_no, :created_by, :creation_date, :last_update_date, 
          :last_update_by, :complete_date, :receive_date, :return_user, :remark)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhTransitReturnByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_transit_return
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhTransitReturnByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_transit_return
        SET <[return_no = :return_no,]>
          <[status = :status,]>
          <[box_no = :box_no,]>
          <[created_by = :created_by,]>
          <[creation_date = :creation_date,]>
          <[last_update_date = :last_update_date,]>
          <[last_update_by = :last_update_by,]>
          <[complete_date = :complete_date,]>
          <[receive_date = :receive_date,]>
          <[return_user = :return_user,]>
          <[remark = :remark,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhTransitReturnAndItems">
    <content>
      <![CDATA[
  		select wr.id,wr.return_no,wr.status,wr.box_no,wr.created_by,wr.creation_date,
  		       wr.last_update_date,wr.last_update_by,wr.complete_date,wr.receive_date,
  		       wr.return_user,wr.remark,wri.id,wri.return_id,wri.sku,wri.status,wri.sku_store,wri.site,
  		       wri.quantity,wri.complete_quantity,wri.stock_quantity,wri.creation_date,wri.last_update_date,
  		       wri.sell_sku,wri.stock_id,ws.sku,ws.name,ws.location_number,ws.status
		from wh_transit_return wr
		left join wh_transit_return_item wri on wr.id=wri.return_id
		left join wh_sku ws on wri.sku=ws.sku
		INNER JOIN (
			select wr.id
			from wh_transit_return wr
			<[:INNER_JOIN_SUM]>
			WHERE 1 = 1
	        <[AND wr.id = :id]>
	        <[AND wr.return_no = :return_no]>
	        <[AND wr.status = :status]>
	        <[AND wr.status IN (:statusList)]>
	        <[AND wr.box_no = :box_no]>
	        <[AND wr.created_by = :created_by]>
	        <[AND wr.creation_date = :creation_date]>
	        <[AND wr.last_update_date = :last_update_date]>
	        <[AND wr.last_update_by = :last_update_by]>
	        <[AND wr.complete_date = :complete_date]>
	        <[AND wr.receive_date = :receive_date]>
	        <[AND wr.return_user = :return_user]>
	        <[AND wr.remark = :remark]>
	        <[AND wr.creation_date >= :from_creation_date]>
	        <[AND wr.creation_date <= :to_creation_date]>
	        <[AND wr.id IN (select item.return_id from wh_transit_return_item item where item.sku = :sku)]>
	        <[AND wr.id IN (select item.return_id from wh_transit_return_item item where item.return_order_no = :returnOrderNo)]>
	        <[AND wr.id IN (:ids)]>
	        <[:IsUpDiff]>
	        <[:IsStockDiff]>
	        ORDER BY wr.id DESC
	        <[:LIMIT]>
		) whreturn on wr.id = whreturn.id
  		]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhTransitReturnAndItemCount">
    <content>
      <![CDATA[
  		select count(distinct wr.id)
		from wh_transit_return wr
		<[:INNER_JOIN_SUM]>
  		 WHERE 1 = 1
        <[AND wr.id = :id]>
        <[AND wr.return_no = :return_no]>
        <[AND wr.status = :status]>
        <[AND wr.status IN (:statusList)]>
        <[AND wr.box_no = :box_no]>
        <[AND wr.created_by = :created_by]>
        <[AND wr.creation_date = :creation_date]>
        <[AND wr.last_update_date = :last_update_date]>
        <[AND wr.last_update_by = :last_update_by]>
        <[AND wr.complete_date = :complete_date]>
        <[AND wr.receive_date = :receive_date]>
        <[AND wr.return_user = :return_user]>
        <[AND wr.remark = :remark]>
        <[AND wr.creation_date >= :from_creation_date]>
        <[AND wr.creation_date <= :to_creation_date]>
        <[AND wr.id IN (select item.return_id from wh_transit_return_item item where item.sku = :sku)]>
        <[AND wr.id IN (select item.return_id from wh_transit_return_item item where item.return_order_no = :returnOrderNo)]>
        <[AND wr.id IN (:ids)]>
        <[:IsUpDiff]>
	    <[:IsStockDiff]>
  		]]>
    </content>
  </sql>
</sqlmap>