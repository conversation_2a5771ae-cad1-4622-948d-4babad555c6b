<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="querySkuGpsrWaringCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM sku_gpsr_waring
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND warning_en = :warning_en]>
        <[AND warning_fr = :warning_fr]>
        <[AND warning_de = :warning_de]>
        <[AND warning_es = :warning_es]>
        <[AND warning_it = :warning_it]>
        <[AND warning_pl = :warning_pl]>
        <[AND warning_nl = :warning_nl]>
        <[AND warning_se = :warning_se]>
        <[AND warning_be = :warning_be]>
        <[AND warning_tr = :warning_tr]>
        <[AND warning_cz = :warning_cz]>
        <[AND warning_sk = :warning_sk]>
        <[AND sku IN (:skuList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySkuGpsrWaringList" >
    <content >
      <![CDATA[
        SELECT id, sku, warning_en, warning_fr, warning_de, warning_es, warning_it, warning_pl, 
        warning_nl, warning_se, warning_be, warning_tr, warning_cz, warning_sk,warning_other
        FROM sku_gpsr_waring
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND warning_en = :warning_en]>
        <[AND warning_fr = :warning_fr]>
        <[AND warning_de = :warning_de]>
        <[AND warning_es = :warning_es]>
        <[AND warning_it = :warning_it]>
        <[AND warning_pl = :warning_pl]>
        <[AND warning_nl = :warning_nl]>
        <[AND warning_se = :warning_se]>
        <[AND warning_be = :warning_be]>
        <[AND warning_tr = :warning_tr]>
        <[AND warning_cz = :warning_cz]>
        <[AND warning_sk = :warning_sk]>
        <[AND sku IN (:skuList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySkuGpsrWaringByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, sku, warning_en, warning_fr, warning_de, warning_es, warning_it, warning_pl, 
        warning_nl, warning_se, warning_be, warning_tr, warning_cz, warning_sk,warning_other
        FROM sku_gpsr_waring
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySkuGpsrWaring" >
    <content >
      <![CDATA[
        SELECT id, sku, warning_en, warning_fr, warning_de, warning_es, warning_it, warning_pl, 
        warning_nl, warning_se, warning_be, warning_tr, warning_cz, warning_sk,warning_other
        FROM sku_gpsr_waring
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND warning_en = :warning_en]>
        <[AND warning_fr = :warning_fr]>
        <[AND warning_de = :warning_de]>
        <[AND warning_es = :warning_es]>
        <[AND warning_it = :warning_it]>
        <[AND warning_pl = :warning_pl]>
        <[AND warning_nl = :warning_nl]>
        <[AND warning_se = :warning_se]>
        <[AND warning_be = :warning_be]>
        <[AND warning_tr = :warning_tr]>
        <[AND warning_cz = :warning_cz]>
        <[AND warning_sk = :warning_sk]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createSkuGpsrWaring" >
    <content >
      <![CDATA[
        INSERT INTO sku_gpsr_waring (sku, warning_en, warning_fr, warning_de, warning_es, warning_it, warning_pl, 
          warning_nl, warning_se, warning_be, warning_tr, warning_cz, warning_sk,warning_other)
        VALUES (:sku, :warning_en, :warning_fr, :warning_de, :warning_es, :warning_it, :warning_pl, 
          :warning_nl, :warning_se, :warning_be, :warning_tr, :warning_cz, :warning_sk,:warning_other)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteSkuGpsrWaringByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM sku_gpsr_waring
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateSkuGpsrWaringByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE sku_gpsr_waring
        SET <[sku = :sku,]>
          <[warning_en = :warning_en,]>
          <[warning_fr = :warning_fr,]>
          <[warning_de = :warning_de,]>
          <[warning_es = :warning_es,]>
          <[warning_it = :warning_it,]>
          <[warning_pl = :warning_pl,]>
          <[warning_nl = :warning_nl,]>
          <[warning_se = :warning_se,]>
          <[warning_be = :warning_be,]>
          <[warning_tr = :warning_tr,]>
          <[warning_cz = :warning_cz,]>
          <[warning_sk = :warning_sk,]>
          <[warning_other = :warning_other,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>