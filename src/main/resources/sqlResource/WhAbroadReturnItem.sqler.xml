<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhAbroadReturnItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_abroad_return_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_id = :return_id]>
        <[AND sku_id = :sku_id]>
        <[AND sku = :sku]>
        <[AND status = :status]>
        <[AND status IN (:status_list)]>
        <[AND quantity = :quantity]>
        <[AND complete_quantity = :complete_quantity]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAbroadReturnItemList" >
    <content >
      <![CDATA[
        SELECT id, return_id, sku_id, sku, status, quantity, complete_quantity, creation_date, 
        last_update_date,stock_id
        FROM wh_abroad_return_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_id = :return_id]>
        <[AND sku_id = :sku_id]>
        <[AND sku = :sku]>
        <[AND status = :status]>
        <[AND status IN (:status_list)]>
        <[AND quantity = :quantity]>
        <[AND complete_quantity = :complete_quantity]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAbroadReturnItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, return_id, sku_id, sku, status, quantity, complete_quantity, creation_date, 
        last_update_date,stock_id
        FROM wh_abroad_return_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAbroadReturnItem" >
    <content >
      <![CDATA[
        SELECT id, return_id, sku_id, sku, status, quantity, complete_quantity, creation_date, 
        last_update_date,stock_id
        FROM wh_abroad_return_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_id = :return_id]>
        <[AND sku_id = :sku_id]>
        <[AND sku = :sku]>
        <[AND status = :status]>
        <[AND quantity = :quantity]>
        <[AND complete_quantity = :complete_quantity]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhAbroadReturnItem" >
    <content >
      <![CDATA[
        INSERT INTO wh_abroad_return_item (return_id, sku_id, sku, status, quantity, complete_quantity, creation_date, 
          last_update_date, sale_attribute,stock_id)
        VALUES (:return_id, :sku_id, :sku, :status, :quantity, :complete_quantity, :creation_date, 
          :last_update_date, :sale_attribute, :stock_id)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhAbroadReturnItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_abroad_return_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhAbroadReturnItemByReturnId" >
    <content >
      <![CDATA[
        DELETE FROM wh_abroad_return_item
        WHERE 1 = 1
        AND return_id = :returnId
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhAbroadReturnItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_abroad_return_item
        SET <[return_id = :return_id,]>
          <[sku_id = :sku_id,]>
          <[sku = :sku,]>
          <[status = :status,]>
          <[quantity = :quantity,]>
          <[complete_quantity = :complete_quantity,]>
          <[creation_date = :creation_date,]>
          <[last_update_date = :last_update_date,]>
          <[stock_id = :stock_id,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>