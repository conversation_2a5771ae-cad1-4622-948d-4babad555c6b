<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhReturnCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_return
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_no = :return_no]>
        <[AND status = :status]>
        <[AND type = :type]>
        <[AND box_no = :box_no]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND complete_date = :complete_date]>
        <[AND receive_date = :receive_date]>
        <[AND return_user = :return_user]>
        <[AND remark = :remark]>
        <[AND batch_no = :batch_no]>
        <[AND flag = :flag]>
        <[AND creation_date >= :from_creation_date]>
        <[AND creation_date <= :to_creation_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhReturnList" >
    <content >
      <![CDATA[
        SELECT id, return_no, status, box_no, created_by, creation_date, last_update_date, type, 
        last_update_by, complete_date, receive_date, return_user, remark, flag, batch_no
        FROM wh_return
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_no = :return_no]>
        <[AND status = :status]>
        <[AND type = :type]>
        <[AND box_no = :box_no]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND complete_date = :complete_date]>
        <[AND receive_date = :receive_date]>
        <[AND return_user = :return_user]>
        <[AND remark = :remark]>
        <[AND batch_no = :batch_no]>
        <[AND flag = :flag]>
        <[AND creation_date >= :from_creation_date]>
        <[AND creation_date <= :to_creation_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhReturnByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, return_no, status, box_no, created_by, creation_date, last_update_date, 
        last_update_by, complete_date, receive_date, return_user, remark, type, flag, batch_no
        FROM wh_return
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhReturn" >
    <content >
      <![CDATA[
        SELECT id, return_no, status, box_no, created_by, creation_date, last_update_date, 
        last_update_by, complete_date, receive_date, return_user, remark, type, flag, batch_no
        FROM wh_return
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_no = :return_no]>
        <[AND status = :status]>
        <[AND type = :type]>
        <[AND box_no = :box_no]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND complete_date = :complete_date]>
        <[AND receive_date = :receive_date]>
        <[AND return_user = :return_user]>
        <[AND remark = :remark]>
        <[AND batch_no = :batch_no]>
        <[AND flag = :flag]>
        <[AND creation_date >= :from_creation_date]>
        <[AND creation_date <= :to_creation_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhReturn" >
    <content >
      <![CDATA[
        INSERT INTO wh_return (return_no, status, box_no, created_by, creation_date, last_update_date, 
          last_update_by, complete_date, receive_date, return_user, remark, type, flag, batch_no)
        VALUES (:return_no, :status, :box_no, :created_by, :creation_date, :last_update_date, 
          :last_update_by, :complete_date, :receive_date, :return_user, :remark, :type, :flag, :batch_no)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhReturnByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_return
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhReturnByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_return
        SET <[return_no = :return_no,]>
          <[status = :status,]>
          <[type = :type,]>
          <[box_no = :box_no,]>
          <[created_by = :created_by,]>
          <[creation_date = :creation_date,]>
          <[last_update_date = :last_update_date,]>
          <[last_update_by = :last_update_by,]>
          <[complete_date = :complete_date,]>
          <[receive_date = :receive_date,]>
          <[return_user = :return_user,]>
          <[remark = :remark,]>
          <[flag = :flag,]>
          <[batch_no = :batch_no,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhReturnListAndItems">
  	<content>
  		<![CDATA[
  		select wr.id,wr.return_no,wr.status,wr.box_no,wr.created_by,wr.creation_date,
  		       wr.last_update_date,wr.last_update_by,wr.complete_date,wr.receive_date,
  		       wr.return_user,wr.remark,wri.id,wri.return_id,wri.sku_id,wri.sku,wri.status,
  		       wri.quantity,wri.complete_quantity,wri.stock_quantity,wri.creation_date,wri.last_update_date, wri.return_order_no,
  		       ws.sku,ws.name,IF(wri.stock_id is null,ws.location_number,(select s.location_number from wh_stock s WHERE s.id=wri.stock_id)) AS 'ws.location_number',ws.status, wr.type, wr.flag, wr.batch_no
		from wh_return wr 
		left join wh_return_item wri on wr.id=wri.return_id
		left join wh_sku ws on wri.sku=ws.sku
		INNER JOIN (
			select wr.id
			from wh_return wr
			<[:INNER_JOIN_SUM]>
			WHERE 1 = 1
	        <[AND wr.id = :id]>
	        <[AND wr.return_no = :return_no]>
	        <[AND wr.status = :status]>
	        <[AND wr.status IN (:statusList)]>
	        <[AND wr.type = :type]>
	        <[AND wr.box_no = :box_no]>
	        <[AND wr.created_by = :created_by]>
	        <[AND wr.creation_date = :creation_date]>
	        <[AND wr.last_update_date = :last_update_date]>
	        <[AND wr.last_update_by = :last_update_by]>
	        <[AND wr.complete_date = :complete_date]>
	        <[AND wr.receive_date = :receive_date]>
	        <[AND wr.return_user = :return_user]>
	        <[AND wr.remark = :remark]>
	        <[AND wr.flag = :flag]>
	        <[AND wr.batch_no = :batch_no]>
	        <[AND wr.batch_no IN (:batchNoList)]>
	        <[AND wr.creation_date >= :from_creation_date]>
	        <[AND wr.creation_date <= :to_creation_date]>
	        <[AND wr.id IN (select item.return_id from wh_return_item item where item.sku = :sku)]>
	        <[AND wr.id IN (select item.return_id from wh_return_item item where item.return_order_no = :returnOrderNo)]>
	        <[AND wr.id IN (:ids)]>
	        <[:IsUpDiff]>
	        <[:IsStockDiff]>
	        ORDER BY wr.id DESC
	        <[:LIMIT]>
		) whreturn on wr.id = whreturn.id
  		]]>
  	</content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhReturnListAndItemCount">
  	<content>
  		<![CDATA[
  		select count(distinct wr.id)
		from wh_return wr
		<[:INNER_JOIN_SUM]>
  		 WHERE 1 = 1
        <[AND wr.id = :id]>
        <[AND wr.return_no = :return_no]>
        <[AND wr.status = :status]>
        <[AND wr.status IN (:statusList)]>
        <[AND wr.type = :type]>
        <[AND wr.box_no = :box_no]>
        <[AND wr.created_by = :created_by]>
        <[AND wr.creation_date = :creation_date]>
        <[AND wr.last_update_date = :last_update_date]>
        <[AND wr.last_update_by = :last_update_by]>
        <[AND wr.complete_date = :complete_date]>
        <[AND wr.receive_date = :receive_date]>
        <[AND wr.return_user = :return_user]>
        <[AND wr.remark = :remark]>
        <[AND wr.flag = :flag]>
        <[AND wr.batch_no = :batch_no]>
        <[AND wr.batch_no IN (:batchNoList)]>
        <[AND wr.creation_date >= :from_creation_date]>
        <[AND wr.creation_date <= :to_creation_date]>
        <[AND wr.id IN (select item.return_id from wh_return_item item where item.sku = :sku)]>
        <[AND wr.id IN (select item.return_id from wh_return_item item where item.return_order_no = :returnOrderNo)]>
        <[AND wr.id IN (:ids)]>
        <[:IsUpDiff]>
	    <[:IsStockDiff]>
  		]]>
  	</content>
  </sql>
</sqlmap>