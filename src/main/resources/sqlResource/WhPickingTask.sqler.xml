<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhPickingTaskCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_picking_task wpt
        LEFT  JOIN pick_task_expand pte ON wpt.id=pte.task_id
        <[:LEFT_BOXNO]>
        <[:LEFT_SKU]>
        <[:LEFT_APVNO]>
        WHERE 1 = 1
        <[:WHERE_SKU]>
        <[:WHERE_APVNO]>
        <[:WHERE_BOXNO]>
        <[:NORMAL_TASK]>
        <[:TASK_TYPE_NOT_IN]>
        <[:IS_ASN_STATUS]>
        <[AND wpt.id = :id]>
        <[AND wpt.id IN (:taskIds)]>
        <[AND wpt.task_no = :task_no]>
        <[AND wpt.task_no IN (:taskNos)]>
        <[AND wpt.task_type = :task_type]>
        <[AND wpt.task_status = :task_status]>
        <[AND wpt.task_status IN (:taskStatuss)]>
        <[AND wpt.receive_person = :receive_person]>
        <[AND wpt.created_by = :created_by]>
        <[AND wpt.created_date = :created_date]>
        <[AND wpt.id IN (SELECT DISTINCT task_id FROM wh_picking_task_sku WHERE end_pick_date >= :from_pick_date)]>
        <[AND wpt.id IN (SELECT DISTINCT task_id FROM wh_picking_task_sku WHERE end_pick_date <= :to_pick_date)]>
        <[AND wpt.warehouse_type = :warehouse_type]>
        <[AND wpt.waybill_type = :waybill_type]>
        <[AND wpt.task_level = :task_level]>
        <[AND wpt.receive_date = :receive_date]>
        <[AND wpt.last_update_date = :last_update_date]>
        <[AND wpt.last_update_by = :last_update_by]>
        <[AND wpt.created_date >= :from_created_date]>
        <[AND wpt.created_date <= :to_created_date]>
        <[AND wpt.receive_date >= :from_receive_date]>
        <[AND wpt.receive_date <= :to_receive_date]>
        <[AND wpt.is_printing = :is_printing]>
        <[AND wpt.is_asn = :is_asn]>
        <[AND wpt.cross_floor = :cross_floor]>
        <[AND wpt.task_status != :freeze_status]>
        <[AND pte.grid_status = :grid_status]>
        <[AND pte.grid_status IN (:gridStatusList)]>
        <[AND pte.yc_box_no = :yc_box_no]>
        <[AND wpt.task_type IN (:task_type_list)]>
        <[AND wpt.is_asn IN (:is_asn_list)]>
        <[:IS_SOW_DIFFER]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhPickingTaskList" >
    <content >
      <![CDATA[
      <[:QUERY_GRID_EXPAND_START]>
        SELECT wpt.id, 
        wpt.task_no, 
        wpt.task_type, 
        wpt.task_status, 
        wpt.receive_person, 
        wpt.created_by, 
        wpt.created_date, 
        wpt.warehouse_type, 
        wpt.waybill_type, 
        wpt.task_level,
        wpt.receive_date, 
        wpt.last_update_date, 
        wpt.last_update_by,
        wpt.picking_end_date,
        wpt.is_printing,
        wpt.is_asn,
        wpt.cross_floor,
        wpt.check_quantity,
        wpt.print_user,
        wpt.sow_differ_quantity,
        pte.yc_box_no,
        wpt.print_date
		<[:COUNT_SQL]>
        <[:QUERY_SKU]>
        <[:QUERY_BOX_SQL]>
        FROM wh_picking_task wpt
        LEFT  JOIN pick_task_expand pte ON wpt.id=pte.task_id
        <[:LEFT_BOXNO]>
        <[:LEFT_SKU]>
        <[:LEFT_APVNO]>
        WHERE 1 = 1
        <[:WHERE_SKU]>
        <[:WHERE_APVNO]>
        <[:WHERE_BOXNO]>
        <[:NORMAL_TASK]>
        <[:TASK_TYPE_NOT_IN]>
        <[AND wpt.id = :id]>
        <[AND wpt.id IN (:taskIds)]>
        <[AND wpt.task_no = :task_no]>
        <[AND wpt.task_no IN (:taskNos)]>
        <[AND wpt.task_type = :task_type]>
        <[AND wpt.task_status = :task_status]>
        <[AND wpt.task_status IN (:taskStatuss)]>
        <[AND wpt.receive_person = :receive_person]>
        <[AND wpt.created_by = :created_by]>
        <[AND wpt.created_date = :created_date]>
        <[AND wpt.warehouse_type = :warehouse_type]>
        <[AND wpt.waybill_type = :waybill_type]>
        <[AND wpt.task_level = :task_level]>
        <[AND wpt.receive_date = :receive_date]>
        <[AND wpt.last_update_date = :last_update_date]>
        <[AND wpt.last_update_by = :last_update_by]>
        <[AND wpt.created_date >= :from_created_date]>
        <[AND wpt.created_date <= :to_created_date]>
        <[AND wpt.id IN (SELECT DISTINCT task_id FROM wh_picking_task_sku WHERE end_pick_date >= :from_pick_date)]>
        <[AND wpt.id IN (SELECT DISTINCT task_id FROM wh_picking_task_sku WHERE end_pick_date <= :to_pick_date)]>
        <[AND wpt.receive_date >= :from_receive_date]>
        <[AND wpt.receive_date <= :to_receive_date]>
        <[AND wpt.is_printing = :is_printing]>
        <[AND wpt.is_asn = :is_asn]>
        <[AND wpt.cross_floor = :cross_floor]>
        <[AND pte.grid_status = :grid_status]>
        <[AND pte.grid_status IN (:gridStatusList)]>
        <[AND wpt.id IN (:DownloadtaskIds)]>
        <[AND wpt.task_status != :freeze_status]>
        <[AND pte.yc_box_no = :yc_box_no]>
        <[AND wpt.task_type IN (:task_type_list)]>
        <[AND wpt.is_asn IN (:is_asn_list)]>
        <[:IS_ASN_STATUS]>
        <[:IS_SOW_DIFFER]>
        <[:ORDER_BY]>
        <[:LIMIT]>
        <[:QUERY_GRID_EXPAND_END]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhPickingTaskByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, task_no, task_type, task_status, receive_person, created_by, created_date, 
        warehouse_type, waybill_type, task_level, is_asn, cross_floor,
        receive_date, last_update_date, last_update_by,
        picking_end_date,
        is_printing,
        check_quantity,
        print_user,
        print_date
        FROM wh_picking_task
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhPickingTask" >
    <content >
      <![CDATA[
        SELECT wpt.id, wpt.task_no, wpt.task_type, wpt.task_status, wpt.receive_person, wpt.created_by, 
        wpt.created_date, is_asn, cross_floor,
        wpt.warehouse_type, wpt.waybill_type, wpt.task_level,
        wpt.receive_date, wpt.last_update_date, wpt.last_update_by,
        wpt.picking_end_date,
        wpt.is_printing,
        wpt.check_quantity,
        wpt.print_user,
        wpt.print_date
        FROM wh_picking_task wpt
        WHERE 1 = 1
        <[AND wpt.id = :id]>
        <[AND wpt.task_no = :task_no]>
        <[AND wpt.task_type = :task_type]>
        <[AND wpt.task_status = :task_status]>
        <[AND wpt.receive_person = :receive_person]>
        <[AND wpt.created_by = :created_by]>
        <[AND wpt.created_date = :created_date]>
        <[AND wpt.id IN (SELECT DISTINCT task_id FROM wh_picking_task_sku WHERE end_pick_date >= :from_pick_date)]>
        <[AND wpt.id IN (SELECT DISTINCT task_id FROM wh_picking_task_sku WHERE end_pick_date <= :to_pick_date)]>
        <[AND wpt.warehouse_type = :warehouse_type]>
        <[AND wpt.waybill_type = :waybill_type]>
        <[AND wpt.task_level = :task_level]>
        <[AND wpt.receive_date = :receive_date]>
        <[AND wpt.last_update_date = :last_update_date]>
        <[AND wpt.last_update_by = :last_update_by]>
        <[:NORMAL_TASK]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhPickingTask" >
    <content >
      <![CDATA[
        INSERT INTO wh_picking_task (task_no, task_type, task_status, receive_person, created_by, created_date, 
        warehouse_type, waybill_type, task_level, is_asn, cross_floor,
        receive_date, last_update_date, last_update_by
          )
        VALUES (:task_no, :task_type, :task_status, :receive_person, :created_by, :created_date, 
        :warehouse_type, :waybill_type, :task_level, :is_asn, :cross_floor,
        :receive_date, :last_update_date, :last_update_by
          )
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhPickingTaskByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_picking_task
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhPickingTaskByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_picking_task
        SET <[task_no = :task_no,]>
          <[task_type = :task_type,]>
          <[task_status = :task_status,]>
          <[receive_person = :receive_person,]>
          <[created_by = :created_by,]>
          <[created_date = :created_date,]>
          <[warehouse_type = :warehouse_type,]>
          <[waybill_type = :waybill_type,]>
          <[task_level = :task_level,]>
          <[receive_date = :receive_date,]>
          <[last_update_date = :last_update_date,]>
          <[last_update_by = :last_update_by,]>
          <[picking_end_date = :picking_end_date,]>
          <[is_printing = :is_printing,]>
          <[check_quantity = :check_quantity,]>
          <[print_user = :print_user,]>
          <[print_date = :print_date,]>
          <[cross_floor = :cross_floor,]>
          <[sow_differ_quantity = :sow_differ_quantity,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="receivePickingTask" >
    <content >
      <![CDATA[
        UPDATE wh_picking_task
        SET 
          <[task_status = :task_status,]>
          <[receive_person = :receive_person,]>
          <[receive_date = :receive_date,]>
          <[last_update_date = :last_update_date,]>
          <[last_update_by = :last_update_by,]>
        id = id
        WHERE 1 = 1
        AND id = :id
        AND task_status = :beforeStatus
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="receiveAndPrintingPickingTask" >
    <content >
      <![CDATA[
        UPDATE wh_picking_task
        SET 
          <[task_status = :task_status,]>
          <[is_printing = :is_printing,]>
          <[receive_person = :receive_person,]>
          <[receive_date = :receive_date,]>
          <[last_update_date = :last_update_date,]>
          <[last_update_by = :last_update_by,]>
          <[print_user = :print_user,]>
          <[print_date = :print_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
        AND task_status = :beforeStatus
      ]]>
    </content>
  </sql>
  
  
  
  
  <sql datasource="dataSource" id="queryWhPickingTaskMax" >
    <content >
      <![CDATA[
        SELECT MAX(task_level)
        FROM wh_picking_task
      ]]>
    </content>
  </sql>
  
  
  <sql datasource="dataSource" id="setTopList" >
    <content >
      <![CDATA[
        UPDATE wh_picking_task
        SET <[task_level = :task_level,]>
        id = id
        WHERE 1 = 1
        <[AND id IN (:taskIds)]>
      ]]>
    </content>
  </sql>
  
  
  
  <sql datasource="dataSource" id="queryPrintingWhPickingTask" >
    <content >
      <![CDATA[
        SELECT wpts.id, 
        wpts.task_id, 
        wpts.sku, 
        wpts.quantity, 
        wpts.status, 
        wpt.task_type, 
        wpt.task_no, 
        wpt.warehouse_type, 
        wpt.task_status, 
        wpt.waybill_type,
        ws.warehouse_id, 
        ws.location_number, 
        ws.name,
        packaging.name,
        (select COUNT(wpti.apv_id) from wh_picking_task_item wpti where wpti.task_id = wpt.id ) as orderQuantity
        FROM wh_picking_task_sku wpts 
        LEFT JOIN wh_sku ws on ws.sku = wpts.sku
        LEFT JOIN wh_picking_task wpt on wpt.id =  wpts.task_id
        LEFT JOIN wh_packaging_material_management packaging ON ws.packaging_id = packaging.id

        WHERE 1 = 1
        <[AND wpts.task_id = :id]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPickingQuantity" >
    <content >
      <![CDATA[
        SELECT 
        wpt.task_no, 
        wpt.check_quantity,
        wpt.task_status,
        wpt.is_printing,
        wpt.id, 
        wpt.warehouse_type,
        wpt.task_type,
        (select MAX(wpts.sku) from wh_picking_task_sku wpts where wpts.task_id = wpt.id ) as sku,
        (select SUM(wpts.quantity) from wh_picking_task_sku wpts where wpts.task_id = wpt.id ) as quantity,    
        (select COUNT(wpti.apv_id) from wh_picking_task_item wpti where wpti.task_id = wpt.id ) as orderQuantity
        FROM wh_picking_task wpt 
        WHERE 1 = 1
        <[AND wpt.task_no = :taskNo]>
        <[AND wpt.task_type = :taskType]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updatePickingTaskStatus" >
    <content >
      <![CDATA[
        UPDATE wh_picking_task
        SET 
          <[task_status = :task_status,]>
          <[last_update_date = :last_update_date,]>
          <[last_update_by = :last_update_by,]>
          <[receive_person = :receive_person,]>
          <[receive_date = :receive_date,]>
          <[picking_end_date = :picking_end_date,]>
          <[is_printing = :is_printing,]>
          <[check_quantity = :check_quantity,]>
          <[cross_floor = :cross_floor,]>
        id = id
        WHERE 1 = 1
        AND id = :id
        <[AND task_status = :beforeTaskStatus]>
        <[AND task_status in (:beforeTaskStatuss)]>
      ]]>
    </content>
  </sql>
  
  
  
  <sql datasource="dataSource" id="queryPickQuantity" >
    <content >
      <![CDATA[
        SELECT SUM(wpts.pick_quantity)
        FROM wh_picking_task wpt
		LEFT JOIN wh_picking_task_sku wpts on wpts.task_id = wpt.id
		WHERE 1 = 1
        <[AND wpt.task_no = :task_no]>
      ]]>
    </content>
  </sql>
  
  
  <sql datasource="dataSource" id="untiedPickingTask" >
    <content >
      <![CDATA[
        UPDATE wh_picking_task
        SET 
          <[last_update_date = :last_update_date,]>
          <[last_update_by = :last_update_by,]>
          <[receive_person = :receive_person,]>
		  <[task_type = :task_type,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  
  <sql datasource="dataSource" id="updatePickingTaskForPartToReceived" >
    <content >
      <![CDATA[
        UPDATE wh_picking_task
        SET 
          <[task_status = :task_status,]>
          <[cross_floor = :cross_floor,]>
          <[receive_person = :receive_person,]>
          <[receive_date = :receive_date,]>
          <[last_update_date = :last_update_date,]>
          <[last_update_by = :last_update_by,]>
        id = id
        WHERE 1 = 1
        AND id = :id
        <[:beforeStatus]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updatePickingTaskUser" >
    <content >
      <![CDATA[
        UPDATE wh_picking_task
        SET
          <[task_status = :task_status,]>
          <[last_update_date = :last_update_date,]>
          <[last_update_by = :last_update_by,]>
          <[receive_person = :receive_person,]>
        id = id
        WHERE 1 = 1
        AND id = :id
        AND task_status= :task_status
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="deletePickingTaskByIds">
    <content >
      <![CDATA[
          DELETE wa,wai,was FROM wh_picking_task wa
				INNER JOIN wh_picking_task_item wai ON wa.id = wai.task_id
				INNER JOIN wh_picking_task_sku was ON wa.id = was.task_id
				WHERE  wa.id IN <[:Ids]>
        ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="querySecondaryDistributionSku" >
    <content >
      <![CDATA[
        SELECT ts.sku,ts.pick_quantity
        FROM wh_picking_task t
		LEFT JOIN wh_picking_task_sku ts on ts.task_id = t.id
        WHERE 1 = 1
        <[AND t.task_no = :task_no]>
        <[AND t.task_type in(:taskTypeList)]>
      ]]>
    </content>
  </sql>

</sqlmap>