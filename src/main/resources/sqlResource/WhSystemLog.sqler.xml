<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhSystemLogCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_system_log
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND relevance_id = :relevance_id]>
        <[AND module = :module]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
        <[AND content = :content]>
        <[:sku_log_ignore]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhSystemLogList" >
    <content >
      <![CDATA[
        SELECT id, relevance_id, module, creation_date, create_by, content
        FROM <[:table_index]>
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND relevance_id = :relevance_id]>
        <[AND module = :module]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
        <[AND content = :content]>
        <[:sku_log_ignore]>
        ORDER BY creation_date desc
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhSystemLogByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, relevance_id, module, creation_date, create_by, content
        FROM wh_system_log
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhSystemLog" >
    <content >
      <![CDATA[
        SELECT id, relevance_id, module, creation_date, create_by, content
        FROM wh_system_log
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND relevance_id = :relevance_id]>
        <[AND module = :module]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
        <[AND content = :content]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhSystemLog" >
    <content >
      <![CDATA[
        INSERT INTO <[:table_index]> (relevance_id, module, creation_date, create_by, content)
        VALUES (:relevance_id, :module, :creation_date, :create_by, :content)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhSystemLogByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_system_log
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhSystemLogByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_system_log
        SET <[relevance_id = :relevance_id,]>
          <[module = :module,]>
          <[creation_date = :creation_date,]>
          <[create_by = :create_by,]>
          <[content = :content,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="deleteSystemLogByIds" >
    <content >
      <![CDATA[
        DELETE FROM <[:table_index]>
        WHERE 1 = 1
        AND id IN <[:id_list]>
      ]]>
    </content>
  </sql>
</sqlmap>