<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryCePhotoTaskCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM ce_photo_task
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_no = :task_no]>
        <[AND status = :status]>
        <[AND is_printing = :is_printing]>
        <[AND receive_person = :receive_person]>
        <[AND receive_date = :receive_date]>
        <[AND created_by = :created_by]>
        <[AND created_date = :created_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND picking_end_date = :picking_end_date]>
        <[AND task_no in (:taskNoList)]>
        <[AND id in (:ids)]>
        <[AND created_date >= :fromCreatedDate]>
        <[AND created_date <= :toCreatedDate]>
        <[AND picking_end_date >= :fromPickingEndDate]>
        <[AND picking_end_date <= :toPickingEndDate]>
        <[AND id IN (SELECT DISTINCT i.task_id FROM ce_photo_task_item i LEFT JOIN ce_manage c on c.id=i.ce_id WHERE c.spu IN (:spuList))]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryCePhotoTaskList" >
    <content >
      <![CDATA[
        SELECT c.id, c.task_no, c.status, c.is_printing, c.receive_person, c.receive_date, c.created_by,
        c.created_date, c.last_update_date, c.last_update_by, c.picking_end_date,
        i.id, i.task_id,  i.sku,  i.name,  i.location_number,  i.outsourcing_image,  i.warehouse_image,
        i.last_update_by, i.last_update_date, i.status, i.ce_id
        FROM ce_photo_task c
        LEFT JOIN ce_photo_task_item i ON c.id = i.task_id
        INNER JOIN (
          SELECT id  FROM
          ce_photo_task
          WHERE 1 = 1
          <[AND id = :id]>
          <[AND task_no = :task_no]>
          <[AND status = :status]>
          <[AND is_printing = :is_printing]>
          <[AND receive_person = :receive_person]>
          <[AND receive_date = :receive_date]>
          <[AND created_by = :created_by]>
          <[AND created_date = :created_date]>
          <[AND last_update_date = :last_update_date]>
          <[AND last_update_by = :last_update_by]>
          <[AND picking_end_date = :picking_end_date]>
          <[AND task_no in (:taskNoList)]>
          <[AND id in (:ids)]>
          <[AND created_date >= :fromCreatedDate]>
          <[AND created_date <= :toCreatedDate]>
          <[AND picking_end_date >= :fromPickingEndDate]>
          <[AND picking_end_date <= :toPickingEndDate]>
          <[AND id IN (SELECT DISTINCT i.task_id FROM ce_photo_task_item i LEFT JOIN ce_manage c on c.id=i.ce_id WHERE c.spu IN (:spuList))]>
          <[AND id IN (SELECT DISTINCT task_id FROM ce_photo_task_item WHERE ce_id IN (:ceIdList))]>
           ORDER BY id
          <[:LIMIT]>
        )ct on c.id = ct.id
        ORDER BY c.id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryCePhotoTaskByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, task_no, status, is_printing, receive_person, receive_date, created_by, 
        created_date, last_update_date, last_update_by, picking_end_date
        FROM ce_photo_task
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryCePhotoTask" >
    <content >
      <![CDATA[
        SELECT id, task_no, status, is_printing, receive_person, receive_date, created_by, 
        created_date, last_update_date, last_update_by, picking_end_date
        FROM ce_photo_task
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_no = :task_no]>
        <[AND status = :status]>
        <[AND is_printing = :is_printing]>
        <[AND receive_person = :receive_person]>
        <[AND receive_date = :receive_date]>
        <[AND created_by = :created_by]>
        <[AND created_date = :created_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND picking_end_date = :picking_end_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createCePhotoTask" >
    <content >
      <![CDATA[
        INSERT INTO ce_photo_task (task_no, status, is_printing, receive_person, receive_date, created_by, 
          created_date, last_update_date, last_update_by, picking_end_date)
        VALUES (:task_no, :status, :is_printing, :receive_person, :receive_date, :created_by, 
          :created_date, :last_update_date, :last_update_by, :picking_end_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteCePhotoTaskByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM ce_photo_task
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateCePhotoTaskByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE ce_photo_task
        SET <[task_no = :task_no,]>
          <[status = :status,]>
          <[is_printing = :is_printing,]>
          <[receive_person = :receive_person,]>
          <[receive_date = :receive_date,]>
          <[created_by = :created_by,]>
          <[created_date = :created_date,]>
          <[last_update_date = :last_update_date,]>
          <[last_update_by = :last_update_by,]>
          <[picking_end_date = :picking_end_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>