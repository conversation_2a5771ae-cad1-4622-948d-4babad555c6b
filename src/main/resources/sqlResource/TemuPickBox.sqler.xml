<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryTemuPickBoxCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM temu_pick_box
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id in (SELECT DISTINCT box_id FROM temu_pick_box_item WHERE relation_item_id in (:relation_item_ids))]>
        <[AND account_number = :account_number]>
        <[AND receive_house_id = :receive_house_id]>
        <[AND express_delivery = :express_delivery,]>
        <[AND creation_by = :creation_by]>
        <[AND creation_date = :creation_date]>
        <[AND number = :number]>
        <[AND status = :status]>
        <[:FILTER_PACKAGE_NO]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryTemuPickBoxList" >
    <content >
      <![CDATA[
        SELECT box.id, box.account_number, box.receive_house_id, box.express_delivery, box.creation_by, box.creation_date, box.number,
        box.status
        <[:ITEM_COLUMNS]>
        FROM temu_pick_box box
        <[:ITEM_JOIN]>
        WHERE 1 = 1
        <[AND box.id = :id]>
        <[AND box.id in (SELECT DISTINCT box_id FROM temu_pick_box_item WHERE relation_item_id in (:relation_item_ids))]>
        <[AND box.account_number = :account_number]>
        <[AND box.receive_house_id = :receive_house_id]>
        <[AND box.express_delivery = :express_delivery]>
        <[AND box.creation_by = :creation_by]>
        <[AND box.creation_date = :creation_date]>
        <[AND box.number = :number]>
        <[AND box.status = :status]>
        <[:FILTER_PACKAGE_NO]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTemuPickBoxByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT box.id, box.account_number, box.receive_house_id, box.express_delivery, box.creation_by, box.creation_date, box.number,
        box.status
        FROM temu_pick_box box
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTemuPickBox" >
    <content >
      <![CDATA[
        SELECT box.id, box.account_number, box.receive_house_id, box.express_delivery, box.creation_by, box.creation_date, box.number, box.status
        <[:ITEM_COLUMNS]>
        FROM temu_pick_box box
        <[:ITEM_JOIN]>
        WHERE 1 = 1
        <[AND box.id = :id]>
        <[AND box.id in (SELECT DISTINCT box_id FROM temu_pick_box_item WHERE relation_item_id in (:relation_item_ids))]>
        <[AND box.account_number = :account_number]>
        <[AND box.receive_house_id = :receive_house_id]>
        <[AND box.express_delivery = :express_delivery]>
        <[AND box.creation_by = :creation_by]>
        <[AND box.creation_date = :creation_date]>
        <[AND box.number = :number]>
        <[AND box.status = :status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createTemuPickBox" >
    <content >
      <![CDATA[
        INSERT INTO temu_pick_box (account_number, receive_house_id, express_delivery, creation_by, creation_date, number,
          status)
        VALUES (:account_number, :receive_house_id, :express_delivery, :creation_by, :creation_date, :number,
          :status)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteTemuPickBoxByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM temu_pick_box
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateTemuPickBoxByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE temu_pick_box
        SET <[account_number = :account_number,]>
          <[receive_house_id = :receive_house_id,]>
          <[express_delivery = :express_delivery,]>
          <[creation_by = :creation_by,]>
          <[creation_date = :creation_date,]>
          <[number = :number,]>
          <[status = :status,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>