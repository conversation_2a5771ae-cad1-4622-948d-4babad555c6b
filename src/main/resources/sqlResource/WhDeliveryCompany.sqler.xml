<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhDeliveryCompanyCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM whdeliverycompany
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND code = :code]>
        <[AND name = :name]>
        <[AND collectCompanyId = :collectCompanyId]>
        <[AND collectCompanyCode = :collectCompanyCode]>
        <[AND code IN (:codeList)]>
        <[AND telephone = :telephone]>
        <[AND creatUserName = :creatUserName]>
        <[AND createTime = :createTime]>
        <[AND updateTime = :updateTime]>
        <[AND collectCompanyId in (:collectCompanyIdList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhDeliveryCompanyList" >
    <content >
      <![CDATA[
        SELECT id, code, name, collectCompanyId, collectCompanyCode, telephone, creatUserName, 
        createTime, updateTime
        FROM whdeliverycompany
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND code = :code]>
        <[AND name = :name]>
        <[AND collectCompanyId = :collectCompanyId]>
        <[AND collectCompanyCode = :collectCompanyCode]>
        <[AND code IN (:codeList)]>
        <[AND telephone = :telephone]>
        <[AND creatUserName = :creatUserName]>
        <[AND createTime = :createTime]>
        <[AND updateTime = :updateTime]>
        <[AND collectCompanyId in (:collectCompanyIdList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhDeliveryCompanyByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, code, name, collectCompanyId, collectCompanyCode, telephone, creatUserName, 
        createTime, updateTime
        FROM whdeliverycompany
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhDeliveryCompany" >
    <content >
      <![CDATA[
        SELECT id, code, name, collectCompanyId, collectCompanyCode, telephone, creatUserName, 
        createTime, updateTime
        FROM whdeliverycompany
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND code = :code]>
        <[AND name = :name]>
        <[AND collectCompanyId = :collectCompanyId]>
        <[AND collectCompanyCode = :collectCompanyCode]>
        <[AND telephone = :telephone]>
        <[AND creatUserName = :creatUserName]>
        <[AND createTime = :createTime]>
        <[AND updateTime = :updateTime]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhDeliveryCompany" >
    <content >
      <![CDATA[
        INSERT INTO whdeliverycompany (id, code, name, collectCompanyId, collectCompanyCode, telephone, creatUserName, 
          createTime, updateTime)
        VALUES (:id, :code, :name, :collectCompanyId, :collectCompanyCode, :telephone, :creatUserName, 
          :createTime, :updateTime)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhDeliveryCompanyByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM whdeliverycompany
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhDeliveryCompanyByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE whdeliverycompany
        SET <[code = :code,]>
          <[name = :name,]>
          <[collectCompanyId = :collectCompanyId,]>
          <[collectCompanyCode = :collectCompanyCode,]>
          <[telephone = :telephone,]>
          <[creatUserName = :creatUserName,]>
          <[createTime = :createTime,]>
          <[updateTime = :updateTime,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>