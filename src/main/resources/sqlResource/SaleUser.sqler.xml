<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >

  <!-- 联合查询 -->
	<sql id="queryUserWithRoleCount" datasource="dataSource">
		<content>
			<![CDATA[
				SELECT COUNT(*)
				FROM t_user users
				WHERE 1 = 1
			    <[AND users.name LIKE :like_name]>
			    <[AND users.enabled = :enabled]>
       		    <[AND users.username LIKE :like_username]>
       		    <[AND users.username IN (:username_list)]>
       		    <[AND users.department_id = :department_id]>
			    <[AND users.user_id IN (SELECT user_role2.user_id FROM t_user_role user_role2 WHERE user_role2.role_id IN (:role_list))]>
			    <[AND users.user_id IN (SELECT user_role2.user_id FROM t_user_role user_role2 WHERE user_role2.role_id = :role_id)]>
			    
			    <[AND users.user_id IN (SELECT users2.user_id FROM t_user users2 
				    	left join t_department department2 ON department2.department_id = users2.department_id
				    	WHERE department2.department_id IN (:departmentId_list))]>
				    	
		    	<[AND users.user_id IN (SELECT users2.user_id FROM t_user users2 
				    	left join t_department department2 ON department2.department_id = users2.department_id
			    	WHERE department2.department_id = :department_id)]>
			    	
		    	<[AND users.user_id IN (SELECT user_role2.user_id FROM t_user_role user_role2 
				     						LEFT JOIN t_role role2 ON user_role2.role_id = role2.role_id
				     						WHERE role2.role_name = :role_name)]>
			    <[AND users.user_id IN (:user_id_list)]>
			    <[AND users.user_id = :user_id]>
			    <[AND users.supervisor = :supervisor]>
			    <[AND users.emp_id = :emp_id]>
			    <[AND users.emp_no = :emp_no]>
			    <[AND users.emp_name = :emp_name]>
			    <[AND users.emp_id IN (:emp_id_list)]>
			]]>
		</content>
	</sql>
  
  	<!-- 联合查询(使用子查询是为了查询用户只要有其中一个角色就好，不用完全是那个角色) -->
	<sql id="queryUserWithRoleList" datasource="dataSource">
		<content>
			<![CDATA[
				SELECT users.user_id, users.name, users.job_number, users.username, users.enabled, users.remark, 
				role.role_id, role.role_name,users.emp_id,users.emp_no,users.emp_name,
				department.department_id, department.department_name,
			    user_supervisor.user_id, user_supervisor.name, user_supervisor.username
				FROM t_user users
				
				 	INNER JOIN (SELECT users.user_id
					FROM t_user users
					WHERE 1 = 1
				    <[AND users.name LIKE :like_name]>
				    <[AND users.enabled = :enabled]>
        		    <[AND users.username LIKE :like_username]>
        		    <[AND users.username IN (:username_list)]>
        		    <[AND users.department_id = :department_id]>
       		    	<[AND users.department_id IN (:department_id_list)]>
				    <[AND users.user_id IN (SELECT user_role2.user_id FROM t_user_role user_role2 WHERE user_role2.role_id IN (:role_list))]>
				    <[AND users.user_id IN (SELECT user_role2.user_id FROM t_user_role user_role2 WHERE user_role2.role_id = :role_id)]>
				    
				    <[AND users.user_id IN (SELECT users2.user_id FROM t_user users2 
				    	left join t_department department2 ON department2.department_id = users2.department_id
				    	WHERE department2.department_id IN (:departmentId_list))]>
				    	
			    	<[AND users.user_id IN (SELECT users2.user_id FROM t_user users2 
				    	left join t_department department2 ON department2.department_id = users2.department_id
			    	WHERE department2.department_id = :department_id)]>
			    	
			    	<[AND users.user_id IN (SELECT user_role2.user_id FROM t_user_role user_role2 
				     						LEFT JOIN t_role role2 ON user_role2.role_id = role2.role_id
				     						WHERE role2.role_name = :role_name)]>
				    
				    <[AND users.user_id IN (:user_id_list)]>
				    <[AND users.user_id = :user_id]>
				    <[AND users.supervisor = :supervisor]>
				    <[AND users.remark LIKE :like_remark]>
				    <[AND users.emp_id = :emp_id]>
                    <[AND users.emp_no = :emp_no]>
                    <[AND users.emp_name = :emp_name]>
                    <[AND users.emp_id IN (:emp_id_list)]>
				    <[:LIMIT]>
				    ) X ON users.user_id = X.user_id
				LEFT JOIN t_user_role user_role ON users.user_id = user_role.user_id
				LEFT JOIN t_role role ON user_role.role_id = role.role_id
				LEFT JOIN t_department department ON department.department_id = users.department_id
				LEFT JOIN t_user user_supervisor ON user_supervisor.username = users.supervisor
				WHERE 1 = 1
				
			]]>
		</content>
	</sql>
	

  <sql datasource="dataSource" id="querySaleUserCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM t_user
        WHERE 1 = 1
        <[AND user_id = :user_id]>
        <[AND user_id IN (:user_id_list)]>
        <[AND name = :name]>
        <[AND name LIKE :like_name]>
        <[AND job_number = :job_number]>
        <[AND username = :username]>
        <[AND password = :password]>
        <[AND department_id = :department_id]>
        <[AND creation_date = :creation_date]>
        <[AND created_by = :created_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND remark = :remark]>
        <[AND enabled = :enabled]>
        <[AND contact_details = :contact_details]>
        <[AND supervisor = :supervisor]>
        <[AND emp_id = :emp_id]>
        <[AND emp_no = :emp_no]>
        <[AND emp_name = :emp_name]>
        <[AND name IN (:name_list)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySaleUserListByRoleId" >
    <content >
      <![CDATA[
        SELECT users.user_id, name, job_number, username, password, department_id, creation_date, 
        created_by, last_update_date, last_updated_by, remark, enabled, contact_details, 
        supervisor, emp_id,emp_no,emp_name
        FROM t_user users
        WHERE 1 = 1
        <[AND users.user_id IN (SELECT user_id FROM t_user_role user_role WHERE user_role.role_id = :role_id)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySaleUserList" >
    <content >
      <![CDATA[
        SELECT user_id, name, job_number, username, password, department_id, creation_date, 
        created_by, last_update_date, last_updated_by, remark, enabled, contact_details, 
        supervisor, emp_id,emp_no,emp_name
        FROM t_user
        WHERE 1 = 1
        <[AND user_id = :user_id]>
        <[AND user_id IN (:user_id_list)]>
        <[AND name = :name]>
        <[AND name LIKE :like_name]>
        <[AND job_number = :job_number]>
        <[AND username = :username]>
        <[AND password = :password]>
        <[AND department_id = :department_id]>
        <[AND department_id IN (:departmentId_list)]>
        <[AND creation_date = :creation_date]>
        <[AND created_by = :created_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND remark = :remark]>
        <[AND enabled = :enabled]>
        <[AND contact_details = :contact_details]>
        <[AND supervisor = :supervisor]>
        <[AND emp_id = :emp_id]>
        <[AND emp_no = :emp_no]>
        <[AND emp_name = :emp_name]>
        <[AND emp_id IN (:emp_id_list)]>
        <[AND name IN (:name_list)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySaleUserByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT user_id, name, job_number, username, password, department_id, creation_date, 
        created_by, last_update_date, last_updated_by, remark, enabled, contact_details, 
        supervisor, emp_id,emp_no,emp_name
        FROM t_user
        WHERE 1 = 1
        AND user_id = :user_id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySaleUser" >
    <content >
      <![CDATA[
        SELECT user_id, name, job_number, username, password, department_id, creation_date, 
        created_by, last_update_date, last_updated_by, remark, enabled, contact_details, 
        supervisor, emp_id,emp_no,emp_name
        FROM t_user
        WHERE 1 = 1
        <[AND user_id = :user_id]>
        <[AND name = :name]>
        <[AND job_number = :job_number]>
        <[AND username = :username]>
        <[AND password = :password]>
        <[AND department_id = :department_id]>
        <[AND creation_date = :creation_date]>
        <[AND created_by = :created_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND remark = :remark]>
        <[AND enabled = :enabled]>
        <[AND contact_details = :contact_details]>
        <[AND supervisor = :supervisor]>
        <[AND emp_id = :emp_id]>
        <[AND emp_no = :emp_no]>
        <[AND emp_name = :emp_name]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createSaleUser" >
    <content >
      <![CDATA[
        INSERT INTO t_user (user_id, name, job_number, username, password, department_id, creation_date, 
          created_by, last_update_date, last_updated_by, remark, enabled, contact_details, 
          supervisor, emp_id,emp_no,emp_name)
        VALUES (:user_id, :name, :job_number, :username, :password, :department_id, :creation_date, 
          :created_by, :last_update_date, :last_updated_by, :remark, :enabled, :contact_details, 
          :supervisor, :emp_id,:emp_no,:emp_name)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createSaleUserNotId" >
    <content >
      <![CDATA[
        INSERT INTO t_user (name, job_number, username, password, department_id, creation_date, 
          created_by, last_update_date, last_updated_by, remark, enabled, contact_details, 
          supervisor, emp_id,emp_no,emp_name)
        VALUES (:name, :job_number, :username, :password, :department_id, :creation_date, 
          :created_by, :last_update_date, :last_updated_by, :remark, :enabled, :contact_details, 
          :supervisor, :emp_id,:emp_no,:emp_name)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteSaleUserByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM t_user
        WHERE 1 = 1
        AND user_id = :user_id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateSaleUserByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE t_user
        SET <[name = :name,]>
          <[job_number = :job_number,]>
          <[username = :username,]>
          <[password = :password,]>
          <[department_id = :department_id,]>
          <[creation_date = :creation_date,]>
          <[created_by = :created_by,]>
          <[last_update_date = :last_update_date,]>
          <[last_updated_by = :last_updated_by,]>
          <[remark = :remark,]>
          <[enabled = :enabled,]>
          <[contact_details = :contact_details,]>
          <[supervisor = :supervisor,]>
          <[emp_id = :emp_id,]>
          <[emp_no = :emp_no,]>
          <[emp_name = :emp_name,]>
        user_id = user_id
        WHERE 1 = 1
        AND user_id = :user_id
      ]]>
    </content>
  </sql>
  
    <sql datasource="dataSource" id="queryExUserByUsername" >
    <content >
      <![CDATA[
        SELECT u.* FROM t_user u
		JOIN t_user_role ur ON (ur.user_id = u.user_id)
		JOIN t_role r ON (ur.role_id = r.role_id AND r.role_name = :role_name)
		WHERE u.username = :username
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryUserByRolename" >
    <content >
      <![CDATA[
        select users.* from t_user users
        INNER JOIN (SELECT user_role2.user_id FROM t_user_role user_role2
                    LEFT JOIN t_role role2 ON user_role2.role_id = role2.role_id
                    WHERE 1 = 1
                    <[AND role2.role_name = :role_name]>
            ) role on users.user_id = role.user_id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryUserByPermissionCode" >
    <content >
      <![CDATA[
        SELECT u.*
        FROM t_user u
        WHERE u.user_id in
        (SELECT ur.user_id FROM t_role_permission rp
        INNER JOIN t_user_role ur ON ur.role_id = rp.role_id WHERE rp.permission_code in (:permissionCode))
      ]]>
    </content>
  </sql>
</sqlmap>