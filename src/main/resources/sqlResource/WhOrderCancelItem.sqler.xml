<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhOrderCancelItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_order_cancel_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id in (:ids)]>
        <[AND apv_no = :apv_no]>
        <[AND apv_no in (:apvNoList)]>
        <[AND push_date = :push_date]>
        <[AND cancel_date = :cancel_date]>
        <[AND cancel_by = :cancel_by]>
        <[AND cancel_type = :cancel_type]>
        <[AND cancel_date >= :START_CANCEL_DATE]>
        <[AND cancel_date <= :END_CANCEL_DATE]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhOrderCancelItemList" >
    <content >
      <![CDATA[
        SELECT id, apv_no, push_date, cancel_date, cancel_by, cancel_type
        FROM wh_order_cancel_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id in (:ids)]>
        <[AND apv_no = :apv_no]>
        <[AND apv_no in (:apvNoList)]>
        <[AND push_date = :push_date]>
        <[AND cancel_date = :cancel_date]>
        <[AND cancel_by = :cancel_by]>
        <[AND cancel_type = :cancel_type]>
        <[AND cancel_date >= :START_CANCEL_DATE]>
        <[AND cancel_date <= :END_CANCEL_DATE]>
        ORDER BY cancel_date DESC
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhOrderCancelItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, apv_no, push_date, cancel_date, cancel_by, cancel_type
        FROM wh_order_cancel_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhOrderCancelItem" >
    <content >
      <![CDATA[
        SELECT id, apv_no, push_date, cancel_date, cancel_by, cancel_type
        FROM wh_order_cancel_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_no = :apv_no]>
        <[AND push_date = :push_date]>
        <[AND cancel_date = :cancel_date]>
        <[AND cancel_by = :cancel_by]>
        <[AND cancel_type = :cancel_type]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhOrderCancelItem" >
    <content >
      <![CDATA[
        INSERT INTO wh_order_cancel_item (apv_no, push_date, cancel_date, cancel_by, cancel_type)
        VALUES (:apv_no, :push_date, :cancel_date, :cancel_by, :cancel_type)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhOrderCancelItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_order_cancel_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhOrderCancelItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_order_cancel_item
        SET <[apv_no = :apv_no,]>
          <[push_date = :push_date,]>
          <[cancel_date = :cancel_date,]>
          <[cancel_by = :cancel_by,]>
          <[cancel_type = :cancel_type,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>


  <sql datasource="dataSource" id="queryWhOrderCancelList" >
    <content >
      <![CDATA[
        SELECT
         <[:DATE_FORMAT]>
          COUNT(IF(cancel_type=1,1,null)) AS beforePickingQuantity,
		  COUNT(IF(cancel_type=2,1,null)) AS AfterPickingQuantity,
		  COUNT(IF(cancel_type=3,1,null)) AS underStockQuantity,
		  COUNT(IF(cancel_type=4,1,null)) AS pacAllotCancelQuantity,
		  COUNT(IF(cancel_type=5,1,null)) AS warehouseIdCancelQuantity,
		  COUNT(IF(cancel_type=6,1,null)) AS trackingNumberCancelQuantity,
		  COUNT(IF(cancel_type=7,1,null)) AS stockoutNotCancelQuantity
        FROM wh_order_cancel_item
        WHERE 1 = 1
        <[AND cancel_date >= :start_time]>
		<[AND cancel_date < :end_time]>
		 <[:GROUP_BY]>
      ]]>
    </content>
  </sql>

</sqlmap>