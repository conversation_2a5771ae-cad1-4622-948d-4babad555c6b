<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhLocationStorageCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_location_storage
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND name = :name]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhLocationStorageList" >
    <content >
      <![CDATA[
        SELECT id, name
        FROM wh_location_storage
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND name = :name]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhLocationStorageByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, name
        FROM wh_location_storage
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhLocationStorage" >
    <content >
      <![CDATA[
        SELECT id, name
        FROM wh_location_storage
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND name = :name]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhLocationStorage" >
    <content >
      <![CDATA[
        INSERT INTO wh_location_storage (name)
        VALUES (:name)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhLocationStorageByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_location_storage
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhLocationStorageByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_location_storage
        SET <[name = :name,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>