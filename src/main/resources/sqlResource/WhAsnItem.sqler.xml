<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhAsnItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_asn_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND wh_asn_id = :wh_asn_id]>
        <[AND product_sku = :product_sku]>
        <[AND product_barcode = :product_barcode]>
        <[AND quantity = :quantity]>
        <[AND received_quantity = :received_quantity]>
        <[AND putaway_qty = :putaway_qty]>
        <[AND box_no = :box_no]>
        <[AND putaway_quantity = :putaway_quantity]>
        <[AND product_price = :product_price]>
        <[AND rd_update_time = :rd_update_time]>
        <[AND product_length = :product_length]>
        <[AND product_width = :product_width]>
        <[AND product_height = :product_height]>
        <[AND product_weight = :product_weight]>
        <[AND allot_quantity = :allot_quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND loading_quantity = :loading_quantity]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAsnItemList" >
    <content >
      <![CDATA[
        SELECT id, wh_asn_id, product_sku, product_barcode, quantity, received_quantity, 
        putaway_qty, box_no, putaway_quantity, product_price, rd_update_time, product_length, 
        product_width, product_height, product_weight, allot_quantity, pick_quantity, loading_quantity, warehouse_id,
        seller_sku,seller_sku_name
        FROM wh_asn_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND wh_asn_id = :wh_asn_id]>
        <[AND product_sku = :product_sku]>
        <[AND product_barcode = :product_barcode]>
        <[AND quantity = :quantity]>
        <[AND received_quantity = :received_quantity]>
        <[AND putaway_qty = :putaway_qty]>
        <[AND box_no = :box_no]>
        <[AND putaway_quantity = :putaway_quantity]>
        <[AND product_price = :product_price]>
        <[AND rd_update_time = :rd_update_time]>
        <[AND product_length = :product_length]>
        <[AND product_width = :product_width]>
        <[AND product_height = :product_height]>
        <[AND product_weight = :product_weight]>
        <[AND allot_quantity = :allot_quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND loading_quantity = :loading_quantity]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAsnItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, wh_asn_id, product_sku, product_barcode, quantity, received_quantity, 
        putaway_qty, box_no, putaway_quantity, product_price, rd_update_time, product_length, 
        product_width, product_height, product_weight, allot_quantity, pick_quantity, loading_quantity, warehouse_id,
        seller_sku,seller_sku_name
        FROM wh_asn_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAsnItem" >
    <content >
      <![CDATA[
        SELECT id, wh_asn_id, product_sku, product_barcode, quantity, received_quantity, 
        putaway_qty, box_no, putaway_quantity, product_price, rd_update_time, product_length, 
        product_width, product_height, product_weight, allot_quantity, pick_quantity, loading_quantity, warehouse_id,
         seller_sku,seller_sku_name
        FROM wh_asn_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND wh_asn_id = :wh_asn_id]>
        <[AND product_sku = :product_sku]>
        <[AND product_barcode = :product_barcode]>
        <[AND quantity = :quantity]>
        <[AND received_quantity = :received_quantity]>
        <[AND putaway_qty = :putaway_qty]>
        <[AND box_no = :box_no]>
        <[AND putaway_quantity = :putaway_quantity]>
        <[AND product_price = :product_price]>
        <[AND rd_update_time = :rd_update_time]>
        <[AND product_length = :product_length]>
        <[AND product_width = :product_width]>
        <[AND product_height = :product_height]>
        <[AND product_weight = :product_weight]>
        <[AND allot_quantity = :allot_quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND loading_quantity = :loading_quantity]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhAsnItem" >
    <content >
      <![CDATA[
        INSERT INTO wh_asn_item (wh_asn_id, product_sku, product_barcode, quantity, received_quantity, 
          putaway_qty, box_no, putaway_quantity, product_price, rd_update_time, product_length, 
          product_width, product_height, product_weight, allot_quantity, pick_quantity, 
          loading_quantity,warehouse_id,seller_sku,seller_sku_name)
        VALUES (:wh_asn_id, :product_sku, :product_barcode, :quantity, :received_quantity, 
          :putaway_qty, :box_no, :putaway_quantity, :product_price, :rd_update_time, :product_length, 
          :product_width, :product_height, :product_weight, :allot_quantity, :pick_quantity, 
          :loading_quantity,:warehouse_id,:seller_sku,:seller_sku_name)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhAsnItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_asn_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhAsnItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_asn_item
        SET <[wh_asn_id = :wh_asn_id,]>
          <[product_sku = :product_sku,]>
          <[product_barcode = :product_barcode,]>
          <[quantity = :quantity,]>
          <[received_quantity = :received_quantity,]>
          <[putaway_qty = :putaway_qty,]>
          <[box_no = :box_no,]>
          <[putaway_quantity = :putaway_quantity,]>
          <[product_price = :product_price,]>
          <[rd_update_time = :rd_update_time,]>
          <[product_length = :product_length,]>
          <[product_width = :product_width,]>
          <[product_height = :product_height,]>
          <[product_weight = :product_weight,]>
          <[allot_quantity = :allot_quantity,]>
          <[pick_quantity = :pick_quantity,]>
          <[loading_quantity = :loading_quantity,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>