<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhRecordCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_record
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND sku IN (:sku_list)]>
        <[AND quantity = :quantity]>
        <[AND on_passage_quantity = :on_passage_quantity]>
        <[AND overseas_passage_quantity = :overseas_passage_quantity]>
        <[AND surplus_quantity = :surplus_quantity]>
        <[AND safety_quantity = :safety_quantity]>
        <[AND readiness_quantity = :readiness_quantity]>
        <[AND lack_quantity = :lack_quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND price = :price]>
        <[AND last_price = :last_price]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND last_order_date = :last_order_date]>
        <[AND last_surplus_date = :last_surplus_date]>
        <[AND surplus_quantity <= :lessSurplusQuantity]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhRecordList" >
    <content >
      <![CDATA[
        SELECT id, sku, quantity, on_passage_quantity, overseas_passage_quantity, surplus_quantity, allocation_quantity, 
        safety_quantity, readiness_quantity, lack_quantity, pick_quantity, warehouse_id, 
        price, last_price, remark, creation_date, last_update_date, last_updated_by, last_order_date, last_surplus_date
        FROM wh_record T1
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND sku IN (:sku_list)]>
        <[AND quantity = :quantity]>
        <[AND on_passage_quantity = :on_passage_quantity]>
        <[AND overseas_passage_quantity = :overseas_passage_quantity]>
        <[AND surplus_quantity = :surplus_quantity]>
        <[AND safety_quantity = :safety_quantity]>
        <[AND readiness_quantity = :readiness_quantity]>
        <[AND lack_quantity = :lack_quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND sku IN (SELECT T2.sku FROM wh_sku T2 WHERE T1.sku=T2.sku AND T2.warehouse_id = :warehouse_id)]>
        <[AND price = :price]>
        <[AND last_price = :last_price]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND last_order_date = :last_order_date]>
        <[AND last_surplus_date = :last_surplus_date]>
        <[AND surplus_quantity >= :thenSurplusQuantity]>
        <[AND surplus_quantity <= :lessSurplusQuantity]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhRecordByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, sku, quantity, on_passage_quantity, overseas_passage_quantity, surplus_quantity, allocation_quantity, 
        safety_quantity, readiness_quantity, lack_quantity, pick_quantity, warehouse_id, 
        price, last_price, remark, creation_date, last_update_date, last_updated_by, last_order_date, last_surplus_date
        FROM wh_record
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhRecord" >
    <content >
      <![CDATA[
        SELECT id, sku, quantity, on_passage_quantity, overseas_passage_quantity, surplus_quantity, 
        safety_quantity, readiness_quantity, lack_quantity, pick_quantity, warehouse_id, allocation_quantity, 
        price, last_price, remark, creation_date, last_update_date, last_updated_by, last_order_date, last_surplus_date
        FROM wh_record
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND sku IN (:sku_list)]>
        <[AND quantity = :quantity]>
        <[AND on_passage_quantity = :on_passage_quantity]>
        <[AND overseas_passage_quantity = :overseas_passage_quantity]>
        <[AND surplus_quantity = :surplus_quantity]>
        <[AND safety_quantity = :safety_quantity]>
        <[AND readiness_quantity = :readiness_quantity]>
        <[AND lack_quantity = :lack_quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND price = :price]>
        <[AND last_price = :last_price]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND last_order_date = :last_order_date]>
        <[AND last_surplus_date = :last_surplus_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhRecord" >
    <content >
      <![CDATA[
        INSERT INTO wh_record (sku, quantity, on_passage_quantity, overseas_passage_quantity, surplus_quantity, 
          safety_quantity, readiness_quantity, lack_quantity, pick_quantity, warehouse_id, 
          price, last_price, remark, creation_date, last_update_date, last_updated_by, 
          last_order_date)
        VALUES (:sku, :quantity, :on_passage_quantity, :overseas_passage_quantity, :surplus_quantity, 
          :safety_quantity, :readiness_quantity, :lack_quantity, :pick_quantity, :warehouse_id, 
          :price, :last_price, :remark, :creation_date, :last_update_date, :last_updated_by, 
          :last_order_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhRecordByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_record
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhRecordByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_record
        SET <[sku = :sku,]>
          <[quantity = :quantity,]>
          <[on_passage_quantity = :on_passage_quantity,]>
          <[overseas_passage_quantity = :overseas_passage_quantity,]>
          <[surplus_quantity = :surplus_quantity,]>
          <[allocation_quantity = :allocation_quantity,]>
          <[safety_quantity = :safety_quantity,]>
          <[readiness_quantity = :readiness_quantity,]>
          <[lack_quantity = :lack_quantity,]>
          <[pick_quantity = :pick_quantity,]>
          <[warehouse_id = :warehouse_id,]>
          <[price = :price,]>
          <[last_price = :last_price,]>
          <[remark = :remark,]>
          <[creation_date = :creation_date,]>
          <[last_update_date = :last_update_date,]>
          <[last_updated_by = :last_updated_by,]>
          <[last_order_date = :last_order_date,]>
          <[last_surplus_date = :last_surplus_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySyncPmsRecord" >
    <content >
      <![CDATA[
        SELECT 
        	whRecord.id, 
        	whRecord.sku, 
        	whRecord.quantity, 
        	whRecord.on_passage_quantity, 
        	whRecord.overseas_passage_quantity, 
        	whRecord.surplus_quantity, 
        	whRecord.allocation_quantity, 
	        whRecord.safety_quantity, 
	        whRecord.readiness_quantity, 
	        whRecord.lack_quantity, 
	        whRecord.pick_quantity, 
	        whRecord.warehouse_id, 
	        whRecord.price, 
	        whRecord.last_price, 
	        whRecord.remark, 
	        whRecord.creation_date, 
	        whRecord.last_update_date, 
	        whRecord.last_updated_by, 
	        whRecord.last_order_date, 
	        whRecord.last_surplus_date
        FROM 
        	wh_record whRecord LEFT JOIN wh_sku whSku ON whRecord.sku=whSku.sku
        WHERE 
        	1 = 1
        	<[AND whRecord.last_surplus_date > DATE_SUB(NOW(),INTERVAL :syn_time_hour HOUR)]>
        	<[AND whSku.warehouse_id = :warehouse_id]>
        	AND whSku.location_number IS NOT NULL
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPageWhRecordsCount" >
    <content >
      <![CDATA[
        SELECT 
        	COUNT(1)
        FROM 
        	wh_record T1 
        	<[:wh_sku]>
        WHERE 
        	1 = 1
	        <[AND T1.sku IN (SELECT T2.sku FROM wh_sku T2 WHERE T2.warehouse_id = :warehouse_id)]>
	        <[:skus_condition]>
	        <[AND T1.sku IN (SELECT T2.sku FROM wh_sku T2 WHERE T2.location_number IN (:location_no_list))]>
	        
	        <[:area_condition]>
	        <[:access_condition]>
	        <[AND T1.sku IN (SELECT T2.sku FROM wh_sku T2 WHERE T2.name LIKE :sku_name_condition)]>

	        <[AND T1.sku IN (SELECT T2.sku FROM wh_sku T2 WHERE T2.no_stock_up = :no_stock_up)]>
	        
	        <[AND T1.quantity >= :thenQuantity]>
	        <[AND T1.quantity <= :lessQuantity]>
	        
	        <[AND T1.surplus_quantity >= :thenSurplusQuantity]>
	        <[AND T1.surplus_quantity <= :lessSurplusQuantity]>
	        
	        <[AND T1.pick_quantity >= :thenPickQuantity]>
	        <[AND T1.pick_quantity <= :lessPickQuantity]>
	        
	        <[AND (SELECT SUM(IF(purchase_item.on_way_quantity=NULL, 0, purchase_item.on_way_quantity)) FROM wh_purchase_item purchase_item WHERE purchase_item.sku=T1.sku AND purchase_item.status NOT IN ('All_Stock_In','Abandon')) >= :thenOnPassageQuantity]>
	        <[AND (SELECT SUM(IF(purchase_item.on_way_quantity=NULL, 0, purchase_item.on_way_quantity)) FROM wh_purchase_item purchase_item WHERE purchase_item.sku=T1.sku AND purchase_item.status NOT IN ('All_Stock_In','Abandon')) <= :lessOnPassageQuantity]>
	        
	        <[:filter_zero_record]>
	        
	        <[AND T1.id IN (:id_list)]>
	        <[AND T1.sku IN (:sku_list)]>
	        <[:MOVE_LOCATION_LAST_UPDATE_TIME_QUERY]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPageWhRecordsSku" >
    <content >
      <![CDATA[
        SELECT 
        	T1.sku
        FROM 
        	wh_record T1 
        	<[:wh_sku]>
        WHERE 
        	1 = 1
	        <[AND T1.sku IN (SELECT T2.sku FROM wh_sku T2 WHERE T2.warehouse_id = :warehouse_id)]>
	        <[:skus_condition]>
	        <[AND T1.sku IN (SELECT T2.sku FROM wh_sku T2 WHERE T2.location_number IN (:location_no_list))]>
	        
	        <[:area_condition]>
	        <[:access_condition]>
	        <[AND T1.sku IN (SELECT T2.sku FROM wh_sku T2 WHERE T2.name LIKE :sku_name_condition)]>
	        
	        <[AND T1.quantity >= :thenQuantity]>
	        <[AND T1.quantity <= :lessQuantity]>
	        
	        <[AND T1.surplus_quantity >= :thenSurplusQuantity]>
	        <[AND T1.surplus_quantity <= :lessSurplusQuantity]>
	        
	        <[AND T1.pick_quantity >= :thenPickQuantity]>
	        <[AND T1.pick_quantity <= :lessPickQuantity]>
	        
	        <[AND (SELECT SUM(IF(purchase_item.on_way_quantity=NULL, 0, purchase_item.on_way_quantity)) FROM wh_purchase_item purchase_item WHERE purchase_item.sku=T1.sku AND purchase_item.status NOT IN ('All_Stock_In','Abandon')) >= :thenOnPassageQuantity]>
	        <[AND (SELECT SUM(IF(purchase_item.on_way_quantity=NULL, 0, purchase_item.on_way_quantity)) FROM wh_purchase_item purchase_item WHERE purchase_item.sku=T1.sku AND purchase_item.status NOT IN ('All_Stock_In','Abandon')) <= :lessOnPassageQuantity]>
	        
	        <[:filter_zero_record]>
	        
	        <[AND T1.id IN (:id_list)]>
	        <[AND T1.sku IN (:sku_list)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPageWhRecordList" >
    <content >
      <![CDATA[
        SELECT
	        T1.id, 
			T1.sku, 
			T1.warehouse_id, 
			T1.quantity, 
			T1.surplus_quantity, 
			T1.allocation_quantity, 
			T1.pick_quantity, 
			T1.last_update_date, 
			T1.last_updated_by, 
			T2.sku AS 'whSku.sku', 
			T2.name AS 'whSku.name',
			T2.enroll_date AS 'whSku.enroll_date',
			T2.thirty_days_sales_orders AS 'whSku.thirtyDaysSalesOrders',
			T2.thirty_days_sales_days AS 'whSku.thirtyDaysSalesDays',
			T2.warehouse_id AS 'whSku.warehouseId',
			T2.status AS 'whSku.status',
			T2.fourteen_days_sale_quantity AS 'whSku.fourteenDaysSaleQuantity',
			T2.thirty_days_sale_quantity AS 'whSku.thirtyDaysSaleQuantity',
			T2.ninety_days_sale_quantity AS 'whSku.ninetyDaysSaleQuantity',
			T2.location_number AS 'whSku.locationNumber',
			<[:on_way_quantity]>
			<[:transit_location_quantity]>
			<[:pac_location_quantity]>
			<[:checkin_quantity]>
			<[:allot_quantity]>
			<[:move_last_update_time]>
			<[:pick_sku_quantity]>
        FROM 
        	wh_record T1 LEFT JOIN wh_sku T2 ON T1.sku=T2.sku 
        WHERE 
	        1 = 1
	        <[AND T2.warehouse_id = :warehouse_id]>
	        <[:skus_condition]>
	        <[AND T2.location_number IN (:location_no_list)]>

	        <[AND T2.no_stock_up = :no_stock_up]>

	        <[:area_condition]>
	        <[:access_condition]>
	        <[AND T2.name LIKE :sku_name_condition]>
	        
	        <[AND T1.quantity >= :thenQuantity]>
	        <[AND T1.quantity <= :lessQuantity]>
	        
	        <[AND T1.surplus_quantity >= :thenSurplusQuantity]>
	        <[AND T1.surplus_quantity <= :lessSurplusQuantity]>
	        
	        <[AND T1.pick_quantity >= :thenPickQuantity]>
	        <[AND T1.pick_quantity <= :lessPickQuantity]>
	        
	        <[AND (SELECT SUM(IF(purchase_item.on_way_quantity=NULL, 0, purchase_item.on_way_quantity)) FROM wh_purchase_item purchase_item WHERE purchase_item.sku=T1.sku AND purchase_item.status NOT IN ('All_Stock_In','Abandon')) >= :thenOnPassageQuantity]>
	        <[AND (SELECT SUM(IF(purchase_item.on_way_quantity=NULL, 0, purchase_item.on_way_quantity)) FROM wh_purchase_item purchase_item WHERE purchase_item.sku=T1.sku AND purchase_item.status NOT IN ('All_Stock_In','Abandon')) <= :lessOnPassageQuantity]>
	        
	        <[:filter_zero_record]>
	        
	        <[AND T1.id IN (:id_list)]>
	        <[AND T1.sku IN (:sku_list)]>
	        <[AND T1.sku = :sku]>
	        <[:MOVE_LOCATION_LAST_UPDATE_TIME_QUERY]>
	        ORDER BY T1.last_update_date DESC
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="countWhRecordGroup" >
    <content >
      <![CDATA[
        SELECT
			SUM(surplus_quantity) AS 'groupSurplusQuantity', 
			SUM(allotQuantity) AS 'groupAllotQuantity',
			SUM(pickSkuQuantity) AS 'groupPickSkuQuantity',
			SUM(allocation_quantity) AS 'groupAllocationquantity',
			SUM(onWayQuantity) AS 'groupOnWayQuantity', 
			SUM(SUBSTRING_INDEX(checkInQuantityStr, ',', 1)) AS 'groupWaitQcQuantity',
			SUM(SUBSTRING_INDEX(SUBSTRING_INDEX(checkInQuantityStr, ',', 2), ',', -1)) AS 'groupWaitUpQuantity',
			SUM(SUBSTRING_INDEX(checkInQuantityStr, ',', -1)) AS 'groupUpingQuantity',
			SUM(transitLocationQuantity) AS 'groupTransitLocationQuantity'
		FROM
			(
				SELECT 
					T1.sku,
					T1.surplus_quantity, 
					T1.allocation_quantity,
					<[:on_way_quantity]>
					<[:transit_location_quantity]>
					<[:pac_location_quantity]>
					<[:checkin_quantity]>
					<[:allot_quantity]>
					<[:pick_sku_quantity]>
				FROM wh_record T1
					<[:wh_sku]>
				WHERE 
					1 = 1
			        <[AND T1.sku IN (SELECT T2.sku FROM wh_sku T2 WHERE T2.warehouse_id = :warehouse_id)]>
			        <[:skus_condition]>
			        <[AND T1.sku IN (SELECT T2.sku FROM wh_sku T2 WHERE T2.location_number IN (:location_no_list))]>
			        
			        <[:area_condition]>
			        <[:access_condition]>
			        <[AND T1.sku IN (SELECT T2.sku FROM wh_sku T2 WHERE T2.name LIKE :sku_name_condition)]>
			        
			        <[AND T1.quantity >= :thenQuantity]>
			        <[AND T1.quantity <= :lessQuantity]>
			        
			        <[AND T1.surplus_quantity >= :thenSurplusQuantity]>
			        <[AND T1.surplus_quantity <= :lessSurplusQuantity]>
			        
			        <[AND T1.pick_quantity >= :thenPickQuantity]>
			        <[AND T1.pick_quantity <= :lessPickQuantity]>
			        
			        <[AND (SELECT SUM(IF(purchase_item.on_way_quantity=NULL, 0, purchase_item.on_way_quantity)) FROM wh_purchase_item purchase_item WHERE purchase_item.sku=T1.sku AND purchase_item.status NOT IN ('All_Stock_In','Abandon')) >= :thenOnPassageQuantity]>
	        		<[AND (SELECT SUM(IF(purchase_item.on_way_quantity=NULL, 0, purchase_item.on_way_quantity)) FROM wh_purchase_item purchase_item WHERE purchase_item.sku=T1.sku AND purchase_item.status NOT IN ('All_Stock_In','Abandon')) >= :lessOnPassageQuantity]>
			        
			        <[:filter_zero_record]>
			        
			        <[AND T1.id IN (:id_list)]>
			        <[AND T1.sku IN (:sku_list)]>
			        <[:MOVE_LOCATION_LAST_UPDATE_TIME_QUERY]>
			) T
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="countWhRecordGroupTotal" >
    <content >
      <![CDATA[
        SELECT SUM(surplus_quantity) AS 'groupSurplusQuantity', SUM(allocation_quantity) AS 'groupAllocationquantity',
		(SELECT SUM(item.sale_quantity) FROM wh_apv_item item WHERE item.apv_id IN (SELECT id FROM wh_apv apv 
		WHERE apv.status IN (6,8,10,12,14,15,16) AND apv.ship_status NOT IN (1))) + IFNULL((SELECT SUM(allocation_item.allocation_num)
		FROM wh_apv_allocation_item allocation_item WHERE allocation_item.allocation_id IN (SELECT allocation.allocation_id FROM 
		wh_apv_allocation allocation WHERE allocation.allocation_type = 1 AND allocation.allocation_status IN (1, 2, 3, 4, 5, 6))
		), 0) AS 'groupAllotQuantity',
		(SELECT SUM(task_sku.pick_quantity) FROM wh_picking_task_sku task_sku WHERE task_sku.task_id IN (SELECT 
		(SELECT task.task_id FROM wh_picking_task_item task WHERE task.apv_id = apv.id ORDER BY task.id DESC LIMIT 1) task_id 
		FROM wh_apv apv WHERE apv.status IN (10, 12, 14, 15, 16))) - (SELECT SUM(apv_item.sale_quantity) 
		FROM wh_apv_item apv_item LEFT JOIN wh_apv apv ON apv.id = apv_item.apv_id
		LEFT JOIN wh_picking_task_item task_item ON task_item.apv_id = apv_item.apv_id WHERE task_item.task_id IN (SELECT 
		(SELECT task.task_id FROM wh_picking_task_item task WHERE task.apv_id = apv.id ORDER BY task.id DESC LIMIT 1) task_id 
		FROM wh_apv apv WHERE apv.status IN (10, 12, 14, 15, 16)) AND apv.`status` NOT IN (10, 12, 14, 15, 16)) AS 'groupPickSkuQuantity'
		FROM wh_record
      ]]>
    </content>
  </sql>

  	<!-- 拣货缺货盘点查询库位库存 -->
   <sql datasource="dataSource" id="queryWhRecordLocationQuantityBySku" >
    <content >
      <![CDATA[
        SELECT
	        T1.id, 
			T1.sku, 
			T1.surplus_quantity, 
			<[:allot_quantity]>
			<[:pick_sku_quantity]>
        FROM 
        	wh_record T1
        WHERE 
	        1 = 1
	        <[AND T1.sku = :sku]>
      ]]>
    </content>
  </sql>
</sqlmap>