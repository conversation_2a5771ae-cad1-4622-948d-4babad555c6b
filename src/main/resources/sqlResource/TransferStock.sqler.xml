<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryTransStockCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_transfer_stock
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND stock_id IN (select id from wh_stock where location_number = :location_number)]>
        <[AND stock_id IN (select id from wh_stock where location_number IN (:location_list))]>
        <[AND site = :site]>
        <[AND store = :store]>
        <[AND surplus_quantity = :surplus_quantity]>
        <[AND surplus_quantity <= :surplusLessQuantity]>
        <[AND surplus_quantity >= :surplusThanQuantity]>
        <[AND allot_quantity = :allot_quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND pick_return_quantity = :pick_return_quantity]>
        <[AND first_deliver_quantity = :first_deliver_quantity]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>

        <[AND id IN (:ids)]>
        <[AND sku IN (:skuList)]>
        <[AND store IN (:store_list)]>
        <[AND site IN (:site_list)]>
        <[AND stock_id IN (:stock_id_list)]>
        <[AND stock_id = :stock_id]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryTransferStockList" >
    <content >
      <![CDATA[
        SELECT id, sku,location_tag, site, store, surplus_quantity, allot_quantity, pick_quantity,
        pick_return_quantity, first_deliver_quantity, remark, creation_date, last_update_date, stock_id,
        (select location_number from wh_stock where id = stock_id) location_number,
        last_updated_by
        <[:QUERY_LAST_UP_TIME]>
        FROM wh_transfer_stock
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND stock_id IN (select id from wh_stock where location_number = :location_number)]>
        <[AND stock_id IN (select id from wh_stock where location_number IN (:location_list))]>
        <[AND site = :site]>
        <[AND store = :store]>
        <[AND surplus_quantity = :surplus_quantity]>
        <[AND surplus_quantity <= :surplusLessQuantity]>
        <[AND surplus_quantity >= :surplusThanQuantity]>
        <[AND allot_quantity = :allot_quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND pick_return_quantity = :pick_return_quantity]>
        <[AND first_deliver_quantity = :first_deliver_quantity]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>

        <[AND id IN (:id_list)]>
        <[AND sku IN (:sku_list)]>
        <[AND store IN (:store_list)]>
        <[AND site IN (:site_list)]>
        <[AND stock_id IN (:stock_id_list)]>
        <[AND stock_id = :stock_id]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryTransferStockByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, sku, location_tag,site, store, surplus_quantity, allot_quantity, pick_quantity,
        pick_return_quantity, first_deliver_quantity, remark, creation_date, last_update_date, stock_id,
        (select location_number from wh_stock where id = stock_id) location_number,
        last_updated_by
        FROM wh_transfer_stock
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryTransferStock" >
    <content >
      <![CDATA[
        SELECT id, sku, location_tag,site, store, surplus_quantity, allot_quantity, pick_quantity,
        pick_return_quantity, first_deliver_quantity, remark, creation_date, last_update_date, stock_id,
        (select location_number from wh_stock where id = stock_id) location_number,
        last_updated_by
        FROM wh_transfer_stock
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND stock_id IN (select id from wh_stock where location_number = :location_number)]>
        <[AND site = :site]>
        <[AND store = :store]>
        <[AND surplus_quantity = :surplus_quantity]>
        <[AND allot_quantity = :allot_quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND pick_return_quantity = :pick_return_quantity]>
        <[AND first_deliver_quantity = :first_deliver_quantity]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND stock_id = :stock_id]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="createTransferStock" >
    <content >
      <![CDATA[
        INSERT INTO wh_transfer_stock (sku, location_number,location_tag,site, store, surplus_quantity, allot_quantity, pick_quantity,
          pick_return_quantity, first_deliver_quantity, remark, creation_date, last_update_date, stock_id,
          last_updated_by)
        VALUES (:sku, :location_number,:location_tag,:site, :store, :surplus_quantity, :allot_quantity, :pick_quantity,
          :pick_return_quantity, :first_deliver_quantity, :remark, :creation_date, :last_update_date, :stock_id,
          :last_updated_by)
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="deleteTransferStockByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_transfer_stock
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateTransferStockByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_transfer_stock
        SET <[sku = :sku,]>
         <[location_number = :location_number,]>
         <[location_tag = :location_tag,]>
          <[site = :site,]>
          <[store = :store,]>
          <[surplus_quantity = :surplus_quantity,]>
          <[allot_quantity = :allot_quantity,]>
          <[pick_quantity = :pick_quantity,]>
          <[pick_return_quantity = :pick_return_quantity,]>
          <[first_deliver_quantity = :first_deliver_quantity,]>
          <[remark = :remark,]>
          <[creation_date = :creation_date,]>
          <[last_update_date = :last_update_date,]>
          <[last_updated_by = :last_updated_by,]>
          <[stock_id = :stock_id,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>


  <sql datasource="dataSource" id="queryStockDetailCount" >
    <content >
      <![CDATA[
        SELECT COUNT(DISTINCT s.id)
        FROM wh_transfer_stock s
        LEFT JOIN  wh_transfer_stock_count c ON c.sku = s.sku
        <[:LEFT_JOIN_IN_ALLOCATE_TABLE]>

        WHERE 1 = 1
        <[AND s.id = :id]>
        <[AND s.sku = :sku]>
        <[AND s.site = :site]>
        <[AND s.store = :store]>
        <[AND c.img = :img]>
        <[AND c.sku_name = :sku_name]>
        <[AND s.location_number = :location)]>
        <[AND s.stock_id IN (select id from wh_stock where location_number = :location)]>
        <[AND c.on_way_quantity = :on_way_quantity]>
        <[AND c.waiting_qc_quantity = :waiting_qc_quantity]>
        <[AND c.waiting_up_quantity = :waiting_up_quantity]>
        <[AND c.up_quantity = :up_quantity]>
        <[AND s.surplus_quantity = :surplus_quantity]>
        <[AND s.allot_quantity = :allot_quantity]>
        <[AND s.pick_quantity = :pick_quantity]>
        <[AND s.pick_return_quantity = :pick_return_quantity]>
        <[AND s.first_deliver_quantity = :first_deliver_quantity]>
        <[AND c.warehouse_id = :warehouse_id]>
        <[AND s.remark = :remark]>
        <[AND s.creation_date = :creation_date]>
        <[AND s.last_update_date = :last_update_date]>
        <[AND s.last_updated_by = :last_updated_by]>
        <[AND re.allocation_in_time >= :in_allocate_time_begin]>
        <[AND re.allocation_in_time <= :in_allocate_time_end]>
        <[AND re.allocation_out_time >= :out_allocate_time_begin]>
        <[AND re.allocation_out_time <= :out_allocate_time_end]>

        <[AND s.id IN (:id_list)]>
        <[AND s.sku IN (:sku_list)]>
        <[AND s.store IN (:store_list)]>
        <[AND s.site IN (:site_list)]>
        <[AND s.stock_id IN (select id from wh_stock where location_number IN (:location_list))]>
        <[AND s.sku IN (SELECT sku FROM wh_transfer_stock_relation WHERE sku = s.sku AND store = s.store AND site = s.site AND sell_sku IN (:sell_sku_list))]>
        <[AND s.sku IN (SELECT sku FROM wh_transfer_stock_relation WHERE sku = s.sku AND store = s.store AND site = s.site AND fn_sku IN (:fn_sku_list))]>
        <[AND s.stock_id IN (:stock_id_list)]>
        <[AND s.stock_id = :stock_id]>
        <[:FILTER_BY_TYPE]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryStockDetailIds" >
    <content >
      <![CDATA[
        SELECT s.id
        FROM wh_transfer_stock s
        LEFT JOIN  wh_transfer_stock_count c ON c.sku = s.sku
        <[:LEFT_JOIN_IN_ALLOCATE_TABLE]>

        WHERE 1 = 1
        <[AND s.id = :id]>
        <[AND s.sku = :sku]>
        <[AND s.site = :site]>
        <[AND s.store = :store]>
        <[AND c.img = :img]>
        <[AND c.sku_name = :sku_name]>
        <[AND s.stock_id IN (select id from wh_stock where location_number = :location)]>
        <[AND c.on_way_quantity = :on_way_quantity]>
        <[AND c.waiting_qc_quantity = :waiting_qc_quantity]>
        <[AND c.waiting_up_quantity = :waiting_up_quantity]>
        <[AND c.up_quantity = :up_quantity]>
        <[AND s.surplus_quantity = :surplus_quantity]>
        <[AND s.allot_quantity = :allot_quantity]>
        <[AND s.pick_quantity = :pick_quantity]>
        <[AND s.pick_return_quantity = :pick_return_quantity]>
        <[AND s.first_deliver_quantity = :first_deliver_quantity]>
        <[AND c.warehouse_id = :warehouse_id]>
        <[AND s.remark = :remark]>
        <[AND s.creation_date = :creation_date]>
        <[AND s.last_update_date = :last_update_date]>
        <[AND s.last_updated_by = :last_updated_by]>
        <[AND re.allocation_in_time >= :in_allocate_time_begin]>
        <[AND re.allocation_in_time <= :in_allocate_time_end]>
        <[AND re.allocation_out_time >= :out_allocate_time_begin]>
        <[AND re.allocation_out_time <= :out_allocate_time_end]>

        <[AND s.id IN (:id_list)]>
        <[AND s.sku IN (:sku_list)]>
        <[AND s.store IN (:store_list)]>
        <[AND s.site IN (:site_list)]>
        <[AND s.stock_id IN (select id from wh_stock where location_number IN (:location_list))]>
        <[AND s.sku IN (SELECT sku FROM wh_transfer_stock_relation WHERE sku = s.sku AND store = s.store AND site = s.site AND sell_sku IN (:sell_sku_list))]>
        <[AND s.sku IN (SELECT sku FROM wh_transfer_stock_relation WHERE sku = s.sku AND store = s.store AND site = s.site AND fn_sku IN (:fn_sku_list))]>
        <[AND s.stock_id IN (:stock_id_list)]>
        <[AND s.stock_id = :stock_id]>
        <[:FILTER_BY_TYPE]>
        ORDER BY s.id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryStockDetailList" >
    <content >
      <![CDATA[

        SELECT s.id,s.sku,r.sell_sku,r.fn_sku,r.flag,s.site,s.store,s.remark,c.sku_name,c.img
        , ws.status AS status
		, wr.thirty_days_sales_orders
		, wr.thirty_days_sales_days
		, wr.sale_attribute_setting_str
        , IF(s.stock_id,(select location_number from wh_stock where id=s.stock_id),null) as location_number
        , s.stock_id
        , s.location_tag
        ,IFNULL(s.surplus_quantity,0) surplus_quantity
        ,IFNULL(s.allot_quantity,0) allot_quantity
        ,IFNULL(s.pick_quantity,0) pick_quantity
        ,IFNULL(s.pick_return_quantity,0) pick_return_quantity
        ,IFNULL(s.first_deliver_quantity,0) first_deliver_quantity
        , 0 as on_way_quantity
        , 0 as waiting_qc_quantity
        , 0 as waiting_up_quantity
        , 0 as up_quantity
        , re.allocation_in_time
		, re.allocation_out_time
		, re.last_up_time
        FROM wh_transfer_stock s
        LEFT JOIN  wh_transfer_stock_count c ON c.sku = s.sku
        LEFT JOIN wh_transfer_stock_relation r ON r.sku = s.sku AND r.store = s.store AND r.site = s.site
        LEFT JOIN wh_sku ws on c.sku = ws.sku
        LEFT JOIN wh_sku_sale_statistic_record wr on c.sku = wr.sku
        LEFT JOIN transfer_stock_time_relation re ON re.stock_id = s.id
        WHERE 1 = 1
        <[AND s.id = :id]>
        <[AND s.sku = :sku]>
        <[AND s.site = :site]>
        <[AND s.store = :store]>
        <[AND c.img = :img]>
        <[AND c.sku_name = :sku_name]>
        <[AND s.stock_id IN (select id from wh_stock where location_number = :location)]>
        <[AND c.on_way_quantity = :on_way_quantity]>
        <[AND c.waiting_qc_quantity = :waiting_qc_quantity]>
        <[AND c.waiting_up_quantity = :waiting_up_quantity]>
        <[AND c.up_quantity = :up_quantity]>
        <[AND s.surplus_quantity = :surplus_quantity]>
        <[AND s.allot_quantity = :allot_quantity]>
        <[AND s.pick_quantity = :pick_quantity]>
        <[AND s.pick_return_quantity = :pick_return_quantity]>
        <[AND s.first_deliver_quantity = :first_deliver_quantity]>
        <[AND c.warehouse_id = :warehouse_id]>
        <[AND s.remark = :remark]>
        <[AND s.creation_date = :creation_date]>
        <[AND s.last_update_date = :last_update_date]>
        <[AND s.last_updated_by = :last_updated_by]>
        <[AND re.allocation_in_time >= :in_allocate_time_begin]>
        <[AND re.allocation_in_time <= :in_allocate_time_end]>
        <[AND re.allocation_out_time >= :out_allocate_time_begin]>
        <[AND re.allocation_out_time <= :out_allocate_time_end]>

        <[AND s.id IN (:id_list)]>
        <[AND s.sku IN (:sku_list)]>
        <[AND s.store IN (:store_list)]>
        <[AND s.site IN (:site_list)]>
        <[AND s.stock_id IN (select id from wh_stock where location_number IN (:location_list))]>
        <[AND s.sku IN (SELECT sku FROM wh_transfer_stock_relation WHERE sku = s.sku AND store = s.store AND site = s.site AND sell_sku IN (:sell_sku_list))]>
        <[AND s.sku IN (SELECT sku FROM wh_transfer_stock_relation WHERE sku = s.sku AND store = s.store AND site = s.site AND fn_sku IN (:fn_sku_list))]>
        <[AND s.stock_id IN (:stock_id_list)]>
        <[AND s.stock_id = :stock_id]>
        <[:FILTER_BY_TYPE]>
        <[:LIMIT]>
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="queryStoreListBySku" >
    <content >
      <![CDATA[
        SELECT DISTINCT store
        FROM wh_transfer_stock
        WHERE sku = :sku
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryStockCount" >
    <content >
      <![CDATA[
        SELECT
            SUM(IFNULL( t.surplus_quantity, 0 )) surplus_quantity,
            SUM(IFNULL( t.allot_quantity, 0 )) allot_quantity,
            SUM(IFNULL( t.pick_quantity, 0 )) pick_quantity,
            SUM(IFNULL( t.pick_return_quantity, 0 )) pick_return_quantity,
            SUM(IFNULL( t.first_deliver_quantity, 0 )) first_deliver_quantity,
            0 as on_way_quantity,
            0 as waiting_qc_quantity,
            0 as waiting_up_quantity,
            0 as up_quantity
        FROM (
          SELECT
          s.surplus_quantity,
          s.allot_quantity,
          s.pick_quantity,
          s.pick_return_quantity,
          s.first_deliver_quantity
          FROM wh_transfer_stock s
          LEFT JOIN  wh_transfer_stock_count c ON c.sku = s.sku
          LEFT JOIN wh_transfer_stock_relation r ON r.sku = s.sku AND r.store = s.store AND r.site = s.site
          WHERE 1 = 1
          <[AND s.id = :id]>
          <[AND s.sku = :sku]>
          <[AND s.site = :site]>
          <[AND s.store = :store]>
          <[AND c.img = :img]>
          <[AND c.sku_name = :sku_name]>
          <[AND s.stock_id IN (select id from wh_stock where location_number = :location)]>
          <[AND c.on_way_quantity = :on_way_quantity]>
          <[AND c.waiting_qc_quantity = :waiting_qc_quantity]>
          <[AND c.waiting_up_quantity = :waiting_up_quantity]>
          <[AND c.up_quantity = :up_quantity]>
          <[AND s.surplus_quantity = :surplus_quantity]>
          <[AND s.allot_quantity = :allot_quantity]>
          <[AND s.pick_quantity = :pick_quantity]>
          <[AND s.pick_return_quantity = :pick_return_quantity]>
          <[AND s.first_deliver_quantity = :first_deliver_quantity]>
          <[AND c.warehouse_id = :warehouse_id]>
          <[AND s.remark = :remark]>
          <[AND s.creation_date = :creation_date]>
          <[AND s.last_update_date = :last_update_date]>
          <[AND s.last_updated_by = :last_updated_by]>

          <[AND s.id IN (:id_list)]>
          <[AND s.sku IN (:sku_list)]>
          <[AND s.store IN (:store_list)]>
          <[AND s.site IN (:site_list)]>
          <[AND s.stock_id IN (select id from wh_stock where location_number IN (:location_list))]>
          <[AND s.sku IN (SELECT sku FROM wh_transfer_stock_relation WHERE sku = s.sku AND store = s.store AND site = s.site AND sell_sku IN (:sell_sku_list))]>
          <[AND s.sku IN (SELECT sku FROM wh_transfer_stock_relation WHERE sku = s.sku AND store = s.store AND site = s.site AND fn_sku IN (:fn_sku_list))]>
          <[AND s.stock_id IN (:stock_id_list)]>
          <[AND s.stock_id = :stock_id]>
          <[:FILTER_BY_TYPE]>
          GROUP BY s.id
        )t
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="queryTransferStockBySkuAndType" >
    <content >
      <![CDATA[
        SELECT sku,store,site,location_number
        <[:QUANTITY_SQL]>
        FROM wh_transfer_stock
        WHERE 1 = 1
        <[AND sku IN (:skus)]>
        <[AND store = :store]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateTfLocationByStockId" >
    <content >
      <![CDATA[
        UPDATE wh_transfer_stock
        SET
         <[location_number = :location_number,]>
         <[last_update_date = :last_update_date,]>
         <[last_updated_by = :last_updated_by,]>
         location_tag = :location_tag,
         stock_id = :stock_id
        WHERE 1 = 1
        AND stock_id = :stock_id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryTransStockAndRelationItem" >
    <content >
      <![CDATA[
        SELECT  sku
        FROM wh_transfer_stock_relation
        WHERE 1 = 1
        <[AND store = :store]>
        <[AND sku IN (:sku_list)]>
        <[AND store IN (:store_list)]>
        <[AND site IN (:site_list)]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="querySkuStockBySkuAndStore" >
    <content >
      <![CDATA[
        SELECT
            s.sku,
            s.store,
            s.remark,
            SUM(IFNULL(surplus_quantity, 0 )+IFNULL(pick_return_quantity,0)) AS quantity
            FROM
                wh_transfer_stock s
            WHERE
                1 = 1
                <[AND s.sku IN (:sku_list)]>
        GROUP BY
            s.sku,s.store
      ]]>
    </content>
  </sql>
</sqlmap>