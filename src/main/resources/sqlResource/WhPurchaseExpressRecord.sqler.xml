<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhPurchaseExpressRecordCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_purchase_express_record
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:ids)]>
        <[AND tracking_number IN (:tracking_number)]>
        <[AND tracking_number IN (:trackingNos)]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND serial_number = :serial_number]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND split_user = :split_user]>
        <[AND split_date = :split_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND comment = :comment]>
        <[AND reason = :reason]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND weight = :weight]>
        <[AND total_weight = :total_weight]>
        <[AND quantity = :quantity]>
        <[AND shipping_cpn = :shipping_cpn]>
		<[AND purchase_order_no LIKE :purchase_order_no_like]>
		<[AND tracking_number IN (SELECT express_id FROM wh_purchase_to_express WHERE purchase_order_no = :purchase_order_no_query)]>
        <[:HasPurchaseOrderNoStr]>
        <[:HasCommentStr]>
        <[:PURCHASE_TYPE]>

        <[AND creation_date >= :from_creation_date]>
        <[AND creation_date <= :to_creation_date]>
        <[AND split_date >= :from_split_date]>
        <[AND split_date <= :to_split_date]>
        <[AND box_no IN (:box_no)]>
        <[AND receive_user = :receive_user]>
        <[AND receive_date >= :from_receive_date]>
        <[AND receive_date <= :to_receive_date]>
        <[AND check_in_scan_status = :check_in_scan_status]>
        <[AND status = :status]>
        <[AND scan_mark = :scan_mark]>
        <[:IsSplitStr]>
        <[:CHECK_IN_SCAN_FIND_RECORD]>
        <[:STATUS_QUERY]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhPurchaseExpressRecordList" >
    <content >
      <![CDATA[
        SELECT id, tracking_number, purchase_order_no, serial_number, created_by, creation_date,check_in_scanner,check_in_scan_time,check_in_scan_status,
        split_user, split_date, last_updated_by, last_update_date, comment, reason, warehouse_id, weight, total_weight, box_no, receive_user, receive_date
        ,quantity, status, length, width, height, image_url, scan_mark
		,(
          SELECT
              GROUP_CONCAT( DISTINCT p.purchase_user )
          FROM
              wh_purchase_to_express e
              LEFT JOIN wh_purchase_order p ON e.purchase_order_no = p.purchase_order_no
          WHERE
              e.express_id = wh_purchase_express_record.tracking_number
              ) AS purchase_user
        ,(
          SELECT
              GROUP_CONCAT( DISTINCT p.logistics_mark )
          FROM
              wh_purchase_to_express e
              LEFT JOIN wh_purchase_order p ON e.purchase_order_no = p.purchase_order_no
          WHERE
              e.express_id = wh_purchase_express_record.tracking_number
        ) AS logistics_mark, shipping_cpn
		FROM wh_purchase_express_record
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:ids)]>
        <[AND tracking_number IN (:tracking_number)]>
        <[AND tracking_number IN (:trackingNos)]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND serial_number = :serial_number]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND split_user = :split_user]>
        <[AND split_date = :split_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND comment = :comment]>
        <[AND reason = :reason]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND weight = :weight]>
        <[AND total_weight = :total_weight]>
        <[AND quantity = :quantity]>
        <[AND shipping_cpn = :shipping_cpn]>
        <[AND check_in_scan_status = :check_in_scan_status]>
        <[AND purchase_order_no LIKE :purchase_order_no_like]>
        <[AND tracking_number IN (SELECT express_id FROM wh_purchase_to_express WHERE purchase_order_no = :purchase_order_no_query)]>
        <[:HasPurchaseOrderNoStr]>
        <[:HasCommentStr]>
        <[:PURCHASE_TYPE]>

        <[AND creation_date >= :from_creation_date]>
        <[AND creation_date <= :to_creation_date]>
        <[AND split_date >= :from_split_date]>
        <[AND split_date <= :to_split_date]>
        <[AND box_no IN (:box_no)]>
        <[AND status = :status]>
        <[AND scan_mark = :scan_mark]>
        <[AND receive_user = :receive_user]>
        <[AND receive_date >= :from_receive_date]>
        <[AND receive_date <= :to_receive_date]>
        <[:IsSplitStr]>
        <[:CHECK_IN_SCAN_FIND_RECORD]>
        <[:STATUS_QUERY]>
		<[:order_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhPurchaseExpressRecordByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, tracking_number, purchase_order_no, serial_number, created_by, creation_date,check_in_scanner,check_in_scan_time,check_in_scan_status,
        split_user, split_date, last_updated_by, last_update_date, comment, reason, warehouse_id, weight, total_weight, box_no, receive_user, receive_date
        ,quantity, status, shipping_cpn, length, width, height, image_url, scan_mark
        FROM wh_purchase_express_record
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhPurchaseExpressRecord" >
    <content >
      <![CDATA[
        SELECT id, tracking_number, purchase_order_no, serial_number, created_by, creation_date,check_in_scanner,check_in_scan_time,check_in_scan_status,
        split_user, split_date, last_updated_by, last_update_date, comment, reason, warehouse_id, weight, total_weight,box_no, receive_user ,receive_date
        ,quantity, status, shipping_cpn, length, width, height, image_url, scan_mark
        FROM wh_purchase_express_record
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND tracking_number = :tracking_number]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND serial_number = :serial_number]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND split_user = :split_user]>
        <[AND split_date = :split_date]>
        <[AND shipping_cpn = :shipping_cpn]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND comment = :comment]>
        <[AND reason = :reason]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND weight = :weight]>
        <[AND status = :status]>
        <[AND total_weight = :total_weight]>
        <[AND quantity = :quantity]>
        <[AND scan_mark = :scan_mark]>
        <[AND check_in_scan_status = :check_in_scan_status]>
        <[AND purchase_order_no LIKE :purchase_order_no_like]>
        <[:STATUS_QUERY]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="getNotFinishExpressRecord" >
    <content >
      <![CDATA[
        select id, tracking_number, purchase_order_no, serial_number, created_by, creation_date,check_in_scanner,check_in_scan_time,check_in_scan_status,
          split_user, split_date, last_updated_by, last_update_date, comment, reason, warehouse_id, weight, total_weight,box_no, receive_user ,
          receive_date,quantity, status, shipping_cpn, image_url, scan_mark
        from wh_purchase_express_record where
          check_in_scan_status = 1
          and status = 2
          <[AND check_in_scanner = :check_in_scanner]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhPurchaseExpressRecord" >
    <content >
      <![CDATA[
        INSERT INTO wh_purchase_express_record (tracking_number, purchase_order_no, serial_number, created_by, creation_date, 
          split_user, split_date, last_updated_by, last_update_date, comment, reason, warehouse_id, weight, total_weight
          ,quantity, box_no, receive_user, receive_date, status, shipping_cpn, length, width, height, image_url, scan_mark)
        VALUES (:tracking_number, :purchase_order_no, :serial_number, :created_by, :creation_date, 
          :split_user, :split_date, :last_updated_by, :last_update_date, :comment, :reason, :warehouse_id, :weight, :total_weight, 
          :quantity, :box_no, :receive_user, :receive_date, :status, :shipping_cpn, :length, :width, :height, :image_url, :scan_mark)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhPurchaseExpressRecordByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_purchase_express_record
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhPurchaseExpressRecordByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_purchase_express_record
        SET <[tracking_number = :tracking_number,]>
          <[purchase_order_no = :purchase_order_no,]>
          <[serial_number = :serial_number,]>
          <[created_by = :created_by,]>
          <[creation_date = :creation_date,]>
          <[split_user = :split_user,]>
          <[split_date = :split_date,]>
          <[check_in_scanner = :check_in_scanner,]>
          <[check_in_scan_time = :check_in_scan_time,]>
          <[check_in_scan_status = :check_in_scan_status,]>
          <[last_updated_by = :last_updated_by,]>
          <[last_update_date = :last_update_date,]>
          <[comment = :comment,]>
          <[reason = :reason,]>
          <[warehouse_id = :warehouse_id,]>
          <[weight = :weight,]>
          <[length = :length,]>
          <[width = :width,]>
          <[height = :height,]>
          <[box_no = :box_no,]>
          <[status = :status,]>
          <[total_weight = :total_weight,]>
          <[quantity = :quantity,]>
          <[shipping_cpn = :shipping_cpn,]>
          <[image_url = :image_url,]>
          <[scan_mark = :scan_mark,]>
          receive_user = :receive_user,
          receive_date = :receive_date,
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

    <sql datasource="dataSource" id="batchUpdateCommentByIds">
        <content>
            <![CDATA[
                UPDATE wh_purchase_express_record
                SET comment = :comment
                WHERE id IN (:ids)
             ]]>
        </content>
    </sql>
</sqlmap>