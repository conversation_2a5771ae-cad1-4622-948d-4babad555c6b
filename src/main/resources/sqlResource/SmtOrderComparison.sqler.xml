<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="querySmtOrderComparisonCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM smt_order_comparison
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND compar_type = :compar_type]>
        <[AND order_type = :order_type]>
        <[AND order_id = :order_id]>
        <[AND order_id IN (:orderIdList)]>
        <[AND inventory_id = :inventory_id]>
        <[AND inventory_id IN (:inventoryIdList)]>
        <[AND create_time = :create_time]>
        <[:ORDER_MATCH]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySmtOrderComparisonList" >
    <content >
      <![CDATA[
        SELECT id, compar_type, order_type, order_id, inventory_id, create_time,order_qty,inventory_qty
        FROM smt_order_comparison
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND compar_type = :compar_type]>
        <[AND order_type = :order_type]>
        <[AND order_id = :order_id]>
        <[AND order_id IN (:orderIdList)]>
        <[AND inventory_id = :inventory_id]>
        <[AND inventory_id IN (:inventoryIdList)]>
        <[AND create_time = :create_time]>
        <[:ORDER_MATCH]>
      ]]>
    </content>
  </sql>


  <sql datasource="dataSource" id="querySmtOrderComparisonAndPrepareOrderCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM smt_order_comparison soc
          LEFT JOIN smt_warehouse_inventory swi ON soc.inventory_id = swi.id
          LEFT JOIN wh_fba_allocation_item wfai ON soc.order_id = wfai.id
        WHERE 1 = 1
        <[AND soc.id = :id]>
        <[AND soc.id IN (:idList)]>
        <[AND soc.compar_type = :compar_type]>
        <[AND soc.order_type = :order_type]>
        <[AND soc.order_id = :order_id]>
        <[AND soc.inventory_id = :inventory_id]>
        <[AND soc.create_time = :create_time]>
        <[AND IF(:hasDiscrepancy,  soc.order_qty != soc.inventory_qty, soc.order_qty = soc.inventory_qty)]>
        <[AND swi.account_number = :account_number]>
        <[AND swi.account_number IN (:accountNumberList)]>
        <[AND swi.account_number IN (:swi_accountNumberList)]>
        <[AND swi.account_number = :swi_account_number]>
        <[AND wfai.fba_id IN (SELECT id FROM wh_fba_allocation WHERE account_number IN (:order_accountNumberList))]>
        <[AND wfai.fba_id IN (SELECT id FROM wh_fba_allocation WHERE account_number = :order_account_number)]>
        <[AND swi.product_id = :product_id]>
        <[AND swi.product_id IN (:productIdList)]>
        <[AND swi.scitem_code = :scitem_code]>
        <[AND swi.scitem_code IN (:scItemCodeList)]>
        <[AND swi.store = :store]>
        <[AND swi.store IN (:storeList)]>
        <[AND swi.gmt_create >= :fromGmtDate]>
        <[AND swi.gmt_create <= :toGmtDate]>
        <[AND wfai.product_sku = :product_sku]>
        <[AND wfai.product_sku IN (:skuList)]>
        <[AND wfai.product_sku = :order_product_sku]>
        <[AND wfai.product_sku IN (:order_skuList)]>
        <[AND swi.scitem_code IN (SELECT scitem_code FROM smt_inventory_sku_relation WHERE sku = :swi_product_sku)]>
        <[AND swi.scitem_code IN (SELECT scitem_code FROM smt_inventory_sku_relation WHERE sku IN (:swi_skuList))]>
        <[AND wfai.temu_tag_url = :temu_tag_url]>
        <[AND wfai.temu_tag_url IN (:lbxList)]>
        <[AND wfai.fba_id IN (SELECT id FROM wh_fba_allocation WHERE deliver_time >= :fromDeliveryDate)]>
        <[AND wfai.fba_id IN (SELECT id FROM wh_fba_allocation WHERE deliver_time <= :toDeliveryDate)]>
        <[:ORDER_MATCH]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="querySmtOrderComparisonAndPrepareOrderList">
    <content >
      <![CDATA[
        SELECT soc.id, soc.compar_type, soc.order_type, soc.order_id, soc.inventory_id, soc.create_time, soc.order_qty, soc.inventory_qty,
          wfai.fba_id, wfai.product_sku, wfai.temu_tag_url, (SELECT deliver_time FROM wh_fba_allocation WHERE id = wfai.fba_id) AS `wfai.deliver_time`,
          (SELECT account_number FROM wh_fba_allocation WHERE id = wfai.fba_id) AS `wfai.account_number`,
          swi.change_quantity, swi.main_order, swi.main_sub_order, swi.trade_order, swi.trade_sub_order, swi.gmt_create,swi.result_quantity,swi.account_number,
          swi.product_id, swi.scitem_code, swi.biz_activity_type,swi.inventory_type, swi.store, swi.order_type
          <[:QUERY_SKU_COL]>
        FROM smt_order_comparison soc
          LEFT JOIN smt_warehouse_inventory swi ON soc.inventory_id = swi.id
          LEFT JOIN wh_fba_allocation_item wfai ON soc.order_id = wfai.id
        WHERE 1 = 1
        <[AND soc.id = :id]>
        <[AND soc.id IN (:idList)]>
        <[AND soc.compar_type = :compar_type]>
        <[AND soc.order_type = :order_type]>
        <[AND soc.order_id = :order_id]>
        <[AND soc.inventory_id = :inventory_id]>
        <[AND soc.create_time = :create_time]>
        <[AND IF(:hasDiscrepancy,  soc.order_qty != soc.inventory_qty, soc.order_qty = soc.inventory_qty)]>
        <[AND swi.account_number = :account_number]>
        <[AND swi.account_number IN (:accountNumberList)]>
        <[AND swi.account_number IN (:swi_accountNumberList)]>
        <[AND swi.account_number = :swi_account_number]>
        <[AND wfai.fba_id IN (SELECT id FROM wh_fba_allocation WHERE account_number IN (:order_accountNumberList))]>
        <[AND wfai.fba_id IN (SELECT id FROM wh_fba_allocation WHERE account_number = :order_account_number)]>
        <[AND swi.product_id = :product_id]>
        <[AND swi.product_id IN (:productIdList)]>
        <[AND swi.scitem_code = :scitem_code]>
        <[AND swi.scitem_code IN (:scItemCodeList)]>
        <[AND swi.store = :store]>
        <[AND swi.store IN (:storeList)]>
        <[AND swi.gmt_create >= :fromGmtDate]>
        <[AND swi.gmt_create <= :toGmtDate]>
        <[AND wfai.product_sku = :product_sku]>
        <[AND wfai.product_sku IN (:skuList)]>
        <[AND wfai.product_sku = :order_product_sku]>
        <[AND wfai.product_sku IN (:order_skuList)]>
        <[AND swi.scitem_code IN (SELECT scitem_code FROM smt_inventory_sku_relation WHERE sku = :swi_product_sku)]>
        <[AND swi.scitem_code IN (SELECT scitem_code FROM smt_inventory_sku_relation WHERE sku IN (:swi_skuList))]>
        <[AND wfai.temu_tag_url = :temu_tag_url]>
        <[AND wfai.temu_tag_url IN (:lbxList)]>
        <[AND wfai.fba_id IN (SELECT id FROM wh_fba_allocation WHERE deliver_time >= :fromDeliveryDate)]>
        <[AND wfai.fba_id IN (SELECT id FROM wh_fba_allocation WHERE deliver_time <= :toDeliveryDate)]>
        <[:ORDER_MATCH]>
      ]]>
    </content>
  </sql>


  <sql datasource="dataSource" id="querySmtOrderComparisonByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, compar_type, order_type, order_id, inventory_id, create_time,order_qty,inventory_qty
        FROM smt_order_comparison
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySmtOrderComparison" >
    <content >
      <![CDATA[
        SELECT id, compar_type, order_type, order_id, inventory_id, create_time,order_qty,inventory_qty
        FROM smt_order_comparison
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND compar_type = :compar_type]>
        <[AND order_type = :order_type]>
        <[AND order_id = :order_id]>
        <[AND inventory_id = :inventory_id]>
        <[AND create_time = :create_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createSmtOrderComparison" >
    <content >
      <![CDATA[
        INSERT INTO smt_order_comparison (compar_type, order_type, order_id, inventory_id, create_time,order_qty,inventory_qty)
        VALUES (:compar_type, :order_type, :order_id, :inventory_id, :create_time,:order_qty,:inventory_qty)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteSmtOrderComparisonByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM smt_order_comparison
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateSmtOrderComparisonByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE smt_order_comparison
        SET <[compar_type = :compar_type,]>
          <[order_type = :order_type,]>
          <[order_id = :order_id,]>
          <[inventory_id = :inventory_id,]>
          <[create_time = :create_time,]>
          <[order_qty = :order_qty,]>
          <[inventory_qty = :inventory_qty,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="deleteByOrderIds" >
    <content >
      <![CDATA[
        DELETE FROM smt_order_comparison
        WHERE compar_type = :compar_type AND order_id in (:orderIdList)
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="querySmtReturnComparisonCount" >
    <content >
      <![CDATA[
        SELECT count(1)
        FROM smt_order_comparison c
        LEFT JOIN smt_return_order_item ri ON ri.id = c.order_id
        LEFT JOIN smt_return_order r ON ri.return_id = r.id
        LEFT JOIN smt_warehouse_inventory swi ON c.inventory_id = swi.id
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND compar_type = :compar_type]>
        <[AND order_type = :order_type]>
        <[AND order_id = :order_id]>
        <[AND inventory_id = :inventory_id]>
        <[AND create_time = :create_time]>

        <[AND r.account_number in (:accountNumberList)]>
        <[AND r.account_number = :account_number]>
        <[AND r.account_number = :order_account_number]>
        <[AND r.account_number in (:order_accountNumberList)]>
        <[AND ri.barcode in (:skuList)]>
        <[AND ri.barcode = :product_sku]>
        <[AND ri.barcode in (:order_skuList)]>
        <[AND ri.barcode = :order_product_sku]>
        <[AND ri.origin_outbound_no in (:lbxList)]>
        <[AND ri.origin_outbound_no = :temu_tag_url]>
        <[AND swi.product_id in (:productIdList)]>
        <[AND swi.product_id = :product_id]>
        <[AND swi.gmt_create >= :fromGmtDate]>
        <[AND swi.gmt_create <= :toGmtDate]>
        <[AND swi.scitem_code in (:scItemCodeList)]>
        <[AND swi.scitem_code = :scitem_code]>
        <[AND swi.account_number in (:swi_accountNumberList)]>
        <[AND swi.account_number = :swi_account_number]>
        <[AND swi.scitem_code IN (SELECT scitem_code FROM smt_inventory_sku_relation WHERE sku = :swi_product_sku)]>
        <[AND swi.scitem_code IN (SELECT scitem_code FROM smt_inventory_sku_relation WHERE sku IN (:swi_skuList))]>
        <[AND IF(:hasDiscrepancy, IFNULL(ri.good_quantity,0) + IFNULL(ri.bad_quantity,0) != IFNULL(c.inventory_qty,0),IFNULL(ri.good_quantity,0) + IFNULL(ri.bad_quantity,0) = IFNULL(c.inventory_qty,0))]>
        <[:ORDER_MATCH]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="querySmtReturnComparisonList" >
    <content >
      <![CDATA[
        SELECT c.id, c.compar_type, c.order_type, c.order_id, c.inventory_id, c.create_time, c.order_qty, c.inventory_qty,
          r.biz_type, r.type, r.account_number, r.platform_status, r.status, r.create_time, r.checkout_time, r.split_time, r.grid_time, r.up_end_time,
          ri.sc_item_id, ri.barcode, ri.origin_order_no, ri.origin_outbound_no, ri.return_quantity, ri.good_quantity, ri.bad_quantity,
          swi.account_number, swi.main_order, swi.main_sub_order, swi.trade_order, swi.trade_sub_order, swi.store, swi.gmt_create, swi.product_id,
          swi.scitem_code, swi.biz_activity_type, swi.inventory_type, swi.change_quantity, swi.result_quantity
          <[:QUERY_SKU_COL]>
        FROM smt_order_comparison c
        LEFT JOIN smt_return_order_item ri ON ri.id = c.order_id
        LEFT JOIN smt_return_order r ON ri.return_id = r.id
        LEFT JOIN smt_warehouse_inventory swi ON c.inventory_id = swi.id
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND compar_type = :compar_type]>
        <[AND order_type = :order_type]>
        <[AND order_id = :order_id]>
        <[AND inventory_id = :inventory_id]>
        <[AND create_time = :create_time]>

        <[AND r.account_number in (:accountNumberList)]>
        <[AND r.account_number = :account_number]>
        <[AND r.account_number = :order_account_number]>
        <[AND r.account_number in (:order_accountNumberList)]>
        <[AND ri.barcode in (:skuList)]>
        <[AND ri.barcode = :product_sku]>
        <[AND ri.barcode in (:order_skuList)]>
        <[AND ri.barcode = :order_product_sku]>
        <[AND ri.origin_outbound_no in (:lbxList)]>
        <[AND ri.origin_outbound_no = :temu_tag_url]>
        <[AND swi.product_id in (:productIdList)]>
        <[AND swi.product_id = :product_id]>
        <[AND swi.gmt_create >= :fromGmtDate]>
        <[AND swi.gmt_create <= :toGmtDate]>
        <[AND swi.scitem_code in (:scItemCodeList)]>
        <[AND swi.scitem_code = :scitem_code]>
        <[AND IF(:hasDiscrepancy, IFNULL(ri.good_quantity,0) + IFNULL(ri.bad_quantity,0) != IFNULL(c.inventory_qty,0),IFNULL(ri.good_quantity,0) + IFNULL(ri.bad_quantity,0) = IFNULL(c.inventory_qty,0))]>
        <[:ORDER_MATCH]>
        <[:LIMIT]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="querySmtOmsOrderComparisonCount" >
    <content >
      <![CDATA[
        SELECT count(1)
        FROM smt_order_comparison c
        LEFT JOIN smt_oms_order o ON o.id = c.order_id
        LEFT JOIN smt_warehouse_inventory swi ON swi.id = c.inventory_id
        WHERE c.compar_type = 2
        <[AND c.id = :id]>
        <[AND c.order_type = :order_type]>
        <[AND c.order_id = :order_id]>
        <[AND c.inventory_id = :inventory_id]>
        <[AND c.create_time = :create_time]>

        <[AND swi.product_id IN (SELECT product_id FROM smt_inventory_sku_relation WHERE sku = :product_sku)]>
        <[AND swi.product_id IN (SELECT product_id FROM smt_inventory_sku_relation WHERE sku IN (:skuList))]>
        <[AND o.sku in (:order_skuList)]>
        <[AND o.sku = (:order_product_sku)]>
        <[AND o.platform_order = :platformOrderId]>
        <[AND o.platform_order IN (:platformOrderIdList)]>

        <[AND swi.account_number in (:accountNumberList)]>
        <[AND swi.account_number = :account_number]>
        <[AND o.account_number in (:order_accountNumberList)]>
        <[AND o.account_number = :order_account_number]>
        <[AND swi.product_id = :product_id]>
        <[AND swi.product_id IN (:productIdList)]>
        <[AND swi.scitem_code = :scitem_code]>
        <[AND swi.scitem_code IN (:scitemCodeList)]>

        <[AND o.deliver_time >= :fromDeliveryDate]>
        <[AND o.deliver_time <= :toDeliveryDate]>
        <[AND swi.gmt_create >= :fromGmtDate]>
        <[AND swi.gmt_create <= :toGmtDate]>
        <[AND IF(:hasDiscrepancy, IFNULL(c.order_qty,0) != IFNULL(c.inventory_qty,0),IFNULL(c.order_qty,0) = IFNULL(c.inventory_qty,0))]>
        <[:ORDER_MATCH]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="querySmtOmsOrderComparisonList" >
    <content >
      <![CDATA[
        SELECT
         c.id, c.compar_type, c.order_type, c.order_id, c.inventory_id, c.create_time, c.order_qty, c.inventory_qty,
        o.id, o.order_type, o.account_number, o.platform_order, o.sku, o.quantity, o.pay_time, o.deliver_time, o.create_time,o.status_name,
        swi.account_number, swi.main_order, swi.main_sub_order, swi.trade_order, swi.trade_sub_order, swi.store, swi.gmt_create, swi.product_id,
        swi.scitem_code, swi.biz_activity_type, swi.inventory_type, swi.change_quantity, swi.result_quantity
        <[:QUERY_SKU_COL]>
        FROM smt_order_comparison c
        LEFT JOIN smt_oms_order o ON o.id = c.order_id
        LEFT JOIN smt_warehouse_inventory swi ON swi.id = c.inventory_id
        WHERE c.compar_type = 2
        <[AND c.id = :id]>
        <[AND c.order_type = :order_type]>
        <[AND c.order_id = :order_id]>
        <[AND c.inventory_id = :inventory_id]>
        <[AND c.create_time = :create_time]>

        <[AND swi.product_id IN (SELECT product_id FROM smt_inventory_sku_relation WHERE sku = :product_sku)]>
        <[AND swi.product_id IN (SELECT product_id FROM smt_inventory_sku_relation WHERE sku IN (:skuList))]>
        <[AND o.sku in (:order_skuList)]>
        <[AND o.sku = (:order_product_sku)]>
        <[AND o.platform_order = :platformOrderId]>
        <[AND o.platform_order IN (:platformOrderIdList)]>

        <[AND swi.account_number in (:accountNumberList)]>
        <[AND swi.account_number = :account_number]>
        <[AND o.account_number in (:order_accountNumberList)]>
        <[AND o.account_number = :order_account_number]>
        <[AND swi.product_id = :product_id]>
        <[AND swi.product_id IN (:productIdList)]>
        <[AND swi.scitem_code = :scitem_code]>
        <[AND swi.scitem_code IN (:scitemCodeList)]>

        <[AND o.deliver_time >= :fromDeliveryDate]>
        <[AND o.deliver_time <= :toDeliveryDate]>
        <[AND swi.gmt_create >= :fromGmtDate]>
        <[AND swi.gmt_create <= :toGmtDate]>
        <[AND IF(:hasDiscrepancy, IFNULL(c.order_qty,0) != IFNULL(c.inventory_qty,0),IFNULL(c.order_qty,0) = IFNULL(c.inventory_qty,0))]>
        <[:ORDER_MATCH]>
      ]]>
    </content>
  </sql>



  <sql datasource="dataSource" id="queryComparisonNotMatchInventoryCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM smt_order_comparison c
        LEFT JOIN smt_warehouse_inventory swi ON swi.id = c.inventory_id
          WHERE 1=1
          <[AND c.id = :id]>
          <[AND c.order_type = :order_type]>
          <[AND c.order_id = :order_id]>
          <[AND c.inventory_id = :inventory_id]>
          <[AND c.create_time = :create_time]>
          <[AND c.compar_type = :compar_type]>
          <[AND swi.account_number in (:accountNumberList)]>
          <[AND swi.account_number = :account_number]>
          <[AND swi.product_id = :product_id]>
          <[AND swi.product_id = :product_id]>
          <[AND swi.store = :store]>
          <[AND swi.store IN (:storeList)]>
          <[AND swi.scitem_code = :scitem_code]>
          <[AND swi.scitem_code IN (:scitemCodeList)]>
          <[AND swi.gmt_create >= :fromGmtDate]>
          <[AND swi.gmt_create <= :toGmtDate]>
          <[AND swi.product_id IN (SELECT product_id FROM smt_inventory_sku_relation WHERE sku = :product_sku)]>
          <[AND swi.product_id IN (SELECT product_id FROM smt_inventory_sku_relation WHERE sku IN (:skuList))]>
          <[:ORDER_MATCH]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryComparisonNotMatchInventoryList" >
    <content >
      <![CDATA[
        SELECT
         c.id, c.compar_type, c.order_type, c.order_id, c.inventory_id, c.create_time, c.order_qty, c.inventory_qty,
        swi.account_number, swi.main_order, swi.main_sub_order, swi.trade_order, swi.trade_sub_order, swi.store, swi.gmt_create, swi.product_id,
        swi.scitem_code, swi.biz_activity_type, swi.inventory_type, swi.change_quantity, swi.result_quantity
        ,r.sku,r.suit_num,r.flag,r.product_id, r.scitem_code
        FROM smt_order_comparison c
        LEFT JOIN smt_oms_order o ON o.id = c.order_id
        LEFT JOIN smt_warehouse_inventory swi ON swi.id = c.inventory_id
        LEFT JOIN smt_inventory_sku_relation r ON r.product_id = swi.product_id AND r.scitem_code = swi.scitem_code
        INNER JOIN (
          SELECT c.id FROM smt_order_comparison c
          LEFT JOIN smt_warehouse_inventory swi ON swi.id = c.inventory_id
            WHERE 1=1
            <[AND c.id = :id]>
            <[AND c.order_type = :order_type]>
            <[AND c.order_id = :order_id]>
            <[AND c.inventory_id = :inventory_id]>
            <[AND c.create_time = :create_time]>
            <[AND c.compar_type = :compar_type]>
            <[AND swi.account_number in (:accountNumberList)]>
            <[AND swi.account_number = :account_number]>
            <[AND swi.product_id = :product_id]>
            <[AND swi.product_id = :product_id]>
            <[AND swi.store = :store]>
            <[AND swi.store IN (:storeList)]>
            <[AND swi.scitem_code = :scitem_code]>
            <[AND swi.scitem_code IN (:scitemCodeList)]>
            <[AND swi.gmt_create >= :fromGmtDate]>
            <[AND swi.gmt_create <= :toGmtDate]>
            <[AND swi.product_id IN (SELECT product_id FROM smt_inventory_sku_relation WHERE sku = :product_sku)]>
            <[AND swi.product_id IN (SELECT product_id FROM smt_inventory_sku_relation WHERE sku IN (:skuList))]>
            <[:ORDER_MATCH]>
            <[:LIMIT]>
        )t ON t.id = c.id
      ]]>
    </content>
  </sql>
</sqlmap>