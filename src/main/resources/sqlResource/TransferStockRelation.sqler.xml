<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryTransferStockRelationCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_transfer_stock_relation
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND sell_sku = :sell_sku]>
        <[AND fn_sku = :fn_sku]>
        <[AND store = :store]>
        <[AND site = :site]>
        <[AND flag = :flag]>
        <[AND sku IN (:sku_list)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTransferStockRelationList" >
    <content >
      <![CDATA[
        SELECT id, sku, sell_sku, fn_sku, store, site, flag
        FROM wh_transfer_stock_relation
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND sell_sku = :sell_sku]>
        <[AND fn_sku = :fn_sku]>
        <[AND store = :store]>
        <[AND site = :site]>
        <[AND flag = :flag]>
        <[AND sku IN (:sku_list)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTransferStockRelationByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, sku, sell_sku, fn_sku, store, site, flag
        FROM wh_transfer_stock_relation
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTransferStockRelation" >
    <content >
      <![CDATA[
        SELECT id, sku, sell_sku, fn_sku, store, site, flag
        FROM wh_transfer_stock_relation
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND sell_sku = :sell_sku]>
        <[AND fn_sku = :fn_sku]>
        <[AND store = :store]>
        <[AND site = :site]>
        <[AND flag = :flag]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createTransferStockRelation" >
    <content >
      <![CDATA[
        INSERT INTO wh_transfer_stock_relation (sku, sell_sku, fn_sku, store, site, flag)
        VALUES (:sku, :sell_sku, :fn_sku, :store, :site, :flag)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteTransferStockRelationByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_transfer_stock_relation
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateTransferStockRelationByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_transfer_stock_relation
        SET <[sku = :sku,]>
          <[sell_sku = :sell_sku,]>
          <[fn_sku = :fn_sku,]>
          <[store = :store,]>
          <[site = :site,]>
          <[flag = :flag,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>