<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhLendReturnCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_lend_return
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND lend_check_out_code = :lend_check_out_code]>
        <[AND lend_check_out_code IN (:lendCheckOutCodeList)]>
        <[AND sku = :sku]>
        <[AND sku IN (:skuList)]>
        <[AND quantity = :quantity]>
        <[AND return_quantity = :return_quantity]>
        <[AND destroy_quantity = :destroy_quantity]>
        <[AND status = :status]>
        <[AND accept_by = :accept_by]>
        <[AND accept_by_name = :accept_by_name]>
        <[AND plan_return_time = :plan_return_time]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND name = :name]>
        <[AND creation_date >= :from_creation_date]>
	    <[AND creation_date <= :to_creation_date]>
	    <[AND plan_return_time >= :from_plan_return_time]>
	    <[AND plan_return_time <= :to_plan_return_time]>
        <[AND stock_id = :stock_id]>
	    <[:over_time]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhLendReturnListCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM(
          SELECT DISTINCT(lend_check_out_code)
          FROM wh_lend_return
          WHERE 1 = 1
          <[AND id = :id]>
          <[AND wlr.id in (:idList)]>
          <[AND lend_check_out_code = :lend_check_out_code]>
          <[AND lend_check_out_code IN (:lendCheckOutCodeList)]>
          <[AND sku = :sku]>
          <[AND sku IN (:skuList)]>
          <[AND quantity = :quantity]>
          <[AND return_quantity = :return_quantity]>
          <[AND destroy_quantity = :destroy_quantity]>
          <[AND status = :status]>
          <[AND return_status = :return_status]>
          <[AND accept_by = :accept_by]>
          <[AND accept_by_name = :accept_by_name]>
          <[AND plan_return_time = :plan_return_time]>
          <[AND created_by = :created_by]>
          <[AND creation_date = :creation_date]>
          <[AND name = :name]>
          <[AND creation_date >= :from_creation_date]>
          <[AND creation_date <= :to_creation_date]>
          <[AND plan_return_time >= :from_plan_return_time]>
          <[AND plan_return_time <= :to_plan_return_time]>
          <[AND stock_id = :stock_id]>
          <[:over_time]>
        )  wh_lend_return
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhLendReturnAndItemList" >
    <content >
      <![CDATA[
        SELECT wlr.id,wlr.stock_id, wlr.lend_check_out_code, wlr.sku, wlr.quantity, wlr.return_quantity, wlr.destroy_quantity, wlr.return_status,
        wlr.status, wlr.accept_by, wlr.accept_by_name, wlr.plan_return_time, wlr.created_by, wlr.creation_date, wlr.name,
        wli.pick_quantity,wli.quantity AS sku_total , wli.batch_no_json, wl.source
        FROM wh_lend_return wlr
        LEFT JOIN wh_lend  wl  ON wlr.lend_check_out_code=wl.lend_check_out_code
        LEFT JOIN wh_lend_item wli ON wl.id=wli.wh_lend_id
        WHERE 1 = 1
         AND wli.sku=wlr.sku
         AND wli.stock_id=wlr.stock_id
        <[AND wlr.id = :id]>
        <[AND wlr.id in (:idList)]>
        <[AND wlr.lend_check_out_code = :lend_check_out_code]>
        <[AND wlr.lend_check_out_code IN (:lendCheckOutCodeList)]>
        <[AND wlr.sku = :sku]>
        <[AND wlr.sku IN (:skuList)]>
        <[AND wlr.quantity = :quantity]>
        <[AND wlr.return_quantity = :return_quantity]>
        <[AND wlr.destroy_quantity = :destroy_quantity]>
        <[AND wlr.status = :status]>
        <[AND wlr.return_status = :return_status]>
        <[AND wlr.accept_by = :accept_by]>
        <[AND wlr.accept_by_name = :accept_by_name]>
        <[AND wlr.plan_return_time = :plan_return_time]>
        <[AND wlr.created_by = :created_by]>
        <[AND wlr.creation_date = :creation_date]>
        <[AND wlr.name = :name]>
        <[AND wlr.creation_date >= :from_creation_date]>
	    <[AND wlr.creation_date <= :to_creation_date]>
	    <[AND wlr.plan_return_time >= :from_plan_return_time]>
	    <[AND wlr.plan_return_time <= :to_plan_return_time]>
        <[AND wlr.stock_id = :stock_id]>
        <[AND wlr.stock_id IN (:stockIdList)]>
	    <[:over_time]>
	    <[:LIMIT]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhLendReturnList" >
    <content >
      <![CDATA[
        SELECT id, lend_check_out_code, sku, quantity, return_quantity, destroy_quantity, 
        status, accept_by, accept_by_name, plan_return_time, created_by, creation_date, name,stock_id,
        (SELECT source FROM wh_lend WHERE wh_lend_return.lend_check_out_code = wh_lend.lend_check_out_code) AS source
        FROM wh_lend_return
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND lend_check_out_code = :lend_check_out_code]>
        <[AND lend_check_out_code IN (:lendCheckOutCodeList)]>
        <[AND sku = :sku]>
        <[AND sku IN (:skuList)]>
        <[AND quantity = :quantity]>
        <[AND return_quantity = :return_quantity]>
        <[AND destroy_quantity = :destroy_quantity]>
        <[AND status = :status]>
        <[AND accept_by = :accept_by]>
        <[AND accept_by_name = :accept_by_name]>
        <[AND plan_return_time = :plan_return_time]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND name = :name]>
        <[AND creation_date >= :from_creation_date]>
	    <[AND creation_date <= :to_creation_date]>
	    <[AND plan_return_time >= :from_plan_return_time]>
	    <[AND plan_return_time <= :to_plan_return_time]>
        <[AND stock_id = :stock_id]>
	    <[:over_time]>
	    <[:LIMIT]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhLendReturnByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, lend_check_out_code, sku, quantity, return_quantity, destroy_quantity, stock_id,
        status, accept_by, accept_by_name, plan_return_time, created_by, creation_date, name,
        (SELECT source FROM wh_lend WHERE wh_lend_return.lend_check_out_code = wh_lend.lend_check_out_code) AS source
        FROM wh_lend_return
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhLendReturn" >
    <content >
      <![CDATA[
        SELECT id, lend_check_out_code, sku, quantity, return_quantity, destroy_quantity, stock_id,
        status, accept_by, accept_by_name, plan_return_time, created_by, creation_date, name,
        (SELECT source FROM wh_lend WHERE wh_lend_return.lend_check_out_code = wh_lend.lend_check_out_code) AS source
        FROM wh_lend_return
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND lend_check_out_code = :lend_check_out_code]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND return_quantity = :return_quantity]>
        <[AND destroy_quantity = :destroy_quantity]>
        <[AND status = :status]>
        <[AND accept_by = :accept_by]>
        <[AND accept_by_name = :accept_by_name]>
        <[AND plan_return_time = :plan_return_time]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND name = :name]>
        <[AND stock_id = :stock_id]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhLendReturn" >
    <content >
      <![CDATA[
        INSERT INTO wh_lend_return (lend_check_out_code, sku, quantity, return_quantity, destroy_quantity, 
          status, accept_by, accept_by_name, plan_return_time, created_by, creation_date, stock_id,
          name,return_status)
        VALUES (:lend_check_out_code, :sku, :quantity, :return_quantity, :destroy_quantity, 
          :status, :accept_by, :accept_by_name, :plan_return_time, :created_by, :creation_date, :stock_id,
          :name, :return_status)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhLendReturnByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_lend_return
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhLendReturnByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_lend_return
        SET <[lend_check_out_code = :lend_check_out_code,]>
          <[sku = :sku,]>
          <[quantity = :quantity,]>
          <[return_quantity = :return_quantity,]>
          <[destroy_quantity = :destroy_quantity,]>
          <[status = :status,]>
          <[return_status = :return_status,]>
          <[accept_by = :accept_by,]>
          <[accept_by_name = :accept_by_name,]>
          <[plan_return_time = :plan_return_time,]>
          <[created_by = :created_by,]>
          <[creation_date = :creation_date,]>
          <[name = :name,]>
          <[stock_id = :stock_id,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>