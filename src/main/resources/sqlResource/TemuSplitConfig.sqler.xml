<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryTemuSplitConfigCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM temu_split_config
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND sku IN (:skuList)]>
        <[AND weight = :weight]>
        <[AND length = :length]>
        <[AND width = :width]>
        <[AND height = :height]>
        <[AND pack_qty = :pack_qty]>
        <[AND real_pack_qty = :real_pack_qty]>
        <[AND update_num = :update_num]>
        <[AND creation_date = :creation_date]>
        <[AND created_by = :created_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[:COMPARE_QTY_TYPE_SQL]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTemuSplitConfigList" >
    <content >
      <![CDATA[
        SELECT t.id as id, t.sku as sku, s.weight as weight, s.length as length, s.width as width, s.height as height, pack_qty, real_pack_qty, update_num,
        t.creation_date as creation_date, t.created_by as created_by, t.last_update_date as last_update_date, t.last_updated_by as last_updated_by,
        IF(IFNULL(real_pack_qty,0) > IFNULL(pack_qty,0),true,false) as realQtyGtFlag
        FROM temu_split_config t
        LEFT JOIN wh_sku s ON t.sku = s.sku
        WHERE 1 = 1
        <[AND t.id = :id]>
        <[AND t.sku = :sku]>
        <[AND t.sku IN (:skuList)]>
        <[AND t.update_num = :update_num]>
        <[AND t.creation_date = :creation_date]>
        <[AND t.created_by = :created_by]>
        <[AND t.last_update_date = :last_update_date]>
        <[AND t.last_updated_by = :last_updated_by]>
        <[:COMPARE_QTY_TYPE_SQL]>
        <[:GREATER_THAN_ID_SQL]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTemuSplitConfigByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, sku, weight, length, width, height, pack_qty, real_pack_qty, update_num, 
        creation_date, created_by, last_update_date, last_updated_by
        FROM temu_split_config
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTemuSplitConfig" >
    <content >
      <![CDATA[
        SELECT id, sku, weight, length, width, height, pack_qty, real_pack_qty, update_num, 
        creation_date, created_by, last_update_date, last_updated_by
        FROM temu_split_config
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND weight = :weight]>
        <[AND length = :length]>
        <[AND width = :width]>
        <[AND height = :height]>
        <[AND pack_qty = :pack_qty]>
        <[AND real_pack_qty = :real_pack_qty]>
        <[AND update_num = :update_num]>
        <[AND creation_date = :creation_date]>
        <[AND created_by = :created_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createTemuSplitConfig" >
    <content >
      <![CDATA[
        INSERT INTO temu_split_config (sku, weight, length, width, height, pack_qty, real_pack_qty, update_num, 
          creation_date, created_by, last_update_date, last_updated_by)
        VALUES (:sku, :weight, :length, :width, :height, :pack_qty, :real_pack_qty, :update_num, 
          :creation_date, :created_by, :last_update_date, :last_updated_by)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteTemuSplitConfigByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM temu_split_config
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateTemuSplitConfigByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE temu_split_config
        SET <[sku = :sku,]>
          <[weight = :weight,]>
          <[length = :length,]>
          <[width = :width,]>
          <[height = :height,]>
          <[pack_qty = :pack_qty,]>
          <[real_pack_qty = :real_pack_qty,]>
          <[update_num = :update_num,]>
          <[creation_date = :creation_date,]>
          <[created_by = :created_by,]>
          <[last_update_date = :last_update_date,]>
          <[last_updated_by = :last_updated_by,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>