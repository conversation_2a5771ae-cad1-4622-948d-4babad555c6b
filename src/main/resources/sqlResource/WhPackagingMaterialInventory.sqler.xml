<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhPackagingMaterialInventoryCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_packaging_material_inventory
        WHERE 1 = 1
         <[AND id = :id]>
        <[AND id in (:ids)]>
        <[AND task_no = :task_no]>
        <[AND task_no in (:taskNoList)]>
        <[AND material_article_number = :material_article_number]>
        <[AND material_article_number in (:materialArticleNumberList)]>
        <[AND quantity = :quantity]>
        <[AND status = :status]>
        <[AND remark = :remark]>
        <[AND verify_date = :verify_date]>
        <[AND inventory_date = :inventory_date]>
        <[AND inventory_date >= :from_inventory_date]>
        <[AND inventory_date <= :end_inventory_date]>
        <[AND inventory_by = :inventory_by]>
        <[AND creation_date = :creation_date]>
        <[AND creation_by = :creation_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhPackagingMaterialInventoryList" >
    <content >
      <![CDATA[
        SELECT id, task_no, material_article_number, quantity, status, remark, verify_date, 
        inventory_date, inventory_by, creation_date, creation_by, last_update_date, last_updated_by,
        difference_quantity
        FROM wh_packaging_material_inventory
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id in (:ids)]>
        <[AND task_no = :task_no]>
        <[AND task_no in (:taskNoList)]>
        <[AND material_article_number = :material_article_number]>
        <[AND material_article_number in (:materialArticleNumberList)]>
        <[AND quantity = :quantity]>
        <[AND status = :status]>
        <[AND remark = :remark]>
        <[AND verify_date = :verify_date]>
        <[AND inventory_date = :inventory_date]>
        <[AND inventory_date >= :from_inventory_date]>
        <[AND inventory_date <= :end_inventory_date]>
        <[AND inventory_by = :inventory_by]>
        <[AND creation_date = :creation_date]>
        <[AND creation_by = :creation_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
        ORDER BY creation_date DESC
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhPackagingMaterialInventoryByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, task_no, material_article_number, quantity, status, remark, verify_date, 
        inventory_date, inventory_by, creation_date, creation_by, last_update_date, last_updated_by,
        difference_quantity
        FROM wh_packaging_material_inventory
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhPackagingMaterialInventory" >
    <content >
      <![CDATA[
        SELECT id, task_no, material_article_number, quantity, status, remark, verify_date, 
        inventory_date, inventory_by, creation_date, creation_by, last_update_date, last_updated_by,
        difference_quantity
        FROM wh_packaging_material_inventory
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_no = :task_no]>
        <[AND material_article_number = :material_article_number]>
        <[AND quantity = :quantity]>
        <[AND status = :status]>
        <[AND remark = :remark]>
        <[AND verify_date = :verify_date]>
        <[AND inventory_date = :inventory_date]>
        <[AND inventory_by = :inventory_by]>
        <[AND creation_date = :creation_date]>
        <[AND creation_by = :creation_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhPackagingMaterialInventory" >
    <content >
      <![CDATA[
        INSERT INTO wh_packaging_material_inventory (task_no, material_article_number, quantity, status, remark, verify_date, 
          inventory_date, inventory_by, creation_date, creation_by, last_update_date, 
          last_updated_by, difference_quantity)
        VALUES (:task_no, :material_article_number, :quantity, :status, :remark, :verify_date, 
          :inventory_date, :inventory_by, :creation_date, :creation_by, :last_update_date, 
          :last_updated_by, :difference_quantity)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhPackagingMaterialInventoryByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_packaging_material_inventory
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhPackagingMaterialInventoryByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_packaging_material_inventory
        SET <[task_no = :task_no,]>
          <[material_article_number = :material_article_number,]>
          <[quantity = :quantity,]>
          <[status = :status,]>
          <[remark = :remark,]>
          <[verify_date = :verify_date,]>
          <[inventory_date = :inventory_date,]>
          <[inventory_by = :inventory_by,]>
          <[creation_date = :creation_date,]>
          <[creation_by = :creation_by,]>
          <[last_update_date = :last_update_date,]>
          <[last_updated_by = :last_updated_by,]>
          <[difference_quantity = :difference_quantity,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>