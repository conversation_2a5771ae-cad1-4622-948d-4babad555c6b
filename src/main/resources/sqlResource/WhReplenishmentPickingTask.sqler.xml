<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhReplenishmentPickingTaskCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_replenishment_picking_task
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_no = :task_no]>
        <[AND task_status = :task_status]>
        <[AND task_type = :task_type]>
        <[AND apv_num = :apv_num]>
        <[AND sku_num = :sku_num]>
        <[AND pcs = :pcs]>
        <[AND picking_quantity = :picking_quantity]>
        <[AND picking_differ_quantity = :picking_differ_quantity]>
        <[AND created_by = :created_by]>
        <[AND created_date = :created_date]>
        <[AND receive_person = :receive_person]>
        <[AND receive_date = :receive_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND picking_end_date = :picking_end_date]>
        <[AND is_printing = :is_printing]>
        <[AND cross_floor = :cross_floor]>
        <[AND warehouse_type = :warehouse_type]>
        <[AND waybill_type = :waybill_type]>
        <[AND task_level = :task_level]>
        <[AND is_asn = :is_asn]>
        <[AND print_user = :print_user]>
        <[AND print_date = :print_date]>

        <[AND id in (:task_id_list)]>
        <[AND task_status in (:status_list)]>
        <[AND created_date >= :fromCreatedDate]>
        <[AND created_date <= :toCreatedDate]>
        <[AND picking_end_date >= :fromPickingEndDate]>
        <[AND picking_end_date <= :toPickingEndDate]>

        <[AND task_no in (:taskNoList)]>
        <[AND id in (select task_id from wh_replenishment_picking_sku where sku in (:skuList))]>
        <[AND id in (select task_id from wh_replenishment_picking_item where apv_no in (:apvNoList))]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhReplenishmentPickingTaskList" >
    <content >
      <![CDATA[
        SELECT id, task_no, task_status, task_type, apv_num, sku_num, pcs, picking_quantity, 
        picking_differ_quantity, created_by, created_date, receive_person, receive_date, 
        last_update_date, last_update_by, picking_end_date, is_printing, cross_floor, warehouse_type, 
        waybill_type, task_level, is_asn, print_user, print_date
        FROM wh_replenishment_picking_task
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_no = :task_no]>
        <[AND task_status = :task_status]>
        <[AND task_status in (:status_list)]>
        <[AND task_type = :task_type]>
        <[AND apv_num = :apv_num]>
        <[AND sku_num = :sku_num]>
        <[AND pcs = :pcs]>
        <[AND picking_quantity = :picking_quantity]>
        <[AND picking_differ_quantity = :picking_differ_quantity]>
        <[AND created_by = :created_by]>
        <[AND created_date = :created_date]>
        <[AND receive_person = :receive_person]>
        <[AND receive_date = :receive_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND picking_end_date = :picking_end_date]>
        <[AND is_printing = :is_printing]>
        <[AND cross_floor = :cross_floor]>
        <[AND warehouse_type = :warehouse_type]>
        <[AND waybill_type = :waybill_type]>
        <[AND task_level = :task_level]>
        <[AND is_asn = :is_asn]>
        <[AND print_user = :print_user]>
        <[AND print_date = :print_date]>

        <[AND id in (:task_id_list)]>
        <[AND task_status in (:status_list)]>
        <[AND created_date >= :fromCreatedDate]>
        <[AND created_date <= :toCreatedDate]>
        <[AND picking_end_date >= :fromPickingEndDate]>
        <[AND picking_end_date <= :toPickingEndDate]>

        <[AND task_no in (:taskNoList)]>
        <[AND id in (select task_id from wh_replenishment_picking_sku where sku in (:skuList))]>
        <[AND id in (select task_id from wh_replenishment_picking_item where apv_no in (:apvNoList))]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhReplenishmentPickingTaskByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, task_no, task_status, task_type, apv_num, sku_num, pcs, picking_quantity, 
        picking_differ_quantity, created_by, created_date, receive_person, receive_date, 
        last_update_date, last_update_by, picking_end_date, is_printing, cross_floor, warehouse_type, 
        waybill_type, task_level, is_asn, print_user, print_date
        FROM wh_replenishment_picking_task
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhReplenishmentPickingTask" >
    <content >
      <![CDATA[
        SELECT id, task_no, task_status, task_type, apv_num, sku_num, pcs, picking_quantity, 
        picking_differ_quantity, created_by, created_date, receive_person, receive_date, 
        last_update_date, last_update_by, picking_end_date, is_printing, cross_floor, warehouse_type, 
        waybill_type, task_level, is_asn, print_user, print_date
        FROM wh_replenishment_picking_task
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_no = :task_no]>
        <[AND task_status = :task_status]>
        <[AND task_type = :task_type]>
        <[AND apv_num = :apv_num]>
        <[AND sku_num = :sku_num]>
        <[AND pcs = :pcs]>
        <[AND picking_quantity = :picking_quantity]>
        <[AND picking_differ_quantity = :picking_differ_quantity]>
        <[AND created_by = :created_by]>
        <[AND created_date = :created_date]>
        <[AND receive_person = :receive_person]>
        <[AND receive_date = :receive_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND picking_end_date = :picking_end_date]>
        <[AND is_printing = :is_printing]>
        <[AND cross_floor = :cross_floor]>
        <[AND warehouse_type = :warehouse_type]>
        <[AND waybill_type = :waybill_type]>
        <[AND task_level = :task_level]>
        <[AND is_asn = :is_asn]>
        <[AND print_user = :print_user]>
        <[AND print_date = :print_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhReplenishmentPickingTask" >
    <content >
      <![CDATA[
        INSERT INTO wh_replenishment_picking_task (task_no, task_status, task_type, apv_num, sku_num, pcs, picking_quantity, 
          picking_differ_quantity, created_by, created_date, receive_person, receive_date, 
          last_update_date, last_update_by, picking_end_date, is_printing, cross_floor, 
          warehouse_type, waybill_type, task_level, is_asn, print_user, print_date)
        VALUES (:task_no, :task_status, :task_type, :apv_num, :sku_num, :pcs, :picking_quantity, 
          :picking_differ_quantity, :created_by, :created_date, :receive_person, :receive_date, 
          :last_update_date, :last_update_by, :picking_end_date, :is_printing, :cross_floor, 
          :warehouse_type, :waybill_type, :task_level, :is_asn, :print_user, :print_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhReplenishmentPickingTaskByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_replenishment_picking_task
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhReplenishmentPickingTaskByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_replenishment_picking_task
        SET <[task_no = :task_no,]>
          <[task_status = :task_status,]>
          <[task_type = :task_type,]>
          <[apv_num = :apv_num,]>
          <[sku_num = :sku_num,]>
          <[pcs = :pcs,]>
          <[picking_quantity = :picking_quantity,]>
          <[picking_differ_quantity = :picking_differ_quantity,]>
          <[created_by = :created_by,]>
          <[created_date = :created_date,]>
          <[receive_person = :receive_person,]>
          <[receive_date = :receive_date,]>
          <[last_update_date = :last_update_date,]>
          <[last_update_by = :last_update_by,]>
          <[picking_end_date = :picking_end_date,]>
          <[is_printing = :is_printing,]>
          <[cross_floor = :cross_floor,]>
          <[warehouse_type = :warehouse_type,]>
          <[waybill_type = :waybill_type,]>
          <[task_level = :task_level,]>
          <[is_asn = :is_asn,]>
          <[print_user = :print_user,]>
          <[print_date = :print_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="receiveReplenishmentPickingTask" >
    <content >
      <![CDATA[
        UPDATE wh_replenishment_picking_task
        SET
          <[task_status = :task_status,]>
          <[receive_person = :receive_person,]>
          <[receive_date = :receive_date,]>
          <[last_update_date = :last_update_date,]>
          <[last_update_by = :last_update_by,]>
        id = id
        WHERE 1 = 1
        AND id = :id
        AND task_status = :beforeStatus
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="completePickingTask" >
    <content >
      <![CDATA[
        UPDATE wh_replenishment_picking_task
        SET
          <[task_status = :task_status,]>
          <[last_update_date = :last_update_date,]>
          <[last_update_by = :last_update_by,]>
          <[receive_person = :receive_person,]>
          <[receive_date = :receive_date,]>
          <[picking_end_date = :picking_end_date,]>
          <[is_printing = :is_printing,]>
          <[cross_floor = :cross_floor,]>
          <[picking_quantity = :picking_quantity,]>
          <[picking_differ_quantity = :picking_differ_quantity,]>
        id = id
        WHERE 1 = 1
        AND id = :id
        <[AND task_status in (:beforeTaskStatus)]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryReplenishmentAndItemList" >
    <content >
      <![CDATA[
        SELECT task.id, task_no, task_status, task_type, apv_num, sku_num, pcs, picking_quantity,
          picking_differ_quantity, task.created_by, task.created_date, receive_person, receive_date,
          task.last_update_date, task.last_update_by, picking_end_date, is_printing, cross_floor, warehouse_type,
          waybill_type, task_level, is_asn, print_user, task.print_date,
          wa.apv_no, wai.sku, wai.sale_quantity, wai.pick_quantity, ws.name, ws.location_number
        FROM (select *
               from wh_replenishment_picking_task
               WHERE 1 = 1
                <[AND id = :id]>
                <[AND task_no = :task_no]>
                <[AND task_status = :task_status]>
                <[AND task_status in (:status_list)]>
                <[AND task_type = :task_type]>
                <[AND apv_num = :apv_num]>
                <[AND sku_num = :sku_num]>
                <[AND pcs = :pcs]>
                <[AND picking_quantity = :picking_quantity]>
                <[AND picking_differ_quantity = :picking_differ_quantity]>
                <[AND created_by = :created_by]>
                <[AND created_date = :created_date]>
                <[AND receive_person = :receive_person]>
                <[AND receive_date = :receive_date]>
                <[AND last_update_date = :last_update_date]>
                <[AND last_update_by = :last_update_by]>
                <[AND picking_end_date = :picking_end_date]>
                <[AND is_printing = :is_printing]>
                <[AND cross_floor = :cross_floor]>
                <[AND warehouse_type = :warehouse_type]>
                <[AND waybill_type = :waybill_type]>
                <[AND task_level = :task_level]>
                <[AND is_asn = :is_asn]>
                <[AND print_user = :print_user]>
                <[AND print_date = :print_date]>

                <[AND id in (:task_id_list)]>
                <[AND task_status in (:status_list)]>
                <[AND created_date >= :fromCreatedDate]>
                <[AND created_date <= :toCreatedDate]>
                <[AND picking_end_date >= :fromPickingEndDate]>
                <[AND picking_end_date <= :toPickingEndDate]>

                <[AND task_no in (:taskNoList)]>
                <[AND id in (select task_id from wh_replenishment_picking_sku where sku in (:skuList))]>
                <[AND id in (select task_id from wh_replenishment_picking_item where apv_no in (:apvNoList))]>
                <[:LIMIT]>
             ) task
        left join wh_replenishment_picking_item wpti on task.id = wpti.task_id
        Left join wh_apv wa on  wa.apv_no = wpti.apv_no
        Left join wh_apv_item wai on  wai.apv_id = wa.id
        Left join wh_sku ws on ws.sku = wai.sku
      ]]>
    </content>
  </sql>

</sqlmap>