<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhShippingMethodVolumeInfoCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_shipping_method_volume_info
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN ( :ids)]>
        <[AND length = :length]>
        <[AND wide = :wide]>
        <[AND high = :high]>
        <[AND sum = :sum]>
        <[AND volume = :volume]>
        <[AND create_time = :create_time]>
        <[AND update_time = :update_time]>
        <[AND shipping_method_id = :shipping_method_id]>
        <[AND country = :country]>
        <[AND shipping_method_code = :shipping_method_code]>
        <[AND shipping_method_name = :shipping_method_name]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhShippingMethodVolumeInfoList" >
    <content >
      <![CDATA[
        SELECT id, length, wide, high, sum, volume, create_time, update_time, shipping_method_id, 
        country, shipping_method_code, shipping_method_name
        FROM wh_shipping_method_volume_info
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN ( :ids)]>
        <[AND length = :length]>
        <[AND wide = :wide]>
        <[AND high = :high]>
        <[AND sum = :sum]>
        <[AND volume = :volume]>
        <[AND create_time = :create_time]>
        <[AND update_time = :update_time]>
        <[AND shipping_method_id = :shipping_method_id]>
        <[AND country = :country]>
        <[AND shipping_method_code = :shipping_method_code]>
        <[AND shipping_method_name = :shipping_method_name]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhShippingMethodVolumeInfoByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, length, wide, high, sum, volume, create_time, update_time, shipping_method_id, 
        country, shipping_method_code, shipping_method_name
        FROM wh_shipping_method_volume_info
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhShippingMethodVolumeInfo" >
    <content >
      <![CDATA[
        SELECT id, length, wide, high, sum, volume, create_time, update_time, shipping_method_id, 
        country, shipping_method_code, shipping_method_name
        FROM wh_shipping_method_volume_info
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN ( :ids)]>
        <[AND length = :length]>
        <[AND wide = :wide]>
        <[AND high = :high]>
        <[AND sum = :sum]>
        <[AND volume = :volume]>
        <[AND create_time = :create_time]>
        <[AND update_time = :update_time]>
        <[AND shipping_method_id = :shipping_method_id]>
        <[AND country = :country]>
        <[AND shipping_method_code = :shipping_method_code]>
        <[AND shipping_method_name = :shipping_method_name]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhShippingMethodVolumeInfo" >
    <content >
      <![CDATA[
        INSERT INTO wh_shipping_method_volume_info (id, length, wide, high, sum, volume, create_time, update_time, shipping_method_id,
          country, shipping_method_code, shipping_method_name)
        VALUES (:id, :length, :wide, :high, :sum, :volume, :create_time, :update_time, :shipping_method_id,
          :country, :shipping_method_code, :shipping_method_name)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhShippingMethodVolumeInfoByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_shipping_method_volume_info
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhShippingMethodVolumeInfoByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_shipping_method_volume_info
        SET <[length = :length,]>
          <[wide = :wide,]>
          <[high = :high,]>
          <[sum = :sum,]>
          <[volume = :volume,]>
          <[create_time = :create_time,]>
          <[update_time = :update_time,]>
          <[shipping_method_id = :shipping_method_id,]>
          <[country = :country,]>
          <[shipping_method_code = :shipping_method_code,]>
          <[shipping_method_name = :shipping_method_name,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>