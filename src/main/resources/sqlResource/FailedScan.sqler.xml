<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryFailedScanCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM failed_scan
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:idList)]>
        <[AND message = :message]>
        <[AND wms_success = :wms_success]>
        <[AND pms_success = :pms_success]>
        <[AND repeat_number = :repeat_number]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND creation_date >= :from_date]>
        <[AND creation_date <= :to_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryFailedScanList" >
    <content >
      <![CDATA[
        SELECT id, message, wms_success, pms_success, repeat_number, created_by, creation_date
        FROM failed_scan
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:idList)]>
        <[AND message = :message]>
        <[AND wms_success = :wms_success]>
        <[AND pms_success = :pms_success]>
        <[AND repeat_number = :repeat_number]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND creation_date >= :from_date]>
        <[AND creation_date <= :to_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryFailedScanByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, message, wms_success, pms_success, repeat_number, created_by, creation_date
        FROM failed_scan
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryFailedScan" >
    <content >
      <![CDATA[
        SELECT id, message, wms_success, pms_success, repeat_number, created_by, creation_date
        FROM failed_scan
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND message = :message]>
        <[AND wms_success = :wms_success]>
        <[AND pms_success = :pms_success]>
        <[AND repeat_number = :repeat_number]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createFailedScan" >
    <content >
      <![CDATA[
        INSERT INTO failed_scan (message, wms_success, pms_success, repeat_number, created_by, creation_date
          )
        VALUES (:message, :wms_success, :pms_success, :repeat_number, :created_by, :creation_date
          )
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteFailedScanByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM failed_scan
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateFailedScanByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE failed_scan
        SET <[message = :message,]>
          <[wms_success = :wms_success,]>
          <[pms_success = :pms_success,]>
          <[repeat_number = :repeat_number,]>
          <[created_by = :created_by,]>
          <[creation_date = :creation_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryNeedToRetryFailedScanList" >
    <content >
      <![CDATA[
        SELECT id, message, wms_success, pms_success, repeat_number, created_by, creation_date
        FROM failed_scan
        WHERE 1 = 1
        AND repeat_number <= 20
      ]]>
    </content>
  </sql>
  
</sqlmap>