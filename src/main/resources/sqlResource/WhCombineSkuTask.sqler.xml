<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhCombineSkuTaskCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_combine_sku_task
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND spu = :spu]>
        <[AND status = :status]>
        <[AND created_date = :created_date]>
        <[AND create_by = :create_by]>
        <[AND combine_date = :combine_date]>
        <[AND combine_by = :combine_by]>
        <[AND up_date = :up_date]>
        <[AND up_by = :up_by]>
        <[AND combine_quantity = :combine_quantity]>
        <[AND real_combine_quantity = :real_combine_quantity]>
        <[AND location_number = :location_number]>
        <[AND stock_id = :stock_id]>
        <[AND id IN (:id_list)]>
        <[AND spu IN (:spu_list)]>
        <[AND status IN (:status_list)]>
        <[AND id IN (SELECT combine_sku_task_id FROM wh_combine_sku_task_item WHERE sku IN (:sku_list))]>
        <[AND created_date >= :from_create_time]>
        <[AND created_date <= :to_create_time]>
        <[:RECEIVE_DATE_CONDITION_SQL]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhCombineSkuTaskList" >
    <content >
      <![CDATA[
        SELECT s.id, s.spu, s.status, s.created_date, s.create_by, s.combine_date, s.combine_by, s.up_date,
        s.up_by, s.combine_quantity, s.real_combine_quantity, s.location_number,s.stock_id, s.up_quantity,
         s.estimated_combine_quantity, s.allot_estimated_combine_quantity,
        i.id, i.combine_sku_task_id, i.spu, i.sku, i.location, i.name, i.quantity, i.allot_qty, i.pick_qty
        <[:RECEIVE_PERSON_FILED_SQL]>
        <[:RECEIVE_DATE_FILED_SQL]>
        FROM wh_combine_sku_task s
        LEFT JOIN wh_combine_sku_task_item i ON i.combine_sku_task_id = s.id
        INNER JOIN (
         SELECT id
        FROM wh_combine_sku_task
        WHERE 1 = 1
         <[AND id = :id]>
        <[AND spu = :spu]>
        <[AND status = :status]>
        <[AND created_date = :created_date]>
        <[AND create_by = :create_by]>
        <[AND combine_date = :combine_date]>
        <[AND combine_by = :combine_by]>
        <[AND up_date = :up_date]>
        <[AND up_by = :up_by]>
        <[AND combine_quantity = :combine_quantity]>
        <[AND real_combine_quantity = :real_combine_quantity]>
        <[AND location_number = :location_number]>
        <[AND stock_id = :stock_id]>
        <[AND id IN (:id_list)]>
        <[AND spu IN (:spu_list)]>
        <[AND status IN (:status_list)]>
        <[AND id IN (SELECT combine_sku_task_id FROM wh_combine_sku_task_item WHERE sku IN (:sku_list))]>
        <[AND created_date >= :from_create_time]>
        <[AND created_date <= :to_create_time]>
        <[:RECEIVE_DATE_CONDITION_SQL]>
        <[:LIMIT]>
        ) so on so.id = s.id

      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhCombineSkuTaskByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, spu, status, created_date, create_by, combine_date, combine_by, up_date, 
        up_by, combine_quantity, real_combine_quantity, location_number,stock_id, up_quantity, estimated_combine_quantity,allot_estimated_combine_quantity
        FROM wh_combine_sku_task
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhCombineSkuTask" >
    <content >
      <![CDATA[
        SELECT id, spu, status, created_date, create_by, combine_date, combine_by, up_date, 
        up_by, combine_quantity, real_combine_quantity, location_number,stock_id, up_quantity, estimated_combine_quantity,allot_estimated_combine_quantity
        FROM wh_combine_sku_task
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id in (:idList)]>
        <[AND spu = :spu]>
        <[AND status = :status]>
        <[AND created_date = :created_date]>
        <[AND create_by = :create_by]>
        <[AND combine_date = :combine_date]>
        <[AND combine_by = :combine_by]>
        <[AND up_date = :up_date]>
        <[AND up_by = :up_by]>
        <[AND combine_quantity = :combine_quantity]>
        <[AND real_combine_quantity = :real_combine_quantity]>
        <[AND location_number = :location_number]>
        <[AND stock_id = :stock_id]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhCombineSkuTask" >
    <content >
      <![CDATA[
        INSERT INTO wh_combine_sku_task (id, spu, status, created_date, create_by, combine_date, combine_by, up_date,
          up_by, combine_quantity, real_combine_quantity, location_number,stock_id, up_quantity, estimated_combine_quantity,allot_estimated_combine_quantity)
        VALUES (:id, :spu, :status, :created_date, :create_by, :combine_date, :combine_by, :up_date,
          :up_by, :combine_quantity, :real_combine_quantity, :location_number,:stock_id, :up_quantity, :estimated_combine_quantity,:allot_estimated_combine_quantity)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhCombineSkuTaskByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_combine_sku_task
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhCombineSkuTaskByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_combine_sku_task
        SET <[spu = :spu,]>
          <[status = :status,]>
          <[created_date = :created_date,]>
          <[create_by = :create_by,]>
          <[combine_date = :combine_date,]>
          <[combine_by = :combine_by,]>
          <[up_date = :up_date,]>
          <[up_by = :up_by,]>
          <[combine_quantity = :combine_quantity,]>
          <[real_combine_quantity = :real_combine_quantity,]>
          <[location_number = :location_number,]>
          <[stock_id = :stock_id,]>
          <[up_quantity = :up_quantity,]>
          <[estimated_combine_quantity = :estimated_combine_quantity,]>
          <[allot_estimated_combine_quantity = :allot_estimated_combine_quantity,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>