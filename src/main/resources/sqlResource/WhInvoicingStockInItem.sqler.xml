<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhInvoicingStockInItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_invoicing_stock_in_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND create_date = :create_date]>
        <[AND bat_no = :bat_no]>
        <[AND relation_bat_no = :relation_bat_no]>
        <[AND account_number = :account_number]>
        <[AND account_number IN (:accountNumberList)]>
        <[AND order_no = :order_no]>
        <[AND quantity = :quantity]>
        <[AND match_quantity = :match_quantity]>
        <[AND purchase_price = :purchase_price]>
        <[AND shipping_cost = :shipping_cost]>
        <[AND percentage = :percentage]>
        <[AND tax = :tax]>
        <[AND sku IN (:sku_list)]>
        <[AND relation_bat_no IN (:relation_bat_no_list)]>
        <[:QUERY_MATCH_ITEM]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhInvoicingStockInItemList" >
    <content >
      <![CDATA[
        SELECT id, sku, create_date, bat_no, relation_bat_no, account_number, order_no, quantity,
        match_quantity, purchase_price, shipping_cost, percentage, total_weight, tax
        FROM wh_invoicing_stock_in_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND create_date = :create_date]>
        <[AND bat_no = :bat_no]>
        <[AND relation_bat_no = :relation_bat_no]>
        <[AND account_number = :account_number]>
        <[AND account_number IN (:accountNumberList)]>
        <[AND order_no = :order_no]>
        <[AND quantity = :quantity]>
        <[AND match_quantity = :match_quantity]>
        <[AND purchase_price = :purchase_price]>
        <[AND shipping_cost = :shipping_cost]>
        <[AND percentage = :percentage]>
        <[AND tax = :tax]>
        <[AND sku IN (:sku_list)]>
        <[AND relation_bat_no IN (:relation_bat_no_list)]>
        <[:QUERY_MATCH_ITEM]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhInvoicingStockInItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, sku, create_date, bat_no, relation_bat_no, account_number, order_no, quantity,
        match_quantity, purchase_price, shipping_cost, percentage, total_weight, tax
        FROM wh_invoicing_stock_in_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhInvoicingStockInItem" >
    <content >
      <![CDATA[
        SELECT id, sku, create_date, bat_no, relation_bat_no, account_number, order_no, quantity,
        match_quantity, purchase_price, shipping_cost, percentage, total_weight, tax
        FROM wh_invoicing_stock_in_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND create_date = :create_date]>
        <[AND bat_no = :bat_no]>
        <[AND relation_bat_no = :relation_bat_no]>
        <[AND account_number = :account_number]>
        <[AND order_no = :order_no]>
        <[AND quantity = :quantity]>
        <[AND match_quantity = :match_quantity]>
        <[AND purchase_price = :purchase_price]>
        <[AND shipping_cost = :shipping_cost]>
        <[AND percentage = :percentage]>
        <[AND tax = :tax]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhInvoicingStockInItem" >
    <content >
      <![CDATA[
        INSERT INTO wh_invoicing_stock_in_item (sku, create_date, bat_no, relation_bat_no, account_number, order_no,
          quantity, match_quantity, purchase_price, shipping_cost, percentage, total_weight, tax)
        VALUES (:sku, :create_date, :bat_no, :relation_bat_no, :account_number, :order_no,
          :quantity, :match_quantity, :purchase_price, :shipping_cost, :percentage, :total_weight, :tax)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhInvoicingStockInItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_invoicing_stock_in_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhInvoicingStockInItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_invoicing_stock_in_item
        SET <[sku = :sku,]>
          <[create_date = :create_date,]>
          <[bat_no = :bat_no,]>
          <[relation_bat_no = :relation_bat_no,]>
          <[account_number = :account_number,]>
          <[order_no = :order_no,]>
          <[quantity = :quantity,]>
          <[match_quantity = :match_quantity,]>
          <[purchase_price = :purchase_price,]>
          <[shipping_cost = :shipping_cost,]>
          <[percentage = :percentage,]>
          <[total_weight = :total_weight,]>
          <[tax = :tax,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>