<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryAmazonFbaFulfillmentInventorySummaryDataCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM amazon_fba_fulfillment_inventory_summary_data
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND account_number = :account_number]>
        <[AND snapshot_date = :snapshot_date]>
        <[AND transaction_type = :transaction_type]>
        <[AND fnsku = :fnsku]>
        <[AND sku = :sku]>
        <[AND product_name = :product_name]>
        <[AND fulfillment_center_id = :fulfillment_center_id]>
        <[AND quantity = :quantity]>
        <[AND disposition = :disposition]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_user = :last_update_user]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAmazonFbaFulfillmentInventorySummaryDataList" >
    <content >
      <![CDATA[
        SELECT id, account_number, snapshot_date, transaction_type, fnsku, sku, product_name, 
        fulfillment_center_id, quantity, disposition, created_by, creation_date, last_update_user, 
        last_update_date
        FROM amazon_fba_fulfillment_inventory_summary_data
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND account_number = :account_number]>
        <[AND snapshot_date = :snapshot_date]>
        <[AND transaction_type = :transaction_type]>
        <[AND fnsku = :fnsku]>
        <[AND sku = :sku]>
        <[AND product_name = :product_name]>
        <[AND fulfillment_center_id = :fulfillment_center_id]>
        <[AND quantity = :quantity]>
        <[AND disposition = :disposition]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_user = :last_update_user]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAmazonFbaFulfillmentInventorySummaryDataByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, account_number, snapshot_date, transaction_type, fnsku, sku, product_name, 
        fulfillment_center_id, quantity, disposition, created_by, creation_date, last_update_user, 
        last_update_date
        FROM amazon_fba_fulfillment_inventory_summary_data
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAmazonFbaFulfillmentInventorySummaryData" >
    <content >
      <![CDATA[
        SELECT id, account_number, snapshot_date, transaction_type, fnsku, sku, product_name, 
        fulfillment_center_id, quantity, disposition, created_by, creation_date, last_update_user, 
        last_update_date
        FROM amazon_fba_fulfillment_inventory_summary_data
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND account_number = :account_number]>
        <[AND snapshot_date = :snapshot_date]>
        <[AND transaction_type = :transaction_type]>
        <[AND fnsku = :fnsku]>
        <[AND sku = :sku]>
        <[AND product_name = :product_name]>
        <[AND fulfillment_center_id = :fulfillment_center_id]>
        <[AND quantity = :quantity]>
        <[AND disposition = :disposition]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_user = :last_update_user]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createAmazonFbaFulfillmentInventorySummaryData" >
    <content >
      <![CDATA[
        INSERT INTO amazon_fba_fulfillment_inventory_summary_data (account_number, snapshot_date, transaction_type, fnsku, sku, product_name, 
          fulfillment_center_id, quantity, disposition, created_by, creation_date, last_update_user, 
          last_update_date)
        VALUES (:account_number, :snapshot_date, :transaction_type, :fnsku, :sku, :product_name, 
          :fulfillment_center_id, :quantity, :disposition, :created_by, :creation_date, :last_update_user, 
          :last_update_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteAmazonFbaFulfillmentInventorySummaryDataByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM amazon_fba_fulfillment_inventory_summary_data
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="batchInsertOrUpdateAmazonFbaFulfillmentInventorySummaryData">
    <![CDATA[
       INSERT INTO amazon_fba_fulfillment_inventory_summary_data (account_number, snapshot_date, transaction_type, fnsku, sku, product_name,
          fulfillment_center_id, quantity, disposition, created_by, creation_date, last_update_user,
          last_update_date,market_place,merchant_id)
        VALUES
        <[:INSERT_SQL]>
        ON DUPLICATE KEY UPDATE
        <[:UPDATE_SQL]>
      ]]>
  </sql>
  
  <sql datasource="dataSource" id="updateAmazonFbaFulfillmentInventorySummaryDataByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE amazon_fba_fulfillment_inventory_summary_data
        SET <[account_number = :account_number,]>
          <[snapshot_date = :snapshot_date,]>
          <[transaction_type = :transaction_type,]>
          <[fnsku = :fnsku,]>
          <[sku = :sku,]>
          <[product_name = :product_name,]>
          <[fulfillment_center_id = :fulfillment_center_id,]>
          <[quantity = :quantity,]>
          <[disposition = :disposition,]>
          <[created_by = :created_by,]>
          <[creation_date = :creation_date,]>
          <[last_update_user = :last_update_user,]>
          <[last_update_date = :last_update_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>