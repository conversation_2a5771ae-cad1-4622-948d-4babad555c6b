<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhLogCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_log
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND type = :type]>
        <[AND type IN (:type_list)]>
        <[AND type != :exclude_type]>
        <[AND type NOT IN (:exclude_type_list)]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[:CHANGE_QUANTITY]>
        <[AND original_quantity = :original_quantity]>
        <[AND price = :price]>
        <[AND original_price = :original_price]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND created_by = :created_by]>
        <[AND creation_date >= :from_date]>
        <[AND creation_date <= :to_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhLogList" >
    <content >
      <![CDATA[
        SELECT id, warehouse_id, type, sku, quantity, original_quantity, price, original_price, 
        remark, creation_date, created_by, last_update_date, last_update_by
        FROM wh_log
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND type = :type]>
        <[AND type IN (:type_list)]>
        <[AND type != :exclude_type]>
        <[AND type NOT IN (:exclude_type_list)]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[:CHANGE_QUANTITY]>
        <[AND original_quantity = :original_quantity]>
        <[AND price = :price]>
        <[AND original_price = :original_price]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND created_by = :created_by]>
        <[AND creation_date >= :from_date]>
        <[AND creation_date <= :to_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        ORDER BY id DESC
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhLogByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, warehouse_id, type, sku, quantity, original_quantity, price, original_price, 
        remark, creation_date, created_by, last_update_date, last_update_by
        FROM wh_log
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhLog" >
    <content >
      <![CDATA[
        SELECT id, warehouse_id, type, sku, quantity, original_quantity, price, original_price, 
        remark, creation_date, created_by, last_update_date, last_update_by
        FROM wh_log
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND type = :type]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND original_quantity = :original_quantity]>
        <[AND price = :price]>
        <[AND original_price = :original_price]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND created_by = :created_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhLog" >
    <content >
      <![CDATA[
        INSERT INTO wh_log (warehouse_id, type, sku, quantity, original_quantity, price, original_price, 
          remark, creation_date, created_by, last_update_date, last_update_by)
        VALUES (:warehouse_id, :type, :sku, :quantity, :original_quantity, :price, :original_price, 
          :remark, :creation_date, :created_by, :last_update_date, :last_update_by)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhLogByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_log
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhLogByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_log
        SET <[warehouse_id = :warehouse_id,]>
          <[type = :type,]>
          <[sku = :sku,]>
          <[quantity = :quantity,]>
          <[original_quantity = :original_quantity,]>
          <[price = :price,]>
          <[original_price = :original_price,]>
          <[remark = :remark,]>
          <[creation_date = :creation_date,]>
          <[created_by = :created_by,]>
          <[last_update_date = :last_update_date,]>
          <[last_update_by = :last_update_by,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryLogStatisticsCount" >
    <content >
      <![CDATA[
        SELECT count(1)
		FROM (
			SELECT sum(quantity), sku, type
			FROM wh_log
			WHERE 1 = 1
	        <[AND type IN (:type_list)]>
	        <[AND type = :type]>
	        <[AND sku = :sku]>
	        <[:SQL_QUANTITY]>
	        <[AND creation_date >= :from_date]>
	        <[AND creation_date <= :to_date]>
			GROUP BY sku, type
		) statistics
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryLogStatistics" >
    <content >
      <![CDATA[
        SELECT sum(quantity) as quantity, sku, type
		FROM wh_log
		WHERE 1 = 1
        <[AND type IN (:type_list)]>
        <[AND type = :type]>
        <[AND sku = :sku]>
        <[:SQL_QUANTITY]>
        <[AND creation_date >= :from_date]>
        <[AND creation_date <= :to_date]>
		GROUP BY sku, type
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryLogPCS" >
    <content >
      <![CDATA[
        SELECT sum(quantity) as quantity, type, 0 as sku
		FROM wh_log
		WHERE 1 = 1
        <[AND type IN (:type_list)]>
        <[AND type = :type]>
        <[AND sku = :sku]>
        <[:SQL_QUANTITY]>
        <[AND creation_date >= :from_date]>
        <[AND creation_date <= :to_date]>
		GROUP BY type
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryLogDayQuantity" >
    <content >
      <![CDATA[
        SELECT SUM(count) AS count
		FROM (
			SELECT SUM(count) AS count
			FROM (SELECT original_quantity + quantity AS count, row_number() over(partition by sku order by id desc) rn
				FROM wh_log
				WHERE type IN (1, 2, 3, 8, 10)
				<[AND creation_date <= :to_date]>
			) t WHERE t.rn <=1
			UNION ALL
			SELECT SUM(original_quantity) AS count
			FROM (SELECT MIN(creation_date), original_quantity, sku
				FROM wh_log
				WHERE type IN (1, 2, 3, 8, 10)
				AND sku NOT IN (
					SELECT sku from (
						SELECT sku FROM wh_log WHERE type IN (1, 2, 3, 8, 10) <[AND creation_date <= :to_date]> GROUP BY sku
						UNION ALL 
						SELECT sku FROM wh_stock <[WHERE creation_date > :to_date]>
						UNION ALL
						SELECT sku FROM wh_log WHERE sku not in (SELECT sku FROM wh_stock)
					) t_sku GROUP BY sku
				)
				GROUP BY sku
			) t1
			UNION ALL
			SELECT SUM(qc_quantity+waiting_up_quantity+up_quantity+surplus_quantity+pick_quantity+cancel_quantity+allot_quantity+pick_not_quantity) AS count
			FROM wh_stock
			WHERE sku NOT IN (SELECT sku FROM wh_log WHERE type IN (1, 2, 3, 8, 10) GROUP BY sku)
			<[AND creation_date <= :to_date]>
		) tt
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryLogPcsByDay" >
    <content >
      <![CDATA[
		SELECT quantity, type, date as sku from (
			SELECT sum(quantity) AS quantity, type, DATE_FORMAT(creation_date, '%Y-%m-%d') AS date
				FROM wh_log
				WHERE 1 = 1
				AND type IN (1, 3, 8, 10)
		        <[AND creation_date >= :from_date]>
		        <[AND creation_date <= :to_date]>
				GROUP BY type, date
			UNION ALL
			SELECT sum(quantity) AS quantity, CONCAT('10', type) AS type, DATE_FORMAT(creation_date, '%Y-%m-%d') AS date
				FROM wh_log
				WHERE 1 = 1
				AND type = 2
				AND quantity < 0
		        <[AND creation_date >= :from_date]>
		        <[AND creation_date <= :to_date]>
				GROUP BY type, date
			UNION ALL
			SELECT sum(quantity) AS quantity, type, DATE_FORMAT(creation_date, '%Y-%m-%d') AS date
				FROM wh_log
				WHERE 1 = 1
				AND type = 2
				AND quantity > 0
		        <[AND creation_date >= :from_date]>
		        <[AND creation_date <= :to_date]>
				GROUP BY type, date
		) t
		ORDER BY sku DESC
      ]]>
    </content>
  </sql>
</sqlmap>