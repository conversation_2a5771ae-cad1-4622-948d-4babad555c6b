<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryVerifyCheckInItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM verify_check_in_item vcii
              LEFT JOIN wh_check_in wci ON vcii.in_id = wci.in_id
              LEFT JOIN verify_check_in_task vcitt ON vcii.verify_task_id = vcitt.id
        WHERE 1 = 1
        <[AND vcii.id = :id]>
        <[AND vcii.id IN (:ids)]>
        <[AND wci.in_id = :in_id]>
        <[AND wci.in_id in (:in_id_list)]>
        <[AND vcii.sku = :sku]>
        <[AND vcii.sku IN (:sku_list)]>
        <[AND vcii.sku IN (SELECT sku FROM wh_sku_extend WHERE FIND_IN_SET(:warehouse_sku_tag, tags))]>
        <[AND vcii.sku IN (SELECT sku FROM wh_sku_qc_category WHERE qc_category_remark LIKE :q_category_remark)]>
        <[AND verify_task_id = :verify_task_id]>
        <[AND verify_task_id IN (:verify_task_ids)]>
        <[AND vcitt.task_no IN (:verify_task_no_list)]>
        <[AND vcitt.task_no = :verify_task_no]>
        <[AND vcii.verify_result = :verify_result]>
        <[AND vcii.verify_reason = :verify_reason]>
        <[AND vcii.creation_date = :creation_date]>
        <[AND vcii.task_date = :task_date]>
        <[AND vcii.verify_date = :verify_date]>
        <[AND wci.up_time >= :from_up_date]>
        <[AND vcii.verify_date >= :from_verify_date]>
        <[AND wci.up_time <= :to_up_date]>
        <[AND vcii.verify_date <= :to_verify_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryVerifyCheckInItemList" >
    <content >
      <![CDATA[
        SELECT vcii.id, vcii.in_id, vcii.sku, vcii.verify_task_id, vcii.verify_result, vcii.verify_reason, vcii.creation_date,
        vcii.task_date, vcii.verify_date, vcitt.task_no, up_quantity
        ,
        CASE
          WHEN wcii.sku_id IS NULL THEN wcii.location
          ELSE (
              SELECT location_number
              FROM wh_stock
              WHERE id = wcii.sku_id
          )
	    END AS location_number
        FROM verify_check_in_item vcii
              LEFT JOIN wh_check_in wci ON vcii.in_id = wci.in_id
              INNER JOIN wh_check_in_item wcii ON wcii.in_id = wci.in_id
              LEFT JOIN verify_check_in_task vcitt ON vcii.verify_task_id = vcitt.id
        WHERE 1 = 1
        <[AND vcii.id = :id]>
        <[AND vcii.id IN (:ids)]>
        <[AND wci.in_id = :in_id]>
        <[AND wci.in_id in (:in_id_list)]>
        <[AND vcii.sku = :sku]>
        <[AND vcii.sku IN (:sku_list)]>
        <[AND vcii.sku IN (SELECT sku FROM wh_sku_extend WHERE FIND_IN_SET(:warehouse_sku_tag, tags))]>
        <[AND vcii.sku IN (SELECT sku FROM wh_sku_qc_category WHERE qc_category_remark LIKE :q_category_remark)]>
        <[AND verify_task_id = :verify_task_id]>
        <[AND verify_task_id IN (:verify_task_ids)]>
        <[AND vcitt.task_no IN (:verify_task_no_list)]>
        <[AND vcitt.task_no = :verify_task_no]>
        <[AND vcii.verify_result = :verify_result]>
        <[AND vcii.verify_reason = :verify_reason]>
        <[AND vcii.creation_date = :creation_date]>
        <[AND vcii.task_date = :task_date]>
        <[AND vcii.verify_date = :verify_date]>
        <[AND wci.up_time >= :from_up_date]>
        <[AND vcii.verify_date >= :from_verify_date]>
        <[AND wci.up_time <= :to_up_date]>
        <[AND vcii.verify_date <= :to_verify_date]>
        ORDER BY IF(vcii.verify_task_id IS NULL, 0, vcii.verify_task_id) ASC,
         CASE
              WHEN SUBSTR(location_number,1,1) IN ('Y', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M')
              THEN FIELD(SUBSTR(location_number,1,1),"Y", "D", "E", "F", "G", "H", "J", "K", "L", "M")
              WHEN SUBSTR(location_number,1,1) IN ('2')
              THEN FIELD(SUBSTR(location_number,1,2),"2K", "2A", "2B", "2C", "2D", "2F", "2E")
              ELSE 30
          END, location_number ASC
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryVerifyCheckInItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT vcii.id, vcii.in_id, vcii.sku, vcii.verify_task_id, vcii.verify_result, vcii.verify_reason, vcii.creation_date,
        vcii.task_date, vcii.verify_date, vcitt.task_no, up_quantity
        ,
        CASE
          WHEN wcii.sku_id IS NULL THEN wcii.location
          ELSE (
              SELECT location_number
              FROM wh_stock
              WHERE id = wcii.sku_id
          )
	    END AS location_number
        FROM verify_check_in_item vcii
              LEFT JOIN wh_check_in wci ON vcii.in_id = wci.in_id
              INNER JOIN wh_check_in_item wcii ON wcii.in_id = wci.in_id
              LEFT JOIN verify_check_in_task vcitt ON vcii.verify_task_id = vcitt.id
        WHERE 1 = 1
        AND vcii.id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryVerifyCheckInItem" >
    <content >
      <![CDATA[
        SELECT vcii.id, vcii.in_id, vcii.sku, vcii.verify_task_id, vcii.verify_result, vcii.verify_reason, vcii.creation_date,
        vcii.task_date, vcii.verify_date, vcitt.task_no, up_quantity
        ,
        CASE
          WHEN wcii.sku_id IS NULL THEN wcii.location
          ELSE (
              SELECT location_number
              FROM wh_stock
              WHERE id = wcii.sku_id
          )
	    END AS location_number
        FROM verify_check_in_item vcii
              LEFT JOIN wh_check_in wci ON vcii.in_id = wci.in_id
              INNER JOIN wh_check_in_item wcii ON wcii.in_id = wci.in_id
              LEFT JOIN verify_check_in_task vcitt ON vcii.verify_task_id = vcitt.id
        WHERE 1 = 1
        <[AND vcii.id = :id]>
        <[AND vcii.id IN (:ids)]>
        <[AND wci.in_id = :in_id]>
        <[AND wci.in_id in (:in_id_list)]>
        <[AND vcii.sku = :sku]>
        <[AND vcii.sku IN (:sku_list)]>
        <[AND vcii.sku IN (SELECT sku FROM wh_sku_extend WHERE FIND_IN_SET(:warehouse_sku_tag, tags))]>
        <[AND vcii.sku IN (SELECT sku FROM wh_sku_qc_category WHERE qc_category_remark LIKE :q_category_remark)]>
        <[AND verify_task_id = :verify_task_id]>
        <[AND verify_task_id IN (:verify_task_ids)]>
        <[AND vcitt.task_no IN (:verify_task_no_list)]>
        <[AND vcitt.task_no = :verify_task_no]>
        <[AND vcii.verify_result = :verify_result]>
        <[AND vcii.verify_reason = :verify_reason]>
        <[AND vcii.creation_date = :creation_date]>
        <[AND vcii.task_date = :task_date]>
        <[AND vcii.verify_date = :verify_date]>
        <[AND wci.up_time >= :from_up_date]>
        <[AND vcii.verify_date >= :from_verify_date]>
        <[AND wci.up_time <= :to_up_date]>
        <[AND vcii.verify_date <= :to_verify_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createVerifyCheckInItem" >
    <content >
      <![CDATA[
        INSERT INTO verify_check_in_item (in_id, sku, verify_task_id, verify_result, verify_reason, creation_date, 
          task_date, verify_date)
        VALUES (:in_id, :sku, :verify_task_id, :verify_result, :verify_reason, :creation_date, 
          :task_date, :verify_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteVerifyCheckInItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM verify_check_in_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateVerifyCheckInItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE verify_check_in_item
        SET <[in_id = :in_id,]>
          <[sku = :sku,]>
          <[verify_task_id = :verify_task_id,]>
          <[verify_task_id = CASE WHEN :release_task = TRUE THEN null ELSE verify_task_id END,]>
          <[task_date = CASE WHEN :release_task = TRUE THEN null ELSE task_date END,]>
          <[verify_result = :verify_result,]>
          <[verify_reason = :verify_reason,]>
          <[creation_date = :creation_date,]>
          <[task_date = :task_date,]>
          <[verify_date = :verify_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>