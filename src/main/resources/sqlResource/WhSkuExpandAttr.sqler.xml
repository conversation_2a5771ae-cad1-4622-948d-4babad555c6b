<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhSkuExpandAttrCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_sku_expand_attr
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND sku IN (:skuList)]>
        <[AND attr_code IN (:codeList)]>
        <[AND attr_code = :attr_code]>
        <[AND attr_name = :attr_name]>
        <[AND attr_value = :attr_value]>
        <[AND attr_type_code = :attr_type_code]>
        <[AND created_date = :created_date]>
        <[AND modified_date = :modified_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhSkuExpandAttrList" >
    <content >
      <![CDATA[
        SELECT id, sku, attr_code, attr_name, attr_value, attr_type_code, created_date, modified_date
        FROM wh_sku_expand_attr
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND sku IN (:skuList)]>
        <[AND attr_code IN (:codeList)]>
        <[AND attr_code = :attr_code]>
        <[AND attr_name = :attr_name]>
        <[AND attr_value = :attr_value]>
        <[AND attr_type_code = :attr_type_code]>
        <[AND created_date = :created_date]>
        <[AND modified_date = :modified_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhSkuExpandAttrByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, sku, attr_code, attr_name, attr_value, attr_type_code, created_date, modified_date
        FROM wh_sku_expand_attr
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhSkuExpandAttr" >
    <content >
      <![CDATA[
        SELECT id, sku, attr_code, attr_name, attr_value, attr_type_code, created_date, modified_date
        FROM wh_sku_expand_attr
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND attr_code = :attr_code]>
        <[AND attr_name = :attr_name]>
        <[AND attr_value = :attr_value]>
        <[AND attr_type_code = :attr_type_code]>
        <[AND created_date = :created_date]>
        <[AND modified_date = :modified_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhSkuExpandAttr" >
    <content >
      <![CDATA[
        INSERT INTO wh_sku_expand_attr (sku, attr_code, attr_name, attr_value, attr_type_code)
        VALUES (:sku, :attr_code, :attr_name, :attr_value, :attr_type_code)
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="insertOrUpdateWhSkuExpandAttr" >
    <content >
      <![CDATA[
        INSERT INTO wh_sku_expand_attr (sku, attr_code, attr_name, attr_value, attr_type_code)
        VALUES
        <[:INSERT_SQL]>
        ON DUPLICATE KEY UPDATE
        <[:UPDATE_SQL]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhSkuExpandAttrByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_sku_expand_attr
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="deleteWhSkuExpandAttrBySku" >
    <content >
      <![CDATA[
        DELETE FROM wh_sku_expand_attr
        WHERE 1 = 1
        AND sku = :sku
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhSkuExpandAttrByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_sku_expand_attr
        SET <[sku = :sku,]>
          <[attr_code = :attr_code,]>
          <[attr_name = :attr_name,]>
          <[attr_value = :attr_value,]>
          <[attr_type_code = :attr_type_code,]>
          <[created_date = :created_date,]>
          <[modified_date = :modified_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>