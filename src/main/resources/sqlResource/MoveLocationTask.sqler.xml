<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryMoveLocationTaskCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM move_location_task
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_no = :task_no]>
        <[AND location_count = :location_count]>
        <[AND sku_count = :sku_count]>
        <[AND shelf = :shelf]>
        <[AND box_no = :box_no]>
        <[AND task_status = :task_status]>
        <[AND created_by = :created_by]>
        <[AND pick_person = :pick_person]>
        <[AND up_person = :up_person]>
        <[AND creation_date = :creation_date]>
        <[AND receive_date = :receive_date]>
        <[AND picking_end_date = :picking_end_date]>
        <[AND receive_up_date = :receive_up_date]>
        <[AND up_end_date = :up_end_date]>
        <[AND task_status IN (:statusList)]>
        <[AND task_no IN (SELECT task_no FROM move_location_task_item WHERE sku = :sku)]>
        <[AND task_no IN (SELECT task_no FROM move_location_task_item WHERE sku IN (:skuList))]>
        <[AND id IN (:ids)]>
        <[AND task_no IN (:taskNos)]>
        <[AND creation_date >= :fromCreationDate]>
        <[AND creation_date < :toCreationDate]>
        <[AND receive_date >= :fromReceiveDate]>
        <[AND receive_date < :toReceiveDate]>
        <[AND up_end_date >= :fromUpEndDate]>
        <[AND up_end_date < :toUpEndDate]>

      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryMoveLocationTaskList" >
    <content >
      <![CDATA[
        SELECT id, task_no, location_count, sku_count, shelf, box_no, task_status, created_by, 
        pick_person, up_person, creation_date, receive_date, picking_end_date, receive_up_date, 
        up_end_date
        FROM move_location_task
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_no = :task_no]>
        <[AND location_count = :location_count]>
        <[AND sku_count = :sku_count]>
        <[AND shelf = :shelf]>
        <[AND box_no = :box_no]>
        <[AND task_status = :task_status]>
        <[AND created_by = :created_by]>
        <[AND pick_person = :pick_person]>
        <[AND up_person = :up_person]>
        <[AND creation_date = :creation_date]>
        <[AND receive_date = :receive_date]>
        <[AND picking_end_date = :picking_end_date]>
        <[AND receive_up_date = :receive_up_date]>
        <[AND up_end_date = :up_end_date]>
        <[AND task_status IN (:statusList)]>
        <[AND task_no IN (SELECT task_no FROM move_location_task_item WHERE sku = :sku)]>
        <[AND task_no IN (SELECT task_no FROM move_location_task_item WHERE sku IN (:skuList))]>
        <[AND id IN (:ids)]>
        <[AND task_no IN (:taskNos)]>
        <[AND creation_date >= :fromCreationDate]>
        <[AND creation_date < :toCreationDate]>
        <[AND receive_date >= :fromReceiveDate]>
        <[AND receive_date < :toReceiveDate]>
        <[AND up_end_date >= :fromUpEndDate]>
        <[AND up_end_date < :toUpEndDate]>

        ORDER BY <[:ORDER_BY]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryMoveLocationTaskByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, task_no, location_count, sku_count, shelf, box_no, task_status, created_by, 
        pick_person, up_person, creation_date, receive_date, picking_end_date, receive_up_date, 
        up_end_date
        FROM move_location_task
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryMoveLocationTask" >
    <content >
      <![CDATA[
        SELECT id, task_no, location_count, sku_count, shelf, box_no, task_status, created_by, 
        pick_person, up_person, creation_date, receive_date, picking_end_date, receive_up_date, 
        up_end_date
        FROM move_location_task
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_no = :task_no]>
        <[AND location_count = :location_count]>
        <[AND sku_count = :sku_count]>
        <[AND shelf = :shelf]>
        <[AND box_no = :box_no]>
        <[AND task_status = :task_status]>
        <[AND created_by = :created_by]>
        <[AND pick_person = :pick_person]>
        <[AND up_person = :up_person]>
        <[AND creation_date = :creation_date]>
        <[AND receive_date = :receive_date]>
        <[AND picking_end_date = :picking_end_date]>
        <[AND receive_up_date = :receive_up_date]>
        <[AND up_end_date = :up_end_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createMoveLocationTask" >
    <content >
      <![CDATA[
        INSERT INTO move_location_task (task_no, location_count, sku_count, shelf, box_no, task_status, created_by, 
          pick_person, up_person, creation_date, receive_date, picking_end_date, receive_up_date, 
          up_end_date)
        VALUES (:task_no, :location_count, :sku_count, :shelf, :box_no, :task_status, :created_by, 
          :pick_person, :up_person, :creation_date, :receive_date, :picking_end_date, :receive_up_date, 
          :up_end_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteMoveLocationTaskByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM move_location_task
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateMoveLocationTaskByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE move_location_task
        SET <[task_no = :task_no,]>
          <[location_count = :location_count,]>
          <[sku_count = :sku_count,]>
          <[shelf = :shelf,]>
          <[box_no = :box_no,]>
          <[task_status = :task_status,]>
          <[created_by = :created_by,]>
          <[pick_person = :pick_person,]>
          <[up_person = :up_person,]>
          <[creation_date = :creation_date,]>
          <[receive_date = :receive_date,]>
          <[picking_end_date = :picking_end_date,]>
          <[receive_up_date = :receive_up_date,]>
          <[up_end_date = :up_end_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>