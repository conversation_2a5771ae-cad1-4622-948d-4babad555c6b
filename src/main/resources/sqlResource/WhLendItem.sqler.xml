<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhLendItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_lend_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND wh_lend_id = :wh_lend_id]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND price = :price]>
        <[AND amount = :amount]>
        <[AND name = :name]>
        <[AND stock_id = :stock_id]>
        <[AND wh_lend_id in (:WhLendIdList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhLendItemList" >
    <content >
      <![CDATA[
        SELECT id, wh_lend_id, sku, quantity, price, amount, name,is_exp_sku,batch_no_json,pick_quantity,status, stock_id
        ,(SELECT location_number FROM wh_stock WHERE id = wh_lend_item.stock_id) AS location_number
        FROM wh_lend_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND wh_lend_id = :wh_lend_id]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND price = :price]>
        <[AND amount = :amount]>
        <[AND name = :name]>
        <[AND stock_id = :stock_id]>
        <[AND wh_lend_id in (:WhLendIdList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhLendItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, wh_lend_id, sku, quantity, price, amount, name,is_exp_sku,batch_no_json,pick_quantity,status,stock_id
        ,(SELECT location_number FROM wh_stock WHERE id = wh_lend_item.stock_id) AS location_number
        FROM wh_lend_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhLendItem" >
    <content >
      <![CDATA[
        SELECT id, wh_lend_id, sku, quantity, price, amount, name,is_exp_sku,batch_no_json,pick_quantity,status,stock_id
        ,(SELECT location_number FROM wh_stock WHERE id = wh_lend_item.stock_id) AS location_number
        FROM wh_lend_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND wh_lend_id = :wh_lend_id]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND price = :price]>
        <[AND amount = :amount]>
        <[AND name = :name]>
        <[AND stock_id = :stock_id]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhLendItem" >
    <content >
      <![CDATA[
        INSERT INTO wh_lend_item (wh_lend_id, sku, quantity, price, amount, name,is_exp_sku,batch_no_json,stock_id)
        VALUES (:wh_lend_id, :sku, :quantity, :price, :amount, :name,:is_exp_sku,:batch_no_json,:stock_id)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhLendItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_lend_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhLendItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_lend_item
        SET <[wh_lend_id = :wh_lend_id,]>
          <[sku = :sku,]>
          <[quantity = :quantity,]>
          <[pick_quantity = :pick_quantity,]>
          <[price = :price,]>
          <[amount = :amount,]>
          <[name = :name,]>
          <[status = :status,]>
          <[stock_id = :stock_id,]>
          <[batch_no_json = :batch_no_json,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>