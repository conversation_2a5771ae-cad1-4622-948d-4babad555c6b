<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhCheckInItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_check_in_item
        WHERE 1 = 1
        <[AND item_id = :item_id]>
        <[AND in_id = :in_id]>
        <[AND sku_id = :sku_id]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND up_quantity = :up_quantity]>
        <[AND qc_quantity = :qc_quantity]>
        <[AND purchase_price = :purchase_price]>
        <[AND comment = :comment]>
        <[AND sku_id IN (SELECT id FROM wh_stock WHERE location_number = :location]>
        <[AND qc_num = :qc_num]>
        <[AND check_in_sku_flags = :check_in_sku_flags]>
        <[AND first_order_type = :first_order_type]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhCheckInItemList" >
    <content >
      <![CDATA[
        SELECT item_id, in_id, sku_id, sku, quantity, up_quantity, qc_quantity, purchase_price, comment,pick_qty
        ,CASE WHEN i.sku_id IS NULL THEN i.location ELSE ( SELECT location_number FROM wh_stock WHERE id = i.sku_id ) END AS location
        , qc_num,purchase_quantity, check_in_sku_flags, first_order_type, pac_up_num, check_in_package_attr
        FROM wh_check_in_item i
        WHERE 1 = 1
        <[AND item_id = :item_id]>
        <[AND in_id = :in_id]>
        <[AND sku_id = :sku_id]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND up_quantity = :up_quantity]>
        <[AND qc_quantity = :qc_quantity]>
        <[AND purchase_price = :purchase_price]>
        <[AND comment = :comment]>
        <[AND sku_id IN (SELECT id FROM wh_stock WHERE location_number = :location]>
        <[AND qc_num = :qc_num]>
        <[AND check_in_sku_flags = :check_in_sku_flags]>
        <[AND first_order_type = :first_order_type]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhCheckInItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT item_id, in_id, sku_id, sku, quantity, up_quantity, qc_quantity, purchase_price, comment,pick_qty
        , CASE WHEN i.sku_id IS NULL THEN i.location ELSE ( SELECT location_number FROM wh_stock WHERE id = i.sku_id ) END AS location, qc_num,
        purchase_quantity, check_in_sku_flags, first_order_type, pac_up_num, check_in_package_attr
        FROM wh_check_in_item i
        WHERE 1 = 1
        AND item_id = :item_id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhCheckInItem" >
    <content >
      <![CDATA[
        SELECT item_id, in_id, sku_id, sku, quantity, up_quantity, qc_quantity, purchase_price, comment,pick_qty
        , CASE WHEN i.sku_id IS NULL THEN i.location ELSE ( SELECT location_number FROM wh_stock WHERE id = i.sku_id ) END AS location, qc_num,
        purchase_quantity, check_in_sku_flags, first_order_type, pac_up_num, check_in_package_attr
        FROM wh_check_in_item i
        WHERE 1 = 1
        <[AND item_id = :item_id]>
        <[AND in_id = :in_id]>
        <[AND sku_id = :sku_id]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND up_quantity = :up_quantity]>
        <[AND qc_quantity = :qc_quantity]>
        <[AND purchase_price = :purchase_price]>
        <[AND comment = :comment]>
        <[AND sku_id IN (SELECT id FROM wh_stock WHERE location_number = :location]>
        <[AND qc_num = :qc_num]>
        <[AND check_in_sku_flags = :check_in_sku_flags]>
        <[AND first_order_type = :first_order_type]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhCheckInItem" >
    <content >
      <![CDATA[
        INSERT INTO wh_check_in_item (in_id,pick_qty, sku_id, sku, quantity, pac_up_num, up_quantity, qc_quantity, purchase_price, comment, location, qc_num, purchase_quantity, check_in_sku_flags, first_order_type, check_in_process, check_in_package_attr)
        VALUES (:in_id,:pick_qty, :sku_id, :sku, :quantity, :pac_up_num, :up_quantity, :qc_quantity, :purchase_price, :comment, :location, :qc_num, :purchase_quantity, :check_in_sku_flags, :first_order_type, :check_in_process, :check_in_package_attr)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhCheckInItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_check_in_item
        WHERE 1 = 1
        AND item_id = :item_id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhCheckInItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_check_in_item
        SET <[in_id = :in_id,]>
          <[sku_id = :sku_id,]>
          <[sku = :sku,]>
          <[quantity = :quantity,]>
          <[up_quantity = :up_quantity,]>
          <[pac_up_num = :pac_up_num,]>
          <[qc_quantity = :qc_quantity,]>
          <[purchase_price = :purchase_price,]>
          <[comment = :comment,]>
          <[location = :location,]>
          <[qc_num = :qc_num,]>
          <[purchase_quantity = :purchase_quantity,]>
          <[check_in_sku_flags = :check_in_sku_flags,]>
          <[first_order_type = :first_order_type,]>
          <[check_in_package_attr = :check_in_package_attr,]>
          <[pick_qty = :pick_qty,]>
        item_id = item_id
        WHERE 1 = 1
        AND item_id = :item_id
      ]]>
    </content>
  </sql>
</sqlmap>