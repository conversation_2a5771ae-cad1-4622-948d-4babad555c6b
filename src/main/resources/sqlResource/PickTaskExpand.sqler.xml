<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryPickTaskExpandCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM pick_task_expand
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_no = :task_no]>
        <[AND task_id = :task_id]>
        <[AND grid_status = :grid_status]>
        <[AND grid_date = :grid_date]>
        <[AND grid_by = :grid_by]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPickTaskExpandList" >
    <content >
      <![CDATA[
        SELECT id, task_no, task_id, grid_status, grid_date, grid_by, creation_date, create_by
        FROM pick_task_expand
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_no = :task_no]>
        <[AND task_id = :task_id]>
        <[AND grid_status = :grid_status]>
        <[AND grid_date = :grid_date]>
        <[AND grid_by = :grid_by]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPickTaskExpandByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, task_no, task_id, grid_status, grid_date, grid_by, creation_date, create_by
        FROM pick_task_expand
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPickTaskExpand" >
    <content >
      <![CDATA[
        SELECT id, task_no, task_id, grid_status, grid_date, grid_by, creation_date, create_by
        FROM pick_task_expand
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_no = :task_no]>
        <[AND task_id = :task_id]>
        <[AND grid_status = :grid_status]>
        <[AND grid_date = :grid_date]>
        <[AND grid_by = :grid_by]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createPickTaskExpand" >
    <content >
      <![CDATA[
        INSERT INTO pick_task_expand (task_no, task_id, grid_status, grid_date, grid_by, creation_date, create_by,yc_box_no
          )
        VALUES (:task_no, :task_id, :grid_status, :grid_date, :grid_by, :creation_date, :create_by,:yc_box_no
          )
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deletePickTaskExpandByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM pick_task_expand
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updatePickTaskExpandByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE pick_task_expand
        SET <[task_no = :task_no,]>
          <[task_id = :task_id,]>
          <[grid_status = :grid_status,]>
          <[grid_date = :grid_date,]>
          <[grid_by = :grid_by,]>
          <[creation_date = :creation_date,]>
          <[create_by = :create_by,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updatePickTaskExpandStatusByTaskNo" >
    <content >
      <![CDATA[
        UPDATE pick_task_expand
        SET <[grid_status = :grid_status,]>
          <[grid_date = :grid_date,]>
          <[grid_by = :grid_by,]>
        id = id
        WHERE 1 = 1
        AND task_no = :task_no
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="updatePickTaskExpandBoxUnBind" >
    <content >
      <![CDATA[
        UPDATE pick_task_expand
        SET yc_box_no = NULL
        WHERE 1 = 1
        AND task_no = :task_no
      ]]>
    </content>
  </sql>
</sqlmap>