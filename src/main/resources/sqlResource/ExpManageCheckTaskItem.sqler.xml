<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryExpManageCheckTaskItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM exp_manage_check_task_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND relation_id = :relation_id]>
        <[AND batch_no = :batch_no]>
        <[AND quantity = :quantity]>
        <[AND check_quantity = :check_quantity]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryExpManageCheckTaskItemList" >
    <content >
      <![CDATA[
        SELECT id, relation_id, batch_no,  quantity, check_quantity
        FROM exp_manage_check_task_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND relation_id = :relation_id]>
        <[AND batch_no = :batch_no]>
        <[AND quantity = :quantity]>
        <[AND check_quantity = :check_quantity]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryExpManageCheckTaskItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, relation_id, batch_no,   quantity, check_quantity
        FROM exp_manage_check_task_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryExpManageCheckTaskItem" >
    <content >
      <![CDATA[
        SELECT id, relation_id, batch_no,  quantity, check_quantity
        FROM exp_manage_check_task_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND relation_id = :relation_id]>
        <[AND batch_no = :batch_no]>
        <[AND quantity = :quantity]>
        <[AND check_quantity = :check_quantity]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createExpManageCheckTaskItem" >
    <content >
      <![CDATA[
        INSERT INTO exp_manage_check_task_item (relation_id, batch_no, quantity, check_quantity)
        VALUES (:relation_id, :batch_no,   :quantity, :check_quantity)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteExpManageCheckTaskItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM exp_manage_check_task_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateExpManageCheckTaskItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE exp_manage_check_task_item
        SET <[relation_id = :relation_id,]>
          <[batch_no = :batch_no,]>
          <[quantity = :quantity,]>
          <[check_quantity = :check_quantity,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>