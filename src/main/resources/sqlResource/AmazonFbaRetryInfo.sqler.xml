<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap>
    <sql datasource="dataSource" id="queryAmazonFbaRetryInfoCount">
        <content>
            <![CDATA[
        SELECT COUNT(1)
        FROM amazon_fba_retry_info
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND account_number = :account_number]>
        <[AND account_detail = :account_detail]>
        <[AND status = :status]>
        <[AND create_time >= :create_time]>
        <[AND report_type = :report_type]>
        <[AND sync_date = :sync_date]>
        <[AND id IN (:ids)]>
        <[AND system_account like :system_account]>
        <[AND report_type_des like :report_type_des]>
        <[AND request_result = :request_result]>
        <[AND merchant_id = :merchant_id]>
        <[AND request_time >= :startRequestTime]>
        <[AND request_time <= :endRequestTime]>
        <[:QUERY_MATCH_ITEM]>
        <[:LESS_RETRY_COUNT]>
        order by request_time desc
        ]]>
        </content>
    </sql>

    <sql datasource="dataSource" id="queryAmazonFbaRetryInfoList">
        <content>
        <![CDATA[
        SELECT id, account_number, account_detail, status, create_time, report_type, sync_date , start_time ,end_time
        ,merchant_id , system_account, report_type_des, sync_range, sync_frequency, request_result, error_detail, retry_count,
        request_time, finish_time, retry_id , progress_id
        FROM amazon_fba_retry_info
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND account_number = :account_number]>
        <[AND account_detail = :account_detail]>
        <[AND status = :status]>
        <[AND create_time >= :create_time]>
        <[AND report_type = :report_type]>
        <[AND sync_date = :sync_date]>
        <[AND id IN (:ids)]>
        <[AND system_account like :system_account]>
        <[AND report_type_des like :report_type_des]>
        <[AND request_result = :request_result]>
        <[AND merchant_id = :merchant_id]>
        <[AND request_time >= :startRequestTime]>
        <[AND request_time <= :endRequestTime]>
        <[AND account_number = :account_number]>
        <[AND report_type = :report_type]>
        <[:QUERY_MATCH_ITEM]>
        <[:LESS_RETRY_COUNT]>
        order by request_time desc
        ]]>
        </content>
    </sql>

    <sql datasource="dataSource" id="queryAmazonFbaRetryInfoByPrimaryKey">
        <content>
            <![CDATA[
        SELECT id, account_number, account_detail, status, create_time, report_type, sync_date , start_time ,end_time
        ,merchant_id , system_account, report_type_des, sync_range, sync_frequency, request_result, error_detail
        FROM amazon_fba_retry_info
        WHERE 1 = 1
        AND id = :id
      ]]>
        </content>
    </sql>

    <sql datasource="dataSource" id="queryAmazonFbaRetryInfo">
        <content>
            <![CDATA[
       SELECT id, account_number, account_detail, status, create_time, report_type, sync_date , start_time ,end_time
        ,merchant_id , system_account, report_type_des, sync_range, sync_frequency, request_result, error_detail ,retry_count, request_time,finish_time,retry_id ,progress_id
        FROM amazon_fba_retry_info
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND account_number = :account_number]>
        <[AND account_detail = :account_detail]>
        <[AND status = :status]>
        <[AND create_time = :create_time]>
        <[AND report_type = :report_type]>
        <[AND sync_date = :sync_date]>
        <[AND retry_id = :retry_id]>
        <[AND progress_id = :progress_id]>
      ]]>
        </content>
    </sql>

    <sql datasource="dataSource" id="createAmazonFbaRetryInfo">
        <content>
            <![CDATA[
        INSERT INTO amazon_fba_retry_info (account_number, account_detail, status, create_time, report_type,
        sync_date, start_time, end_time, merchant_id, system_account, report_type_des, sync_range, sync_frequency,
        request_result, error_detail, retry_count, request_time, finish_time , retry_id , progress_id
          )
        VALUES (:account_number, :account_detail, :status, :create_time, :report_type, :sync_date, :start_time, :end_time
        ,:merchant_id ,:system_account, :report_type_des, :sync_range, :sync_frequency, :request_result, :error_detail, :retry_count
        ,:request_time,:finish_time,:retry_id,:progress_id
          )
      ]]>
        </content>
    </sql>

    <sql datasource="dataSource" id="deleteAmazonFbaRetryInfoByPrimaryKey">
        <content>
            <![CDATA[
        DELETE FROM amazon_fba_retry_info
        WHERE 1 = 1
        AND id = :id
      ]]>
        </content>
    </sql>

    <sql datasource="dataSource" id="updateAmazonFbaRetryInfoByPrimaryKey">
        <content>
            <![CDATA[
        UPDATE amazon_fba_retry_info
        SET <[account_number = :account_number,]>
          <[account_detail = :account_detail,]>
          <[status = :status,]>
          <[create_time = :create_time,]>
          <[report_type = :report_type,]>
          <[sync_date = :sync_date,]>
          <[finish_time = :finish_time,]>
          <[request_result = :request_result,]>
          <[error_detail = :error_detail,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
        </content>
    </sql>


    <sql datasource="dataSource" id="updateAmazonFbaRetryInfoStatus">
        <content>
            <![CDATA[
        UPDATE amazon_fba_retry_info
         SET <[status = :status,]>
          <[update_time = :update_time,]>
          <[request_result = :request_result,]>
          <[update_time = :update_time,]>
          <[retry_count = retry_count + 1]>
        WHERE 1 = 1
        AND id = :id
      ]]>
        </content>
    </sql>

    <sql datasource="dataSource" id="saveOrUpdateAmazonFbaRetryInfo">
        <content>
            <![CDATA[
       INSERT INTO amazon_fba_retry_info (account_number, account_detail, status, create_time, report_type, sync_date , start_time , end_time)
        VALUES
        <[:INSERT_SQL]>
        ON DUPLICATE KEY UPDATE
        <[:UPDATE_SQL]>
      ]]>
        </content>
    </sql>
</sqlmap>