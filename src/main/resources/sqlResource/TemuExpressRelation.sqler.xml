<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryTemuExpressRelationCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM temu_express_relation
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND delivery_order_sn = :delivery_order_sn]>
        <[AND express_batch_sn = :express_batch_sn]>
        <[AND creation_date = :creation_date]>
        <[AND delivery_order_sn IN (:orderList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTemuExpressRelationList" >
    <content >
      <![CDATA[
        SELECT id, delivery_order_sn, express_batch_sn, creation_date
        FROM temu_express_relation
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND delivery_order_sn = :delivery_order_sn]>
        <[AND express_batch_sn = :express_batch_sn]>
        <[AND creation_date = :creation_date]>
        <[AND delivery_order_sn IN (:orderList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTemuExpressRelationByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, delivery_order_sn, express_batch_sn, creation_date
        FROM temu_express_relation
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTemuExpressRelation" >
    <content >
      <![CDATA[
        SELECT id, delivery_order_sn, express_batch_sn, creation_date
        FROM temu_express_relation
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND delivery_order_sn = :delivery_order_sn]>
        <[AND express_batch_sn = :express_batch_sn]>
        <[AND creation_date = :creation_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createTemuExpressRelation" >
    <content >
      <![CDATA[
        INSERT INTO temu_express_relation (delivery_order_sn, express_batch_sn, creation_date)
        VALUES (:delivery_order_sn, :express_batch_sn, :creation_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteTemuExpressRelationByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM temu_express_relation
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateTemuExpressRelationByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE temu_express_relation
        SET <[delivery_order_sn = :delivery_order_sn,]>
          <[express_batch_sn = :express_batch_sn,]>
          <[creation_date = :creation_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>