<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryAmazonFbaFinishOrderDataCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM amazon_fba_finish_order_data
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND account_number = :account_number]>
        <[AND shipment_date = :shipment_date]>
        <[AND sku = :sku]>
        <[AND fnsku = :fnsku]>
        <[AND asin = :asin]>
        <[AND fulfillment_center_id = :fulfillment_center_id]>
        <[AND quantity = :quantity]>
        <[AND amazon_order_id = :amazon_order_id]>
        <[AND currency = :currency]>
        <[AND item_price_per_unit = :item_price_per_unit]>
        <[AND shipping_price = :shipping_price]>
        <[AND gift_wrap_price = :gift_wrap_price]>
        <[AND ship_city = :ship_city]>
        <[AND ship_state = :ship_state]>
        <[AND ship_postal_code = :ship_postal_code]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_user = :last_update_user]>
        <[AND last_update_date = :last_update_date]>
        <[AND market_place = :market_place]>
        <[AND merchant_id = :merchant_id]>
        <[AND hash_code_str = :hash_code_str]>
        <[AND hash_code_str IN (:hs_code_list)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAmazonFbaFinishOrderDataList" >
    <content >
      <![CDATA[
        SELECT id, account_number, shipment_date, sku, fnsku, asin, fulfillment_center_id, 
        quantity, amazon_order_id, currency, item_price_per_unit, shipping_price, gift_wrap_price, 
        ship_city, ship_state, ship_postal_code, created_by, creation_date, last_update_user, 
        last_update_date, market_place, merchant_id, hash_code_str
        FROM amazon_fba_finish_order_data
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND account_number = :account_number]>
        <[AND shipment_date = :shipment_date]>
        <[AND sku = :sku]>
        <[AND fnsku = :fnsku]>
        <[AND asin = :asin]>
        <[AND fulfillment_center_id = :fulfillment_center_id]>
        <[AND quantity = :quantity]>
        <[AND amazon_order_id = :amazon_order_id]>
        <[AND currency = :currency]>
        <[AND item_price_per_unit = :item_price_per_unit]>
        <[AND shipping_price = :shipping_price]>
        <[AND gift_wrap_price = :gift_wrap_price]>
        <[AND ship_city = :ship_city]>
        <[AND ship_state = :ship_state]>
        <[AND ship_postal_code = :ship_postal_code]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_user = :last_update_user]>
        <[AND last_update_date = :last_update_date]>
        <[AND market_place = :market_place]>
        <[AND merchant_id = :merchant_id]>
        <[AND hash_code_str = :hash_code_str]>
        <[AND hash_code_str IN (:hs_code_list)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAmazonFbaFinishOrderDataByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, account_number, shipment_date, sku, fnsku, asin, fulfillment_center_id, 
        quantity, amazon_order_id, currency, item_price_per_unit, shipping_price, gift_wrap_price, 
        ship_city, ship_state, ship_postal_code, created_by, creation_date, last_update_user, 
        last_update_date, market_place, merchant_id, hash_code_str
        FROM amazon_fba_finish_order_data
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAmazonFbaFinishOrderData" >
    <content >
      <![CDATA[
        SELECT id, account_number, shipment_date, sku, fnsku, asin, fulfillment_center_id, 
        quantity, amazon_order_id, currency, item_price_per_unit, shipping_price, gift_wrap_price, 
        ship_city, ship_state, ship_postal_code, created_by, creation_date, last_update_user, 
        last_update_date, market_place, merchant_id, hash_code_str
        FROM amazon_fba_finish_order_data
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND account_number = :account_number]>
        <[AND shipment_date = :shipment_date]>
        <[AND sku = :sku]>
        <[AND fnsku = :fnsku]>
        <[AND asin = :asin]>
        <[AND fulfillment_center_id = :fulfillment_center_id]>
        <[AND quantity = :quantity]>
        <[AND amazon_order_id = :amazon_order_id]>
        <[AND currency = :currency]>
        <[AND item_price_per_unit = :item_price_per_unit]>
        <[AND shipping_price = :shipping_price]>
        <[AND gift_wrap_price = :gift_wrap_price]>
        <[AND ship_city = :ship_city]>
        <[AND ship_state = :ship_state]>
        <[AND ship_postal_code = :ship_postal_code]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_user = :last_update_user]>
        <[AND last_update_date = :last_update_date]>
        <[AND market_place = :market_place]>
        <[AND merchant_id = :merchant_id]>
        <[AND hash_code_str = :hash_code_str]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createAmazonFbaFinishOrderData" >
    <content >
      <![CDATA[
        INSERT INTO amazon_fba_finish_order_data (account_number, shipment_date, sku, fnsku, asin, fulfillment_center_id, 
          quantity, amazon_order_id, currency, item_price_per_unit, shipping_price, hash_code_str,
          gift_wrap_price, ship_city, ship_state, ship_postal_code, created_by, creation_date, 
          last_update_user, last_update_date, market_place, merchant_id)
        VALUES (:account_number, :shipment_date, :sku, :fnsku, :asin, :fulfillment_center_id, 
          :quantity, :amazon_order_id, :currency, :item_price_per_unit, :shipping_price, :hash_code_str,
          :gift_wrap_price, :ship_city, :ship_state, :ship_postal_code, :created_by, :creation_date, 
          :last_update_user, :last_update_date, :market_place, :merchant_id)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteAmazonFbaFinishOrderDataByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM amazon_fba_finish_order_data
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateAmazonFbaFinishOrderDataByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE amazon_fba_finish_order_data
        SET <[account_number = :account_number,]>
          <[shipment_date = :shipment_date,]>
          <[sku = :sku,]>
          <[fnsku = :fnsku,]>
          <[asin = :asin,]>
          <[fulfillment_center_id = :fulfillment_center_id,]>
          <[quantity = :quantity,]>
          <[amazon_order_id = :amazon_order_id,]>
          <[currency = :currency,]>
          <[item_price_per_unit = :item_price_per_unit,]>
          <[shipping_price = :shipping_price,]>
          <[gift_wrap_price = :gift_wrap_price,]>
          <[ship_city = :ship_city,]>
          <[ship_state = :ship_state,]>
          <[ship_postal_code = :ship_postal_code,]>
          <[created_by = :created_by,]>
          <[creation_date = :creation_date,]>
          <[last_update_user = :last_update_user,]>
          <[last_update_date = :last_update_date,]>
          <[market_place = :market_place,]>
          <[merchant_id = :merchant_id,]>
          <[hash_code_str = :hash_code_str,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>