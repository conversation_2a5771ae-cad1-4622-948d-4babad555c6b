<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  
  
  
  <!-- 按货打单拣货单 -->
   <sql datasource="dataSource" id="queryWhApvPickingList" >
    <content >
      <![CDATA[
       	SELECT sku.warehouse_id, apv_item.sku, sku.location_number, sku.name, SUM(sale_quantity) AS picking_quantity
       
        FROM wh_apv apv
        LEFT JOIN wh_apv_lock apv_lock ON apv.id = apv_lock.apv_id
        LEFT JOIN wh_apv_item apv_item ON apv.id = apv_item.apv_id
		LEFT JOIN wh_sku sku ON apv_item.sku = sku.sku 
         
        WHERE 1 = 1
   		<[AND apv.id = :id]>
   		<[AND apv.id IN (:apv_id_list)]>
        <[AND apv.apv_no = :apv_no]>
        <[AND apv.platform = :platform]>
        <[AND apv.platform IN (:platform_list)]>
        <[AND apv.platform_order_id = :platform_order_id]>
        <[AND apv.original_order_id = :original_order_id]>
        <[AND apv.original_order_id >= :then_hz]>
        <[AND apv.original_order_id <= :less_hz]>
        <[AND apv.copy_type = :copy_type]>
        <[AND apv.resend_type = :resend_type]>
        <[AND apv.split_type = :split_type]>
        <[AND apv.order_type = :order_type]>
        <[AND apv.order_status = :order_status]>
        <[AND apv.total_currency = :total_currency]>
        <[AND apv.total_price = :total_price]>
        <[AND apv.fee_or_credit = :fee_or_credit]>
        <[AND apv.sale_date = :sale_date]>
        <[AND apv.paid_date = :paid_date]>
        <[AND apv.print_date = :print_date]>
        <[AND apv.deliver_date = :deliver_date]>
        <[AND apv.last_modified_time = :last_modified_time]>
        <[AND apv.seller_id = :seller_id]>
        <[AND apv.seller_email = :seller_email]>
        <[AND apv.seller_pay = :seller_pay]>
        <[AND apv.buyer_id = :buyer_id]>
        <[AND apv.buyer_name = :buyer_name]>
        <[AND apv.buyer_mobile = :buyer_mobile]>
        <[AND apv.buyer_tel = :buyer_tel]>
        <[AND apv.buyer_country_code = :buyer_country_code]>
        <[AND apv.buyer_country = :buyer_country]>
        <[AND apv.buyer_postal_code = :buyer_postal_code]>
        <[AND apv.buyer_street = :buyer_street]>
        <[AND apv.buyer_alley = :buyer_alley]>
        <[AND apv.buyer_city = :buyer_city]>
        <[AND apv.buyer_state_or_province = :buyer_state_or_province]>
        <[AND apv.buyer_email = :buyer_email]>
        <[AND apv.buyer_checkout = :buyer_checkout]>
        <[AND apv.third_party_transaction_id = :third_party_transaction_id]>
        <[AND apv.payment_status = :payment_status]>
        <[AND apv.is_payment = :is_payment]>
        <[AND apv.is_refund = :is_refund]>
        <[AND apv.complete_status = :complete_status]>
        <[AND apv.sales_record_number = :sales_record_number]>
        <[AND apv.logistics_company = :logistics_company]>
        <[AND apv.logistics_company IN (:logistics_company_list)]>
        <[AND apv.id NOT IN (select inner_apv.id from wh_apv inner_apv  where inner_apv.logistics_company IN (:not_in_logistics_company_list))]>
        <[AND apv.ship_service = :ship_service]>
        <[AND apv.ship_freight = :ship_freight]>
        <[AND apv.ship_status = :ship_status]>
        <[AND apv.ship_freight_cost = :ship_freight_cost]>
        <[AND apv.is_shipment = :is_shipment]>
        <[AND apv.shipment_time = :shipment_time]>
        <[AND apv.logistics_type = :logistics_type]>
        <[AND apv.tracking_number = :tracking_number]>
        <[AND apv.service_provider_no = :service_provider_no]>
        <[AND apv.sign_deliver_goods = :sign_deliver_goods]>
        <[AND apv.sign_out_treasury = :sign_out_treasury]>
        <[AND apv.sign_transport = :sign_transport]>
        <[AND apv.sign_contact_buyers = :sign_contact_buyers]>
        <[AND apv.sign_customer_reviews = :sign_customer_reviews]>
        <[AND apv.sign_payment = :sign_payment]>
        <[AND apv.sign_refund = :sign_refund]>
        <[AND apv.sign_press_money = :sign_press_money]>
        <[AND apv.sign_distribution_goods = :sign_distribution_goods]>
        <[AND apv.actual_weight = :actual_weight]>
        <[AND apv.system_price = :system_price]>
        <[AND apv.status = :status]>
        <[AND apv.extends_status = :extends_status]>
        <[AND apv.creation_date = :creation_date]>
        <[AND apv.creation_date >= :from_create_date]>
	    <[AND apv.creation_date <= :to_create_date]>
        <[AND apv.created_by = :created_by]>
        <[AND apv.last_update_date = :last_update_date]>
        <[AND apv.last_updated_by = :last_updated_by]>
        <[AND apv.apv_desc = :apv_desc]>
        <[:lock_status]>
        
        <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.apv_id = apv.id GROUP BY inner_item.apv_id HAVING SUM(inner_item.sale_quantity) = :single_order_item)]>
	    <[AND apv.id NOT IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.apv_id = apv.id GROUP BY inner_item.apv_id HAVING SUM(inner_item.sale_quantity) = :multi_order_item)]>
	    
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku where inner_sku.warehouse_id IN (:warehouse_id_list))]>
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku where inner_sku.warehouse_id = :warehouse_id)]>
        
        <[AND apv.id IN (SELECT inner_item.apv_id FROM wh_apv_item inner_item WHERE inner_item.apv_id = apv.id AND inner_item.sku = :sku)]>
        
        <[AND apv.id IN (SELECT inner_item.apv_id FROM wh_apv_item inner_item WHERE inner_item.apv_id = apv.id AND inner_item.sku LIKE :like_sku)]>
        <[AND apv.id IN (SELECT inner_item.apv_id FROM wh_apv_item inner_item WHERE inner_item.apv_id = apv.id AND inner_item.sku IN (:sku_list) )]>
        
        <[AND apv.id IN (SELECT task_item.apv_id from wh_picking_task_item task_item 
	    				LEFT JOIN wh_picking_task task ON task.id = task_item.task_id
						LEFT JOIN wh_box box ON box.relation_no = task.id
						where box.box_no = :box_no )
	    
	    ]>
        
        <[:is_exist_tracking_number]>
        
        <[:allot_task]>
        
        GROUP BY apv_item.sku
        ORDER BY sku.location_number, apv_item.sku, picking_quantity DESC
      ]]>
    </content>
  </sql>
  
  
  
  
  <!--   按货打单 返回id -->
  <sql datasource="dataSource" id="queryGoodsPrintWhApvIdList" >
    <content >
      <![CDATA[
        SELECT 
        	apv.id
        
        FROM wh_apv apv
        LEFT JOIN wh_apv_lock apv_lock ON apv.id = apv_lock.apv_id
        LEFT JOIN wh_apv_item apv_item ON apv.id = apv_item.apv_id
		LEFT JOIN wh_sku sku ON apv_item.sku = sku.sku 
		LEFT JOIN wh_stock stock ON stock.sku = sku.sku
		
        WHERE 1 = 1 
        <[AND apv.id = :id]>
        <[AND apv.id IN (:apv_id_list)]>
        <[AND apv.apv_no = :apv_no]>
        <[AND apv.platform = :platform]>
        <[AND apv.platform IN (:platform_list)]>
        <[AND apv.platform_order_id = :platform_order_id]>
        <[AND apv.original_order_id = :original_order_id]>
        <[AND apv.original_order_id >= :then_hz]>
        <[AND apv.original_order_id <= :less_hz]>
        <[AND apv.copy_type = :copy_type]>
        <[AND apv.resend_type = :resend_type]>
        <[AND apv.split_type = :split_type]>
        <[AND apv.order_type = :order_type]>
        <[AND apv.order_status = :order_status]>
        <[AND apv.total_currency = :total_currency]>
        <[AND apv.total_price = :total_price]>
        <[AND apv.fee_or_credit = :fee_or_credit]>
        <[AND apv.sale_date = :sale_date]>
        <[AND apv.paid_date = :paid_date]>
        <[AND apv.print_date = :print_date]>
        <[AND apv.deliver_date = :deliver_date]>
        <[AND apv.last_modified_time = :last_modified_time]>
        <[AND apv.seller_id = :seller_id]>
        <[AND apv.seller_email = :seller_email]>
        <[AND apv.seller_pay = :seller_pay]>
        <[AND apv.buyer_id = :buyer_id]>
        <[AND apv.buyer_name = :buyer_name]>
        <[AND apv.buyer_mobile = :buyer_mobile]>
        <[AND apv.buyer_tel = :buyer_tel]>
        <[AND apv.buyer_country_code = :buyer_country_code]>
        <[AND apv.buyer_country = :buyer_country]>
        <[AND apv.buyer_postal_code = :buyer_postal_code]>
        <[AND apv.buyer_street = :buyer_street]>
        <[AND apv.buyer_alley = :buyer_alley]>
        <[AND apv.buyer_city = :buyer_city]>
        <[AND apv.buyer_state_or_province = :buyer_state_or_province]>
        <[AND apv.buyer_email = :buyer_email]>
        <[AND apv.buyer_checkout = :buyer_checkout]>
        <[AND apv.third_party_transaction_id = :third_party_transaction_id]>
        <[AND apv.payment_status = :payment_status]>
        <[AND apv.is_payment = :is_payment]>
        <[AND apv.is_refund = :is_refund]>
        <[AND apv.complete_status = :complete_status]>
        <[AND apv.sales_record_number = :sales_record_number]>
        <[AND apv.logistics_company = :logistics_company]>
        <[AND apv.logistics_company IN (:logistics_company_list)]>
        <[AND apv.id NOT IN (select inner_apv.id from wh_apv inner_apv  where inner_apv.logistics_company IN (:not_in_logistics_company_list))]>
        <[AND apv.ship_service = :ship_service]>
        <[AND apv.ship_freight = :ship_freight]>
        <[AND apv.ship_status = :ship_status]>
        <[AND apv.ship_status IN (:ship_status_list)]>
        <[AND apv.ship_freight_cost = :ship_freight_cost]>
        <[AND apv.is_shipment = :is_shipment]>
        <[AND apv.shipment_time = :shipment_time]>
        <[AND apv.logistics_type = :logistics_type]>
        <[AND apv.tracking_number = :tracking_number]>
        <[AND apv.service_provider_no = :service_provider_no]>
        <[AND apv.sign_deliver_goods = :sign_deliver_goods]>
        <[AND apv.sign_out_treasury = :sign_out_treasury]>
        <[AND apv.sign_transport = :sign_transport]>
        <[AND apv.sign_contact_buyers = :sign_contact_buyers]>
        <[AND apv.sign_customer_reviews = :sign_customer_reviews]>
        <[AND apv.sign_payment = :sign_payment]>
        <[AND apv.sign_refund = :sign_refund]>
        <[AND apv.sign_press_money = :sign_press_money]>
        <[AND apv.sign_distribution_goods = :sign_distribution_goods]>
        <[AND apv.actual_weight = :actual_weight]>
        <[AND apv.system_price = :system_price]>
        <[AND apv.status = :status]>
        <[AND apv.extends_status = :extends_status]>
        <[AND apv.creation_date = :creation_date]>
        <[AND apv.creation_date >= :from_create_date]>
	    <[AND apv.creation_date <= :to_create_date]>
        <[AND apv.created_by = :created_by]>
        <[AND apv.last_update_date = :last_update_date]>
        <[AND apv.last_updated_by = :last_updated_by]>
        <[AND apv.apv_desc = :apv_desc]>
        <[:lock_status]>
        
        <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.apv_id = apv.id GROUP BY inner_item.apv_id HAVING SUM(inner_item.sale_quantity) = :single_order_item)]>
	    <[AND apv.id NOT IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.apv_id = apv.id GROUP BY inner_item.apv_id HAVING SUM(inner_item.sale_quantity) = :multi_order_item)]>
	    
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku where inner_sku.warehouse_id IN (:warehouse_id_list))]>
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku where inner_sku.warehouse_id = :warehouse_id)]>
	    
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku = :sku)]>
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku LIKE :like_sku)]>
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku IN (:sku_list)]>
	    
	    <[AND apv.id IN (SELECT task_item.apv_id from wh_picking_task_item task_item 
	    				LEFT JOIN wh_picking_task task ON task.id = task_item.task_id
						LEFT JOIN wh_box box ON box.relation_no = task.id
						where box.box_no = :box_no )
	    
	    ]>
        
        <[:is_exist_tracking_number]>
        
        <[:allot_task]>
        
        <[:ORDER_BY]>
      ]]>
    </content>
  </sql>
  
  
  <!--   按货打出最优先级的订单 -->
  <sql datasource="dataSource" id="queryMaxPriorityGoodsPrintWhApvList" >
    <content >
      <![CDATA[
        SELECT 
        	apv.id, apv.apv_no, apv.logistics_company, apv.platform, apv.seller_id, apv.logistics_type, apv.status,apv.tracking_number,
        	
        	apv_item.sku, apv_item.sale_quantity, apv.ship_service, apv.sign_refund,apv.buyer_checkout,apv_item.buyer_checkout,apv.ship_status,

		   	CASE WHEN apv_item.is_combination_sku=1 THEN combine_sku.name ELSE sku.name END AS 'sku.name',
            CASE WHEN apv_item.is_combination_sku=1 THEN combine_sku.image_url ELSE sku.image_url END AS 'sku.image_url',
            sku.pack_image,
		   	
		   	(SELECT name FROM wh_packaging_material_management WHERE id= sku.packaging_id) as material,
			(SELECT GROUP_CONCAT(DISTINCT(name)) FROM wh_packaging_material_management WHERE FIND_IN_SET(id,sku.match_materials_code)) as matchMaterials
		   	
        FROM wh_apv apv
        LEFT JOIN wh_apv_lock apv_lock ON apv.id = apv_lock.apv_id
        LEFT JOIN wh_apv_item apv_item ON apv.id = apv_item.apv_id
		LEFT JOIN wh_sku sku ON apv_item.sku = sku.sku
		LEFT JOIN wh_combine_sku combine_sku ON apv_item.sku = combine_sku.spu
		LEFT JOIN wh_stock stock ON stock.sku = apv_item.sku
		LEFT JOIN wh_picking_task_item task_item ON task_item.apv_id = apv.id
		LEFT JOIN wh_picking_task task ON task_item.task_id = task.id
		
        WHERE 1 = 1 
        <[AND apv.id = :id]>
        <[AND apv.id IN (:apv_id_list)]>
        <[AND apv.apv_no = :apv_no]>
        <[AND apv.platform = :platform]>
        <[AND apv.platform IN (:platform_list)]>
        <[AND apv.platform_order_id = :platform_order_id]>
        <[AND apv.original_order_id = :original_order_id]>
        <[AND apv.original_order_id >= :then_hz]>
        <[AND apv.original_order_id <= :less_hz]>
        <[AND apv.status = :status]>
        <[AND apv.status in (:status_list)]>
        <[AND apv.copy_type = :copy_type]>
        <[AND apv.resend_type = :resend_type]>
        <[AND apv.split_type = :split_type]>
        <[AND apv.order_type = :order_type]>
        <[AND apv.order_status = :order_status]>
        <[AND apv.total_currency = :total_currency]>
        <[AND apv.total_price = :total_price]>
        <[AND apv.fee_or_credit = :fee_or_credit]>
        <[AND apv.sale_date = :sale_date]>
        <[AND apv.paid_date = :paid_date]>
        <[AND apv.print_date = :print_date]>
        <[AND apv.deliver_date = :deliver_date]>
        <[AND apv.last_modified_time = :last_modified_time]>
        <[AND apv.seller_id = :seller_id]>
        <[AND apv.seller_email = :seller_email]>
        <[AND apv.seller_pay = :seller_pay]>
        <[AND apv.buyer_id = :buyer_id]>
        <[AND apv.buyer_name = :buyer_name]>
        <[AND apv.buyer_mobile = :buyer_mobile]>
        <[AND apv.buyer_tel = :buyer_tel]>
        <[AND apv.buyer_country_code = :buyer_country_code]>
        <[AND apv.buyer_country = :buyer_country]>
        <[AND apv.buyer_postal_code = :buyer_postal_code]>
        <[AND apv.buyer_street = :buyer_street]>
        <[AND apv.buyer_alley = :buyer_alley]>
        <[AND apv.buyer_city = :buyer_city]>
        <[AND apv.buyer_state_or_province = :buyer_state_or_province]>
        <[AND apv.buyer_email = :buyer_email]>
        <[AND apv.buyer_checkout = :buyer_checkout]>
        <[AND apv.third_party_transaction_id = :third_party_transaction_id]>
        <[AND apv.payment_status = :payment_status]>
        <[AND apv.is_payment = :is_payment]>
        <[AND apv.is_refund = :is_refund]>
        <[AND apv.complete_status = :complete_status]>
        <[AND apv.sales_record_number = :sales_record_number]>
        <[AND apv.logistics_company = :logistics_company]>
        <[AND apv.logistics_company IN (:logistics_company_list)]>
        <[AND apv.logistics_company NOT IN (:not_in_logistics_company_list)]>
        <[AND apv.ship_service = :ship_service]>
        <[AND apv.ship_freight = :ship_freight]>
        <[AND apv.ship_status = :ship_status]>
        <[AND apv.ship_status IN (:ship_status_list)]>
        <[AND apv.ship_status NOT IN (:not_in_apv_deliverytype_list)]>
        
        <[:normal_apv]>
        
        <[AND apv.ship_freight_cost = :ship_freight_cost]>
        <[AND apv.is_shipment = :is_shipment]>
        <[AND apv.shipment_time = :shipment_time]>
        <[AND apv.logistics_type = :logistics_type]>
        <[AND apv.tracking_number = :tracking_number]>
        <[AND apv.service_provider_no = :service_provider_no]>
        <[AND apv.sign_deliver_goods = :sign_deliver_goods]>
        <[AND apv.sign_out_treasury = :sign_out_treasury]>
        <[AND apv.sign_transport = :sign_transport]>
        <[AND apv.sign_contact_buyers = :sign_contact_buyers]>
        <[AND apv.sign_customer_reviews = :sign_customer_reviews]>
        <[AND apv.sign_payment = :sign_payment]>
        <[AND apv.sign_refund = :sign_refund]>
        <[AND apv.sign_press_money = :sign_press_money]>
        <[AND apv.sign_distribution_goods = :sign_distribution_goods]>
        <[AND apv.actual_weight = :actual_weight]>
        <[AND apv.system_price = :system_price]>
        <[AND apv.extends_status = :extends_status]>
        <[AND apv.creation_date = :creation_date]>
        <[AND apv.creation_date >= :from_create_date]>
	    <[AND apv.creation_date <= :to_create_date]>
        <[AND apv.created_by = :created_by]>
        <[AND apv.last_update_date = :last_update_date]>
        <[AND apv.last_updated_by = :last_updated_by]>
        <[AND apv.apv_desc = :apv_desc]>
        <[:lock_status]>
        
        <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.apv_id = apv.id GROUP BY inner_item.apv_id HAVING SUM(inner_item.sale_quantity) = :single_order_item)]>
	    <[AND apv.id NOT IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.apv_id = apv.id GROUP BY inner_item.apv_id HAVING SUM(inner_item.sale_quantity) = :multi_order_item)]>
	    
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku where inner_sku.warehouse_id IN (:warehouse_id_list))]>
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku where inner_sku.warehouse_id = :warehouse_id)]>
	    
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku = :sku)]>
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku LIKE :like_sku)]>
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku IN (:sku_list)]>
	    
	    <[AND apv.id IN (SELECT task_item.apv_id from wh_picking_task_item task_item 
	    				LEFT JOIN wh_picking_task task ON task.id = task_item.task_id
						LEFT JOIN wh_box box ON box.relation_no = task.id
						where box.box_no = :box_no )
	    
	    ]>
	    
	    <[AND task.task_status = :task_status]>
	    
	    <[AND apv.id = (SELECT task_item.apv_id from wh_picking_task_item task_item 
	    				LEFT JOIN wh_picking_task task ON task.id = task_item.task_id
						where task_item.apv_id = apv.id and task.task_type != :not_in_task_type and task.id = (SELECT max(task.id) from wh_picking_task_item task_item 
	    				LEFT JOIN wh_picking_task task ON task.id = task_item.task_id
						where task_item.apv_id = apv.id ))    
	    ]>
	    
	    <[AND apv.id = (SELECT task_item.apv_id from wh_picking_task_item task_item 
	    				LEFT JOIN wh_picking_task task ON task.id = task_item.task_id
						where task_item.apv_id = apv.id and task.is_printing = :is_pick_task_print and task.id = (SELECT max(task.id) from wh_picking_task_item task_item 
	    				LEFT JOIN wh_picking_task task ON task.id = task_item.task_id
						where task_item.apv_id = apv.id ))]>
	    
	    <[AND task.task_no = :task_no]>
        
        <[:is_exist_tracking_number]>
        
        <[:allot_task]>
        <[:EXCLUDE_GPSR]>
        <[:IS_GPSR]>

        <[:ORDER_BY]>
        
        <[:LIMIT_COUNT]>
      ]]>
    </content>
  </sql>
  
  
    <!--   按货打印的记录数 -->
  <sql datasource="dataSource" id="queryGoodsPrintWhApvCount" >
    <content >
      <![CDATA[
        SELECT 
		    COUNT(1) FROM (
		SELECT DISTINCT apv.id
        FROM wh_apv apv
        LEFT JOIN wh_apv_lock apv_lock ON apv.id = apv_lock.apv_id
        LEFT JOIN wh_apv_item apv_item ON apv.id = apv_item.apv_id
		LEFT JOIN wh_sku sku ON apv_item.sku = sku.sku 
        WHERE 1 = 1 
        <[AND apv.id = :id]>
        <[AND apv.id IN (:apv_id_list)]>
        <[AND apv.apv_no = :apv_no]>
        <[AND apv.apv_no IN (:apv_no_list)]>
        <[AND apv.platform = :platform]>
        <[AND apv.platform IN (:platform_list)]>
        <[AND apv.platform_order_id = :platform_order_id]>
        <[AND apv.original_order_id = :original_order_id]>
        <[AND apv.original_order_id >= :then_hz]>
        <[AND apv.original_order_id <= :less_hz]>
        <[AND apv.copy_type = :copy_type]>
        <[AND apv.resend_type = :resend_type]>
        <[AND apv.split_type = :split_type]>
        <[AND apv.order_type = :order_type]>
        <[AND apv.order_status = :order_status]>
        <[AND apv.total_currency = :total_currency]>
        <[AND apv.total_price = :total_price]>
        <[AND apv.fee_or_credit = :fee_or_credit]>
        <[AND apv.sale_date = :sale_date]>
        <[AND apv.paid_date = :paid_date]>
        <[AND apv.print_date = :print_date]>
        <[AND apv.deliver_date = :deliver_date]>
        <[AND apv.last_modified_time = :last_modified_time]>
        <[AND apv.seller_id = :seller_id]>
        <[AND apv.seller_email = :seller_email]>
        <[AND apv.seller_pay = :seller_pay]>
        <[AND apv.buyer_id = :buyer_id]>
        <[AND apv.buyer_name = :buyer_name]>
        <[AND apv.buyer_mobile = :buyer_mobile]>
        <[AND apv.buyer_tel = :buyer_tel]>
        <[AND apv.buyer_country_code = :buyer_country_code]>
        <[AND apv.buyer_country = :buyer_country]>
        <[AND apv.buyer_postal_code = :buyer_postal_code]>
        <[AND apv.buyer_street = :buyer_street]>
        <[AND apv.buyer_alley = :buyer_alley]>
        <[AND apv.buyer_city = :buyer_city]>
        <[AND apv.buyer_state_or_province = :buyer_state_or_province]>
        <[AND apv.buyer_email = :buyer_email]>
        <[AND apv.buyer_checkout = :buyer_checkout]>
        <[AND apv.third_party_transaction_id = :third_party_transaction_id]>
        <[AND apv.payment_status = :payment_status]>
        <[AND apv.is_payment = :is_payment]>
        <[AND apv.is_refund = :is_refund]>
        <[AND apv.complete_status = :complete_status]>
        <[AND apv.sales_record_number = :sales_record_number]>
        <[AND apv.logistics_company = :logistics_company]>
        <[AND apv.logistics_company IN (:logistics_company_list)]>
        <[AND apv.id NOT IN (select inner_apv.id from wh_apv inner_apv  where inner_apv.logistics_company IN (:not_in_logistics_company_list))]>
        <[AND apv.ship_service = :ship_service]>
        <[AND apv.ship_freight = :ship_freight]>
        <[AND apv.ship_status = :ship_status]>
        <[AND apv.ship_status IN (:ship_status_list)]>
        <[AND apv.ship_freight_cost = :ship_freight_cost]>
        <[AND apv.is_shipment = :is_shipment]>
        <[AND apv.shipment_time = :shipment_time]>
        <[AND apv.logistics_type = :logistics_type]>
        <[AND apv.tracking_number = :tracking_number]>
        <[AND apv.service_provider_no = :service_provider_no]>
        <[AND apv.sign_deliver_goods = :sign_deliver_goods]>
        <[AND apv.sign_out_treasury = :sign_out_treasury]>
        <[AND apv.sign_transport = :sign_transport]>
        <[AND apv.sign_contact_buyers = :sign_contact_buyers]>
        <[AND apv.sign_customer_reviews = :sign_customer_reviews]>
        <[AND apv.sign_payment = :sign_payment]>
        <[AND apv.sign_refund = :sign_refund]>
        <[AND apv.sign_press_money = :sign_press_money]>
        <[AND apv.sign_distribution_goods = :sign_distribution_goods]>
        <[AND apv.actual_weight = :actual_weight]>
        <[AND apv.system_price = :system_price]>
        <[AND apv.status = :status]>
        <[AND apv.status in (:status_list)]>
        <[AND apv.extends_status = :extends_status]>
        <[AND apv.creation_date = :creation_date]>
        <[AND apv.creation_date >= :from_create_date]>
	    <[AND apv.creation_date <= :to_create_date]>
        <[AND apv.created_by = :created_by]>
        <[AND apv.last_update_date = :last_update_date]>
        <[AND apv.last_updated_by = :last_updated_by]>
        <[AND apv.apv_desc = :apv_desc]>
        <[:lock_status]>
        
        <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.apv_id = apv.id GROUP BY inner_item.apv_id HAVING SUM(inner_item.sale_quantity) = :single_order_item)]>
	    <[AND apv.id NOT IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.apv_id = apv.id GROUP BY inner_item.apv_id HAVING SUM(inner_item.sale_quantity) = :multi_order_item)]>
	    
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku where inner_sku.warehouse_id IN (:warehouse_id_list))]>
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku where inner_sku.warehouse_id = :warehouse_id)]>
	    
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku = :sku)]>
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku LIKE :like_sku)]>
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku IN (:sku_list)]>
	    
	    <[AND apv.id IN (SELECT task_item.apv_id from wh_picking_task_item task_item 
	    				LEFT JOIN wh_picking_task task ON task.id = task_item.task_id
						LEFT JOIN wh_box box ON box.relation_no = task.id
						where box.box_no = :box_no )
	    
	    ]>
	    
	    <[:SINGLE_WAREHOUSE]>
        <[:MULTI_WAREHOUSE]>
        
        <[:allot_task]>
        
        <[:is_exist_tracking_number]> ) TEMP
      ]]>
    </content>
  </sql>
  
  
  
  <!--   按货打单 -->
  <sql datasource="dataSource" id="searchGoodsPrintWhApvList" >
    <content >
      <![CDATA[
        SELECT 
        	apv.id, apv.apv_no, apv.platform, apv.platform_order_id, apv.original_order_id, apv.copy_type, apv.resend_type, 
	        apv.split_type, apv.apv_type, apv.apv_status, apv.total_currency, apv.total_price, apv.fee_or_credit, 
	        apv.sale_date, apv.paid_date, apv.print_date, apv.deliver_date, apv.last_modified_time, apv.seller_id, apv.seller_email, 
	        apv.seller_pay, apv.buyer_id, apv.buyer_name, apv.buyer_mobile, apv.buyer_tel, apv.buyer_country_code, apv.buyer_country, 
	        apv.buyer_postal_code, apv.buyer_street, apv.buyer_alley, apv.buyer_city, apv.buyer_state_or_province, 
	        apv.buyer_email, apv.buyer_checkout, apv.third_party_transaction_id, apv.payment_status, apv.is_payment, 
	        apv.is_refund, apv.complete_status, apv.sales_record_number, apv.logistics_company, apv.ship_service, 
	        apv.ship_freight, apv.ship_status, apv.ship_freight_cost, apv.is_shipment, apv.shipment_time, apv.logistics_type, 
	        apv.tracking_number, apv.service_provider_no, apv.sign_deliver_goods, apv.sign_out_treasury, apv.sign_transport, 
	        apv.sign_contact_buyers, apv.sign_customer_reviews, apv.sign_payment, apv.sign_refund, apv.sign_press_money, 
	        apv.sign_distribution_goods, apv.actual_weight, apv.system_price, apv.status, apv.extends_status, apv.creation_date, 
	        apv.created_by, apv.last_update_date, apv.last_updated_by, apv.apv_desc,
        	
        	apv_item.id, apv_item.apv_id, apv_item.apv_line_item_id, apv_item.transaction_id, apv_item.site, apv_item.sku_id, apv_item.sku_title, apv_item.sku, 
	        apv_item.salesperson, apv_item.currency, apv_item.sku_price, apv_item.sale_quantity, apv_item.sale_price, apv_item.transaction_cost, apv_item.final_transaction_cost, 
	        apv_item.sales_record_number, apv_item.logistics_tracking_number, apv_item.shipping_carrier_used, apv_item.multi_attr, 
	        apv_item.buyer_checkout, apv_item.created_date, apv_item.last_update_date, apv_item.last_updated_by, apv_item.item_desc, apv_item.salesperson,
        	
        	apv_lock.status,
        	
		   	sku.id, sku.sku, sku.image_url, sku.weight, sku.name, sku.warehouse_id, sku.location_number, sku.floor_location,
		   	
		   	(SELECT name FROM wh_packaging_material_management WHERE id= sku.packaging_id) as material,
			(SELECT GROUP_CONCAT(DISTINCT(name)) FROM wh_packaging_material_management WHERE FIND_IN_SET(id,sku.match_materials_code)) as matchMaterials,
		   	
	   		(stock.qc_quantity+stock.waiting_up_quantity+stock.up_quantity+stock.surplus_quantity+stock.pick_quantity+stock.cancel_quantity+stock.allot_quantity+stock.pick_not_quantity) AS stockQuantity,
	   		 stock.surplus_quantity
		   	
        
        FROM wh_apv apv
        LEFT JOIN wh_apv_lock apv_lock ON apv.id = apv_lock.apv_id
        LEFT JOIN wh_apv_item apv_item ON apv.id = apv_item.apv_id
		LEFT JOIN wh_sku sku ON apv_item.sku = sku.sku 
		LEFT JOIN wh_stock stock ON stock.sku = sku.sku
		INNER JOIN (select id 
			from wh_apv apv
			 WHERE 1 = 1 
	        <[AND apv.id = :id]>
	        <[AND apv.id IN (:apv_id_list)]>
	        <[AND apv.apv_no = :apv_no]>
	        <[AND apv.apv_no IN (:apv_no_list)]>
	        
	        <[AND apv.platform = :platform]>
	        <[AND apv.platform IN (:platform_list)]>
	        <[AND apv.platform_order_id = :platform_order_id]>
	        <[AND apv.original_order_id = :original_order_id]>
        	<[AND apv.original_order_id >= :then_hz]>
	        <[AND apv.original_order_id <= :less_hz]>
	        <[AND apv.copy_type = :copy_type]>
	        <[AND apv.resend_type = :resend_type]>
	        <[AND apv.split_type = :split_type]>
	        <[AND apv.order_type = :order_type]>
	        <[AND apv.order_status = :order_status]>
	        <[AND apv.total_currency = :total_currency]>
	        <[AND apv.total_price = :total_price]>
	        <[AND apv.fee_or_credit = :fee_or_credit]>
	        <[AND apv.sale_date = :sale_date]>
	        <[AND apv.paid_date = :paid_date]>
	        <[AND apv.print_date = :print_date]>
	        <[AND apv.deliver_date = :deliver_date]>
	        <[AND apv.last_modified_time = :last_modified_time]>
	        <[AND apv.seller_id = :seller_id]>
	        <[AND apv.seller_email = :seller_email]>
	        <[AND apv.seller_pay = :seller_pay]>
	        <[AND apv.buyer_id = :buyer_id]>
	        <[AND apv.buyer_name = :buyer_name]>
	        <[AND apv.buyer_mobile = :buyer_mobile]>
	        <[AND apv.buyer_tel = :buyer_tel]>
	        <[AND apv.buyer_country_code = :buyer_country_code]>
	        <[AND apv.buyer_country = :buyer_country]>
	        <[AND apv.buyer_postal_code = :buyer_postal_code]>
	        <[AND apv.buyer_street = :buyer_street]>
	        <[AND apv.buyer_alley = :buyer_alley]>
	        <[AND apv.buyer_city = :buyer_city]>
	        <[AND apv.buyer_state_or_province = :buyer_state_or_province]>
	        <[AND apv.buyer_email = :buyer_email]>
	        <[AND apv.buyer_checkout = :buyer_checkout]>
	        <[AND apv.third_party_transaction_id = :third_party_transaction_id]>
	        <[AND apv.payment_status = :payment_status]>
	        <[AND apv.is_payment = :is_payment]>
	        <[AND apv.is_refund = :is_refund]>
	        <[AND apv.complete_status = :complete_status]>
	        <[AND apv.sales_record_number = :sales_record_number]>
	        <[AND apv.logistics_company = :logistics_company]>
	        <[AND apv.logistics_company IN (:logistics_company_list)]>
	        <[AND apv.id NOT IN (select inner_apv.id from wh_apv inner_apv  where inner_apv.logistics_company IN (:not_in_logistics_company_list))]>
	        <[AND apv.ship_service = :ship_service]>
	        <[AND apv.ship_freight = :ship_freight]>
	        <[AND apv.ship_status = :ship_status]>
	        <[AND apv.ship_status IN (:ship_status_list)]>
	        <[AND apv.ship_freight_cost = :ship_freight_cost]>
	        <[AND apv.is_shipment = :is_shipment]>
	        <[AND apv.shipment_time = :shipment_time]>
	        <[AND apv.logistics_type = :logistics_type]>
	        <[AND apv.tracking_number = :tracking_number]>
	        <[AND apv.service_provider_no = :service_provider_no]>
	        <[AND apv.sign_deliver_goods = :sign_deliver_goods]>
	        <[AND apv.sign_out_treasury = :sign_out_treasury]>
	        <[AND apv.sign_transport = :sign_transport]>
	        <[AND apv.sign_contact_buyers = :sign_contact_buyers]>
	        <[AND apv.sign_customer_reviews = :sign_customer_reviews]>
	        <[AND apv.sign_payment = :sign_payment]>
	        <[AND apv.sign_refund = :sign_refund]>
	        <[AND apv.sign_press_money = :sign_press_money]>
	        <[AND apv.sign_distribution_goods = :sign_distribution_goods]>
	        <[AND apv.actual_weight = :actual_weight]>
	        <[AND apv.system_price = :system_price]>
	        <[AND apv.status = :status]>
	        <[AND apv.status in(:status_list)]>
	        <[AND apv.extends_status = :extends_status]>
	        <[AND apv.creation_date = :creation_date]>
	        <[AND apv.creation_date >= :from_create_date]>
	        <[AND apv.creation_date <= :to_create_date]>
	        <[AND apv.created_by = :created_by]>
	        <[AND apv.last_update_date = :last_update_date]>
	        <[AND apv.last_updated_by = :last_updated_by]>
	        <[AND apv.apv_desc = :apv_desc]>
	        <[:lock_status]>
	        
	        <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.apv_id = apv.id GROUP BY inner_item.apv_id HAVING SUM(inner_item.sale_quantity) = :single_order_item)]>
		    <[AND apv.id NOT IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.apv_id = apv.id GROUP BY inner_item.apv_id HAVING SUM(inner_item.sale_quantity) = :multi_order_item)]>
		    
		    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku where inner_sku.warehouse_id IN (:warehouse_id_list))]>
		    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku where inner_sku.warehouse_id = :warehouse_id)]>
		    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku = :sku)]>
		    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku LIKE :like_sku)]>
		    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku IN (:sku_list)]>
		    
		    <[AND apv.id IN (SELECT task_item.apv_id from wh_picking_task_item task_item 
	    				LEFT JOIN wh_picking_task task ON task.id = task_item.task_id
						LEFT JOIN wh_box box ON box.relation_no = task.id
						where box.box_no = :box_no )
	    
	    	]>
	        
	        <[:is_exist_tracking_number]>
	        
	        <[:SINGLE_WAREHOUSE]>
	        <[:MULTI_WAREHOUSE]>
	        
	        <[:allot_task]>
	        <[:LIMIT]>
		) whapv on apv.id=whapv.id
        WHERE 1 = 1 
        <[:ORDER_BY]>
      ]]>
    </content>
  </sql>
  
  
  <!--   按框打单 -->
  <sql datasource="dataSource" id="queryBasketPrintWhApvList" >
    <content >
      <![CDATA[
        SELECT 
        	apv.id, apv.apv_no, apv.platform, apv.platform_order_id, apv.apv_desc, apv.apv_type, apv.status, apv.sign_payment, apv.logistics_company, apv.logistics_type, apv.ship_service,
        	
        	apv_item.id, apv_item.apv_id, apv_item.sku_id, apv_item.sku_title, apv_item.sku, apv_item.sale_quantity,apv_item.buyer_checkout,apv.buyer_checkout,
        	
        	apv_lock.status,sku.pack_image,apv.ship_status,

		   	CASE WHEN apv_item.is_combination_sku=1 THEN combine_sku.name ELSE sku.name END AS 'sku.name',
            CASE WHEN apv_item.is_combination_sku=1 THEN combine_sku.image_url ELSE sku.image_url END AS 'sku.image_url',
		   	sku.id, apv_item.sku AS 'sku.sku', sku.weight,  sku.warehouse_id, sku.location_number, sku.floor_location,
		   	
		   	(SELECT name FROM wh_packaging_material_management WHERE id= sku.packaging_id) as material,
			(SELECT GROUP_CONCAT(DISTINCT(name)) FROM wh_packaging_material_management WHERE FIND_IN_SET(id,sku.match_materials_code)) as matchMaterials,
			(SELECT GROUP_CONCAT(DISTINCT(image_url) SEPARATOR ',') FROM wh_packaging_material_management WHERE FIND_IN_SET(id,sku.match_materials_code)) as matchMaterialPictures,
		   	
	   		(stock.qc_quantity+stock.waiting_up_quantity+stock.up_quantity+stock.surplus_quantity+stock.pick_quantity+stock.cancel_quantity+stock.allot_quantity+stock.pick_not_quantity) AS stockQuantity,
	   		stock.surplus_quantity
        
        FROM wh_apv apv
        LEFT JOIN wh_apv_lock apv_lock ON apv.id = apv_lock.apv_id
        LEFT JOIN wh_apv_item apv_item ON apv.id = apv_item.apv_id
		LEFT JOIN wh_sku sku ON apv_item.sku = sku.sku
		LEFT JOIN wh_combine_sku combine_sku ON apv_item.sku = combine_sku.spu
		LEFT JOIN wh_stock stock ON stock.sku = apv_item.sku
		LEFT JOIN wh_picking_task_item task_item ON task_item.apv_id = apv.id
		LEFT JOIN wh_picking_task task ON task_item.task_id = task.id
        WHERE 1 = 1 
        <[AND apv.id = :id]>
        <[AND apv.id IN (:apv_id_list)]>
        <[AND apv.apv_no = :apv_no]>
        <[AND apv.platform = :platform]>
        <[AND apv.platform IN (:platform_list)]>
        <[AND apv.platform_order_id = :platform_order_id]>
        <[AND apv.original_order_id = :original_order_id]>
        <[AND apv.original_order_id >= :then_hz]>
        <[AND apv.original_order_id <= :less_hz]>
        <[AND apv.copy_type = :copy_type]>
        <[AND apv.resend_type = :resend_type]>
        <[AND apv.split_type = :split_type]>
        <[AND apv.order_type = :order_type]>
        <[AND apv.order_status = :order_status]>
        <[AND apv.total_currency = :total_currency]>
        <[AND apv.total_price = :total_price]>
        <[AND apv.fee_or_credit = :fee_or_credit]>
        <[AND apv.sale_date = :sale_date]>
        <[AND apv.paid_date = :paid_date]>
        <[AND apv.print_date = :print_date]>
        <[AND apv.deliver_date = :deliver_date]>
        <[AND apv.last_modified_time = :last_modified_time]>
        <[AND apv.seller_id = :seller_id]>
        <[AND apv.seller_email = :seller_email]>
        <[AND apv.seller_pay = :seller_pay]>
        <[AND apv.buyer_id = :buyer_id]>
        <[AND apv.buyer_name = :buyer_name]>
        <[AND apv.buyer_mobile = :buyer_mobile]>
        <[AND apv.buyer_tel = :buyer_tel]>
        <[AND apv.buyer_country_code = :buyer_country_code]>
        <[AND apv.buyer_country = :buyer_country]>
        <[AND apv.buyer_postal_code = :buyer_postal_code]>
        <[AND apv.buyer_street = :buyer_street]>
        <[AND apv.buyer_alley = :buyer_alley]>
        <[AND apv.buyer_city = :buyer_city]>
        <[AND apv.buyer_state_or_province = :buyer_state_or_province]>
        <[AND apv.buyer_email = :buyer_email]>
        <[AND apv.buyer_checkout = :buyer_checkout]>
        <[AND apv.third_party_transaction_id = :third_party_transaction_id]>
        <[AND apv.payment_status = :payment_status]>
        <[AND apv.is_payment = :is_payment]>
        <[AND apv.is_refund = :is_refund]>
        <[AND apv.complete_status = :complete_status]>
        <[AND apv.complete_status = :reXiaoTaskNo]>
        <[AND apv.sales_record_number = :sales_record_number]>
        <[AND apv.logistics_company = :logistics_company]>
        <[AND apv.logistics_company IN (:logistics_company_list)]>
        <[AND apv.id NOT IN (select inner_apv.id from wh_apv inner_apv  where inner_apv.logistics_company IN (:not_in_logistics_company_list))]>
        <[AND apv.ship_service = :ship_service]>
        <[AND apv.ship_freight = :ship_freight]>
        <[AND apv.ship_status = :ship_status]>
        <[AND apv.ship_freight_cost = :ship_freight_cost]>
        <[AND apv.is_shipment = :is_shipment]>
        <[AND apv.shipment_time = :shipment_time]>
        <[AND apv.logistics_type = :logistics_type]>
        <[AND apv.tracking_number = :tracking_number]>
        <[AND apv.service_provider_no = :service_provider_no]>
        <[AND apv.sign_deliver_goods = :sign_deliver_goods]>
        <[AND apv.sign_out_treasury = :sign_out_treasury]>
        <[AND apv.sign_transport = :sign_transport]>
        <[AND apv.sign_contact_buyers = :sign_contact_buyers]>
        <[AND apv.sign_customer_reviews = :sign_customer_reviews]>
        <[AND apv.sign_payment = :sign_payment]>
        <[AND apv.sign_refund = :sign_refund]>
        <[AND apv.sign_press_money = :sign_press_money]>
        <[AND apv.sign_distribution_goods = :sign_distribution_goods]>
        <[AND apv.actual_weight = :actual_weight]>
        <[AND apv.system_price = :system_price]>
        <[AND apv.status = :status]>
        <[AND apv.status IN (:status_list)]>
        <[AND apv.extends_status = :extends_status]>
        <[AND apv.creation_date = :creation_date]>
        <[AND apv.creation_date >= :from_create_date]>
	    <[AND apv.creation_date <= :to_create_date]>
        <[AND apv.created_by = :created_by]>
        <[AND apv.last_update_date = :last_update_date]>
        <[AND apv.last_updated_by = :last_updated_by]>
        <[AND apv.ship_status NOT IN (:not_in_apv_deliverytype_list)]>
        <[AND apv.apv_desc = :apv_desc]>
        <[:lock_status]>
        
        <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.apv_id = apv.id GROUP BY inner_item.apv_id HAVING SUM(inner_item.sale_quantity) = :single_order_item)]>
	    <[AND apv.id NOT IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.apv_id = apv.id GROUP BY inner_item.apv_id HAVING SUM(inner_item.sale_quantity) = :multi_order_item)]>
	    
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku where inner_sku.warehouse_id IN (:warehouse_id_list))]>
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku where inner_sku.warehouse_id = :warehouse_id)]>
	    
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku = :sku)]>
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku LIKE :like_sku)]>
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku IN (:sku_list)]>
	    
	    <[AND apv.id IN (SELECT task_item.apv_id from wh_picking_task_item task_item 
	    				LEFT JOIN wh_picking_task task ON task.id = task_item.task_id
						LEFT JOIN wh_box box ON box.relation_no = task.id
						where box.box_no = :box_no )
	    
	    ]>
	    
	    <[AND task.task_no = :task_no]>
        
        <[:is_exist_tracking_number]>
        <[:IS_GPSR]>
        <[:EXCLUDE_GPSR]>
        <[:allot_task]>
        
        <[:ORDER_BY]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryBasketPrintWhApvListForSM" >
    <content >
      <![CDATA[
        SELECT 
        	apv.id, apv.apv_no, apv.platform, apv.platform_order_id, apv.apv_desc, apv.apv_type, apv.status, apv.sign_payment, apv.logistics_company, apv.logistics_type, apv.ship_service,
        	
        	apv_item.id, apv_item.apv_id, apv_item.sku_id, apv_item.sku_title, apv_item.sku, apv_item.sale_quantity,apv_item.buyer_checkout,apv.ship_status,
        	
        	apv_lock.status,apv.buyer_checkout,sku.pack_image,
        	CASE WHEN apv_item.is_combination_sku=1 THEN combine_sku.name ELSE sku.name END AS 'sku.name',
            CASE WHEN apv_item.is_combination_sku=1 THEN combine_sku.image_url ELSE sku.image_url END AS 'sku.image_url',
		   	sku.id, apv_item.sku AS 'sku.sku',  sku.weight,  sku.warehouse_id, sku.location_number, sku.floor_location,
		   	
		   	(SELECT name FROM wh_packaging_material_management WHERE id= sku.packaging_id) as material,
			(SELECT GROUP_CONCAT(DISTINCT(name)) FROM wh_packaging_material_management WHERE FIND_IN_SET(id,sku.match_materials_code)) as matchMaterials,
			(SELECT GROUP_CONCAT(DISTINCT(image_url) SEPARATOR ',') FROM wh_packaging_material_management WHERE FIND_IN_SET(id,sku.match_materials_code)) as matchMaterialPictures,
		   	
	   		(stock.qc_quantity+stock.waiting_up_quantity+stock.up_quantity+stock.surplus_quantity+stock.pick_quantity+stock.cancel_quantity+stock.allot_quantity+stock.pick_not_quantity) AS stockQuantity,
	   		stock.surplus_quantity
        
        FROM wh_apv apv
        LEFT JOIN wh_apv_lock apv_lock ON apv.id = apv_lock.apv_id
        LEFT JOIN wh_apv_item apv_item ON apv.id = apv_item.apv_id
		LEFT JOIN wh_sku sku ON apv_item.sku = sku.sku
		LEFT JOIN wh_combine_sku combine_sku ON apv_item.sku = combine_sku.spu
		LEFT JOIN wh_stock stock ON stock.sku = apv_item.sku
		LEFT JOIN wh_picking_task_item task_item ON task_item.apv_id = apv.id
		LEFT JOIN wh_picking_task task ON task_item.task_id = task.id
        WHERE 1 = 1 
        <[AND apv.id = :id]>
        <[AND apv.id IN (:apv_id_list)]>
        <[AND apv.apv_no = :apv_no]>
        <[AND apv.platform = :platform]>
        <[AND apv.platform IN (:platform_list)]>
        <[AND apv.platform_order_id = :platform_order_id]>
        <[AND apv.original_order_id = :original_order_id]>
        <[AND apv.original_order_id >= :then_hz]>
        <[AND apv.original_order_id <= :less_hz]>
        <[AND apv.copy_type = :copy_type]>
        <[AND apv.resend_type = :resend_type]>
        <[AND apv.split_type = :split_type]>
        <[AND apv.order_type = :order_type]>
        <[AND apv.order_status = :order_status]>
        <[AND apv.total_currency = :total_currency]>
        <[AND apv.total_price = :total_price]>
        <[AND apv.fee_or_credit = :fee_or_credit]>
        <[AND apv.sale_date = :sale_date]>
        <[AND apv.paid_date = :paid_date]>
        <[AND apv.print_date = :print_date]>
        <[AND apv.deliver_date = :deliver_date]>
        <[AND apv.last_modified_time = :last_modified_time]>
        <[AND apv.seller_id = :seller_id]>
        <[AND apv.seller_email = :seller_email]>
        <[AND apv.seller_pay = :seller_pay]>
        <[AND apv.buyer_id = :buyer_id]>
        <[AND apv.buyer_name = :buyer_name]>
        <[AND apv.buyer_mobile = :buyer_mobile]>
        <[AND apv.buyer_tel = :buyer_tel]>
        <[AND apv.buyer_country_code = :buyer_country_code]>
        <[AND apv.buyer_country = :buyer_country]>
        <[AND apv.buyer_postal_code = :buyer_postal_code]>
        <[AND apv.buyer_street = :buyer_street]>
        <[AND apv.buyer_alley = :buyer_alley]>
        <[AND apv.buyer_city = :buyer_city]>
        <[AND apv.buyer_state_or_province = :buyer_state_or_province]>
        <[AND apv.buyer_email = :buyer_email]>
        <[AND apv.buyer_checkout = :buyer_checkout]>
        <[AND apv.third_party_transaction_id = :third_party_transaction_id]>
        <[AND apv.payment_status = :payment_status]>
        <[AND apv.is_payment = :is_payment]>
        <[AND apv.is_refund = :is_refund]>
        <[AND apv.complete_status = :complete_status]>
        <[AND apv.sales_record_number = :sales_record_number]>
        <[AND apv.logistics_company = :logistics_company]>
        <[AND apv.logistics_company IN (:logistics_company_list)]>
        <[AND apv.id NOT IN (select inner_apv.id from wh_apv inner_apv  where inner_apv.logistics_company IN (:not_in_logistics_company_list))]>
        <[AND apv.ship_service = :ship_service]>
        <[AND apv.ship_freight = :ship_freight]>
        <[AND apv.ship_status = :ship_status]>
        <[AND apv.ship_freight_cost = :ship_freight_cost]>
        <[AND apv.is_shipment = :is_shipment]>
        <[AND apv.shipment_time = :shipment_time]>
        <[AND apv.logistics_type = :logistics_type]>
        <[AND apv.tracking_number = :tracking_number]>
        <[AND apv.service_provider_no = :service_provider_no]>
        <[AND apv.sign_deliver_goods = :sign_deliver_goods]>
        <[AND apv.sign_out_treasury = :sign_out_treasury]>
        <[AND apv.sign_transport = :sign_transport]>
        <[AND apv.sign_contact_buyers = :sign_contact_buyers]>
        <[AND apv.sign_customer_reviews = :sign_customer_reviews]>
        <[AND apv.sign_payment = :sign_payment]>
        <[AND apv.sign_refund = :sign_refund]>
        <[AND apv.sign_press_money = :sign_press_money]>
        <[AND apv.sign_distribution_goods = :sign_distribution_goods]>
        <[AND apv.actual_weight = :actual_weight]>
        <[AND apv.system_price = :system_price]>
        <[AND apv.status = :status]>
        <[AND apv.extends_status = :extends_status]>
        <[AND apv.creation_date = :creation_date]>
        <[AND apv.creation_date >= :from_create_date]>
	    <[AND apv.creation_date <= :to_create_date]>
        <[AND apv.created_by = :created_by]>
        <[AND apv.last_update_date = :last_update_date]>
        <[AND apv.last_updated_by = :last_updated_by]>
        <[AND apv.apv_desc = :apv_desc]>
        <[AND apv.ship_status NOT IN (:not_in_apv_deliverytype_list)]>
        <[:lock_status]>
        
        <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.apv_id = apv.id GROUP BY inner_item.apv_id HAVING SUM(inner_item.sale_quantity) = :single_order_item)]>
	    <[AND apv.id NOT IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.apv_id = apv.id GROUP BY inner_item.apv_id HAVING SUM(inner_item.sale_quantity) = :multi_order_item)]>
	    
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku where inner_sku.warehouse_id IN (:warehouse_id_list))]>
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku where inner_sku.warehouse_id = :warehouse_id)]>
	    
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku = :sku)]>
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku LIKE :like_sku)]>
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku IN (:sku_list)]>
	    
	    <[AND apv.id IN (SELECT task_item.apv_id from wh_picking_task_item task_item 
	    				LEFT JOIN wh_picking_task task ON task.id = task_item.task_id
						LEFT JOIN wh_box box ON box.relation_no = task.id
						where box.box_no = :box_no )
	    
	    ]>
	    
	    <[AND task.task_no = :task_no]>
	    
	    <[AND task.task_type = :pick_task_type]>
        
        <[:is_exist_tracking_number]>
        <[:IS_GPSR]>
        <[:EXCLUDE_GPSR]>

        <[:allot_task]>
        
        <[:ORDER_BY]>
      ]]>
    </content>
  </sql>
 
  
  <sql datasource="dataSource" id="queryWhApvBySinglesingleAndReturns" >
    <content >
      <![CDATA[
        SELECT 
        	apv.id, apv.apv_no, apv.logistics_company, apv.platform, apv.seller_id, apv.logistics_type, apv.status, apv.sign_distribution_goods,
        	
        	apv_item.sku, apv_item.sale_quantity, apv.ship_service, 
        	
		   	sku.name, sku.image_url,
		   	
		   	(SELECT name FROM wh_packaging_material_management WHERE id= sku.packaging_id) as material,
			(SELECT GROUP_CONCAT(DISTINCT(name)) FROM wh_packaging_material_management WHERE FIND_IN_SET(id,sku.match_materials_code)) as matchMaterials
		   	
        FROM wh_apv apv
        LEFT JOIN wh_apv_lock apv_lock ON apv.id = apv_lock.apv_id
        LEFT JOIN wh_apv_item apv_item ON apv.id = apv_item.apv_id
		LEFT JOIN wh_sku sku ON apv_item.sku = sku.sku 
		LEFT JOIN wh_stock stock ON stock.sku = sku.sku
		LEFT JOIN wh_picking_task_item task_item ON task_item.apv_id = apv.id
		LEFT JOIN wh_picking_task task ON task_item.task_id = task.id
		
        WHERE 1 = 1 
        <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku = :sku)]>
        <[AND apv.apv_type = :apv_type]>
        <[AND apv.status in (:status_list)]>
        <[:normal_apv]>
        <[:ORDER_BY]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryOutStockApv" >
    <content >
      <![CDATA[
        SELECT
        	apv.id, apv.apv_no, apv.platform, apv.platform_order_id, apv.apv_desc, apv.apv_type, apv.status, apv.sign_payment, apv.logistics_company,
        	apv.logistics_type, apv.ship_service,apv_item.id, apv_item.apv_id, apv_item.sku_id, apv_item.sku_title, apv_item.sku, apv_item.sale_quantity,
        	apv_lock.status,apv.buyer_checkout,sku.id, sku.sku, sku.image_url, sku.weight, sku.name, sku.warehouse_id, sku.location_number, sku.floor_location,
		   	(SELECT name FROM wh_packaging_material_management WHERE id= sku.packaging_id) as material,sku.pack_image,apv_item.buyer_checkout,apv.ship_status,
			(SELECT GROUP_CONCAT(DISTINCT(name)) FROM wh_packaging_material_management WHERE FIND_IN_SET(id,sku.match_materials_code)) as matchMaterials,
			(SELECT GROUP_CONCAT(DISTINCT(image_url) SEPARATOR ',') FROM wh_packaging_material_management WHERE FIND_IN_SET(id,sku.match_materials_code)) as matchMaterialPictures
        FROM wh_apv apv
        LEFT JOIN wh_apv_lock apv_lock ON apv.id = apv_lock.apv_id
        LEFT JOIN wh_apv_item apv_item ON apv.id = apv_item.apv_id
		LEFT JOIN wh_sku sku ON apv_item.sku = sku.sku
        WHERE 1 = 1
        <[AND apv.id = :id]>
        <[AND apv.id IN (:apv_id_list)]>
        <[AND apv.apv_no = :apv_no]>
        <[AND apv.platform = :platform]>
        <[AND apv.platform IN (:platform_list)]>
        <[AND apv.order_type = :order_type]>
        <[AND apv.order_status = :order_status]>
        <[AND apv.ship_status = :ship_status]>
        <[AND apv.ship_status NOT IN (:not_in_apv_deliverytype_list)]>
        <[AND apv.status = :status]>
        <[AND apv.apv_type =:apv_type]>
        <[AND apv.apv_type in (:apv_type_list)]>
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku = :sku)]>
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku IN (:sku_list)]>
        ORDER BY apv_item.sale_quantity DESC
      ]]>
    </content>
  </sql>
  
</sqlmap>