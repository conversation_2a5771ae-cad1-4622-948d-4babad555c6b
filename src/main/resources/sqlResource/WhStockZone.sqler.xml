<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhStockZoneCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_stock_zone
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND code = :code]>
        <[AND name = :name]>
        <[AND stock_location_prefixes = :stock_location_prefixes]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhStockZoneList" >
    <content >
      <![CDATA[
        SELECT id, warehouse_id, code, name, stock_location_prefixes
        FROM wh_stock_zone
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND code = :code]>
        <[AND name = :name]>
        <[AND stock_location_prefixes = :stock_location_prefixes]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhStockZoneByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, warehouse_id, code, name, stock_location_prefixes
        FROM wh_stock_zone
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhStockZone" >
    <content >
      <![CDATA[
        SELECT id, warehouse_id, code, name, stock_location_prefixes
        FROM wh_stock_zone
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND code = :code]>
        <[AND name = :name]>
        <[AND stock_location_prefixes = :stock_location_prefixes]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhStockZone" >
    <content >
      <![CDATA[
        INSERT INTO wh_stock_zone (warehouse_id, code, name, stock_location_prefixes)
        VALUES (:warehouse_id, :code, :name, :stock_location_prefixes)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhStockZoneByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_stock_zone
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhStockZoneByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_stock_zone
        SET <[warehouse_id = :warehouse_id,]>
          <[code = :code,]>
          <[name = :name,]>
          <[stock_location_prefixes = :stock_location_prefixes,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>