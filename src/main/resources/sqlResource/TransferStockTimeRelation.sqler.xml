<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryTransferStockTimeRelationCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM transfer_stock_time_relation
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND stock_id = :stock_id]>
        <[AND allocation_in_time = :allocation_in_time]>
        <[AND allocation_out_time = :allocation_out_time]>
        <[AND last_up_time = :last_up_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTransferStockTimeRelationList" >
    <content >
      <![CDATA[
        SELECT id, stock_id, allocation_in_time, allocation_out_time, last_up_time
        FROM transfer_stock_time_relation
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND stock_id = :stock_id]>
        <[AND allocation_in_time = :allocation_in_time]>
        <[AND allocation_out_time = :allocation_out_time]>
        <[AND last_up_time = :last_up_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTransferStockTimeRelationByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, stock_id, allocation_in_time, allocation_out_time, last_up_time
        FROM transfer_stock_time_relation
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTransferStockTimeRelation" >
    <content >
      <![CDATA[
        SELECT id, stock_id, allocation_in_time, allocation_out_time, last_up_time
        FROM transfer_stock_time_relation
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND stock_id = :stock_id]>
        <[AND allocation_in_time = :allocation_in_time]>
        <[AND allocation_out_time = :allocation_out_time]>
        <[AND last_up_time = :last_up_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createTransferStockTimeRelation" >
    <content >
      <![CDATA[
        INSERT INTO transfer_stock_time_relation (stock_id, allocation_in_time, allocation_out_time, last_up_time)
        VALUES (:stock_id, :allocation_in_time, :allocation_out_time, :last_up_time)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteTransferStockTimeRelationByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM transfer_stock_time_relation
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateTransferStockTimeRelationByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE transfer_stock_time_relation
        SET <[stock_id = :stock_id,]>
          <[allocation_in_time = :allocation_in_time,]>
          <[allocation_out_time = :allocation_out_time,]>
          <[last_up_time = :last_up_time,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>