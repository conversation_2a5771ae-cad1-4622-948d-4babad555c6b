<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhApvItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_apv_item apv_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_id = :apv_id]>
        <[AND apv_id IN (:apv_id_list)]>
        <[AND apv_line_item_id = :apv_line_item_id]>
        <[AND transaction_id = :transaction_id]>
        <[AND site = :site]>
        <[AND sku_id = :sku_id]>
        <[AND sku_title = :sku_title]>
        <[AND sku = :sku]>
        <[AND salesperson = :salesperson]>
        <[AND currency = :currency]>
        <[AND sku_price = :sku_price]>
        <[AND sale_quantity = :sale_quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND sale_price = :sale_price]>
        <[AND transaction_cost = :transaction_cost]>
        <[AND final_transaction_cost = :final_transaction_cost]>
        <[AND sales_record_number = :sales_record_number]>
        <[AND logistics_tracking_number = :logistics_tracking_number]>
        <[AND shipping_carrier_used = :shipping_carrier_used]>
        <[AND multi_attr = :multi_attr]>
        <[AND buyer_checkout = :buyer_checkout]>
        <[AND created_date = :created_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND item_desc = :item_desc]>
        <[AND apv_id IN (SELECT apv.id FROM wh_apv apv WHERE apv.id=apv_item.apv_id AND apv.status = :apv_status)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhApvItemList" >
    <content >
      <![CDATA[
        SELECT id, apv_id, apv_line_item_id, transaction_id, site, sku_id, sku_title, sku, 
        salesperson, currency, sku_price, sale_quantity, pick_quantity, sale_price, transaction_cost, final_transaction_cost, 
        sales_record_number, logistics_tracking_number, shipping_carrier_used, multi_attr, 
        buyer_checkout, created_date, last_update_date, last_updated_by, item_desc
        FROM wh_apv_item apv_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_id = :apv_id]>
        <[AND apv_id IN (:apv_id_list)]>
        <[AND apv_line_item_id = :apv_line_item_id]>
        <[AND transaction_id = :transaction_id]>
        <[AND site = :site]>
        <[AND sku_id = :sku_id]>
        <[AND sku_title = :sku_title]>
        <[AND sku = :sku]>
        <[AND salesperson = :salesperson]>
        <[AND currency = :currency]>
        <[AND sku_price = :sku_price]>
        <[AND sale_quantity = :sale_quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND sale_price = :sale_price]>
        <[AND transaction_cost = :transaction_cost]>
        <[AND final_transaction_cost = :final_transaction_cost]>
        <[AND sales_record_number = :sales_record_number]>
        <[AND logistics_tracking_number = :logistics_tracking_number]>
        <[AND shipping_carrier_used = :shipping_carrier_used]>
        <[AND multi_attr = :multi_attr]>
        <[AND buyer_checkout = :buyer_checkout]>
        <[AND created_date = :created_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND item_desc = :item_desc]>
        <[AND apv_id IN (SELECT apv.id FROM wh_apv apv WHERE apv.id=apv_item.apv_id AND apv.status = :apv_status)]>
        <[AND apv_id IN (SELECT apv.id FROM wh_apv apv WHERE apv.id=apv_item.apv_id AND apv.status not in (:not_apv_status_list))]>
        <[AND apv_id IN (SELECT apv.id FROM wh_apv apv WHERE apv.id=apv_item.apv_id AND apv.status in (:apv_status_list))]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhApvItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, apv_id, apv_line_item_id, transaction_id, site, sku_id, sku_title, sku, 
        salesperson, currency, sku_price, sale_quantity, pick_quantity, sale_price, transaction_cost, final_transaction_cost, 
        sales_record_number, logistics_tracking_number, shipping_carrier_used, multi_attr, 
        buyer_checkout, created_date, last_update_date, last_updated_by, item_desc
        FROM wh_apv_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhApvItem" >
    <content >
      <![CDATA[
        SELECT id, apv_id, apv_line_item_id, transaction_id, site, sku_id, sku_title, sku, 
        salesperson, currency, sku_price, sale_quantity, pick_quantity, sale_price, transaction_cost, final_transaction_cost, 
        sales_record_number, logistics_tracking_number, shipping_carrier_used, multi_attr, 
        buyer_checkout, created_date, last_update_date, last_updated_by, item_desc
        FROM wh_apv_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_id = :apv_id]>
        <[AND apv_line_item_id = :apv_line_item_id]>
        <[AND transaction_id = :transaction_id]>
        <[AND site = :site]>
        <[AND sku_id = :sku_id]>
        <[AND sku_title = :sku_title]>
        <[AND sku = :sku]>
        <[AND salesperson = :salesperson]>
        <[AND currency = :currency]>
        <[AND sku_price = :sku_price]>
        <[AND sale_quantity = :sale_quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND sale_price = :sale_price]>
        <[AND transaction_cost = :transaction_cost]>
        <[AND final_transaction_cost = :final_transaction_cost]>
        <[AND sales_record_number = :sales_record_number]>
        <[AND logistics_tracking_number = :logistics_tracking_number]>
        <[AND shipping_carrier_used = :shipping_carrier_used]>
        <[AND multi_attr = :multi_attr]>
        <[AND buyer_checkout = :buyer_checkout]>
        <[AND created_date = :created_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND item_desc = :item_desc]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhApvItem" >
    <content >
      <![CDATA[
        INSERT INTO wh_apv_item (apv_id, apv_line_item_id, transaction_id, site, sku_id, sku_title, 
          sku, salesperson, currency, sku_price, sale_quantity, pick_quantity, sale_price, transaction_cost, 
          final_transaction_cost, sales_record_number, logistics_tracking_number, shipping_carrier_used, 
          multi_attr, buyer_checkout, created_date, last_update_date, last_updated_by, seller_sku,product_id,
          item_desc,is_combination_sku)
        VALUES (:apv_id, :apv_line_item_id, :transaction_id, :site, :sku_id, :sku_title, 
          :sku, :salesperson, :currency, :sku_price, :sale_quantity, :pick_quantity, :sale_price, :transaction_cost, 
          :final_transaction_cost, :sales_record_number, :logistics_tracking_number, :shipping_carrier_used, 
          :multi_attr, :buyer_checkout, :created_date, :last_update_date, :last_updated_by, :seller_sku,:product_id,
          :item_desc, :is_combination_sku)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhApvItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_apv_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhApvItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_apv_item
        SET <[apv_id = :apv_id,]>
          <[apv_line_item_id = :apv_line_item_id,]>
          <[transaction_id = :transaction_id,]>
          <[site = :site,]>
          <[sku_id = :sku_id,]>
          <[sku_title = :sku_title,]>
          <[sku = :sku,]>
          <[salesperson = :salesperson,]>
          <[currency = :currency,]>
          <[sku_price = :sku_price,]>
          <[sale_quantity = :sale_quantity,]>
          <[pick_quantity = :pick_quantity,]>
          <[sale_price = :sale_price,]>
          <[transaction_cost = :transaction_cost,]>
          <[final_transaction_cost = :final_transaction_cost,]>
          <[sales_record_number = :sales_record_number,]>
          <[logistics_tracking_number = :logistics_tracking_number,]>
          <[shipping_carrier_used = :shipping_carrier_used,]>
          <[multi_attr = :multi_attr,]>
          <[buyer_checkout = :buyer_checkout,]>
          <[created_date = :created_date,]>
          <[last_update_date = :last_update_date,]>
          <[last_updated_by = :last_updated_by,]>
          <[item_desc = :item_desc,]>
          <[is_combination_sku = :is_combination_sku,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateWhApvItemByApvIdAndSku" >
    <content >
      <![CDATA[
        UPDATE wh_apv_item item
        SET
          <[last_update_date = :last_update_date,]>
          <[last_updated_by = :last_updated_by,]>
          <[item_desc = :item_desc,]>
          salesperson = (SELECT whSku.warehouse_id FROM wh_sku whSku WHERE whSku.sku = item.sku)
        WHERE 1 = 1
        AND apv_id = :apv_id
        AND sku IN (:sku_list)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySkuAndSaleQuantityByApvNo" >
    <content >
      <![CDATA[
        SELECT 
        	id,
	        sku,
	        apv_line_item_id,
	        sale_quantity,
	        pick_quantity,
	        apv_id,
	        (SELECT `status` FROM wh_apv WHERE id = apv_id) as sku_id,
	        (SELECT ship_status FROM wh_apv WHERE id = apv_id) as last_updated_by,
	        (SELECT warehouse_id FROM wh_sku whSku WHERE whSku.sku = item.sku) as warehouse_id,
	        is_combination_sku
        FROM 
        	wh_apv_item item
        WHERE
        	apv_id IN (SELECT id FROM wh_apv WHERE apv_no = :apv_no)
      ]]>
    </content>
  </sql>
</sqlmap>