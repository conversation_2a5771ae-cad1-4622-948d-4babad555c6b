<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryAsnPickBoxCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM asn_pick_box
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND pick_up_order_no = :pick_up_order_no]>
        <[AND number = :number]>
        <[AND status = :status]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAsnPickBoxList" >
    <content >
      <![CDATA[
        SELECT id, pick_up_order_no, number, status, creation_date, create_by, last_update_date, 
        last_updated_by
        FROM asn_pick_box
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND pick_up_order_no = :pick_up_order_no]>
        <[AND number = :number]>
        <[AND status = :status]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAsnPickBoxByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, pick_up_order_no, number, status, creation_date, create_by, last_update_date, 
        last_updated_by
        FROM asn_pick_box
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAsnPickBox" >
    <content >
      <![CDATA[
        SELECT id, pick_up_order_no, number, status, creation_date, create_by, last_update_date, 
        last_updated_by
        FROM asn_pick_box
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND pick_up_order_no = :pick_up_order_no]>
        <[AND number = :number]>
        <[AND status = :status]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createAsnPickBox" >
    <content >
      <![CDATA[
        INSERT INTO asn_pick_box (pick_up_order_no, number, status, creation_date, create_by, last_update_date, 
          last_updated_by)
        VALUES (:pick_up_order_no, :number, :status, :creation_date, :create_by, :last_update_date, 
          :last_updated_by)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteAsnPickBoxByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM asn_pick_box
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateAsnPickBoxByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE asn_pick_box
        SET <[pick_up_order_no = :pick_up_order_no,]>
          <[number = :number,]>
          <[status = :status,]>
          <[creation_date = :creation_date,]>
          <[create_by = :create_by,]>
          <[last_update_date = :last_update_date,]>
          <[last_updated_by = :last_updated_by,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>