<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
    <sql datasource="dataSource" id="queryPrestorageStockTransferOrderCount" >
        <content ><![CDATA[
            SELECT count(*)
            FROM prestorage_stock_transfer_order
            WHERE 1 = 1
            <[AND create_date >= :from_create_date]>
            <[AND create_date <= :to_create_date]>
            <[AND id in (SELECT DISTINCT order_id FROM prestorage_stock_transfer_order_item where sku in (:skus))]>
            <[AND order_no in (:order_nos)]>
            <[AND order_no = :order_no]>
            <[AND id in (:ids)]>
            <[AND id = :id]>
            <[AND status = :status]>
            <[AND status in (:status_list)]>
            <[AND print_status = :print_status]>
            <[AND accept_person_id = :accept_person_id]>
            <[AND completed_date >= :from_completed_date]>
            <[AND completed_date <= :to_completed_date]>
            <[AND source =:source]>
            <[AND warehouse_type =:warehouse_type]>
            <[AND bind_box_no = :box_no]>
            <[AND type = :type]>
            <[AND FIND_IN_SET(:relevant_order_no,relevant_order_no)]>
        ]]></content>
    </sql>


    <sql datasource="dataSource" id="queryPrestorageStockTransferOrder" >
        <content ><![CDATA[
            SELECT id,order_no,source,warehouse_type,type,status,print_status,create_person_id,create_date,accept_person_id,
            accept_date,upload_person_id,upload_date,completed_date,bind_box_no,relevant_order_no
            FROM prestorage_stock_transfer_order
            WHERE 1 = 1
            <[AND create_date >= :from_create_date]>
            <[AND create_date <= :to_create_date]>
            <[AND id in (SELECT DISTINCT order_id FROM prestorage_stock_transfer_order_item where sku in (:skus))]>
            <[AND order_no in (:order_nos)]>
            <[AND order_no = :order_no]>
            <[AND id in (:ids)]>
            <[AND id = :id]>
            <[AND status = :status]>
            <[AND status in (:status_list)]>
            <[AND print_status = :print_status]>
            <[AND accept_person_id = :accept_person_id]>
            <[AND completed_date >= :from_completed_date]>
            <[AND completed_date <= :to_completed_date]>
            <[AND source =:source]>
            <[AND warehouse_type =:warehouse_type]>
            <[AND bind_box_no = :box_no]>
            <[AND type = :type]>
            <[AND FIND_IN_SET(:relevant_order_no,relevant_order_no)]>
            ORDER BY id DESC
        ]]></content>
    </sql>

    <sql datasource="dataSource" id="queryPrestorageStockTransfer" >
        <content ><![CDATA[
            SELECT t.id,t.order_no,t.source,t.warehouse_type,t.type,
                COUNT(DISTINCT item.sku) as sku_type_amount,
                COUNT(DISTINCT item.emigration_location_number) as emigration_location_type_amount,
                COUNT(DISTINCT item.immigration_location_number) as immigration_location_type_amount,
                SUM(item.sku_amount) as migration_sku_amount,
                SUM(item.picking_sku_amount)  as picking_sku_amount,
                t.create_person_id as create_person,t.accept_person_id as accept_person,
                t.status,t.print_status,t.create_date,t.accept_date,t.completed_date
            FROM prestorage_stock_transfer_order t
			LEFT JOIN prestorage_stock_transfer_order_item item ON item.order_id = t.id
            WHERE 1 = 1
            <[AND t.create_date >= :from_create_date]>
            <[AND t.create_date <= :to_create_date]>
            <[AND item.sku in (:skus)]>
            <[AND t.order_no in (:order_nos)]>
            <[AND t.order_no = :order_no]>
            <[AND t.id in (:ids)]>
            <[AND t.id = :id]>
            <[AND t.status = :status]>
            <[AND t.status in (:status_list)]>
            <[AND t.print_status = :print_status]>
            <[AND t.accept_person_id = :accept_person_id]>
            <[AND t.completed_date >= :from_completed_date]>
            <[AND t.completed_date <= :to_completed_date]>
            <[AND t.source =:source]>
            <[AND warehouse_type =:warehouse_type]>
            <[AND t.type = :type]>
            <[AND FIND_IN_SET(:relevant_order_no,relevant_order_no)]>
            GROUP BY t.id
            ORDER BY t.id DESC
        ]]></content>
    </sql>

    <sql datasource="dataSource" id="queryPrestorageStockTransferDetail" >
        <content ><![CDATA[
            SELECT detail.id,detail.order_id,warehouse_type,order_no,type,detail.sku,sku.name as sku_name,emigration_location_number,immigration_location_number,
                   sku_amount ,picking_sku_amount,upload_sku_amount
            FROM prestorage_stock_transfer_order_item detail
            INNER JOIN prestorage_stock_transfer_order transfer_order on detail.order_id = transfer_order.id
            INNER JOIN wh_sku sku on sku.sku = detail.sku
            WHERE 1 = 1
            <[AND create_date >= :from_create_date]>
            <[AND create_date <= :to_create_date]>
            <[AND transfer_order.id in (SELECT DISTINCT order_id FROM prestorage_stock_transfer_order_item where sku in (:skus))]>
            <[AND order_no in (:order_nos)]>
            <[AND order_no = :order_no]>
            <[AND transfer_order.id in (:ids)]>
            <[AND transfer_order.id = :id]>
            <[AND transfer_order.status = :status]>
            <[AND transfer_order.status in (:status_list)]>
            <[AND print_status = :print_status]>
            <[AND accept_person_id = :accept_person_id]>
            <[AND completed_date >= :from_completed_date]>
            <[AND completed_date <= :to_completed_date]>
            <[AND source =:source]>
            <[AND warehouse_type =:warehouse_type]>
            <[AND bind_box_no = :box_no]>
            <[AND type = :type]>
            <[AND FIND_IN_SET(:relevant_order_no,relevant_order_no)]>
            ORDER BY detail.id
        ]]></content>
    </sql>


    <sql datasource="dataSource" id="queryPrestorageStockTransferCount" >
        <content ><![CDATA[
            SELECT COUNT(*)
            FROM prestorage_stock_transfer_order_item detail
            INNER JOIN prestorage_stock_transfer_order transfer_order on detail.order_id = transfer_order.id
            INNER JOIN wh_sku sku on sku.sku = detail.sku
            WHERE 1 = 1
            <[AND create_date >= :from_create_date]>
            <[AND create_date <= :to_create_date]>
            <[AND transfer_order.id in (SELECT DISTINCT order_id FROM prestorage_stock_transfer_order_item where sku in (:skus))]>
            <[AND order_no in (:order_nos)]>
            <[AND order_no = :order_no]>
            <[AND transfer_order.id in (:ids)]>
            <[AND transfer_order.id = :id]>
            <[AND status = :status]>
            <[AND status in (:status_list)]>
            <[AND print_status = :print_status]>
            <[AND accept_person_id = :accept_person_id]>
            <[AND completed_date >= :from_completed_date]>
            <[AND completed_date <= :to_completed_date]>
            <[AND source =:source]>
            <[AND warehouse_type =:warehouse_type]>
            <[AND bind_box_no = :box_no]>
            <[AND type = :type]>
            <[AND FIND_IN_SET(:relevant_order_no,relevant_order_no)]>
        ]]></content>
    </sql>


    <sql datasource="dataSource" id="insertPrestorageStockTransferOrderDetail" >
        <content ><![CDATA[
            INSERT INTO prestorage_stock_transfer_order_item(order_id,sku,emigration_location_number,immigration_location_number,
                    sku_amount,picking_sku_amount,upload_sku_amount)
            VALUES(:order_id,:sku,:emigration_location_number,:immigration_location_number,:sku_amount,:picking_sku_amount
                    ,:upload_sku_amount)
        ]]></content>
    </sql>


    <sql datasource="dataSource" id="updatePrestorageStockTransferOrderDetail" >
        <content ><![CDATA[
            UPDATE prestorage_stock_transfer_order_item
            SET id = id
                <[,order_id = :order_id]>
                <[,sku = :sku]>
                <[,emigration_location_number = :emigration_location_number]>
                <[,immigration_location_number = :immigration_location_number]>
                <[,sku_amount = :sku_amount]>
                <[,picking_sku_amount = :picking_sku_amount]>
                <[,upload_sku_amount = :upload_sku_amount]>
            WHERE id = :id
        ]]></content>
    </sql>


    <sql datasource="dataSource" id="insertPrestorageStockTransferOrder" >
        <content ><![CDATA[
            INSERT INTO prestorage_stock_transfer_order(order_no,source,warehouse_type,type,status,print_status,create_person_id,create_date
                    ,accept_person_id,accept_date,upload_person_id,upload_date,completed_date,bind_box_no,relevant_order_no)
            VALUES(:order_no,:source,:warehouse_type,:type,:status,:print_status,:create_person_id,:create_date,:accept_person_id,
                    :accept_date,:upload_person_id,:upload_date,:completed_date,:bind_box_no,:relevant_order_no)
        ]]></content>
    </sql>

    <sql datasource="dataSource" id="updatePrestorageStockTransferOrder" >
        <content ><![CDATA[
            UPDATE prestorage_stock_transfer_order
            SET id = id
                <[,order_no = :order_no]>
                <[,source = :source]>
                <[,warehouse_type = :warehouse_type]>
                <[,type = :type]>
                <[,status = :status]>
                <[,print_status = :print_status]>
                <[,create_person_id = :create_person_id]>
                <[,create_date = :create_date]>
                <[,accept_person_id = :accept_person_id]>
                <[,accept_date = :accept_date]>
                <[,upload_person_id = :upload_person_id]>
                <[,upload_date = :upload_date]>
                <[,completed_date = :completed_date]>
                <[,bind_box_no = :box_no]>
                <[,relevant_order_no = :relevant_order_no]>
                <[,relevant_order_no = CONCAT_WS(',',relevant_order_no,:ADD_RELEVANT_ORDER_NOS)]>
            WHERE id = :id
        ]]></content>
    </sql>

</sqlmap>