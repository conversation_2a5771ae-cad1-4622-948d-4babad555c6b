<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="querySmtInventoryMonthCountCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM smt_warehouse_inventory_count
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND account_number = :account_number]>
        <[AND product_id = :product_id]>
        <[AND scitem_code = :scitem_code]>
        <[AND warehouse_name = :warehouse_name]>
        <[AND warehouse_code = :warehouse_code]>
        <[AND order_type = :order_type]>
        <[AND good_qty = :good_qty]>
        <[AND bad_qty = :bad_qty]>
        <[AND on_way_qty = :on_way_qty]>
        <[AND end_good_qty = :end_good_qty]>
        <[AND end_bad_qty = :end_bad_qty]>
        <[AND good_price = :good_price]>
        <[AND good_amount = :good_amount]>
        <[AND bad_price = :bad_price]>
        <[AND bad_amount = :bad_amount]>
        <[AND count_month = :count_month]>
        <[AND crawl_time = :crawl_time]>
        <[AND product_id IN (:product_id_list)]>
        <[AND scitem_code IN (:scitem_code_list)]>
        <[AND id IN (:idList)]>
        <[AND product_id IN (SELECT product_id FROM smt_inventory_sku_relation WHERE sku = :sku)]>
        <[AND product_id IN (SELECT product_id FROM smt_inventory_sku_relation WHERE sku IN (:skuList))]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySmtInventoryMonthCountList" >
    <content >
      <![CDATA[
        SELECT id, account_number, product_id, scitem_code, warehouse_name, warehouse_code, 
        order_type, good_qty, bad_qty, on_way_qty, end_good_qty,
        end_bad_qty, good_price, good_amount, bad_price, bad_amount, count_month, crawl_time
        FROM smt_warehouse_inventory_count
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND account_number = :account_number]>
        <[AND product_id = :product_id]>
        <[AND scitem_code = :scitem_code]>
        <[AND warehouse_name = :warehouse_name]>
        <[AND warehouse_code = :warehouse_code]>
        <[AND order_type = :order_type]>
        <[AND good_qty = :good_qty]>
        <[AND bad_qty = :bad_qty]>
        <[AND on_way_qty = :on_way_qty]>
        <[AND end_good_qty = :end_good_qty]>
        <[AND end_bad_qty = :end_bad_qty]>
        <[AND good_price = :good_price]>
        <[AND good_amount = :good_amount]>
        <[AND bad_price = :bad_price]>
        <[AND bad_amount = :bad_amount]>
        <[AND count_month = :count_month]>
        <[AND crawl_time = :crawl_time]>
        <[AND product_id IN (:product_id_list)]>
        <[AND scitem_code IN (:scitem_code_list)]>
        <[AND id IN (:idList)]>
        <[AND product_id IN (SELECT product_id FROM smt_inventory_sku_relation WHERE sku = :sku)]>
        <[AND product_id IN (SELECT product_id FROM smt_inventory_sku_relation WHERE sku IN (:skuList))]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryMonthCountAndSkuList" >
    <content >
      <![CDATA[
        SELECT s.id, s.account_number, s.product_id, s.scitem_code, s.warehouse_name, s.warehouse_code,
        s.order_type, s.good_qty, s.bad_qty, s.on_way_qty, s.end_good_qty,
        s.end_bad_qty, s.good_price, s.good_amount, s.bad_price, s.bad_amount, s.count_month, s.crawl_time
        ,r.sku,r.suit_num,r.flag,r.product_id, r.scitem_code
        FROM smt_warehouse_inventory_count s
		LEFT JOIN smt_inventory_sku_relation r ON r.product_id = s.product_id AND r.scitem_code = s.scitem_code
		INNER JOIN (
          SELECT id FROM smt_warehouse_inventory_count
          WHERE 1 = 1
          <[AND id = :id]>
          <[AND account_number = :account_number]>
          <[AND product_id = :product_id]>
          <[AND scitem_code = :scitem_code]>
          <[AND warehouse_name = :warehouse_name]>
          <[AND warehouse_code = :warehouse_code]>
          <[AND order_type = :order_type]>
          <[AND good_qty = :good_qty]>
          <[AND bad_qty = :bad_qty]>
          <[AND on_way_qty = :on_way_qty]>
          <[AND end_good_qty = :end_good_qty]>
          <[AND end_bad_qty = :end_bad_qty]>
          <[AND good_price = :good_price]>
          <[AND good_amount = :good_amount]>
          <[AND bad_price = :bad_price]>
          <[AND bad_amount = :bad_amount]>
          <[AND count_month = :count_month]>
          <[AND crawl_time = :crawl_time]>
          <[AND product_id IN (:product_id_list)]>
          <[AND scitem_code IN (:scitem_code_list)]>
          <[AND id IN (:idList)]>
          <[AND product_id IN (SELECT product_id FROM smt_inventory_sku_relation WHERE sku = :sku)]>
          <[AND product_id IN (SELECT product_id FROM smt_inventory_sku_relation WHERE sku IN (:skuList))]>
          <[:LIMIT]>
        )t on t.id = s.id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySmtInventoryMonthCountByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, account_number, product_id, scitem_code, warehouse_name, warehouse_code, 
        order_type, good_qty, bad_qty, on_way_qty, end_good_qty,
        end_bad_qty, good_price, good_amount, bad_price, bad_amount, count_month, crawl_time
        FROM smt_warehouse_inventory_count
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySmtInventoryMonthCount" >
    <content >
      <![CDATA[
        SELECT id, account_number, product_id, scitem_code, warehouse_name, warehouse_code, 
        order_type, good_qty, bad_qty, on_way_qty, end_good_qty,
        end_bad_qty, good_price, good_amount, bad_price, bad_amount, count_month, crawl_time
        FROM smt_warehouse_inventory_count
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND account_number = :account_number]>
        <[AND product_id = :product_id]>
        <[AND scitem_code = :scitem_code]>
        <[AND warehouse_name = :warehouse_name]>
        <[AND warehouse_code = :warehouse_code]>
        <[AND order_type = :order_type]>
        <[AND good_qty = :good_qty]>
        <[AND bad_qty = :bad_qty]>
        <[AND on_way_qty = :on_way_qty]>
        <[AND end_good_qty = :end_good_qty]>
        <[AND end_bad_qty = :end_bad_qty]>
        <[AND good_price = :good_price]>
        <[AND good_amount = :good_amount]>
        <[AND bad_price = :bad_price]>
        <[AND bad_amount = :bad_amount]>
        <[AND count_month = :count_month]>
        <[AND crawl_time = :crawl_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createSmtInventoryMonthCount" >
    <content >
      <![CDATA[
        INSERT INTO smt_warehouse_inventory_count (account_number, product_id, scitem_code, warehouse_name, warehouse_code, 
          order_type, good_qty, bad_qty, on_way_qty,
          end_good_qty, end_bad_qty, good_price, good_amount, bad_price, bad_amount, 
          count_month, crawl_time)
        VALUES (:account_number, :product_id, :scitem_code, :warehouse_name, :warehouse_code, 
          :order_type, :good_qty, :bad_qty, :on_way_qty,
          :end_good_qty, :end_bad_qty, :good_price, :good_amount, :bad_price, :bad_amount, 
          :count_month, :crawl_time)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteSmtInventoryMonthCountByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM smt_warehouse_inventory_count
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateSmtInventoryMonthCountByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE smt_warehouse_inventory_count
        SET <[account_number = :account_number,]>
          <[product_id = :product_id,]>
          <[scitem_code = :scitem_code,]>
          <[warehouse_name = :warehouse_name,]>
          <[warehouse_code = :warehouse_code,]>
          <[order_type = :order_type,]>
          <[good_qty = :good_qty,]>
          <[bad_qty = :bad_qty,]>
          <[on_way_qty = :on_way_qty,]>
          <[end_good_qty = :end_good_qty,]>
          <[end_bad_qty = :end_bad_qty,]>
          <[good_price = :good_price,]>
          <[good_amount = :good_amount,]>
          <[bad_price = :bad_price,]>
          <[bad_amount = :bad_amount,]>
          <[count_month = :count_month,]>
          <[crawl_time = :crawl_time,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryMonthlyCount" >
    <content >
      <![CDATA[
        SELECT
            account_number,
            product_id,
            scitem_code,
            warehouse_name,
            warehouse_code,
            DATE_FORMAT(gmt_create, '%Y-%m')count_month,
            order_type,
            SUM(IF( inventory_type = '1', IFNULL( change_quantity, 0 ), 0 )) good_qty,
            SUM(IF( inventory_type = '101', IFNULL( change_quantity, 0 ), 0 )) bad_qty,
            crawl_time
        FROM
            `smt_warehouse_inventory`
            WHERE 1=1
            <[AND account_number = :account_number]>
            <[AND gmt_create >= :gmt_create_start]>
            <[AND gmt_create <= :gmt_create_end]>
        GROUP BY
            account_number,
            warehouse_code,
            product_id
      ]]>
    </content>
  </sql>
</sqlmap>