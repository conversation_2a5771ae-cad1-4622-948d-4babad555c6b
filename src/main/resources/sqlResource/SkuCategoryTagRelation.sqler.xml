<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="querySkuCategoryTagRelationCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM sku_category_tag_relation
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND category_path = :category_path]>
        <[AND tag = :tag]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySkuCategoryTagRelationList" >
    <content >
      <![CDATA[
        SELECT id, category_path, tag
        FROM sku_category_tag_relation
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND category_path = :category_path]>
        <[AND tag = :tag]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySkuCategoryTagRelationByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, category_path, tag
        FROM sku_category_tag_relation
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySkuCategoryTagRelation" >
    <content >
      <![CDATA[
        SELECT id, category_path, tag
        FROM sku_category_tag_relation
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND category_path = :category_path]>
        <[AND tag = :tag]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createSkuCategoryTagRelation" >
    <content >
      <![CDATA[
        INSERT INTO sku_category_tag_relation (category_path, tag)
        VALUES (:category_path, :tag)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteSkuCategoryTagRelationByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM sku_category_tag_relation
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateSkuCategoryTagRelationByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE sku_category_tag_relation
        SET <[category_path = :category_path,]>
          <[tag = :tag,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>