<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryCePhotoTaskItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM ce_photo_task_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND sku = :sku]>
        <[AND name = :name]>
        <[AND location_number = :location_number]>
        <[AND outsourcing_image = :outsourcing_image]>
        <[AND warehouse_image = :warehouse_image]>
        <[AND last_update_by = :last_update_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND status = :status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryCePhotoTaskItemList" >
    <content >
      <![CDATA[
        SELECT id, task_id, sku, name, location_number, outsourcing_image, warehouse_image,
        last_update_by, last_update_date, status
        FROM ce_photo_task_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND sku = :sku]>
        <[AND name = :name]>
        <[AND location_number = :location_number]>
        <[AND outsourcing_image = :outsourcing_image]>
        <[AND warehouse_image = :warehouse_image]>
        <[AND last_update_by = :last_update_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND status = :status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryCePhotoTaskItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, task_id, sku, name, location_number, outsourcing_image, warehouse_image,
        last_update_by, last_update_date, status
        FROM ce_photo_task_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryCePhotoTaskItem" >
    <content >
      <![CDATA[
        SELECT id, task_id, sku, name, location_number, outsourcing_image, warehouse_image,
        last_update_by, last_update_date, status
        FROM ce_photo_task_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND sku = :sku]>
        <[AND name = :name]>
        <[AND location_number = :location_number]>
        <[AND outsourcing_image = :outsourcing_image]>
        <[AND warehouse_image = :warehouse_image]>
        <[AND last_update_by = :last_update_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND status = :status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createCePhotoTaskItem" >
    <content >
      <![CDATA[
        INSERT INTO ce_photo_task_item (task_id, sku, name, location_number, outsourcing_image,
          warehouse_image, last_update_by, last_update_date, status,ce_id)
        VALUES (:task_id, :sku, :name, :location_number, :outsourcing_image,
          :warehouse_image, :last_update_by, :last_update_date, :status, :ce_id)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteCePhotoTaskItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM ce_photo_task_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateCePhotoTaskItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE ce_photo_task_item
        SET <[task_id = :task_id,]>
          <[sku = :sku,]>
          <[name = :name,]>
          <[location_number = :location_number,]>
          <[outsourcing_image = :outsourcing_image,]>
          <[warehouse_image = :warehouse_image,]>
          <[last_update_by = :last_update_by,]>
          <[last_update_date = :last_update_date,]>
          <[status = :status,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>