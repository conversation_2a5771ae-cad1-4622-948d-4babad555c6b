<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhAbroadPacExpandCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_abroad_pac_expand
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_no = :return_no]>
        <[AND apv_no = :apv_no]>
        <[AND uuid_sku = :uuid_sku]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAbroadPacExpandList" >
    <content >
      <![CDATA[
        SELECT id, return_no, apv_no, uuid_sku, creation_date, last_update_date
        FROM wh_abroad_pac_expand
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_no = :return_no]>
        <[AND apv_no = :apv_no]>
        <[AND uuid_sku = :uuid_sku]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAbroadPacExpandByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, return_no, apv_no, uuid_sku, creation_date, last_update_date
        FROM wh_abroad_pac_expand
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAbroadPacExpand" >
    <content >
      <![CDATA[
        SELECT id, return_no, apv_no, uuid_sku, creation_date, last_update_date
        FROM wh_abroad_pac_expand
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_no = :return_no]>
        <[AND apv_no = :apv_no]>
        <[AND uuid_sku = :uuid_sku]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhAbroadPacExpand" >
    <content >
      <![CDATA[
        INSERT INTO wh_abroad_pac_expand (return_no, apv_no, uuid_sku, creation_date, last_update_date)
        VALUES (:return_no, :apv_no, :uuid_sku, :creation_date, :last_update_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhAbroadPacExpandByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_abroad_pac_expand
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhAbroadPacExpandByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_abroad_pac_expand
        SET <[return_no = :return_no,]>
          <[apv_no = :apv_no,]>
          <[uuid_sku = :uuid_sku,]>
          <[creation_date = :creation_date,]>
          <[last_update_date = :last_update_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>