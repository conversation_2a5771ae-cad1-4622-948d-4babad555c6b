<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
    <sql datasource="dataSource" id="queryPrestorageStockCount" >
        <content ><![CDATA[
            SELECT COUNT(DISTINCT sku)
            FROM wh_stock
            WHERE FIND_IN_SET(1,location_tag)
            <[:PASSAGE_CONDITION]>
            <[AND sku in (SELECT sku FROM wh_sku WHERE id in (:skuIds))]>
            <[AND sku in (:skus)]>
            <[AND sku in (SELECT sku FROM wh_sku_sale_statistic_record WHERE sale_attribute in (:saleAttributes))]>
            <[AND sku in (SELECT sku FROM wh_sku WHERE status in (:singularStatus))]>
            <[AND location_number in (:locations)]>
            <[AND location_number in (SELECT location FROM wh_location WHERE location_region in (:regions))]>
        ]]></content>
    </sql>

    <sql datasource="dataSource" id="queryPrestorageStocks" >
        <content ><![CDATA[
            SELECT s.id AS sku_id,T1.sku ,s.name,s.status AS singular_status,T1.location_number AS location,l.location_type AS type,
                    T1.surplus_quantity AS stock_amount,r.sale_attribute_setting_str AS sale_attribute,
                    IFNULL(r.thirty_days_sales_days,0) AS sale_days,IFNULL(r.thirty_days_sales_orders,0) AS sale_orders,
                    (SELECT SUM(surplus_quantity) FROM wh_transfer_stock WHERE stock_id = T1.id) AS  transfer_stock_amount
            FROM wh_stock T1
            LEFT JOIN wh_location l ON l.location = T1.location_number
            LEFT JOIN wh_sku s ON s.sku = T1.sku
            LEFT JOIN wh_sku_sale_statistic_record r ON r.sku = T1.sku
            INNER JOIN(
                SELECT DISTINCT sku
                FROM wh_stock
                WHERE FIND_IN_SET(1,location_tag)
                <[:PASSAGE_CONDITION]>
                <[AND sku in (SELECT sku FROM wh_sku WHERE id in (:skuIds))]>
                <[AND sku in (:skus)]>
                <[AND sku in (SELECT sku FROM wh_sku_sale_statistic_record WHERE sale_attribute in (:saleAttributes))]>
                <[AND sku in (SELECT sku FROM wh_sku WHERE status in (:singularStatus))]>
                <[AND location_number in (:locations)]>
                <[AND location_number in (SELECT location FROM wh_location WHERE location_region in (:regions))]>
                order by sku
                <[:SKU_LIMIT]>
            )t ON t.sku = T1.sku
            WHERE l.location_type in (1,5)
            <[AND T1.location_number in (:locations)]>
            <[AND T1.location_number in (SELECT location FROM wh_location WHERE location_region in (:regions))]>
        ]]></content>
    </sql>

    <sql datasource="dataSource" id="queryStatisticPrestorageStock" >
        <content ><![CDATA[
        SELECT  SUM(sku_picking_total_stock) AS picking_total_stock,
				SUM(sku_prestorage_total_stock) AS prestorage_total_stock,
                SUM(sku_sale_days) AS sale_days,
                SUM(sku_sale_orders) AS sale_orders,
                SUM(sku_total_stock) AS  total_stock
        FROM (
            SELECT  SUM(IF(l.location_type = 1,IFNULL(T1.surplus_quantity,0) + IFNULL((SELECT SUM(surplus_quantity) FROM wh_transfer_stock WHERE stock_id = T1.id),0),0)) AS sku_picking_total_stock,
				    SUM(IF(l.location_type = 5,IFNULL(T1.surplus_quantity,0) + IFNULL((SELECT SUM(surplus_quantity) FROM wh_transfer_stock WHERE stock_id = T1.id),0),0)) AS sku_prestorage_total_stock,
                    IFNULL(r.thirty_days_sales_days,0) AS sku_sale_days,
					IFNULL(r.thirty_days_sales_orders,0) AS sku_sale_orders,
                    SUM(IFNULL(T1.surplus_quantity,0) + IFNULL((SELECT SUM(surplus_quantity) FROM wh_transfer_stock WHERE stock_id = T1.id),0)) AS  sku_total_stock
            FROM wh_stock T1
            LEFT JOIN wh_location l ON l.location = T1.location_number
            LEFT JOIN wh_sku s ON s.sku = T1.sku
            LEFT JOIN wh_sku_sale_statistic_record r ON r.sku = T1.sku
            INNER JOIN(
                SELECT DISTINCT sku
                FROM wh_stock
                WHERE FIND_IN_SET(1,location_tag)
                <[:PASSAGE_CONDITION]>
                <[AND sku in (SELECT sku FROM wh_sku WHERE id in (:skuIds))]>
                <[AND sku in (:skus)]>
                <[AND sku in (SELECT sku FROM wh_sku_sale_statistic_record WHERE sale_attribute in (:saleAttributes))]>
                <[AND sku in (SELECT sku FROM wh_sku WHERE status in (:singularStatus))]>
                <[AND location_number in (:locations)]>
                <[AND location_number in (SELECT location FROM wh_location WHERE location_region in (:regions))]>
                order by sku
                <[:SKU_LIMIT]>
            )t ON t.sku = T1.sku
            WHERE l.location_type in (1,5)
            <[AND T1.location_number in (:locations)]>
            <[AND T1.location_number in (SELECT location FROM wh_location WHERE location_region in (:regions))]>
            group by T1.sku
        ) statistic
        ]]></content>
    </sql>
</sqlmap>