<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhRulePickingCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_rule_picking
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND picking_area = :picking_area]>
        <[AND picking_aisle = :picking_aisle]>
        <[AND merge_quantity = :merge_quantity]>
        <[AND picking_by = :picking_by]>
        <[AND create_by = :create_by]>
        <[AND created_date = :created_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhRulePickingList" >
    <content >
      <![CDATA[
        SELECT id, picking_area, picking_aisle, picking_by, merge_quantity, create_by, created_date, last_update_by, last_update_date
        FROM wh_rule_picking
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND picking_area = :picking_area]>
        <[AND picking_aisle = :picking_aisle]>
        <[AND merge_quantity = :merge_quantity]>
        <[AND picking_by = :picking_by]>
        <[AND create_by = :create_by]>
        <[AND created_date = :created_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhRulePickingByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, picking_area, picking_aisle, merge_quantity, picking_by, create_by, created_date, last_update_by, last_update_date
        FROM wh_rule_picking
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhRulePicking" >
    <content >
      <![CDATA[
        SELECT id, picking_area, picking_aisle, merge_quantity, picking_by, create_by, created_date, last_update_by, last_update_date
        FROM wh_rule_picking
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND picking_area = :picking_area]>
        <[AND picking_aisle = :picking_aisle]>
        <[AND merge_quantity = :merge_quantity]>
        <[AND picking_by = :picking_by]>
        <[AND create_by = :create_by]>
        <[AND created_date = :created_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhRulePicking" >
    <content >
      <![CDATA[
        INSERT INTO wh_rule_picking (picking_area, picking_aisle, merge_quantity, picking_by, create_by, created_date, last_update_by, 
          last_update_date)
        VALUES (:picking_area, :picking_aisle, :merge_quantity, :picking_by, :create_by, :created_date, :last_update_by, 
          :last_update_date)
      ]]>
    </content>
  </sql>
  
  
  
  <sql datasource="dataSource" id="updateWhRulePickingByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_rule_picking
        SET 
          <[picking_area = :picking_area,]>
          <[picking_aisle = :picking_aisle,]>
          <[merge_quantity = :merge_quantity,]>
          picking_by = :picking_by,
          <[create_by = :create_by,]>
          <[created_date = :created_date,]>
          <[last_update_by = :last_update_by,]>
          <[last_update_date = :last_update_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhRulePickingByPickingBy" >
    <content >
      <![CDATA[
        DELETE FROM wh_rule_picking
        WHERE 1 = 1
        AND picking_area = :picking_area
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhRulePickingByPickingAisle" >
    <content >
      <![CDATA[
        DELETE FROM wh_rule_picking
        WHERE 1 = 1
        AND picking_aisle = :picking_aisle
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPageWhRulePickingList" >
    <content >
      <![CDATA[
        SELECT 
        	id, 
        	picking_area, 
        	GROUP_CONCAT(picking_aisle) AS picking_aisle,
        	merge_quantity, 
        	picking_by, 
        	create_by, 
        	created_date, 
        	last_update_by, 
        	last_update_date
        FROM 
        	wh_rule_picking t1
        WHERE 
        	1 = 1
	        <[AND id = :id]>
	        <[AND picking_area = :picking_area]>
	        <[AND picking_aisle = :picking_aisle]>
	        <[AND merge_quantity = :merge_quantity]>
	        <[AND picking_by = :picking_by]>
	        <[AND create_by = :create_by]>
	        <[AND created_date = :created_date]>
	        <[AND last_update_by = :last_update_by]>
	        <[AND last_update_date = :last_update_date]>
	        
	        <[AND t1.picking_area in (SELECT t2.picking_area FROM wh_rule_picking t2 WHERE t1.picking_area=t2.picking_area AND t2.picking_aisle IN (:picking_aisle_list))]>
        
        GROUP BY picking_area
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPickCountAlreadyCount" >
    <content >
      <![CDATA[
        SELECT 
        	receive_person AS pickBy, 
        	COUNT(1) AS taskCount 
        FROM 
        	wh_picking_task 
        WHERE 
        	task_type IN (13,14) 
        	AND task_status IN (0,1) 
        	AND receive_person IN (:pick_by_list) 
        	AND receive_person IS NOT NULL
        GROUP BY receive_person
      ]]>
    </content>
  </sql>
  
</sqlmap>