<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhScanShipmentToApvCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_scan_shipment_to_apv
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND scan_shipment_id = :scan_shipment_id]>
        <[AND scan_shipment_id in (:scanShipmentIdList)]>
        <[AND apv_no = :apv_no]>
        <[AND apv_no IN (:apvNoList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhScanShipmentToApvList" >
    <content >
      <![CDATA[
        SELECT id, scan_shipment_id, apv_no, ship_code, lazbao_box_no
        FROM wh_scan_shipment_to_apv
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND scan_shipment_id = :scan_shipment_id]>
        <[AND scan_shipment_id in (:scanShipmentIdList)]>
        <[AND apv_no = :apv_no]>
        <[AND apv_no IN (:apvNoList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhScanShipmentToApvByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, scan_shipment_id, apv_no, ship_code, lazbao_box_no
        FROM wh_scan_shipment_to_apv
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhScanShipmentToApv" >
    <content >
      <![CDATA[
        SELECT id, scan_shipment_id, apv_no, ship_code, lazbao_box_no
        FROM wh_scan_shipment_to_apv
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND scan_shipment_id = :scan_shipment_id]>
        <[AND apv_no = :apv_no]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhScanShipmentToApv" >
    <content >
      <![CDATA[
        INSERT INTO wh_scan_shipment_to_apv (scan_shipment_id, apv_no, ship_code, lazbao_box_no)
        VALUES (:scan_shipment_id, :apv_no, :ship_code, :lazbao_box_no)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhScanShipmentToApvByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_scan_shipment_to_apv
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhScanShipmentToApvByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_scan_shipment_to_apv
        SET <[scan_shipment_id = :scan_shipment_id,]>
          <[apv_no = :apv_no,]>
          <[ship_code = :ship_code,]>
          <[lazbao_box_no = :lazbao_box_no,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>