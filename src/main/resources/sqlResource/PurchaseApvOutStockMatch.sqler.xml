<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryPurchaseApvOutStockMatchCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM purchase_apv_out_stock_match
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_no = :apv_no]>
        <[AND sales_record_number = :sales_record_number]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND sku = :sku]>
        <[AND status = :status]>
        <[AND stock_id = :stock_id]>
        <[AND quantity = :quantity]>
        <[AND check_in_qty = :check_in_qty]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND sku IN (:skuList)]>
        <[AND purchase_order_no IN (:purchaseOrderNoList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPurchaseApvOutStockMatchList" >
    <content >
      <![CDATA[
        SELECT id, apv_no, sales_record_number, purchase_order_no, sku, status, stock_id, 
        quantity, check_in_qty, creation_date, last_update_date, sync_logistics_time, deliver_method
        FROM purchase_apv_out_stock_match
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_no = :apv_no]>
        <[AND sales_record_number = :sales_record_number]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND sku = :sku]>
        <[AND status = :status]>
        <[AND stock_id = :stock_id]>
        <[AND quantity = :quantity]>
        <[AND check_in_qty = :check_in_qty]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND sku IN (:skuList)]>
        <[AND purchase_order_no IN (:purchaseOrderNoList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPurchaseApvOutStockMatchByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, apv_no, sales_record_number, purchase_order_no, sku, status, stock_id, 
        quantity, check_in_qty, creation_date, last_update_date, sync_logistics_time, deliver_method
        FROM purchase_apv_out_stock_match
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPurchaseApvOutStockMatch" >
    <content >
      <![CDATA[
        SELECT id, apv_no, sales_record_number, purchase_order_no, sku, status, stock_id, 
        quantity, check_in_qty, creation_date, last_update_date, sync_logistics_time, deliver_method
        FROM purchase_apv_out_stock_match
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_no = :apv_no]>
        <[AND sales_record_number = :sales_record_number]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND sku = :sku]>
        <[AND status = :status]>
        <[AND stock_id = :stock_id]>
        <[AND quantity = :quantity]>
        <[AND check_in_qty = :check_in_qty]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createPurchaseApvOutStockMatch" >
    <content >
      <![CDATA[
        INSERT INTO purchase_apv_out_stock_match (apv_no, sales_record_number, purchase_order_no, sku, status, stock_id, 
          quantity, check_in_qty, creation_date, last_update_date, sync_logistics_time, deliver_method)
        VALUES (:apv_no, :sales_record_number, :purchase_order_no, :sku, :status, :stock_id, 
          :quantity, :check_in_qty, :creation_date, :last_update_date, :sync_logistics_time, :deliver_method)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deletePurchaseApvOutStockMatchByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM purchase_apv_out_stock_match
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updatePurchaseApvOutStockMatchByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE purchase_apv_out_stock_match
        SET <[apv_no = :apv_no,]>
          <[sales_record_number = :sales_record_number,]>
          <[purchase_order_no = :purchase_order_no,]>
          <[sku = :sku,]>
          <[status = :status,]>
          <[stock_id = :stock_id,]>
          <[quantity = :quantity,]>
          <[check_in_qty = :check_in_qty,]>
          <[creation_date = :creation_date,]>
          <[last_update_date = :last_update_date,]>
          <[sync_logistics_time = :sync_logistics_time,]>
          <[deliver_method = :deliver_method,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>