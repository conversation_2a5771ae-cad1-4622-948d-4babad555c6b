<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryRfoPickingTaskItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_rfo_picking_task_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND rfo_id = :rfo_id]>
        <[AND created_date = :created_date]>
        <[AND create_by = :create_by]>
        <[AND status = :status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryRfoPickingTaskItemList" >
    <content >
      <![CDATA[
        SELECT id, task_id, rfo_id, created_date, create_by, status
        FROM wh_rfo_picking_task_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND rfo_id = :rfo_id]>
        <[AND created_date = :created_date]>
        <[AND create_by = :create_by]>
        <[AND status = :status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryRfoPickingTaskItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, task_id, rfo_id, created_date, create_by, status
        FROM wh_rfo_picking_task_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryRfoPickingTaskItem" >
    <content >
      <![CDATA[
        SELECT id, task_id, rfo_id, created_date, create_by, status
        FROM wh_rfo_picking_task_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND rfo_id = :rfo_id]>
        <[AND created_date = :created_date]>
        <[AND create_by = :create_by]>
        <[AND status = :status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createRfoPickingTaskItem" >
    <content >
      <![CDATA[
        INSERT INTO wh_rfo_picking_task_item (task_id, rfo_id, created_date, create_by, status)
        VALUES (:task_id, :rfo_id, :created_date, :create_by, :status)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteRfoPickingTaskItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_rfo_picking_task_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateRfoPickingTaskItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_rfo_picking_task_item
        SET <[task_id = :task_id,]>
          <[rfo_id = :rfo_id,]>
          <[created_date = :created_date,]>
          <[create_by = :create_by,]>
          <[status = :status,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>