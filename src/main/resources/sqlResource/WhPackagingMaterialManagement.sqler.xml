<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhPackagingMaterialManagementCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_packaging_material_management
        <[:STOCK_TABLE]>
        WHERE 1 = 1
        <[AND wh_packaging_material_management.id = :id]>
        <[AND wh_packaging_material_management.id IN (:ids)]>
        <[:ALERT_STOCK]>
        <[AND wh_packaging_material_management.material_article_number = :material_article_number]>
        <[AND wh_packaging_material_management.material_article_number IN (:materialArticleNumbers)]>
        <[AND sku = :sku]>
        <[AND wh_packaging_material_management.name = :name]>
        <[AND wh_packaging_material_management.name LIKE :like_name]>
        <[AND length = :length]>
        <[AND width = :width]>
        <[AND height = :height]>
        <[AND weight = :weight]>
        <[AND unit = :unit]>
        <[AND price = :price]>
        <[AND image_url = :image_url]>
        <[AND type = :type]>
        <[AND status = :status]>
        <[AND box_specification = :box_specification]>
        <[AND use_link = :use_link]>
        <[AND use_link LIKE :userLink]>
        <[AND quantity = :quantity]>
        <[AND wh_packaging_material_management.create_by = :create_by]>
        <[AND wh_packaging_material_management.create_date = :create_date]>
        <[AND wh_packaging_material_management.last_update_by = :last_update_by]>
        <[AND wh_packaging_material_management.last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhPackagingMaterialManagementList" >
    <content >
      <![CDATA[
        SELECT wh_packaging_material_management.id, wh_packaging_material_management.material_article_number, sku, wh_packaging_material_management.name,
         length, width, height, weight, unit, price, image_url, type, status, box_specification, use_link, quantity, wh_packaging_material_management.create_by,
          wh_packaging_material_management.create_date, wh_packaging_material_management.last_update_by,wh_packaging_material_management.last_update_date, remark,
           stock_threshold, sale_weight
        <[:STOCK_QUERY_COLUMNS]>
        FROM wh_packaging_material_management
        <[:STOCK_TABLE]>
        WHERE 1 = 1
        <[AND wh_packaging_material_management.id = :id]>
        <[AND wh_packaging_material_management.id IN (:ids)]>
        <[:ALERT_STOCK]>
        <[AND wh_packaging_material_management.material_article_number = :material_article_number]>
        <[AND wh_packaging_material_management.material_article_number IN (:materialArticleNumbers)]>
        <[AND sku = :sku]>
        <[AND wh_packaging_material_management.name = :name]>
        <[AND wh_packaging_material_management.name LIKE :like_name]>
        <[AND length = :length]>
        <[AND width = :width]>
        <[AND height = :height]>
        <[AND weight = :weight]>
        <[AND unit = :unit]>
        <[AND price = :price]>
        <[AND image_url = :image_url]>
        <[AND type = :type]>
        <[AND status = :status]>
        <[AND box_specification = :box_specification]>
        <[AND use_link = :use_link]>
        <[AND use_link LIKE :userLink]>
        <[AND quantity = :quantity]>
        <[AND wh_packaging_material_management.create_by = :create_by]>
        <[AND wh_packaging_material_management.create_date = :create_date]>
        <[AND wh_packaging_material_management.last_update_by = :last_update_by]>
        <[AND wh_packaging_material_management.last_update_date = :last_update_date]>
        <[AND wh_packaging_material_management.material_article_number NOT IN (:NOT_MATERIAL_ARTICLE_NUMBERS)]>
        ORDER BY create_date DESC
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhPackagingMaterialManagementByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, material_article_number, sku, name, length, width, height, weight, unit, 
        price, image_url, type, status, box_specification, use_link, quantity, create_by, create_date, last_update_by,
        last_update_date, remark,stock_threshold, sale_weight
        FROM wh_packaging_material_management
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhPackagingMaterialManagement" >
    <content >
      <![CDATA[
        SELECT id, material_article_number, sku, name, length, width, height, weight, unit, 
        price, image_url, type, status, box_specification, use_link, quantity, create_by, create_date, last_update_by,
        last_update_date, remark,stock_threshold, sale_weight
        FROM wh_packaging_material_management
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND material_article_number = :material_article_number]>
        <[AND sku = :sku]>
        <[AND name = :name]>
        <[AND name LIKE :like_name]>
        <[AND length = :length]>
        <[AND width = :width]>
        <[AND height = :height]>
        <[AND weight = :weight]>
        <[AND unit = :unit]>
        <[AND price = :price]>
        <[AND image_url = :image_url]>
        <[AND type = :type]>
        <[AND status = :status]>
        <[AND box_specification = :box_specification]>
        <[AND use_link = :use_link]>
        <[AND quantity = :quantity]>
        <[AND create_by = :create_by]>
        <[AND create_date = :create_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhPackagingMaterialManagement" >
    <content >
      <![CDATA[
        INSERT INTO wh_packaging_material_management (material_article_number, sku, name, length, width, height, weight, 
          unit, price, image_url, type, status, box_specification, use_link, quantity, create_by, create_date,
          last_update_by, last_update_date, remark,stock_threshold, sale_weight)
        VALUES (:material_article_number, :sku, :name, :length, :width, :height, :weight, 
          :unit, :price, :image_url, :type, :status, :box_specification, :use_link , :quantity, :create_by, :create_date,
          :last_update_by, :last_update_date, :remark,:stock_threshold, :sale_weight)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhPackagingMaterialManagementByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_packaging_material_management
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhPackagingMaterialManagementByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_packaging_material_management
        SET <[material_article_number = :material_article_number,]>
          <[sku = :sku,]>
          <[name = :name,]>
          <[length = :length,]>
          <[width = :width,]>
          <[height = :height,]>
          <[weight = :weight,]>
          <[sale_weight = :sale_weight,]>
          <[unit = :unit,]>
          <[price = :price,]>
          <[type = :type,]>
          <[status = :status,]>
          <[box_specification = :box_specification,]>
          <[use_link = :use_link,]>
          <[quantity = :quantity,]>
          <[create_by = :create_by,]>
          <[create_date = :create_date,]>
          <[last_update_by = :last_update_by,]>
          <[last_update_date = :last_update_date,]>
          <[remark = :remark,]>
          <[stock_threshold = :stock_threshold,]>
          image_url = :image_url,
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="queryPackagingMaterialForCheckIn" >
    <content >
      <![CDATA[
        SELECT id, material_article_number, sku, name, length, width, height, weight, unit,
        price, image_url, type, status, box_specification, use_link, quantity, create_by, create_date, last_update_by,
        last_update_date, remark,stock_threshold, sale_weight
        FROM
            wh_packaging_material_management
        WHERE
            `status` = 1
            AND length IS NOT NULL
            AND width IS NOT NULL
            AND `name` REGEXP '封口袋|背心袋'
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="getMaterialMessageList" >
    <content >
      <![CDATA[
        SELECT m.id, m.material_article_number,m.name, m.length,m.width, m.height, m.weight, m.unit,
        m.price, m.image_url, m.type, m.status, m.box_specification, m.use_link, m.quantity, m.remark,
        m.stock_threshold,s.on_way_quantity,s.surplus_quantity
        FROM
            wh_packaging_material_management m
        LEFT JOIN
            wh_packaging_material_stock s
        ON m.material_article_number=s.material_article_number
        WHERE 1=1
          <[AND m.id in (:ids)]>
      ]]>
    </content>
  </sql>
</sqlmap>