<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhApvAllocationItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_apv_allocation_item
        WHERE 1 = 1
        <[AND allocation_item_id = :allocation_item_id]>
        <[AND allocation_id = :allocation_id]>
        <[AND allocation_no = :allocation_no]>
        <[AND sku = :sku]>
        <[AND stock_id = :stock_id]>
        <[AND json_str = :json_str]>
        <[AND sku_name = :sku_name]>
        <[AND allocation_num = :allocation_num]>
        <[AND create_by = :create_by]>
        <[AND create_time = :create_time]>
        <[AND pick_status = :pick_status]>
        <[AND pick_num = :pick_num]>
        <[AND pick_by = :pick_by]>
        <[AND pick_time = :pick_time]>
        <[AND history_pick_num = :history_pick_num]>
        <[AND box_status = :box_status]>
        <[AND box_num = :box_num]>
        <[AND box_no = :box_no]>
        <[AND box_by = :box_by]>
        <[AND box_time = :box_time]>
        <[AND board_status = :board_status]>
        <[AND board_no = :board_no]>
        <[AND board_by = :board_by]>
        <[AND board_time = :board_time]>
        <[AND load_status = :load_status]>
        <[AND load_by = :load_by]>
        <[AND load_time = :load_time]>
        <[AND up_status = :up_status]>
        <[AND up_num = :up_num]>
        <[AND up_by = :up_by]>
        <[AND up_time = :up_time]>
        <[AND put_status = :put_status]>
        <[AND put_by = :put_by]>
        <[AND put_time = :put_time]>
        <[AND is_audit = :is_audit]>
        <[AND inventory_status = :inventory_status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhApvAllocationItem2List" >
    <content >
      <![CDATA[
        SELECT allocation_item_id, allocation_id, allocation_no, sku, stock_id, json_str, 
        sku_name, allocation_num, create_by, create_time, pick_status, pick_num, pick_by, 
        pick_time, history_pick_num, box_status, box_num, box_no, box_by, box_time, board_status, 
        board_no, board_by, board_time, load_status, load_by, load_time, up_status, up_num, 
        up_by, up_time, put_status, put_by, put_time, is_audit, inventory_status
        FROM wh_apv_allocation_item
        WHERE 1 = 1
        <[AND allocation_item_id = :allocation_item_id]>
        <[AND allocation_id = :allocation_id]>
        <[AND allocation_no = :allocation_no]>
        <[AND sku = :sku]>
        <[AND stock_id = :stock_id]>
        <[AND json_str = :json_str]>
        <[AND sku_name = :sku_name]>
        <[AND allocation_num = :allocation_num]>
        <[AND create_by = :create_by]>
        <[AND create_time = :create_time]>
        <[AND pick_status = :pick_status]>
        <[AND pick_num = :pick_num]>
        <[AND pick_by = :pick_by]>
        <[AND pick_time = :pick_time]>
        <[AND history_pick_num = :history_pick_num]>
        <[AND box_status = :box_status]>
        <[AND box_num = :box_num]>
        <[AND box_no = :box_no]>
        <[AND box_by = :box_by]>
        <[AND box_time = :box_time]>
        <[AND board_status = :board_status]>
        <[AND board_no = :board_no]>
        <[AND board_by = :board_by]>
        <[AND board_time = :board_time]>
        <[AND load_status = :load_status]>
        <[AND load_by = :load_by]>
        <[AND load_time = :load_time]>
        <[AND up_status = :up_status]>
        <[AND up_num = :up_num]>
        <[AND up_by = :up_by]>
        <[AND up_time = :up_time]>
        <[AND put_status = :put_status]>
        <[AND put_by = :put_by]>
        <[AND put_time = :put_time]>
        <[AND is_audit = :is_audit]>
        <[AND inventory_status = :inventory_status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhApvAllocationItemById" >
    <content >
      <![CDATA[
        SELECT allocation_item_id, allocation_id, allocation_no, sku, stock_id, json_str, 
        sku_name, allocation_num, create_by, create_time, pick_status, pick_num, pick_by, 
        pick_time, history_pick_num, box_status, box_num, box_no, box_by, box_time, board_status, 
        board_no, board_by, board_time, load_status, load_by, load_time, up_status, up_num, 
        up_by, up_time, put_status, put_by, put_time, is_audit, inventory_status
        FROM wh_apv_allocation_item
        WHERE 1 = 1
        AND allocation_item_id = :allocation_item_id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhApvAllocationItem2" >
    <content >
      <![CDATA[
        SELECT allocation_item_id, allocation_id, allocation_no, sku, stock_id, json_str, 
        sku_name, allocation_num, create_by, create_time, pick_status, pick_num, pick_by, 
        pick_time, history_pick_num, box_status, box_num, box_no, box_by, box_time, board_status, 
        board_no, board_by, board_time, load_status, load_by, load_time, up_status, up_num, 
        up_by, up_time, put_status, put_by, put_time, is_audit, inventory_status
        FROM wh_apv_allocation_item
        WHERE 1 = 1
        <[AND allocation_item_id = :allocation_item_id]>
        <[AND allocation_id = :allocation_id]>
        <[AND allocation_no = :allocation_no]>
        <[AND sku = :sku]>
        <[AND stock_id = :stock_id]>
        <[AND json_str = :json_str]>
        <[AND sku_name = :sku_name]>
        <[AND allocation_num = :allocation_num]>
        <[AND create_by = :create_by]>
        <[AND create_time = :create_time]>
        <[AND pick_status = :pick_status]>
        <[AND pick_num = :pick_num]>
        <[AND pick_by = :pick_by]>
        <[AND pick_time = :pick_time]>
        <[AND history_pick_num = :history_pick_num]>
        <[AND box_status = :box_status]>
        <[AND box_num = :box_num]>
        <[AND box_no = :box_no]>
        <[AND box_by = :box_by]>
        <[AND box_time = :box_time]>
        <[AND board_status = :board_status]>
        <[AND board_no = :board_no]>
        <[AND board_by = :board_by]>
        <[AND board_time = :board_time]>
        <[AND load_status = :load_status]>
        <[AND load_by = :load_by]>
        <[AND load_time = :load_time]>
        <[AND up_status = :up_status]>
        <[AND up_num = :up_num]>
        <[AND up_by = :up_by]>
        <[AND up_time = :up_time]>
        <[AND put_status = :put_status]>
        <[AND put_by = :put_by]>
        <[AND put_time = :put_time]>
        <[AND is_audit = :is_audit]>
        <[AND inventory_status = :inventory_status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhApvAllocationItem" >
    <content >
      <![CDATA[
        INSERT INTO wh_apv_allocation_item (allocation_id, allocation_no, sku, stock_id, json_str, sku_name, allocation_num, 
          create_by, create_time, pick_status, pick_num, pick_by, pick_time, history_pick_num, 
          box_status, box_num, box_no, box_by, box_time, board_status, board_no, board_by, 
          board_time, load_status, load_by, load_time, up_status, up_num, up_by, up_time, 
          put_status, put_by, put_time, is_audit, inventory_status)
        VALUES (:allocation_id, :allocation_no, :sku, :stock_id, :json_str, :sku_name, :allocation_num, 
          :create_by, :create_time, :pick_status, :pick_num, :pick_by, :pick_time, :history_pick_num, 
          :box_status, :box_num, :box_no, :box_by, :box_time, :board_status, :board_no, :board_by, 
          :board_time, :load_status, :load_by, :load_time, :up_status, :up_num, :up_by, :up_time, 
          :put_status, :put_by, :put_time, :is_audit, :inventory_status)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhApvAllocationItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_apv_allocation_item
        WHERE 1 = 1
        AND allocation_item_id = :allocation_item_id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhApvAllocationItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_apv_allocation_item
        SET <[allocation_id = :allocation_id,]>
          <[allocation_no = :allocation_no,]>
          <[sku = :sku,]>
          <[stock_id = :stock_id,]>
          <[json_str = :json_str,]>
          <[sku_name = :sku_name,]>
          <[allocation_num = :allocation_num,]>
          <[create_by = :create_by,]>
          <[create_time = :create_time,]>
          <[pick_status = :pick_status,]>
          <[pick_num = :pick_num,]>
          <[pick_by = :pick_by,]>
          <[pick_time = :pick_time,]>
          <[history_pick_num = :history_pick_num,]>
          <[box_status = :box_status,]>
          <[box_num = :box_num,]>
          <[box_no = :box_no,]>
          <[box_by = :box_by,]>
          <[box_time = :box_time,]>
          <[board_status = :board_status,]>
          <[board_no = :board_no,]>
          <[board_by = :board_by,]>
          <[board_time = :board_time,]>
          <[load_status = :load_status,]>
          <[load_by = :load_by,]>
          <[load_time = :load_time,]>
          <[up_status = :up_status,]>
          <[up_num = :up_num,]>
          <[up_by = :up_by,]>
          <[up_time = :up_time,]>
          <[put_status = :put_status,]>
          <[put_by = :put_by,]>
          <[put_time = :put_time,]>
          <[is_audit = :is_audit,]>
          <[inventory_status = :inventory_status,]>
        allocation_item_id = allocation_item_id
        WHERE 1 = 1
        AND allocation_item_id = :allocation_item_id
      ]]>
    </content>
  </sql>
</sqlmap>