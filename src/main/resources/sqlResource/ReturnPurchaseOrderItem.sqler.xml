<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryReturnPurchaseOrderItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM return_purchase_order_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_order_no = :return_order_no]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND article_number = :article_number]>
        <[AND quantity = :quantity]>
        <[AND return_quantity = :return_quantity]>
        <[AND product_manager = :product_manager]>
        <[AND puchase_assistanter = :puchase_assistanter]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND product_img = :product_img]>
        <[AND sku_life_cycle_phase = :sku_life_cycle_phase]>
        <[AND receive_shipping_order_no = :receive_shipping_order_no]>
        <[AND exception_id = :exception_id]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryReturnPurchaseOrderItemList" >
    <content >
      <![CDATA[
        SELECT id, return_order_no, purchase_order_no, article_number, quantity, return_quantity, sku_life_cycle_phase, product_img,
        product_manager, puchase_assistanter, warehouse_id, receive_shipping_order_no, exception_id
        FROM return_purchase_order_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_order_no = :return_order_no]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND article_number = :article_number]>
        <[AND quantity = :quantity]>
        <[AND return_quantity = :return_quantity]>
        <[AND product_manager = :product_manager]>
        <[AND puchase_assistanter = :puchase_assistanter]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND product_img = :product_img]>
        <[AND sku_life_cycle_phase = :sku_life_cycle_phase]>
        <[AND receive_shipping_order_no = :receive_shipping_order_no]>
        <[AND exception_id = :exception_id]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryReturnPurchaseOrderItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, return_order_no, purchase_order_no, article_number, quantity, return_quantity, sku_life_cycle_phase, product_img,
        product_manager, puchase_assistanter, warehouse_id, receive_shipping_order_no, exception_id
        FROM return_purchase_order_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryReturnPurchaseOrderItem" >
    <content >
      <![CDATA[
        SELECT id, return_order_no, purchase_order_no, article_number, quantity, return_quantity, sku_life_cycle_phase, product_img,
        product_manager, puchase_assistanter, warehouse_id, receive_shipping_order_no, exception_id
        FROM return_purchase_order_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND return_order_no = :return_order_no]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND article_number = :article_number]>
        <[AND quantity = :quantity]>
        <[AND return_quantity = :return_quantity]>
        <[AND product_manager = :product_manager]>
        <[AND puchase_assistanter = :puchase_assistanter]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND product_img = :product_img]>
        <[AND sku_life_cycle_phase = :sku_life_cycle_phase]>
        <[AND receive_shipping_order_no = :receive_shipping_order_no]>
        <[AND exception_id = :exception_id]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createReturnPurchaseOrderItem" >
    <content >
      <![CDATA[
        INSERT INTO return_purchase_order_item (id, return_order_no, purchase_order_no, article_number, quantity, return_quantity,
          product_manager, puchase_assistanter, warehouse_id, receive_shipping_order_no, sku_life_cycle_phase, product_img,
          exception_id)
        VALUES (:id, :return_order_no, :purchase_order_no, :article_number, :quantity, :return_quantity,
          :product_manager, :puchase_assistanter, :warehouse_id, :receive_shipping_order_no, :sku_life_cycle_phase, :product_img,
          :exception_id)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteReturnPurchaseOrderItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM return_purchase_order_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="deleteReturnPurchaseOrderItemBySku" >
    <content >
      <![CDATA[
        DELETE FROM return_purchase_order_item
        WHERE 1 = 1
        AND article_number = :article_number
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateReturnPurchaseOrderItemBySku" >
    <content >
      <![CDATA[
        UPDATE return_purchase_order_item
        SET <[return_order_no = :return_order_no,]>
          <[purchase_order_no = :purchase_order_no,]>
          <[article_number = :article_number,]>
          <[quantity = :quantity,]>
          <[return_quantity = :return_quantity,]>
          <[product_manager = :product_manager,]>
          <[puchase_assistanter = :puchase_assistanter,]>
          <[warehouse_id = :warehouse_id,]>
          <[product_img = :product_img,]>
          <[sku_life_cycle_phase = :sku_life_cycle_phase,]>
          <[receive_shipping_order_no = :receive_shipping_order_no,]>
          <[exception_id = :exception_id,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateReturnPurchaseOrderItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE return_purchase_order_item
        SET <[return_order_no = :return_order_no,]>
          <[purchase_order_no = :purchase_order_no,]>
          <[article_number = :article_number,]>
          <[quantity = :quantity,]>
          <[return_quantity = :return_quantity,]>
          <[product_manager = :product_manager,]>
          <[puchase_assistanter = :puchase_assistanter,]>
          <[warehouse_id = :warehouse_id,]>
          <[product_img = :product_img,]>
          <[sku_life_cycle_phase = :sku_life_cycle_phase,]>
          <[receive_shipping_order_no = :receive_shipping_order_no,]>
          <[exception_id = :exception_id,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>