<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >

  <sql datasource="dataSource" id="queryApvDelivers" >
    <content >
      <![CDATA[
        select
          DATE_FORMAT(t.deliver_time,'%Y-%m-%d') groupDay,
          COUNT(IF(sa.sale_attribute_setting_str = '爆款',TRUE,NULL)) in_vogue_num,
          COUNT(IF(sa.sale_attribute_setting_str = '热销',TRUE,NULL)) hot_sale_num,
          COUNT(IF(sa.sale_attribute_setting_str = '畅销',TRUE,NULL)) bestselling_num,
          COUNT(IF(sa.sale_attribute_setting_str = '平销',TRUE,NULL)) normal_num,
          COUNT(IF(sa.sale_attribute_setting_str = '低销',TRUE,NULL)) low_selling_num,
          COUNT(IF(sa.sale_attribute_setting_str = '滞销',TRUE,NULL)) unsold_num,
          COUNT(IF(sa.sale_attribute_setting_str = '短呆滞',TRUE,NULL)) short_term_unsold_num,
          COUNT(IF(sa.sale_attribute_setting_str = '长呆滞',TRUE,NULL)) long_term_unsold_num,
          COUNT(IF(sa.sale_attribute_setting_str = '新品',TRUE,NULL)) new_product_num,
          COUNT(IF(sa.sale_attribute_setting_str = '' || sa.sale_attribute_setting_str is null,TRUE,NULL)) blank_num
        from wh_apv a
        inner join apv_track t on t.apv_no = a.apv_no
        inner join wh_apv_item ai on a.id = ai.apv_id
        left join wh_sku_sale_statistic_record sa on ai.sku = sa.sku
        where a.apv_type in ('SS', 'SM')
        and a.status in (17, 18)
        and t.deliver_time >= :start_time
        and t.deliver_time <= :end_time
        GROUP BY groupDay
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryDeliverStatisticsRecord" >
    <content >
      <![CDATA[
        select
          <[:DATE_FORMAT]>
          sum(r.in_vogue_num) as in_vogue_num,
          sum(r.hot_sale_num) as hot_sale_num,
          sum(r.bestselling_num) as bestselling_num,
          sum(r.normal_num) as normal_num,
          sum(r.low_selling_num) as low_selling_num,
          sum(r.unsold_num) as unsold_num,
          sum(r.short_term_unsold_num) as short_term_unsold_num,
          sum(r.long_term_unsold_num) as long_term_unsold_num,
          sum(r.blank_num) as blank_num,
          sum(r.new_product_num) as new_product_num
        from deliver_statistics_record r
        where r.record_date >= :start_time
        and r.record_date <= :end_time
        <[:GROUP_BY]>
      ]]>
    </content>
  </sql>


  <sql datasource="dataSource" id="createDeliverStatisticsReocrd" >
    <content >
      <![CDATA[
        INSERT INTO deliver_statistics_record
        (record_date, in_vogue_num, hot_sale_num, bestselling_num, normal_num, low_selling_num, unsold_num, short_term_unsold_num, long_term_unsold_num,
        blank_num, new_product_num)
        VALUES
        (:record_date, :in_vogue_num, :hot_sale_num, :bestselling_num, :normal_num, :low_selling_num, :unsold_num, :short_term_unsold_num, :long_term_unsold_num,
        :blank_num, :new_product_num)
      ]]>
    </content>
  </sql>

</sqlmap>