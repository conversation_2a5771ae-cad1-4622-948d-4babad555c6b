<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhAllocateRecordItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM
            wh_allocate_record_item ari
            LEFT JOIN wh_allocate_record ar ON ari.allocate_number = ar.allocate_number
            LEFT JOIN wh_transfer_stock_relation t ON ari.out_sku = t.sku
        WHERE 1 = 1
        <[AND ari.id = :id]>
        <[AND ari.allocate_number = :allocate_number]>
        <[AND ari.out_sku = :out_sku]>
        <[AND ari.allocate_amount = :allocate_amount]>
        <[AND ari.exp_batch_no = :exp_batch_no]>

        GROUP BY ari.id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAllocateRecordItemList" >
    <content >
      <![CDATA[
        SELECT
            ari.id,
            ari.allocate_number,
            ari.allocate_amount,
            ari.out_sku,
            ari.exp_batch_no,
            t.fn_sku AS fnSku
        FROM
            wh_allocate_record_item ari
            LEFT JOIN wh_allocate_record ar ON ari.allocate_number = ar.allocate_number
            LEFT JOIN wh_transfer_stock_relation t ON ari.out_sku = t.sku
        WHERE 1 = 1
        <[AND ari.id = :id]>
        <[AND ari.allocate_number = :allocate_number]>
        <[AND ari.out_sku = :out_sku]>
        <[AND ari.allocate_amount = :allocate_amount]>
        <[AND ari.exp_batch_no = :exp_batch_no]>
         GROUP BY ari.id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAllocateRecordItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, allocate_number, out_sku, allocate_amount, exp_batch_no
        FROM wh_allocate_record_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAllocateRecordItem" >
    <content >
      <![CDATA[
        SELECT id, allocate_number, out_sku, allocate_amount, exp_batch_no
        FROM wh_allocate_record_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND allocate_number = :allocate_number]>
        <[AND out_sku = :out_sku]>
        <[AND allocate_amount = :allocate_amount]>
        <[AND exp_batch_no = :exp_batch_no]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhAllocateRecordItem" >
    <content >
      <![CDATA[
        INSERT INTO wh_allocate_record_item (allocate_number, out_sku, allocate_amount, exp_batch_no)
        VALUES (:allocate_number, :out_sku, :allocate_amount, :exp_batch_no)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhAllocateRecordItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_allocate_record_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhAllocateRecordItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_allocate_record_item
        SET <[allocate_number = :allocate_number,]>
          <[out_sku = :out_sku,]>
          <[allocate_amount = :allocate_amount,]>
          <[exp_batch_no = :exp_batch_no,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>