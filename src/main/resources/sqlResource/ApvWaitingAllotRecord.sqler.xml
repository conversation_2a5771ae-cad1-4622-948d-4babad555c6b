<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
    <sql datasource="dataSource" id="createWhApvWaitingAllotRecord" >
        <content ><![CDATA[

            INSERT INTO wh_apv_waiting_allot_record(apv_id, task_id, apv_historical_status, remark, operator, create_time, intercept_type)
            VALUES (:apv_id, :task_id, :apv_historical_status, :remark, :operator, :create_time, :intercept_type)

        ]]></content>
    </sql>

    <sql datasource="dataSource" id="queryWhApvWaitingAllotRecords" >
        <content ><![CDATA[

        SELECT record.id, apv.apv_no, apv.status, record.apv_historical_status, apv.platform_order_id, apv.sales_record_number,
         apv.ship_status, record.remark, record.operator, record.create_time, record.task_id,record.intercept_type

        FROM wh_apv_waiting_allot_record record
        INNER JOIN wh_apv apv ON record.apv_id = apv.id
        WHERE 1=1
        <[AND apv.apv_no in (:apv_no_list)]>
        <[AND apv.apv_no = :apv_no]>
        <[AND apv.status = :status]>
        <[AND apv.ship_status = :ship_status]>
        <[AND apv.ship_status in (:shipStatusList)]>
        <[AND record.apv_historical_status = :apv_historical_status]>
        <[AND record.create_time >= :from_operate_time]>
        <[AND record.create_time <= :to_operate_time]>
        <[AND record.id in (:record_id_list)]>
        <[AND record.id = :record_id]>
        <[AND record.intercept_type = :intercept_type]>
        <[AND record.intercept_type in (:interceptTypeList)]>
        <[AND apv.id in (select i.apv_id from wh_apv_item i where  i.sku= :sku)]>
        <[AND apv.id in (select i.apv_id from wh_apv_item i where  i.sku in (:skuList))]>
        ORDER BY record.id DESC
        ]]></content>
    </sql>

    <sql datasource="dataSource" id="queryWhApvWaitingAllotRecordsCount" >
        <content ><![CDATA[

        SELECT COUNT(1)

        FROM wh_apv_waiting_allot_record record
        INNER JOIN wh_apv apv ON record.apv_id = apv.id
        WHERE 1=1
        <[AND apv.apv_no in (:apv_no_list)]>
        <[AND apv.apv_no = :apv_no]>
        <[AND apv.status = :status]>
        <[AND apv.ship_status = :ship_status]>
        <[AND apv.ship_status in (:shipStatusList)]>
        <[AND record.apv_historical_status = :apv_historical_status]>
        <[AND record.create_time >= :from_operate_time]>
        <[AND record.create_time <= :to_operate_time]>
        <[AND record.id in (:record_id_list)]>
        <[AND record.id = :record_id]>
        <[AND record.intercept_type = :intercept_type]>
        <[AND record.intercept_type in (:interceptTypeList)]>
        <[AND apv.id in (select i.apv_id from wh_apv_item i where  i.sku= :sku)]>
        <[AND apv.id in (select i.apv_id from wh_apv_item i where  i.sku in (:skuList))]>
        ]]></content>
    </sql>

    <sql datasource="dataSource" id="queryCountByApvIdAndIncepType" >
        <content ><![CDATA[
        SELECT COUNT(1)
        FROM wh_apv_waiting_allot_record
        WHERE 1=1
        <[AND apv_id = :apv_id]>
        <[AND intercept_type = :intercept_type]>
        ]]></content>
    </sql>

</sqlmap>