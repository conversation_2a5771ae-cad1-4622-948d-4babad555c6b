<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryPacStockInOrderItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM pac_stock_in_order_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND pac_stock_in_order_id = :pac_stock_in_order_id]>
        <[AND owner_user_id = :owner_user_id]>
        <[AND owner_user_name = :owner_user_name]>
        <[AND item_id = :item_id]>
        <[AND item_name = :item_name]>
        <[AND bar_code = :bar_code]>
        <[AND item_code = :item_code]>
        <[AND item_quantity = :item_quantity]>
        <[AND create_time = :create_time]>
        <[AND update_time = :update_time]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryPacInOrderItemSkuCount" >
    <content >
      <![CDATA[
        SELECT COUNT(DISTINCT item_code)
        FROM pac_stock_in_order_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND pac_stock_in_order_id = :pac_stock_in_order_id]>
        <[AND owner_user_id = :owner_user_id]>
        <[AND owner_user_name = :owner_user_name]>
        <[AND item_id = :item_id]>
        <[AND item_name = :item_name]>
        <[AND bar_code = :bar_code]>
        <[AND item_code = :item_code]>
        <[AND item_code in (:itemCodeList)]>
        <[AND item_quantity = :item_quantity]>
        <[AND create_time = :create_time]>
        <[AND update_time = :update_time]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryPacInOrderItemSkuList" >
    <content >
      <![CDATA[
          SELECT  DISTINCT item_code,id, pac_stock_in_order_id, owner_user_id, owner_user_name, item_id, item_name,
          bar_code, item_quantity, create_time, update_time,item_up_quantity,order_item_id
          FROM pac_stock_in_order_item
          WHERE  1 = 1
           <[AND id = :id]>
          <[AND owner_user_id = :owner_user_id]>
          <[AND item_code in (:itemCodeList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPacStockInOrderItemList" >
    <content >
      <![CDATA[
        SELECT id, pac_stock_in_order_id, owner_user_id, owner_user_name, item_id, item_name, 
        bar_code, item_code, item_quantity, create_time, update_time,item_up_quantity,order_item_id
        FROM pac_stock_in_order_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND pac_stock_in_order_id = :pac_stock_in_order_id]>
        <[AND owner_user_id = :owner_user_id]>
        <[AND owner_user_name = :owner_user_name]>
        <[AND item_id = :item_id]>
        <[AND item_name = :item_name]>
        <[AND bar_code = :bar_code]>
        <[AND item_code = :item_code]>
        <[AND item_quantity = :item_quantity]>
        <[AND create_time = :create_time]>
        <[AND update_time = :update_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPacStockInOrderItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, pac_stock_in_order_id, owner_user_id, owner_user_name, item_id, item_name, 
        bar_code, item_code, item_quantity,item_up_quantity, create_time, update_time
        FROM pac_stock_in_order_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPacStockInOrderItem" >
    <content >
      <![CDATA[
        SELECT id, pac_stock_in_order_id, owner_user_id, owner_user_name, item_id, item_name, 
        bar_code, item_code, item_quantity,item_up_quantity,create_time, update_time
        FROM pac_stock_in_order_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND pac_stock_in_order_id = :pac_stock_in_order_id]>
        <[AND owner_user_id = :owner_user_id]>
        <[AND owner_user_name = :owner_user_name]>
        <[AND item_id = :item_id]>
        <[AND item_name = :item_name]>
        <[AND bar_code = :bar_code]>
        <[AND item_code = :item_code]>
        <[AND item_quantity = :item_quantity]>
        <[AND create_time = :create_time]>
        <[AND update_time = :update_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createPacStockInOrderItem" >
    <content >
      <![CDATA[
        INSERT INTO pac_stock_in_order_item (pac_stock_in_order_id, owner_user_id, owner_user_name, order_item_id, item_id, item_name,
          bar_code, item_code, item_quantity,item_up_quantity, create_time, update_time)
        VALUES (:pac_stock_in_order_id, :owner_user_id, :owner_user_name, :order_item_id, :item_id, :item_name,
          :bar_code, :item_code, :item_quantity,:item_up_quantity, :create_time, :update_time)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deletePacStockInOrderItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM pac_stock_in_order_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updatePacStockInOrderItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE pac_stock_in_order_item
        SET <[pac_stock_in_order_id = :pac_stock_in_order_id,]>
          <[owner_user_id = :owner_user_id,]>
          <[owner_user_name = :owner_user_name,]>
          <[item_id = :item_id,]>
          <[item_name = :item_name,]>
          <[bar_code = :bar_code,]>
          <[item_code = :item_code,]>
          <[item_quantity = :item_quantity,]>
          <[item_up_quantity = :item_up_quantity,]>
          <[create_time = :create_time,]>
          <[update_time = :update_time,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>